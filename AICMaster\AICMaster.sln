﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.31702.278
MinimumVisualStudioVersion = 10.0.40219.1
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ENChartCAC", "..\ENChartCAC\ENChartCAC\ENChartCAC.vbproj", "{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartDOLib", "..\ENChartCAC\EnchartDOLib\EnchartDOLib.vbproj", "{06C12653-799B-4345-BD29-D83ED6BB6D44}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ECConfig", "..\ENChartCAC\ECConfig\ECConfig.vbproj", "{2EF033CD-5C08-427A-B7D8-F406F90E8377}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "AIC.SharedData", "..\ENChartCAC\AIC.SharedData\AIC.SharedData.vbproj", "{B00D9EB7-8558-4E81-9755-23187838CBA5}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ECLauncher", "..\ENChartCAC\ECLauncher\ECLauncher.vbproj", "{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ECLauncherUpdater", "..\ENChartCAC\ECLauncherUpdater\ECLauncherUpdater.vbproj", "{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ECUpdater4", "..\ENChartCAC\ECUpdater4\ECUpdater4.vbproj", "{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ESPCodeEditV2", "..\ENChartCAC\ESPCodeEdit\ESPCodeEditV2.vbproj", "{DC57A6EA-3C03-4794-837D-528A8A6F5093}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MICCustomConnectionProviders", "..\ENChartCAC\MICCustomDataProviders\MICCustomConnectionProviders.csproj", "{FDE0A768-7AB0-4607-BE77-188E4B75000F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MICMiscUtilCSharp", "..\ENChartCAC\MICMiscUtilCSharp\MICMiscUtilCSharp.csproj", "{58DAAC63-8388-4B89-B876-1270309143BD}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "PublishUpdateV2", "..\ENChartCAC\PublishUpdateV2\PublishUpdateV2.vbproj", "{639F4C3D-1A1F-4B00-BC7E-445659830C70}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DataLink", "DataLink", "{4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "CDMReportViewer2", "..\ENChartCAC\CdmReportsViewer2\CDMReportViewer2.vbproj", "{3235178B-B987-4874-A537-EDE1697A3B0F}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "CodingReport", "..\CodingReport\CodingReport\CodingReport.vbproj", "{71A9473C-E5DA-4340-9537-75623870E71E}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ENChartReports", "..\AdminReports\ENChartReports\ENChartReports\ENChartReports.vbproj", "{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink", "..\McKessonIntelligentCoding\DataLink\DataLink\DataLink.vbproj", "{196AD751-9B89-4B00-B364-E5C6DB2A21A3}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.BlueElm", "..\McKessonIntelligentCoding\DataLink.BlueElm\DataLink.BlueElm.vbproj", "{2C595808-3628-498F-BC52-7A31E0880641}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Paragon", "..\McKessonIntelligentCoding\DataLink.Paragon\DataLink.Paragon.vbproj", "{EE8F349B-F17F-4C28-8239-238EC2B1FB58}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Processor", "..\McKessonIntelligentCoding\DataLink.Processor\DataLink.Processor\DataLink.Processor.vbproj", "{4D854B82-5C16-491C-80F5-06EB3EC5C67C}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Utilities.ChartToRedisplayMappingEditor", "..\McKessonIntelligentCoding\DataLink.Utilities.ChartToRedisplayMappingEditor\DataLink.Utilities.ChartToRedisplayMappingEditor.vbproj", "{A5F0B21A-213A-4E23-9443-F86D88864B94}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Utilities.MatchEditor", "..\McKessonIntelligentCoding\DataLink.Utilities.MatchEditor\DataLink.Utilities.MatchEditor.vbproj", "{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UI", "UI", "{A64EFB65-5EC0-4FBD-B004-275F8F64E81E}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Utilities.ParagonDataRequester", "..\McKessonIntelligentCoding\DataLink.Utilities.ParagonDataRequester\DataLink.Utilities.ParagonDataRequester.vbproj", "{D0342723-79D2-4602-A67A-29E11C52C8D8}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Business.Chart", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Business.Chart\McKessonIntelligentCoding.Business.Chart.vbproj", "{E70F0809-D148-4D44-AE0C-537EAEEB3307}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Common.Contracts", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Common.Contracts\McKessonIntelligentCoding.Common.Contracts.vbproj", "{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Common.Logging", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Common.Logging\McKessonIntelligentCoding.Common.Logging.vbproj", "{1F2C64B8-9E44-4821-8A31-4732089C641C}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Common.Utils", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Common.Utils\McKessonIntelligentCoding.Common.Utils.vbproj", "{560FB555-69A7-4DC3-89D5-1E662E247501}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Console", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Console\McKessonIntelligentCoding.Console.vbproj", "{C0B7A132-A862-4171-8174-6429605F1CD9}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Data", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Data\McKessonIntelligentCoding.Data.vbproj", "{53CC252D-D4B6-453F-A498-7A7D5A046857}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Data.Repository", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Data.Repository\McKessonIntelligentCoding.Data.Repository.vbproj", "{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.DataLink.UHS", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.DataLink.UHS\McKessonIntelligentCoding.DataLink.UHS.vbproj", "{3E07144E-F0D8-44BF-AB50-878CB70336CA}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.ServiceLib", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.ServiceLib\McKessonIntelligentCoding.ServiceLib.vbproj", "{BC774602-8A04-4FF5-B9C7-67F34CAA1031}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Test.Business.Chart", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Test.Business.Chart\McKessonIntelligentCoding.Test.Business.Chart.vbproj", "{28C962F6-A231-420B-836B-3056F2563B5A}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKessonIntelligentCoding.Test.Data.Repository", "..\McKessonIntelligentCoding\McKessonIntelligentCoding.Test.Data.Repository\McKessonIntelligentCoding.Test.Data.Repository.vbproj", "{ABF5FBBB-8152-485E-B968-BB9A2502234C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ESS", "ESS", "{B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "AICCryptoHelper", "..\Server\EnchartServer\ICCryptoHelper\AICCryptoHelper.vbproj", "{807B6493-8B9E-431A-8757-0855E73B9235}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnchartServer", "..\Server\EnchartServer\src\EnchartServer\EnchartServer.csproj", "{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartServer.Business", "..\Server\EnchartServer\src\EnchartServer.Business\EnchartServer.Business.vbproj", "{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartServer.Data", "..\Server\EnchartServer\src\EnchartServer.Data\EnchartServer.Data.vbproj", "{28F65D6C-7F2E-45A6-A512-73EEBEED8778}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartServer.ExternalComm", "..\Server\EnchartServer\src\EnchartServer.ExternalComm\EnchartServer.ExternalComm.vbproj", "{6B2DD185-EF46-4B01-8163-B882394C1DD9}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartServer.Repository", "..\Server\EnchartServer\src\EnchartServer.Repository\EnchartServer.Repository.vbproj", "{47489602-B873-404B-A54A-D49F9C4864D9}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartServer.Test", "..\Server\EnchartServer\src\EnchartServer.Test\EnchartServer.Test.vbproj", "{74645A52-90E1-49A7-8667-4AB645561FBB}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ReportsEngine", "..\Server\EnchartServer\src\ReportsEngine\ReportsEngine.vbproj", "{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DataLink.Assistant", "..\DataLink.Assistant\DataLink.Assistant\DataLink.Assistant.vbproj", "{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "Timeout", "..\Timeout\Timeout\Timeout.vbproj", "{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ColdFeed", "ColdFeed", "{ECE0F7CF-6277-4DBA-8C39-01A1AE6EABF9}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKesson.HIC.ChargeSummaryColdFeedHelper", "..\ENChartCAC\ColdFeedExport\McKesson.HIC.ChargeSummaryColdFeedHelper\McKesson.HIC.ChargeSummaryColdFeedHelper.vbproj", "{EA521030-4058-44E6-AF25-B46FA2F59E60}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "McKesson.HIC.HPFColdFeed", "..\ENChartCAC\ColdFeedExport\McKeesson.HIC.HPFColdFeed.dll\McKesson.HIC.HPFColdFeed.vbproj", "{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		DatabaseMigration|Any CPU = DatabaseMigration|Any CPU
		DatabaseMigration|x64 = DatabaseMigration|x64
		DatabaseMigration|x86 = DatabaseMigration|x86
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		JJCDebug|Any CPU = JJCDebug|Any CPU
		JJCDebug|x64 = JJCDebug|x64
		JJCDebug|x86 = JJCDebug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
		UI and DOLib|Any CPU = UI and DOLib|Any CPU
		UI and DOLib|x64 = UI and DOLib|x64
		UI and DOLib|x86 = UI and DOLib|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x64.Build.0 = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x64.ActiveCfg = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x64.Build.0 = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x86.ActiveCfg = Debug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x86.Build.0 = Debug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|Any CPU.ActiveCfg = JJCDebug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|Any CPU.Build.0 = JJCDebug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x64.ActiveCfg = JJCDebug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x64.Build.0 = JJCDebug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x86.ActiveCfg = JJCDebug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x86.Build.0 = JJCDebug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x64.ActiveCfg = Release|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x64.Build.0 = Release|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x86.ActiveCfg = Release|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x86.Build.0 = Release|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x64.Build.0 = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x86.Build.0 = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x64.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x64.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x86.ActiveCfg = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x86.Build.0 = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x86.ActiveCfg = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x86.Build.0 = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|Any CPU.Build.0 = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x64.ActiveCfg = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x64.Build.0 = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x86.ActiveCfg = Release|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x86.Build.0 = Release|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x86.Build.0 = Debug|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|x64.ActiveCfg = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|x64.Build.0 = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|x86.ActiveCfg = Debug|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Debug|x86.Build.0 = Debug|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|x86.ActiveCfg = Debug|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.JJCDebug|x86.Build.0 = Debug|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|Any CPU.Build.0 = Release|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|x64.ActiveCfg = Release|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|x64.Build.0 = Release|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|x86.ActiveCfg = Release|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.Release|x86.Build.0 = Release|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|Any CPU.ActiveCfg = UI and DOLib|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|Any CPU.Build.0 = UI and DOLib|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|x64.ActiveCfg = UI and DOLib|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|x64.Build.0 = UI and DOLib|Any CPU
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|x86.ActiveCfg = UI and DOLib|x86
		{2EF033CD-5C08-427A-B7D8-F406F90E8377}.UI and DOLib|x86.Build.0 = UI and DOLib|x86
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|x64.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Debug|x86.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|x64.ActiveCfg = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|x64.Build.0 = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|x86.ActiveCfg = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.Release|x86.Build.0 = Release|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{B00D9EB7-8558-4E81-9755-23187838CBA5}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|x64.Build.0 = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|x86.ActiveCfg = Debug|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Debug|x86.Build.0 = Debug|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|x86.ActiveCfg = Debug|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.JJCDebug|x86.Build.0 = Debug|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|Any CPU.Build.0 = Release|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|x64.ActiveCfg = Release|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|x64.Build.0 = Release|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|x86.ActiveCfg = Release|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.Release|x86.Build.0 = Release|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|Any CPU.ActiveCfg = UI and DOLib|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|Any CPU.Build.0 = UI and DOLib|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|x64.ActiveCfg = UI and DOLib|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|x64.Build.0 = UI and DOLib|Any CPU
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|x86.ActiveCfg = UI and DOLib|x86
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C}.UI and DOLib|x86.Build.0 = UI and DOLib|x86
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|x64.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Debug|x86.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|x64.ActiveCfg = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|x64.Build.0 = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|x86.ActiveCfg = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.Release|x86.Build.0 = Release|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|x64.ActiveCfg = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|x64.Build.0 = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|x86.ActiveCfg = Debug|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Debug|x86.Build.0 = Debug|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|x86.ActiveCfg = Debug|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.JJCDebug|x86.Build.0 = Debug|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|Any CPU.Build.0 = Release|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|x64.ActiveCfg = Release|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|x64.Build.0 = Release|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|x86.ActiveCfg = Release|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.Release|x86.Build.0 = Release|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|Any CPU.ActiveCfg = UI and DOLib|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|Any CPU.Build.0 = UI and DOLib|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|x64.ActiveCfg = UI and DOLib|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|x64.Build.0 = UI and DOLib|Any CPU
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|x86.ActiveCfg = UI and DOLib|x86
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280}.UI and DOLib|x86.Build.0 = UI and DOLib|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|x64.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|x86.ActiveCfg = Debug|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Debug|x86.Build.0 = Debug|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|x86.ActiveCfg = Debug|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.JJCDebug|x86.Build.0 = Debug|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|x64.ActiveCfg = Release|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|x64.Build.0 = Release|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|x86.ActiveCfg = Release|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.Release|x86.Build.0 = Release|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{DC57A6EA-3C03-4794-837D-528A8A6F5093}.UI and DOLib|x86.Build.0 = Debug|x86
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|x64.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Debug|x86.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|Any CPU.Build.0 = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|x64.ActiveCfg = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|x64.Build.0 = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|x86.ActiveCfg = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.Release|x86.Build.0 = Release|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{FDE0A768-7AB0-4607-BE77-188E4B75000F}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x64.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x64.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x86.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x86.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|x64.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|x64.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|x86.ActiveCfg = Debug|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Debug|x86.Build.0 = Debug|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|x86.ActiveCfg = Debug|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.JJCDebug|x86.Build.0 = Debug|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|Any CPU.Build.0 = Release|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|x64.ActiveCfg = Release|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|x64.Build.0 = Release|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|x86.ActiveCfg = Release|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.Release|x86.Build.0 = Release|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{639F4C3D-1A1F-4B00-BC7E-445659830C70}.UI and DOLib|x86.Build.0 = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|x64.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|x86.ActiveCfg = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Debug|x86.Build.0 = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|x86.ActiveCfg = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.JJCDebug|x86.Build.0 = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|x64.ActiveCfg = Release|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|x64.Build.0 = Release|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|x86.ActiveCfg = Release|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.Release|x86.Build.0 = Release|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{3235178B-B987-4874-A537-EDE1697A3B0F}.UI and DOLib|x86.Build.0 = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.DatabaseMigration|x86.Build.0 = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|x64.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|x86.ActiveCfg = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.Debug|x86.Build.0 = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|x86.ActiveCfg = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.JJCDebug|x86.Build.0 = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|Any CPU.Build.0 = Release|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|x64.ActiveCfg = Release|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|x64.Build.0 = Release|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|x86.ActiveCfg = Release|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.Release|x86.Build.0 = Release|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{71A9473C-E5DA-4340-9537-75623870E71E}.UI and DOLib|x86.Build.0 = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.DatabaseMigration|x86.Build.0 = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|x64.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|x64.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|x86.ActiveCfg = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Debug|x86.Build.0 = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|x86.ActiveCfg = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.JJCDebug|x86.Build.0 = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|Any CPU.Build.0 = Release|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|x64.ActiveCfg = Release|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|x64.Build.0 = Release|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|x86.ActiveCfg = Release|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.Release|x86.Build.0 = Release|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}.UI and DOLib|x86.Build.0 = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.DatabaseMigration|x86.Build.0 = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|x64.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|x86.ActiveCfg = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Debug|x86.Build.0 = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|x86.ActiveCfg = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.JJCDebug|x86.Build.0 = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|x64.ActiveCfg = Release|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|x64.Build.0 = Release|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|x86.ActiveCfg = Release|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.Release|x86.Build.0 = Release|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3}.UI and DOLib|x86.Build.0 = Debug|x86
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|x64.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|x64.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|x86.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Debug|x86.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|x64.ActiveCfg = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|x64.Build.0 = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|x86.ActiveCfg = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.Release|x86.Build.0 = Release|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{2C595808-3628-498F-BC52-7A31E0880641}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|x64.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Debug|x86.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|x64.ActiveCfg = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|x64.Build.0 = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|x86.ActiveCfg = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.Release|x86.Build.0 = Release|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.DatabaseMigration|x86.Build.0 = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|x64.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|x86.ActiveCfg = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Debug|x86.Build.0 = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|x86.ActiveCfg = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.JJCDebug|x86.Build.0 = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|x64.ActiveCfg = Release|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|x64.Build.0 = Release|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|x86.ActiveCfg = Release|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.Release|x86.Build.0 = Release|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C}.UI and DOLib|x86.Build.0 = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|x64.Build.0 = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.DatabaseMigration|x86.Build.0 = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|x64.ActiveCfg = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|x64.Build.0 = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|x86.ActiveCfg = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Debug|x86.Build.0 = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|x64.ActiveCfg = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|x64.Build.0 = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|x86.ActiveCfg = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.JJCDebug|x86.Build.0 = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|x64.ActiveCfg = Release|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|x64.Build.0 = Release|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|x86.ActiveCfg = Release|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.Release|x86.Build.0 = Release|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|x64.Build.0 = Debug|x64
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{A5F0B21A-213A-4E23-9443-F86D88864B94}.UI and DOLib|x86.Build.0 = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|x64.Build.0 = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.DatabaseMigration|x86.Build.0 = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|x64.ActiveCfg = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|x64.Build.0 = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|x86.ActiveCfg = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Debug|x86.Build.0 = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|x64.ActiveCfg = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|x64.Build.0 = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|x86.ActiveCfg = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.JJCDebug|x86.Build.0 = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|x64.ActiveCfg = Release|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|x64.Build.0 = Release|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|x86.ActiveCfg = Release|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.Release|x86.Build.0 = Release|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|x64.Build.0 = Debug|x64
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F}.UI and DOLib|x86.Build.0 = Debug|x86
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|x64.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Debug|x86.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|x64.ActiveCfg = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|x64.Build.0 = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|x86.ActiveCfg = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.Release|x86.Build.0 = Release|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{D0342723-79D2-4602-A67A-29E11C52C8D8}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|x64.Build.0 = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.DatabaseMigration|x86.Build.0 = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|x64.ActiveCfg = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|x64.Build.0 = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|x86.ActiveCfg = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Debug|x86.Build.0 = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|x64.ActiveCfg = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|x64.Build.0 = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|x86.ActiveCfg = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.JJCDebug|x86.Build.0 = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|Any CPU.Build.0 = Release|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|x64.ActiveCfg = Release|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|x64.Build.0 = Release|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|x86.ActiveCfg = Release|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.Release|x86.Build.0 = Release|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|x64.Build.0 = Debug|x64
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{E70F0809-D148-4D44-AE0C-537EAEEB3307}.UI and DOLib|x86.Build.0 = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|x64.Build.0 = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.DatabaseMigration|x86.Build.0 = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|x64.ActiveCfg = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|x64.Build.0 = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|x86.ActiveCfg = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Debug|x86.Build.0 = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|x64.ActiveCfg = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|x64.Build.0 = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|x86.ActiveCfg = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.JJCDebug|x86.Build.0 = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|x64.ActiveCfg = Release|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|x64.Build.0 = Release|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|x86.ActiveCfg = Release|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.Release|x86.Build.0 = Release|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|x64.Build.0 = Debug|x64
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6}.UI and DOLib|x86.Build.0 = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|x64.Build.0 = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.DatabaseMigration|x86.Build.0 = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|x64.ActiveCfg = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|x64.Build.0 = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|x86.ActiveCfg = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Debug|x86.Build.0 = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|x64.ActiveCfg = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|x64.Build.0 = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|x86.ActiveCfg = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.JJCDebug|x86.Build.0 = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|x64.ActiveCfg = Release|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|x64.Build.0 = Release|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|x86.ActiveCfg = Release|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.Release|x86.Build.0 = Release|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|x64.Build.0 = Debug|x64
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{1F2C64B8-9E44-4821-8A31-4732089C641C}.UI and DOLib|x86.Build.0 = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.DatabaseMigration|x86.Build.0 = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|x64.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|x64.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|x86.ActiveCfg = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Debug|x86.Build.0 = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|x86.ActiveCfg = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.JJCDebug|x86.Build.0 = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|Any CPU.Build.0 = Release|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|x64.ActiveCfg = Release|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|x64.Build.0 = Release|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|x86.ActiveCfg = Release|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.Release|x86.Build.0 = Release|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{560FB555-69A7-4DC3-89D5-1E662E247501}.UI and DOLib|x86.Build.0 = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|x64.Build.0 = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.DatabaseMigration|x86.Build.0 = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|x64.ActiveCfg = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|x64.Build.0 = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|x86.ActiveCfg = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Debug|x86.Build.0 = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|x64.ActiveCfg = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|x64.Build.0 = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|x86.ActiveCfg = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.JJCDebug|x86.Build.0 = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|x64.ActiveCfg = Release|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|x64.Build.0 = Release|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|x86.ActiveCfg = Release|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.Release|x86.Build.0 = Release|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|x64.Build.0 = Debug|x64
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{C0B7A132-A862-4171-8174-6429605F1CD9}.UI and DOLib|x86.Build.0 = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|x64.Build.0 = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.DatabaseMigration|x86.Build.0 = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|x64.ActiveCfg = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|x64.Build.0 = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|x86.ActiveCfg = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Debug|x86.Build.0 = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|x64.ActiveCfg = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|x64.Build.0 = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|x86.ActiveCfg = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.JJCDebug|x86.Build.0 = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|Any CPU.Build.0 = Release|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|x64.ActiveCfg = Release|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|x64.Build.0 = Release|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|x86.ActiveCfg = Release|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.Release|x86.Build.0 = Release|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|x64.Build.0 = Debug|x64
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{53CC252D-D4B6-453F-A498-7A7D5A046857}.UI and DOLib|x86.Build.0 = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|x64.Build.0 = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.DatabaseMigration|x86.Build.0 = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|x64.ActiveCfg = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|x64.Build.0 = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|x86.ActiveCfg = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Debug|x86.Build.0 = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|x64.ActiveCfg = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|x64.Build.0 = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|x86.ActiveCfg = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.JJCDebug|x86.Build.0 = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|x64.ActiveCfg = Release|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|x64.Build.0 = Release|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|x86.ActiveCfg = Release|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.Release|x86.Build.0 = Release|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|x64.Build.0 = Debug|x64
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60}.UI and DOLib|x86.Build.0 = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.DatabaseMigration|x86.Build.0 = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|x64.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|x86.ActiveCfg = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Debug|x86.Build.0 = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|x86.ActiveCfg = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.JJCDebug|x86.Build.0 = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|x64.ActiveCfg = Release|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|x64.Build.0 = Release|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|x86.ActiveCfg = Release|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.Release|x86.Build.0 = Release|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{3E07144E-F0D8-44BF-AB50-878CB70336CA}.UI and DOLib|x86.Build.0 = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|x64.Build.0 = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.DatabaseMigration|x86.Build.0 = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|x64.ActiveCfg = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|x64.Build.0 = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|x86.ActiveCfg = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Debug|x86.Build.0 = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|x64.ActiveCfg = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|x64.Build.0 = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|x86.ActiveCfg = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.JJCDebug|x86.Build.0 = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|x64.ActiveCfg = Release|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|x64.Build.0 = Release|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|x86.ActiveCfg = Release|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.Release|x86.Build.0 = Release|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|x64.Build.0 = Debug|x64
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031}.UI and DOLib|x86.Build.0 = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|x64.Build.0 = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.DatabaseMigration|x86.Build.0 = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|x64.ActiveCfg = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|x64.Build.0 = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|x86.ActiveCfg = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.Debug|x86.Build.0 = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|x64.ActiveCfg = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|x64.Build.0 = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|x86.ActiveCfg = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.JJCDebug|x86.Build.0 = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|x64.ActiveCfg = Release|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|x64.Build.0 = Release|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|x86.ActiveCfg = Release|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.Release|x86.Build.0 = Release|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|x64.Build.0 = Debug|x64
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{28C962F6-A231-420B-836B-3056F2563B5A}.UI and DOLib|x86.Build.0 = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|x64.ActiveCfg = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|x64.Build.0 = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.DatabaseMigration|x86.Build.0 = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|x64.ActiveCfg = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|x64.Build.0 = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|x86.ActiveCfg = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Debug|x86.Build.0 = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|x64.ActiveCfg = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|x64.Build.0 = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|x86.ActiveCfg = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.JJCDebug|x86.Build.0 = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|x64.ActiveCfg = Release|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|x64.Build.0 = Release|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|x86.ActiveCfg = Release|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.Release|x86.Build.0 = Release|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|x64.ActiveCfg = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|x64.Build.0 = Debug|x64
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{ABF5FBBB-8152-485E-B968-BB9A2502234C}.UI and DOLib|x86.Build.0 = Debug|x86
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|x64.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|x64.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|x86.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Debug|x86.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|Any CPU.Build.0 = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|x64.ActiveCfg = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|x64.Build.0 = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|x86.ActiveCfg = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.Release|x86.Build.0 = Release|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{807B6493-8B9E-431A-8757-0855E73B9235}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.DatabaseMigration|x86.Build.0 = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|x64.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|x86.ActiveCfg = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Debug|x86.Build.0 = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|x86.ActiveCfg = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.JJCDebug|x86.Build.0 = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|x64.ActiveCfg = Release|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|x64.Build.0 = Release|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|x86.ActiveCfg = Release|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.Release|x86.Build.0 = Release|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806}.UI and DOLib|x86.Build.0 = Debug|x86
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|x64.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Debug|x86.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|x64.ActiveCfg = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|x64.Build.0 = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|x86.ActiveCfg = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.Release|x86.Build.0 = Release|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|x64.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|x64.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|x86.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Debug|x86.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|Any CPU.Build.0 = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|x64.ActiveCfg = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|x64.Build.0 = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|x86.ActiveCfg = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.Release|x86.Build.0 = Release|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|x64.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Debug|x86.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|x64.ActiveCfg = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|x64.Build.0 = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|x86.ActiveCfg = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.Release|x86.Build.0 = Release|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{6B2DD185-EF46-4B01-8163-B882394C1DD9}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|x64.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Debug|x86.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|x64.ActiveCfg = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|x64.Build.0 = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|x86.ActiveCfg = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.Release|x86.Build.0 = Release|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{47489602-B873-404B-A54A-D49F9C4864D9}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|x64.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Debug|x86.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|x64.ActiveCfg = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|x64.Build.0 = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|x86.ActiveCfg = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.Release|x86.Build.0 = Release|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{74645A52-90E1-49A7-8667-4AB645561FBB}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|x64.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Debug|x86.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|Any CPU.Build.0 = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|x64.ActiveCfg = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|x64.Build.0 = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|x86.ActiveCfg = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.Release|x86.Build.0 = Release|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|x64.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|x64.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|x86.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Debug|x86.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|x64.ActiveCfg = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|x64.Build.0 = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|x86.ActiveCfg = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.Release|x86.Build.0 = Release|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|x64.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Debug|x86.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|Any CPU.Build.0 = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|x64.ActiveCfg = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|x64.Build.0 = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|x86.ActiveCfg = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.Release|x86.Build.0 = Release|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|x86.ActiveCfg = Debug|Any CPU
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE}.UI and DOLib|x86.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.DatabaseMigration|x86.Build.0 = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|x64.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|x86.ActiveCfg = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Debug|x86.Build.0 = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|x86.ActiveCfg = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.JJCDebug|x86.Build.0 = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|x64.ActiveCfg = Release|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|x64.Build.0 = Release|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|x86.ActiveCfg = Release|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.Release|x86.Build.0 = Release|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{EA521030-4058-44E6-AF25-B46FA2F59E60}.UI and DOLib|x86.Build.0 = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|x86.ActiveCfg = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.DatabaseMigration|x86.Build.0 = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|x64.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|x86.ActiveCfg = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Debug|x86.Build.0 = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|x86.ActiveCfg = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.JJCDebug|x86.Build.0 = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|x64.ActiveCfg = Release|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|x64.Build.0 = Release|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|x86.ActiveCfg = Release|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.Release|x86.Build.0 = Release|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|Any CPU.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|Any CPU.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|x64.ActiveCfg = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|x64.Build.0 = Debug|Any CPU
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|x86.ActiveCfg = Debug|x86
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA}.UI and DOLib|x86.Build.0 = Debug|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{06C12653-799B-4345-BD29-D83ED6BB6D44} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{2EF033CD-5C08-427A-B7D8-F406F90E8377} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{B00D9EB7-8558-4E81-9755-23187838CBA5} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{08CA1E20-249B-4CD5-9FB3-D3E3C7B2810C} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{84A299C6-B7F2-40E9-81F0-6DD1CECBD2EA} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{22DD7CCD-ABC5-407E-9AC4-86B17DAA0280} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{DC57A6EA-3C03-4794-837D-528A8A6F5093} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{FDE0A768-7AB0-4607-BE77-188E4B75000F} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{58DAAC63-8388-4B89-B876-1270309143BD} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{639F4C3D-1A1F-4B00-BC7E-445659830C70} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{3235178B-B987-4874-A537-EDE1697A3B0F} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{71A9473C-E5DA-4340-9537-75623870E71E} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{196AD751-9B89-4B00-B364-E5C6DB2A21A3} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{2C595808-3628-498F-BC52-7A31E0880641} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{EE8F349B-F17F-4C28-8239-238EC2B1FB58} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{4D854B82-5C16-491C-80F5-06EB3EC5C67C} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{A5F0B21A-213A-4E23-9443-F86D88864B94} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{D05035CC-C0F2-46B3-8E16-AA8E3F68840F} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{D0342723-79D2-4602-A67A-29E11C52C8D8} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{E70F0809-D148-4D44-AE0C-537EAEEB3307} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{66B24CCD-EBF4-4FD0-9AA1-1DD9CC35B7F6} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{1F2C64B8-9E44-4821-8A31-4732089C641C} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{560FB555-69A7-4DC3-89D5-1E662E247501} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{C0B7A132-A862-4171-8174-6429605F1CD9} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{53CC252D-D4B6-453F-A498-7A7D5A046857} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{C3EBDF8E-F18F-4062-8718-D2BC03AC7D60} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{3E07144E-F0D8-44BF-AB50-878CB70336CA} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{BC774602-8A04-4FF5-B9C7-67F34CAA1031} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{28C962F6-A231-420B-836B-3056F2563B5A} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{ABF5FBBB-8152-485E-B968-BB9A2502234C} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{807B6493-8B9E-431A-8757-0855E73B9235} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{3E4E9B97-1BC2-40D9-B0F6-B351EF9E5806} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{DA85DA39-9FA7-4DBF-A1F4-32163BD84A97} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{28F65D6C-7F2E-45A6-A512-73EEBEED8778} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{6B2DD185-EF46-4B01-8163-B882394C1DD9} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{47489602-B873-404B-A54A-D49F9C4864D9} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{74645A52-90E1-49A7-8667-4AB645561FBB} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{7762F8BF-2F0E-4DF7-ACD9-A6DE48A7FC82} = {B403F31A-F90C-404B-B0A8-8FD1ECA6F5D1}
		{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464} = {4B56E25C-3F9A-4AB8-A38C-76D6DD5F0A81}
		{C13A9370-E9BF-4E73-8686-37FCFFDECEFE} = {A64EFB65-5EC0-4FBD-B004-275F8F64E81E}
		{EA521030-4058-44E6-AF25-B46FA2F59E60} = {ECE0F7CF-6277-4DBA-8C39-01A1AE6EABF9}
		{7FEC1EA6-A17E-4D8E-BC04-2420C953D7DA} = {ECE0F7CF-6277-4DBA-8C39-01A1AE6EABF9}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {68DAA0C0-9CF9-41AD-BAF8-9BDD49F9E90B}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 45
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfs.paragon.mckesson.com:8080/tfs/collection1-eis
		SccProjectUniqueName0 = ..\\ENChartCAC\\ENChartCAC\\ENChartCAC.vbproj
		SccProjectTopLevelParentUniqueName0 = AICMaster.sln
		SccProjectName0 = ../ENChartCAC/ENChartCAC
		SccLocalPath0 = ..\\ENChartCAC\\ENChartCAC
		SccProjectUniqueName1 = ..\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj
		SccProjectTopLevelParentUniqueName1 = AICMaster.sln
		SccProjectName1 = ../ENChartCAC/EnchartDOLib
		SccLocalPath1 = ..\\ENChartCAC\\EnchartDOLib
		SccProjectUniqueName2 = ..\\ENChartCAC\\ECConfig\\ECConfig.vbproj
		SccProjectTopLevelParentUniqueName2 = AICMaster.sln
		SccProjectName2 = ../ENChartCAC/ECConfig
		SccLocalPath2 = ..\\ENChartCAC\\ECConfig
		SccProjectUniqueName3 = ..\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj
		SccProjectTopLevelParentUniqueName3 = AICMaster.sln
		SccProjectName3 = ../ENChartCAC/AIC.SharedData
		SccLocalPath3 = ..\\ENChartCAC\\AIC.SharedData
		SccProjectUniqueName4 = ..\\ENChartCAC\\ECLauncher\\ECLauncher.vbproj
		SccProjectTopLevelParentUniqueName4 = AICMaster.sln
		SccProjectName4 = ../ENChartCAC/ECLauncher
		SccLocalPath4 = ..\\ENChartCAC\\ECLauncher
		SccProjectUniqueName5 = ..\\ENChartCAC\\ECLauncherUpdater\\ECLauncherUpdater.vbproj
		SccProjectTopLevelParentUniqueName5 = AICMaster.sln
		SccProjectName5 = ../ENChartCAC/ECLauncherUpdater
		SccLocalPath5 = ..\\ENChartCAC\\ECLauncherUpdater
		SccProjectUniqueName6 = ..\\ENChartCAC\\ECUpdater4\\ECUpdater4.vbproj
		SccProjectTopLevelParentUniqueName6 = AICMaster.sln
		SccProjectName6 = ../ENChartCAC/ECUpdater4
		SccLocalPath6 = ..\\ENChartCAC\\ECUpdater4
		SccProjectUniqueName7 = ..\\ENChartCAC\\ESPCodeEdit\\ESPCodeEditV2.vbproj
		SccProjectTopLevelParentUniqueName7 = AICMaster.sln
		SccProjectName7 = ../ENChartCAC/ESPCodeEdit
		SccLocalPath7 = ..\\ENChartCAC\\ESPCodeEdit
		SccProjectUniqueName8 = ..\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj
		SccProjectTopLevelParentUniqueName8 = AICMaster.sln
		SccProjectName8 = ../ENChartCAC/MICCustomDataProviders
		SccLocalPath8 = ..\\ENChartCAC\\MICCustomDataProviders
		SccProjectUniqueName9 = ..\\ENChartCAC\\MICMiscUtilCSharp\\MICMiscUtilCSharp.csproj
		SccProjectTopLevelParentUniqueName9 = AICMaster.sln
		SccProjectName9 = ../ENChartCAC/MICMiscUtilCSharp
		SccLocalPath9 = ..\\ENChartCAC\\MICMiscUtilCSharp
		SccProjectUniqueName10 = ..\\ENChartCAC\\PublishUpdateV2\\PublishUpdateV2.vbproj
		SccProjectTopLevelParentUniqueName10 = AICMaster.sln
		SccProjectName10 = ../ENChartCAC/PublishUpdateV2
		SccLocalPath10 = ..\\ENChartCAC\\PublishUpdateV2
		SccProjectUniqueName11 = ..\\ENChartCAC\\CdmReportsViewer2\\CDMReportViewer2.vbproj
		SccProjectTopLevelParentUniqueName11 = AICMaster.sln
		SccProjectName11 = ../ENChartCAC/CdmReportsViewer2
		SccLocalPath11 = ..\\ENChartCAC\\CdmReportsViewer2
		SccProjectUniqueName12 = ..\\CodingReport\\CodingReport\\CodingReport.vbproj
		SccProjectTopLevelParentUniqueName12 = AICMaster.sln
		SccProjectName12 = ../CodingReport/CodingReport
		SccLocalPath12 = ..\\CodingReport\\CodingReport
		SccProjectUniqueName13 = ..\\AdminReports\\ENChartReports\\ENChartReports\\ENChartReports.vbproj
		SccProjectTopLevelParentUniqueName13 = AICMaster.sln
		SccProjectName13 = ../AdminReports/ENChartReports/ENChartReports
		SccLocalPath13 = ..\\AdminReports\\ENChartReports\\ENChartReports
		SccProjectUniqueName14 = ..\\McKessonIntelligentCoding\\DataLink\\DataLink\\DataLink.vbproj
		SccProjectTopLevelParentUniqueName14 = AICMaster.sln
		SccProjectName14 = ../McKessonIntelligentCoding/DataLink/DataLink
		SccLocalPath14 = ..\\McKessonIntelligentCoding\\DataLink\\DataLink
		SccProjectUniqueName15 = ..\\McKessonIntelligentCoding\\DataLink.BlueElm\\DataLink.BlueElm.vbproj
		SccProjectTopLevelParentUniqueName15 = AICMaster.sln
		SccProjectName15 = ../McKessonIntelligentCoding/DataLink.BlueElm
		SccLocalPath15 = ..\\McKessonIntelligentCoding\\DataLink.BlueElm
		SccProjectUniqueName16 = ..\\McKessonIntelligentCoding\\DataLink.Paragon\\DataLink.Paragon.vbproj
		SccProjectTopLevelParentUniqueName16 = AICMaster.sln
		SccProjectName16 = ../McKessonIntelligentCoding/DataLink.Paragon
		SccLocalPath16 = ..\\McKessonIntelligentCoding\\DataLink.Paragon
		SccProjectUniqueName17 = ..\\McKessonIntelligentCoding\\DataLink.Processor\\DataLink.Processor\\DataLink.Processor.vbproj
		SccProjectTopLevelParentUniqueName17 = AICMaster.sln
		SccProjectName17 = ../McKessonIntelligentCoding/DataLink.Processor/DataLink.Processor
		SccLocalPath17 = ..\\McKessonIntelligentCoding\\DataLink.Processor\\DataLink.Processor
		SccProjectUniqueName18 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.ChartToRedisplayMappingEditor\\DataLink.Utilities.ChartToRedisplayMappingEditor.vbproj
		SccProjectTopLevelParentUniqueName18 = AICMaster.sln
		SccProjectName18 = ../McKessonIntelligentCoding/DataLink.Utilities.ChartToRedisplayMappingEditor
		SccLocalPath18 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.ChartToRedisplayMappingEditor
		SccProjectUniqueName19 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.MatchEditor\\DataLink.Utilities.MatchEditor.vbproj
		SccProjectTopLevelParentUniqueName19 = AICMaster.sln
		SccProjectName19 = ../McKessonIntelligentCoding/DataLink.Utilities.MatchEditor
		SccLocalPath19 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.MatchEditor
		SccProjectUniqueName20 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.ParagonDataRequester\\DataLink.Utilities.ParagonDataRequester.vbproj
		SccProjectTopLevelParentUniqueName20 = AICMaster.sln
		SccProjectName20 = ../McKessonIntelligentCoding/DataLink.Utilities.ParagonDataRequester
		SccLocalPath20 = ..\\McKessonIntelligentCoding\\DataLink.Utilities.ParagonDataRequester
		SccProjectUniqueName21 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Business.Chart\\McKessonIntelligentCoding.Business.Chart.vbproj
		SccProjectTopLevelParentUniqueName21 = AICMaster.sln
		SccProjectName21 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Business.Chart
		SccLocalPath21 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Business.Chart
		SccProjectUniqueName22 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Contracts\\McKessonIntelligentCoding.Common.Contracts.vbproj
		SccProjectTopLevelParentUniqueName22 = AICMaster.sln
		SccProjectName22 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Common.Contracts
		SccLocalPath22 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Contracts
		SccProjectUniqueName23 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Logging\\McKessonIntelligentCoding.Common.Logging.vbproj
		SccProjectTopLevelParentUniqueName23 = AICMaster.sln
		SccProjectName23 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Common.Logging
		SccLocalPath23 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Logging
		SccProjectUniqueName24 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Utils\\McKessonIntelligentCoding.Common.Utils.vbproj
		SccProjectTopLevelParentUniqueName24 = AICMaster.sln
		SccProjectName24 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Common.Utils
		SccLocalPath24 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Common.Utils
		SccProjectUniqueName25 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Console\\McKessonIntelligentCoding.Console.vbproj
		SccProjectTopLevelParentUniqueName25 = AICMaster.sln
		SccProjectName25 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Console
		SccLocalPath25 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Console
		SccProjectUniqueName26 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj
		SccProjectTopLevelParentUniqueName26 = AICMaster.sln
		SccProjectName26 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Data
		SccLocalPath26 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data
		SccProjectUniqueName27 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data.Repository\\McKessonIntelligentCoding.Data.Repository.vbproj
		SccProjectTopLevelParentUniqueName27 = AICMaster.sln
		SccProjectName27 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Data.Repository
		SccLocalPath27 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data.Repository
		SccProjectUniqueName28 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.DataLink.UHS\\McKessonIntelligentCoding.DataLink.UHS.vbproj
		SccProjectTopLevelParentUniqueName28 = AICMaster.sln
		SccProjectName28 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.DataLink.UHS
		SccLocalPath28 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.DataLink.UHS
		SccProjectUniqueName29 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.ServiceLib\\McKessonIntelligentCoding.ServiceLib.vbproj
		SccProjectTopLevelParentUniqueName29 = AICMaster.sln
		SccProjectName29 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.ServiceLib
		SccLocalPath29 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.ServiceLib
		SccProjectUniqueName30 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Test.Business.Chart\\McKessonIntelligentCoding.Test.Business.Chart.vbproj
		SccProjectTopLevelParentUniqueName30 = AICMaster.sln
		SccProjectName30 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Test.Business.Chart
		SccLocalPath30 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Test.Business.Chart
		SccProjectUniqueName31 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Test.Data.Repository\\McKessonIntelligentCoding.Test.Data.Repository.vbproj
		SccProjectTopLevelParentUniqueName31 = AICMaster.sln
		SccProjectName31 = ../McKessonIntelligentCoding/McKessonIntelligentCoding.Test.Data.Repository
		SccLocalPath31 = ..\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Test.Data.Repository
		SccProjectUniqueName32 = ..\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj
		SccProjectTopLevelParentUniqueName32 = AICMaster.sln
		SccProjectName32 = ../Server/EnchartServer/ICCryptoHelper
		SccLocalPath32 = ..\\Server\\EnchartServer\\ICCryptoHelper
		SccProjectUniqueName33 = ..\\Server\\EnchartServer\\src\\EnchartServer\\EnchartServer.csproj
		SccProjectTopLevelParentUniqueName33 = AICMaster.sln
		SccProjectName33 = ../Server/EnchartServer/src/EnchartServer
		SccLocalPath33 = ..\\Server\\EnchartServer\\src\\EnchartServer
		SccProjectUniqueName34 = ..\\Server\\EnchartServer\\src\\EnchartServer.Business\\EnchartServer.Business.vbproj
		SccProjectTopLevelParentUniqueName34 = AICMaster.sln
		SccProjectName34 = ../Server/EnchartServer/src/EnchartServer.Business
		SccLocalPath34 = ..\\Server\\EnchartServer\\src\\EnchartServer.Business
		SccProjectUniqueName35 = ..\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj
		SccProjectTopLevelParentUniqueName35 = AICMaster.sln
		SccProjectName35 = ../Server/EnchartServer/src/EnchartServer.Data
		SccLocalPath35 = ..\\Server\\EnchartServer\\src\\EnchartServer.Data
		SccProjectUniqueName36 = ..\\Server\\EnchartServer\\src\\EnchartServer.ExternalComm\\EnchartServer.ExternalComm.vbproj
		SccProjectTopLevelParentUniqueName36 = AICMaster.sln
		SccProjectName36 = ../Server/EnchartServer/src/EnchartServer.ExternalComm
		SccLocalPath36 = ..\\Server\\EnchartServer\\src\\EnchartServer.ExternalComm
		SccProjectUniqueName37 = ..\\Server\\EnchartServer\\src\\EnchartServer.Repository\\EnchartServer.Repository.vbproj
		SccProjectTopLevelParentUniqueName37 = AICMaster.sln
		SccProjectName37 = ../Server/EnchartServer/src/EnchartServer.Repository
		SccLocalPath37 = ..\\Server\\EnchartServer\\src\\EnchartServer.Repository
		SccProjectUniqueName38 = ..\\Server\\EnchartServer\\src\\EnchartServer.Test\\EnchartServer.Test.vbproj
		SccProjectTopLevelParentUniqueName38 = AICMaster.sln
		SccProjectName38 = ../Server/EnchartServer/src/EnchartServer.Test
		SccLocalPath38 = ..\\Server\\EnchartServer\\src\\EnchartServer.Test
		SccProjectUniqueName39 = ..\\Server\\EnchartServer\\src\\ReportsEngine\\ReportsEngine.vbproj
		SccProjectTopLevelParentUniqueName39 = AICMaster.sln
		SccProjectName39 = ../Server/EnchartServer/src/ReportsEngine
		SccLocalPath39 = ..\\Server\\EnchartServer\\src\\ReportsEngine
		SccProjectUniqueName40 = ..\\DataLink.Assistant\\DataLink.Assistant\\DataLink.Assistant.vbproj
		SccProjectTopLevelParentUniqueName40 = AICMaster.sln
		SccProjectName40 = ../DataLink.Assistant/DataLink.Assistant
		SccLocalPath40 = ..\\DataLink.Assistant\\DataLink.Assistant
		SccProjectUniqueName41 = ..\\Timeout\\Timeout\\Timeout.vbproj
		SccProjectTopLevelParentUniqueName41 = AICMaster.sln
		SccProjectName41 = ../Timeout/Timeout
		SccLocalPath41 = ..\\Timeout\\Timeout
		SccProjectUniqueName42 = ..\\ENChartCAC\\ColdFeedExport\\McKesson.HIC.ChargeSummaryColdFeedHelper\\McKesson.HIC.ChargeSummaryColdFeedHelper.vbproj
		SccProjectTopLevelParentUniqueName42 = AICMaster.sln
		SccProjectName42 = ../ENChartCAC/ColdFeedExport/McKesson.HIC.ChargeSummaryColdFeedHelper
		SccLocalPath42 = ..\\ENChartCAC\\ColdFeedExport\\McKesson.HIC.ChargeSummaryColdFeedHelper
		SccProjectUniqueName43 = ..\\ENChartCAC\\ColdFeedExport\\McKeesson.HIC.HPFColdFeed.dll\\McKesson.HIC.HPFColdFeed.vbproj
		SccProjectTopLevelParentUniqueName43 = AICMaster.sln
		SccProjectName43 = ../ENChartCAC/ColdFeedExport/McKeesson.HIC.HPFColdFeed.dll
		SccLocalPath43 = ..\\ENChartCAC\\ColdFeedExport\\McKeesson.HIC.HPFColdFeed.dll
		SccLocalPath44 = .
	EndGlobalSection
EndGlobal

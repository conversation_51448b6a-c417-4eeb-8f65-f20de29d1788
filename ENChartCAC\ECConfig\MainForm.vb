Option Infer On

#Region "Imports..."

Imports EnchartDOLib
Imports System.Configuration
Imports DevExpress.Xpo
Imports DevExpress.Data
Imports DevExpress.Xpo.DB
Imports DevExpress.Data.Filtering
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports System.Security.Permissions
Imports System.Reflection

'Imports iAnywhere.Data.AsaClient
Imports System.Collections.Specialized
Imports System.Xml.Serialization
Imports System.Xml
'Imports HICLegacyDatabase
Imports System.Text

Imports System.Security.Cryptography
Imports SSP = System.Security.Principal

#End Region

Imports System.IO
Imports System.IO.FileStream
Imports Timeout
Imports System.ComponentModel
Imports ENChartCAC
Imports EnchartDOLib.SimpleResult
Imports System.Linq
Imports CDMReports
Imports System.Threading.Tasks
Imports System.Drawing
Imports System.Data
Imports Newtonsoft.Json
Imports Formatting = Newtonsoft.Json.Formatting
'Imports DevExpress.XtraPrinting

Public Class MainForm
    'Implements ITimeoutable

#Region "Command Line Constants"
    Private Const CLA_FACILITYSETTINGS As String = "-FS"
    Private Const CLA_FS_ADDNEW As String = "-ADDNEW"
#End Region


    Public ControlDefaultLocationDict As New Dictionary(Of Control, Point)

    Private Sub InitControlDefaultLocationDict()
        ' ControlDefaultLocationDict = New Dictionary(Of Control, Point)
        If ControlDefaultLocationDict.Count > 0 Then Return

        For Each c As Control In GroupControl15.Controls
            ControlDefaultLocationDict(c) = c.Location
        Next
    End Sub

    Private Async Sub MainForm_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Load
        HandleCommandLineArgs()

        DOConfigInstance.SharedActiveFormClassName = "ChartForm"

        Me.Text += " (V" & My.Application.Info.Version.ToString & ")"

        InitControlDefaultLocationDict()
        SetUpIdleTimeout()

        Await Task.Run(Sub() InitEncryptMenuOption())
    End Sub

    Private Sub HandleCommandLineArgs()
        IsProcessingCommandLineArgs = True

        If My.Application.CommandLineArgs.Count > 0 Then
            Dim clas As New List(Of String)
            For Each arg As String In My.Application.CommandLineArgs
                clas.Add(arg.ToUpper)
            Next

            If clas.Contains(CLA_FACILITYSETTINGS) Then
                If clas.Contains(CLA_FS_ADDNEW) Then AddNewFacilitySettings()
            End If
        End If

        IsProcessingCommandLineArgs = False
    End Sub

    Private IsProcessingCommandLineArgs As Boolean = False

    Sub ExportConfigToXML()
        Dim f As DOFacility = ECGlobals.CurrentFacility
        'ReportsDBTest()

        Dim ExportFileName As String = String.Format("{0} Config.hfc", Utils.StripValue(f.LongName))
        Dim fs As New FileStream(ExportFileName, FileMode.Create)
        Dim x As New XmlSerializer(GetType(FacilityProxy))

        Dim fp As New FacilityProxy(f, Nothing, Nothing)
        x.Serialize(fs, fp)
        fs.Flush()
        fs.Close()

        MessageBox.Show(String.Format("Facility configuration successfully exported to {0}", fs.Name), "JJC Still Rules", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Shared Function SafeSqlLiteral(ByVal inputSQL As String) As String
        Return inputSQL.Replace("'", "''")
    End Function

    ''' <summary>
    ''' Create the empty CDM Tables using the FacilityID and Treatment Area
    ''' </summary>
    ''' <param name="newCDMTableName"></param>
    ''' <remarks>
    '''
    '''REMEMBER - DON'T USE A USING CLAUSE ON THE CONNECTION OBJECT...
    '''OR WE'LL CLOSE IT AND NOT REOPEN IT!!!
    '''</remarks>
    Shared Sub CreateCDMTable(ByVal newCDMTableName As String)
        'there is no way for someone to manipulate the tablenames as we, the MIC Team, define them and store them in db...
        Dim conn As IDbConnection = DevExpress.Xpo.Session.DefaultSession.Connection
        Using comm As IDbCommand = conn.CreateCommand

            If conn.ConnectionString.ToUpper.Contains("INITIAL CATALOG") Then
                'Use syntax for SQL
                comm.CommandText = My.Resources.NewCreateCDMTableQuery.Replace("CDMTableName", SafeSqlLiteral(newCDMTableName))
            Else
                'Use syntax for Sybase
                comm.CommandText = My.Resources.CreateCDMTableQuery.Replace("CDMTableName", SafeSqlLiteral(newCDMTableName))
            End If

            comm.CommandType = CommandType.Text

            comm.ExecuteNonQuery() 'TODO VERICODE SQL Injection error... (not!)
        End Using
        '#End If
    End Sub

    Shared Sub DeleteAllRecordsFromCDMTable(ByVal newCDMTableName As String)
        Dim conn As IDbConnection = DevExpress.Xpo.Session.DefaultSession.Connection
        Using comm As IDbCommand = conn.CreateCommand
            comm.CommandText = "Delete From " & SafeSqlLiteral(newCDMTableName)
            comm.CommandType = CommandType.Text

            comm.ExecuteNonQuery() 'TODO VERICODE SQL Injection error... (not!)
        End Using
        '#End If
    End Sub

    Public Class DTOReportCategory
        Public OID As Integer
        <XmlIgnore()>
        Public Facility As Integer
        Public CategoryName As String

        Public Order As Integer
        Public ReportType As String

        Private _ParentCategory As DTOReportCategory
        'ParentCategory would  be null if it is a top level Category
        Public Property ParentCategory() As DTOReportCategory
            Get
                Return _ParentCategory
            End Get
            Set(ByVal value As DTOReportCategory)
                _ParentCategory = value
            End Set
        End Property

        Protected IsNew As Boolean
        Protected Expiration As Date = Nothing

        Sub New(ByVal ReportsDatabase)

        End Sub
    End Class

    Public Class DTOCategoryReportXRef
        Public OID

        Public Category As DTOReportCategory
        Public Report As DTOReportList

        Public Order As Integer
    End Class

    Public Class DTOReportList

        Public OID As Integer
        Public Facility As String
        Public ReportDescription As String
        Public ReportFilename As String
        Public ReportListOrder As Integer
        Public ReportType As String
        Public IsNew As Boolean
        Public Expiration As Date = Nothing
        Public ParentCategory As DTOReportList = Nothing

#Region "blah"
        Public Function IsExport() As Boolean
            Return (ReportType.ToLower.Contains("export"))
        End Function
        Public Function IsPhysician() As Boolean
            Return (ReportType.ToLower.Contains("physician"))
        End Function
        Public Function IsCategory() As Boolean
            Return (ReportType.ToLower.StartsWith("category"))
        End Function
        Public Function IsSubCategory() As Boolean
            Return (ReportType.ToLower.StartsWith("subcategory"))
        End Function
        Public Function IsFixedCategory() As Boolean
            Return (ReportType.ToLower.EndsWith("fixed"))
        End Function
        Public Function IsUserCategory() As Boolean
            Return (ReportType.ToLower.EndsWith("user"))
        End Function
        Public Function IsNewReportsCategory() As Boolean
            Return (ReportType.ToLower.EndsWith("newreports"))
        End Function
        Public Function IsEditable() As Boolean
            Return Not (IsNewReportsCategory() Or IsFixedCategory())
        End Function
        Public Overrides Function ToString() As String
            Return ReportDescription
        End Function

#End Region
        Public Sub New()
            ' MyBase.New()
        End Sub

        'Public Sub New(ByVal data As DataRow)
        '    OID = data("oid")
        '    Facility = data("facility")
        '    ReportDescription = NullStringCheck(data("reportdescription"))
        '    ReportFilename = NullStringCheck(data("reportfilename"))
        '    ReportListOrder = NullIntegerCheck(data("reportlistorder"))
        '    ReportType = NullStringCheck(data("reporttype"))
        'End Sub

    End Class

    Sub ReportsDBTest()
        'HICLegacyDatabase.DatabaseConnection.SetupConnection(XpoDefault.DataLayer.Connection)

        'Dim facilityOid As String = ECGlobals.CurrentFacility.Oid.ToString
        'Dim CategoryList() As HICLegacyDatabase.ReportList = ReportsDatabase.RetrieveReportCategories(facilityOid)
        'For Each Category In CategoryList
        '    Dim newCat As New DTOReportCategory(Category)
        '    Debug.WriteLine(Category.ReportDescription)
        '    For Each Report In HICLegacyDatabase.ReportsDatabase.RetrieveReportList(facilityOid, Category.OID, True)
        '        Debug.WriteLine(Report.ReportDescription)
        '    Next
        'Next
        'Debug.WriteLine(CategoryList.Length)
    End Sub

    Sub LoadConfigFromXML()
        Dim f As New FacilityConfigImportForm
        f.ShowDialog()

        f.Dispose()
        'GC.SuppressFinalize(f)
    End Sub

    Public Shared Sub DeleteList(Of t As XPBaseObject)(ByVal xpcol As XPCollection(Of t))
        Dim nlist As New List(Of t)(xpcol)

        For i As Integer = 0 To nlist.Count - 1
            nlist(i).Delete()
        Next i
    End Sub

    Public Class FacLookUp
        Public facilityPtr As DOFacility

        Public Property OID As Integer
        Public Property FacilityID As String

        Public Property Grid As Boolean
        Public Property Points As Boolean
        Public Property Infusion As Boolean
        Public Property Obs As Boolean
        Public Property Phys As Boolean

        Private _facPtr As DOFacility
        Public Property FacPtr() As DOFacility
            Get
                Return _facPtr
            End Get
            Set(ByVal value As DOFacility)
                _facPtr = value
            End Set
        End Property

        Private _EID As String
        Public Property EID() As String
            Get
                Return _EID
            End Get
            Set(ByVal value As String)
                _EID = value
            End Set
        End Property

        Private _Company As String
        Public Property Company() As String
            Get
                Return _Company
            End Get
            Set(ByVal value As String)
                _Company = value
            End Set
        End Property

        Private _CompanyClient As String
        Public Property CompanyClient() As String
            Get
                Return _CompanyClient
            End Get
            Set(ByVal value As String)
                _CompanyClient = value
            End Set
        End Property

        Private _Faciltiy As String
        Public Property Facility() As String
            Get
                Return _Faciltiy
            End Get
            Set(ByVal value As String)
                _Faciltiy = value
            End Set
        End Property

        Public Sub New(ByVal pfac As DOFacility)
            FacPtr = pfac
            EID = pfac.CompanyClient.Company.EnchartID.LongName
            Company = pfac.CompanyClient.Company.LongName
            CompanyClient = pfac.CompanyClient.LongName
            Facility = pfac.LongName
            OID = pfac.Oid
            FacilityID = pfac.FacilityID

            Dim lAppConfigGroup As DOConfigGroup = pfac.ConfigInstanceVersion.AppConfigGroup
            Infusion = lAppConfigGroup("EnableClinicMode", True).Enabled
            'If Not Infusion Then
            Grid = lAppConfigGroup("EnableEMShellTab", True).Enabled
            'End If

            Obs = lAppConfigGroup("EnableObservationTab", True).Enabled
            Dim obsStandAlone = lAppConfigGroup("ObservationStandAloneMode", True).Enabled
            If Not obsStandAlone Then
                Points = Not (Grid Or Infusion)
            End If
            Phys = lAppConfigGroup("EnablePhysicianTab", True).Enabled
        End Sub
    End Class

#Region "Fields and Properties"
    Private bFirstTimeActived As Boolean = True
    Dim eg As ECGlobals
    Dim ECForm As ENChartCAC.ChartBaseForm

    Private blInPending As Boolean = False
    Private blImportedConfig As Boolean = False
    Private blSavedConfig As Boolean = False

    Private prevFormClass As String
    Private prevComments As String
    Private prevActivDt As Date
    Private prevMajorDt As Date

    Public FacilityLookUPList As New List(Of FacLookUp)

    Protected _ConfigInstance As DOConfigInstance = Nothing 'pointer to final configInstance to be used.

    Private _ConfigInstancesEnabled As Boolean
    Public Property ConfigInstanceEnabled() As Boolean
        Get
            Return _ConfigInstancesEnabled
        End Get
        Set(ByVal value As Boolean)
            _ConfigInstancesEnabled = value
        End Set
    End Property

    Private _creatingNewCI As Boolean
    Public Property CreatingNewCI() As Boolean
        Get
            Return _creatingNewCI
        End Get
        Set(ByVal value As Boolean)
            _creatingNewCI = value
        End Set
    End Property

    Private _EditingPrevCI As Boolean
    Public Property EditingPrevCI() As Boolean
        Get
            Return _EditingPrevCI
        End Get
        Set(ByVal value As Boolean)
            _EditingPrevCI = value
        End Set
    End Property

    Protected _editingPendingConfiguration As Boolean
    Public Property EditingPendingConfiguration() As Boolean
        Get
            Return _editingPendingConfiguration
        End Get
        Set(ByVal Value As Boolean)
            _editingPendingConfiguration = Value
        End Set
    End Property

    Private _pendingConfigExists As Boolean
    Public Property PendingConfigExists() As Boolean
        Get
            Return _pendingConfigExists
        End Get
        Set(ByVal Value As Boolean)
            _pendingConfigExists = Value
        End Set
    End Property

#End Region

#Region "StartUp"

    Private Sub MainForm_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Activated
        If bFirstTimeActived Then
            ECForm = New ENChartCAC.ChartFormV15
            Me.Text = My.Application.Info.AssemblyName & " Ver: " & My.Application.Info.Version.ToString
            bFirstTimeActived = False
            Me.bbiConnect.Caption = "Connecting"
            Timer1.Interval = 250
            Timer1.Start()
        End If
    End Sub

    Private Sub testing()
        Throw New System.NotImplementedException()
    End Sub
    Sub testing2()
        Dim lblah As Boolean = False

        testing()
    End Sub

    Private Sub bbiConnect_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiConnect.ItemClick
        ConnectToDatabase()
    End Sub

    Sub ConnectToDatabase()
        Try
            If Me.bbiConnect.Caption = "Connect" OrElse Me.bbiConnect.Caption = "Connecting" Then
                whileConn(False)
                Me.bbiConnect.Caption = "Connecting..."
                My.Application.DoEvents()
                EnchartDOLib.Utils.ConnectToECDataBase()
                bbiConnect.Enabled = False
                Me.bsiFacilityLabel.Enabled = True
                Me.beiFacility2.Enabled = True
                InitFacilitySelect()
                Me.bbiConnect.Caption = "Disconnect"
                'If EnchartDOLib.Utils.IsDbSchemaUpdateNeeded() Then

                'End If
                'UpdateSchema()
                whileConn(True)
            End If
        Catch ex As Exception
            Dim fail As New FailedResult(Of Boolean) With {.ErrorException = ex}
            MessageBox.Show(Me, $"An unexpected error was encountered trying to connect to the Database: {vbCrLf}{vbCrLf}{fail.GetExceptionChainMessage}", "Database Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Application.Exit()
        End Try

    End Sub

    Private Sub UpdateSchema()

        Try
            Cursor.Current = Cursors.WaitCursor
            'EnchartDOLib.Utils.ConnectToECDataBase()
            EnchartDOLib.Utils.InitDbConnectionAndUpdateSchema()
            Debug.Assert(XpoDefault.Session IsNot Nothing)
            Session.DefaultSession?.UpdateSchema()
        Catch ex As Exception

        Finally
            Cursor.Current = DefaultCursor
            MessageBox.Show($"Database Schema was successfully updated! {vbCrLf} The AppPool in ESS should be restarted to pickup the changes.", "StatusUpdate", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Try

    End Sub

    Public Sub whileConn(ByVal blTrue As Boolean)

        Me.BarSubItem2.Enabled = blTrue
        Me.BarSubItem3.Enabled = blTrue

    End Sub

    Sub InitFacilitySelect()

        FacilityLookUPList.Clear()

        'I have no idea why i should have to create a new list, but when adding a new facility
        ' and then immediately selecting it, the facility select control displays a blank value...
        FacilityLookUPList = New List(Of FacLookUp) 'jjc 06.28.2010

        Dim fcol As New XPCollection(GetType(DOFacility))
        fcol.Reload()

        For Each f As DOFacility In fcol
            FacilityLookUPList.Add(New FacLookUp(f))
        Next

        ' Me.beiFacLookUp.DataSource = 
        RepositoryItemSearchLookUpEdit1.DataSource = FacilityLookUPList
        ' SearchLookUpEdit2.properties.DataSource = FacilityLookUPList
        'Me.beiFacLookUp.BestFit()
        Me.beiFacLookUp.DisplayMember = "Facility"
        Me.beiFacLookUp.ValueMember = "FacPtr"

        If FacilityLookUPList.Count > 0 Then
            'Me.beiFacLookUp.
            beiFacility2.EditValue = FacilityLookUPList(0).FacPtr
        End If

        'Me.beiFacLookUp.Columns.Add(
        'Me.beiFacLookUp.PopulateColumns()
    End Sub

    Sub InitTabs()

        Dim dupptr = New DuplicatePointer(teDefaultProfeeV2PhysChartStatus, teDefaultPhysChartStatus)
        dupptr = New DuplicatePointer(ProfeeV2_Summary_SwapModifierschk, cePhysSwapModWithDx)
        dupptr = New DuplicatePointer(ceProfeeV2UseEMGrid, ceEnableEMGrid)

        InitGeneralTab()
        InitTimesTab()
        InitQITab()
        InitRequiredFields()
        InitMisc()
        InitPhysQITab()
        InitObservationTab()
        InitEMShellTab()
        InitClinicTab()
        InitTreatmentAreas()
        InitUrgentCareTab()
        ' gcControlOverride.DataSource = ECGlobals.CurrentConfigInstance.ControlOverrides



    End Sub


    Sub SaveAllTabs()

        SaveUgrentCareTab()
        SaveRequiredFields()
        Save_TimesTab()
        SaveQITab()
        SaveGeneralTab()
        SaveMiscTab()
        SavePhysQITab()
        SaveObsTab()
        SaveEMShellTab()
        SaveClinicTab()


        'lazy testing....
        For Each cs As DOConfigSetting In ECGlobals.CurrentConfigInstance.AppConfigGroup.Settings 'lAppConfigGroup.Settings
            cs.Save()
        Next

        MessageBox.Show("Changes saved successfully", "Saving...", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ''<FileDialogPermission(SecurityAction.Assert, Unrestricted:=True)> _
    'Function GetFacility(ByVal oid As Integer) As DOFacility
    '    Dim fcol As New XPCollection(GetType(DOFacility), CriteriaOperator.Parse(String.Format("oid = '{0}'", oid)))
    '    Return fcol(0)
    'End Function

#End Region 'Startup

    Public Class DOControlConfigGroupMiscTab
        ' Inherits DOControlConfigGroup

        Sub New(ByVal pCOGroup As DOControlConfigGroup)
            COGroup = pCOGroup
        End Sub

        Private _COGroup As DOControlConfigGroup
        Public Property COGroup() As DOControlConfigGroup
            Get
                Return _COGroup
            End Get
            Set(ByVal value As DOControlConfigGroup)
                _COGroup = value
            End Set
        End Property

        Sub LoadUDCheckBox(ByVal chk As CheckEdit, ByVal te As TextEdit, ByVal req As CheckEdit)
            Dim OverrideName As String = chk.Name

            Dim cor As DOControlConfig = COGroup(OverrideName, True)

            If cor IsNot Nothing Then
                chk.Checked = cor.Enabled
                If Not String.IsNullOrEmpty(cor.DisplayText) Then
                    te.Text = cor.DisplayText
                Else
                    te.Text = chk.Name
                End If
                req.Checked = cor.IsRequiredOverride
                'te.Text = cor.DisplayText
            End If
        End Sub

        Public Sub SaveUDCheckBox(ByVal chk As CheckEdit, ByVal te As TextEdit, ByVal req As CheckEdit)
            Dim OverrideName As String = chk.Name
            Dim cor As DOControlConfig = COGroup(OverrideName, True)

            cor.Enabled = chk.Checked
            cor.DisplayText = te.EditValue
            cor.IsRequiredOverride = req.Checked

            cor.Save()

        End Sub

        Sub LoadUDCombo(ByVal chk As CheckEdit, ByVal te As TextEdit, ByVal cbo As ComboBoxEdit, ByVal req As CheckEdit)
            cbo.Properties.DropDownRows = 25
            Dim YPos As Integer
            Dim OverrideName As String = te.Name
            OverrideName = OverrideName.Substring(0, OverrideName.Length - 3)

            Dim cor As DOControlConfig = COGroup(OverrideName, True) 'Load the control override for the label control associated with the combobox

            If cor IsNot Nothing Then
                chk.Checked = cor.Enabled
                If Not String.IsNullOrEmpty(cor.DisplayText) Then
                    te.Text = cor.DisplayText
                Else
                    te.Text = cbo.Name
                End If

                YPos = cor.YPosMod

                If YPos > 0 Then
                    chk.Location = New Point(chk.Location.X, YPos + cboOffset)
                    te.Location = New Point(te.Location.X, YPos + cboOffset)
                End If
            End If

            OverrideName = cbo.Name
            cor = COGroup(OverrideName, True) 'load the combobox override
            If cor IsNot Nothing Then
                If YPos > 0 Then
                    cbo.Location = New Point(cbo.Location.X, YPos + cboOffset)
                    req.Location = New Point(req.Location.X, YPos + cboOffset)

                End If
                req.Checked = cor.IsRequiredOverride
                cbo.EditValue = cor.DisplayText

                'find list for this cbo and populate list? just as a remidner.. ?
                Dim cbolist As DOConfigComboBoxList = ECGlobals.CurrentConfigInstance.GetComboBoxListByControlName(cbo.Name)
                If cbolist IsNot Nothing Then
                    cbo.Tag = cbolist
                    cbo.ToolTip = cbolist.ListName
                    cbo.Properties.Items.Clear()

                    If cbo Is Nothing Then Return

                    ' If Me.ChartIsNotLoading Then cbo.EditValue = ""
                    cbo.Properties.Items.Add("")
                    For Each cboListItem As DOConfigComboBoxListsItem In cbolist.ListItems
                        'If cbo IsNot Nothing And cboListItem.Enabled Then
                        If cboListItem.Enabled Then
                            cbo.Properties.Items.Add(cboListItem.ItemDisplayName)
                        End If
                    Next
                End If

            End If
        End Sub

        Dim cboOffset As Integer = 25 ' to offset the positions to better mirror the UI
        'which doesn't have the Group control or header...

        Sub SaveUDCombo(ByVal chk As CheckEdit, ByVal te As TextEdit, ByVal cbo As ComboBoxEdit, ByVal req As CheckEdit)
            Dim OverrideName As String = te.Name
            OverrideName = OverrideName.Substring(0, OverrideName.Length - 3)

            Dim cor As DOControlConfig = COGroup(OverrideName, True)

            If cor IsNot Nothing Then
                cor.Enabled = chk.Checked
                cor.DisplayText = te.EditValue

                'Try
                '    If My.Forms.MainForm.ControlDefaultLocationDict(te) = te.Location Then
                '        'if the control is back at it's default location, just clear it...
                '        cor.YPosMod = 0
                '    Else
                '        cor.YPosMod = te.Location.Y
                '    End If
                'Catch
                '    cor.YPosMod = 0
                'End Try

                If My.Forms.MainForm.ceUseAlternatePositions.Checked Then
                    cor.YPosMod = te.Location.Y - cboOffset
                Else
                    cor.YPosMod = 0 'tells UI to ignore xy coords
                End If

                cor.Save()

                'chk.Checked = cor.Enabled
                'te.Text = cor.DisplayText
            End If

            OverrideName = cbo.Name
            cor = COGroup(OverrideName, True)
            If cor IsNot Nothing Then
                cor.Enabled = chk.Checked
                cor.DisplayText = cbo.EditValue
                cor.IsRequiredOverride = req.Checked
                'If My.Forms.MainForm.ControlDefaultLocationDict(cbo) = cbo.Location Then
                '    'if the control is back at it's default location, just clear it...
                '    cor.YPosMod = 0
                'Else
                '    cor.YPosMod = cbo.Location.Y
                'End If
                If My.Forms.MainForm.ceUseAlternatePositions.Checked Then
                    cor.YPosMod = cbo.Location.Y - cboOffset
                Else
                    cor.YPosMod = 0
                End If
                cor.Save()
            End If
        End Sub

    End Class

    Sub InitMisc()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")
        Me.ceUseAlternatePositions.Checked = lAppConfigGroup("Use UD Combo Alt Positions", True).Enabled

        Dim lMisc As New DOControlConfigGroupMiscTab(GetControlConfigGroup(GetMiscGroupName))

        lMisc.LoadUDCheckBox(Miscellaneous_UD_01_chk, Miscellaneous_UD_01_chk_te, Miscellaneous_UD_01_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_02_chk, Miscellaneous_UD_02_chk_te, Miscellaneous_UD_02_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_03_chk, Miscellaneous_UD_03_chk_te, Miscellaneous_UD_03_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_04_chk, Miscellaneous_UD_04_chk_te, Miscellaneous_UD_04_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_05_chk, Miscellaneous_UD_05_chk_te, Miscellaneous_UD_05_chk_Req_chk)

        lMisc.LoadUDCheckBox(Miscellaneous_UD_06_chk, Miscellaneous_UD_06_chk_te, Miscellaneous_UD_06_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_07_chk, Miscellaneous_UD_07_chk_te, Miscellaneous_UD_07_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_08_chk, Miscellaneous_UD_08_chk_te, Miscellaneous_UD_08_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_09_chk, Miscellaneous_UD_09_chk_te, Miscellaneous_UD_09_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_10_chk, Miscellaneous_UD_10_chk_te, Miscellaneous_UD_10_chk_Req_chk)

        lMisc.LoadUDCheckBox(Miscellaneous_UD_11_chk, Miscellaneous_UD_11_chk_te, Miscellaneous_UD_11_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_12_chk, Miscellaneous_UD_12_chk_te, Miscellaneous_UD_12_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_13_chk, Miscellaneous_UD_13_chk_te, Miscellaneous_UD_13_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_14_chk, Miscellaneous_UD_14_chk_te, Miscellaneous_UD_14_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_15_chk, Miscellaneous_UD_15_chk_te, Miscellaneous_UD_15_chk_Req_chk)

        lMisc.LoadUDCheckBox(Miscellaneous_UD_16_chk, Miscellaneous_UD_16_chk_te, Miscellaneous_UD_16_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_17_chk, Miscellaneous_UD_17_chk_te, Miscellaneous_UD_17_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_18_chk, Miscellaneous_UD_18_chk_te, Miscellaneous_UD_18_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_19_chk, Miscellaneous_UD_19_chk_te, Miscellaneous_UD_19_chk_Req_chk)
        lMisc.LoadUDCheckBox(Miscellaneous_UD_20_chk, Miscellaneous_UD_20_chk_te, Miscellaneous_UD_20_chk_Req_chk)

        lMisc.LoadUDCombo(MiscShowCbo01_chk, Miscellaneous_UD_01_lbl_te, Miscellaneous_UD_01_cbo, Miscellaneous_UD_01_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo02_chk, Miscellaneous_UD_02_lbl_te, Miscellaneous_UD_02_cbo, Miscellaneous_UD_02_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo03_chk, Miscellaneous_UD_03_lbl_te, Miscellaneous_UD_03_cbo, Miscellaneous_UD_03_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo04_chk, Miscellaneous_UD_04_lbl_te, Miscellaneous_UD_04_cbo, Miscellaneous_UD_04_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo05_chk, Miscellaneous_UD_05_lbl_te, Miscellaneous_UD_05_cbo, Miscellaneous_UD_05_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo06_chk, Miscellaneous_UD_06_lbl_te, Miscellaneous_UD_06_cbo, Miscellaneous_UD_06_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo07_chk, Miscellaneous_UD_07_lbl_te, Miscellaneous_UD_07_cbo, Miscellaneous_UD_07_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo08_chk, Miscellaneous_UD_08_lbl_te, Miscellaneous_UD_08_cbo, Miscellaneous_UD_08_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo09_chk, Miscellaneous_UD_09_lbl_te, Miscellaneous_UD_09_cbo, Miscellaneous_UD_09_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo10_chk, Miscellaneous_UD_10_lbl_te, Miscellaneous_UD_10_cbo, Miscellaneous_UD_10_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo11_chk, Miscellaneous_UD_11_lbl_te, Miscellaneous_UD_11_cbo, Miscellaneous_UD_11_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo12_chk, Miscellaneous_UD_12_lbl_te, Miscellaneous_UD_12_cbo, Miscellaneous_UD_12_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo13_chk, Miscellaneous_UD_13_lbl_te, Miscellaneous_UD_13_cbo, Miscellaneous_UD_13_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo14_chk, Miscellaneous_UD_14_lbl_te, Miscellaneous_UD_14_cbo, Miscellaneous_UD_14_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo15_chk, Miscellaneous_UD_15_lbl_te, Miscellaneous_UD_15_cbo, Miscellaneous_UD_15_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo16_chk, Miscellaneous_UD_16_lbl_te, Miscellaneous_UD_16_cbo, Miscellaneous_UD_16_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo17_chk, Miscellaneous_UD_17_lbl_te, Miscellaneous_UD_17_cbo, Miscellaneous_UD_17_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo18_chk, Miscellaneous_UD_18_lbl_te, Miscellaneous_UD_18_cbo, Miscellaneous_UD_18_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo19_chk, Miscellaneous_UD_19_lbl_te, Miscellaneous_UD_19_cbo, Miscellaneous_UD_19_cbo_Req_chk)
        lMisc.LoadUDCombo(MiscShowCbo20_chk, Miscellaneous_UD_20_lbl_te, Miscellaneous_UD_20_cbo, Miscellaneous_UD_20_cbo_Req_chk)

        InitMiscTab()

    End Sub

    Function GetMiscGroupName() As String
        If GetControlConfigGroup("Miscellaneous", False) IsNot Nothing Then
            If GetControlConfigGroup("Miscellaneous", False).Enabled Then
                Return "Miscellaneous"
            End If
        End If

        If GetControlConfigGroup("Misc", False) IsNot Nothing Then
            If GetControlConfigGroup("Misc", False).Enabled Then
                Return "Misc"
            End If
        End If

        Return "Miscellaneous"
    End Function

    Sub SaveMiscTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")
        '  Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup
        lAppConfigGroup("Use UD Combo Alt Positions", True).Enabled = Me.ceUseAlternatePositions.Checked

        Dim lMisc As New DOControlConfigGroupMiscTab(GetControlConfigGroup(GetMiscGroupName))
        lMisc.COGroup.Save()

        lMisc.SaveUDCheckBox(Miscellaneous_UD_01_chk, Miscellaneous_UD_01_chk_te, Miscellaneous_UD_01_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_02_chk, Miscellaneous_UD_02_chk_te, Miscellaneous_UD_02_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_03_chk, Miscellaneous_UD_03_chk_te, Miscellaneous_UD_03_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_04_chk, Miscellaneous_UD_04_chk_te, Miscellaneous_UD_04_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_05_chk, Miscellaneous_UD_05_chk_te, Miscellaneous_UD_05_chk_Req_chk)

        lMisc.SaveUDCheckBox(Miscellaneous_UD_06_chk, Miscellaneous_UD_06_chk_te, Miscellaneous_UD_06_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_07_chk, Miscellaneous_UD_07_chk_te, Miscellaneous_UD_07_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_08_chk, Miscellaneous_UD_08_chk_te, Miscellaneous_UD_08_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_09_chk, Miscellaneous_UD_09_chk_te, Miscellaneous_UD_09_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_10_chk, Miscellaneous_UD_10_chk_te, Miscellaneous_UD_10_chk_Req_chk)

        lMisc.SaveUDCheckBox(Miscellaneous_UD_11_chk, Miscellaneous_UD_11_chk_te, Miscellaneous_UD_11_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_12_chk, Miscellaneous_UD_12_chk_te, Miscellaneous_UD_12_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_13_chk, Miscellaneous_UD_13_chk_te, Miscellaneous_UD_13_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_14_chk, Miscellaneous_UD_14_chk_te, Miscellaneous_UD_14_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_15_chk, Miscellaneous_UD_15_chk_te, Miscellaneous_UD_15_chk_Req_chk)

        lMisc.SaveUDCheckBox(Miscellaneous_UD_16_chk, Miscellaneous_UD_16_chk_te, Miscellaneous_UD_16_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_17_chk, Miscellaneous_UD_17_chk_te, Miscellaneous_UD_17_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_18_chk, Miscellaneous_UD_18_chk_te, Miscellaneous_UD_18_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_19_chk, Miscellaneous_UD_19_chk_te, Miscellaneous_UD_19_chk_Req_chk)
        lMisc.SaveUDCheckBox(Miscellaneous_UD_20_chk, Miscellaneous_UD_20_chk_te, Miscellaneous_UD_20_chk_Req_chk)

        'lMisc.SaveUDCombo(MiscShowCbo01_chk, Miscellaneous_UD_01_lbl_te, Miscellaneous_UD_01_cbo)
        'lMisc.SaveUDCombo(MiscShowCbo02_chk, Miscellaneous_UD_02_lbl_te, Miscellaneous_UD_02_cbo)

        lMisc.SaveUDCombo(MiscShowCbo01_chk, Miscellaneous_UD_01_lbl_te, Miscellaneous_UD_01_cbo, Miscellaneous_UD_01_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo02_chk, Miscellaneous_UD_02_lbl_te, Miscellaneous_UD_02_cbo, Miscellaneous_UD_02_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo03_chk, Miscellaneous_UD_03_lbl_te, Miscellaneous_UD_03_cbo, Miscellaneous_UD_03_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo04_chk, Miscellaneous_UD_04_lbl_te, Miscellaneous_UD_04_cbo, Miscellaneous_UD_04_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo05_chk, Miscellaneous_UD_05_lbl_te, Miscellaneous_UD_05_cbo, Miscellaneous_UD_05_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo06_chk, Miscellaneous_UD_06_lbl_te, Miscellaneous_UD_06_cbo, Miscellaneous_UD_06_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo07_chk, Miscellaneous_UD_07_lbl_te, Miscellaneous_UD_07_cbo, Miscellaneous_UD_07_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo08_chk, Miscellaneous_UD_08_lbl_te, Miscellaneous_UD_08_cbo, Miscellaneous_UD_08_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo09_chk, Miscellaneous_UD_09_lbl_te, Miscellaneous_UD_09_cbo, Miscellaneous_UD_09_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo10_chk, Miscellaneous_UD_10_lbl_te, Miscellaneous_UD_10_cbo, Miscellaneous_UD_10_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo11_chk, Miscellaneous_UD_11_lbl_te, Miscellaneous_UD_11_cbo, Miscellaneous_UD_11_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo12_chk, Miscellaneous_UD_12_lbl_te, Miscellaneous_UD_12_cbo, Miscellaneous_UD_12_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo13_chk, Miscellaneous_UD_13_lbl_te, Miscellaneous_UD_13_cbo, Miscellaneous_UD_13_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo14_chk, Miscellaneous_UD_14_lbl_te, Miscellaneous_UD_14_cbo, Miscellaneous_UD_14_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo15_chk, Miscellaneous_UD_15_lbl_te, Miscellaneous_UD_15_cbo, Miscellaneous_UD_15_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo16_chk, Miscellaneous_UD_16_lbl_te, Miscellaneous_UD_16_cbo, Miscellaneous_UD_16_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo17_chk, Miscellaneous_UD_17_lbl_te, Miscellaneous_UD_17_cbo, Miscellaneous_UD_17_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo18_chk, Miscellaneous_UD_18_lbl_te, Miscellaneous_UD_18_cbo, Miscellaneous_UD_18_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo19_chk, Miscellaneous_UD_19_lbl_te, Miscellaneous_UD_19_cbo, Miscellaneous_UD_19_cbo_Req_chk)
        lMisc.SaveUDCombo(MiscShowCbo20_chk, Miscellaneous_UD_20_lbl_te, Miscellaneous_UD_20_cbo, Miscellaneous_UD_20_cbo_Req_chk)

    End Sub

    Sub InitGeneralTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        teMinVersion.EditValue = GetUpdaterObj.CurAppVer

        If lAppConfigGroup("ChartID Mask", True).Enabled Then
            Me.teEditMask.EditValue = lAppConfigGroup("ChartID Mask", True).SettingValue
        Else
            Me.teEditMask.EditValue = ""
        End If

        If lAppConfigGroup("SuppliesMEMask", True).Enabled Then
            Me.teSuppliesEditMask.EditValue = lAppConfigGroup("SuppliesMEMask", True).SettingValue
        Else
            Me.teSuppliesEditMask.EditValue = ""
            lAppConfigGroup("SuppliesMEMask", True).Enabled = True
        End If

        If lAppConfigGroup("SuppliesMEMaskTooltipMsg", True).Enabled Then
            Me.teEditMaskTooltip.EditValue = lAppConfigGroup("SuppliesMEMaskTooltipMsg", True).SettingValue
        Else
            Me.teEditMaskTooltip.EditValue = "The entered value is not in the required format."
            lAppConfigGroup("SuppliesMEMaskTooltipMsg", True).Enabled = True
        End If

        'Supplies
        'lAppConfigGroup("SuppliesMEMask", True).SettingValue = Me.teSuppliesEditMask.EditValue
        'lAppConfigGroup("SuppliesMEMaskTooltipMsg", True).SettingValue = Me.teEditMaskTooltip.EditValue

        If lAppConfigGroup("UseConfigInstances", True).Enabled Then
            Me.ceUseConfigInstances.Checked = True 'lAppConfigGroup("UseConfigInstances", True).SettingValue
        Else
            Me.ceUseConfigInstances.Checked = False
        End If
        If lAppConfigGroup("DefaultChartStatus", True).Enabled Then
            Me.teDefaultChartStatus.EditValue = lAppConfigGroup("DefaultChartStatus").SettingValue
        Else
            Me.teDefaultChartStatus.EditValue = ""
        End If
        'ChartLockTimeOut

        If lAppConfigGroup("ChartLockTimeOut", True).Enabled Then
            Me.seChartLockTimeout.EditValue = lAppConfigGroup("ChartLockTimeOut").SettingValue
        Else
            lAppConfigGroup("ChartLockTimeOut").Enabled = True
            Me.seChartLockTimeout.EditValue = 8
        End If

        If lAppConfigGroup("Pediatric Age", True).Enabled Then
            Me.sePediatricAge.EditValue = lAppConfigGroup("Pediatric Age").SettingValue
        Else
            lAppConfigGroup("Pediatric Age").Enabled = True
            Me.sePediatricAge.EditValue = 5
        End If

        If lAppConfigGroup("PatientListRefreshTimeout", True).Enabled Then
            Me.seAPLAutoRefreshInterval.EditValue = lAppConfigGroup("PatientListRefreshTimeout").SettingValue
        Else
            lAppConfigGroup("PatientListRefreshTimeout").Enabled = True
            Me.seAPLAutoRefreshInterval.EditValue = 60
        End If

        If lAppConfigGroup("IgnoreInfusionLocalWD", True).Enabled Then
            Me.ceIgnoreInfustionLWD.Checked = True 'lAppConfigGroup("UseConfigInstances", True).SettingValue
        Else
            Me.ceIgnoreInfustionLWD.Checked = False
        End If

        If lAppConfigGroup("Translator Listening IPAddress", True).Enabled Then
            Me.teTransAddress.EditValue = lAppConfigGroup("Translator Listening IPAddress").SettingValue
            If lAppConfigGroup("Translator Listening IPAddress")("Port").Enabled Then
                Me.teInbTransPort.EditValue = lAppConfigGroup("Translator Listening IPAddress")("Port").PropertyValue
            Else
                Me.teInbTransPort.EditValue = ""
            End If
        Else
            'lAppConfigGroup("Translator Listening IPAddress").Enabled = True
            Me.teTransAddress.EditValue = ""
            Me.teInbTransPort.EditValue = ""

        End If

        Try
            Dim d As DOUpdater = GetUpdaterObj()
            If d IsNot Nothing Then
                Me.teUpdateDir.EditValue = d.UpdatePath
            Else
                Me.teUpdateDir.EditValue = ""
            End If

        Catch ex As Exception

        End Try

        If lAppConfigGroup("UseNewMedicationsConfig", True).Enabled Then
            Me.ceUseNewMedicationsTable.Checked = True 'lAppConfigGroup("UseConfigInstances", True).SettingValue
        Else
            Me.ceUseNewMedicationsTable.Checked = False
        End If

        If lAppConfigGroup("NumberOfDropDownRows", True).Enabled Then
            Me.seDropDownRows.EditValue = lAppConfigGroup("NumberOfDropDownRows").SettingValue

            Dim rows As Integer
            Try
                rows = Me.seDropDownRows.EditValue
            Catch
                'hide!
            End Try

            If rows < 7 Then
                Me.seDropDownRows.EditValue = 7
            End If
        Else
            lAppConfigGroup("NumberOfDropDownRows").Enabled = True
            Me.seDropDownRows.EditValue = 7
        End If

        Me.ceComboBoxImmediatePopup.Checked = lAppConfigGroup("ComboBoxImmediatePopup", True).Enabled

        Me.ceAllowFutureDates.Checked = lAppConfigGroup("AllowFutureDates", True).Enabled

        If lAppConfigGroup("TALeaveBlank", True).Enabled Then
            Me.ceTALeaveBlank.Checked = True
        Else
            Me.ceTALeaveBlank.Checked = False
        End If

        If lAppConfigGroup("EnableColdFeed", True).Enabled Then
            Me.ceEnableColdFeedExport.Checked = True
        Else
            Me.ceEnableColdFeedExport.Checked = False
        End If
        'ceEnableColdFeedExport

        ceEnableFinancialClassMapping.Checked = lAppConfigGroup("EnableFinancialClassMappings", True).Enabled

        ceEnableSeriesCharts.Checked = lAppConfigGroup("EnableSeriesVisits", True).Enabled
        ceHideSuppliesTab.Checked = lAppConfigGroup("HideSuppliesTab", True).Enabled
        ceHideFacilityModifiersTab.Checked = lAppConfigGroup("HideFacilityModifiersTab", True).Enabled

        ceUseGlobalMedicationsTable.Checked = lAppConfigGroup("UseGlobalMedicationsTable", True).Enabled

        'ceHideSuppliesTab

    End Sub

    Public Shared Function GetUpdaterObj() As DOUpdater

        Dim u As New XPCollection(Of DOUpdater)
        If u.Count > 1 Then
            Console.WriteLine("There needs to be one and only one record in the DOUpdater Table")
            '    Application.Exit()
            Return Nothing
        End If

        If u.Count = 1 Then
            Return u(0)
        Else
            Dim tu As New DOUpdater
            tu.UpdateVer = 1
            tu.Save()
            Return tu
        End If

    End Function

    'Sub CheckEnableDefaultCI(ByVal plAppConfigGroup As DOConfigGroup)
    '    If plAppConfigGroup("UseConfigInstances").Enabled = True Then
    '        ECGlobals.CurrentConfigInstance.get()
    '    End If

    'End Sub

    Sub SaveGeneralTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup 'CurrentFacility.ConfigInstanceVersion.GetConfigSettingGroup("Application")

        ECGlobals.CurrentConfigInstance.Enabled = True

        Dim uo = GetUpdaterObj()
        uo.CurAppVer = teMinVersion.EditValue
        uo.Save()

        lAppConfigGroup("UseConfigInstances").Enabled = ceUseConfigInstances.Checked
        'CheckEnableDefaultCI(lAppConfigGroup)

        lAppConfigGroup("ChartID Mask", True).SettingValue = Me.teEditMask.EditValue

        'Supplies
        lAppConfigGroup("SuppliesMEMask", True).SettingValue = Me.teSuppliesEditMask.EditValue
        lAppConfigGroup("SuppliesMEMaskTooltipMsg", True).SettingValue = Me.teEditMaskTooltip.EditValue

        Dim doChartStatus As DOConfigSetting = lAppConfigGroup("DefaultChartStatus")

        If String.IsNullOrEmpty(Me.teDefaultChartStatus.EditValue) Then
            doChartStatus.Enabled = False
        Else
            doChartStatus.Enabled = True
        End If

        doChartStatus.SettingValue = Me.teDefaultChartStatus.EditValue

        lAppConfigGroup("ChartLockTimeOut").Enabled = True
        lAppConfigGroup("ChartLockTimeOut").SettingValue = Me.seChartLockTimeout.EditValue

        lAppConfigGroup("Pediatric Age").Enabled = True
        lAppConfigGroup("Pediatric Age").SettingValue = Me.sePediatricAge.EditValue
        'seAPLAutoRefreshInterval
        lAppConfigGroup("PatientListRefreshTimeout").Enabled = True
        lAppConfigGroup("PatientListRefreshTimeout").SettingValue = Me.seAPLAutoRefreshInterval.EditValue

        lAppConfigGroup("IgnoreInfusionLocalWD").Enabled = ceIgnoreInfustionLWD.Checked

        Dim tla As DOConfigSetting = lAppConfigGroup("Translator Listening IPAddress", True)
        tla.Enabled = True
        tla.SettingValue = Me.teTransAddress.EditValue

        Dim tlap As DOConfigPropertySetting = tla("Port", True)

        If String.IsNullOrEmpty(Me.teInbTransPort.EditValue) Then
            tlap.Enabled = False
        Else
            tlap.Enabled = True
            tlap.PropertyValue = Me.teInbTransPort.EditValue
        End If

        Try
            'This really should get saved automatically ( i thought) when it's parent object gets saved... but that does not
            'apper to be happening...(06.30.09)
            tlap.Save()
        Catch ex As Exception
            MessageBox.Show("Error occurred trying to save Inbound Translator Port", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Try
            Dim d As DOUpdater = GetUpdaterObj()
            If d IsNot Nothing Then
                d.UpdatePath = Me.teUpdateDir.EditValue
            Else
                d.UpdatePath = ""
            End If
            d.Save()
        Catch ex As Exception

        End Try

        'UseNewMedicationsConfig
        lAppConfigGroup("UseNewMedicationsConfig").Enabled = Me.ceUseNewMedicationsTable.Checked

        'NumberOfDropDownRows
        lAppConfigGroup("NumberOfDropDownRows").Enabled = True
        lAppConfigGroup("NumberOfDropDownRows").SettingValue = Me.seDropDownRows.EditValue

        lAppConfigGroup("ComboBoxImmediatePopup", True).Enabled = Me.ceComboBoxImmediatePopup.Checked
        lAppConfigGroup("AllowFutureDates", True).Enabled = Me.ceAllowFutureDates.Checked

        lAppConfigGroup("TALeaveBlank").Enabled = Me.ceTALeaveBlank.Checked

        '05.15.10
        lAppConfigGroup("EnableColdFeed").Enabled = Me.ceEnableColdFeedExport.Checked

        lAppConfigGroup("EnableFinancialClassMappings", True).Enabled = ceEnableFinancialClassMapping.Checked

        lAppConfigGroup("EnableSeriesVisits", True).Enabled = ceEnableSeriesCharts.Checked
        lAppConfigGroup("HideSuppliesTab", True).Enabled = ceHideSuppliesTab.Checked
        lAppConfigGroup("HideFacilityModifiersTab", True).Enabled = ceHideFacilityModifiersTab.Checked

        lAppConfigGroup("UseGlobalMedicationsTable", True).Enabled = ceUseGlobalMedicationsTable.Checked
        '  ceUseGlobalMedicationsTable.Checked = lAppConfigGroup("UseGlobalMedicationsTable", True).Enabled

        DOCILogic() 'do config instance logic stuff

    End Sub

    Sub InitUrgentCareTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        ceEnableEMGrid.Checked = lAppConfigGroup("EnableEMShellTab", True).Enabled
        ceUseAdvancedModel.Checked = lAppConfigGroup("EnableEMShellAdvanced", True).Enabled

        ceUseUrgentCareConfig.Checked = lAppConfigGroup("UseUrgentCareConfig", True).Enabled

        If lAppConfigGroup("DefaultChartStatusPhysTab", True).Enabled Then
            Me.teDefaultProfeeV2PhysChartStatus.EditValue = lAppConfigGroup("DefaultChartStatusPhysTab").SettingValue
        Else
            Me.teDefaultProfeeV2PhysChartStatus.EditValue = ""
        End If

        'ProfeeV2_Summary_SwapModifierschk.Checked = lAppConfigGroup("PhysModFirst", True).Enabled

        If lAppConfigGroup("ProfeeV2PhysManualEntryEditMask", True).Enabled Then
            ceProfeeV2PhysManualEntryEditMask.EditValue = lAppConfigGroup("ProfeeV2PhysManualEntryEditMask", True).SettingValue
        Else
            ceProfeeV2PhysManualEntryEditMask.EditValue = ""
            lAppConfigGroup("ProfeeV2PhysManualEntryEditMask", True).Enabled = True
        End If

        If lAppConfigGroup("ProfeeV2PhysManualEntryEditMaskErrorMsg", True).Enabled Then
            ceProfeeV2PhysManualEntryEditMaskErrorMsg.EditValue = lAppConfigGroup("ProfeeV2PhysManualEntryEditMaskErrorMsg", True).SettingValue
        Else
            ceProfeeV2PhysManualEntryEditMaskErrorMsg.EditValue = "The entered value is not in the required format."
            lAppConfigGroup("ProfeeV2PhysManualEntryEditMaskErrorMsg", True).Enabled = True
        End If

        'QI Options -----------------------------------------------------------------------------------------------
        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides")) ' = GetControlConfigGroup("Qi")
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option1_chk", chkProfeeV2PhysTabQI1, teProfeeV2PhysTabQI1)
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option2_chk", chkProfeeV2PhysTabQI2, teProfeeV2PhysTabQI2)
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option3_chk", chkProfeeV2PhysTabQI3, teProfeeV2PhysTabQI3)
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option4_chk", chkProfeeV2PhysTabQI4, teProfeeV2PhysTabQI4)
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option5_chk", chkProfeeV2PhysTabQI5, teProfeeV2PhysTabQI5)
        QIgrp.LoadObsQIControls("ProfeeV2_Summary_QI_Option6_chk", chkProfeeV2PhysTabQI6, teProfeeV2PhysTabQI6)

    End Sub

    Public Class DuplicatePointer
        Public Sub New(c1 As BaseEdit, c2 As BaseEdit)
            Me.C1 = c1
            Me.C2 = c2

            AddHandler c1.EditValueChanged, AddressOf EditvalueChanged
            AddHandler c2.EditValueChanged, AddressOf EditvalueChanged
        End Sub

        Private Sub EditvalueChanged(sender As Object, e As EventArgs)
            If sender Is C1 Then
                If C2?.EditValue <> C1.EditValue Then
                    C2.EditValue = C1.EditValue
                End If
            Else
                If C1?.EditValue <> C2.EditValue Then
                    C1.EditValue = C2.EditValue
                End If
            End If

        End Sub

        Public ReadOnly Property C1 As BaseEdit
        Public ReadOnly Property C2 As BaseEdit
    End Class

    Sub SaveUgrentCareTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        lAppConfigGroup("UseUrgentCareConfig").Enabled = ceUseUrgentCareConfig.Checked
        Dim doChartStatus As DOConfigSetting = lAppConfigGroup("DefaultChartStatusPhysTab")

        If String.IsNullOrEmpty(Me.teDefaultProfeeV2PhysChartStatus.EditValue) Then
            doChartStatus.Enabled = False
        Else
            doChartStatus.Enabled = True
        End If

        doChartStatus.SettingValue = Me.teDefaultProfeeV2PhysChartStatus.EditValue

        lAppConfigGroup("ProfeeV2PhysManualEntryEditMask", True).SettingValue = ceProfeeV2PhysManualEntryEditMask.EditValue
        lAppConfigGroup("ProfeeV2PhysManualEntryEditMaskErrorMsg", True).SettingValue = ceProfeeV2PhysManualEntryEditMaskErrorMsg.EditValue

        'QI Options -----------------------------------------------------------------------------------------------
        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides")) ' = GetControlConfigGroup("Qi")
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option1_chk", chkProfeeV2PhysTabQI1, teProfeeV2PhysTabQI1)
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option2_chk", chkProfeeV2PhysTabQI2, teProfeeV2PhysTabQI2)
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option3_chk", chkProfeeV2PhysTabQI3, teProfeeV2PhysTabQI3)
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option4_chk", chkProfeeV2PhysTabQI4, teProfeeV2PhysTabQI4)
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option5_chk", chkProfeeV2PhysTabQI5, teProfeeV2PhysTabQI5)
        QIgrp.SaveControls("ProfeeV2_Summary_QI_Option6_chk", chkProfeeV2PhysTabQI6, teProfeeV2PhysTabQI6)

    End Sub

    Sub InitClinicTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        Me.ceClinicMode.Checked = lAppConfigGroup("EnableClinicMode", True).Enabled
        Me.ceEnableClinicPhysTab.Checked = lAppConfigGroup("EnableClinicModePhysicianTab", True).Enabled

        Me.ceEnableClinicICDXTab.Checked = lAppConfigGroup("EnableClinicModeICDTab", True).Enabled
        Me.ceEnableClinicOrthoTab.Checked = lAppConfigGroup("EnableClinicModeOrthoTab", True).Enabled
        Me.ceEnableClinicProceduresTab.Checked = lAppConfigGroup("EnableClinicModeProcedureTab", True).Enabled
        Me.ceEnableClinicSurgicalTab.Checked = lAppConfigGroup("EnableClinicModeSurgicalTab", True).Enabled
        Me.ceEnableClinicSuppliesTab.Checked = lAppConfigGroup("EnableSuppliesTabInClinicMode", True).Enabled
        ''EnableSuppliesTabInClinicMode

        If lAppConfigGroup("ClinicManualEntryEditMask", True).Enabled Then
            ceClinicManualEntryEditMask.EditValue = lAppConfigGroup("ClinicManualEntryEditMask", True).SettingValue
        Else
            ceClinicManualEntryEditMask.EditValue = ""
            lAppConfigGroup("ClinicManualEntryEditMask", True).Enabled = True
        End If

        If lAppConfigGroup("ClinicManualEntryEditMaskErrorMsg", True).Enabled Then
            ceClinicManualEntryEditMaskErrorMsg.EditValue = lAppConfigGroup("ClinicManualEntryEditMaskErrorMsg", True).SettingValue
        Else
            ceClinicManualEntryEditMaskErrorMsg.EditValue = "The entered value is not in the required format."
            lAppConfigGroup("ClinicManualEntryEditMaskErrorMsg", True).Enabled = True
        End If

        If lAppConfigGroup("ClinicPhysManualEntryEditMask", True).Enabled Then
            ceClinicPhysManualEntryEditMask.EditValue = lAppConfigGroup("ClinicPhysManualEntryEditMask", True).SettingValue
        Else
            ceClinicPhysManualEntryEditMask.EditValue = ""
            lAppConfigGroup("ClinicPhysManualEntryEditMask", True).Enabled = True
        End If

        If lAppConfigGroup("ClinicPhysManualEntryEditMaskErrorMsg", True).Enabled Then
            ceClinicPhysManualEntryEditMaskErrorMsg.EditValue = lAppConfigGroup("ClinicPhysManualEntryEditMaskErrorMsg", True).SettingValue
        Else
            ceClinicPhysManualEntryEditMaskErrorMsg.EditValue = "The entered value is not in the required format."
            lAppConfigGroup("ClinicPhysManualEntryEditMaskErrorMsg", True).Enabled = True
        End If

    End Sub

    Sub SaveClinicTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup

        lAppConfigGroup("EnableClinicMode").Enabled = ceClinicMode.Checked
        lAppConfigGroup("EnableClinicModePhysicianTab").Enabled = ceEnableClinicPhysTab.Checked

        lAppConfigGroup("EnableClinicModeICDTab").Enabled = ceEnableClinicICDXTab.Checked
        lAppConfigGroup("EnableClinicModeOrthoTab").Enabled = ceEnableClinicOrthoTab.Checked
        lAppConfigGroup("EnableClinicModeProcedureTab").Enabled = ceEnableClinicProceduresTab.Checked
        lAppConfigGroup("EnableClinicModeSurgicalTab").Enabled = ceEnableClinicSurgicalTab.Checked
        lAppConfigGroup("EnableSuppliesTabInClinicMode").Enabled = ceEnableClinicSuppliesTab.Checked

        'Manual Entry
        lAppConfigGroup("ClinicManualEntryEditMask", True).SettingValue = ceClinicManualEntryEditMask.EditValue
        lAppConfigGroup("ClinicManualEntryEditMaskErrorMsg", True).SettingValue = ceClinicManualEntryEditMaskErrorMsg.EditValue

        lAppConfigGroup("ClinicPhysManualEntryEditMask", True).SettingValue = ceClinicPhysManualEntryEditMask.EditValue
        lAppConfigGroup("ClinicPhysManualEntryEditMaskErrorMsg", True).SettingValue = ceClinicPhysManualEntryEditMaskErrorMsg.EditValue

        'QIgrp.SaveControls("Clinic_QI1_chk", Clinic_QI1_chk, Clinic_QI1_txt)

        'QIgrp.SaveControls("Physician_Summary_QI_Option2_chk", Physician_Summary_QI_Option22_chk, Physician_Summary_QI_Option22_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_Option4_chk", Physician_Summary_QI_Option42_chk, Physician_Summary_QI_Option42_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_Option6_chk", Physician_Summary_QI_Option62_chk, Physician_Summary_QI_Option62_txt)

    End Sub

    Sub InitRequiredFields()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        Me.ceTriageVitalSignsRequired.Checked = lAppConfigGroup("TriageVitalSignsRequired", True).Enabled
        Me.ceTriagePainScaleRequired.Checked = lAppConfigGroup("TriagePainScaleRequired", True).Enabled
        Me.cePaymentRuleRequired.Checked = lAppConfigGroup("TriageLevelOfConsciousnessRequired", True).Enabled

        Me.ceTriageCategoryRequired.Checked = lAppConfigGroup("TriageCategoryRequired", True).Enabled
        Me.ceTriageSafeInHomeRequired.Checked = lAppConfigGroup("TriageSafeInHomeRequired", True).Enabled

        Me.ceTriageNurseRequired.Checked = lAppConfigGroup("TriageNurseRequired", True).Enabled
        Me.ceTriagePhysicianRequired.Checked = lAppConfigGroup("TriagePhysRequired", True).Enabled
        Me.ceTriageSourceRequired.Checked = lAppConfigGroup("TriageSourceRequired", True).Enabled

        Me.ceDispoVitalSignsRequired.Checked = lAppConfigGroup("DischargeVitalSignsRequired", True).Enabled
        Me.ceDispoPainAssesmentRequired.Checked = lAppConfigGroup("DischargePainAssessmentRequired", True).Enabled
        Me.ceDispoAssessmentRequired.Checked = lAppConfigGroup("DispositionAssessmentRequired", True).Enabled
        Me.ceDispositionNurseRequired.Checked = lAppConfigGroup("DispositionNurseRequired", True).Enabled

        Me.ceMedicationImmunResReq.Checked = lAppConfigGroup("Immunization Response Required", True).Enabled
        Me.ceMedMedResReq.Checked = lAppConfigGroup("Medication Response Required", True).Enabled
        Me.ceMedInjResReq.Checked = lAppConfigGroup("Injection Response Required", True).Enabled

        Me.ceInfusInfusResReq.Checked = lAppConfigGroup("Infused Meds Response Required", True).Enabled
        Me.ceInfusHydrResReq.Checked = lAppConfigGroup("Hydrations Response Required", True).Enabled
        Me.ceInfusTitratedResReq.Checked = lAppConfigGroup("Titrated Meds Response Required", True).Enabled

        'cePhysHistoryException
        Me.cePhysHistoryPhysRequired.Checked = lAppConfigGroup("PhysicianPhysicianRequired", True).Enabled
        Me.cePhysSumTabProviderRequired.Checked = lAppConfigGroup("PhysSumTabProviderRequired", True).Enabled
        Me.cePhysDXxCodesRequired.Checked = lAppConfigGroup("PhysDXxCodesRequired", True).Enabled
        Me.cePhysOverrideReasonRequired.Checked = lAppConfigGroup("PhysicianRequireEmOverrideReason", True).Enabled

        'ceMedicationsIVPushTimeNotRequired
        Me.ceMedicationsIVPushTimeNotRequired.Checked = lAppConfigGroup("IvpushTimeNotRequired", True).Enabled
        Me.cePhysManDiagRequired.Checked = lAppConfigGroup("PhysManDiagRequired", True).Enabled

        Me.cePhysSwapModWithDx.Checked = lAppConfigGroup("PhysModFirst", True).Enabled
        '"Medication Date Required"
        Me.ceMedImmunDateRequired.Checked = lAppConfigGroup("Immun Date Required", True).Enabled

        'EM Grid
        Me.ceEmGridPhysNameRequired.Checked = lAppConfigGroup("EmGridPhysNameRequired", True).Enabled
        Me.ceEmGridDispositionRequired.Checked = lAppConfigGroup("EmGridDispositionRequired", True).Enabled
        Me.ceEmGridTriageCategoryRequired.Checked = lAppConfigGroup("EmGridTriageCategoryRequired", True).Enabled

        Me.chkGridUseNewControlNaming.Checked = lAppConfigGroup("GridUseNewControlNaming", True).Enabled
        Me.chkGridUseNewControlNamingForChargableProcs.Checked = lAppConfigGroup("GridUseNewControlNamingForChargableProcs", True).Enabled

        'EM Grid
        Me.ceObsPhysNameRequired.Checked = lAppConfigGroup("ObsPhysNameRequired", True).Enabled
        Me.ceObsDispositionRequired.Checked = lAppConfigGroup("ObsDispositionRequired", True).Enabled
        Me.ceObsDiagCategoryRequired.Checked = lAppConfigGroup("ObsDiagCategoryRequired", True).Enabled

        'Triage/EMGrid
        Me.cePayorRuleRequired.Checked = lAppConfigGroup("FacilityPayorRuleRequired", True).Enabled 'Payor, Payment --- you say tomato ...

        ''Obs
        'lAppConfigGroup("ObsPhysNameRequired", True).Enabled = Me.ceObsPhysNameRequired.Checked
        'lAppConfigGroup("ObsDispositionRequired", True).Enabled = Me.ceObsDispositionRequired.Checked
        'lAppConfigGroup("ObsDiagCategoryRequired", True).Enabled = Me.ceObsDiagCategoryRequired.Checked

    End Sub

    Sub InitTreatmentAreas()
        TreatmentAreasPage1.InitTreatmentAreasForFacility(ECGlobals.CurrentFacility)
    End Sub

    Sub SaveRequiredFields()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        lAppConfigGroup("TriageVitalSignsRequired").Enabled = Me.ceTriageVitalSignsRequired.Checked
        lAppConfigGroup("TriagePainScaleRequired").Enabled = Me.ceTriagePainScaleRequired.Checked
        lAppConfigGroup("TriageLevelOfConsciousnessRequired").Enabled = Me.cePaymentRuleRequired.Checked

        lAppConfigGroup("TriageCategoryRequired").Enabled = Me.ceTriageCategoryRequired.Checked
        lAppConfigGroup("TriageSafeInHomeRequired").Enabled = Me.ceTriageSafeInHomeRequired.Checked

        lAppConfigGroup("TriageNurseRequired", True).Enabled = Me.ceTriageNurseRequired.Checked
        lAppConfigGroup("TriagePhysRequired", True).Enabled = Me.ceTriagePhysicianRequired.Checked
        lAppConfigGroup("TriageSourceRequired", True).Enabled = Me.ceTriageSourceRequired.Checked

        lAppConfigGroup("DischargeVitalSignsRequired").Enabled = Me.ceDispoVitalSignsRequired.Checked
        lAppConfigGroup("DischargePainAssessmentRequired").Enabled = Me.ceDispoPainAssesmentRequired.Checked
        lAppConfigGroup("DispositionAssessmentRequired").Enabled = Me.ceDispoAssessmentRequired.Checked
        lAppConfigGroup("DispositionNurseRequired", True).Enabled = Me.ceDispositionNurseRequired.Checked

        lAppConfigGroup("Immunization Response Required", True).Enabled = Me.ceMedicationImmunResReq.Checked
        lAppConfigGroup("Medication Response Required", True).Enabled = Me.ceMedMedResReq.Checked
        lAppConfigGroup("Injection Response Required", True).Enabled = Me.ceMedInjResReq.Checked

        lAppConfigGroup("Infused Meds Response Required", True).Enabled = Me.ceInfusInfusResReq.Checked
        lAppConfigGroup("Hydrations Response Required", True).Enabled = Me.ceInfusHydrResReq.Checked
        lAppConfigGroup("Titrated Meds Response Required", True).Enabled = Me.ceInfusTitratedResReq.Checked

        lAppConfigGroup("PhysicianPhysicianRequired", True).Enabled = Me.cePhysHistoryPhysRequired.Checked
        lAppConfigGroup("PhysSumTabProviderRequired", True).Enabled = Me.cePhysSumTabProviderRequired.Checked

        lAppConfigGroup("IvpushTimeNotRequired", True).Enabled = Me.ceMedicationsIVPushTimeNotRequired.Checked

        lAppConfigGroup("PhysSumTabProviderRequired", True).Enabled = Me.cePhysSumTabProviderRequired.Checked
        lAppConfigGroup("PhysDXxCodesRequired", True).Enabled = Me.cePhysDXxCodesRequired.Checked
        lAppConfigGroup("PhysicianRequireEmOverrideReason", True).Enabled = Me.cePhysOverrideReasonRequired.Checked

        lAppConfigGroup("PhysManDiagRequired", True).Enabled = Me.cePhysManDiagRequired.Checked

        lAppConfigGroup("PhysModFirst", True).Enabled = Me.cePhysSwapModWithDx.Checked

        lAppConfigGroup("Immun Date Required", True).Enabled = Me.ceMedImmunDateRequired.Checked

        'EM Grid
        lAppConfigGroup("EmGridPhysNameRequired", True).Enabled = Me.ceEmGridPhysNameRequired.Checked
        lAppConfigGroup("EmGridDispositionRequired", True).Enabled = Me.ceEmGridDispositionRequired.Checked
        lAppConfigGroup("EmGridTriageCategoryRequired", True).Enabled = Me.ceEmGridTriageCategoryRequired.Checked
        lAppConfigGroup("GridUseNewControlNaming", True).Enabled = Me.chkGridUseNewControlNaming.Checked
        lAppConfigGroup("GridUseNewControlNamingForChargableProcs", True).Enabled = Me.chkGridUseNewControlNamingForChargableProcs.Checked

        'Obs
        lAppConfigGroup("ObsPhysNameRequired", True).Enabled = Me.ceObsPhysNameRequired.Checked
        lAppConfigGroup("ObsDispositionRequired", True).Enabled = Me.ceObsDispositionRequired.Checked
        lAppConfigGroup("ObsDiagCategoryRequired", True).Enabled = Me.ceObsDiagCategoryRequired.Checked

        'Triage/EMGrid
        lAppConfigGroup("FacilityPayorRuleRequired", True).Enabled = Me.cePayorRuleRequired.Checked

    End Sub

#Region "Times Tab"
    Sub InitTimesTab()

        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")
        ceTimes_AdvancedTimes.Checked = lAppConfigGroup("Advanced Times Module", True).Enabled

        ceTimes_Arrival_Required.Checked = True ' Yes allways required

        ceTimes_Nurse_Required.Checked = Not lAppConfigGroup("DateTimeTriageNurseNotRequired", True).Enabled
        ceTimes_Physician_Required.Checked = Not lAppConfigGroup("DateTimeTriageDocNotRequired", True).Enabled
        ceTimes_Protocol_Required.Checked = Not lAppConfigGroup("DateTimeProtocolNotRequired", True).Enabled

        ceTimes_Treatment_Required.Checked = Not lAppConfigGroup("DateTimeTreatmentLocNotRequired", True).Enabled
        ceTimes_MSE_Required.Checked = Not lAppConfigGroup("DateTimeMSENotRequired", True).Enabled
        ceTimes_Decision_Required.Checked = Not lAppConfigGroup("DateTimeDecisionNotRequired", True).Enabled
        ceTimes_Dispostion_Required.Checked = Not lAppConfigGroup("DateTimeDispositionNotRequired", True).Enabled

        If lAppConfigGroup("MaxDispoSubDate", True).Enabled Then
            Me.seMaxDispoSubDate.EditValue = lAppConfigGroup("MaxDispoSubDate", True).SettingValue
        Else
            Me.seMaxDispoSubDate.EditValue = 10
        End If

        If lAppConfigGroup("MinValidDate", True).Enabled Then
            Me.seMinValidDate.EditValue = lAppConfigGroup("MinValidDate", True).SettingValue
        Else
            Me.seMinValidDate.EditValue = 10
        End If
        'MaxDispoSubDate

        'ceTimes_CCStart_Required.Checked = lAppConfigGroup("DateTimeProtocolNotRequired").Enabled
        'ceTimes_CCEnd_Required.Checked = lAppConfigGroup("DateTimeProtocolNotRequired").Enabled
        'ceTimes_Border_Required.Checked = lAppConfigGroup("DateTimeProtocolNotRequired").Enabled
    End Sub
    Sub Save_TimesTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup
        lAppConfigGroup("Advanced Times Module", True).Enabled = ceTimes_AdvancedTimes.Checked

        lAppConfigGroup("DateTimeTriageNurseNotRequired", True).Enabled = Not ceTimes_Nurse_Required.Checked
        lAppConfigGroup("DateTimeTriageDocNotRequired", True).Enabled = Not ceTimes_Physician_Required.Checked
        lAppConfigGroup("DateTimeProtocolNotRequired", True).Enabled = Not ceTimes_Protocol_Required.Checked

        lAppConfigGroup("DateTimeTreatmentLocNotRequired", True).Enabled = Not ceTimes_Treatment_Required.Checked
        lAppConfigGroup("DateTimeMSENotRequired", True).Enabled = Not ceTimes_MSE_Required.Checked
        lAppConfigGroup("DateTimeDecisionNotRequired", True).Enabled = Not ceTimes_Decision_Required.Checked
        lAppConfigGroup("DateTimeDispositionNotRequired", True).Enabled = Not ceTimes_Dispostion_Required.Checked

        lAppConfigGroup("MaxDispoSubDate").Enabled = True
        lAppConfigGroup("MaxDispoSubDate").SettingValue = Me.seMaxDispoSubDate.EditValue

        lAppConfigGroup("MinValidDate").Enabled = True
        lAppConfigGroup("MinValidDate").SettingValue = Me.seMinValidDate.EditValue

        'MinValidDate

        ''lazy testing....
        'For Each cs As DOConfigSetting In ECGlobals.CurrentConfigInstance.AppConfigGroup.Settings 'lAppConfigGroup.Settings
        '    cs.Save()
        'Next

    End Sub
#End Region

#Region "Quality Inidicators Tab"

    Sub InitQITab()

        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides")) ' = GetControlConfigGroup("Qi")

        'Obs Tab- Meds Qi's
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option1_chk", Obs_Medication_QI_Option1_chk, Obs_Medication_QI_Option1_txt)
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option2_chk", Obs_Medication_QI_Option2_chk, Obs_Medication_QI_Option2_txt)
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option3_chk", Obs_Medication_QI_Option3_chk, Obs_Medication_QI_Option3_txt)
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option4_chk", Obs_Medication_QI_Option4_chk, Obs_Medication_QI_Option4_txt)

        'Obs Tab- Times Qi's
        QIgrp.LoadObsQIControls("ObsTimesQIObsOption1_chk", ObsTimesQIObsOption1_chk, ObsTimesQIObsOption1_txt)
        QIgrp.LoadObsQIControls("ObsTimesQIObsOption2_chk", ObsTimesQIObsOption2_chk, ObsTimesQIObsOption2_txt)

        'Obs Tab- Proc Tab Qi's
        QIgrp.LoadObsQIControls("Obs_Procs_QI_Option1_chk", Obs_Procs_QI_Option1_chk, Obs_Procs_QI_Option1_txt)
        QIgrp.LoadObsQIControls("Obs_Procs_QI_Option2_chk", Obs_Procs_QI_Option2_chk, Obs_Procs_QI_Option2_txt)
        QIgrp.LoadObsQIControls("Obs_Procs_QI_Option3_chk", Obs_Procs_QI_Option3_chk, Obs_Procs_QI_Option3_txt)
        QIgrp.LoadObsQIControls("Obs_Procs_QI_Option4_chk", Obs_Procs_QI_Option4_chk, Obs_Procs_QI_Option4_txt)

        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option2_chk", Obs_Medication_QI_Option2_chk, Obs_Medication_QI_Option2_txt)
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option3_chk", Obs_Medication_QI_Option3_chk, Obs_Medication_QI_Option3_txt)
        QIgrp.LoadObsQIControls("Obs_Medication_QI_Option4_chk", Obs_Medication_QI_Option4_chk, Obs_Medication_QI_Option4_txt)

        'Init Triage
        QIgrp.LoadControls(ECForm, "QualityIndicators_Return48_chk", QualityIndicators_Return48_chk, QualityIndicators_Return48_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_Return24_chk", QualityIndicators_Return24_chk, QualityIndicators_Return24_txt)

        QIgrp.LoadControls(ECForm, "QualityIndicators_WorkersComp_chk", QualityIndicators_WorkersComp_chk, QualityIndicators_WorkersComp_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ImmuneStatus_chk", QualityIndicators_ImmuneStatus_chk, QualityIndicators_ImmuneStatus_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_Respiratory_chk", QualityIndicators_Respiratory_chk, QualityIndicators_Respiratory_txt)

        'Init Psychosocial
        QIgrp.LoadControls(ECForm, "QualityIndicators_RiskOfFalls_chk", QualityIndicators_RiskOfFalls_chk, QualityIndicators_RiskOfFalls_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_RestraintDocumentationComplete_chk", QualityIndicators_RestraintDocumentationComplete_chk, QualityIndicators_RestraintDocumentationComplete_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_RestraintDocumentationCompleteND_chk", QualityIndicators_RestraintDocumentationCompleteND_chk, QualityIndicators_RestraintDocumentationCompleteND_txt)

        QIgrp.LoadControls(ECForm, "QualityIndicators_PsychosocialOption01_chk", QualityIndicators_PsychosocialOption01_chk, QualityIndicators_PsychosocialOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_PsychosocialOption02_chk", QualityIndicators_PsychosocialOption02_chk, QualityIndicators_PsychosocialOption02_txt)

        'Init Assesments
        QIgrp.LoadControls(ECForm, "QualityIndicators_NoReassessments_chk", QualityIndicators_NoReassessments_chk, QualityIndicators_NoReassessments_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_VitalSignsND_chk", QualityIndicators_VitalSignsND_chk, QualityIndicators_VitalSignsND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_AssessmentOption01_chk", QualityIndicators_AssessmentOption01_chk, QualityIndicators_AssessmentOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_AssessmentOption02_chk", QualityIndicators_AssessmentOption02_chk, QualityIndicators_AssessmentOption02_txt)

        'Init Disposition
        QIgrp.LoadControls(ECForm, "QualityIndicators_DispositionAssessmentND_chk", QualityIndicators_DispositionAssessmentND_chk, QualityIndicators_DispositionAssessmentND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_DispositionND_chk", QualityIndicators_DispositionND_chk, QualityIndicators_DispositionND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_DischargePainAssessmentND_chk", QualityIndicators_DischargePainAssessmentND_chk, QualityIndicators_DischargePainAssessmentND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_DischargeVitalSignsND_chk", QualityIndicators_DischargeVitalSignsND_chk, QualityIndicators_DischargeVitalSignsND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_DispositionOption01_chk", QualityIndicators_DispositionOption01_chk, QualityIndicators_DispositionOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_DispositionOption02_chk", QualityIndicators_DispositionOption02_chk, QualityIndicators_DispositionOption02_txt)

        'Init Tests
        QIgrp.LoadControls(ECForm, "QualityIndicators_TestND_chk", QualityIndicators_TestND_chk, QualityIndicators_TestND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_TestsOption01_chk", QualityIndicators_TestsOption01_chk, QualityIndicators_TestsOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_TestsOption02_chk", QualityIndicators_TestsOption02_chk, QualityIndicators_TestsOption02_txt)

        'Init Medications
        QIgrp.LoadControls(ECForm, "QualityIndicators_RouteUnclear_chk", QualityIndicators_RouteUnclear_chk, QualityIndicators_RouteUnclear_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_TimesND_chk", QualityIndicators_TimesND_chk, QualityIndicators_TimesND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_UnableToDetermineInjections_chk", QualityIndicators_UnableToDetermineInjections_chk, QualityIndicators_UnableToDetermineInjections_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncompleteNursingDocumentation_chk", QualityIndicators_IncompleteNursingDocumentation_chk, QualityIndicators_IncompleteNursingDocumentation_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MedicationsOption01_chk", QualityIndicators_MedicationsOption01_chk, QualityIndicators_MedicationsOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MedicationsOption02_chk", QualityIndicators_MedicationsOption02_chk, QualityIndicators_MedicationsOption02_txt)

        'Init Infusions
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncompleteTimes_chk", QualityIndicators_IncompleteTimes_chk, QualityIndicators_IncompleteTimes_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncorrectNursingDocumentation_chk", QualityIndicators_IncorrectNursingDocumentation_chk, QualityIndicators_IncorrectNursingDocumentation_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_SitesND_chk", QualityIndicators_SitesND_chk, QualityIndicators_SitesND_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_InfusionsOption01_chk", QualityIndicators_InfusionsOption01_chk, QualityIndicators_InfusionsOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_InfusionsOption02_chk", QualityIndicators_InfusionsOption02_chk, QualityIndicators_InfusionsOption02_txt)

        'Init Procedures
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProcedureTimeOut_chk", QualityIndicators_ProcedureTimeOut_chk, QualityIndicators_ProcedureTimeOut_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProceduresOption01_chk", QualityIndicators_ProceduresOption01_chk, QualityIndicators_ProceduresOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProceduresOption02_chk", QualityIndicators_ProceduresOption02_chk, QualityIndicators_ProceduresOption02_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProceduresOption03_chk", QualityIndicators_ProceduresOption03_chk, QualityIndicators_ProceduresOption03_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProceduresOption04_chk", QualityIndicators_ProceduresOption04_chk, QualityIndicators_ProceduresOption04_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_ProceduresOption05_chk", QualityIndicators_ProceduresOption05_chk, QualityIndicators_ProceduresOption05_txt)

        'Init Ortho ...
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncompleteOrthoNote_chk", QualityIndicators_IncompleteOrthoNote_chk, QualityIndicators_IncompleteOrthoNote_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_OrthoOption01_chk", QualityIndicators_OrthoOption01_chk, QualityIndicators_OrthoOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_OrthoOption02_chk", QualityIndicators_OrthoOption02_chk, QualityIndicators_OrthoOption02_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_OrthoOption03_chk", QualityIndicators_OrthoOption03_chk, QualityIndicators_OrthoOption03_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_OrthoOption04_chk", QualityIndicators_OrthoOption04_chk, QualityIndicators_OrthoOption04_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_OrthoOption05_chk", QualityIndicators_OrthoOption05_chk, QualityIndicators_OrthoOption05_txt)

        'Init Surgical ...
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncompletedLacerationNote_chk", QualityIndicators_IncompletedLacerationNote_chk, QualityIndicators_IncompletedLacerationNote_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_IncompletedFBIDNote_chk", QualityIndicators_IncompletedFBIDNote_chk, QualityIndicators_IncompletedFBIDNote_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_SurgicalOption01_chk", QualityIndicators_SurgicalOption01_chk, QualityIndicators_SurgicalOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_SurgicalOption02_chk", QualityIndicators_SurgicalOption02_chk, QualityIndicators_SurgicalOption02_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_SurgicalOption03_chk", QualityIndicators_SurgicalOption03_chk, QualityIndicators_SurgicalOption03_txt)

        'Init Misc ...
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption01_chk", QualityIndicators_MiscOption01_chk, QualityIndicators_MiscOption01_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption02_chk", QualityIndicators_MiscOption02_chk, QualityIndicators_MiscOption02_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption03_chk", QualityIndicators_MiscOption03_chk, QualityIndicators_MiscOption03_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption04_chk", QualityIndicators_MiscOption04_chk, QualityIndicators_MiscOption04_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption05_chk", QualityIndicators_MiscOption05_chk, QualityIndicators_MiscOption05_txt)
        QIgrp.LoadControls(ECForm, "QualityIndicators_MiscOption06_chk", QualityIndicators_MiscOption06_chk, QualityIndicators_MiscOption06_txt)

        ''Clinic QIs
        'QIgrp.LoadControls(ECForm, "Clinic_QI1_chk", Clinic_QI1_chk, Clinic_QI1_txt)
        'QIgrp.LoadControls(ECForm, "Clinic_QI2_chk", Clinic_QI2_chk, Clinic_QI2_txt)
        'QIgrp.LoadControls(ECForm, "Clinic_QI3_chk", Clinic_QI3_chk, Clinic_QI3_txt)
        'QIgrp.LoadControls(ECForm, "Clinic_QI4_chk", Clinic_QI4_chk, Clinic_QI4_txt)

        ''Clinic Prof Misc QI
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI1_chk", ClinicPhys_Misc_QI1_chk, ClinicPhys_Misc_QI1_txt)
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI2_chk", ClinicPhys_Misc_QI2_chk, ClinicPhys_Misc_QI2_txt)
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI3_chk", ClinicPhys_Misc_QI3_chk, ClinicPhys_Misc_QI3_txt)
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI4_chk", ClinicPhys_Misc_QI4_chk, ClinicPhys_Misc_QI4_txt)
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI5_chk", ClinicPhys_Misc_QI1_chk, ClinicPhys_Misc_QI5_txt)
        'QIgrp.LoadControls(ECForm, "ClinicPhys_Misc_QI6_chk", ClinicPhys_Misc_QI1_chk, ClinicPhys_Misc_QI6_txt)

    End Sub

    Sub SaveQITab()
        'Load ConfigInstance specific Control Overrides
        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides"))

        'Obs QI Tab
        QIgrp.SaveControls("Obs_Medication_QI_Option1_chk", Obs_Medication_QI_Option1_chk, Obs_Medication_QI_Option1_txt)
        QIgrp.SaveControls("Obs_Medication_QI_Option2_chk", Obs_Medication_QI_Option2_chk, Obs_Medication_QI_Option2_txt)
        QIgrp.SaveControls("Obs_Medication_QI_Option3_chk", Obs_Medication_QI_Option3_chk, Obs_Medication_QI_Option3_txt)
        QIgrp.SaveControls("Obs_Medication_QI_Option4_chk", Obs_Medication_QI_Option4_chk, Obs_Medication_QI_Option4_txt)

        QIgrp.SaveControls("ObsTimesQIObsOption1_chk", ObsTimesQIObsOption1_chk, ObsTimesQIObsOption1_txt)
        QIgrp.SaveControls("ObsTimesQIObsOption2_chk", ObsTimesQIObsOption2_chk, ObsTimesQIObsOption2_txt)

        QIgrp.SaveControls("Obs_Procs_QI_Option1_chk", Obs_Procs_QI_Option1_chk, Obs_Procs_QI_Option1_txt)
        QIgrp.SaveControls("Obs_Procs_QI_Option2_chk", Obs_Procs_QI_Option2_chk, Obs_Procs_QI_Option2_txt)
        QIgrp.SaveControls("Obs_Procs_QI_Option3_chk", Obs_Procs_QI_Option3_chk, Obs_Procs_QI_Option3_txt)
        QIgrp.SaveControls("Obs_Procs_QI_Option4_chk", Obs_Procs_QI_Option4_chk, Obs_Procs_QI_Option4_txt)

        'Init Triage
        QIgrp.SaveControls("QualityIndicators_Return48_chk", QualityIndicators_Return48_chk, QualityIndicators_Return48_txt)
        QIgrp.SaveControls("Triage_Return48_chk", QualityIndicators_Return48_chk, QualityIndicators_Return48_txt)

        QIgrp.SaveControls("QualityIndicators_Return24_chk", QualityIndicators_Return24_chk, QualityIndicators_Return24_txt)
        QIgrp.SaveControls("Triage_Return24_chk", QualityIndicators_Return24_chk, QualityIndicators_Return24_txt)

        QIgrp.SaveControls("QualityIndicators_WorkersComp_chk", QualityIndicators_WorkersComp_chk, QualityIndicators_WorkersComp_txt)
        QIgrp.SaveControls("Triage_WorkersComp_chk", QualityIndicators_WorkersComp_chk, QualityIndicators_WorkersComp_txt)

        QIgrp.SaveControls("QualityIndicators_ImmuneStatus_chk", QualityIndicators_ImmuneStatus_chk, QualityIndicators_ImmuneStatus_txt)
        QIgrp.SaveControls("Triage_ImmuneStatus_chk", QualityIndicators_ImmuneStatus_chk, QualityIndicators_ImmuneStatus_txt)

        QIgrp.SaveControls("QualityIndicators_Respiratory_chk", QualityIndicators_Respiratory_chk, QualityIndicators_Respiratory_txt)
        QIgrp.SaveControls("Triage_Respiratory_chk", QualityIndicators_Respiratory_chk, QualityIndicators_Respiratory_txt)

        'Save Psychosocial
        QIgrp.SaveControls("QualityIndicators_RiskOfFalls_chk", QualityIndicators_RiskOfFalls_chk, QualityIndicators_RiskOfFalls_txt)
        QIgrp.SaveControls("Psychosocial_RiskOfFalls_chk", QualityIndicators_RiskOfFalls_chk, QualityIndicators_RiskOfFalls_txt)

        QIgrp.SaveControls("QualityIndicators_RestraintDocumentationComplete_chk", QualityIndicators_RestraintDocumentationComplete_chk, QualityIndicators_RestraintDocumentationComplete_txt)
        QIgrp.SaveControls("Psychosocial_RestraintDocumentationComplete_chk", QualityIndicators_RestraintDocumentationComplete_chk, QualityIndicators_RestraintDocumentationComplete_txt)

        QIgrp.SaveControls("QualityIndicators_RestraintDocumentationCompleteND_chk", QualityIndicators_RestraintDocumentationCompleteND_chk, QualityIndicators_RestraintDocumentationCompleteND_txt)
        QIgrp.SaveControls("Psychosocial_RestraintDocumentationCompleteND_chk", QualityIndicators_RestraintDocumentationCompleteND_chk, QualityIndicators_RestraintDocumentationCompleteND_txt)

        QIgrp.SaveControls("QualityIndicators_PsychosocialOption01_chk", QualityIndicators_PsychosocialOption01_chk, QualityIndicators_PsychosocialOption01_txt)
        QIgrp.SaveControls("Psychosocial_QIOption01_chk", QualityIndicators_PsychosocialOption01_chk, QualityIndicators_PsychosocialOption01_txt)

        QIgrp.SaveControls("QualityIndicators_PsychosocialOption02_chk", QualityIndicators_PsychosocialOption02_chk, QualityIndicators_PsychosocialOption02_txt)
        QIgrp.SaveControls("Psychosocial_QIOption02_chk", QualityIndicators_PsychosocialOption02_chk, QualityIndicators_PsychosocialOption02_txt)

        'Save Assesments
        QIgrp.SaveControls("QualityIndicators_NoReassessments_chk", QualityIndicators_NoReassessments_chk, QualityIndicators_NoReassessments_txt)
        QIgrp.SaveControls("QualityIndicators_VitalSignsND_chk", QualityIndicators_VitalSignsND_chk, QualityIndicators_VitalSignsND_txt)
        QIgrp.SaveControls("QualityIndicators_AssessmentOption01_chk", QualityIndicators_AssessmentOption01_chk, QualityIndicators_AssessmentOption01_txt)
        QIgrp.SaveControls("QualityIndicators_AssessmentOption02_chk", QualityIndicators_AssessmentOption02_chk, QualityIndicators_AssessmentOption02_txt)

        QIgrp.SaveControls("Assessments_NoReassessments_chk", QualityIndicators_NoReassessments_chk, QualityIndicators_NoReassessments_txt)
        QIgrp.SaveControls("Assessments_VitalSignsND_chk", QualityIndicators_VitalSignsND_chk, QualityIndicators_VitalSignsND_txt)
        QIgrp.SaveControls("Assessments_QIOption01_chk", QualityIndicators_AssessmentOption01_chk, QualityIndicators_AssessmentOption01_txt)
        QIgrp.SaveControls("Assessments_QIOption02_chk", QualityIndicators_AssessmentOption02_chk, QualityIndicators_AssessmentOption02_txt)

        'save Disposition
        QIgrp.SaveControls("QualityIndicators_DispositionAssessmentND_chk", QualityIndicators_DispositionAssessmentND_chk, QualityIndicators_DispositionAssessmentND_txt)
        QIgrp.SaveControls("QualityIndicators_DispositionND_chk", QualityIndicators_DispositionND_chk, QualityIndicators_DispositionND_txt)
        QIgrp.SaveControls("QualityIndicators_DischargePainAssessmentND_chk", QualityIndicators_DischargePainAssessmentND_chk, QualityIndicators_DischargePainAssessmentND_txt)
        QIgrp.SaveControls("QualityIndicators_DischargeVitalSignsND_chk", QualityIndicators_DischargeVitalSignsND_chk, QualityIndicators_DischargeVitalSignsND_txt)
        QIgrp.SaveControls("QualityIndicators_DispositionOption01_chk", QualityIndicators_DispositionOption01_chk, QualityIndicators_DispositionOption01_txt)
        QIgrp.SaveControls("QualityIndicators_DispositionOption02_chk", QualityIndicators_DispositionOption02_chk, QualityIndicators_DispositionOption02_txt)

        QIgrp.SaveControls("Disposition_DispositionAssessmentND_chk", QualityIndicators_DispositionAssessmentND_chk, QualityIndicators_DispositionAssessmentND_txt)
        QIgrp.SaveControls("Disposition_DispositionND_chk", QualityIndicators_DispositionND_chk, QualityIndicators_DispositionND_txt)
        QIgrp.SaveControls("Disposition_DischargePainAssessmentND_chk", QualityIndicators_DischargePainAssessmentND_chk, QualityIndicators_DischargePainAssessmentND_txt)
        QIgrp.SaveControls("Disposition_DischargeVitalSignsND_chk", QualityIndicators_DischargeVitalSignsND_chk, QualityIndicators_DischargeVitalSignsND_txt)
        QIgrp.SaveControls("Disposition_QualityIndicatorsOption01_chk", QualityIndicators_DispositionOption01_chk, QualityIndicators_DispositionOption01_txt)
        QIgrp.SaveControls("Disposition_QualityIndicatorsOption02_chk", QualityIndicators_DispositionOption02_chk, QualityIndicators_DispositionOption02_txt)

        'Save Tests
        QIgrp.SaveControls("QualityIndicators_TestND_chk", QualityIndicators_TestND_chk, QualityIndicators_TestND_txt)
        QIgrp.SaveControls("QualityIndicators_TestsOption01_chk", QualityIndicators_TestsOption01_chk, QualityIndicators_TestsOption01_txt)
        QIgrp.SaveControls("QualityIndicators_TestsOption02_chk", QualityIndicators_TestsOption02_chk, QualityIndicators_TestsOption02_txt)

        QIgrp.SaveControls("Tests_TestND_chk", QualityIndicators_TestND_chk, QualityIndicators_TestND_txt)
        QIgrp.SaveControls("Tests_Option01_chk", QualityIndicators_TestsOption01_chk, QualityIndicators_TestsOption01_txt)
        QIgrp.SaveControls("Tests_Option02_chk", QualityIndicators_TestsOption02_chk, QualityIndicators_TestsOption02_txt)

        'Save Medications
        QIgrp.SaveControls("QualityIndicators_RouteUnclear_chk", QualityIndicators_RouteUnclear_chk, QualityIndicators_RouteUnclear_txt)
        QIgrp.SaveControls("QualityIndicators_TimesND_chk", QualityIndicators_TimesND_chk, QualityIndicators_TimesND_txt)
        QIgrp.SaveControls("QualityIndicators_UnableToDetermineInjections_chk", QualityIndicators_UnableToDetermineInjections_chk, QualityIndicators_UnableToDetermineInjections_txt)
        QIgrp.SaveControls("QualityIndicators_IncompleteNursingDocumentation_chk", QualityIndicators_IncompleteNursingDocumentation_chk, QualityIndicators_IncompleteNursingDocumentation_txt)
        QIgrp.SaveControls("QualityIndicators_MedicationsOption01_chk", QualityIndicators_MedicationsOption01_chk, QualityIndicators_MedicationsOption01_txt)
        QIgrp.SaveControls("QualityIndicators_MedicationsOption02_chk", QualityIndicators_MedicationsOption02_chk, QualityIndicators_MedicationsOption02_txt)

        QIgrp.SaveControls("Medication_RouteUnclear_chk", QualityIndicators_RouteUnclear_chk, QualityIndicators_RouteUnclear_txt)
        QIgrp.SaveControls("Medication_TimesND_chk", QualityIndicators_TimesND_chk, QualityIndicators_TimesND_txt)
        QIgrp.SaveControls("Medication_UnableToDetermineInjections_chk", QualityIndicators_UnableToDetermineInjections_chk, QualityIndicators_UnableToDetermineInjections_txt)
        QIgrp.SaveControls("Medication_IncompleteNursingDocumentation_chk", QualityIndicators_IncompleteNursingDocumentation_chk, QualityIndicators_IncompleteNursingDocumentation_txt)
        QIgrp.SaveControls("Medication_QI_Option01_chk", QualityIndicators_MedicationsOption01_chk, QualityIndicators_MedicationsOption01_txt)
        QIgrp.SaveControls("Medication_QI_Option02_chk", QualityIndicators_MedicationsOption02_chk, QualityIndicators_MedicationsOption02_txt)

        'Save Infusions
        QIgrp.SaveControls("QualityIndicators_IncompleteTimes_chk", QualityIndicators_IncompleteTimes_chk, QualityIndicators_IncompleteTimes_txt)
        QIgrp.SaveControls("QualityIndicators_IncorrectNursingDocumentation_chk", QualityIndicators_IncorrectNursingDocumentation_chk, QualityIndicators_IncorrectNursingDocumentation_txt)
        QIgrp.SaveControls("QualityIndicators_SitesND_chk", QualityIndicators_SitesND_chk, QualityIndicators_SitesND_txt)
        QIgrp.SaveControls("QualityIndicators_InfusionsOption01_chk", QualityIndicators_InfusionsOption01_chk, QualityIndicators_InfusionsOption01_txt)
        QIgrp.SaveControls("QualityIndicators_InfusionsOption02_chk", QualityIndicators_InfusionsOption02_chk, QualityIndicators_InfusionsOption02_txt)

        QIgrp.SaveControls("Infusion_IncompleteTimes_chk", QualityIndicators_IncompleteTimes_chk, QualityIndicators_IncompleteTimes_txt)
        QIgrp.SaveControls("Infusion_IncorrectNursingDocumentation_chk", QualityIndicators_IncorrectNursingDocumentation_chk, QualityIndicators_IncorrectNursingDocumentation_txt)
        QIgrp.SaveControls("Infusion_SitesND_chk", QualityIndicators_SitesND_chk, QualityIndicators_SitesND_txt)
        QIgrp.SaveControls("Infusion_QIOption01_chk", QualityIndicators_InfusionsOption01_chk, QualityIndicators_InfusionsOption01_txt)
        QIgrp.SaveControls("Infusion_QIOption02_chk", QualityIndicators_InfusionsOption02_chk, QualityIndicators_InfusionsOption02_txt)

        'Save Procedures
        QIgrp.SaveControls("QualityIndicators_ProcedureTimeOut_chk", QualityIndicators_ProcedureTimeOut_chk, QualityIndicators_ProcedureTimeOut_txt)
        QIgrp.SaveControls("QualityIndicators_ProceduresOption01_chk", QualityIndicators_ProceduresOption01_chk, QualityIndicators_ProceduresOption01_txt)
        QIgrp.SaveControls("QualityIndicators_ProceduresOption02_chk", QualityIndicators_ProceduresOption02_chk, QualityIndicators_ProceduresOption02_txt)
        QIgrp.SaveControls("QualityIndicators_ProceduresOption03_chk", QualityIndicators_ProceduresOption03_chk, QualityIndicators_ProceduresOption03_txt)
        QIgrp.SaveControls("QualityIndicators_ProceduresOption04_chk", QualityIndicators_ProceduresOption04_chk, QualityIndicators_ProceduresOption04_txt)
        QIgrp.SaveControls("QualityIndicators_ProceduresOption05_chk", QualityIndicators_ProceduresOption05_chk, QualityIndicators_ProceduresOption05_txt)

        QIgrp.SaveControls("Procedures02_ProcedureTimeOut_chk", QualityIndicators_ProcedureTimeOut_chk, QualityIndicators_ProcedureTimeOut_txt)
        QIgrp.SaveControls("Procedures02_QI_Option01_chk", QualityIndicators_ProceduresOption01_chk, QualityIndicators_ProceduresOption01_txt)
        QIgrp.SaveControls("Procedures02_QI_Option02_chk", QualityIndicators_ProceduresOption02_chk, QualityIndicators_ProceduresOption02_txt)
        QIgrp.SaveControls("Procedures02_QI_Option03_chk", QualityIndicators_ProceduresOption03_chk, QualityIndicators_ProceduresOption03_txt)
        QIgrp.SaveControls("Procedures02_QI_Option04_chk", QualityIndicators_ProceduresOption04_chk, QualityIndicators_ProceduresOption04_txt)
        QIgrp.SaveControls("Procedures02_QI_Option05_chk", QualityIndicators_ProceduresOption05_chk, QualityIndicators_ProceduresOption05_txt)

        'Save Ortho ...
        QIgrp.SaveControls("QualityIndicators_IncompleteOrthoNote_chk", QualityIndicators_IncompleteOrthoNote_chk, QualityIndicators_IncompleteOrthoNote_txt)
        QIgrp.SaveControls("QualityIndicators_OrthoOption01_chk", QualityIndicators_OrthoOption01_chk, QualityIndicators_OrthoOption01_txt)
        QIgrp.SaveControls("QualityIndicators_OrthoOption02_chk", QualityIndicators_OrthoOption02_chk, QualityIndicators_OrthoOption02_txt)
        QIgrp.SaveControls("QualityIndicators_OrthoOption03_chk", QualityIndicators_OrthoOption03_chk, QualityIndicators_OrthoOption03_txt)
        QIgrp.SaveControls("QualityIndicators_OrthoOption04_chk", QualityIndicators_OrthoOption04_chk, QualityIndicators_OrthoOption04_txt)
        QIgrp.SaveControls("QualityIndicators_OrthoOption05_chk", QualityIndicators_OrthoOption05_chk, QualityIndicators_OrthoOption05_txt)

        QIgrp.SaveControls("Orthopedics_QI_IncompleteOrthoNote_chk", QualityIndicators_IncompleteOrthoNote_chk, QualityIndicators_IncompleteOrthoNote_txt)
        QIgrp.SaveControls("Orthopedics_QI_Option01_chk", QualityIndicators_OrthoOption01_chk, QualityIndicators_OrthoOption01_txt)
        QIgrp.SaveControls("Orthopedics_QI_Option02_chk", QualityIndicators_OrthoOption02_chk, QualityIndicators_OrthoOption02_txt)
        QIgrp.SaveControls("Orthopedics_QI_Option03_chk", QualityIndicators_OrthoOption03_chk, QualityIndicators_OrthoOption03_txt)
        QIgrp.SaveControls("Orthopedics_QI_Option04_chk", QualityIndicators_OrthoOption04_chk, QualityIndicators_OrthoOption04_txt)
        QIgrp.SaveControls("Orthopedics_QI_Option05_chk", QualityIndicators_OrthoOption05_chk, QualityIndicators_OrthoOption05_txt)

        'Init Surgical ...
        QIgrp.SaveControls("QualityIndicators_IncompletedLacerationNote_chk", QualityIndicators_IncompletedLacerationNote_chk, QualityIndicators_IncompletedLacerationNote_txt)
        QIgrp.SaveControls("QualityIndicators_IncompletedFBIDNote_chk", QualityIndicators_IncompletedFBIDNote_chk, QualityIndicators_IncompletedFBIDNote_txt)
        QIgrp.SaveControls("QualityIndicators_SurgicalOption01_chk", QualityIndicators_SurgicalOption01_chk, QualityIndicators_SurgicalOption01_txt)
        QIgrp.SaveControls("QualityIndicators_SurgicalOption02_chk", QualityIndicators_SurgicalOption02_chk, QualityIndicators_SurgicalOption02_txt)
        QIgrp.SaveControls("QualityIndicators_SurgicalOption03_chk", QualityIndicators_SurgicalOption03_chk, QualityIndicators_SurgicalOption03_txt)

        QIgrp.SaveControls("Surgical_QI_IncompletedLacerationNote_chk", QualityIndicators_IncompletedLacerationNote_chk, QualityIndicators_IncompletedLacerationNote_txt)
        QIgrp.SaveControls("Surgical_IncompletedFBIDNote_chk", QualityIndicators_IncompletedFBIDNote_chk, QualityIndicators_IncompletedFBIDNote_txt)
        QIgrp.SaveControls("Surgical_QI_Option01_chk", QualityIndicators_SurgicalOption01_chk, QualityIndicators_SurgicalOption01_txt)
        QIgrp.SaveControls("Surgical_QI_Option02_chk", QualityIndicators_SurgicalOption02_chk, QualityIndicators_SurgicalOption02_txt)
        QIgrp.SaveControls("Surgical_QI_Option03_chk", QualityIndicators_SurgicalOption03_chk, QualityIndicators_SurgicalOption03_txt)

        'Init Misc ...
        QIgrp.SaveControls("QualityIndicators_MiscOption01_chk", QualityIndicators_MiscOption01_chk, QualityIndicators_MiscOption01_txt)
        QIgrp.SaveControls("QualityIndicators_MiscOption02_chk", QualityIndicators_MiscOption02_chk, QualityIndicators_MiscOption02_txt)
        QIgrp.SaveControls("QualityIndicators_MiscOption03_chk", QualityIndicators_MiscOption03_chk, QualityIndicators_MiscOption03_txt)
        QIgrp.SaveControls("QualityIndicators_MiscOption04_chk", QualityIndicators_MiscOption04_chk, QualityIndicators_MiscOption04_txt)
        QIgrp.SaveControls("QualityIndicators_MiscOption05_chk", QualityIndicators_MiscOption05_chk, QualityIndicators_MiscOption05_txt)
        QIgrp.SaveControls("QualityIndicators_MiscOption06_chk", QualityIndicators_MiscOption06_chk, QualityIndicators_MiscOption06_txt)

        QIgrp.SaveControls("Miscellaneous_QI_Option01_chk", QualityIndicators_MiscOption01_chk, QualityIndicators_MiscOption01_txt)
        QIgrp.SaveControls("Miscellaneous_QI_Option02_chk", QualityIndicators_MiscOption02_chk, QualityIndicators_MiscOption02_txt)
        QIgrp.SaveControls("Miscellaneous_QI_Option03_chk", QualityIndicators_MiscOption03_chk, QualityIndicators_MiscOption03_txt)
        QIgrp.SaveControls("Miscellaneous_QI_Option04_chk", QualityIndicators_MiscOption04_chk, QualityIndicators_MiscOption04_txt)
        QIgrp.SaveControls("Miscellaneous_QI_Option05_chk", QualityIndicators_MiscOption05_chk, QualityIndicators_MiscOption05_txt)
        QIgrp.SaveControls("Miscellaneous_QI_Option06_chk", QualityIndicators_MiscOption06_chk, QualityIndicators_MiscOption06_txt)

        'Clinic QIs
        QIgrp.SaveControls("Clinic_QI1_chk", Clinic_QI1_chk, Clinic_QI1_txt)
        QIgrp.SaveControls("Clinic_QI2_chk", Clinic_QI2_chk, Clinic_QI2_txt)
        QIgrp.SaveControls("Clinic_QI3_chk", Clinic_QI3_chk, Clinic_QI3_txt)
        QIgrp.SaveControls("Clinic_QI4_chk", Clinic_QI4_chk, Clinic_QI4_txt)

        'Clinic Prof Misc QI
        QIgrp.SaveControls("ClinicPhys_Misc_QI1_chk", ClinicPhys_Misc_QI1_chk, ClinicPhys_Misc_QI1_txt)
        QIgrp.SaveControls("ClinicPhys_Misc_QI2_chk", ClinicPhys_Misc_QI2_chk, ClinicPhys_Misc_QI2_txt)
        QIgrp.SaveControls("ClinicPhys_Misc_QI3_chk", ClinicPhys_Misc_QI3_chk, ClinicPhys_Misc_QI3_txt)
        QIgrp.SaveControls("ClinicPhys_Misc_QI4_chk", ClinicPhys_Misc_QI4_chk, ClinicPhys_Misc_QI4_txt)
        QIgrp.SaveControls("ClinicPhys_Misc_QI5_chk", ClinicPhys_Misc_QI5_chk, ClinicPhys_Misc_QI5_txt)
        QIgrp.SaveControls("ClinicPhys_Misc_QI6_chk", ClinicPhys_Misc_QI6_chk, ClinicPhys_Misc_QI6_txt)

    End Sub

#End Region

    Sub InitObservationTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup
        'Dim ept = lAppConfigGroup("EnablePhysicianTab")

        ceEnableObservationTab.Checked = lAppConfigGroup("EnableObservationTab", True).Enabled
        ceEnableOSAMode.Checked = lAppConfigGroup("ObservationStandAloneMode", True).Enabled

        If lAppConfigGroup("DefaultChartStatusObsTab", True).Enabled Then
            teDefaultObsChartStatus.EditValue = lAppConfigGroup("DefaultChartStatusObsTab").SettingValue
        Else
            Me.teDefaultObsChartStatus.EditValue = ""
        End If

        If lAppConfigGroup("ObservationManualEntryCDMLength", True).Enabled Then
            Me.seObsCDMMaxLength.EditValue = lAppConfigGroup("ObservationManualEntryCDMLength").SettingValue
        Else
            lAppConfigGroup("ObservationManualEntryCDMLength").Enabled = True
            Me.seObsCDMMaxLength.EditValue = 7
        End If

        'MaxObsEndDateDaysFromStartDate

        If lAppConfigGroup("ObsMaxEndDateDaysFromStartDate", True).Enabled Then
            Me.seObsMaxDaysFromStart.EditValue = lAppConfigGroup("ObsMaxEndDateDaysFromStartDate").SettingValue
        Else
            lAppConfigGroup("ObsMaxEndDateDaysFromStartDate").Enabled = True
            Me.seObsMaxDaysFromStart.EditValue = 30
        End If
    End Sub

    Sub SaveObsTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        'lAppConfigGroup("TriageVitalSignsRequired").Enabled = Me.ceTriageVitalSignsRequired.Checked
        Dim enableObsTab As DOConfigSetting = lAppConfigGroup("EnableObservationTab")
        enableObsTab.Enabled = Me.ceEnableObservationTab.Checked
        enableObsTab.Save()

        Dim enableOsaMode As DOConfigSetting = lAppConfigGroup("ObservationStandAloneMode")
        enableOsaMode.Enabled = Me.ceEnableOSAMode.Checked
        enableOsaMode.Save()

        'ObservationStandAloneMode

        'lAppConfigGroup("EnablePhysicianTab").Enabled = Me.ceEnablePhysicianTab.Checked

        Dim doChartStatus As DOConfigSetting = lAppConfigGroup("DefaultChartStatusObsTab")

        If String.IsNullOrEmpty(Me.teDefaultObsChartStatus.EditValue) Then
            doChartStatus.Enabled = False
        Else
            doChartStatus.Enabled = True
        End If

        doChartStatus.SettingValue = Me.teDefaultObsChartStatus.EditValue

        lAppConfigGroup("ObservationManualEntryCDMLength").Enabled = True
        lAppConfigGroup("ObservationManualEntryCDMLength").SettingValue = Me.seObsCDMMaxLength.EditValue

        lAppConfigGroup("ObsMaxEndDateDaysFromStartDate").Enabled = True
        lAppConfigGroup("ObsMaxEndDateDaysFromStartDate").SettingValue = Me.seObsMaxDaysFromStart.EditValue
    End Sub

    Sub InitEMShellTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup
        'Dim ept = lAppConfigGroup("EnablePhysicianTab")

        ceEnableEMGrid.Checked = lAppConfigGroup("EnableEMShellTab", True).Enabled
        ceUseAdvancedModel.Checked = lAppConfigGroup("EnableEMShellAdvanced", True).Enabled

        'ceUseUrgentCareConfig.Checked = lAppConfigGroup("UseUrgentCareConfig", True).Enabled

        If lAppConfigGroup("EMGridV2Level1", True).Enabled Then
            Me.seEMGridV2Level1.EditValue = lAppConfigGroup("EMGridV2Level1").SettingValue
        Else
            lAppConfigGroup("EMGridV2Level1").Enabled = True
            Me.seEMGridV2Level1.EditValue = 0
        End If

        If lAppConfigGroup("EMGridV2Level2", True).Enabled Then
            Me.seEMGridV2Level2.EditValue = lAppConfigGroup("EMGridV2Level2").SettingValue
        Else
            lAppConfigGroup("EMGridV2Level2").Enabled = True
            Me.seEMGridV2Level2.EditValue = 0
        End If

        If lAppConfigGroup("EMGridV2Level3", True).Enabled Then
            Me.seEMGridV2Level3.EditValue = lAppConfigGroup("EMGridV2Level3").SettingValue
        Else
            lAppConfigGroup("EMGridV2Level3").Enabled = True
            Me.seEMGridV2Level3.EditValue = 0
        End If

        If lAppConfigGroup("EMGridV2Level4", True).Enabled Then
            Me.seEMGridV2Level4.EditValue = lAppConfigGroup("EMGridV2Level4").SettingValue
        Else
            lAppConfigGroup("EMGridV2Level4").Enabled = True
            Me.seEMGridV2Level4.EditValue = 0
        End If

        If lAppConfigGroup("EMGridV2Level5", True).Enabled Then
            Me.seEMGridV2Level5.EditValue = lAppConfigGroup("EMGridV2Level5").SettingValue
        Else
            lAppConfigGroup("EMGridV2Level5").Enabled = True
            Me.seEMGridV2Level5.EditValue = 0
        End If

    End Sub

    Sub SaveEMShellTab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup

        Dim doSetting As DOConfigSetting = lAppConfigGroup("EnableEMShellTab")
        doSetting.Enabled = Me.ceEnableEMGrid.Checked
        'doSetting.Save()

        'doSetting

        doSetting = lAppConfigGroup("EnableEMShellAdvanced")
        doSetting.Enabled = Me.ceUseAdvancedModel.Checked
        ' doSetting.Save()

        'lAppConfigGroup("UseUrgentCareConfig").Enabled = ceUseUrgentCareConfig.Checked

        lAppConfigGroup("EMGridV2Level1").Enabled = True
        lAppConfigGroup("EMGridV2Level1").SettingValue = Me.seEMGridV2Level1.EditValue

        lAppConfigGroup("EMGridV2Level2").Enabled = True
        lAppConfigGroup("EMGridV2Level2").SettingValue = Me.seEMGridV2Level2.EditValue

        lAppConfigGroup("EMGridV2Level3").Enabled = True
        lAppConfigGroup("EMGridV2Level3").SettingValue = Me.seEMGridV2Level3.EditValue

        lAppConfigGroup("EMGridV2Level4").Enabled = True
        lAppConfigGroup("EMGridV2Level4").SettingValue = Me.seEMGridV2Level4.EditValue

        lAppConfigGroup("EMGridV2Level5").Enabled = True
        lAppConfigGroup("EMGridV2Level5").SettingValue = Me.seEMGridV2Level5.EditValue

    End Sub

#Region "Physician Quality Inidicators Tab"

    Sub InitPhysQITab()

        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup
        'Dim ept = lAppConfigGroup("EnablePhysicianTab")

        ceEnablePhysicianTab.Checked = lAppConfigGroup("EnablePhysicianTab", True).Enabled

        If lAppConfigGroup("DefaultChartStatusPhysTab", True).Enabled Then
            Me.teDefaultPhysChartStatus.EditValue = lAppConfigGroup("DefaultChartStatusPhysTab").SettingValue
        Else
            Me.teDefaultPhysChartStatus.EditValue = ""
        End If

        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides")) ' = GetControlConfigGroup("Qi")

        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_NoChiefComplaint_chk", Physician_Summary_QI_NoChiefComplaint2_chk, Physician_Summary_QI_NoChiefComplaint2_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteHPI_chk", Physician_Summary_QI_IncompleteHPI2_chk, Physician_Summary_QI_IncompleteHPI2_txt)

        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteROS_chk", Physician_Summary_QI_IncompleteROS2_chk, Physician_Summary_QI_IncompleteROS2_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompletePFSH_chk", Physician_Summary_QI_IncompletePFSH2_chk, Physician_Summary_QI_IncompletePFSH2_txt)

        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompletePE_chk", Physician_Summary_QI_IncompletePE2_chk, Physician_Summary_QI_IncompletePE2_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteMDM_chk", Physician_Summary_QI_IncompleteMDM2_chk, Physician_Summary_QI_IncompleteMDM2_txt)

        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_ChargesImpacted_chk", Physician_Summary_QI_ChargesImpacted2_chk, Physician_Summary_QI_ChargesImpacted2_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option3_chk", Physician_Summary_QI_Option32_chk, Physician_Summary_QI_Option32_txt)
        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option5_chk", Physician_Summary_QI_Option52_chk, Physician_Summary_QI_Option52_txt)

        '----
        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteLaceration_chk", Physician_Summary_QI_IncompleteLaceration2_chk, Physician_Summary_QI_IncompleteLaceration2_txt)
        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteOrtho_chk", Physician_Summary_QI_IncompleteOrtho2_chk, Physician_Summary_QI_IncompleteOrtho2_txt)

        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_IncompleteOtherSurgical_chk", Physician_Summary_QI_IncompleteOtherSurgical2_chk, Physician_Summary_QI_IncompleteOtherSurgical2_txt)
        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_NoDiagnosisListed_chk", Physician_Summary_QI_NoDiagnosisListed2_chk, Physician_Summary_QI_NoDiagnosisListed2_txt)

        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_ChartNotSigned_chk", Physician_Summary_QI_ChartNotSigned2_chk, Physician_Summary_QI_ChartNotSigned2_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option1_chk", Physician_Summary_QI_Option12_chk, Physician_Summary_QI_Option12_txt)

        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option2_chk", Physician_Summary_QI_Option22_chk, Physician_Summary_QI_Option22_txt)
        QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option4_chk", Physician_Summary_QI_Option42_chk, Physician_Summary_QI_Option42_txt)
        'QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option6_chk", Physician_Summary_QI_Option62_chk, Physician_Summary_QI_Option62_txt)

        ' QIgrp.LoadControls(ECForm, "Physician_Summary_QI_Option6_chk", Physician_Summary_QI_Option62_chk, Physician_Summary_QI_Option62_txt)

    End Sub

    Sub SavePhysQITab()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")

        'lAppConfigGroup("TriageVitalSignsRequired").Enabled = Me.ceTriageVitalSignsRequired.Checked
        Dim enablePhysTab As DOConfigSetting = lAppConfigGroup("EnablePhysicianTab")
        enablePhysTab.Enabled = Me.ceEnablePhysicianTab.Checked
        enablePhysTab.Save()

        'lAppConfigGroup("EnablePhysicianTab").Enabled = Me.ceEnablePhysicianTab.Checked

        Dim doChartStatus As DOConfigSetting = lAppConfigGroup("DefaultChartStatusPhysTab")

        If String.IsNullOrEmpty(Me.teDefaultPhysChartStatus.EditValue) Then
            doChartStatus.Enabled = False
        Else
            doChartStatus.Enabled = True
        End If

        doChartStatus.SettingValue = Me.teDefaultPhysChartStatus.EditValue

        Dim QIgrp As New DOControlConfigGroupAdv(GetControlConfigGroup("QIOverrides"))

        '...
        'QIgrp.SaveControls("Physician_Summary_QI_NoChiefComplaint_chk", Physician_Summary_QI_NoChiefComplaint2_chk, Physician_Summary_QI_NoChiefComplaint2_txt)
        QIgrp.SaveControls("Physician_Summary_QI_IncompleteHPI_chk", Physician_Summary_QI_IncompleteHPI2_chk, Physician_Summary_QI_IncompleteHPI2_txt)

        QIgrp.SaveControls("Physician_Summary_QI_IncompleteROS_chk", Physician_Summary_QI_IncompleteROS2_chk, Physician_Summary_QI_IncompleteROS2_txt)
        QIgrp.SaveControls("Physician_Summary_QI_IncompletePFSH_chk", Physician_Summary_QI_IncompletePFSH2_chk, Physician_Summary_QI_IncompletePFSH2_txt)

        QIgrp.SaveControls("Physician_Summary_QI_IncompletePE_chk", Physician_Summary_QI_IncompletePE2_chk, Physician_Summary_QI_IncompletePE2_txt)
        QIgrp.SaveControls("Physician_Summary_QI_IncompleteMDM_chk", Physician_Summary_QI_IncompleteMDM2_chk, Physician_Summary_QI_IncompleteMDM2_txt)

        'QIgrp.SaveControls("Physician_Summary_QI_ChargesImpacted_chk", Physician_Summary_QI_ChargesImpacted2_chk, Physician_Summary_QI_ChargesImpacted2_txt)
        QIgrp.SaveControls("Physician_Summary_QI_Option3_chk", Physician_Summary_QI_Option32_chk, Physician_Summary_QI_Option32_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_Option5_chk", Physician_Summary_QI_Option52_chk, Physician_Summary_QI_Option52_txt)

        '----
        'QIgrp.SaveControls("Physician_Summary_QI_IncompleteLaceration_chk", Physician_Summary_QI_IncompleteLaceration2_chk, Physician_Summary_QI_IncompleteLaceration2_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_IncompleteOrtho_chk", Physician_Summary_QI_IncompleteOrtho2_chk, Physician_Summary_QI_IncompleteOrtho2_txt)

        'QIgrp.SaveControls("Physician_Summary_QI_IncompleteOtherSurgical_chk", Physician_Summary_QI_IncompleteOtherSurgical2_chk, Physician_Summary_QI_IncompleteOtherSurgical2_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_NoDiagnosisListed_chk", Physician_Summary_QI_NoDiagnosisListed2_chk, Physician_Summary_QI_NoDiagnosisListed2_txt)

        'QIgrp.SaveControls("Physician_Summary_QI_ChartNotSigned_chk", Physician_Summary_QI_ChartNotSigned2_chk, Physician_Summary_QI_ChartNotSigned2_txt)
        QIgrp.SaveControls("Physician_Summary_QI_Option1_chk", Physician_Summary_QI_Option12_chk, Physician_Summary_QI_Option12_txt)

        QIgrp.SaveControls("Physician_Summary_QI_Option2_chk", Physician_Summary_QI_Option22_chk, Physician_Summary_QI_Option22_txt)
        QIgrp.SaveControls("Physician_Summary_QI_Option4_chk", Physician_Summary_QI_Option42_chk, Physician_Summary_QI_Option42_txt)
        'QIgrp.SaveControls("Physician_Summary_QI_Option6_chk", Physician_Summary_QI_Option62_chk, Physician_Summary_QI_Option62_txt)

    End Sub

#End Region

#Region "Util"
    Sub LoadNonConfigInstanceSettings()
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetConfigSettingGroup("Application")
        ceUseConfigInstances.Checked = lAppConfigGroup("UseConfigInstances", True).Enabled

    End Sub

    Private Function GetControlConfigGroup(ByVal GrpName As String, Optional ByVal pbCreateNew As Boolean = True) As DOControlConfigGroup
        For Each ccg As DOControlConfigGroup In ECGlobals.CurrentConfigInstance.ControlOverrides
            If ccg.GroupName = GrpName Then
                Return ccg
            End If
        Next

        If Not pbCreateNew Then
            Return Nothing
        End If

        'didn't find it... so lets create one for shits and giggles
        Dim rccg As New DOControlConfigGroup
        rccg.GroupName = GrpName
        rccg.Enabled = True

        ECGlobals.CurrentConfigInstance.ControlOverrides.Add(rccg)

        Return rccg
    End Function

    Sub EnableViewing()
        XtraTabControl1.Enabled = True
        XtraTabControl1.Visible = True
    End Sub


    Sub EnableEditing()
        bbiEditCurrentConfig.Caption = "End Edit"
        XtraTabControl1.Enabled = True
        bbiEndEditConfig.Enabled = True
        Me.bbiSaveChanges.Enabled = True
        Me.bbiViewCurrentConfig.Enabled = False

        Me.bbiEditCurrentConfig.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Me.bbiNewCI.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Me.bbiEditPending.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Me.bbiEndEdit.Visibility = DevExpress.XtraBars.BarItemVisibility.Always

        XtraTabControl1.Visible = True
    End Sub

    Sub EndEditConfig()
        'SaveAllTabs()

        Me.bbiEditCurrentConfig.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Me.bbiNewCI.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Me.bbiEditPending.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Me.bbiEndEdit.Visibility = DevExpress.XtraBars.BarItemVisibility.Never

        bbiEditCurrentConfig.Caption = "Edit"
        'Me.bbiEditCurrentConfig.Enabled = True

        Me.cePending.Enabled = False
        'Me.cbeFormClassaName.Enabled = False
        'Me.ceMajorChange.Enabled = False

        Me.bbiEndEditConfig.Enabled = False
        Me.bbiSaveChanges.Enabled = False

        Me.bsiFacilityLabel.Enabled = True
        Me.beiFacility2.Enabled = True
        'Me.bbiViewCurrentConfig.Enabled = True

        'XtraTabControl1.Visible = False
        XtraTabControl1.Enabled = False
        Me.CreatingNewCI = False
        Me.EditingPendingConfiguration = False
        EditingPendingConfiguration = False

        ECGlobals.CurrentConfigInstance = Nothing

        'jjc 06.20.13 ---
        'these next 2 controls are mutually exclusive... so to avoid getting a warning dialog when
        'sequentially editing mutliple config instances with these two settings different just reset them here...
        'Would probably be good idea to clear them all ...
        ceEnableEMGrid.Checked = False
        ceEnableOSAMode.Checked = False

        EditingPrevCI = False

    End Sub
#End Region

    '#Region "Event Handlers"
    Private Sub bbiEditPending_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditPending.ItemClick
        Me.EditingPendingConfiguration = True
        DoEdit()
        DoLabelCurPend(True, False)
    End Sub

    Private Sub bbiNewCI_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiNewCI.ItemClick
        If MessageBox.Show("Are you sure you want to create a new ConfigInstance?", "Create New Configinstance?", MessageBoxButtons.OKCancel) _
        = DialogResult.Cancel Then
            Return
        End If
        BasicTimeoutWatcher.Suspend()
        Try
            Me.CreatingNewCI = True
            DoEdit()
            DoLabelCurPend(True, False)

        Finally
            BasicTimeoutWatcher.Resume()
        End Try

    End Sub
    Private Sub bbiEditCurrentConfig_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditCurrentConfig.ItemClick
        CheckFacilityConfig(ECGlobals.CurrentFacility)
        DoEdit()
        DoLabelCurPend(False, False)
    End Sub

    Private OriginalMajorChangeDate As Date

    Sub DoLabelCurPend(ByVal blPend As Boolean, ByVal blOn As Boolean) ', ByVal blOff As Boolean)
        If ECGlobals.CurrentFacility Is Nothing Then
            Exit Sub
        End If
        'If Pending
        If Not blPend Then
            ' lblCurrentPending.Text = "Current Facility # " + ECGlobals.CurrentFacility.Oid.ToString + " Config Instance"
            Me.bbiEndEdit.Caption = "End Edit"
            blInPending = False
            BarStaticImportConfig.Enabled = False
        Else
            ' lblCurrentPending.Text = "Pending Facility # " + ECGlobals.CurrentFacility.Oid.ToString + " Config Instance"
            Me.bbiEndEdit.Caption = "End Pending Edit"
            blInPending = True
            BarStaticImportConfig.Enabled = True
        End If


        lblCurrentPending.Text = "Facility # " + ECGlobals.CurrentFacility.Oid.ToString ' + " Config Instance"
        lblCurrentPending.Visible = True
        btnFormClose.Enabled = blOn

        Me.bsbeiFacility.EditValue = "[Facility #" + ECGlobals.CurrentFacility.Oid.ToString + "] [Name: " + ECGlobals.CurrentFacility.LongName + "]"

    End Sub

    Sub DoEdit()
        Me.bsiFacilityLabel.Enabled = False
        Me.beiFacility2.Enabled = False

        ECGlobals.CurrentConfigInstance = Me.GetConfigInstanceToEdit

        'Store this so we can come back to this date
        OriginalMajorChangeDate = ECGlobals.CurrentConfigInstance.MajorChangeDate

        InitTabs()
        EnableEditing()

        If CreatingNewCI Then
            Me.cePending.Checked = True
            Me.ceMajorChange.Checked = True
        End If

        If Not CreatingNewCI AndAlso Not EditingPendingConfiguration Then
            Me.deActivationDate.Enabled = False
        Else
            Me.deActivationDate.Enabled = True
        End If

        bMajorChangeDateChanged = False

    End Sub

    Private Sub bbiEndEdit_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEndEdit.ItemClick
        If MessageBox.Show("Are you sure you want to exit edit mode?", "End Edit?", MessageBoxButtons.OKCancel) _
        = DialogResult.Cancel Then
            Return
        End If
        EndEditConfig()
        Me.InitToolBarButtons()
        DoLabelCurPend(False, True)

        If blImportedConfig And Not blSavedConfig Then
            cbeFormClassaName.Text = prevFormClass
            Dim dt As New DateTimePicker
            dt.Text = Now()
            deActivationDate.DateTime = prevActivDt
            deMajorChange.DateTime = prevMajorDt
            teComments.Text = prevComments
            blImportedConfig = False
            blSavedConfig = False
        End If

    End Sub

    Private Sub bbiEditPendingConfig_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs)
        ECGlobals.CurrentConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
    End Sub

    Private Sub bbiEndEditConfig_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEndEditConfig.ItemClick
        EndEditConfig()
    End Sub

    Private Sub bbiViewCurrentConfig_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiViewCurrentConfig.ItemClick
        ECGlobals.CurrentConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
        InitTabs()

        EnableViewing()
    End Sub

    Private Sub beiFacility_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles beiFacility.EditValueChanged
        If TypeOf beiFacility.EditValue Is String Then
            Exit Sub
        End If

        ECGlobals.CurrentFacility = beiFacility.EditValue

        If ECGlobals.CurrentFacility IsNot Nothing Then
            ' Me.beiECID.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.LongName
            Me.sbbeiEID.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.LongName
            Me.sbbeiEID.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.Oid.ToString
            Me.sbbeiEID.Hint += ", ID = " & ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.EnchartID.ToString

            ' Me.beiCompany.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.LongName
            Me.sbbeiCompany.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.LongName
            Me.sbbeiCompany.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Company.Oid.ToString
            Me.sbbeiCompany.Hint += ", CompanyID = " & ECGlobals.CurrentFacility.CompanyClient.Company.CompanyID.ToString

            ' beiCompanyClient.EditValue = ECGlobals.CurrentFacility.CompanyClient.LongName
            Me.sbbeiCompanyClient.EditValue = ECGlobals.CurrentFacility.CompanyClient.LongName
            Me.sbbeiCompanyClient.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Oid.ToString
            Me.sbbeiCompanyClient.Hint += ", CompanyClientID = " & ECGlobals.CurrentFacility.CompanyClient.CompanyClientID.ToString

            Me.bsbeiFacility.EditValue = ECGlobals.CurrentFacility.LongName
            Me.bsbeiFacility.Hint = "OID = " & ECGlobals.CurrentFacility.Oid.ToString
            Me.bsbeiFacility.Hint += ", FacilityID = " & ECGlobals.CurrentFacility.FacilityID.ToString
        End If
        'Default to Active/Current Config
        'ECGlobals.CurrentConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
        'Me.bbiViewCurrentConfig.Enabled = True
        Me.bbiEditCurrentConfig.Enabled = True

        ''Reload Settigns
        'InitTabs()
        'EnableViewing()

        'Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentConfigInstance.AppConfigGroup ' GetConfigSettingGroup("Application")
        'Me.ceUseConfigInstances.Checked = lAppConfigGroup("UseConfigInstances", True).Enabled

        'If Me.ceUseConfigInstances.Checked = False Then
        '    Me.StartEditMode()
        '    ' EnableEditing()
        'End If

        ''XtraTabControl1.Visible = False
    End Sub

    Protected lConfigInstanceToEdit As DOConfigInstance 'pointer to source config that will be copied or edited.
    Protected lNewConfigInstance As DOConfigInstance

    Protected Function GetConfigInstanceToEdit() As DOConfigInstance
        'Note DOFacility.ConfigInstanceVersion will create and save a blank CI for the facility if one does not already exist
        lNewConfigInstance = New DOConfigInstance

        Me.ConfigInstanceEnabled = True 'ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup("UseConfigInstances").Enabled
        _ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion 'default to this...

        'Dont' forget, the ActiveFormClassName is inialized to the sharedActiveFormClassName in the DOConfigInstance constructor.
        'why you ask.... (I have no idea! Seemed like a good idea at the time.)
        Try
            If Me.CreatingNewCI Then
                lConfigInstanceToEdit = ECGlobals.CurrentFacility.ConfigInstanceVersion
                _ConfigInstance = lNewConfigInstance 'to make this all as confusing as possible :-(
                lNewConfigInstance.Facility = ECGlobals.CurrentFacility

                'Inherit the MajorChangeDate from currently active one.... and overwrite later with new date
                'if new config is another major update that will be incompatible.

                lNewConfigInstance.MajorChangeDate = lConfigInstanceToEdit.MajorChangeDate
                lNewConfigInstance.ActiveFormClassName = lConfigInstanceToEdit.ActiveFormClassName
                lNewConfigInstance.Save() 'save now so object gets assigned an instance ID that will be used in child objects.

                DupData() 'duplicate old settings so we can edit new without changing old
            End If

            If Me.EditingPendingConfiguration Then
                'lConfigInstanceToEdit = ECGlobals.CurrentFacility.PendingConfigInstanceVersion
                _ConfigInstance = ECGlobals.CurrentFacility.PendingConfigInstanceVersion
                EditingPendingConfiguration = True
                'Me.ceMajorChange.Enabled = True
                Me.deActivationDate.Enabled = True
                Me.cePending.Enabled = True
            End If

            If Not Me.ConfigInstanceEnabled Then 'If lAppConfigGroup("UseConfigInstances").Enabled = false
                _ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
            End If

            'This is a config error, but i think we can recover and just treat it as if it was disabled.
        Catch ex As ConfigSettingNotFoundException
            If ex.ConfigSettingName = "UseConfigInstances" Then
                'lets complain, but not abort
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                _ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
                GoTo InitControls
            Else
                Throw
            End If

        End Try

        'Note, if the user selects not to create a new CI, we jump straight to here... thus...we'll be editing an existing CI
InitControls:
        Debug.Assert(_ConfigInstance IsNot Nothing)

        Me.lblConfigInstanceOID.Text = "ConfigInstance OID: " & _ConfigInstance.Oid.ToString
        If _ConfigInstance.IsDeleted Then
            Me.lblConfigInstanceOID.Text += "(WARNING: ConfigInstance is marked for deletion!)"
        End If

        If CreatingNewCI Then
            'we need to do this or there would be no way to create a pending config
            Me.cePending.Enabled = True
        End If

        If EditingPendingConfiguration Then 'ECGlobals.CurrentFacility.HasPendingConfig Then
            'This will set activationdate time to 12:00 AM, so it needs to be set before activationDate
            'or when loading a pending config for edit, the time portion would always say 12:00 am regardless
            'of what it actually is in the database...
            'See cePending.checkchanged eventhandler for me info...
            Me.cePending.Enabled = True
            Me.cePending.Checked = True

        Else
            Me.cePending.Checked = False
        End If

        If Not CreatingNewCI AndAlso Not EditingPendingConfiguration Then

        End If

        Me.ceMajorChange.EditValue = _ConfigInstance.MajorChange
        If Not _ConfigInstance.ActivationDate = #12:00:00 AM# Then
            Me.deActivationDate.EditValue = _ConfigInstance.ActivationDate
        End If

        If Me.CreatingNewCI Then
            Me.deActivationDate.EditValue = Now
        End If

        If Not _ConfigInstance.MajorChangeDate = #12:00:00 AM# Then
            Me.deMajorChange.EditValue = _ConfigInstance.MajorChangeDate
        End If

        Me.teCreationDate.EditValue = _ConfigInstance.CreationDate
        Me.teComments.EditValue = _ConfigInstance.Comments
        Me.cbeFormClassaName.EditValue = _ConfigInstance.ActiveFormClassName

        Me.deReadOnlyActivationDate.EditValue = _ConfigInstance.ReadOnlyActivationDate

        Return _ConfigInstance
    End Function

    Sub DupData()
        Dim f As New DoingSomethingMsgForm
        f.lbMsg.Text = "Taking Out the Trash"
        f.UpDateProgress("Probably about to do something important", 0)
        f.Show()

        Dim progress As Double = 0
        Dim pstep As Short = 7
        Try
            f.UpDateProgress("Copying ComboBoxLists", progress)
            Dim count = lConfigInstanceToEdit.ComboBoxLists.Count
            Dim step1 = 70 / count
            For Each cboList As DOConfigComboBoxList In lConfigInstanceToEdit.ComboBoxLists
                Dim lNewCboList As New DOConfigComboBoxList(cboList)
                lNewCboList.ConfigInstance = lNewConfigInstance
                lNewCboList.Save()
                lNewConfigInstance.ComboBoxLists.Add(lNewCboList)
                progress += step1
                f.Progress(progress)
            Next

            progress += pstep
            f.UpDateProgress("Copying ConfigSettings", progress)
            For Each lConfigGroup As DOConfigGroup In lConfigInstanceToEdit.ConfigSettingsGroups
                Dim lNewConfigSetting As New DOConfigGroup(lConfigGroup)
                lNewConfigSetting.ConfigInstance = lNewConfigInstance
                lNewConfigSetting.Save()
                lNewConfigInstance.ConfigSettingsGroups.Add(lNewConfigSetting)
            Next

            progress += pstep
            f.UpDateProgress("Copying ControlOverrides", progress)
            For Each lConfigGroup As DOControlConfigGroup In lConfigInstanceToEdit.ControlOverrides
                Dim lNewControlConfig As New DOControlConfigGroup(lConfigGroup)
                lNewControlConfig.ConfigInstance = lNewConfigInstance
                lNewControlConfig.Save()
                lNewConfigInstance.ControlOverrides.Add(lNewControlConfig)
            Next

            progress += pstep
            f.UpDateProgress("Copying CodingReportTexts", progress)
            For Each CodingReportText As DOCodingReportText In lConfigInstanceToEdit.CodingReportTexts
                Dim lNewDOCodingReportText As New DOCodingReportText(CodingReportText)
                lNewDOCodingReportText.ConfigInstance = lNewConfigInstance
                lNewDOCodingReportText.Save()
                lNewConfigInstance.CodingReportTexts.Add(lNewDOCodingReportText)
            Next

            progress += pstep
            f.UpDateProgress("Copying PhysicianCodesFilter", progress)
            For Each PhysicianCodeFilter As DOPhysicianCodesFilter In lConfigInstanceToEdit.PhysicianCodesFilter
                Dim lnewcode As New DOPhysicianCodesFilter(PhysicianCodeFilter)
                lnewcode.ConfigInstance = lNewConfigInstance
                lnewcode.Save()
                lNewConfigInstance.PhysicianCodesFilter.Add(lnewcode)
            Next

            progress += pstep
            f.UpDateProgress("Copying Medications", progress)
            For Each Medication As DOMedication In lConfigInstanceToEdit.Medications
                Dim m As New DOMedication(Medication)
                m.ConfigInstance = lNewConfigInstance
                m.Save()
                lNewConfigInstance.Medications.Add(m)
            Next

            progress += pstep
            f.UpDateProgress("Copying SupplyCatMap", progress)
            For Each SupplyCategoryMap In lConfigInstanceToEdit.SupplyCatMap
                Dim s As New DOSupplyCatMap(SupplyCategoryMap)
                s.ConfigInstance = lNewConfigInstance
                s.Save()
                lNewConfigInstance.SupplyCatMap.Add(s)
            Next

            For Each deductedTime In lConfigInstanceToEdit.DeductedTimes
                Dim d As New DODeductedTimes(deductedTime)
                d.ConfigInstance = lNewConfigInstance
                d.Save()
                lNewConfigInstance.DeductedTimes.Add(d)
            Next

            '' --- JJC commented out (below) fix as it has not been approved to be fixed by cCB ---
            '' --- Uncomment the below for each to copy financialClassMappings
            For Each fclassMap In lConfigInstanceToEdit.FinancialClassMappings
                Dim newFinClassMap As New DOFinancialClassMapping(fclassMap)
                newFinClassMap.ConfigInstance = lNewConfigInstance
                newFinClassMap.Save()
                lNewConfigInstance.FinancialClassMappings.Add(newFinClassMap)
            Next

            f.UpDateProgress("CI Created Successfully", 100)
            Threading.Thread.Sleep(250)

        Finally

            If f IsNot Nothing Then
                f.Close()
                f.Dispose()
            End If
        End Try
    End Sub

    Private Sub UpdateAllCIsPriorToOneBeingEditedToSame_ReadOnlyActivationDate()
        If ceUpdatePriorCIs.Checked = False Then
            Return
        End If
        ceUpdatePriorCIs.Checked = False
        Dim pCIList As System.Collections.Generic.List(Of DOConfigInstance) = GetCIList(_ConfigInstance.Facility)
        Dim bCurCIFound As Boolean = False
        For Each ci In pCIList
            If ci = _ConfigInstance Then
                bCurCIFound = True
                Continue For
            End If

            If bCurCIFound Then
                ci.ReadOnlyActivationDate = _ConfigInstance.ReadOnlyActivationDate
                ci.Save()
            End If

        Next
    End Sub
    ''' <summary>
    ''' Handles config instance processing and saving from the info on the generals tab.
    ''' </summary>
    ''' <remarks></remarks>
    Sub DOCILogic()

        'MajorChangeDate is initialized on startup to the previous configs majorChangeDate.

        'JJC 06.18.08 The only reason I can think of for the MajorChange boolean field to exist in the CI
        'is when a pending change is actived, we would know to update the majorChangeDate to the Activated date...
        _ConfigInstance.MajorChange = Me.ceMajorChange.EditValue

        _ConfigInstance.Comments = Me.teComments.EditValue
        _ConfigInstance.ActiveFormClassName = Me.cbeFormClassaName.EditValue

        ' _ConfigInstance.IsReadOnly = ceReadOnly.EditValue 'jjc 06.13.13
        _ConfigInstance.ReadOnlyActivationDate = deReadOnlyActivationDate.EditValue

        If EditingPendingConfiguration Then
            If cePending.Checked Then
                ECGlobals.CurrentFacility.PendingConfigInstanceVersion = _ConfigInstance
            Else 'turning off the pending flag on a pending config means we want to basically delete it
                'So here we are basically want to orphan this config
                ' if we are editing a pending configration and the Pendind checkbox is now deselected
                ' let's just throw out this config, and update the facility record...
                ECGlobals.CurrentFacility.HasPendingConfig = False
                ECGlobals.CurrentFacility.Save()
                Exit Sub
            End If
        End If

        'Scrub the seconds portion of MajorChangedate off...
        _ConfigInstance.MajorChangeDate = Me.deMajorChange.EditValue
        Dim d2 As DateTime = _ConfigInstance.MajorChangeDate
        Dim ddd As New DateTime(d2.Year, d2.Month, d2.Day, d2.Hour, d2.Minute, 0) ' = _ConfigInstance.MajorChangeDate
        _ConfigInstance.MajorChangeDate = ddd 'blah

        If Me.EditingPrevCI Then
            _ConfigInstance.Save()
            UpdateAllCIsPriorToOneBeingEditedToSame_ReadOnlyActivationDate()
            Return
        End If

        If cePending.Checked Then 'ok we're defining a new pending config
            _ConfigInstance.ActivatedDate = Nothing ' ???
            _ConfigInstance.ActivationDate = Me.deActivationDate.EditValue
            '            _ConfigInstance.MajorChangeDate = Me.deMajorChange.EditValue
            ECGlobals.CurrentFacility.HasPendingConfig = True
            ECGlobals.CurrentFacility.PendingConfigInstanceVersion = _ConfigInstance
        Else
            If Me.ConfigInstanceEnabled AndAlso Me.CreatingNewCI Then 'JJC added this condition on 6.18.08
                _ConfigInstance.ActivationDate = Now
                _ConfigInstance.ActivatedDate = _ConfigInstance.ActivationDate
                'lConfigInstance.MajorChangeDate is inherited from previous

                '********** This next line needs some looking at when we enable configInstance Logic!!!! ******
                ECGlobals.CurrentFacility.ConfigInstances.Add(_ConfigInstance) 'Not sure why i'm doing this...
                ECGlobals.CurrentFacility.ConfigInstanceVersion = _ConfigInstance
            End If 'added this condition on 6.18.08
        End If

        '10-17-07 Conditioned the below logic with if to not save if not using config instances...
        'because it causes locking issues when saving charts, among other things...

        Me._ConfigInstance.Save()
        If ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup("UseConfigInstances").Enabled Then
            '  _ConfigInstance.Save() '12-28-07 lets save everytime so we know somwone was in here...
            ECGlobals.CurrentFacility.Save()
        End If

        If Not EditingPendingConfiguration Then
            UpdateAllCIsPriorToOneBeingEditedToSame_ReadOnlyActivationDate()
        End If
    End Sub

    Private Sub DelayedStartup(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        Try
            If Not IsProcessingCommandLineArgs Then
                Me.Timer1.Stop()
                Me.ConnectToDatabase()

                SetupAuditLogger()
                '#Ifdef 
                Auth_MoreSecure.Init()
                AuthenticateUser()

                CheckConfigIntegrity()

            End If
        Catch ex As Exception

            Dim dummy As New FailedResult(Of Boolean)() With {.ErrorException = ex}
            MessageBox.Show($"Unexpected error : {dummy.GetExceptionChainMessage()}", "Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub


    Private Sub CheckConfigIntegrity()
        If EnchartDOLib.Utils.IsDbSchemaUpdateNeeded() Then
            If DialogResult.OK = MessageBox.Show($"The database schema is out of date and needs to be updated. It is strongly recommended that you update it now. This may take a long time... Do you wish to update it now?", "Database update needed", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) Then
                UpdateSchema()
            End If
        End If

        'JJC 3.18.22 commented out the below for loop as it takes too long on startup.
        'TODO: CheckFacilityConfig when editing facility config?

        'For Each facility In New XPCollection(GetType(DOFacility))
        '    CheckFacilityConfig(facility)
        'Next

        'InitEncryptMenuOption()
    End Sub

    Private Sub CheckFacilityConfig(facility As DOFacility)
        MoveNonCiListsToSharedCi(facility)
    End Sub

    Private Sub MoveNonCiListsToSharedCi(facility As DOFacility)
        For Each cboList In facility.ConfigInstanceVersion.ComboBoxLists
            If cboList.Enabled = False Then Continue For
            If IsSharedCboList(cboList) Then
                If facility.SharedConfigInstanceVersion.GetComboBoxList(cboList.ListName) Is Nothing Then
                    cboList.ConfigInstance = facility.SharedConfigInstanceVersion
                    cboList.Save()
                End If
            End If
        Next
    End Sub

    'Private Function IsSharedCboList(cboList As DOConfigComboBoxList) As Boolean
    '    Dim sharedListNames = New List(Of String)({"NurseList", "PhysicianList", "NPPAList", "ObsPhysicianList", "ObsNurseList"})
    '    Dim cboListDict As New Dictionary(Of String, String)
    '    cboListDict("Triage_Nurse_cbo") = "NurseList"
    '    cboListDict("Triage_Physician_cbo") = "PhysicianList"
    '    cboListDict("Triage_NPPA_cbo") = "NPPAList"
    '    cboListDict("ObsTimesFinalBillHoursPhys_cbo") = "ObsPhysicianList"
    '    cboListDict("Obs_Medication_ObsNurse_cbo") = "ObsNurseList"

    '    cboListDict("EMLevel_Physician_cbo") = "PhysicianList"
    '    cboListDict("EMLevel_NPPA_cbo") = "NPPAList"


    '    If sharedListNames.Contains(cboList.ListName) Then
    '        Return True
    '    End If

    '    For Each cbo In cboList.ComboBoxes
    '        If cboListDict.ContainsKey(cbo.ComboBoxName) Then
    '            If sharedListNames.Contains(cboListDict(cbo.ComboBoxName)) Then
    '                Return True
    '            End If
    '        End If
    '    Next

    '    Return False
    'End Function

    Private Sub AuthenticateUser()

        Dim useAdAuth As Boolean = DOGlobalSetting.GetSetting("UseActiveDirectoryLogIn").SettingValueAsBool = True
        '      Dim useEnhancedPasswordAuth As Boolean = DOGlobalSetting.GetSetting("UseEnhancedPasswords").SettingValueAsBool = True


        If ENChartCAC.Auth_MoreSecure.LogInUser() = False Then
            'Application.Exit()
            DoShutDown()
            Return
        End If

        ECGlobals.CurrentUser = ENChartCAC.ECGlobals.CurrentUser 'really?

        If Not (ENChartCAC.ECGlobals.CurrentUser?.IsSupportAccount Or ENChartCAC.ECGlobals.CurrentUser?.IsTheAICAdmin) Then
            MessageBox.Show("Only MIC support personnel are authorized to access this application!", "Unauthorized Access", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            DoShutDown()
        End If

    End Sub

    Private Function IsFirstTimeLoginWithConfigurableAdminPassword() As Boolean
        Throw New NotImplementedException()
        'Dim adminUser = DOUser.GetUserByIdAndDomain(ENChartCAC.Auth_MoreSecure.STR_MicAdmin, Nothing, pCreateNewUser:=False)
        'If adminUser IsNot Nothing Then
        '    If String.IsNullOrWhiteSpace(adminUser.HashedPassword) = False Then
        '        Return True
        '    End If
        'End If
        'Return False
    End Function

    Private Sub SetupAuditLogger()
        Dim loghelper As New DefaultAuditLogHelper(Function() Nothing,
                                                  Function() Nothing,
                                                  Function() Nothing,
                                                  Function() Nothing)

        Dim logWriter As New DefaultAuditLogWriter()
        AuditLogger.Init(loghelper, logWriter)
        AuditLogger.SetIsRemoteSession(System.Windows.Forms.SystemInformation.TerminalServerSession)
    End Sub


    Function GetControl(ByVal cName As String) As Control

        Dim c As Control

        c = Me.ECForm.FindControl(cName)

        Return c
        Return Nothing

    End Function

    Private Sub bbiSaveChanges_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSaveChanges.ItemClick

        blSavedConfig = If(blImportedConfig = True, True, False)

        Dim result As DialogResult
        If Not bMajorChangeDateChanged Then
            SaveAllTabs()
            Return
        End If

        'If Me.CreatingNewCI OrElse Me.EditingPendingConfiguration Then
        If Me.deMajorChange.DateTime < Now Then
            result = MessageBox.Show("The current 'MajorChange' date is in the past. Are you sure you want to save?", "Past Date Detected", MessageBoxButtons.OKCancel)
        Else
            result = DialogResult.OK
        End If
        'Else
        'result = DialogResult.OK
        'End If

        If result = DialogResult.OK Then
            SaveAllTabs()
            bMajorChangeDateChanged = False
        End If
        'Me.InitToolBarButtons()
    End Sub

    Private Sub BarButtonItem2_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        Me.Close()
        Application.Exit()
    End Sub

    Private Sub teTestEditMask_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles teTestEditMask.Enter
        Try
            Me.teTestEditMask.Properties.Mask.EditMask = ""
            Me.teTestEditMask.Properties.Mask.EditMask = Me.teEditMask.EditValue
        Catch ex As Exception
            Beep()
            Me.teEditMask.Focus()
        End Try
    End Sub

    Private Sub teTestEditMask_Leave(ByVal sender As Object, ByVal e As System.EventArgs) Handles teTestEditMask.Leave
        'Me.teTestEditMask.EditValue = ""
    End Sub


    Private Sub AddNewFacilitySettings()
        Me.WindowState = FormWindowState.Minimized
        Me.ConnectToDatabase()
        SetupAuditLogger()
        FacilitySettingsHelper.InsertNewSettings()
        DoShutDown()
    End Sub

    Private Sub SetUpIdleTimeout()
        BasicTimeoutWatcher.Init(Me, AddressOf DoShutDown)
    End Sub

    Private Sub DoShutDown()
        ECGlobals.CurrentUser?.LogOut()

        'Todo: Check for STATE issues ...
        Environment.Exit(0)
    End Sub
    Private Sub MiscMoveUP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveUp.Click
        Dim CurRowPos As Integer = MiscRow.CurYPos

        If Me.posList.IndexOf(CurRowPos) <= 0 Then
            Beep()
            Return
        End If

        Dim curIndex As Integer = posList.IndexOf(CurRowPos)
        Dim targetPos As Integer = posList(curIndex - 1)

        MiscRow.CurYPos = targetPos
        For Each c As Control In MiscRow.FocusedRow.Controls
            c.Location = New Point(c.Location.X, targetPos)
        Next

        'move other one down
        'get row to move down
        targetPos = posList(curIndex)
        Dim drow As MiscRow = Me.RowDict(posList(curIndex - 1))
        For Each c As Control In drow.Controls
            c.Location = New Point(c.Location.X, targetPos)
        Next

        Dim newdict As New Dictionary(Of Integer, MiscRow)
        For Each r As MiscRow In RowDict.Values
            newdict.Add(r.Controls(1).Location.Y, r)
        Next
        RowDict = newdict
    End Sub

    Private Sub MiscMoveDown_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveDown.Click
        Dim CurRowPos As Integer = MiscRow.CurYPos

        If Me.posList.IndexOf(CurRowPos) >= 20 Then
            Beep()
            Return
        End If

        Dim curIndex As Integer = posList.IndexOf(CurRowPos)
        Dim targetPos As Integer = posList(curIndex + 1)

        MiscRow.CurYPos = targetPos
        For Each c As Control In MiscRow.FocusedRow.Controls
            c.Location = New Point(c.Location.X, targetPos)
        Next

        'move other one UP
        'get row to move down
        targetPos = posList(curIndex)
        Dim drow As MiscRow = Me.RowDict(posList(curIndex + 1))
        For Each c As Control In drow.Controls
            c.Location = New Point(c.Location.X, targetPos)
        Next

        Dim newdict As New Dictionary(Of Integer, MiscRow)
        For Each r As MiscRow In RowDict.Values
            newdict.Add(r.Controls(1).Location.Y, r)
        Next
        RowDict = newdict
    End Sub

    Public Shared Function StripValue(ByVal value As String) As String
        'combo box list - replace special chars
        value = Replace(value, " ", "")
        value = Replace(value, "-", "")
        value = Replace(value, "/", "")
        value = Replace(value, ",", "")
        value = Replace(value, "&", "")
        value = Replace(value, "`", "")
        value = Replace(value, "@", "")
        value = Replace(value, "#", "")
        value = Replace(value, "%", "")
        value = Replace(value, "*", "")
        value = Replace(value, "\", "")
        value = Replace(value, "|", "")
        value = Replace(value, ".", "")
        value = Replace(value, ">=", "GTE")
        value = Replace(value, "<=", "LTE")
        value = Replace(value, ">", "GT")
        value = Replace(value, "<", "LT")
        value = Replace(value, "=", "")
        value = Replace(value, "(", "")
        value = Replace(value, ")", "")
        Return value
    End Function

    Class MiscRow
        Public Shared FocusedRow As MiscRow
        Public Shared CurYPos As Integer

        Public Sub New(ByVal ParamArray c() As Control)
            Me.Controls.AddRange(c)

            For Each p As Control In Me.Controls
                AddHandler p.GotFocus, AddressOf Me.GotFocus
                AddHandler p.LostFocus, AddressOf Me.LostFocus
                TryCast(p, BaseEdit).ToolTip = p.Name
                If TypeOf p Is ComboBoxEdit Then
                    AddHandler DirectCast(p, ComboBoxEdit).ButtonPressed, AddressOf Me.cbo_ButtonPressed
                End If
            Next

        End Sub

        Private Sub cbo_ButtonPressed(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
            'Blah....
            'All this stopwatch crap is to try and handle a bug that happens sometimes when using gotomeeting or remote desktop
            'the app can get into a state where every mouse click appears to be sent twice...
            Static sw As New Stopwatch
            If Not sw.IsRunning Then
                sw.Start()

            ElseIf sw.ElapsedMilliseconds < 250 Then
                Exit Sub
            End If

            sw.Reset()
            sw.Start()

            Dim cboBox As ComboBoxEdit = Nothing
            If e.Button.Index = 2 Then 'Return
                cboBox = TryCast(sender, ComboBoxEdit)
                Dim mr As DialogResult = MessageBox.Show("Are you sure you want to continue? Continuing will generate a new placeholder record in the 'espcodes' table for any list item that does not already contain one." & vbCrLf & vbCrLf & "Choose YES to continue, NO to continue without generating records, and cancel to abort", "Continue Confirmation", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
                If mr = DialogResult.Cancel Then Exit Sub
                If mr = DialogResult.Yes Then
                    GenerateESPCodeRecords(cboBox.Name)
                End If

                LaunchEditor(cboBox.Name)
            ElseIf e.Button.Index = 1 Then
                cboBox = TryCast(sender, ComboBoxEdit)

                Try
                    Dim cbolist As DOConfigComboBoxList = ECGlobals.CurrentConfigInstance.GetComboBoxListByControlName(sender.name)
                    If cbolist IsNot Nothing Then
                        Dim f As New ComboBoxListEditorFormv2(cbolist, cbolist.ListName)
                        f.ShowDialog()

                        If cbolist IsNot Nothing Then
                            cbolist.Reload()
                            cboBox.Tag = cbolist
                            cboBox.Properties.Items.Clear()

                            If cboBox Is Nothing Then Return

                            cboBox.Properties.Items.Add("")
                            For Each cboListItem As DOConfigComboBoxListsItem In cbolist.ListItems
                                'If cbo IsNot Nothing And cboListItem.Enabled Then
                                If cboListItem.Enabled Then
                                    cboBox.Properties.Items.Add(cboListItem.ItemDisplayName)
                                End If
                            Next
                        End If
                    End If
                Finally
                    sw.Reset()
                    sw.Start()
                End Try

            End If
        End Sub

        Public Function StripValue(ByVal value As String) As String
            'combo box list - replace special chars
            value = Replace(value, " ", "")
            value = Replace(value, "-", "")
            value = Replace(value, "/", "")
            value = Replace(value, ",", "")
            value = Replace(value, "&", "")
            value = Replace(value, "`", "")
            value = Replace(value, "@", "")
            value = Replace(value, "#", "")
            value = Replace(value, "%", "")
            value = Replace(value, "*", "")
            value = Replace(value, "\", "")
            value = Replace(value, "|", "")
            value = Replace(value, ".", "")
            value = Replace(value, ">=", "GTE")
            value = Replace(value, "<=", "LTE")
            value = Replace(value, ">", "GT")
            value = Replace(value, "<", "LT")
            value = Replace(value, "=", "")
            value = Replace(value, "(", "")
            value = Replace(value, ")", "")
            Return value
        End Function

        Private Sub GenerateESPCodeRecords(ByVal controlName As String)
            Dim esp As DOESPCode
            '            Dim sc As StringCollection = Me.GetTreatmentAreasAsList(DirectCast(My.Forms.MainForm.beiFacility2.EditValue, DOFacility).FacilityID)
            Dim sc As StringCollection = EnchartDOLib.Utils.GetTreatmentAreasAsListByFacility(DirectCast(My.Forms.MainForm.beiFacility2.EditValue, DOFacility))
            Dim facObj As DOFacility = My.Forms.MainForm.beiFacility2.EditValue

            Dim cbolist As DOConfigComboBoxList = ECGlobals.CurrentConfigInstance.GetComboBoxListByControlName(controlName)
            For Each ta As String In sc 'for each treatmentarea
                For Each li As DOConfigComboBoxListsItem In cbolist.ListItems
                    Dim espvalue As String = controlName + "_" & StripValue(li.ItemDisplayName) '.Replace(" ", "")

                    'for each item in the list mapped to this control
                    'if record doesn't aready exist then create it
                    esp = New DOESPCode
                    esp.Facility = facObj.Oid
                    esp.Facility_ID = facObj.FacilityID
                    esp.TreatmentArea = ta
                    esp.ESPVALUE = espvalue 'controlName + li.ToString.Replace(" ", "") '"this is the hard one.... "
                    esp.LongName = li.ItemDisplayName
                    esp.ESP_Policy = "Custom_Code"
                    esp.OrcaTab = "Miscellaneous"
                    esp.ReportDisplayOrder = 49500
                    esp.Points = 0
                    esp.Special = 1
                    esp.Flag = 0
                    esp.Quantity = 1

                    If Me.GetESPRecord(esp) = True Then Continue For

                    esp.Save()
                Next
            Next
        End Sub

        Friend Function GetESPRecord(ByVal esp As DOESPCode) As Boolean
            Dim xpcol As New XPCollection(Of DOESPCode)((DevExpress.Data.Filtering.CriteriaOperator.Parse(String.Format("Facility = {0} and TreatmentArea = '{1}' and ESPVALUE = '{2}' ", esp.Facility, esp.TreatmentArea, esp.ESPVALUE))))

            'Dim xpcol As New XPCollection(Of DOESPCode)((DevExpress.Data.Filtering.CriteriaOperator.Parse(String.Format("Facility = {0}", esp.Facility))))
            If xpcol.Count >= 1 Then Return True
            Return False
        End Function

        Protected Sub LaunchEditor(ByVal controlName As String)
            Dim pi As New ProcessStartInfo
            pi.FileName = "espcodeeditv2.exe"
            pi.Arguments = DirectCast(My.Forms.MainForm.beiFacility2.EditValue, DOFacility).Oid & " " & controlName
            pi.UseShellExecute = True

            Dim p As New Process
            p.StartInfo = pi

            p.Start()
        End Sub

        'Friend Function GetTreatmentAreasAsList(ByVal lfacility As String) As StringCollection

        '    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
        '        cmd.CommandText = String.Format("Select Distinct(TreatmentArea) from espcodes WHERE Facility_ID='{0}'", lfacility)

        '        Using reader As IDataReader = cmd.ExecuteReader()
        '            Dim sc As New StringCollection
        '            Do While reader.Read
        '                sc.Add(reader("TreatmentArea"))
        '            Loop

        '            Return sc
        '        End Using
        '    End Using

        'End Function

        Public Controls As New List(Of Control)
        '  Public Ypos As Integer
        'Public Order As Integer
        Private OldColor As Color

        Private Sub GotFocus(ByVal sender As Object, ByVal e As System.EventArgs)
            FocusedRow = Me
            CurYPos = Controls(1).Location.Y
            'Debug.WriteLine(Controls(0).Name & " " & CurYPos)

            Dim bfirsttime As Boolean = True
            For Each c As BaseEdit In Controls
                If bfirsttime Then
                    bfirsttime = False
                    OldColor = c.Properties.Appearance.BackColor
                End If
                c.Properties.Appearance.BackColor = Color.Cyan
            Next
        End Sub

        Private Sub LostFocus(ByVal sender As Object, ByVal e As System.EventArgs)
            If Me.OldColor = Color.Cyan Then
                'we are probably experiencing some sort of error...
                'All this crap is to try and handle a bug that happens sometimes when using gotomeeting or remote desktop
                'the app can get into a state where every mouse click appears to be sent twice...

                Me.OldColor = Color.Empty '
            End If

            For Each c As BaseEdit In Controls
                c.Properties.Appearance.BackColor = Me.OldColor
            Next
        End Sub
    End Class

    'Private MiscRows As New List(Of MiscRow)
    Private posList As New List(Of Integer)

    Private RowDict As New Dictionary(Of Integer, MiscRow)
    Private Sub InitMiscTab()
        RowDict = New Dictionary(Of Integer, MiscRow)

        RowDict.Add(Miscellaneous_UD_01_lbl_te.Location.Y, New MiscRow(MiscShowCbo01_chk, Miscellaneous_UD_01_lbl_te, Miscellaneous_UD_01_cbo, Miscellaneous_UD_01_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_02_lbl_te.Location.Y, New MiscRow(MiscShowCbo02_chk, Miscellaneous_UD_02_lbl_te, Miscellaneous_UD_02_cbo, Miscellaneous_UD_02_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_03_lbl_te.Location.Y, New MiscRow(MiscShowCbo03_chk, Miscellaneous_UD_03_lbl_te, Miscellaneous_UD_03_cbo, Miscellaneous_UD_03_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_04_lbl_te.Location.Y, New MiscRow(MiscShowCbo04_chk, Miscellaneous_UD_04_lbl_te, Miscellaneous_UD_04_cbo, Miscellaneous_UD_04_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_05_lbl_te.Location.Y, New MiscRow(MiscShowCbo05_chk, Miscellaneous_UD_05_lbl_te, Miscellaneous_UD_05_cbo, Miscellaneous_UD_05_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_06_lbl_te.Location.Y, New MiscRow(MiscShowCbo06_chk, Miscellaneous_UD_06_lbl_te, Miscellaneous_UD_06_cbo, Miscellaneous_UD_06_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_07_lbl_te.Location.Y, New MiscRow(MiscShowCbo07_chk, Miscellaneous_UD_07_lbl_te, Miscellaneous_UD_07_cbo, Miscellaneous_UD_07_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_08_lbl_te.Location.Y, New MiscRow(MiscShowCbo08_chk, Miscellaneous_UD_08_lbl_te, Miscellaneous_UD_08_cbo, Miscellaneous_UD_08_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_09_lbl_te.Location.Y, New MiscRow(MiscShowCbo09_chk, Miscellaneous_UD_09_lbl_te, Miscellaneous_UD_09_cbo, Miscellaneous_UD_09_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_10_lbl_te.Location.Y, New MiscRow(MiscShowCbo10_chk, Miscellaneous_UD_10_lbl_te, Miscellaneous_UD_10_cbo, Miscellaneous_UD_10_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_11_lbl_te.Location.Y, New MiscRow(MiscShowCbo11_chk, Miscellaneous_UD_11_lbl_te, Miscellaneous_UD_11_cbo, Miscellaneous_UD_11_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_12_lbl_te.Location.Y, New MiscRow(MiscShowCbo12_chk, Miscellaneous_UD_12_lbl_te, Miscellaneous_UD_12_cbo, Miscellaneous_UD_12_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_13_lbl_te.Location.Y, New MiscRow(MiscShowCbo13_chk, Miscellaneous_UD_13_lbl_te, Miscellaneous_UD_13_cbo, Miscellaneous_UD_13_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_14_lbl_te.Location.Y, New MiscRow(MiscShowCbo14_chk, Miscellaneous_UD_14_lbl_te, Miscellaneous_UD_14_cbo, Miscellaneous_UD_14_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_15_lbl_te.Location.Y, New MiscRow(MiscShowCbo15_chk, Miscellaneous_UD_15_lbl_te, Miscellaneous_UD_15_cbo, Miscellaneous_UD_15_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_16_lbl_te.Location.Y, New MiscRow(MiscShowCbo16_chk, Miscellaneous_UD_16_lbl_te, Miscellaneous_UD_16_cbo, Miscellaneous_UD_16_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_17_lbl_te.Location.Y, New MiscRow(MiscShowCbo17_chk, Miscellaneous_UD_17_lbl_te, Miscellaneous_UD_17_cbo, Miscellaneous_UD_17_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_18_lbl_te.Location.Y, New MiscRow(MiscShowCbo18_chk, Miscellaneous_UD_18_lbl_te, Miscellaneous_UD_18_cbo, Miscellaneous_UD_18_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_19_lbl_te.Location.Y, New MiscRow(MiscShowCbo19_chk, Miscellaneous_UD_19_lbl_te, Miscellaneous_UD_19_cbo, Miscellaneous_UD_19_cbo_Req_chk))
        RowDict.Add(Miscellaneous_UD_20_lbl_te.Location.Y, New MiscRow(MiscShowCbo20_chk, Miscellaneous_UD_20_lbl_te, Miscellaneous_UD_20_cbo, Miscellaneous_UD_20_cbo_Req_chk))

        'create a list of row positions (vertical Y coordiante)
        'posList.Clear()
        If posList.Count = 0 Then
            For Each r As MiscRow In RowDict.Values
                posList.Add(ControlDefaultLocationDict(r.Controls(1)).Y)
            Next
            posList.Sort()
        Else
            ' Debugger.Break()
        End If

        'Apply control overrides from database ... to reposition controls

    End Sub

    Private Sub ceMajorChange_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceMajorChange.CheckedChanged
        If Me.EditingPendingConfiguration OrElse Me.CreatingNewCI Then 'if not editing current config
            If Me.ceMajorChange.Checked Then
                If Me.cePending.Checked Then
                    Me.deMajorChange.EditValue = Me.deActivationDate.EditValue
                Else
                    'Me.deMajorChange.EditValue = Now.ToShortDateString
                    Me.deMajorChange.EditValue = Now

                End If
            End If
        End If

        If Me.ceMajorChange.Checked = False Then
            Me.deMajorChange.EditValue = Me.OriginalMajorChangeDate
            Me.deMajorChange.Enabled = False
        Else
            Me.deMajorChange.Enabled = True
        End If
    End Sub

    Private Sub deActivationDate_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles deActivationDate.EditValueChanged
        If Me.ceMajorChange.Checked Then
            Me.deMajorChange.EditValue = Me.deActivationDate.EditValue
        End If
    End Sub

    Private Sub cePending_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cePending.CheckedChanged
        If Me.cePending.Checked Then
            Try
                Dim tdate As New DateTime
                tdate = Me.deActivationDate.EditValue

                'I'm sure there's a better way to do this. But this is easy and works.
                'Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 0, 0, 0)
                Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 23, 59, 0)
                ' newdate.AddDays(1)
                'Note, Adddays doesnt change the date of the instance object its called on, but it does return a new date.
                Me.deActivationDate.EditValue = newdate '.AddDays(1)
            Catch
            End Try
        End If
    End Sub

    Sub InitToolBarButtons()
        If ECGlobals.CurrentFacility Is Nothing Then
            Beep()
            Return
        End If

        ECGlobals.CurrentFacility.ConfigInstanceVersion.ECReload()

        'Note DOFacility.ConfigInstanceVersion will create and save a blank CI for the facility if one does not already exist
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetConfigSettingGroup("Application")

        Me.ConfigInstanceEnabled = ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup("UseConfigInstances").Enabled

        Me.PendingConfigExists = ECGlobals.CurrentFacility.HasPendingConfig

        If Not ECGlobals.CurrentFacility.HasPendingConfig Then
            Me.bbiEditPending.Enabled = False
            Me.bbiNewCI.Enabled = True
        Else
            Me.bbiEditPending.Enabled = True
            If ConfigInstanceEnabled Then
                Me.bbiNewCI.Enabled = False
            Else
                Me.bbiNewCI.Enabled = True
            End If
        End If

    End Sub
    Private Sub beiF2_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles beiFacility2.EditValueChanged
        If TypeOf beiFacility2.EditValue Is String Then
            Exit Sub
        End If

        ECGlobals.CurrentFacility = beiFacility2.EditValue

        '----------------------------------------------
        InitToolBarButtons()
        '------------------------------------------------------------
        If ECGlobals.CurrentFacility IsNot Nothing Then
            ' Me.beiECID.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.LongName
            Me.sbbeiEID.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.LongName
            Me.sbbeiEID.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.Oid.ToString
            Me.sbbeiEID.Hint += ", ID = " & ECGlobals.CurrentFacility.CompanyClient.Company.EnchartID.EnchartID.ToString

            ' Me.beiCompany.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.LongName
            Me.sbbeiCompany.EditValue = ECGlobals.CurrentFacility.CompanyClient.Company.LongName
            Me.sbbeiCompany.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Company.Oid.ToString
            Me.sbbeiCompany.Hint += ", CompanyID = " & ECGlobals.CurrentFacility.CompanyClient.Company.CompanyID.ToString

            ' beiCompanyClient.EditValue = ECGlobals.CurrentFacility.CompanyClient.LongName
            Me.sbbeiCompanyClient.EditValue = ECGlobals.CurrentFacility.CompanyClient.LongName
            Me.sbbeiCompanyClient.Hint = "OID = " & ECGlobals.CurrentFacility.CompanyClient.Oid.ToString
            Me.sbbeiCompanyClient.Hint += ", CompanyClientID = " & ECGlobals.CurrentFacility.CompanyClient.CompanyClientID.ToString

            Me.bsbeiFacility.EditValue = ECGlobals.CurrentFacility.LongName
            Me.bsbeiFacility.Hint = "OID = " & ECGlobals.CurrentFacility.Oid.ToString
            Me.bsbeiFacility.Hint += ", FacilityID = " & ECGlobals.CurrentFacility.FacilityID.ToString
        End If
        'Default to Active/Current Config
        'ECGlobals.CurrentConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
        'Me.bbiViewCurrentConfig.Enabled = True
        Me.bbiEditCurrentConfig.Enabled = True

        DoLabelCurPend(False, True)

    End Sub

    Protected bMajorChangeDateChanged = False
    Private Sub deMajorChange_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles deMajorChange.EditValueChanged
        bMajorChangeDateChanged = True
        ''Try
        'Dim tdate As New DateTime
        'tdate = Me.deMajorChange.EditValue

        ''I'm sure there's a better way to do this. But this is easy and works.
        'Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 0, 0, 0)
        'Me.deMajorChange.EditValue = newdate
        ''Catch
        ''End Try
    End Sub

    Private Sub deMajorChange_CloseUp(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles deMajorChange.CloseUp
        ''Try
        'Dim tdate As New DateTime
        'tdate = Me.deMajorChange.EditValue

        ''I'm sure there's a better way to do this. But this is easy and works.
        'Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 1, 0, 0)
        'Me.deMajorChange.EditValue = newdate
        ''Catch
        ''End Try
    End Sub

    Private Sub btnResetActivationTime_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnResetActivationTime.Click
        'Try
        Dim tdate As New DateTime
        tdate = Me.deActivationDate.EditValue

        'I'm sure there's a better way to do this. But this is easy and works.
        Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 0, 0, 0)
        Me.deActivationDate.EditValue = newdate
        'Catch
        'End Try
    End Sub

    Private Sub btnResetMajorChangeTime_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnResetMajorChangeTime.Click
        'Try
        Dim tdate As New DateTime
        tdate = Me.deMajorChange.EditValue

        'I'm sure there's a better way to do this. But this is easy and works.
        Dim newdate As New DateTime(tdate.Year, tdate.Month, tdate.Day, 0, 0, 0)
        Me.deMajorChange.EditValue = newdate
        'Catch
        'End Try
    End Sub

    Private Sub btnMiscReset_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMiscReset.Click
        If MessageBox.Show("Are you sure you want to reset all the comobox related controls to their default (hard coded) positions?", "Speak now, or forever hold your peace!", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = DialogResult.Cancel Then
            Return
        End If
        ResetMiscCboPositions()

    End Sub

    Sub ResetMiscCboPositions()
        For Each c As Control In ControlDefaultLocationDict.Keys
            c.Location = ControlDefaultLocationDict(c)
        Next
    End Sub

    Private Sub ceUseAlternatePositions_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceUseAlternatePositions.CheckedChanged
        If Me.ceUseAlternatePositions.Checked Then
            Me.btnMoveDown.Visible = True
            Me.btnMoveUp.Visible = True
            Me.btnMiscReset.Visible = True
            'Me.InitMiscTab()
        Else
            Me.btnMoveDown.Visible = False
            Me.btnMoveUp.Visible = False
            Me.btnMiscReset.Visible = False
            ResetMiscCboPositions()
        End If
    End Sub

    Private Sub BarButtonItem3_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        'Dim pi As New ProcessStartInfo
        'pi.FileName = "enchartcac.exe"

        ''-----------Commented out next two un-needed/unused lines to  see if it helps with vericode scan  --- start
        ' ''Dim tuser As String = Environment.GetEnvironmentVariable("ECUser")
        ' ''Dim tpass As String = Environment.GetEnvironmentVariable("ECPass")

        ' ''pi.Arguments = ""
        ' ''Try
        ' ''    If String.IsNullOrEmpty(tuser) AndAlso String.IsNullOrEmpty(tpass) Then
        ' ''        pi.Arguments = tuser & " " & tpass
        ' ''    End If
        ' ''Catch

        ' ''End Try
        ''-----------Commented out next two unneeded/unused lines few lines to  see if it helps with vericode scan  --- end

        ''"admin admin"
        'pi.UseShellExecute = True

        'Dim p As New Process
        'p.StartInfo = pi

        'p.Start()
    End Sub

    Private Sub BarButtonItem4_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        Dim f As New formEditControlOverrides

        Try
            f.gcControlOverride.DataSource = ECGlobals.CurrentConfigInstance.ControlOverrides
            f.ShowDialog()
        Catch ex As Exception
            MessageBox.Show("Houston, we have a problem!" & vbCrLf & "Try clicking 'Edit' to start an editing session.", "DOH", MessageBoxButtons.OK)
        End Try

    End Sub

    Private Sub bnConvertMeds_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bnConvertMeds.Click
        Dim ci As DOConfigInstance = ECGlobals.CurrentConfigInstance
        Dim doList As DOConfigComboBoxList = Nothing
        Dim doTList As DOConfigComboBoxList = Nothing
        Try
            doList = ci.GetComboBoxListByControlName("Infusion_InfusedMedicationsMedication01_cbo")
            If doList Is Nothing OrElse doList.ListItems.Count <= 0 Then
                MessageBox.Show("Can't find a list mapped to the 'Infusion_InfusedMedicationsMedication01_cbo' control.", "Config Error")
                Return
            End If
        Catch ex As Exception
            MessageBox.Show("Error finding Medications list", "ConfigError")
            Return
        End Try

        'Ok... we have our meds list ...
        Dim medDict As New Dictionary(Of String, DOMedication)
        For Each li As DOConfigComboBoxListsItem In doList.ListItems
            medDict(li.ItemDisplayName) = New DOMedication() With {.MedName = li.ItemDisplayName, .Enabled = li.Enabled, .ConfigInstance = ci}
        Next

        'Ok... now we an enabled medications
        'get titrated meds list
        'Infusions_TitratedMedicationsMedication01_cbo
        Try
            doTList = ci.GetComboBoxListByControlName("Infusions_TitratedMedicationsMedication01_cbo")
            If doTList Is Nothing OrElse doTList.ListItems.Count <= 0 Then
                MessageBox.Show("Errro trying to process Titrated Medications List", "Config Error")
                Return
            End If
        Catch ex As Exception
            MessageBox.Show("Error finding Medications list", "ConfigError")
        End Try

        For Each tli As DOConfigComboBoxListsItem In doTList.ListItems
            If Not medDict.ContainsKey(tli.ItemDisplayName) Then
                MessageBox.Show(String.Format("Titrated med not in Medications List"), "Config Error", MessageBoxButtons.OK)
                Continue For
            End If

            medDict(tli.ItemDisplayName).Titrated = True
        Next

        'now find critical care control list name
        Dim ln As DOConfigSetting = ECGlobals.CurrentConfigInstance.AppConfigGroup("CriticalCareControlsByList")
        If ln Is Nothing Then
            MessageBox.Show("Error, trying to find 'CriticalCareControlsByList'")
            Return
        End If
        Dim CCListName As String = Nothing
        'If ln IsNot Nothing AndAlso ln.SettingValue IsNot Nothing Then
        '    CCListName = ln.SettingValue
        'End If

        If ln.PropertySettings.Count > 0 Then
            Dim scs As DOConfigPropertySetting = ln.PropertySettings(0) 'we're assuming this is correct list...
            CCListName = scs.PropertyName

            Dim cclist As DOConfigComboBoxList
            Try
                cclist = ECGlobals.CurrentConfigInstance.GetComboBoxList(CCListName)
            Catch ex As Exception
                MessageBox.Show(String.Format("Error finding list '{0}'.", CCListName), "Config Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End Try

            For Each ccli As DOConfigComboBoxListsItem In cclist.ListItems
                If Not medDict.ContainsKey(ccli.ItemDisplayName) Then
                    Continue For
                End If
                medDict(ccli.ItemDisplayName).CC = True
            Next
        End If

        Dim fs As New XPCollection(Of DOInfusionSiteMedications)(CriteriaOperator.Parse(String.Format("Facility = {0}", ci.Facility.Oid)))
        If fs.Count <= 1 Then
            'complain
            MessageBox.Show("Error trying to import DedicatedLine Meds from 'facilitysettings' table", "Config Error", MessageBoxButtons.OK)
        Else 'we have liftoff.. lets do something cool
            For Each fsi As DOInfusionSiteMedications In fs
                Dim medName As String = fsi.MedicationName

                If Not medDict.ContainsKey(medName) Then
                    Continue For
                End If
                medDict(medName).DedicatedLine = True
            Next

        End If

        Try
            'this table shoudl be empty, if not, delete the exiting records
            If ci.Medications.Count > 0 Then
                Dim dl As New List(Of DOMedication)
                For Each m In ci.Medications
                    dl.Add(m)
                Next

                For Each m In dl
                    m.Delete()
                Next
            End If
        Catch ex As Exception
            MessageBox.Show("Error trying to delete existing medication records")
        End Try

        For Each ddo As DOMedication In medDict.Values
            ddo.Save()
            ECGlobals.CurrentConfigInstance.Medications.Add(ddo)
        Next

        MessageBox.Show("Medications for the current configInstance have been successfully created. Please very the accuracy of the imported records.")

    End Sub

    Private Sub DoEditPrevCI()
        'DoEditPrevCI
        Me.bsiFacilityLabel.Enabled = False
        Me.beiFacility2.Enabled = False
        'OLD TODO - Need to copy old config to new one prior to editing, then let user edit newly created one
        ECGlobals.CurrentConfigInstance = Me.GetConfigInstanceToEdit
        ECGlobals.CurrentConfigInstance = Me.GetPrevCI(ECGlobals.CurrentFacility)

        EditingPrevCI = True

        Me.lblConfigInstanceOID.Text = "ConfigInstance OID: " & ECGlobals.CurrentConfigInstance.Oid.ToString
        If ECGlobals.CurrentConfigInstance.IsDeleted Then
            Me.lblConfigInstanceOID.Text += "(WARNING: ConfigInstance is marked for deletion!)"
        End If

        'Store this so we can come back to this date
        OriginalMajorChangeDate = ECGlobals.CurrentConfigInstance.MajorChangeDate

        InitTabs()
        EnableEditing()

        Me.deActivationDate.Enabled = False
        'Me.ceMajorChange.Enabled = False

        '------ JJC Added 06.17.13
        _ConfigInstance = ECGlobals.CurrentConfigInstance ' really? why 2 different variables eh...
        Me.ceMajorChange.EditValue = _ConfigInstance.MajorChange
        If Not _ConfigInstance.ActivationDate = #12:00:00 AM# Then
            Me.deActivationDate.EditValue = _ConfigInstance.ActivationDate
        End If

        If Me.CreatingNewCI Then
            Me.deActivationDate.EditValue = Now
        End If

        If Not _ConfigInstance.MajorChangeDate = #12:00:00 AM# Then
            Me.deMajorChange.EditValue = _ConfigInstance.MajorChangeDate
        End If

        Me.teCreationDate.EditValue = _ConfigInstance.CreationDate
        Me.teComments.EditValue = _ConfigInstance.Comments
        Me.cbeFormClassaName.EditValue = _ConfigInstance.ActiveFormClassName

        Me.deReadOnlyActivationDate.EditValue = _ConfigInstance.ReadOnlyActivationDate
        ' Me.ceReadOnly.EditValue = _ConfigInstance.IsReadOnly 'jjc 06.13.13
        'JJC Added 06.17.13 --------------

    End Sub
    Private Sub BarButtonItem5_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem5.ItemClick
        DoEditPrevCI()
    End Sub

    Friend Class DateReverseComparer
        Implements IComparer(Of Date)

        Public Function Compare(ByVal x As Date, ByVal y As Date) As Integer Implements System.Collections.Generic.IComparer(Of Date).Compare
            Return Date.Compare(y, x)
        End Function
    End Class

    Private Shared Function GetCIList(ByVal pfac As DOFacility) As System.Collections.Generic.List(Of DOConfigInstance)
        Dim cicol As New XPCollection(Of DOConfigInstance)(CriteriaOperator.Parse(String.Format("Facility = {0} ", pfac.Oid)))
        cicol.Sorting.Add(New SortProperty("CreationDate", SortingDirection.Ascending))

        'Dim byOIDDict As New SortedDictionary(Of Integer, DOConfigInstance)
        Dim CIDict As New SortedDictionary(Of Date, DOConfigInstance)(New DateReverseComparer) '(system.)

        'each minor ci will overwrite any minor chagnes for a given majorChangeDate
        Dim dci As DOConfigInstance = Nothing
        For Each dci In cicol
            If ECGlobals.CurrentFacility.HasPendingConfig AndAlso dci.Equals(ECGlobals.CurrentFacility.PendingConfigInstanceVersion) Then Continue For
            If dci.ActivatedDate = New Date Then Continue For

            '11.19.09 Added - The config tool now scrubs off the seconds portion of the majorChangeDate, and as a result
            'now, when making a MINOR configInstance change, the majorChangeDate could change when it needs to match...
            ' - if it doesn't match, it's interpreted as a majorchange...
            'so... scrub off the seconds portion
            Dim d2 As DateTime = dci.MajorChangeDate
            Dim ddd As New DateTime(d2.Year, d2.Month, d2.Day, d2.Hour, d2.Minute, 0)
            CIDict(ddd) = dci

            'CIDict(dci.MajorChangeDate) = dci
        Next

        Dim pCIList = New List(Of DOConfigInstance)(CIDict.Values)
        Return pCIList
    End Function
    Public Function GetPrevCI(ByVal pfac As DOFacility) As DOConfigInstance

        'get list of facility specific config instances sorted by oid or creation date, oldest first
        Dim pCIList As System.Collections.Generic.List(Of DOConfigInstance) = GetCIList(pfac)

        Dim pCI As DOConfigInstance

        pCI = pCIList(1)


        Return pCI
    End Function

    Private Sub BarButtonItem1_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles btnAddFacility.ItemClick
        Dim f As New AddNewFacilityForm
        f.ShowDialog()

        InitFacilitySelect()
    End Sub

    Private Sub BarButtonItem6_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem6.ItemClick
        ExportConfigToXML()
    End Sub

    Private Sub bbiLoadConfigFromXML_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiLoadConfigFromXML.ItemClick
        If XtraTabControl1.Enabled And XtraTabControl1.Visible Then
            MessageBox.Show("Please end the current editing session first", "UH OH", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        'If MessageBox.Show("Are you sure? This will overwrite all config settings, comboboxes, medications, espcodes, etc... This" _
        '                   & vbCrLf & "This code is still undergoing development.", "Danger Danger Danger", MessageBoxButtons.YesNo,
        '                   MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = DialogResult.No Then
        '    Return

        'End If

        Try
            LoadConfigFromXML()
        Finally

        End Try

    End Sub

    Private Sub BarButtonItem1_ItemClick_1(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick

        If XtraTabControl1.Enabled And XtraTabControl1.Visible Then
            MessageBox.Show("Please end the current editing session first", "UH OH", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim f As New FacilityCopyForm
        f.ShowDialog()
    End Sub

    Private Sub XtraTabControl1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles XtraTabControl1.Click

    End Sub

    Private Sub deMajorChange_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles deMajorChange.KeyPress
        Me.bMajorChangeDateChanged = True
    End Sub

    Private Sub BarButtonItem9_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles btnFormClose.ItemClick
        If DialogResult.OK = MessageBox.Show("Are you sure you want to exit?", "Exit App?", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
            DoShutDown()
            'Application.Exit()
        End If
    End Sub

    'Private Sub BarButtonItem9_ItemClick_1(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem9.ItemClick
    '    frmViewConfigInstances.frmType = 1
    '    frmViewConfigInstances.ShowDialog(Me)
    'End Sub

    'Private Sub BarStaticItem6_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarStaticItem6.ItemClick
    '    frmViewConfigInstances.frmType = 2
    '    frmViewConfigInstances.ShowDialog(Me)
    'End Sub

    'Private Sub BarStaticItem7_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarStaticItem7.ItemClick
    '    frmViewConfigInstances.frmType = 2
    '    frmViewConfigInstances.ShowDialog(Me)
    'End Sub

    ''' <summary>
    '''
    ''' </summary>
    ''' <param name="str"></param>
    ''' <remarks>
    ''' JJC - I don't believe there is any reason to save these values. Simple don't save before ending the edit
    ''' and you're changes are discarded.
    ''' </remarks>
    Private Sub LoadConfigData(ByVal str As String)
        Dim DelimiterChar As Char = ","c
        Dim Reader As New StreamReader(str)

        prevMajorDt = deMajorChange.DateTime
        prevActivDt = deActivationDate.DateTime
        prevComments = teComments.Text
        prevFormClass = cbeFormClassaName.Text

        blImportedConfig = True
        'Dim cfg As New ConfigImport()
        Try
            While Not Reader.EndOfStream
                Dim Line As String = Reader.ReadLine()
                Dim Fields() As String = Line.Split(DelimiterChar)

                Select Case Fields(0)
                    Case "FormClassName"
                        cbeFormClassaName.Text = Fields(1)
                    Case "ActivationDate"
                        Dim dt As New DateTimePicker
                        dt.Text = Fields(1)
                        deActivationDate.DateTime = dt.Value
                        deMajorChange.DateTime = dt.Value
                    Case "Comment"
                        teComments.Text = Fields(1)
                End Select

            End While

            Reader.Close()
        Catch ex As Exception
            MessageBox.Show(ex.Message)
            blImportedConfig = False
            deMajorChange.DateTime = prevMajorDt
            deActivationDate.DateTime = prevActivDt
            teComments.Text = prevComments
            cbeFormClassaName.Text = prevFormClass

        End Try

    End Sub

    Private Sub BarStaticItem8_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarStaticImportConfig.ItemClick

        OpenFileDialog1.Title = "Please Select a File"
        OpenFileDialog1.InitialDirectory = Application.ExecutablePath '"C:"
        OpenFileDialog1.FileName = "ImportConfig.dat"
        OpenFileDialog1.Filter = "Data Files(*.dat)|*.dat"
        OpenFileDialog1.ShowDialog()
    End Sub

    Private Sub OpenFileDialog1_FileOk(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles OpenFileDialog1.FileOk

        Dim strm As System.IO.Stream
        strm = OpenFileDialog1.OpenFile()

        LoadConfigData(OpenFileDialog1.FileName.ToString())

    End Sub

    'Private Sub BarStaticItem8_ItemClick_1(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarStaticItem8.ItemClick
    '    frmViewConfigInstances.frmType = 4
    '    frmViewConfigInstances.ShowDialog(Me)
    'End Sub

    Private Sub ceUseAdvancedModel_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceUseAdvancedModel.CheckedChanged
        'If ceUseAdvancedModel.Checked = True Then
        '    ceEnableEMGrid.Checked = True
        'End If
    End Sub

    Private Sub ceEnableEMGrid_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceEnableEMGrid.CheckedChanged
        gcEEmGrid.Visible = ceEnableEMGrid.Checked
        If ceEnableEMGrid.Checked Then
            If ceEnableOSAMode.Checked Then
                MessageBox.Show("OSA Mode has been turned off", "Don't do that", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                ceEnableOSAMode.Checked = False
            End If
            ceUseUrgentCareConfig.Enabled = True
        Else
            ceUseUrgentCareConfig.Checked = False
            ceUseUrgentCareConfig.Enabled = False
        End If
    End Sub

    Private Sub ceEnableObservationTab_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceEnableObservationTab.CheckedChanged
        If ceEnableObservationTab.Checked Then
            ceEnableOSAMode.Enabled = True
        Else
            ceEnableOSAMode.Checked = False
            ceEnableOSAMode.Enabled = False
        End If
    End Sub

    Private Sub ceEnableOSAMode_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceEnableOSAMode.CheckedChanged
        If ceEnableOSAMode.Checked Then
            If ceEnableEMGrid.Checked Then
                MessageBox.Show("OSA Mode cannot be used with the EMGrid Option. EMGrid option has been turned off", "Don't do that!", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                ceEnableEMGrid.Checked = False
            End If
        End If
    End Sub

    Private Sub ceClinicMode_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceClinicMode.CheckedChanged
        ClinicManEntryGrp.Visible = ceClinicMode.Checked
    End Sub

    Private Sub BarButtonItem10_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem10.ItemClick
        'copy Treatment Area
        Dim f As New CopyTreatmentAreaForm
        f.ShowDialog()

        f.Close()
    End Sub

    Private Sub TextEdit5_EditValueChanged(sender As Object, e As EventArgs) Handles teClinicDefaultChartStatus.EditValueChanged
        teDefaultChartStatus.EditValue = teClinicDefaultChartStatus.EditValue
    End Sub

    Private Sub teDefaultPhysChartStatus_EditValueChanged(sender As Object, e As EventArgs) Handles teDefaultPhysChartStatus.EditValueChanged
        teClinicDefaultPhysChartStatus.EditValue = teDefaultPhysChartStatus.EditValue
    End Sub

    Private Sub teDefaultChartStatus_EditValueChanged(sender As Object, e As EventArgs) Handles teDefaultChartStatus.EditValueChanged
        teClinicDefaultChartStatus.EditValue = teDefaultChartStatus.EditValue
    End Sub

    Private Sub ceClinicPhysSwapDxMod_CheckedChanged(sender As Object, e As EventArgs) Handles ceClinicPhysSwapModWithDx.CheckedChanged
        cePhysSwapModWithDx.Checked = ceClinicPhysSwapModWithDx.Checked
    End Sub

    Private Sub cePhysSwapModWithDx_CheckedChanged(sender As Object, e As EventArgs) Handles cePhysSwapModWithDx.CheckedChanged
        ceClinicPhysSwapModWithDx.Checked = cePhysSwapModWithDx.Checked
    End Sub

    Private Sub teClinicDefaultPhysChartStatus_EditValueChanged(sender As Object, e As EventArgs) Handles teClinicDefaultPhysChartStatus.EditValueChanged
        teDefaultPhysChartStatus.EditValue = teClinicDefaultPhysChartStatus.EditValue
    End Sub

    Private Sub teEditMask_EditValueChanged(sender As Object, e As EventArgs) Handles teEditMask.EditValueChanged

    End Sub

    Private Sub cePhysSumTabProviderRequired_CheckedChanged(sender As Object, e As EventArgs) Handles cePhysSumTabProviderRequired.CheckedChanged
        ceClinicPhysReqProvider.Checked = cePhysSumTabProviderRequired.Checked
    End Sub

    Private Sub ceClinicPhysReqProvider_CheckedChanged(sender As Object, e As EventArgs) Handles ceClinicPhysReqProvider.CheckedChanged
        cePhysSumTabProviderRequired.Checked = ceClinicPhysReqProvider.Checked
    End Sub

    Private Sub cePhysDXxCodesRequired_CheckedChanged(sender As Object, e As EventArgs) Handles cePhysDXxCodesRequired.CheckedChanged
        ceClinicPhysReqDxCodes.Checked = cePhysDXxCodesRequired.Checked
    End Sub

    Private Sub ceClinicPhysReqDxCodes_CheckedChanged(sender As Object, e As EventArgs) Handles ceClinicPhysReqDxCodes.CheckedChanged
        cePhysDXxCodesRequired.Checked = ceClinicPhysReqDxCodes.Checked
    End Sub

    Private Sub ceEnableClinicPhysTab_CheckedChanged(sender As Object, e As EventArgs) Handles ceEnableClinicPhysTab.CheckedChanged
        '    ClinicProfOptionsGrp.Visible = ceEnableClinicPhysTab.Checked
    End Sub

    Private Sub BarButtonItem11_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem11.ItemClick
        ShowGlobalSettingsTableEditor()

    End Sub

    Private Sub BarButtonItem12_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem12.ItemClick
        ShowGlobalSettingsTableEditor()
    End Sub

    Private Sub ShowGlobalSettingsTableEditor()
        Dim f As New GlobalSettingsForm
        f.ShowDialog()
    End Sub

    Private Sub bbiFacilitySettings_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiFacilitySettings.ItemClick
        ShowFacilitySettingsTableEditor()
    End Sub
    Private Sub ShowFacilitySettingsTableEditor()
        Dim f As New FacilitySettingsForm
        f.ShowDialog()
    End Sub

    Private Sub bbiImageFeed_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiImageFeed.ItemClick
        ShowImageFeedConfigurationDialog()
    End Sub

    Private Sub ShowImageFeedConfigurationDialog()
        Dim f As New ImageFeedForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem7_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem7.ItemClick
        Dim catlist As New List(Of DTOFacilityReportCategory)
        For Each cat In DOFacilityReportCategory.GetRootCategoriesByFacility(ECGlobals.CurrentFacility)
            catlist.Add(New DTOFacilityReportCategory(cat))
        Next
        Debug.WriteLine("boo")
    End Sub

    Private Sub ECConfigMainForm_Closing(sender As Object, e As CancelEventArgs) Handles MyBase.Closing
        If Not IsConnectionStringEncrypted(configFileNameBase) Then
            ToggleConnectionStringEncryption(configFileNameBase)
        End If
        DoShutDown()
    End Sub

    Private Sub chkGridUseNewControlNamingForChargableProcs_CheckedChanged(sender As Object, e As EventArgs) Handles chkGridUseNewControlNamingForChargableProcs.CheckedChanged
        If chkGridUseNewControlNamingForChargableProcs.Checked Then
            chkGridUseNewControlNaming.Checked = False
        End If
    End Sub

    Private Sub chkGridUseNewControlNaming_CheckedChanged(sender As Object, e As EventArgs) Handles chkGridUseNewControlNaming.CheckedChanged
        If chkGridUseNewControlNaming.Checked Then
            chkGridUseNewControlNamingForChargableProcs.Checked = False
        End If
    End Sub

    Private Sub BarButtonItem13_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem13.ItemClick
        Dim f As New LoadNcciEditsForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem14_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem14.ItemClick
        Dim f As New CreateNewConfigInstanceForAllFacilitiesForm
        f.ShowDialog()
        f.Dispose()

        InitToolBarButtons()
    End Sub

    Private Sub ceEnableClinicSuppliesTab_CheckedChanged(sender As Object, e As EventArgs) Handles ceEnableClinicSuppliesTab.CheckedChanged

    End Sub

    Private Sub BarButtonItem15_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem15.ItemClick
        'MigrateAllFacilities()
        Dim tahelp As New MigrateTAHelper
        tahelp.MigrateAllFacilities()
    End Sub

    Private Sub BarButtonItem16_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem16.ItemClick
        Dim f As New Espcodes2018Form
        f.Show()
    End Sub

    Private Const configFileNameBase = ".\ENChartCAC.dll"

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Private Sub BarButtonItem17_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEncryptConnectionStrings.ItemClick
        ToggleConnectionStringEncryption(configFileNameBase)
    End Sub

    Private Sub InitEncryptMenuOption()
        bbiEncryptConnectionStrings.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        If IsConnectionStringEncrypted(configFileNameBase) Then
            bbiEncryptConnectionStrings.Caption = "Decrypt DB Credentials"
        Else
            bbiEncryptConnectionStrings.Caption = "Encrypt DB Credentials"
        End If
    End Sub

    Private Function IsConnectionStringEncrypted(exename As String) As Boolean

        Dim config = ConfigurationManager.OpenExeConfiguration(exename)

        Dim section = CType(config.GetSection("connectionStrings"), ConnectionStringsSection)

        If section.SectionInformation.IsProtected Then
            Return True
        Else
            Return False
        End If
    End Function

    Private Function ToggleConnectionStringEncryption(exename As String) As Boolean
        Dim encrypted As Boolean = False
        Dim config = ConfigurationManager.OpenExeConfiguration(exename)
        Dim connectionStringSection = CType(config.GetSection("connectionStrings"), ConnectionStringsSection)
        Dim appConfigSection = CType(config.GetSection("appSettings"), AppSettingsSection)

        If connectionStringSection.SectionInformation.IsProtected Then
            connectionStringSection.SectionInformation.UnprotectSection()
            appConfigSection.SectionInformation.UnprotectSection()
        Else
            connectionStringSection.SectionInformation.ProtectSection("DataProtectionConfigurationProvider")
            appConfigSection.SectionInformation.ProtectSection("DataProtectionConfigurationProvider")
            encrypted = True
        End If

        config.Save()
        InitEncryptMenuOption()
        Return encrypted
    End Function

    Private Sub BarButtonItem17_ItemClick_1(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem17.ItemClick
        Dim f As New UpdateModifier25Form
        f.ShowDialog(Me)
    End Sub

    Private Sub ceUseUrgentCareConfig_CheckedChanged(sender As Object, e As EventArgs)
        ceProfeeV2EnableProfeeTab.Checked = ceUseUrgentCareConfig.Checked
        ceProfeeV2UseEMGrid.Checked = ceUseUrgentCareConfig.Checked
    End Sub

    Private Sub ceProfeeV2EnableProfeeTab_CheckedChanged(sender As Object, e As EventArgs)
        grpProfeeOptions.Enabled = ceProfeeV2EnableProfeeTab.Checked
        grpProfeeOptions.Visible = ceProfeeV2EnableProfeeTab.Checked
    End Sub

    Private Sub ceUseUrgentCareConfig_CheckedChanged_1(sender As Object, e As EventArgs) Handles ceUseUrgentCareConfig.CheckedChanged
        ' grpProfeeOptions.Visible = ceUseUrgentCareConfig.Checked
        If ceUseUrgentCareConfig.Checked Then
            gcEEmGrid.Enabled = False
            'XtraTabPage2.PageEnabled = False 'phys
            'XtraTabPage2.PageVisible = True 'phys

            'XtraTabPage3.PageEnabled = False 'obs
            'XtraTabPage5.PageEnabled = False 'clinic
            tabQI.PageEnabled = False 'qi tab
        Else
            'XtraTabPage2.PageEnabled = True
            'XtraTabPage2.PageVisible = True
            'XtraTabPage3.PageEnabled = True
            'XtraTabPage5.PageEnabled = True
            tabQI.PageEnabled = True
            If ceEnableEMGrid.Checked Then
                gcEEmGrid.Enabled = True
            End If
        End If
    End Sub

    Private Sub BarButtonItem20_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem20.ItemClick
        Dim oldMedsList As New XPCollection(Of DOMedication)
        Dim medNamesAddedList As New List(Of String)
        Cursor.Current = Cursors.WaitCursor
        Dim sw As New Stopwatch
        sw.Start()

        Try
            For Each oldMed In oldMedsList
                If String.IsNullOrEmpty(oldMed.MedName) Then Continue For
                If Not medNamesAddedList.Contains(oldMed.MedName.Trim, StringComparer.OrdinalIgnoreCase) Then
                    medNamesAddedList.Add(oldMed.MedName.Trim)

                    Dim medMaster As New DOMedicationMaster() With {
                        .MedName = oldMed.MedName.Trim,
                        .CC = oldMed.CC,
                        .Chemo = oldMed.Chemo,
                        .DedicatedLine = oldMed.DedicatedLine,
                        .Enabled = True,
                        .Hormonal = oldMed.Hormonal,
                        .UID = Guid.NewGuid
                    }
                    medMaster.Save()
                End If
            Next
            sw.Stop()
            Cursor.Current = Cursors.Default
            MessageBox.Show($"{medNamesAddedList.Count} facility specific meds successfully merged into Global meds table (DOMedicationMaster) in {sw.Elapsed}", "Merge Successfull", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"{ex.Message}", "Merge failed")
        Finally
            Cursor.Current = Cursors.Default
        End Try

    End Sub

    Private Sub BarButtonItem19_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem19.ItemClick
        Dim ofd As New OpenFileDialog
        ofd.Title = "JJCRules"
        ofd.DefaultExt = "*.csv"
        ofd.Filter = String.Format("AIC Paragon Med List Mapped to MEDIDs_FINAL.csv (*.csv) | *.csv")
        ofd.InitialDirectory = My.Computer.FileSystem.CurrentDirectory 'ECGlobals.ComboBoxListDir
        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub
        Dim fullFileName = ofd.FileName

        Dim phelper As New ShawnasParagonMedIdMappingsHelper
        phelper.LoadFile(fullFileName)
    End Sub

    Private Sub BarButtonItem21_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem21.ItemClick
        Dim f As New LoadMedicationMastersForm

        f.ShowDialog()
    End Sub


    Public Sub ExportAdminReportsToXml()
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "AdminReports Config File|*.xml"
            fsa.FileName = "AdminReportsConfig.xml"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim adminReportsDTO = New DTOAdminReports(ECGlobals.CurrentFacility)

            Dim writer As New XmlSerializer(GetType(DTOAdminReports))
            Dim fw As New System.IO.StreamWriter(fs)
            '      "c:\apps\UrgentCareDefaultReportsv2.xml")
            writer.Serialize(fw, adminReportsDTO)
            fw.Close()
        Catch ex As Exception
            MessageBox.Show($"Failure : {ex}", "Error")
        End Try
        MessageBox.Show("File created successfully", "Status")
    End Sub

    Public Class ReportInfo

        Public Description As String
        Public FileName As String
        Public ReportType As String 'Facility, Obs, Phys, Export
        Public DateField As String   'DOS, DateEntered, ExportDate
        Public TreatmentAreaField As String

        Public PrimaryCategory

        Public Sub New(description As String, fileName As String, reportType As String, dateField As String, TreatmentAreaField As String, primaryCategory As Object)
            If description Is Nothing Then
                Throw New ArgumentNullException(NameOf(description))
            End If

            If fileName Is Nothing Then
                Throw New ArgumentNullException(NameOf(fileName))
            End If

            If reportType Is Nothing Then
                Throw New ArgumentNullException(NameOf(reportType))
            End If

            If dateField Is Nothing Then
                Throw New ArgumentNullException(NameOf(dateField))
            End If

            If TreatmentAreaField Is Nothing Then
                Throw New ArgumentNullException(NameOf(TreatmentAreaField))
            End If

            If primaryCategory Is Nothing Then
                Throw New ArgumentNullException(NameOf(primaryCategory))
            End If

            Me.Description = description
            Me.FileName = fileName
            Me.ReportType = reportType
            Me.DateField = dateField
            Me.TreatmentAreaField = TreatmentAreaField
            Me.PrimaryCategory = primaryCategory
        End Sub
    End Class

    Public Sub ExportAdminReportsToCSV()
        Try
            Dim reportInfoList As New List(Of ReportInfo)

            Dim adminReportsDTO = New DTOAdminReports(ECGlobals.CurrentFacility)
            Dim prodReports As New List(Of String)

            For Each category In adminReportsDTO.FacilityCategories
                If category.Type.Equals("FIXED", StringComparison.OrdinalIgnoreCase) = False Then Continue For
                If category.Reports Is Nothing Then Continue For
                For Each report In category.Reports

                    Dim reportType = CalcReportType(report)
                    Dim dateField = GetDateField(report)
                    If dateField Is Nothing Then
                        Debugger.Break()
                    End If
                    Dim reportRec = New ReportInfo(report.Description, report.Filename, CalcReportType(report), GetDateField(report), GetTANUField(report), category.Name)
                    reportInfoList.Add(reportRec)
                Next
            Next
            Dim csvFile = "reports.csv"
            Using writer = New StreamWriter(csvFile)
                'writer.WriteLine("Name,Description")
                For Each report In reportInfoList
                    writer.WriteLine($" {report.Description}, {report.FileName},{report.ReportType}, {report.DateField},{report.TreatmentAreaField}, {report.PrimaryCategory}")
                Next
            End Using

        Catch ex As Exception
            MessageBox.Show($"Failure : {ex}", "Error")
        End Try
        MessageBox.Show("File created successfully", "Status")
    End Sub

    Private Function GetTANUField(report As DTOAdminReportsReport) As String
        ' Dim szDateField As String
        Dim isPercentCompleteReport As Boolean = False
        Dim repType = CalcReportType(report)

        If report.Filename.Contains("PercentComplete", StringComparison.OrdinalIgnoreCase) Then
            isPercentCompleteReport = True
        End If

        If UCase(repType) = "OBS" Then
            If isPercentCompleteReport Then
                Return "DOChart.OBSNurseUnit"
            Else
                Return "DOChartSummary.ObsTreatmentArea"
            End If
        Else
            If isPercentCompleteReport Then
                Return "DOChart.TreatmentArea"
            Else
                Return "DOChartSummary.TreatmentArea"
            End If
        End If
    End Function

    Private Function CalcReportType(report As DTOAdminReportsReport) As String
        If report.Type.IsObservation Then Return "OBS"
        If report.Type.IsPhysician Then Return "PHYS"
        If report.Type.IsExport Then Return "EXPORT"
        If report.Type.IsInfusion Then Return "INFUSION"
        Return "FACILITY"
    End Function

    Private Function CalcDateType(report As DTOAdminReportsReport) As String
        Dim reportType = CalcReportType(report)

        If report.Description.Contains("Productivity", StringComparison.OrdinalIgnoreCase) Then
            Return "DATEENTERED"
        End If
        If report.Filename.Contains("Productivity", StringComparison.OrdinalIgnoreCase) Then
            Return "DATEENTERED"
        End If

        'If report.Filename.Contains("PercentComplete", StringComparison.OrdinalIgnoreCase) Then
        '    Return "PERCENTCOMPLETE"
        'End If

        If report.Type.IsExport Then
            Return "EXPORTDATE"
        End If

        Return "DOS"
    End Function

    Private Function GetDateField(report) As String
        Dim szDateField As String
        Dim isPercentCompleteReport As Boolean = False
        Dim repType = CalcReportType(report)

        If report.Filename.Contains("PercentComplete", StringComparison.OrdinalIgnoreCase) Then
            isPercentCompleteReport = True
        End If

        szDateField = "DOChartSummary.ObsStartDate"
        Select Case UCase(CalcDateType(report))
            Case "DOS"
                Select Case UCase(repType)
                    Case "OBS"
                        szDateField = "DOChartSummary.ObsStartDate"
                    Case Else
                        szDateField = "DOChartSummary.ArrivalDate"
                End Select

                ' This condition is being added temporarily before a more robust fix is made
                ' This is checking for a Percent Complete report and will use the DOChart.DateOfService (ED) 
                ' or the DOChartRedisplay.ItemValue (Obs, when DOChartRedisplay.ItemName = 'ObsTimesObsTimesStartTime_dte')
                If isPercentCompleteReport Then
                    Select Case UCase(repType)
                        Case "OBS"
                            szDateField = "ObsStartDate (Report Calculated Field)" '"CONVERT(DateTime, DOChartRedisplay.ItemValue, 101)"
                        Case Else
                            szDateField = "DOChart.DateOfService"
                    End Select
                End If
            Case "DATEENTERED"
                If isPercentCompleteReport Then
                    szDateField = "DOChart.CreationDate"
                Else
                    Select Case UCase(repType)
                        Case "FACILITY"
                            szDateField = "DOChart.FacChartStatusChangedOn"
                        Case "PHYSICIAN"
                            szDateField = "DOChart.PhysChartStatusChangedOn"
                        Case "OBS"
                            szDateField = "DOChart.ObsChartStatusChangedOn"
                        Case Else
                            szDateField = "DOChart.CreationDate"
                    End Select
                End If
            Case "DATEEXPORTED"
                szDateField = "ChargeExportHistory.ExportDate"
            Case Else
                szDateField = "DOChartSummary.ArrivalDate"
        End Select

        Return szDateField
    End Function


    Private Sub BarButtonItem23_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem23.ItemClick

        ExportAdminReportsToXml()
    End Sub

    Private Sub BarButtonItem24_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem24.ItemClick
        Auth_MoreSecure.ShutDownESS()
    End Sub

    Private Sub ceHideSuppliesTab_CheckedChanged(sender As Object, e As EventArgs) Handles ceHideSuppliesTab.CheckedChanged

    End Sub

    Private Sub BarButtonItem25_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem25.ItemClick
        Dim f As New ParagonImmunizationMappingsForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem26_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem26.ItemClick
        Dim f As New ParagonMedFiltersForm
        f.ShowDialog()
    End Sub

    Private Sub TreatmentAreasPage1_Load(sender As Object, e As EventArgs) Handles TreatmentAreasPage1.Load

    End Sub

    Private Sub BarButtonItem27_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem27.ItemClick
        Dim cdm = New CDMReports.CDMReports() 'ECGlobals.CurrentFacility.Oid & " " & ECGlobals.CurrentUser.Oid)
        cdm.InitFromUI(ECGlobals.CurrentUser, ECGlobals.CurrentFacility)
        cdm.ShowDialog()
    End Sub

    Private Sub BarButtonItem28_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem28.ItemClick
        ExportAdminReportsToCSV()
    End Sub

    Private Sub ExportQueryFingerPrints_Click(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiExportQueryFingerPrints.ItemClick
        QueryFingerPrintPersistanceHelper.ExportFingerPrints()
    End Sub

    Private Sub ImportQueryFingerPrints_Click(sender As Object, e As EventArgs) Handles bbiImportQueryFingerPrints.ItemClick
        QueryFingerPrintPersistanceHelper.ImportFingerPrints()
    End Sub

    Private Sub BarButtonItem29_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem29.ItemClick
        Dim f As New ParagonHydrationsListForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem30_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem30.ItemClick
        Dim f As New ParagonRouteListForm
        f.ShowDialog()
    End Sub
End Class


Public Class DOControlConfigGroupAdv
    Sub New(ByVal pCOGroup As DOControlConfigGroup)
        COGroup = pCOGroup
    End Sub

    Private _COGroup As DOControlConfigGroup
    Public Property COGroup() As DOControlConfigGroup
        Get
            Return _COGroup
        End Get
        Set(ByVal value As DOControlConfigGroup)
            _COGroup = value
        End Set
    End Property

    Public Sub LoadControls(ByVal pNurseForm As ENChartCAC.ChartBaseForm, ByVal OverrideName As String, ByVal chkHidden As CheckEdit, ByVal teDisplayText As TextEdit)
        Dim cor As DOControlConfig = COGroup(OverrideName, True)
        'Note, even if there is currently no override, COGroup creates one and returns it to us...
        If cor IsNot Nothing Then
            If Not cor.Enabled Then
                chkHidden.CheckState = CheckState.Unchecked
                teDisplayText.EditValue = pNurseForm.FindControl(OverrideName).Text
            Else
                If cor.Hide = True Then
                    chkHidden.CheckState = CheckState.Indeterminate
                Else
                    chkHidden.CheckState = CheckState.Checked
                End If
                teDisplayText.EditValue = cor.DisplayText
            End If

            'chkhidden.CheckState =
            'chkHidden.Checked = cor.Hide
            'teDisplayText.EditValue = cor.DisplayText
        Else
            teDisplayText.EditValue = pNurseForm.FindControl(OverrideName).Text
        End If
    End Sub

    Public Sub LoadObsQIControls(ByVal OverrideName As String, ByVal chkHidden As CheckEdit, ByVal teDisplayText As TextEdit)
        Dim cor As DOControlConfig = COGroup(OverrideName, True)
        'Note, even if there is currently no override, COGroup creates one and returns it to us...
        If cor IsNot Nothing Then
            If Not cor.Enabled Then
                chkHidden.CheckState = CheckState.Unchecked
                teDisplayText.EditValue = "Option"
            Else
                If cor.Hide = True Then
                    chkHidden.CheckState = CheckState.Indeterminate
                Else
                    chkHidden.CheckState = CheckState.Checked
                End If
                teDisplayText.EditValue = cor.DisplayText
            End If

            'chkhidden.CheckState =
            'chkHidden.Checked = cor.Hide
            'teDisplayText.EditValue = cor.DisplayText
        Else
            teDisplayText.EditValue = "Option"
        End If
    End Sub

    Public Sub SaveControls(ByVal OverrideName As String, ByVal chkControl As CheckEdit, ByVal teDisplayText As TextEdit)
        Dim bHidden As Boolean = chkControl.Checked
        Dim DisplayText = teDisplayText.EditValue
        ' bHidden = t
        If Me.COGroup.Oid < 0 Then
            Me.COGroup.Save()
        End If

        Dim cor As DOControlConfig = COGroup(OverrideName, True)
        '=====----------- This is how it should work... but to avoid making changes to the gui... we'll delete it instead
        'If cor IsNot Nothing Then
        '    cor.Enabled = (chkControl.CheckState <> CheckState.Unchecked)
        '    cor.Hide = (chkControl.CheckState = CheckState.Indeterminate)
        '    cor.DisplayText = DisplayText
        '    cor.Save()
        'End If

        If cor IsNot Nothing Then
            cor.Enabled = (chkControl.CheckState <> CheckState.Unchecked)
            If cor.Enabled = False Then
                cor.Delete()
                Return
            End If
            cor.Hide = (chkControl.CheckState = CheckState.Indeterminate)
            cor.DisplayText = DisplayText
            cor.Save()
        End If
    End Sub
End Class

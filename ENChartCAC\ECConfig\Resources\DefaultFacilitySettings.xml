<DTOFacilitySettingsList>
  <Settings>
    <DTOFacilitySetting>
      <Name>AllowNewUserRoles</Name>
      <Category>AdminReports</Category>
      <DefaultValue>True</DefaultValue>
      <Description>When enabled, a reports user with admin privleges can create new custom user roles for reports</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>CodingReportDetailFontSize</Name>
      <Category>CodingReport</Category>
      <DefaultValue>11</DefaultValue>
      <Description>This setting is used by the coding report to control the font size of the detail text</Description>
      <IsTASpecific>1</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>DrugCodeMod59</Name>
      <Category>Modifier59</Category>
      <DefaultValue>no</DefaultValue>
      <Description>When this setting is enabled (yes/true), modifier 59 can be applied to drug codes</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>DrugCodeModXUStartDate</Name>
      <Category>ModifierXU</Category>
      <DefaultValue>12/31/2099</DefaultValue>
      <Description>This setting determines the first day to apply modifier XU to drug codes</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>EMGridLevelAlgorithm</Name>
      <Category>EMLevel</Category>
      <DefaultValue>Original</DefaultValue>
      <Description>When enabled, this setting will allow contributing factors to modify the EM Level</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>InfusionAdditionalHoursCalcType</Name>
      <Category>Observation</Category>
      <DefaultValue>Visit</DefaultValue>
      <Description>This setting defines how to limit additional drug hours by - Visit or Day</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>InfusionAdditionalHydHoursMaxHours</Name>
      <Category>Observation</Category>
      <DefaultValue>9999</DefaultValue>
      <Description>This setting is only applicable when InfusionAdditionalHoursCalcType is Visit or Day</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>InfusionAdditionalMedHoursMaxHours</Name>
      <Category>Observation</Category>
      <DefaultValue>9999</DefaultValue>
      <Description>This setting is only applicable when InfusionAdditionalHoursCalcType is Visit or Day</Description>
      <IsTASpecific>0</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>UseInjectInfuseDOA</Name>
      <Category>InjectionInfusion</Category>
      <DefaultValue>True</DefaultValue>
      <Description>This setting is used by the rules engine to calculate injections and infusions based on their date of administration (True) instead of the date of arrival (False)</Description>
      <IsTASpecific>1</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>UsePaymentTypeLevelCalculation</Name>
      <Category>EMLevel</Category>
      <DefaultValue>No</DefaultValue>
      <Description>When enabled (Yes), this setting will apply additional rules based on the patient's payment type (Medicare/non-Medicare)</Description>
      <IsTASpecific>1</IsTASpecific>
    </DTOFacilitySetting>
    <DTOFacilitySetting>
      <Name>UseWOManipulation</Name>
      <Category>Orthopedics</Category>
      <DefaultValue>No</DefaultValue>
      <Description>The value of this setting determines which set of Orthopedics ESP codes will be used</Description>
      <IsTASpecific>1</IsTASpecific>
    </DTOFacilitySetting>
  </Settings>
</DTOFacilitySettingsList>
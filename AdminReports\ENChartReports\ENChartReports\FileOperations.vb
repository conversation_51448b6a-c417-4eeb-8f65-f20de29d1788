Imports System
Imports System.IO
Imports System.Windows.Forms

Module FileOperations

    Public Function WriteBufferToFile(ByVal buffer As String, ByVal filename As String) As Boolean
        My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "WriteBufferToFile:" & filename, TraceEventType.Start)
        Try
            Dim writerVar As StreamWriter

            If filename = "" Then
                filename = Application.StartupPath & "\DataFiles\OBOutput" & Format(Now, "MMddyyHHmmssfff") & ".txt"
            Else
                filename = Application.StartupPath & "\Datafiles\" & filename
            End If
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "WriteBufferToFile:" & filename, TraceEventType.Verbose)
            writerVar = New StreamWriter(filename, False)
            writerVar.Write(buffer & ControlChars.CrLf)
            writerVar.Close()
            WriteBufferToFile = True
        Catch ex As Exception
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & ex.Message, TraceEventType.Error)
            WriteBufferToFile = False
        End Try
        My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "WriteBufferToFile", TraceEventType.Stop)
    End Function

    Public Function Verify_File_Exists(ByVal pathandfilename As String) As Boolean
        Dim ReturnValue As Boolean

        ReturnValue = File.Exists(pathandfilename)
        Return ReturnValue
    End Function

End Module

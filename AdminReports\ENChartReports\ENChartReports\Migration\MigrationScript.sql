-- Migrate current report list to new DOFacilityReport Table
insert into DOFacilityReport (ReportDescription, ReportFilename, ReportListOrder, OriginalOID)
SELECT A.ReportDescription, A.ReportFilename, A.ReportListOrder, A.OID
FROM FacilityReportList A
WHERE A.OID not in (
    SELECT  A.OID FROM FacilityReportList A
    Where A.reporttype like 'category%'
);

-- Use the new DOFacilityReportRoles table
INSERT INTO DOFacilityReportRole (UserRole, Report, Enabled)
SELECT A.role_id, B.OID, 1
FROM UserRoleReports A, DOFacilityReport B
where A.Report_id = B.OriginalOID;

Insert Into DOFacilityReportType( Report, IsPhysicianReport,IsExportReport, IsObservationReport, IsInfusionReport)
SELECT
    A.Oid,
    Case when B.reporttype like '%physician%' then 1 else 0 end,
    Case when B.reporttype like '%export%' then 1 else 0 end,
    Case when B.reporttype like '%obs%' then 1 else 0 end,
    Case when B.reporttype like '%infusion%' then 1 else 0 end
FROM DOFacilityReport A, FacilityReportList B
WHERE A.OriginalOID = B.OID;

--- This query will delete any old report records for facilities that don't exist
DELETE FROM FacilityReportList WHERE Facility NOT IN (SELECT oid FROM DOFacility WHERE GCRecord IS null);

--- This query will take the current categories and put them into the DOFacilityReportCategory Table
insert into DOFacilityReportCategory (Facility, CategoryName, CategoryOrder, OriginalOID, CategoryType)
SELECT A.Facility, A.ReportDescription, B.ListORder, A.OID, UPPER(Replace(Replace(A.ReportType,'category', ''), '_', '')) FROM FacilityReportList A, FacilityReportCategoryCross B Where B.SubID = A.OID
and A.reporttype like 'category%'

-- Use the new DOFacilityReportCategoryRole table
INSERT INTO DOFacilityReportCategoryRole (UserRole, Category, Enabled)
SELECT A.role_id, B.OID, 1
FROM UserRoleReports A, DOFacilityReportCategory B
where A.Report_id = B.OriginalOID

-- Assign Site Admin to 'NewReports'
INSERT INTO DOFacilityReportCategoryRole (UserRole, Category)
SELECT B.OID, A.OID
FROM DOFacilityReportCategory A, DOUserRolesList B
WHERE A.Facility = B.Facility
AND B.Role = 'Site Admin'
AND A.CategoryType = 'NEWREPORTS'

-- Assign Site Admin to 'User'
INSERT INTO DOFacilityReportCategoryRole (UserRole, Category)
SELECT B.OID, A.OID
FROM DOFacilityReportCategory A, DOUserRolesList B
WHERE A.Facility = B.Facility
AND B.Role = 'Site Admin'
AND A.CategoryType = 'USER'

Update  DOFacilityReport
SET B.ReportCategory = C.OID
FROM FacilityReportCategoryCross  A, DOFacilityReport B,  DOFacilityReportCategory C
WHERE A.SubID = B.OriginalOID
AND C.OriginalOID = A.MasterCategoryID;

UPDATE DOFacilityReport
SET A.ReportType = B.OID
FROM DOFacilityReport A,  DOFacilityReportType B 
WHERE B.Report = A.OID;

update dofacilityreportcategoryrole set enabled = 1
update dofacilityreportrole set enabled = 1
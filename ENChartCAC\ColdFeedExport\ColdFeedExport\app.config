﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="ColdFeedExport.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <!-- system.diagnostics section is not supported on .NET 6 (see https://github.com/dotnet/runtime/issues/23937)-->
    <!--<system.diagnostics>
  <sources>
    <!- - This section defines the logging configuration for My.Application.Log - ->
    <source name="DefaultSource" switchName="DefaultSwitch">
      <listeners>
        <add name="FileLog" />
        <!- - Uncomment the below section to write to the Application Event Log - ->
        <!- -<add name="EventLog"/>- ->
      </listeners>
    </source>
  </sources>
  <switches>
    <add name="DefaultSwitch" value="Information" />
  </switches>
  <sharedListeners>
    <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
    <!- - Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log - ->
    <!- -<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> - ->
  </sharedListeners>
</system.diagnostics>-->
    <applicationSettings>
        <ColdFeedExport.My.MySettings>
            <setting name="ImageOutputPath" serializeAs="String">
                <value>c:\apps\eccoder\ColdFeedExportOutFiles</value>
            </setting>
            <setting name="DetailsOutputPath" serializeAs="String">
                <value>c:\apps\eccoder\ColdFeedExportOutFiles</value>
            </setting>
            <setting name="TriggerMethod" serializeAs="String">
                <value>Exportstatus</value>
            </setting>
            <setting name="BatchLabel" serializeAs="String">
                <value>HIC Coldfeed</value>
            </setting>
            <setting name="ImageFileType" serializeAs="String">
                <value>pdf</value>
            </setting>
            <setting name="StartDate" serializeAs="String">
                <value>2009-04-01</value>
            </setting>
            <setting name="MultiExport" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="DateFormat" serializeAs="String">
                <value>yyyyMMdd hh:mmtt</value>
            </setting>
            <setting name="FacilityID" serializeAs="String">
                <value>all</value>
            </setting>
            <setting name="UseFullPathPData" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ImageFeedMode" serializeAs="String">
                <value>OneContent</value>
            </setting>
        </ColdFeedExport.My.MySettings>
    </applicationSettings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
    </startup>
</configuration>
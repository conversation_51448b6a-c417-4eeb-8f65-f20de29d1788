﻿Imports System
Imports System.Text
Imports System.Collections.Generic
Imports Microsoft.VisualStudio.TestTools.UnitTesting

'Imports System
'Imports System.Text
'Imports System.Collections.Generic
'Imports Microsoft.VisualStudio.TestTools.UnitTesting
Imports McKesson.HIC.ColdFeed
Imports McKesson.HIC.ColdFeed.ColdFeedHelper
Imports EnchartDOLib
Imports DevExpress
Imports DevExpress.Xpo
Imports DevExpress.Data
Imports System.Configuration
Imports DevExpress.Xpo.DB

'Imports NMock2


<TestClass()> Public Class ShouldExportExportStatusTriggerTest1
    Private Const STR_Exported As String = "Exported"
    Private Const ChartOID2 As Integer = 2
    Private Const ChartOID3 As Integer = 3
    Private Const ChartOID4 As Integer = 4
    Private Const ChartOID1 As Integer = 1
    Private Const STR_Complete As String = "Complete"
    Private Const STR_Incomplete As String = "Incomplete"

    Private testContextInstance As TestContext

    '''<summary>
    '''Gets or sets the test context which provides
    '''information about and functionality for the current test run.
    '''</summary>
    Public Property TestContext() As TestContext
        Get
            Return testContextInstance
        End Get
        Set(ByVal value As TestContext)
            testContextInstance = value
        End Set
    End Property


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart = v(1)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest1()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Try
             dim chartinfo As New DOChartInfo()
            
            Dim chartsDict As New Dictionary(Of Integer, DOChart)()
            Get4Charts(chartinfo, chartsDict)
            chartinfo.Chart = chartsDict(ChartOID1)
            chartinfo.ExportStatus = STR_Exported
            'chartinfo.ExportedChart = chartsDict(ChartOID1)
            chartinfo.Chart = chartsDict(ChartOID4)
            chartinfo.CFExportUserRequestPending = False
            Dim result As New Dictionary(Of Integer, ChartObjWrapper)()
            Dim chartWrapper As ChartObjWrapper
            For Each chart In chartsDict.Values
                chartWrapper = New ChartObjWrapper(chart)
                If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                    result(chart.ChartInfo.Oid) = chartWrapper
                End If
            Next
            Dim returnedDOChartOID = result(1).Chart.Oid
            Assert.AreEqual(ChartOID1, returnedDOChartOID)
            Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
        
       Finally
           'DoChartinfo doesn't implement IDispose() --- as there is no need ...
        End Try
       
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = Nothing
    ''' CFExportFacilityStatus = Nothing
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: v2 because CFExportFacilityStatus = nothing
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest2()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = Nothing

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = Nothing


        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID2, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = Nothing
    ''' CFExportFacilityStatus = Nothing
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: v2 because CFExportFacilityStatus = nothing
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest3()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = Nothing

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = Nothing


        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID2, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: v4 exported because CFExportUserRequestPending =  TRUE
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest4()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = STR_Exported


        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.UserRequested)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: no export, because chartinfo.ExportStatus is nothing
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest5()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        'chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        'chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = Nothing


        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 0)
        'Dim returnedDOChartOID = result(1).Chart.Oid

        'Assert.AreEqual(ChartOID4, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.UserRequested)
    End Sub




    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = false
    ''' ChartInfo.ExportedChart =  v(2)
    ''' ChartInfo.PhysExportedChart = N/A
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.CFExportFacilityStatus = STR_Exported
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' 
    ''' Expected Result:  no export, cause exportStatus is Nothing
    ''' even though we show a chart as having been exported....
    ''' Actually, not really sure what we should do here... cause
    ''' it should never really happen... but we'll leave it....
    ''' </summary>
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest6()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.ExportedChart = chartsDict(ChartOID1)
        chartinfo.PhysExportChart = Nothing
        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.CFExportFacilityStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        'Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(0, result.Count)
        'Assert.AreEqual(ChartOID1, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: export v2, cause this is first time we see it as billing exported....
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest7()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        'chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = Nothing


        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID2, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  false
    ''' </summary>
    ''' Export: same as 7, just no userRequest pending
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest8()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        'chartinfo.ExportedChart = chartsDict(ChartOID2)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.PhysExportChart = Nothing
        chartinfo.CFExportFacilityStatus = Nothing


        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID2, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: export v3, cause v3 was the last billing exported version that has also not been CFExported yet
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest9()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'note this will also set exported chartversion to ChartOID2
        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.PhysExportStatus = STR_Exported
        Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID3, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.BothBillingExportStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: 
    ''' <remarks></remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest10()
        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'Exported Chart = 2
        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.PhysExportStatus = STR_Exported 'PhysExported Chart = 3
        Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = STR_Complete

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID3, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export:  Export 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest14() 'Row 14 Test

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'Exported Chart = 2
        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.PhysExportStatus = STR_Exported 'PhysExported Chart = 3
        Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete '(not complete)

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID3, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: 
    ''' <remarks> This is a condition which doesn't make sense to me...
    ''' but the expedted decision is from the Karen and company...
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest15() 'Row 15 Test

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'Exported Chart = 2
        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.PhysExportStatus = STR_Exported 'PhysExported Chart = 3
        Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete '(not complete)

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1)
        Dim returnedDOChartOID = result(1).Chart.Oid
        Assert.AreEqual(ChartOID3, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ExportStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = v(2)
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' CFExportFacilityStatus = "Exported"
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  TRUE
    ''' </summary>
    ''' Export: 
    ''' <remarks> This is a condition which doesn't make sense to me...
    ''' but the expedted decision is from the Karen and company...
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportExportStatusTriggerTest16() 'Row 16 Test

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.ExportStatus = STR_Exported 'Exported Chart = 2
        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.PhysExportStatus = STR_Exported 'PhysExported Chart = 3
        Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete '(not complete)

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 0)
        'Dim returnedDOChartOID = result(1).Chart.Oid

        'Assert.AreEqual(ChartOID3, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityBillingExportStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = NA
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' 
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' chartInfo.Chart.PhysChartStatus =  "Complete"
    ''' 
    ''' chartinfo.CFExportFacilityStatus = Nothing
    ''' chartinfo.CFExportPhysicianStatus = Nothing
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  FALSE
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest22() 'Row 22?

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete '(not complete)

        'chartinfo.ExportStatus = STR_Exported 'Exported Chart = 2

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete '(not complete)

        'chartinfo.PhysExportStatus = STR_Exported 'PhysExported Chart = 3
        'Assert.AreEqual("Exported", chartinfo.PhysExportStatus)

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete '(not complete)

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1, "Count Not = 1")
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.BothChartStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' ChartInfo.ExportedChart = NA
    ''' chartinfo.PhysExportChart = NA
    ''' ChartInfo.Chart = v(4)
    ''' 
    ''' chartinfo.Chart.ChartStatus = "Complete"
    ''' chartInfo.Chart.PhysChartStatus =  "Complete"
    ''' 
    ''' chartinfo.CFExportFacilityStatus = Nothing
    ''' chartinfo.CFExportPhysicianStatus = Nothing
    ''' 
    ''' ChartInfo.CFExportUserRequestPending =  FALSE
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest23() 'Row 23

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1, "Count Not = 1")
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub




    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' 
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest24() 'Row 24

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1, "Count Not = 1")
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' 
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest25() 'Row 25

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 0, "Count Not = 0")
        'Dim returnedDOChartOID = result(1).Chart.Oid

        'Assert.AreEqual(ChartOID4, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub




    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' 
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest26()

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=True) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 0, "Count Not = 0")
        'Dim returnedDOChartOID = result(1).Chart.Oid

        'Assert.AreEqual(ChartOID4, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = false
    ''' 
    ''' chartinfo.CFExportUserRequestPending = True
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest30()

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.CFExportFacilityStatus = Nothing
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = True
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1, "Count Not = 1")
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub



    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' 
    ''' chartinfo.CFExportUserRequestPending = True
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerTest31()

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = Nothing

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = Nothing

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 0, "Count Not = 0")
        'Dim returnedDOChartOID = result(1).Chart.Oid

        'Assert.AreEqual(ChartOID4, returnedDOChartOID)
        'Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub


    ''' <summary>
    ''' TriggerMethod = "ChartStatus"
    ''' PhysicanEnabled = true
    ''' MultiExport =  true
    ''' chartinfo.CFExportUserRequestPending = false
    ''' </summary>
    ''' Export: 
    ''' <remarks> 
    ''' </remarks>
    <TestMethod()> Public Sub ShouldExportChartStatusTriggerMultiExportTest32()

        ' TODO: Add test logic here
        Dim cfh As ColdFeedHelper = GetCfh()
        cfh.OptionsDict("MultiExport") = True
        cfh.iGetChartsToExport.TriggerMethod = "ChartStatus"

        Dim chartinfo As New DOChartInfo
        Dim chartsDict As New Dictionary(Of Integer, DOChart)
        Get4Charts(chartinfo, chartsDict)

        chartinfo.Chart = chartsDict(ChartOID2)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Incomplete

        chartinfo.Chart = chartsDict(ChartOID3)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.Chart = chartsDict(ChartOID4)
        chartinfo.Chart.ChartStatus = STR_Complete
        chartinfo.Chart.PhysChartStatus = STR_Complete

        chartinfo.CFExportFacilityStatus = STR_Exported
        chartinfo.CFExportPhysicianStatus = STR_Exported

        chartinfo.CFExportUserRequestPending = False
        Dim result As New Dictionary(Of Integer, ChartObjWrapper)

        'cfh.iGetChartsToExport.TriggerMethod = "ExportStatus"
        Dim chartWrapper As ChartObjWrapper
        For Each chart In chartsDict.Values
            chartWrapper = New ChartObjWrapper(chart)
            If cfh.iGetChartsToExport.ShouldExport(chartWrapper, IsPhysEnabled:=False) Then
                result(chart.ChartInfo.Oid) = chartWrapper
            End If
        Next

        Assert.IsTrue(result.Count = 1, "Count Not = 1")
        Dim returnedDOChartOID = result(1).Chart.Oid

        Assert.AreEqual(ChartOID4, returnedDOChartOID)
        Assert.IsTrue(result(1).ReasonForExport = ChartObjWrapper.ReasonEnum.FacilityChartStatus)
    End Sub


    '  Dim cfh As ColdFeedHelper
#Region "Additional test attributes"
    '
    ' You can use the following additional attributes as you write your tests:
    '
    ' Use ClassInitialize to run code before running the first test in the class
    <ClassInitialize()> Public Shared Sub MyClassInitialize(ByVal testContext As TestContext)

        ConnectToECDataBase()


    End Sub
    '
    ' Use ClassCleanup to run code after all tests in a class have run
    ' <ClassCleanup()> Public Shared Sub MyClassCleanup()
    ' End Sub
    '
    ' Use TestInitialize to run code before running each test
    <TestInitialize()> Public Sub MyTestInitialize()

    End Sub
    '
    ' Use TestCleanup to run code after each test has run
    ' <TestCleanup()> Public Sub MyTestCleanup()
    ' End Sub
    '
#End Region

    Private Shared Function GetCfh() As ColdFeedHelper
        Dim cfh = New ColdFeedHelper(New HPFColdFeedImageFileHandler, _
                                                      New HPFColdFeedDetailsFileHandler, _
                                                      New DefaulGetChartsToExport, _
                                                      New DefaultLog, _
                                                      New DefaultShowMessage, "..\", "..\")
        cfh.OptionsDict("MultiExport") = False
        Return cfh
    End Function

    Private Sub Get4Charts(ByVal chartInfo As DOChartInfo, ByVal charts As Dictionary(Of Integer, DOChart))
        'If charts Is Nothing Then
        '    charts = New List(Of DOChart)

        'End If

        'chartInfo() '= New DOChartInfoProxy()

        With chartInfo
            .Oid = 1
            .VisitID = "1"
            .CFExportInclude = True
            .ChartVersion = 1
        End With

        Dim c1 As New DOChartProxy(chartInfo.VisitID) With {.Oid = 1}

        'chartInfo.ExportedChart = c1

        'Return chart 1

        c1.ChartInfo = chartInfo
        charts(ChartOID1) = c1

        Dim c2 As New DOChartProxy(chartInfo.VisitID) With {.Oid = ChartOID2}
        c2.ChartInfo = chartInfo
        charts(ChartOID2) = c2
        Dim c3 As New DOChartProxy(chartInfo.VisitID) With {.Oid = ChartOID3}
        c3.ChartInfo = chartInfo
        charts(ChartOID3) = c3
        Dim c4 As New DOChartProxy(chartInfo.VisitID) With {.Oid = ChartOID4}
        c4.ChartInfo = chartInfo
        charts(ChartOID4) = c4
        chartInfo.Chart = c4

    End Sub

    Public Class DOChartProxy
        Inherits DOChart


        Public Sub New()

        End Sub

        Public Sub New(ByVal session As Session)
            MyBase.New(session)

        End Sub
        Public Sub New(ByVal visitID As String)
            MyBase.New(visitID)

        End Sub
        Public Sub New(ByVal pchart As DOChart)
            MyBase.New(pchart)

        End Sub

    End Class

    Private Class DOChartInfoProxy
        Inherits DOChartInfo
        Public Sub New()
            '
        End Sub
        Public Sub New(ByVal session As Session)
            MyBase.New(session)

        End Sub

        Sub SetFacilityExported(ByVal chart As DOChartProxy)
            Me.ExportedChart = chart
            Me.ExportStatus = STR_Exported
        End Sub


        '' Private _ExportedChart As DOChart
        'Public Overrides Property ExportedChart() As DOChart
        '    Get
        '        Return MyBase.ExportedChart
        '    End Get
        '    Set(ByVal value As DOChart)
        '        MyBase.ExportedChart = value
        '        ExportStatus = STR_Exported
        '    End Set
        'End Property



    End Class

    Shared Sub ConnectToECDataBase()
        Dim fm As New System.Configuration.ExeConfigurationFileMap() With {.ExeConfigFilename = "c:\apps\eccoder\ENChartCAC.exe.config"}

        Dim config As Configuration = ConfigurationManager.OpenMappedExeConfiguration(fm, ConfigurationUserLevel.None)
        'Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
        Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
        cs += ";uid=dba;pwd=sql"

        XpoDefault.DataLayer = XpoDefault.GetDataLayer(cs, AutoCreateOption.DatabaseAndSchema)
    End Sub

End Class

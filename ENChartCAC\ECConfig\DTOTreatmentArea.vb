Imports System.Xml.Serialization

'Public Class DTOTreatmentArea

'    Public Sub New()

'    End Sub

'    Public Sub New(ByVal pTA)
'        Me.TreatmentArea = pTA
'    End Sub

'    Public Sub New(ByVal doTA As DOTreatmentArea)
'        Me.TreatmentArea = doTA.Name
'        Me.TreatmentAreaType = doTA.TreatmentAreaType
'        Me.ChargeMaster = doTA.ChargeMaster
'        Me.Enabled = doTA.Enabled
'    End Sub

'    <XmlAttribute()>
'    Public TreatmentArea As String
'    Public TreatmentAreaType As String
'    Public ChargeMaster As String
'    Public Enabled As Boolean

'    Public ESPCodes As New List(Of DTOESPCode)
'    Public FacilitySettings As New List(Of DTOFacilitySettings)
'    Public CDMs As New List(Of DTOChargeMaster)

'    'Public CDMs As New List(Of DTOCDM)
'End Class

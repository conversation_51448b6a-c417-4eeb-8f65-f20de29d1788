﻿Imports System.IO
Imports System.Linq
Imports System.Text
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports Newtonsoft.Json

Public Class LoadMedicationMastersForm
    Private _globalMedicationsTableHasData As Boolean
    Private _helper As New MedicationMasterImportExportHelper


    Private Sub LoadMedicationMastersForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        '_helper = New MedicationMasterImportExportHelper(UnitOfWork1)
        XpCollection1.DeleteObjectOnRemove = True
        _globalMedicationsTableHasData = _helper.DOMedicationHasData
        If _globalMedicationsTableHasData Then
            btnMerge.Enabled = False
        End If
        UpdateDialogInfo()
    End Sub

#Region "Custom"

    Public Sub UpdateDialogInfo()
        _globalMedicationsTableHasData = _helper.DOMedicationHasData
        If _globalMedicationsTableHasData Then
            lblGMTUsage.Text = $"The Global Medicaitons Table Contains Data"
        Else
            lblGMTUsage.Text = $"The Global Medicaitons Table is Empty"
        End If

        UpdateButtonInfo()
        RefreshGrids()

    End Sub

    Private Sub RefreshGrids()
        XpCollection1.Reload()
    End Sub

    Private Sub UpdateButtonInfo()
        If _globalMedicationsTableHasData Then
            btnMerge.Enabled = False
            btnParagonLoad.Enabled = False
            btnSaveAsCsv.Enabled = True
            btnLoadFile.Enabled = False
            btnSaveJson.Enabled = True
            btnLoadJson.Enabled = True
        Else
            'DOMedicationsTable is Emtpy
            btnMerge.Enabled = True
            btnParagonLoad.Enabled = True
            btnSaveAsCsv.Enabled = False
            btnLoadFile.Enabled = True
            btnSaveJson.Enabled = False
            btnLoadJson.Enabled = True
        End If
    End Sub

    Private Sub btnParagonLoad_Click(sender As Object, e As EventArgs) Handles btnParagonLoad.Click
        Dim fileToLoad = _helper.GetShawnasFileToLoad '"AIC Paragon Med List Mapped to MEDIDs_FINAL.csv"
        If fileToLoad Is Nothing Then
            Exit Sub
        End If

        Try
            Cursor.Current = Cursors.WaitCursor
            _helper.LoadDataFromShawnasFile(fileToLoad)
        Finally
            Cursor.Current = Cursors.Default
            UpdateDialogInfo()
            ShowDataLoadSuccessMsg()
        End Try
    End Sub

    Private Sub btnMerge_Click(sender As Object, e As EventArgs) Handles btnMerge.Click
        _helper.Merge()
        UpdateDialogInfo()
    End Sub
#End Region

#Region "CSV"
    Private Sub btnSaveAsCsv_Click(sender As Object, e As EventArgs) Handles btnSaveAsCsv.Click
        _helper.SaveMedicationsToDiskAsCsv()
        UpdateDialogInfo()
    End Sub

    Private Sub btnLoadMedsFromCsv_Click(sender As Object, e As EventArgs) Handles btnLoadFile.Click
        Dim fileToLoad = _helper.GetFileToLoadFromUser()
        If fileToLoad Is Nothing Then Exit Sub

        Try
            Cursor.Current = Cursors.WaitCursor
            _helper.LoadMedsFromCsv2(fileToLoad)
            ShowDataLoadSuccessMsg()
        Finally
            Cursor.Current = Cursors.Default
            UpdateDialogInfo()

        End Try
    End Sub
#End Region

#Region "Global Meds List"

    Private Sub btnSaveJson_Click(sender As Object, e As EventArgs) Handles btnSaveJson.Click
        _helper.SaveMedicationsToDiskAsJson()
        UpdateDialogInfo()
    End Sub

    Private Sub btnLoadJson_Click(sender As Object, e As EventArgs) Handles btnLoadJson.Click

        Dim fileToLoad = _helper.GetJsonFileToLoadFromUser()
        If fileToLoad Is Nothing Then Exit Sub

        Try
            Cursor.Current = Cursors.WaitCursor
            _helper.LoadFromJason(fileToLoad)
        Finally
            Cursor.Current = Cursors.Default
            UpdateDialogInfo()
            ShowDataLoadSuccessMsg()
        End Try

    End Sub

#End Region

    Private Sub btnDeleteAll_Click(sender As Object, e As EventArgs) Handles btnDeleteAll.Click
        Cursor.Current = Cursors.WaitCursor
        Try
            If DialogResult.OK <> MessageBox.Show("You are about to permanently delete all records from the DOParagonMapping and DOMedicationMaster Tables. Are you sure you want to continue?", "Are you sure?", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) Then
                Exit Sub
            End If

            _helper.DeleteAll()
        Finally
            Cursor.Current = Cursors.Default
            UpdateDialogInfo()
        End Try
    End Sub

    Public Sub ShowDataLoadSuccessMsg()
        MessageBox.Show($"Action completed Successfully", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles btnViewMedIdMappings.Click
        'Dim f As New ENChartCAC.ConfigMedicationsFormV2(showGlobalMedsList:=True)
        'f.ShowDialog()
        Dim f As New ParagonMedIdForm
        f.ShowDialog()

    End Sub

    Private Sub btnDone_Click(sender As Object, e As EventArgs) Handles btnDone.Click
        UnitOfWork1.CommitChanges()
    End Sub

    Private Sub GridControl2_EmbeddedNavigator_ButtonClick(sender As Object, e As DevExpress.XtraEditors.NavigatorButtonClickEventArgs) Handles GridControl2.EmbeddedNavigator.ButtonClick
        If e.Button.ButtonType = DevExpress.XtraEditors.NavigatorButtonType.Remove Then
            'Dim blah =  
            GridView2.DeleteRow(GridView2.FocusedRowHandle)
        End If
    End Sub
End Class

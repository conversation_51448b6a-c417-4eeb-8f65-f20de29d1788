﻿
Namespace ColdFeed

    Public Class BatchCompiler
        Public Batch As New BatchElem

    End Class

    Public Class BatchElem
        Private _BLabel As String
        Public Property BLabel() As String
            Get
                Return _BLabel
            End Get
            Set(ByVal value As String)
                _BLabel = value
            End Set
        End Property

        'Public _Documents As New List(Of Document)

        Private _Documents As List(Of Document)
        Public Property Documents() As List(Of Document)
            Get
                If _Documents Is Nothing Then
                    _Documents = New List(Of Document)
                End If
                Return _Documents
            End Get
            Set(ByVal value As List(Of Document))
                _Documents = value
            End Set
        End Property

    End Class

    Public Class Document

        Public Pages As New List(Of Page)
        Public Indexes As New List(Of Index)

    End Class

    Public Class Page
        Public Sub New()

        End Sub
        Public Sub New(ByVal pdata As String)
            Me.PData = pdata
        End Sub

        Private _PData As String
        Public Property PData() As String
            Get
                Return _PData
            End Get
            Set(ByVal value As String)
                _PData = value
            End Set
        End Property

    End Class


    Public Class Index
        Public Sub New()

        End Sub
        Public Sub New(ByVal name As String, ByVal value As String)
            Me.Name = name
            Me.Value = value
        End Sub

        Private _Name As String
        Public Property Name() As String
            Get
                Return _Name
            End Get
            Set(ByVal value As String)
                _Name = value
            End Set
        End Property


        Private _Value As String
        Public Property Value() As String
            Get
                Return _Value
            End Get
            Set(ByVal value As String)
                _Value = value
            End Set
        End Property



    End Class

End Namespace

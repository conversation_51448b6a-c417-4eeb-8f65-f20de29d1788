Imports DevExpress.Xpo

Public Class ECGlobals_Obsolete '(jjc 8.27.18) ECConfig will use the ECGlobals class from the UI Assembly

    Public Shared CurrentUser As DOUser
    Public Shared CurrentFacility As DOFacility
    Public Shared CurrentConfigInstance As DOConfigInstance
    Public Shared DelayShutDown As Boolean


    Private Shared _GSettings As GlobalSettingHelper
    Public Shared ReadOnly Property GlobalSettings() As GlobalSettingHelper
        Get
            If _GSettings Is Nothing Then
                _GSettings = New GlobalSettingHelper(GlobalSettingsDictionary)
            End If

            Return _GSettings
        End Get
    End Property

    Private Shared _settings_dictionary As Dictionary(Of String, String)
    Public Shared ReadOnly Property GlobalSettingsDictionary() As Dictionary(Of String, String)
        Get
            'StringComparer.OrdinalIgnoreCase
            If _settings_dictionary Is Nothing Then
                LoadGlobalSettingsDictionary()
            End If

            Return _settings_dictionary
        End Get
    End Property

    Public Shared Sub LoadGlobalSettingsDictionary()
        _settings_dictionary = New Dictionary(Of String, String)(StringComparer.OrdinalIgnoreCase)

        Dim col As New XPCollection(Of DOGlobalSetting)
        For Each setting As DOGlobalSetting In col
            _settings_dictionary.Add(setting.SettingName, setting.SettingValue)
        Next

    End Sub

    Public Class GlobalSettingHelper
        Private _dict As IDictionary(Of String, String)

        Public Sub New(pdict As IDictionary(Of String, String))
            _dict = New Dictionary(Of String, String)(pdict, StringComparer.OrdinalIgnoreCase)
        End Sub

        Default Public ReadOnly Property Items(SettingName As String)
            Get
                Dim retValue As String = Nothing
                If _dict.TryGetValue(SettingName, retValue) Then
                    If String.IsNullOrWhiteSpace(retValue) Then
                        Return String.Empty
                    Else
                        Return retValue
                    End If
                End If

                Return String.Empty
            End Get
        End Property

        Public Function GetBool(SettingName As String) As Boolean
            Dim retstring As String = Items(SettingName)
            If Items(SettingName) = String.Empty Then Return False
            Return Boolean.Parse(retstring)
        End Function
    End Class
End Class

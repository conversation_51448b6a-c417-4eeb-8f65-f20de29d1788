﻿Imports System.Windows.Forms

Public Class CrystalReportsUpdateDialog

    Dim update_process As Process
    Dim update_complete As Boolean = False

    Private Sub tmrUpdating_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrUpdating.Tick
        If update_process.HasExited Then
            ShowUpdateIsComplete()
            tmrUpdating.Stop()
        End If
    End Sub

    Private Sub btnMultipurpose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMultipurpose.Click
        If update_complete Then
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Me.Close()
        Else
            UpdateCrystalReports()
        End If
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub UpdateCrystalReports()
        btnMultipurpose.Visible = False
        btnExit.Visible = False
        progbarUpdating.Visible = True

        lblMultipurpose.Text = "The AIC reports application is updating. This may take several minutes."
        lblMultipurpose2.Visible = False

        Dim exec_file As String = "crruntime_32bit_13_0_4.msi"
        Dim exec_switch As String = "/q"

        Try
            update_process = System.Diagnostics.Process.Start(exec_file, exec_switch)
            tmrUpdating.Start()
        Catch ex As Exception
            MessageBox.Show("The installation file could not be found. Please contact support. " & ex.ToString, "Update Error", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button1)
            Application.Exit()
        End Try
    End Sub

    Private Sub ShowUpdateIsComplete()
        update_complete = True

        btnMultipurpose.Text = "Exit"
        lblMultipurpose.Text = "The update is complete and will close. You will have to restart the AIC reports application."

        btnMultipurpose.Visible = True
        progbarUpdating.Visible = False
    End Sub
End Class

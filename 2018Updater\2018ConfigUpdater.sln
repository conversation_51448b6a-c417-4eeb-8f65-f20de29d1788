﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27703.2035
MinimumVisualStudioVersion = 10.0.40219.1
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "2018ConfigUpdater", "2018ConfigUpdater.vbproj", "{DD5F841F-8298-44B7-9768-F9387FF16964}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ENChartCAC", "..\ENChartCAC\ENChartCAC\ENChartCAC.vbproj", "{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "EnchartDOLib", "..\ENChartCAC\EnchartDOLib\EnchartDOLib.vbproj", "{06C12653-799B-4345-BD29-D83ED6BB6D44}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MICMiscUtilCSharp", "..\ENChartCAC\MICMiscUtilCSharp\MICMiscUtilCSharp.csproj", "{58DAAC63-8388-4B89-B876-1270309143BD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		DatabaseMigration|Any CPU = DatabaseMigration|Any CPU
		DatabaseMigration|x64 = DatabaseMigration|x64
		DatabaseMigration|x86 = DatabaseMigration|x86
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		JJCDebug|Any CPU = JJCDebug|Any CPU
		JJCDebug|x64 = JJCDebug|x64
		JJCDebug|x86 = JJCDebug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
		UI and DOLib|Any CPU = UI and DOLib|Any CPU
		UI and DOLib|x64 = UI and DOLib|x64
		UI and DOLib|x86 = UI and DOLib|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|x64.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Debug|x86.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|x64.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|x64.Build.0 = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|x86.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.Release|x86.Build.0 = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|Any CPU.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|Any CPU.Build.0 = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|x64.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|x64.Build.0 = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|x86.ActiveCfg = Release|Any CPU
		{DD5F841F-8298-44B7-9768-F9387FF16964}.UI and DOLib|x86.Build.0 = Release|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x64.Build.0 = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x64.ActiveCfg = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x64.Build.0 = Debug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x86.ActiveCfg = Debug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Debug|x86.Build.0 = Debug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|Any CPU.ActiveCfg = JJCDebug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|Any CPU.Build.0 = JJCDebug|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x64.ActiveCfg = JJCDebug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x64.Build.0 = JJCDebug|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x86.ActiveCfg = JJCDebug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.JJCDebug|x86.Build.0 = JJCDebug|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x64.ActiveCfg = Release|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x64.Build.0 = Release|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x86.ActiveCfg = Release|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.Release|x86.Build.0 = Release|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x64.ActiveCfg = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x64.Build.0 = DatabaseMigration|x64
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x86.ActiveCfg = DatabaseMigration|x86
		{1CEF15EC-3B72-4AD4-8A44-1D64192AB1D1}.UI and DOLib|x86.Build.0 = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x64.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x64.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x86.ActiveCfg = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.DatabaseMigration|x86.Build.0 = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x64.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x64.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x86.ActiveCfg = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Debug|x86.Build.0 = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x86.ActiveCfg = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.JJCDebug|x86.Build.0 = Debug|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|Any CPU.Build.0 = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x64.ActiveCfg = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x64.Build.0 = Release|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x86.ActiveCfg = Release|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.Release|x86.Build.0 = Release|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|Any CPU.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|Any CPU.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x64.ActiveCfg = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x64.Build.0 = DatabaseMigration|Any CPU
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x86.ActiveCfg = DatabaseMigration|x86
		{06C12653-799B-4345-BD29-D83ED6BB6D44}.UI and DOLib|x86.Build.0 = DatabaseMigration|x86
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.DatabaseMigration|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Debug|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|Any CPU.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x64.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x64.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x86.ActiveCfg = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.JJCDebug|x86.Build.0 = Debug|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x64.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x64.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x86.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.Release|x86.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|Any CPU.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|Any CPU.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x64.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x64.Build.0 = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x86.ActiveCfg = Release|Any CPU
		{58DAAC63-8388-4B89-B876-1270309143BD}.UI and DOLib|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CB6E1DC6-0A34-4835-A324-891E50DDAC9B}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 5
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfs.paragon.mckesson.com:8080/tfs/collection1-eis
		SccLocalPath0 = .
		SccProjectUniqueName1 = ..\\ENChartCAC\\ENChartCAC\\ENChartCAC.vbproj
		SccProjectName1 = ../ENChartCAC/ENChartCAC
		SccLocalPath1 = ..\\ENChartCAC\\ENChartCAC
		SccProjectUniqueName2 = ..\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj
		SccProjectName2 = ../ENChartCAC/EnchartDOLib
		SccLocalPath2 = ..\\ENChartCAC\\EnchartDOLib
		SccProjectUniqueName3 = 2018ConfigUpdater.vbproj
		SccLocalPath3 = .
		SccProjectUniqueName4 = ..\\ENChartCAC\\MICMiscUtilCSharp\\MICMiscUtilCSharp.csproj
		SccProjectName4 = ../ENChartCAC/MICMiscUtilCSharp
		SccLocalPath4 = ..\\ENChartCAC\\MICMiscUtilCSharp
	EndGlobalSection
EndGlobal

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class UtilCreateChartsForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnCreateCharts = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.teVisitIDBase = New DevExpress.XtraEditors.TextEdit()
        Me.teVisitIDStartNumber = New DevExpress.XtraEditors.TextEdit()
        Me.lblMsg = New DevExpress.XtraEditors.LabelControl()
        Me.teChartsToCreate = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.ceSendToRE = New DevExpress.XtraEditors.CheckEdit()
        Me.teVisitIDsToUse = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.teVisitIDBase.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.teVisitIDStartNumber.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.teChartsToCreate.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.ceSendToRE.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.teVisitIDsToUse.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'btnCreateCharts
        '
        Me.btnCreateCharts.Location = New System.Drawing.Point(45, 265)
        Me.btnCreateCharts.Name = "btnCreateCharts"
        Me.btnCreateCharts.Size = New System.Drawing.Size(99, 23)
        Me.btnCreateCharts.TabIndex = 0
        Me.btnCreateCharts.Text = "Create Charts"
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(191, 265)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 1
        Me.btnCancel.Text = "Cancel"
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(44, 28)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(88, 13)
        Me.LabelControl1.TabIndex = 2
        Me.LabelControl1.Text = "Base Visit ID Value"
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(166, 28)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(64, 13)
        Me.LabelControl2.TabIndex = 3
        Me.LabelControl2.Text = "Start Number"
        '
        'teVisitIDBase
        '
        Me.teVisitIDBase.EditValue = "AutoGen_"
        Me.teVisitIDBase.Location = New System.Drawing.Point(44, 47)
        Me.teVisitIDBase.Name = "teVisitIDBase"
        Me.teVisitIDBase.Size = New System.Drawing.Size(100, 20)
        Me.teVisitIDBase.TabIndex = 4
        '
        'teVisitIDStartNumber
        '
        Me.teVisitIDStartNumber.EditValue = "1"
        Me.teVisitIDStartNumber.Location = New System.Drawing.Point(166, 47)
        Me.teVisitIDStartNumber.Name = "teVisitIDStartNumber"
        Me.teVisitIDStartNumber.Size = New System.Drawing.Size(100, 20)
        Me.teVisitIDStartNumber.TabIndex = 5
        '
        'lblMsg
        '
        Me.lblMsg.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.lblMsg.Location = New System.Drawing.Point(44, 100)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(371, 13)
        Me.lblMsg.TabIndex = 6
        Me.lblMsg.Text = "..."
        '
        'teChartsToCreate
        '
        Me.teChartsToCreate.EditValue = "10"
        Me.teChartsToCreate.Location = New System.Drawing.Point(285, 47)
        Me.teChartsToCreate.Name = "teChartsToCreate"
        Me.teChartsToCreate.Size = New System.Drawing.Size(130, 20)
        Me.teChartsToCreate.TabIndex = 8
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(285, 28)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(130, 13)
        Me.LabelControl4.TabIndex = 7
        Me.LabelControl4.Text = "Number of charts to create"
        '
        'ceSendToRE
        '
        Me.ceSendToRE.EditValue = true
        Me.ceSendToRE.Location = New System.Drawing.Point(45, 225)
        Me.ceSendToRE.Name = "ceSendToRE"
        Me.ceSendToRE.Properties.Caption = "Send To Rules Engine"
        Me.ceSendToRE.Size = New System.Drawing.Size(221, 19)
        Me.ceSendToRE.TabIndex = 9
        '
        'teVisitIDsToUse
        '
        Me.teVisitIDsToUse.EditValue = ""
        Me.teVisitIDsToUse.Enabled = false
        Me.teVisitIDsToUse.Location = New System.Drawing.Point(44, 144)
        Me.teVisitIDsToUse.Name = "teVisitIDsToUse"
        Me.teVisitIDsToUse.Size = New System.Drawing.Size(371, 20)
        Me.teVisitIDsToUse.TabIndex = 10
        '
        'LabelControl3
        '
        Me.LabelControl3.Enabled = false
        Me.LabelControl3.Location = New System.Drawing.Point(44, 125)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(216, 13)
        Me.LabelControl3.TabIndex = 11
        Me.LabelControl3.Text = "Enter VisitID's to Use (seperated by commas)"
        '
        'UtilCreateChartsForm
        '
        Me.CancelButton = Me.btnCancel
        Me.ClientSize = New System.Drawing.Size(466, 323)
        Me.Controls.Add(Me.LabelControl3)
        Me.Controls.Add(Me.teVisitIDsToUse)
        Me.Controls.Add(Me.ceSendToRE)
        Me.Controls.Add(Me.teChartsToCreate)
        Me.Controls.Add(Me.LabelControl4)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.teVisitIDStartNumber)
        Me.Controls.Add(Me.teVisitIDBase)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnCreateCharts)
        Me.Name = "UtilCreateChartsForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "UtilCreateChartsForm"
        CType(Me.teVisitIDBase.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.teVisitIDStartNumber.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.teChartsToCreate.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.ceSendToRE.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.teVisitIDsToUse.Properties,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)
        Me.PerformLayout

End Sub
    Friend WithEvents btnCreateCharts As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents teVisitIDBase As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teVisitIDStartNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblMsg As DevExpress.XtraEditors.LabelControl
    Friend WithEvents teChartsToCreate As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceSendToRE As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents teVisitIDsToUse As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
End Class

﻿Imports System.IO
Imports System.Linq
Imports System.Threading.Tasks
Imports DevExpress.Xpo
Imports EnchartDOLib

Public Class CreateNewConfigInstanceForAllFacilitiesForm
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        CreateNewCiForAllFacilities()
    End Sub

    Private Sub UpdateProgress(info As ProgressUpdateInfo)
        Dim perecentDone As Int16 = info.Progress
        If info.Progress >= 100 Then
            info.Progress = 100
        End If

        ProgressBar1.Value = info.Progress
        ProgressMsgLabel.Text = info.Message
    End Sub

    Private Sub ResetProgres(Optional msg As String = "")
        ProgressBar1.Value = 0
    End Sub

    Private Async Sub CreateNewCiForAllFacilities()
        Panel1.Enabled = False
        Button1.Enabled = False
        CloseButton.Enabled = False

        Dim progressIndicator As New Progress(Of ProgressUpdateInfo)(AddressOf UpdateProgress)

        For Each facility As DOFacility In GetFacilities()
            Try
                Dim newCi As DOConfigInstance = Nothing
                FaciltiyDescriptionLabel.Text = facility.LongName
                ResetProgres("Starting Up ...")

                Dim duh = Await Task.Run(Function() newCi = DupData(facility, progressIndicator))

            Catch ex As Exception
                MsgBox($"CreateNewCIForAllFacilities -  Error : {ex.Message}")
            End Try
        Next

        Panel1.Enabled = True
        Button1.Enabled = True
        CloseButton.Enabled = True
    End Sub


    Function DupData(facility As DOFacility, f As IProgress(Of ProgressUpdateInfo)) As DOConfigInstance

        Dim lNewConfigInstance As New DOConfigInstance
        Dim lConfigInstanceToEdit = facility.ConfigInstanceVersion 'pointer to source config that will be copied or edited.
        Dim progress As Double = 0

        Dim pstep As Short = 5
        Try
            f.Report(New ProgressUpdateInfo("Getting Busy", progress))
            f.Report(New ProgressUpdateInfo("Copying ComboBoxLists", progress))
            Dim count = lConfigInstanceToEdit.ComboBoxLists.Count
            Dim step1 = 70 / count
            For Each cboList As DOConfigComboBoxList In lConfigInstanceToEdit.ComboBoxLists
                Dim lNewCboList As New DOConfigComboBoxList(cboList)
                lNewCboList.ConfigInstance = lNewConfigInstance
                lNewCboList.Save()
                lNewConfigInstance.ComboBoxLists.Add(lNewCboList)
                progress += step1
                f.Report(New ProgressUpdateInfo("Copying ComboBoxLists", progress))
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying ConfigSettings", progress))
            For Each lConfigGroup As DOConfigGroup In lConfigInstanceToEdit.ConfigSettingsGroups
                Dim lNewConfigSetting As New DOConfigGroup(lConfigGroup)
                lNewConfigSetting.ConfigInstance = lNewConfigInstance
                lNewConfigSetting.Save()
                lNewConfigInstance.ConfigSettingsGroups.Add(lNewConfigSetting)
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying ControlOverrides", progress))
            For Each lConfigGroup As DOControlConfigGroup In lConfigInstanceToEdit.ControlOverrides
                Dim lNewControlConfig As New DOControlConfigGroup(lConfigGroup)
                lNewControlConfig.ConfigInstance = lNewConfigInstance
                lNewControlConfig.Save()
                lNewConfigInstance.ControlOverrides.Add(lNewControlConfig)
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying CodingReportTexts", progress))
            For Each codingReportText As DOCodingReportText In lConfigInstanceToEdit.CodingReportTexts
                Dim lNewDoCodingReportText As New DOCodingReportText(codingReportText)
                lNewDoCodingReportText.ConfigInstance = lNewConfigInstance
                lNewDoCodingReportText.Save()
                lNewConfigInstance.CodingReportTexts.Add(lNewDoCodingReportText)
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying PhysicianCodesFilter", progress))
            For Each physicianCodeFilter As DOPhysicianCodesFilter In lConfigInstanceToEdit.PhysicianCodesFilter
                Dim lnewcode As New DOPhysicianCodesFilter(physicianCodeFilter)
                lnewcode.ConfigInstance = lNewConfigInstance
                lnewcode.Save()
                lNewConfigInstance.PhysicianCodesFilter.Add(lnewcode)
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying Medications", progress))
            For Each medication As DOMedication In lConfigInstanceToEdit.Medications
                Dim m As New DOMedication(medication)
                m.ConfigInstance = lNewConfigInstance
                m.Save()
                lNewConfigInstance.Medications.Add(m)
            Next

            progress += pstep
            f.Report(New ProgressUpdateInfo("Copying SupplyCatMap", progress))
            For Each SupplyCategoryMap In lConfigInstanceToEdit.SupplyCatMap
                Dim s As New DOSupplyCatMap(SupplyCategoryMap)
                s.ConfigInstance = lNewConfigInstance
                s.Save()
                lNewConfigInstance.SupplyCatMap.Add(s)
            Next

            f.Report(New ProgressUpdateInfo("Copying DeductedTimes", progress))
            For Each deductedTime In lConfigInstanceToEdit.DeductedTimes
                Dim d As New DODeductedTimes(deductedTime)
                d.ConfigInstance = lNewConfigInstance
                d.Save()
                lNewConfigInstance.DeductedTimes.Add(d)
            Next

            f.Report(New ProgressUpdateInfo("Copying FinancialClassMappings", progress))
            For Each fclassMap In lConfigInstanceToEdit.FinancialClassMappings
                Dim newFinClassMap As New DOFinancialClassMapping(fclassMap)
                newFinClassMap.ConfigInstance = lNewConfigInstance
                newFinClassMap.Save()
                lNewConfigInstance.FinancialClassMappings.Add(newFinClassMap)
            Next

            f.Report(New ProgressUpdateInfo("Doing Something Important ...", progress))
            Threading.Thread.Sleep(500)
            f.Report(New ProgressUpdateInfo("CI Created Successfully", 100))
            'Threading.Thread.Sleep(250)
            'Task.Delay(250)

            lNewConfigInstance.ActivationDate = deActivationDate.EditValue
            lNewConfigInstance.MajorChangeDate = deActivationDate.EditValue
            lNewConfigInstance.MajorChange = True
            lNewConfigInstance.Comments = teComments.Text
            lNewConfigInstance.ActiveFormClassName = cbeFormClassaName.EditValue
            lNewConfigInstance.Facility = facility
            lNewConfigInstance.Save()

            facility.PendingConfigInstanceVersion = lNewConfigInstance
            facility.HasPendingConfig = True
            facility.Save()

        Finally

            'If f IsNot Nothing Then
            '    f.Close()
            '    f.Dispose()
            'End If
        End Try

        Return lNewConfigInstance
    End Function


    Private Function GetFacilities() As IEnumerable(Of DOFacility)
        Return (From fac In New XPQuery(Of DOFacility)(XpoDefault.Session)
                Select fac).ToList()
    End Function


    Protected NewConfigInstance As DOConfigInstance


    Public Class ProgressUpdateInfo
        Public Sub New(message As String, progress As Int16)
            Me.Progress = progress
            Me.Message = message
        End Sub

        Public Progress As Int16
        Public Message As String
    End Class

    Private Sub CloseButton_Click(sender As Object, e As EventArgs) Handles CloseButton.Click
        Me.Close()
    End Sub

    Private Sub LoadConfigData(ByVal fileName As String)
        Const delimiterChar As Char = ","c
        Dim reader As New StreamReader(fileName)

        Try
            While Not reader.EndOfStream
                Dim line As String = reader.ReadLine()
                Dim fields() As String = line.Split(delimiterChar)

                Select Case fields(0)
                    Case "FormClassName"
                        cbeFormClassaName.Text = fields(1)
                    Case "ActivationDate"
                        Dim dt As New DateTimePicker
                        dt.Text = fields(1)
                        deActivationDate.DateTime = dt.Value
                        'deMajorChange.DateTime = dt.Value
                    Case "Comment"
                        teComments.Text = fields(1)
                End Select

            End While


        Catch ex As Exception
            MessageBox.Show(ex.Message)
        Finally
            reader.Close()
        End Try

    End Sub

    Private Sub LoadCiInfoButton_Click(sender As Object, e As EventArgs) Handles LoadCiInfoButton.Click
        Dim pickAFile As New OpenFileDialog

        pickAFile.Title = $"Please Select a File"
        pickAFile.InitialDirectory = Application.ExecutablePath '"C:"
        pickAFile.FileName = "ImportConfig.dat"
        pickAFile.Filter = "Data Files(*.dat)|*.dat"
        pickAFile.ShowDialog()

        LoadConfigData(pickAFile.FileName)

        pickAFile.Dispose()
    End Sub

    Private Sub CreateNewConfigInstanceForAllFacilitiesForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'this is just an attempt to load what we think might be the default config ...
        'if it doesn't work ... no big deal ... shhhh
        Try
            Dim defaultPendingConfigFile = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "ImportConfig.dat")
            If File.Exists(defaultPendingConfigFile) Then
                LoadConfigData(defaultPendingConfigFile)
            End If
        Catch ex As Exception

        End Try

    End Sub
End Class

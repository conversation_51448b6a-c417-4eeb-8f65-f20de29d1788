﻿Option Infer On

Imports System.IO
Imports System.Reflection
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Imports System.Xml
Imports DevExpress.Xpo
Imports DevExpress.XtraEditors
Imports ENChartCAC
Imports EnchartDOLib
Imports MICMiscUtilCSharp

Public Class Form1
    Private Sub Button1_Click(sender As Object, e As EventArgs)

    End Sub


    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ProgressPanel1.Visible = True
        ConnectToECDataBase()

        Application.DoEvents()

        LabelControl1.Text = GetConnectionString()
        Application.DoEvents()
        ProgressPanel1.Visible = False
    End Sub

    Private Async Sub Process()
        lblMsg.Text = ""
        '   Await Task.Delay(1000)

        ProgressPanel1.Visible = True

        Session.DefaultSession?.UpdateSchema()

        ' DeleteExtraCdms()
        'If ceAddMissingEspcodes.Checked Then
        '    CreateMissingEspcodes()
        'End If

        '  test()
        'Exit Sub

        SetupGlobalConfigOptions()
        Application.DoEvents()

        If ceUpdateDbScripts.Checked Then
            UpdateOvernightReports()
            Application.DoEvents()
        End If

        If ceFetalMon.Checked Then
            UpdateFetalStressTestDescription()
            Application.DoEvents()
        End If

        If ceConvertProviderLists.Checked Then
            ConvertProviderLists()
            Application.DoEvents()
        End If

        If ceAddReports.Checked Then
            LoadReports()
            Application.DoEvents()
        End If

        If ceAddBlankList.Checked Then
            CreateExceptionOverrideListForAllFacilities()
            Application.DoEvents()
        End If

        If ceObsCarveOutTimes.Checked Then
            MigrateObsCarveOutTimes()
            Application.DoEvents()
        End If

        If ceNcciEdits.Checked Then
            Await LoadNcciEdits()
            Application.DoEvents()
        End If

        If ceCodeMigration.Checked Then
            progressBar.Visible = True
            Await ConvertEspcodesTo2018()
            ProgressPanel1.Visible = False
            Application.DoEvents()
        End If

        If ceGTubeCodes.Checked Then
            GTubeDbRecords()
            Application.DoEvents()
        End If

        If ceAddMissingEspcodes.Checked Then
            CreateMissingEspcodes()
        End If

        ProgressPanel1.Visible = False
        lblMsg.Text = "Done Processing"
        ' MessageBox.Show("Done", "Status", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub CreateMissingEspcodes()
        Dim count = 0
        Try
            For Each espcode In GetMissingEspcodes()
                espcode.HCPCS = espcode.HCPCS + "{{QTY}}"
                espcode.Flag = "2018 Add Missing Codes"
                espcode.OrcaTab = "Infusions"
                espcode.Special = 1
                espcode.Points = 0
                If GetEspCode(espcode.ESPVALUE) = False Then
                    espcode.Save()
                    count += 1
                End If
            Next
            addMissingEspcodeslbl.Text = $"{count} Missing ESP Records Added"
        Catch ex As Exception
            addMissingEspcodeslbl.Text = "ERROR"
            Return
        End Try
    End Sub

    Sub test()
        Dim qcodes As New List(Of String)
        Using uow As New UnitOfWork
            Dim esps = (From esp In New XPQuery(Of DOESPCode2018)(uow)
                        Where esp.HCPCS IsNot Nothing
                        Select esp)

            Dim dlist = esps.ToList

            Dim regx = New Regex("x[0-9]{1,3}$")
            For Each esp In esps.ToList()
                ' If regx.IsMatch(esp.HCPCS) Then
                If esp.HCPCS.EndsWith("{{QTY}}") Then

                    '  Debug.WriteLine($"{esp.HCPCS}")
                    'Dim re As Regex = New Regex("x[0-9]{1,3}$")
                    'Dim hcpcsParts As String() = re.Split(esp.HCPCS)
                    'Dim hcpcsLookup As String = hcpcsParts(0)

                    Dim hcpcsLookup As String = esp.HCPCS.Replace("{{QTY}}", "")

                    If GetCdmCode(hcpcsLookup) = False Then
                        '     Debug.WriteLine($"{hcpcsLookup} :no chargemaster found")
                        If qcodes.Contains(hcpcsLookup) = False Then
                            qcodes.Add(hcpcsLookup)
                        End If
                    Else
                        '     Debug.WriteLine($"{hcpcsLookup} :fouund")

                    End If
                Else
                    '    Debug.WriteLine($"Nope ------------- {esp.HCPCS}")

                End If
            Next
        End Using

        For Each code In qcodes
            Debug.WriteLine(code)
        Next
    End Sub

    Private Function GetEspCode(cdmcode As String) As Boolean
        Dim code = (From esp In New XPQuery(Of DOESPCode2018)(XpoDefault.Session)
                    Where esp.ESPVALUE = cdmcode And esp.Facility Is Nothing
                    Select esp).FirstOrDefault()

        If code Is Nothing Then Return False
        Return True
    End Function

    Private Function GetCdmCode(cdmcode As String) As Boolean
        Dim code = (From cdm In New XPQuery(Of DOChargeMaster)(XpoDefault.Session)
                    Where cdm.HCPCS = cdmcode
                    Select cdm).FirstOrDefault()

        If code Is Nothing Then Return False
        Return True
    End Function

    Private Function GetMissingEspcodes() As List(Of DOESPCode2018)
        Dim mel As New List(Of DOESPCode2018) From {
            NewEspCode("96415", "IV_Medication_Add_Hours_Chemo_VS1x01_11_mod59", "IV Administration - Chemotherapy - Additional Hours"),
            NewEspCode("96549", "IV_Medication_Concurrent_Chemo_VS1x01_11", "IV Administration - Chemotherapy - Concurrent Medication"),
            NewEspCode("96549", "IV_Medication_Concurrent_Chemo_VS2_mod59x01_11", "IV Administration - Chemotherapy - Concurrent Medication"),
            NewEspCode("96549", "IV_Medication_Concurrent_Chemo_VS2x01_11", "IV Administration - Chemotherapy - Concurrent Medication"),
            NewEspCode("96549", "IV_Medication_Concurrent_Chemo_VS3_mod59x01_11", "IV Administration - Chemotherapy - Concurrent Medication"),
            NewEspCode("96549", "IV_Medication_Concurrent_Chemo_VS3x01_11", "IV Administration - Chemotherapy - Concurrent Medication"),
            NewEspCode("96413", "IV_Medication_Init_Hour_Chemo_VS1x01_11", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-59", "IV_Medication_Init_Hour_Chemo_VS1x01_11_mod59", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-XU", "IV_Medication_Init_Hour_Chemo_VS1x01_11_modXU", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-59", "IV_Medication_Init_Hour_Chemo_VS2x01_11_mod59", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-XU", "IV_Medication_Init_Hour_Chemo_VS2x01_11_modXU", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-59", "IV_Medication_Init_Hour_Chemo_VS3x01_11_mod59", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode("96413-XU", "IV_Medication_Init_Hour_Chemo_VS3x01_11_modXU", "IV Administration - Chemotherapy - Initial hour"),
            NewEspCode(96417, "IV_Medication_Sequential_Chemo_VS1x01_11", "IV administration - Chemotherapy - Additional Sequential Medication"),
            NewEspCode("96417-59", "IV_Medication_Sequential_Chemo_VS2_mod59x01_11", "IV Administration - Chemotherapy - Additional Sequential Medication"),
            NewEspCode("96417-XU", "IV_Medication_Sequential_Chemo_VS2_modXUx01_11", "IV Administration - Chemotherapy - Additional Sequential Medication"),
            NewEspCode("96417-59", "IV_Medication_Sequential_Chemo_VS3_mod59x01_11", "IV Administration - Chemotherapy - Additional Sequential Medication"),
            NewEspCode("96417-XU", "IV_Medication_Sequential_Chemo_VS3_modXUx01_11", "IV Administration - Chemotherapy - Additional Sequential Medication"),
            NewEspCode(96409, "Route_IV_Push_Initial_Chemo_VS1x01_11", "IV Push - Chemotherapy - Initial"),
            NewEspCode("96409-59", "Route_IV_Push_Initial_Chemo_VS1x01_11_mod59", "IV Push - Chemotherapy - Initial"),
            NewEspCode("96409-XU", "Route_IV_Push_Initial_Chemo_VS1x01_11_modXU", "IV Push - Chemotherapy - Initial"),
            NewEspCode(96411, "Route_IV_Push_Sequential_Chemo_VS1x01_11", "IV Push - Chemotherapy - Additional"),
            NewEspCode(96411, "Route_IV_Push_Sequential_Chemo_VS1x01_11_mod59", "IV Push - Chemotherapy - Additional")
        }

        Return mel
    End Function

    Private Function NewEspCode(hcpcs As String, espCode As String, longName As String) As DOESPCode2018
        Return New DOESPCode2018() With {
            .hcpcs = hcpcs,
            .ESPVALUE = espCode,
            .longName = longName
            }
    End Function

    Private Sub DeleteExtraCdms()
        Dim count As Integer = 0
        Using uow As New UnitOfWork
            Dim cdms = (From cdm In New XPQuery(Of DOChargeMaster)(uow)
                        Select cdm)
            Dim regx = New Regex("x[0-9]{1,3}$")
            For Each cdmRecord In cdms.ToList()
                If regx.IsMatch(cdmRecord.HCPCS) Then
                    count += 1
                    cdmRecord.Delete()
                End If
            Next

            uow.CommitChanges()
        End Using
        MessageBox.Show($"Deleted {count} cdm records")

    End Sub

    Private Sub GTubeDbRecords()
        Dim espcodesAdded As Int16 = 0
        Dim cdmsAdded As Int16 = 0
        Try
            GTubeCodeslbl.Text = "Thinking about it ..."
            GTubeCodeslbl.Refresh()

            Dim espcodesToUpdate = (From esp In New XPQuery(Of DOESPCode2018)(XpoDefault.Session)
                                    Where esp.ESPVALUE = "GIGU_Replace_Gas_Tube" Or esp.ESPVALUE = "PhysCharge_43760"
                                    Select esp)


            For Each espcode In espcodesToUpdate
                Dim newEspcode = CloneEspcode(espcode)

                If newEspcode.HCPCS = "43760" Then
                    If newEspcode.ESPVALUE = "PhysCharge_43760" Then
                        newEspcode.ESPVALUE = "PhysCharge_43762"
                    End If
                    newEspcode.HCPCS = "43762"
                    newEspcode.ActiveFrom = New DateTime(2019, 1, 1) ' New DateTime(2018, 10, 23)
                    If GTubeEspcodeExists(newEspcode) Then
                        Continue For
                    End If
                    newEspcode.Save()
                    espcodesAdded += 1
                End If
            Next

            Dim chargeMasterRecords = (From cdm In New XPQuery(Of DOChargeMaster)(XpoDefault.Session)
                                       Where cdm.HCPCS = "43760"
                                       Select cdm)

            For Each cdmRecord In chargeMasterRecords
                If GtubeAlreadyExists(cdmRecord) Then
                    Continue For
                End If
                Dim newCdm = CloneCdm(cdmRecord)
                newCdm.HCPCS = "43762"
                newCdm.Save()
                cdmsAdded += 1
            Next
        Catch ex As Exception
            GTubeCodeslbl.Text = "ERROR"
            MessageBox.Show($"{ex.Message}", "Add GTube DB Records", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End Try
        ceGTubeCodes.Checked = True
        GTubeCodeslbl.Text = $"COMPLETED - Added {espcodesAdded} DOEspcode2018 records and {cdmsAdded} DOChargeMaster records"
    End Sub

    Private Function GtubeAlreadyExists(espcode As DOChargeMaster) As Boolean
        Dim cdmRec = (From cdm In New XPQuery(Of DOChargeMaster)(XpoDefault.Session)
                      Where cdm.HCPCS = "43762" And cdm.Facility = espcode.Facility And cdm.TreatmentArea = espcode.TreatmentArea
                      Select cdm).FirstOrDefault()

        If cdmRec IsNot Nothing Then Return True
        Return False
    End Function

    Private Function GTubeEspcodeExists(espcode As DOESPCode2018) As Boolean
        Dim esprec As DOESPCode2018
        If espcode.Facility.HasValue = False Then
            esprec = (From esp In New XPQuery(Of DOESPCode2018)(XpoDefault.Session)
                      Where esp.ESPVALUE = espcode.ESPVALUE And esp.HCPCS = "43762" And esp.Facility Is Nothing And esp.ActiveFrom = espcode.ActiveFrom
                      Select esp).FirstOrDefault()
        Else
            esprec = (From esp In New XPQuery(Of DOESPCode2018)(XpoDefault.Session)
                      Where esp.ESPVALUE = espcode.ESPVALUE And esp.HCPCS = "43762" And esp.Facility = espcode.Facility And esp.ActiveFrom = espcode.ActiveFrom
                      Select esp).FirstOrDefault()
        End If

        If esprec IsNot Nothing Then Return True
        Return False
    End Function

    Private Function CloneEspcode(source As DOESPCode2018) As DOESPCode2018
        Dim newEspocode As New DOESPCode2018() With {
        .ESPVALUE = source.ESPVALUE,
        .LongName = source.LongName,
        .Points = source.Points,
        .HCPCS = source.HCPCS,
        .Special = source.Special,
        .Flag = source.Flag,
        .ESP_Policy = source.ESP_Policy,
        .ReportDisplayOrder = source.ReportDisplayOrder,
        .OrcaTab = source.OrcaTab,
        .Facility = source.Facility
        }
        Return newEspocode
    End Function

    Private Function CloneCdm(chargemasterRecord As DOChargeMaster) As DOChargeMaster
        Dim newCdm As New DOChargeMaster() With
        {
            .HCPCS = chargemasterRecord.HCPCS,
            .CDM = chargemasterRecord.CDM,
            .LongName = chargemasterRecord.LongName,
            .Quantity = chargemasterRecord.Quantity,
            .PhysicianCDM = chargemasterRecord.PhysicianCDM,
            .Facility = chargemasterRecord.Facility,
            .TreatmentArea = chargemasterRecord.TreatmentArea
        }
        Return newCdm
    End Function

    Private Sub MigrateObsCarveOutTimes()
        ObsCarveOutTimeslbl.Text = "...IN PROGRESS"
        ObsCarveOutTimeslbl.Refresh()
        'get the cbo lists mapped to the carveouts.
        Dim strBlder As New StringBuilder()
        For Each facility In DOFacility.GetAllFacilitesAsList()
            Try
                Dim carvoutList = facility.ConfigInstanceVersion.DeductedTimes()
                For Each cboList In facility.ConfigInstanceVersion.ComboBoxLists
                    If IsCarveOutCboList(cboList) Then
                        AddOrUpdateCarveOutList(carvoutList.ToList(), cboList)
                        cboList.Enabled = False
                        cboList.Save()
                    End If
                Next
                If strBlder.Length > 0 Then
                    strBlder.Append(",")
                End If
                strBlder.Append(facility.Oid)
            Catch ex As Exception
                addBlankListlbl.Text = "ERROR"
                Return
            End Try
        Next
        ObsCarveOutTimeslbl.Text = $"Completed - Please Verify (facities updated: {strBlder.ToString})"
        ceObsCarveOutTimes.Checked = True
    End Sub

    Private Sub AddOrUpdateCarveOutList(carvoutList As List(Of DODeductedTimes), cboList As DOConfigComboBoxList)
        Dim bIsOOList = IsOOCarveOutList(cboList)

        For Each procedureName In cboList.ListItems
            Dim carveOut = GetCarveOutRecord(carvoutList, procedureName.ItemDisplayName)
            carveOut.ConfigInstance = cboList.ConfigInstance

            If bIsOOList Then
                carveOut.TimeOutsideOfObservation = True
            Else
                carveOut.SeparateBillableProcedures = True
            End If
            carveOut.Save()
        Next

    End Sub

    Private Function GetCarveOutRecord(carvoutList As List(Of DODeductedTimes), procedureName As String) As DODeductedTimes
        For Each deductedTimeRecord In carvoutList
            If deductedTimeRecord.Procedure.ToUpper = procedureName.ToUpper Then
                Return deductedTimeRecord
            End If
        Next

        Dim ddtRecord As New DODeductedTimes()
        ddtRecord.Procedure = procedureName
        ddtRecord.Enabled = True
        Return ddtRecord
    End Function

    Private Function IsCarveOutCboList(cboList As DOConfigComboBoxList) As Boolean

        If IsOOCarveOutList(cboList) Then
            Return True
        End If

        If IsBillableProcCarveOutList(cboList) Then
            Return True
        End If

        Return False
    End Function

    Private Function IsOOCarveOutList(cbolist As DOConfigComboBoxList) As Boolean
        For Each itemControl In cbolist.ComboBoxes
            If itemControl.ComboBoxName.ToUpper = "ObsTimesObsTobProc01_cbo".ToUpper Then
                Return True
            End If
        Next

        Return False
    End Function


    Private Function IsBillableProcCarveOutList(cbolist As DOConfigComboBoxList) As Boolean
        For Each itemControl In cbolist.ComboBoxes
            If itemControl.ComboBoxName.ToUpper = "ObsTimesObsSbpProc01_cbo".ToUpper Then
                Return True
            End If
        Next

        Return False
    End Function

    Private Sub UpdateProgressBar(percentDone As Integer)
        ProgressPanel1.Hide()
        progressBar.Position = percentDone
    End Sub

    Private Async Function ConvertEspcodesTo2018() As Task
        'Dim userResponse = MessageBox.Show("Are you sure you want to migrate espcodes to new DOEspCodes2018 Table?", "EsPCode Migration", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning)
        'If userResponse <> DialogResult.OK Then Exit Sub
        progressBar.Visible = True
        EspcodeMigrationlbl.Text = "...IN PROGRESS"

        'DOESPCode2018.DeleteAllEspcodes()

        Dim progressIndicator As New Progress(Of String)(Sub(msg) lblMsg.Text = msg)
        Dim progressBarIndicator As New Progress(Of Integer)(AddressOf UpdateProgressBar) '(Sub(percentDone) progressBar.Position = percentDone)

        Try
            Dim result = Await Task.Run(Function() MigrateEspocodes(progressIndicator, progressBarIndicator))
            '     DOESPCode2018.DeleteAllEspcodes()
        Catch ex As Exception
            'ECLog.WriteExceptionError("ConvertEspcodesTo2018", ex, True)
            EspcodeMigrationlbl.Text = "ERROR"
            MessageBox.Show(ex?.Message)
            Return
        Finally

        End Try

        EspcodeMigrationlbl.Text = "COMPLETED Successfully"
        ceCodeMigration.Checked = True
        lblMsg.Text = ""
        '  progressBar.Visible = False
    End Function

    Private Function MigrateEspocodes(progressIndicator As IProgress(Of String), progressBarIndicator As IProgress(Of Integer)) As List(Of DOESPCode2018)
        Dim codeMigrationEngine = New ESPCode2018Migrator()
        Dim results = codeMigrationEngine.MigrateEspocodesTo2018VersionsV1(progressIndicator, progressBarIndicator)

        Dim updatedEspCodesList = codeMigrationEngine.TokenizeEspcodes(results)
        progressIndicator.Report("Saving Results To DB ... Please wait") ' .Msg = "Saving Results To DB ... Please wait"
        Try
            DOESPCode2018.DeleteAllEspcodes()
            '  DbPollingTimer.Stop()
            XpoDefault.Session.Save(updatedEspCodesList)

        Finally
            '      DbPollingTimer.Start()
        End Try

        Return updatedEspCodesList
    End Function

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        'ConvertEspcodesTo2018()
        Close()

    End Sub

    Private Sub CreateExceptionOverrideListForAllFacilities()

        addBlankListlbl.Text = "...In PROGRESS"
        addBlankListlbl.Refresh()

        Dim listName = "ExceptionOverrideRequired"
        Dim strBlder As New StringBuilder
        For Each facility In DOFacility.GetAllFacilitesAsList
            Try
                If facility.ConfigInstanceVersion IsNot Nothing Then
                    If facility.ConfigInstanceVersion.GetComboBoxList(listName) Is Nothing Then
                        Dim newList As New DOConfigComboBoxList()
                        newList.ListName = listName
                        newList.ConfigInstance = facility.ConfigInstanceVersion
                        newList.Save()

                    End If
                End If

                If strBlder.Length > 0 Then
                    strBlder.Append(",")
                End If
                strBlder.Append(facility.Oid)
            Catch ex As Exception

            End Try
        Next
        addBlankListlbl.Text = $"Completed - Please Verify (facities updated: {strBlder.ToString})"
        ceAddBlankList.Checked = True
    End Sub

    Private Sub LoadReports()
        Try
            Dim adminReportsDTO As DTOAdminReports
            Dim fileToLoadName = "AddReports - 5.0.xml"

            If Not File.Exists(fileToLoadName) Then
                AddReportslbl.Text = $"SKIPPED ({fileToLoadName} file not found)"
                ceAddReports.Enabled = False
                Return
            End If

            '  Exit Sub
            AddReportslbl.Text = "...In PROGRESS"
            AddReportslbl.Refresh()

            Using fs As New FileStream(fileToLoadName, FileMode.Open)
                Dim xmlSettings As New XmlReaderSettings
                xmlSettings.DtdProcessing = DtdProcessing.Prohibit

                Using reader = XmlReader.Create(fs, xmlSettings)
                    Dim x As New Xml.Serialization.XmlSerializer(GetType(DTOAdminReports))
                    adminReportsDTO = x.Deserialize(reader)
                End Using
            End Using
            Dim strBlder As New StringBuilder

            For Each facility In DOFacility.GetAllFacilitesAsList()
                Try
                    If facility IsNot Nothing Then
                        Dim reportsUnpacker As New AdminReportsUnpacker(facility, adminReportsDTO)
                        reportsUnpacker.Unpack()

                        If strBlder.Length > 0 Then
                            strBlder.Append(",")
                        End If
                        strBlder.Append(facility.Oid)
                    End If
                Catch ex As Exception

                End Try
            Next
            ceAddReports.Checked = True
            AddReportslbl.Text = $"Completed - Please Verify (facities updated: {strBlder.ToString})"
        Catch ex As Exception
            AddReportslbl.Text = "Error"
        End Try
    End Sub

    Private tokenSource As CancellationTokenSource

    Private Async Function LoadNcciEdits() As Task
        ProgressPanel1.Visible = False
        If Not File.Exists(ncciEditsFileName) Then
            ncciEditslbl.Text = $"SKIPPED ({ncciEditsFileName} file not found)"
            ceNcciEdits.Enabled = False
            Return
        End If

        Try
            ncciEditslbl.Text = "... Please Wait"
            lblMsg.Text = "Deleting NCCIEdit Records..."
            Application.DoEvents()
            progressBarNcciEdits.Position = 1
            deleteRecs()
        Catch ex As Exception
            MessageBox.Show(ex.Message)
            ncciEditslbl.Text = "Error"
        End Try

        lblMsg.Text = ""
        ncciEditslbl.Text = "...In PROGRESS"
        progressBarNcciEdits.Visible = True

        Dim progressIndicator As New Progress(Of String)(Sub(msg) lblMsg.Text = msg)
        Dim progressBarIndicator As New Progress(Of Integer)(Sub(percentDone) progressBarNcciEdits.Position = percentDone)
        tokenSource = New CancellationTokenSource
        Try
            Dim result = Await Task.Run(Function() CreateDBRecords(progressBarIndicator, True, False, tokenSource.Token))
            ceNcciEdits.Checked = True
        Catch ex As Exception
            ncciEditslbl.Text = "Error"
        End Try
    End Function

    Const ncciEditsFileName = "MicNCCIEdits.csv"
    Public Async Function CreateDBRecords(progress As IProgress(Of Integer), deleteOldFirst As Boolean, mergeData As Boolean, cancelToken As CancellationToken) As Task(Of Boolean)
        Try

            Dim lines As String()

            Dim ffile As New FileInfo(ncciEditsFileName)
            lines = File.ReadAllLines(ffile.FullName)
            Dim lineCount = lines.Length

            Dim drugCodes As New List(Of String)({"96360", "96361", "96365", "96366", "96367", "96368", "96372", "96374", "96375", "96376", "96401", "96402", "96409", "96411", "96413", "96415", "96417"})
            Dim keepCount As Integer = 0
            Dim TotalSoFar As Integer = 0
            Dim uow As New UnitOfWork()
            For Each line In lines
                cancelToken.ThrowIfCancellationRequested()

                keepCount += 1
                TotalSoFar += 1
                Dim percentDone As Integer = (TotalSoFar * 100 / lineCount)
                progress.Report(percentDone)

                If keepCount = 1000 Then
                    keepCount = 0
                    If uow IsNot Nothing Then
                        uow.CommitChanges()
                    End If
                    uow = New UnitOfWork()
                End If

                If line.Contains("Column 1") Then Continue For
                If line = "" Then Continue For
                If line.StartsWith("Procedure") Then Continue For

                Dim IsTabDelemited As Boolean = False
                Dim offset = 0
                Dim columns = Split(line, ",")
                If columns.Length < 5 Then 'we assume we are not loading a comma delimmeted file
                    columns = Split(line, vbTab)
                    IsTabDelemited = True
                End If

                Dim EffectiveDateCol = 3 '2 + offset
                Dim DeletedDateCol = 4   '3 + offset

                If columns(0).Length > 5 Then Continue For
                If String.IsNullOrWhiteSpace(columns(0)) Then Continue For

                Dim codePairRec As DONCCIEdit = Nothing
                If drugCodes.Contains(columns(1)) Then
                    If columns(EffectiveDateCol) = columns(DeletedDateCol) Then
                        Continue For
                    End If
                    If columns.Length >= 6 AndAlso columns(5) = 9 Then
                        Continue For '9 means not allowed
                    End If

                    'If mergeData Then
                    '    codePairRec = (From codePair In New XPQuery(Of DONCCIEdit)(uow)
                    '                   Where codePair.LeftColumn = columns(0) And codePair.RightColumn = columns(1)
                    '                   Select codePair).FirstOrDefault
                    '    If codePairRec IsNot Nothing Then
                    '        codePairRec.Update(columns(0), columns(1), columns(EffectiveDateCol), columns(DeletedDateCol))
                    '    End If
                    'End If

                    If codePairRec Is Nothing Then
                        codePairRec = New DONCCIEdit(uow, columns(0), columns(1), columns(EffectiveDateCol), columns(DeletedDateCol))
                    End If

                    codePairRec.Save()
                End If
            Next

            If uow IsNot Nothing Then
                uow.CommitChanges()
                uow.Dispose()
            End If


        Catch ex As Exception
            MessageBox.Show(ex.Message)
            'ncciEditslbl.Text = "Error"
            Throw
            '     Return False
        End Try

        Return True
    End Function

    Public Sub deleteRecs()
        Dim sw As New Stopwatch
        sw.Start()

        Using uow As New UnitOfWork
            'Dim stuff = New XPCollection(Of DONCCIEdit)(uow)
            'uow.Delete(stuff)
            'uow.CommitChanges()
            'uow.PurgeDeletedObjects()

            uow.DeleteByCriteria(Of DONCCIEdit)(Nothing)
            uow.CommitChanges()

            'uow.ExecuteNonQuery("truncate 'DONcciEdit'")
            'uow.ExecuteNonQuery("DELETE FROM DONCCIEdit")
        End Using
        sw.Stop()
        Debug.WriteLine(sw.ElapsedMilliseconds)

    End Sub

    Private Sub UpdateOvernightReports()
        _assembly = [Assembly].GetExecutingAssembly()
        Try
            Dim cs = EnchartDOLib.GetConnectionString()

            If cs.ToUpper().Contains("ASA17") Then
                UpdateSybaseDbScripts()
            ElseIf cs.ToUpper.Contains("INITIAL CATALOG") Then 'if sql server
                UpdateSqlDbScripts()
            End If
        Catch ex As Exception
            UpdateOvernightScriptslbl.Text = "ERROR"
            Return
        End Try

        ceUpdateDbScripts.Checked = True
        UpdateOvernightScriptslbl.Text = "UPDATED"
    End Sub

    Private Sub UpdateSqlDbScripts()
        Try
            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

            cmd.CommandText = My.Resources.usp_overnight_a
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_b
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_c
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_d
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_rebuild_a
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_rebuild_b
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_rebuild_c
            cmd.ExecuteNonQuery()

            cmd.CommandText = My.Resources.usp_overnight_rebuild_d
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute query {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try
    End Sub

    Dim _assembly As [Assembly]

    Private Sub UpdateSybaseDbScripts()
        Try
            '     Dim dbScript = File.ReadAllText("Overnight.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.Overnight '_assembly.GetManifestResourceStream 'Resources.Overnight_New 'dbScript
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'Overnight.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try

        'Overnight_New.sql

        Try
            ' Dim dbScript = File.ReadAllText("Overnight_New.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.Overnight_New
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'Overnight_New.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try
    End Sub


    Private Sub UpdateFetalStressTestDescription()
        Try
            Dim espcodesToUpdate = (From esp In New XPQuery(Of DOESPCode)(XpoDefault.Session)
                                    Where esp.ESPVALUE = "Mon_Fetal"
                                    Select esp)

            For Each esp In espcodesToUpdate
                esp.LongName = "Fetal Non Stress Test"
                esp.Special = 1
                esp.Save()
            Next

            ceFetalMon.Checked = True
            FetalMonlbl.Text = $"UPDATED {espcodesToUpdate.Count} Records to 'Fetal Non Stress Test'"
        Catch ex As Exception
            FetalMonlbl.Text = $"ERROR"
            MessageBox.Show($"(UpdateFetalStressTestDescription )Error trying to update espcodes. {vbCrLf}{vbCrLf} {ex.Message}")
        End Try
    End Sub



    Private Sub SetupGlobalConfigOptions()
        'MaintenanceModeMessage
        Dim msg = CheckMaintenanceModeMessage()
        If String.IsNullOrEmpty(msg) Then
            msg = "TURNED ON"
        Else
            msg = $"TURNED ON - {msg}"
        End If
        If ceEnableMainenanceMode.Checked Then
            UpdateSetting("MaintenanceModeEnable", ceEnableMainenanceMode, TurnOnMaintenanceModelbl, "True", msg)
        End If

        If ceUse2018ESPCodeLogic.Checked Then
            UpdateSetting("Use2018EspcodeLogic", ceUse2018ESPCodeLogic, Use2018EspcodeLogiclbl)
        End If

        If ceUse2018CodingReport.Checked Then
            UpdateSetting("Use2018CodingReport", ceUse2018CodingReport, Use2018CodingReportLogiclbl)
        End If

        If ceUse2018ChargeAllocationLogic.Checked Then
            UpdateSetting("Use2018ChargeAllocationLogic", ceUse2018ChargeAllocationLogic, Use2018ObsChargeAlloclbl)
        End If

        If ceCreateRealtimeChargesSetting.Checked Then
            UpdateSetting("RealtimeCharges", ceCreateRealtimeChargesSetting, CreateRealTimeSettinglbl, "False", "UPDATED (Set to False)")
        End If
    End Sub
    Public Sub UpdateSetting(settingName As String, checkbox As CheckEdit, label As LabelControl, Optional state As String = "True", Optional result As String = "UPDATED")
        Try
            UpdateGlobalSetting(settingName, state)
            checkbox.Checked = True

            label.Text = result
        Catch ex As Exception
            label.Text = "ERROR"
        End Try

    End Sub

    Private Function CheckMaintenanceModeMessage() As String
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("MaintenanceModeMessage")
        Dim msg As String = setting.SettingValue
        If String.IsNullOrEmpty(msg) Then
            setting.SettingValue = "The IC Server will back up shortly"
            setting.Save()
            Return "MaintenanceModeMessage Setting set to default"
        End If
        Return String.Empty
    End Function

    Private Shared Sub UpdateGlobalSetting(settingName As String, state As String)
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting(settingName)
        setting.SettingValue = state
        setting.Save()
    End Sub

    Private Sub ConvertProviderLists()
        Try
            For Each facility As DOFacility In New XPCollection(GetType(DOFacility))
                'Theoretically, "SharedActiveFormClassName" should be the same for each facility ... but just in case, we'll update it per facility
                DOConfigInstance.SharedActiveFormClassName = facility.ConfigInstanceVersion.ActiveFormClassName 'yuck
                MoveNonCiListsToSharedCi(facility)
            Next

            ceConvertProviderLists.Checked = True
            ceConvertProviderListslbl.Text = "COMPLETED Successfully"
        Catch ex As Exception
            ceConvertProviderListslbl.Text = "ERROR"
            MessageBox.Show(ex?.Message)
            Return
        End Try
    End Sub

    Private Sub MoveNonCiListsToSharedCi(facility As DOFacility)

        For Each cboList In facility.ConfigInstanceVersion.ComboBoxLists
            If IsSharedCboList(cboList) Then
                If facility.SharedConfigInstanceVersion.GetComboBoxList(cboList.ListName) Is Nothing Then
                    cboList.ConfigInstance = facility.SharedConfigInstanceVersion
                    cboList.Save()
                End If
            End If
        Next
    End Sub


    Public Function ConnectToDb() As Boolean
        EnchartDOLib.ConnectToECDataBase()
        Return False
    End Function

    Private Sub btnRun_Click(sender As Object, e As EventArgs)
        Process()
    End Sub

    Private Sub ceNcciEdits_CheckedChanged(sender As Object, e As EventArgs) Handles ceNcciEdits.CheckedChanged
        If ceNcciEdits.Checked Then
            ncciEditslbl.Text = "COMPLETED Successfully"
        End If
    End Sub

    Private Sub btnJustDoIt_Click(sender As Object, e As EventArgs) Handles btnJustDoIt.Click
        Process()
    End Sub

    'Private Sub ceUse2018ESPCodeLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ESPCodeLogic.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018EspcodeLogic")
    '    setting.SettingValue = ceUse2018ESPCodeLogic.Checked.ToString
    '    setting.Save()
    'End Sub

    'Private Sub ceUse2018CodingReport_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018CodingReport.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018CodingReport")
    '    setting.SettingValue = ceUse2018CodingReport.Checked.ToString
    '    setting.Save()
    'End Sub

    'Private Sub ceUse2018ChargeAllocationLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ChargeAllocationLogic.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018ChargeAllocationLogic")
    '    setting.SettingValue = ceUse2018ChargeAllocationLogic.Checked.ToString
    '    setting.Save()
    'End Sub


End Class

﻿**************  (v. 3.0.1.0) 2017-01-27
	* For the Meditech Module, we need to be able to make the Application ID unique. We can do this by adding an option to append the version number
	  to the visit ID
		MeditechColdFeedImageFileHandler.vb
			- Check for a custom setting called AppendVersionNumber. If it's TRUE, then append the version number to the visit ID to be used,
			  as the Application ID. (~lines 67-74)
			  
**************  (v. 3.0.0.0) 2016-03-31
	* US37244: SAP 17 Upgrade - Upgrade ColdFeedExport Project
	* Upgraded solution to .NET 4.0
	* Upgraded DevExpress to v15.2
	
**************  (v. 2.1.1.0) 2015-11-18
	* Reversioning to correspond with MIC release version

**************  (v. 1.0.1.14) 2015-05-27
	* US25157: Database - Server Applications -  Image Feed to support SQL Server
		DefaulGetChartsToExport.vb
			- Don't use the Using contstruct on the connection, it causes errors when DevExpress is connected to SQL Server (~lines 560-572)

**************  (v. 1.0.1.13) 2015-04-10
	* For the Meditech Module, the page count is not exactly optional. MIC needs to send an accurate page count.
		References
			- Added reference to System.Drawing
		MeditechColdFeedImageFileHandler.vb
			- Export each coding summary document to a temporary location (~lines 19-45)
			- Get the page count of the exported file (~lines 73, 160-171)
			- Move the document from the temporary location to the destination folder (~lines 88-102)

**************  (v. 1.0.1.11) 2015-03-04
	* For the Meditech Module, the unit number segment is based on the chart's MRN and is not a static value
		MeditechColdFeedImageFileHandler.vb
			- Segment (2) of the filename is now based on the chart's MRN and not a configuration setting

**************  (v. 1.0.1.10) 2015-02-27
	* US21118: WI4932 - Image Feed - Meditech Module
	* Add a module so that the Image Feed can export a TIFF image with a filename that meets the standards required by Meditech
		MeditechColdFeedImageFileHandler.vb
			- New IExportChargeSummaryImageFile class that will create a TIFF image with an appropriate filename

**************  (v. 1.0.1.9) 2014-08-21
	* US7914: WI 2051 - MIC Image Feed: Configuration to Execute Using a Domain User Account
	* Allow the Cold Feed to optionally impersonate a domain user when exporting coding summaries and index files. I decided to only wrap the specific read/write
	  functionality where impersonation is needed Using the impersonator so that other read/write functionality of the Image Feed (like reading the XML config file)
	  is not affected.
		Impersonation.vb
			- New class that allows the application to impersonate another user
		HPFColdFeedDetailsFileHandler.vb
			- If the "ExportUsingDomainAccount" setting is True, use the configured credentials to write the details file to the output folder (~lines 79-84, 100)
		HPFColdFeedImageAsTextFileHandler.vb
			- If the "ExportUsingDomainAccount" setting is True, use the configured credentials to write the details file to the output folder (~lines 23-28, 45)
		HPFColdFeedImageFileHandler.vb
			- If the "ExportUsingDomainAccount" setting is True, use the configured credentials to write the details file to the output folder (~lines 22-27, 51)

**************  (v. 1.0.1.8) 2013-11-08
	* US5690: WI 1866: MIC Image Feed - Custom Index File Output for OnBase
	* Make updates that are necessary due to the changes of the ChargeSummaryColdFeedHelper project and add a new plug-in that will build an index file
	  in the format required by Wishard - single index for a batch, comma-separated rows with account number and filename
		HPFColdFeedDetailsFileHandler.vb
			- Add import statements for EnchartDOLib and System.Text (~lines 3, 5)
			- Update existing interface implementations to adhere to the new interface requirements, new methods, etc. (~lines 37-39, 101-103, 131-133,
			  146-148)
			- Update the CreateDetail method of HPFColdFeedDetailsFileHandler to use the list construct for mapping definitions (~lines 49, 63-64)
			- Added a new class, SingleDelimitedFileHandler, that implements IExportChargeSummaryDetailsFile and will generate the single-file-per-batch,
			  comma-separated file required by Wishard (~lines 151-269)
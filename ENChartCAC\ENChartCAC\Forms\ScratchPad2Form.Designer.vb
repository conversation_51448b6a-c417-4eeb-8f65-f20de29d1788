﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ScratchPad2Form
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ClinicProcIcdxGrp = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.ClinicProcIcdxAutoPopBtn = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel01 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel07 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel02 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel06 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel05 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel04 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdxLabel03 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcIcdx07 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx05 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx04 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx03 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx02 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcIcdx01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcTestsGrp = New DevExpress.XtraEditors.GroupControl()
        Me.ClinicProcTestsSc = New DevExpress.XtraEditors.XtraScrollableControl()
        Me.ClinicProcLabsGrp = New DevExpress.XtraEditors.GroupControl()
        Me.ClinicProcLabsSc = New DevExpress.XtraEditors.XtraScrollableControl()
        Me.ClinicProcManualEntryGrp = New DevExpress.XtraEditors.GroupControl()
        Me.ClinicProcManualEntryCdmUnits06cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm06cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdmUnits04cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm04cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdmUnits02cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm02cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl87 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl88 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcManualEntryCdmUnits05cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm05cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdmUnits01cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdmUnits03cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm03cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcManualEntryCdm01cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl89 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl90 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProceduresGrp = New DevExpress.XtraEditors.GroupControl()
        Me.ClinicProcProceduresSc = New DevExpress.XtraEditors.XtraScrollableControl()
        Me.ClinicProcFacilityChargesGrp = New DevExpress.XtraEditors.GroupControl()
        Me.ClinicProcFacilityChargesRefreshBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.ClinicProcFacilityChargesLb = New DevExpress.XtraEditors.CheckedListBoxControl()
        Me.ClinicProcProfChargesGrp = New DevExpress.XtraEditors.GroupControl()
        Me.XtraScrollableControl2 = New DevExpress.XtraEditors.XtraScrollableControl()
        Me.ClinicProcProfChargesLineLable01 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable07 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable02 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable06 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable05 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable04 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesLineLable03 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesCpt01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt02 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt03 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt04 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt05 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesCpt07 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl42 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl43 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesMod3Row07 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row06 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row01 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row05 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row02 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row04 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod3Row03 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row07 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row06 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row01 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row05 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row02 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row04 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod2Row03 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl44 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesPhys01 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl45 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx4Row02 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl47 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx4Row01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa07 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx4Row03 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl48 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx3Row07 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa06 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx4Row04 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl49 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx3Row06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa05 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx4Row05 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl52 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx3Row05 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa04 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx4Row06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys02 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx3Row04 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa03 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx4Row07 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys03 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx3Row03 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa02 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx1Row01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys04 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx3Row02 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesNppa01 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx1Row02 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys05 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx3Row01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx2Row07 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx1Row03 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys06 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl53 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesDx2Row06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx1Row04 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesPhys07 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod1Row07 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx2Row05 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx1Row05 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl54 = New DevExpress.XtraEditors.LabelControl()
        Me.ClinicProcProfChargesMod1Row06 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx2Row04 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx1Row06 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesMod1Row01 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod1Row05 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx2Row03 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx1Row07 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesMod1Row02 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesMod1Row04 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ClinicProcProfChargesDx2Row02 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesDx2Row01 = New DevExpress.XtraEditors.TextEdit()
        Me.ClinicProcProfChargesMod1Row03 = New DevExpress.XtraEditors.ComboBoxEdit()
        CType(Me.ClinicProcIcdxGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcIcdxGrp.SuspendLayout()
        CType(Me.ClinicProcIcdx07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcIcdx01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcTestsGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcTestsGrp.SuspendLayout()
        CType(Me.ClinicProcLabsGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcLabsGrp.SuspendLayout()
        CType(Me.ClinicProcManualEntryGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcManualEntryGrp.SuspendLayout()
        CType(Me.ClinicProcManualEntryCdmUnits06cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm06cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdmUnits04cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm04cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdmUnits02cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm02cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdmUnits05cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm05cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdmUnits01cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdmUnits03cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm03cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcManualEntryCdm01cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProceduresGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcProceduresGrp.SuspendLayout()
        CType(Me.ClinicProcFacilityChargesGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcFacilityChargesGrp.SuspendLayout()
        CType(Me.ClinicProcFacilityChargesLb, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesGrp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ClinicProcProfChargesGrp.SuspendLayout()
        Me.XtraScrollableControl2.SuspendLayout()
        CType(Me.ClinicProcProfChargesCpt01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesCpt07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod3Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod2Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx4Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesNppa01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx3Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesPhys07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row06.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row05.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx1Row07.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row04.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row02.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesDx2Row01.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ClinicProcProfChargesMod1Row03.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'ClinicProcIcdxGrp
        '
        Me.ClinicProcIcdxGrp.Controls.Add(Me.SimpleButton2)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxAutoPopBtn)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel01)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel07)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel02)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel06)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel05)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel04)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdxLabel03)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx07)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx06)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx05)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx04)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx03)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx02)
        Me.ClinicProcIcdxGrp.Controls.Add(Me.ClinicProcIcdx01)
        Me.ClinicProcIcdxGrp.Location = New System.Drawing.Point(499, 481)
        Me.ClinicProcIcdxGrp.Name = "ClinicProcIcdxGrp"
        Me.ClinicProcIcdxGrp.Size = New System.Drawing.Size(398, 71)
        Me.ClinicProcIcdxGrp.TabIndex = 240
        Me.ClinicProcIcdxGrp.Text = "ICD Dx"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Location = New System.Drawing.Point(368, 49)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(23, 15)
        Me.SimpleButton2.TabIndex = 294
        Me.SimpleButton2.Text = "..."
        '
        'ClinicProcIcdxAutoPopBtn
        '
        Me.ClinicProcIcdxAutoPopBtn.Location = New System.Drawing.Point(280, 51)
        Me.ClinicProcIcdxAutoPopBtn.Name = "ClinicProcIcdxAutoPopBtn"
        Me.ClinicProcIcdxAutoPopBtn.Size = New System.Drawing.Size(82, 13)
        Me.ClinicProcIcdxAutoPopBtn.TabIndex = 293
        Me.ClinicProcIcdxAutoPopBtn.Text = "Auto Populate All"
        '
        'ClinicProcIcdxLabel01
        '
        Me.ClinicProcIcdxLabel01.Location = New System.Drawing.Point(12, 26)
        Me.ClinicProcIcdxLabel01.Name = "ClinicProcIcdxLabel01"
        Me.ClinicProcIcdxLabel01.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel01.TabIndex = 210
        Me.ClinicProcIcdxLabel01.Text = "1)"
        '
        'ClinicProcIcdxLabel07
        '
        Me.ClinicProcIcdxLabel07.Location = New System.Drawing.Point(279, 26)
        Me.ClinicProcIcdxLabel07.Name = "ClinicProcIcdxLabel07"
        Me.ClinicProcIcdxLabel07.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel07.TabIndex = 209
        Me.ClinicProcIcdxLabel07.Text = "7)"
        '
        'ClinicProcIcdxLabel02
        '
        Me.ClinicProcIcdxLabel02.Location = New System.Drawing.Point(12, 47)
        Me.ClinicProcIcdxLabel02.Name = "ClinicProcIcdxLabel02"
        Me.ClinicProcIcdxLabel02.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel02.TabIndex = 208
        Me.ClinicProcIcdxLabel02.Text = "2)"
        '
        'ClinicProcIcdxLabel06
        '
        Me.ClinicProcIcdxLabel06.Location = New System.Drawing.Point(188, 45)
        Me.ClinicProcIcdxLabel06.Name = "ClinicProcIcdxLabel06"
        Me.ClinicProcIcdxLabel06.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel06.TabIndex = 206
        Me.ClinicProcIcdxLabel06.Text = "6)"
        '
        'ClinicProcIcdxLabel05
        '
        Me.ClinicProcIcdxLabel05.Location = New System.Drawing.Point(188, 24)
        Me.ClinicProcIcdxLabel05.Name = "ClinicProcIcdxLabel05"
        Me.ClinicProcIcdxLabel05.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel05.TabIndex = 205
        Me.ClinicProcIcdxLabel05.Text = "5)"
        '
        'ClinicProcIcdxLabel04
        '
        Me.ClinicProcIcdxLabel04.Location = New System.Drawing.Point(100, 47)
        Me.ClinicProcIcdxLabel04.Name = "ClinicProcIcdxLabel04"
        Me.ClinicProcIcdxLabel04.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel04.TabIndex = 204
        Me.ClinicProcIcdxLabel04.Text = "4)"
        '
        'ClinicProcIcdxLabel03
        '
        Me.ClinicProcIcdxLabel03.Location = New System.Drawing.Point(100, 26)
        Me.ClinicProcIcdxLabel03.Name = "ClinicProcIcdxLabel03"
        Me.ClinicProcIcdxLabel03.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcIcdxLabel03.TabIndex = 203
        Me.ClinicProcIcdxLabel03.Text = "3)"
        '
        'ClinicProcIcdx07
        '
        Me.ClinicProcIcdx07.Location = New System.Drawing.Point(295, 23)
        Me.ClinicProcIcdx07.Name = "ClinicProcIcdx07"
        Me.ClinicProcIcdx07.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx07.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx07.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx07.TabIndex = 50
        '
        'ClinicProcIcdx06
        '
        Me.ClinicProcIcdx06.Location = New System.Drawing.Point(204, 44)
        Me.ClinicProcIcdx06.Name = "ClinicProcIcdx06"
        Me.ClinicProcIcdx06.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx06.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx06.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx06.TabIndex = 49
        '
        'ClinicProcIcdx05
        '
        Me.ClinicProcIcdx05.Location = New System.Drawing.Point(204, 23)
        Me.ClinicProcIcdx05.Name = "ClinicProcIcdx05"
        Me.ClinicProcIcdx05.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx05.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx05.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx05.TabIndex = 48
        '
        'ClinicProcIcdx04
        '
        Me.ClinicProcIcdx04.Location = New System.Drawing.Point(116, 44)
        Me.ClinicProcIcdx04.Name = "ClinicProcIcdx04"
        Me.ClinicProcIcdx04.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx04.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx04.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx04.TabIndex = 47
        '
        'ClinicProcIcdx03
        '
        Me.ClinicProcIcdx03.EditValue = ""
        Me.ClinicProcIcdx03.Location = New System.Drawing.Point(116, 23)
        Me.ClinicProcIcdx03.Name = "ClinicProcIcdx03"
        Me.ClinicProcIcdx03.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx03.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx03.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx03.TabIndex = 46
        '
        'ClinicProcIcdx02
        '
        Me.ClinicProcIcdx02.EditValue = ""
        Me.ClinicProcIcdx02.Location = New System.Drawing.Point(28, 44)
        Me.ClinicProcIcdx02.Name = "ClinicProcIcdx02"
        Me.ClinicProcIcdx02.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx02.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx02.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx02.TabIndex = 45
        '
        'ClinicProcIcdx01
        '
        Me.ClinicProcIcdx01.EditValue = ""
        Me.ClinicProcIcdx01.Location = New System.Drawing.Point(28, 23)
        Me.ClinicProcIcdx01.Name = "ClinicProcIcdx01"
        Me.ClinicProcIcdx01.Properties.Mask.EditMask = "[A-TV-Z][A-Z0-9]{2}(\.[A-Z0-9]{1,4})?|[E][0-9]{3}(\.[0-9])?|[0-9A-Z][0-9]{2}(\.[0" & _
    "-9]{1,2})?"
        Me.ClinicProcIcdx01.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcIcdx01.Size = New System.Drawing.Size(67, 20)
        Me.ClinicProcIcdx01.TabIndex = 44
        '
        'ClinicProcTestsGrp
        '
        Me.ClinicProcTestsGrp.Controls.Add(Me.ClinicProcTestsSc)
        Me.ClinicProcTestsGrp.Location = New System.Drawing.Point(274, 302)
        Me.ClinicProcTestsGrp.Name = "ClinicProcTestsGrp"
        Me.ClinicProcTestsGrp.Size = New System.Drawing.Size(215, 258)
        Me.ClinicProcTestsGrp.TabIndex = 242
        Me.ClinicProcTestsGrp.Text = "Tests"
        '
        'ClinicProcTestsSc
        '
        Me.ClinicProcTestsSc.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ClinicProcTestsSc.Location = New System.Drawing.Point(2, 20)
        Me.ClinicProcTestsSc.Name = "ClinicProcTestsSc"
        Me.ClinicProcTestsSc.Size = New System.Drawing.Size(211, 236)
        Me.ClinicProcTestsSc.TabIndex = 274
        '
        'ClinicProcLabsGrp
        '
        Me.ClinicProcLabsGrp.Controls.Add(Me.ClinicProcLabsSc)
        Me.ClinicProcLabsGrp.Location = New System.Drawing.Point(272, 43)
        Me.ClinicProcLabsGrp.Name = "ClinicProcLabsGrp"
        Me.ClinicProcLabsGrp.Size = New System.Drawing.Size(215, 258)
        Me.ClinicProcLabsGrp.TabIndex = 241
        Me.ClinicProcLabsGrp.Text = "Labs"
        '
        'ClinicProcLabsSc
        '
        Me.ClinicProcLabsSc.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ClinicProcLabsSc.Location = New System.Drawing.Point(2, 20)
        Me.ClinicProcLabsSc.Name = "ClinicProcLabsSc"
        Me.ClinicProcLabsSc.Size = New System.Drawing.Size(211, 236)
        Me.ClinicProcLabsSc.TabIndex = 274
        '
        'ClinicProcManualEntryGrp
        '
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits06cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm06cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits04cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm04cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits02cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm02cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.LabelControl87)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.LabelControl88)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits05cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm05cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits01cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdmUnits03cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm03cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.ClinicProcManualEntryCdm01cbo)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.LabelControl89)
        Me.ClinicProcManualEntryGrp.Controls.Add(Me.LabelControl90)
        Me.ClinicProcManualEntryGrp.Location = New System.Drawing.Point(497, 42)
        Me.ClinicProcManualEntryGrp.Name = "ClinicProcManualEntryGrp"
        Me.ClinicProcManualEntryGrp.Size = New System.Drawing.Size(400, 112)
        Me.ClinicProcManualEntryGrp.TabIndex = 239
        Me.ClinicProcManualEntryGrp.Text = "Manual Entry"
        '
        'ClinicProcManualEntryCdmUnits06cbo
        '
        Me.ClinicProcManualEntryCdmUnits06cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits06cbo.Location = New System.Drawing.Point(338, 88)
        Me.ClinicProcManualEntryCdmUnits06cbo.Name = "ClinicProcManualEntryCdmUnits06cbo"
        Me.ClinicProcManualEntryCdmUnits06cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits06cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits06cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits06cbo.TabIndex = 57
        '
        'ClinicProcManualEntryCdm06cbo
        '
        Me.ClinicProcManualEntryCdm06cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm06cbo.Location = New System.Drawing.Point(199, 88)
        Me.ClinicProcManualEntryCdm06cbo.Name = "ClinicProcManualEntryCdm06cbo"
        Me.ClinicProcManualEntryCdm06cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm06cbo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.ClinicProcManualEntryCdm06cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm06cbo.TabIndex = 56
        '
        'ClinicProcManualEntryCdmUnits04cbo
        '
        Me.ClinicProcManualEntryCdmUnits04cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits04cbo.Location = New System.Drawing.Point(338, 65)
        Me.ClinicProcManualEntryCdmUnits04cbo.Name = "ClinicProcManualEntryCdmUnits04cbo"
        Me.ClinicProcManualEntryCdmUnits04cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits04cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits04cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits04cbo.TabIndex = 55
        '
        'ClinicProcManualEntryCdm04cbo
        '
        Me.ClinicProcManualEntryCdm04cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm04cbo.Location = New System.Drawing.Point(199, 65)
        Me.ClinicProcManualEntryCdm04cbo.Name = "ClinicProcManualEntryCdm04cbo"
        Me.ClinicProcManualEntryCdm04cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm04cbo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.ClinicProcManualEntryCdm04cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm04cbo.TabIndex = 54
        '
        'ClinicProcManualEntryCdmUnits02cbo
        '
        Me.ClinicProcManualEntryCdmUnits02cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits02cbo.Location = New System.Drawing.Point(338, 43)
        Me.ClinicProcManualEntryCdmUnits02cbo.Name = "ClinicProcManualEntryCdmUnits02cbo"
        Me.ClinicProcManualEntryCdmUnits02cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits02cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits02cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits02cbo.TabIndex = 15
        '
        'ClinicProcManualEntryCdm02cbo
        '
        Me.ClinicProcManualEntryCdm02cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm02cbo.Location = New System.Drawing.Point(199, 43)
        Me.ClinicProcManualEntryCdm02cbo.Name = "ClinicProcManualEntryCdm02cbo"
        Me.ClinicProcManualEntryCdm02cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm02cbo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.ClinicProcManualEntryCdm02cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm02cbo.TabIndex = 14
        '
        'LabelControl87
        '
        Me.LabelControl87.Location = New System.Drawing.Point(338, 23)
        Me.LabelControl87.Name = "LabelControl87"
        Me.LabelControl87.Size = New System.Drawing.Size(24, 13)
        Me.LabelControl87.TabIndex = 53
        Me.LabelControl87.Text = "Units"
        '
        'LabelControl88
        '
        Me.LabelControl88.Location = New System.Drawing.Point(199, 23)
        Me.LabelControl88.Name = "LabelControl88"
        Me.LabelControl88.Size = New System.Drawing.Size(33, 13)
        Me.LabelControl88.TabIndex = 52
        Me.LabelControl88.Text = "CDM #"
        '
        'ClinicProcManualEntryCdmUnits05cbo
        '
        Me.ClinicProcManualEntryCdmUnits05cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits05cbo.Location = New System.Drawing.Point(147, 88)
        Me.ClinicProcManualEntryCdmUnits05cbo.Name = "ClinicProcManualEntryCdmUnits05cbo"
        Me.ClinicProcManualEntryCdmUnits05cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits05cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits05cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits05cbo.TabIndex = 5
        '
        'ClinicProcManualEntryCdm05cbo
        '
        Me.ClinicProcManualEntryCdm05cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm05cbo.Location = New System.Drawing.Point(8, 88)
        Me.ClinicProcManualEntryCdm05cbo.Name = "ClinicProcManualEntryCdm05cbo"
        Me.ClinicProcManualEntryCdm05cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm05cbo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.ClinicProcManualEntryCdm05cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm05cbo.TabIndex = 4
        '
        'ClinicProcManualEntryCdmUnits01cbo
        '
        Me.ClinicProcManualEntryCdmUnits01cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits01cbo.Location = New System.Drawing.Point(147, 42)
        Me.ClinicProcManualEntryCdmUnits01cbo.Name = "ClinicProcManualEntryCdmUnits01cbo"
        Me.ClinicProcManualEntryCdmUnits01cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits01cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits01cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits01cbo.TabIndex = 1
        '
        'ClinicProcManualEntryCdmUnits03cbo
        '
        Me.ClinicProcManualEntryCdmUnits03cbo.EditValue = ""
        Me.ClinicProcManualEntryCdmUnits03cbo.Location = New System.Drawing.Point(147, 65)
        Me.ClinicProcManualEntryCdmUnits03cbo.Name = "ClinicProcManualEntryCdmUnits03cbo"
        Me.ClinicProcManualEntryCdmUnits03cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdmUnits03cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcManualEntryCdmUnits03cbo.Size = New System.Drawing.Size(46, 20)
        Me.ClinicProcManualEntryCdmUnits03cbo.TabIndex = 3
        '
        'ClinicProcManualEntryCdm03cbo
        '
        Me.ClinicProcManualEntryCdm03cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm03cbo.Location = New System.Drawing.Point(8, 65)
        Me.ClinicProcManualEntryCdm03cbo.Name = "ClinicProcManualEntryCdm03cbo"
        Me.ClinicProcManualEntryCdm03cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm03cbo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.ClinicProcManualEntryCdm03cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm03cbo.TabIndex = 2
        '
        'ClinicProcManualEntryCdm01cbo
        '
        Me.ClinicProcManualEntryCdm01cbo.EditValue = ""
        Me.ClinicProcManualEntryCdm01cbo.Location = New System.Drawing.Point(8, 42)
        Me.ClinicProcManualEntryCdm01cbo.Name = "ClinicProcManualEntryCdm01cbo"
        Me.ClinicProcManualEntryCdm01cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcManualEntryCdm01cbo.Properties.Mask.EditMask = "[1-9]{2,3}"
        Me.ClinicProcManualEntryCdm01cbo.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcManualEntryCdm01cbo.Size = New System.Drawing.Size(127, 20)
        Me.ClinicProcManualEntryCdm01cbo.TabIndex = 0
        '
        'LabelControl89
        '
        Me.LabelControl89.Location = New System.Drawing.Point(147, 23)
        Me.LabelControl89.Name = "LabelControl89"
        Me.LabelControl89.Size = New System.Drawing.Size(24, 13)
        Me.LabelControl89.TabIndex = 3
        Me.LabelControl89.Text = "Units"
        '
        'LabelControl90
        '
        Me.LabelControl90.Location = New System.Drawing.Point(8, 23)
        Me.LabelControl90.Name = "LabelControl90"
        Me.LabelControl90.Size = New System.Drawing.Size(33, 13)
        Me.LabelControl90.TabIndex = 1
        Me.LabelControl90.Text = "CDM #"
        '
        'ClinicProcProceduresGrp
        '
        Me.ClinicProcProceduresGrp.Controls.Add(Me.ClinicProcProceduresSc)
        Me.ClinicProcProceduresGrp.Location = New System.Drawing.Point(53, 42)
        Me.ClinicProcProceduresGrp.Name = "ClinicProcProceduresGrp"
        Me.ClinicProcProceduresGrp.Size = New System.Drawing.Size(215, 520)
        Me.ClinicProcProceduresGrp.TabIndex = 237
        Me.ClinicProcProceduresGrp.Text = "Procedures"
        '
        'ClinicProcProceduresSc
        '
        Me.ClinicProcProceduresSc.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ClinicProcProceduresSc.Location = New System.Drawing.Point(2, 20)
        Me.ClinicProcProceduresSc.Name = "ClinicProcProceduresSc"
        Me.ClinicProcProceduresSc.Size = New System.Drawing.Size(211, 498)
        Me.ClinicProcProceduresSc.TabIndex = 274
        '
        'ClinicProcFacilityChargesGrp
        '
        Me.ClinicProcFacilityChargesGrp.Controls.Add(Me.ClinicProcFacilityChargesRefreshBtn)
        Me.ClinicProcFacilityChargesGrp.Controls.Add(Me.ClinicProcFacilityChargesLb)
        Me.ClinicProcFacilityChargesGrp.Location = New System.Drawing.Point(499, 156)
        Me.ClinicProcFacilityChargesGrp.Name = "ClinicProcFacilityChargesGrp"
        Me.ClinicProcFacilityChargesGrp.Size = New System.Drawing.Size(398, 145)
        Me.ClinicProcFacilityChargesGrp.TabIndex = 238
        Me.ClinicProcFacilityChargesGrp.Text = "Facility Charges"
        '
        'ClinicProcFacilityChargesRefreshBtn
        '
        Me.ClinicProcFacilityChargesRefreshBtn.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ClinicProcFacilityChargesRefreshBtn.Location = New System.Drawing.Point(5, 115)
        Me.ClinicProcFacilityChargesRefreshBtn.Name = "ClinicProcFacilityChargesRefreshBtn"
        Me.ClinicProcFacilityChargesRefreshBtn.Size = New System.Drawing.Size(384, 23)
        Me.ClinicProcFacilityChargesRefreshBtn.TabIndex = 7
        Me.ClinicProcFacilityChargesRefreshBtn.Text = "Refresh Facility Charges List"
        '
        'ClinicProcFacilityChargesLb
        '
        Me.ClinicProcFacilityChargesLb.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ClinicProcFacilityChargesLb.HorizontalScrollbar = True
        Me.ClinicProcFacilityChargesLb.HotTrackItems = True
        Me.ClinicProcFacilityChargesLb.Location = New System.Drawing.Point(7, 23)
        Me.ClinicProcFacilityChargesLb.Name = "ClinicProcFacilityChargesLb"
        Me.ClinicProcFacilityChargesLb.Size = New System.Drawing.Size(384, 86)
        Me.ClinicProcFacilityChargesLb.TabIndex = 6
        '
        'ClinicProcProfChargesGrp
        '
        Me.ClinicProcProfChargesGrp.Controls.Add(Me.XtraScrollableControl2)
        Me.ClinicProcProfChargesGrp.Location = New System.Drawing.Point(499, 307)
        Me.ClinicProcProfChargesGrp.Name = "ClinicProcProfChargesGrp"
        Me.ClinicProcProfChargesGrp.Size = New System.Drawing.Size(398, 166)
        Me.ClinicProcProfChargesGrp.TabIndex = 243
        Me.ClinicProcProfChargesGrp.Text = "Professional Charges"
        '
        'XtraScrollableControl2
        '
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesLineLable03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesCpt07)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl42)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl43)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod3Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod2Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl44)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys01)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl45)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl47)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl48)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl49)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl52)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx4Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesNppa01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx3Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys06)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl53)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesPhys07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.LabelControl54)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row06)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row05)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row03)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx1Row07)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row04)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row02)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesDx2Row01)
        Me.XtraScrollableControl2.Controls.Add(Me.ClinicProcProfChargesMod1Row03)
        Me.XtraScrollableControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.XtraScrollableControl2.Location = New System.Drawing.Point(2, 20)
        Me.XtraScrollableControl2.Name = "XtraScrollableControl2"
        Me.XtraScrollableControl2.Size = New System.Drawing.Size(394, 144)
        Me.XtraScrollableControl2.TabIndex = 407
        '
        'ClinicProcProfChargesLineLable01
        '
        Me.ClinicProcProfChargesLineLable01.Location = New System.Drawing.Point(7, 25)
        Me.ClinicProcProfChargesLineLable01.Name = "ClinicProcProfChargesLineLable01"
        Me.ClinicProcProfChargesLineLable01.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable01.TabIndex = 450
        Me.ClinicProcProfChargesLineLable01.Text = "1)"
        '
        'ClinicProcProfChargesLineLable07
        '
        Me.ClinicProcProfChargesLineLable07.Location = New System.Drawing.Point(7, 151)
        Me.ClinicProcProfChargesLineLable07.Name = "ClinicProcProfChargesLineLable07"
        Me.ClinicProcProfChargesLineLable07.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable07.TabIndex = 449
        Me.ClinicProcProfChargesLineLable07.Text = "7)"
        '
        'ClinicProcProfChargesLineLable02
        '
        Me.ClinicProcProfChargesLineLable02.Location = New System.Drawing.Point(7, 46)
        Me.ClinicProcProfChargesLineLable02.Name = "ClinicProcProfChargesLineLable02"
        Me.ClinicProcProfChargesLineLable02.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable02.TabIndex = 448
        Me.ClinicProcProfChargesLineLable02.Text = "2)"
        '
        'ClinicProcProfChargesLineLable06
        '
        Me.ClinicProcProfChargesLineLable06.Location = New System.Drawing.Point(7, 128)
        Me.ClinicProcProfChargesLineLable06.Name = "ClinicProcProfChargesLineLable06"
        Me.ClinicProcProfChargesLineLable06.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable06.TabIndex = 447
        Me.ClinicProcProfChargesLineLable06.Text = "6)"
        '
        'ClinicProcProfChargesLineLable05
        '
        Me.ClinicProcProfChargesLineLable05.Location = New System.Drawing.Point(7, 107)
        Me.ClinicProcProfChargesLineLable05.Name = "ClinicProcProfChargesLineLable05"
        Me.ClinicProcProfChargesLineLable05.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable05.TabIndex = 446
        Me.ClinicProcProfChargesLineLable05.Text = "5)"
        '
        'ClinicProcProfChargesLineLable04
        '
        Me.ClinicProcProfChargesLineLable04.Location = New System.Drawing.Point(7, 88)
        Me.ClinicProcProfChargesLineLable04.Name = "ClinicProcProfChargesLineLable04"
        Me.ClinicProcProfChargesLineLable04.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable04.TabIndex = 445
        Me.ClinicProcProfChargesLineLable04.Text = "4)"
        '
        'ClinicProcProfChargesLineLable03
        '
        Me.ClinicProcProfChargesLineLable03.Location = New System.Drawing.Point(7, 67)
        Me.ClinicProcProfChargesLineLable03.Name = "ClinicProcProfChargesLineLable03"
        Me.ClinicProcProfChargesLineLable03.Size = New System.Drawing.Size(10, 13)
        Me.ClinicProcProfChargesLineLable03.TabIndex = 444
        Me.ClinicProcProfChargesLineLable03.Text = "3)"
        '
        'ClinicProcProfChargesCpt01
        '
        Me.ClinicProcProfChargesCpt01.Location = New System.Drawing.Point(23, 22)
        Me.ClinicProcProfChargesCpt01.Name = "ClinicProcProfChargesCpt01"
        Me.ClinicProcProfChargesCpt01.Properties.ReadOnly = True
        Me.ClinicProcProfChargesCpt01.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt01.TabIndex = 0
        '
        'ClinicProcProfChargesCpt02
        '
        Me.ClinicProcProfChargesCpt02.Location = New System.Drawing.Point(23, 43)
        Me.ClinicProcProfChargesCpt02.Name = "ClinicProcProfChargesCpt02"
        Me.ClinicProcProfChargesCpt02.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt02.TabIndex = 10
        '
        'ClinicProcProfChargesCpt03
        '
        Me.ClinicProcProfChargesCpt03.Location = New System.Drawing.Point(23, 64)
        Me.ClinicProcProfChargesCpt03.Name = "ClinicProcProfChargesCpt03"
        Me.ClinicProcProfChargesCpt03.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt03.TabIndex = 20
        '
        'ClinicProcProfChargesCpt04
        '
        Me.ClinicProcProfChargesCpt04.Location = New System.Drawing.Point(23, 85)
        Me.ClinicProcProfChargesCpt04.Name = "ClinicProcProfChargesCpt04"
        Me.ClinicProcProfChargesCpt04.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt04.TabIndex = 440
        '
        'ClinicProcProfChargesCpt05
        '
        Me.ClinicProcProfChargesCpt05.Location = New System.Drawing.Point(23, 106)
        Me.ClinicProcProfChargesCpt05.Name = "ClinicProcProfChargesCpt05"
        Me.ClinicProcProfChargesCpt05.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt05.TabIndex = 441
        '
        'ClinicProcProfChargesCpt06
        '
        Me.ClinicProcProfChargesCpt06.Location = New System.Drawing.Point(23, 127)
        Me.ClinicProcProfChargesCpt06.Name = "ClinicProcProfChargesCpt06"
        Me.ClinicProcProfChargesCpt06.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt06.TabIndex = 442
        '
        'ClinicProcProfChargesCpt07
        '
        Me.ClinicProcProfChargesCpt07.Location = New System.Drawing.Point(23, 148)
        Me.ClinicProcProfChargesCpt07.Name = "ClinicProcProfChargesCpt07"
        Me.ClinicProcProfChargesCpt07.Size = New System.Drawing.Size(71, 20)
        Me.ClinicProcProfChargesCpt07.TabIndex = 443
        '
        'LabelControl42
        '
        Me.LabelControl42.Location = New System.Drawing.Point(470, 3)
        Me.LabelControl42.Name = "LabelControl42"
        Me.LabelControl42.Size = New System.Drawing.Size(26, 13)
        Me.LabelControl42.TabIndex = 436
        Me.LabelControl42.Text = "Mod3"
        '
        'LabelControl43
        '
        Me.LabelControl43.Location = New System.Drawing.Point(435, 3)
        Me.LabelControl43.Name = "LabelControl43"
        Me.LabelControl43.Size = New System.Drawing.Size(26, 13)
        Me.LabelControl43.TabIndex = 435
        Me.LabelControl43.Text = "Mod2"
        '
        'ClinicProcProfChargesMod3Row07
        '
        Me.ClinicProcProfChargesMod3Row07.EditValue = ""
        Me.ClinicProcProfChargesMod3Row07.Location = New System.Drawing.Point(472, 148)
        Me.ClinicProcProfChargesMod3Row07.Name = "ClinicProcProfChargesMod3Row07"
        Me.ClinicProcProfChargesMod3Row07.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row07.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row07.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row07.TabIndex = 427
        '
        'ClinicProcProfChargesMod3Row06
        '
        Me.ClinicProcProfChargesMod3Row06.EditValue = ""
        Me.ClinicProcProfChargesMod3Row06.Location = New System.Drawing.Point(472, 127)
        Me.ClinicProcProfChargesMod3Row06.Name = "ClinicProcProfChargesMod3Row06"
        Me.ClinicProcProfChargesMod3Row06.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row06.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row06.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row06.TabIndex = 426
        '
        'ClinicProcProfChargesMod3Row01
        '
        Me.ClinicProcProfChargesMod3Row01.EditValue = ""
        Me.ClinicProcProfChargesMod3Row01.Location = New System.Drawing.Point(472, 22)
        Me.ClinicProcProfChargesMod3Row01.Name = "ClinicProcProfChargesMod3Row01"
        Me.ClinicProcProfChargesMod3Row01.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row01.Properties.ImmediatePopup = True
        Me.ClinicProcProfChargesMod3Row01.Properties.Items.AddRange(New Object() {"", "jjc1", "jjc2", "jjc3"})
        Me.ClinicProcProfChargesMod3Row01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row01.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row01.TabIndex = 9
        '
        'ClinicProcProfChargesMod3Row05
        '
        Me.ClinicProcProfChargesMod3Row05.EditValue = ""
        Me.ClinicProcProfChargesMod3Row05.Location = New System.Drawing.Point(472, 106)
        Me.ClinicProcProfChargesMod3Row05.Name = "ClinicProcProfChargesMod3Row05"
        Me.ClinicProcProfChargesMod3Row05.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row05.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row05.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row05.TabIndex = 425
        '
        'ClinicProcProfChargesMod3Row02
        '
        Me.ClinicProcProfChargesMod3Row02.EditValue = ""
        Me.ClinicProcProfChargesMod3Row02.Location = New System.Drawing.Point(472, 43)
        Me.ClinicProcProfChargesMod3Row02.Name = "ClinicProcProfChargesMod3Row02"
        Me.ClinicProcProfChargesMod3Row02.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row02.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row02.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row02.TabIndex = 19
        '
        'ClinicProcProfChargesMod3Row04
        '
        Me.ClinicProcProfChargesMod3Row04.EditValue = ""
        Me.ClinicProcProfChargesMod3Row04.Location = New System.Drawing.Point(472, 85)
        Me.ClinicProcProfChargesMod3Row04.Name = "ClinicProcProfChargesMod3Row04"
        Me.ClinicProcProfChargesMod3Row04.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row04.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row04.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row04.TabIndex = 424
        '
        'ClinicProcProfChargesMod3Row03
        '
        Me.ClinicProcProfChargesMod3Row03.EditValue = ""
        Me.ClinicProcProfChargesMod3Row03.Location = New System.Drawing.Point(472, 64)
        Me.ClinicProcProfChargesMod3Row03.Name = "ClinicProcProfChargesMod3Row03"
        Me.ClinicProcProfChargesMod3Row03.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod3Row03.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod3Row03.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod3Row03.TabIndex = 29
        '
        'ClinicProcProfChargesMod2Row07
        '
        Me.ClinicProcProfChargesMod2Row07.EditValue = ""
        Me.ClinicProcProfChargesMod2Row07.Location = New System.Drawing.Point(435, 148)
        Me.ClinicProcProfChargesMod2Row07.Name = "ClinicProcProfChargesMod2Row07"
        Me.ClinicProcProfChargesMod2Row07.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row07.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row07.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row07.TabIndex = 420
        '
        'ClinicProcProfChargesMod2Row06
        '
        Me.ClinicProcProfChargesMod2Row06.EditValue = ""
        Me.ClinicProcProfChargesMod2Row06.Location = New System.Drawing.Point(435, 127)
        Me.ClinicProcProfChargesMod2Row06.Name = "ClinicProcProfChargesMod2Row06"
        Me.ClinicProcProfChargesMod2Row06.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row06.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row06.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row06.TabIndex = 419
        '
        'ClinicProcProfChargesMod2Row01
        '
        Me.ClinicProcProfChargesMod2Row01.EditValue = ""
        Me.ClinicProcProfChargesMod2Row01.Location = New System.Drawing.Point(435, 22)
        Me.ClinicProcProfChargesMod2Row01.Name = "ClinicProcProfChargesMod2Row01"
        Me.ClinicProcProfChargesMod2Row01.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row01.Properties.ImmediatePopup = True
        Me.ClinicProcProfChargesMod2Row01.Properties.Items.AddRange(New Object() {"", "JJC1", "jjc2"})
        Me.ClinicProcProfChargesMod2Row01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row01.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row01.TabIndex = 8
        '
        'ClinicProcProfChargesMod2Row05
        '
        Me.ClinicProcProfChargesMod2Row05.EditValue = ""
        Me.ClinicProcProfChargesMod2Row05.Location = New System.Drawing.Point(435, 106)
        Me.ClinicProcProfChargesMod2Row05.Name = "ClinicProcProfChargesMod2Row05"
        Me.ClinicProcProfChargesMod2Row05.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row05.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row05.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row05.TabIndex = 418
        '
        'ClinicProcProfChargesMod2Row02
        '
        Me.ClinicProcProfChargesMod2Row02.EditValue = ""
        Me.ClinicProcProfChargesMod2Row02.Location = New System.Drawing.Point(435, 43)
        Me.ClinicProcProfChargesMod2Row02.Name = "ClinicProcProfChargesMod2Row02"
        Me.ClinicProcProfChargesMod2Row02.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row02.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row02.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row02.TabIndex = 18
        '
        'ClinicProcProfChargesMod2Row04
        '
        Me.ClinicProcProfChargesMod2Row04.EditValue = ""
        Me.ClinicProcProfChargesMod2Row04.Location = New System.Drawing.Point(435, 85)
        Me.ClinicProcProfChargesMod2Row04.Name = "ClinicProcProfChargesMod2Row04"
        Me.ClinicProcProfChargesMod2Row04.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row04.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row04.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row04.TabIndex = 417
        '
        'ClinicProcProfChargesMod2Row03
        '
        Me.ClinicProcProfChargesMod2Row03.EditValue = ""
        Me.ClinicProcProfChargesMod2Row03.Location = New System.Drawing.Point(435, 64)
        Me.ClinicProcProfChargesMod2Row03.Name = "ClinicProcProfChargesMod2Row03"
        Me.ClinicProcProfChargesMod2Row03.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod2Row03.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod2Row03.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod2Row03.TabIndex = 28
        '
        'LabelControl44
        '
        Me.LabelControl44.Location = New System.Drawing.Point(25, 3)
        Me.LabelControl44.Name = "LabelControl44"
        Me.LabelControl44.Size = New System.Drawing.Size(19, 13)
        Me.LabelControl44.TabIndex = 413
        Me.LabelControl44.Text = "CPT"
        '
        'ClinicProcProfChargesPhys01
        '
        Me.ClinicProcProfChargesPhys01.EditValue = ""
        Me.ClinicProcProfChargesPhys01.Location = New System.Drawing.Point(97, 22)
        Me.ClinicProcProfChargesPhys01.Name = "ClinicProcProfChargesPhys01"
        Me.ClinicProcProfChargesPhys01.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys01.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys01.TabIndex = 1
        '
        'LabelControl45
        '
        Me.LabelControl45.Location = New System.Drawing.Point(193, 3)
        Me.LabelControl45.Name = "LabelControl45"
        Me.LabelControl45.Size = New System.Drawing.Size(30, 13)
        Me.LabelControl45.TabIndex = 405
        Me.LabelControl45.Text = "NP/PA"
        '
        'ClinicProcProfChargesDx4Row02
        '
        Me.ClinicProcProfChargesDx4Row02.Location = New System.Drawing.Point(374, 43)
        Me.ClinicProcProfChargesDx4Row02.Name = "ClinicProcProfChargesDx4Row02"
        Me.ClinicProcProfChargesDx4Row02.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row02.TabIndex = 16
        '
        'LabelControl47
        '
        Me.LabelControl47.Location = New System.Drawing.Point(299, 3)
        Me.LabelControl47.Name = "LabelControl47"
        Me.LabelControl47.Size = New System.Drawing.Size(19, 13)
        Me.LabelControl47.TabIndex = 352
        Me.LabelControl47.Text = "Dx1"
        '
        'ClinicProcProfChargesDx4Row01
        '
        Me.ClinicProcProfChargesDx4Row01.EditValue = ""
        Me.ClinicProcProfChargesDx4Row01.Location = New System.Drawing.Point(374, 22)
        Me.ClinicProcProfChargesDx4Row01.Name = "ClinicProcProfChargesDx4Row01"
        Me.ClinicProcProfChargesDx4Row01.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row01.TabIndex = 6
        '
        'ClinicProcProfChargesNppa07
        '
        Me.ClinicProcProfChargesNppa07.EditValue = ""
        Me.ClinicProcProfChargesNppa07.Location = New System.Drawing.Point(193, 148)
        Me.ClinicProcProfChargesNppa07.Name = "ClinicProcProfChargesNppa07"
        Me.ClinicProcProfChargesNppa07.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa07.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa07.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa07.TabIndex = 50
        '
        'ClinicProcProfChargesDx4Row03
        '
        Me.ClinicProcProfChargesDx4Row03.Location = New System.Drawing.Point(374, 65)
        Me.ClinicProcProfChargesDx4Row03.Name = "ClinicProcProfChargesDx4Row03"
        Me.ClinicProcProfChargesDx4Row03.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row03.TabIndex = 26
        '
        'LabelControl48
        '
        Me.LabelControl48.Location = New System.Drawing.Point(324, 3)
        Me.LabelControl48.Name = "LabelControl48"
        Me.LabelControl48.Size = New System.Drawing.Size(19, 13)
        Me.LabelControl48.TabIndex = 351
        Me.LabelControl48.Text = "Dx2"
        '
        'ClinicProcProfChargesDx3Row07
        '
        Me.ClinicProcProfChargesDx3Row07.EditValue = ""
        Me.ClinicProcProfChargesDx3Row07.Location = New System.Drawing.Point(349, 148)
        Me.ClinicProcProfChargesDx3Row07.Name = "ClinicProcProfChargesDx3Row07"
        Me.ClinicProcProfChargesDx3Row07.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row07.TabIndex = 53
        '
        'ClinicProcProfChargesNppa06
        '
        Me.ClinicProcProfChargesNppa06.EditValue = ""
        Me.ClinicProcProfChargesNppa06.Location = New System.Drawing.Point(193, 127)
        Me.ClinicProcProfChargesNppa06.Name = "ClinicProcProfChargesNppa06"
        Me.ClinicProcProfChargesNppa06.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa06.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa06.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa06.TabIndex = 42
        '
        'ClinicProcProfChargesDx4Row04
        '
        Me.ClinicProcProfChargesDx4Row04.Location = New System.Drawing.Point(374, 85)
        Me.ClinicProcProfChargesDx4Row04.Name = "ClinicProcProfChargesDx4Row04"
        Me.ClinicProcProfChargesDx4Row04.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row04.TabIndex = 30
        '
        'LabelControl49
        '
        Me.LabelControl49.Location = New System.Drawing.Point(374, 3)
        Me.LabelControl49.Name = "LabelControl49"
        Me.LabelControl49.Size = New System.Drawing.Size(19, 13)
        Me.LabelControl49.TabIndex = 350
        Me.LabelControl49.Text = "Dx4"
        '
        'ClinicProcProfChargesDx3Row06
        '
        Me.ClinicProcProfChargesDx3Row06.EditValue = ""
        Me.ClinicProcProfChargesDx3Row06.Location = New System.Drawing.Point(349, 127)
        Me.ClinicProcProfChargesDx3Row06.Name = "ClinicProcProfChargesDx3Row06"
        Me.ClinicProcProfChargesDx3Row06.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row06.TabIndex = 45
        '
        'ClinicProcProfChargesNppa05
        '
        Me.ClinicProcProfChargesNppa05.EditValue = ""
        Me.ClinicProcProfChargesNppa05.Location = New System.Drawing.Point(193, 106)
        Me.ClinicProcProfChargesNppa05.Name = "ClinicProcProfChargesNppa05"
        Me.ClinicProcProfChargesNppa05.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa05.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa05.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa05.TabIndex = 34
        '
        'ClinicProcProfChargesDx4Row05
        '
        Me.ClinicProcProfChargesDx4Row05.Location = New System.Drawing.Point(374, 106)
        Me.ClinicProcProfChargesDx4Row05.Name = "ClinicProcProfChargesDx4Row05"
        Me.ClinicProcProfChargesDx4Row05.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row05.TabIndex = 38
        '
        'LabelControl52
        '
        Me.LabelControl52.Location = New System.Drawing.Point(349, 3)
        Me.LabelControl52.Name = "LabelControl52"
        Me.LabelControl52.Size = New System.Drawing.Size(19, 13)
        Me.LabelControl52.TabIndex = 349
        Me.LabelControl52.Text = "Dx3"
        '
        'ClinicProcProfChargesDx3Row05
        '
        Me.ClinicProcProfChargesDx3Row05.EditValue = ""
        Me.ClinicProcProfChargesDx3Row05.Location = New System.Drawing.Point(349, 106)
        Me.ClinicProcProfChargesDx3Row05.Name = "ClinicProcProfChargesDx3Row05"
        Me.ClinicProcProfChargesDx3Row05.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row05.TabIndex = 37
        '
        'ClinicProcProfChargesNppa04
        '
        Me.ClinicProcProfChargesNppa04.EditValue = ""
        Me.ClinicProcProfChargesNppa04.Location = New System.Drawing.Point(193, 85)
        Me.ClinicProcProfChargesNppa04.Name = "ClinicProcProfChargesNppa04"
        Me.ClinicProcProfChargesNppa04.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa04.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa04.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa04.TabIndex = 26
        '
        'ClinicProcProfChargesDx4Row06
        '
        Me.ClinicProcProfChargesDx4Row06.Location = New System.Drawing.Point(374, 127)
        Me.ClinicProcProfChargesDx4Row06.Name = "ClinicProcProfChargesDx4Row06"
        Me.ClinicProcProfChargesDx4Row06.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row06.TabIndex = 46
        '
        'ClinicProcProfChargesPhys02
        '
        Me.ClinicProcProfChargesPhys02.EditValue = ""
        Me.ClinicProcProfChargesPhys02.Location = New System.Drawing.Point(97, 43)
        Me.ClinicProcProfChargesPhys02.Name = "ClinicProcProfChargesPhys02"
        Me.ClinicProcProfChargesPhys02.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys02.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys02.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys02.TabIndex = 11
        '
        'ClinicProcProfChargesDx3Row04
        '
        Me.ClinicProcProfChargesDx3Row04.EditValue = ""
        Me.ClinicProcProfChargesDx3Row04.Location = New System.Drawing.Point(349, 85)
        Me.ClinicProcProfChargesDx3Row04.Name = "ClinicProcProfChargesDx3Row04"
        Me.ClinicProcProfChargesDx3Row04.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row04.TabIndex = 29
        '
        'ClinicProcProfChargesNppa03
        '
        Me.ClinicProcProfChargesNppa03.EditValue = ""
        Me.ClinicProcProfChargesNppa03.Location = New System.Drawing.Point(193, 64)
        Me.ClinicProcProfChargesNppa03.Name = "ClinicProcProfChargesNppa03"
        Me.ClinicProcProfChargesNppa03.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa03.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa03.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa03.TabIndex = 22
        '
        'ClinicProcProfChargesDx4Row07
        '
        Me.ClinicProcProfChargesDx4Row07.Location = New System.Drawing.Point(374, 148)
        Me.ClinicProcProfChargesDx4Row07.Name = "ClinicProcProfChargesDx4Row07"
        Me.ClinicProcProfChargesDx4Row07.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx4Row07.TabIndex = 54
        '
        'ClinicProcProfChargesPhys03
        '
        Me.ClinicProcProfChargesPhys03.EditValue = ""
        Me.ClinicProcProfChargesPhys03.Location = New System.Drawing.Point(97, 64)
        Me.ClinicProcProfChargesPhys03.Name = "ClinicProcProfChargesPhys03"
        Me.ClinicProcProfChargesPhys03.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys03.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys03.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys03.TabIndex = 21
        '
        'ClinicProcProfChargesDx3Row03
        '
        Me.ClinicProcProfChargesDx3Row03.EditValue = ""
        Me.ClinicProcProfChargesDx3Row03.Location = New System.Drawing.Point(349, 65)
        Me.ClinicProcProfChargesDx3Row03.Name = "ClinicProcProfChargesDx3Row03"
        Me.ClinicProcProfChargesDx3Row03.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row03.TabIndex = 25
        '
        'ClinicProcProfChargesNppa02
        '
        Me.ClinicProcProfChargesNppa02.EditValue = ""
        Me.ClinicProcProfChargesNppa02.Location = New System.Drawing.Point(193, 43)
        Me.ClinicProcProfChargesNppa02.Name = "ClinicProcProfChargesNppa02"
        Me.ClinicProcProfChargesNppa02.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa02.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa02.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa02.TabIndex = 12
        '
        'ClinicProcProfChargesDx1Row01
        '
        Me.ClinicProcProfChargesDx1Row01.EditValue = ""
        Me.ClinicProcProfChargesDx1Row01.Location = New System.Drawing.Point(299, 22)
        Me.ClinicProcProfChargesDx1Row01.Name = "ClinicProcProfChargesDx1Row01"
        Me.ClinicProcProfChargesDx1Row01.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row01.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row01.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row01.TabIndex = 3
        '
        'ClinicProcProfChargesPhys04
        '
        Me.ClinicProcProfChargesPhys04.EditValue = ""
        Me.ClinicProcProfChargesPhys04.Location = New System.Drawing.Point(97, 85)
        Me.ClinicProcProfChargesPhys04.Name = "ClinicProcProfChargesPhys04"
        Me.ClinicProcProfChargesPhys04.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys04.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys04.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys04.TabIndex = 25
        '
        'ClinicProcProfChargesDx3Row02
        '
        Me.ClinicProcProfChargesDx3Row02.EditValue = ""
        Me.ClinicProcProfChargesDx3Row02.Location = New System.Drawing.Point(349, 43)
        Me.ClinicProcProfChargesDx3Row02.Name = "ClinicProcProfChargesDx3Row02"
        Me.ClinicProcProfChargesDx3Row02.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row02.TabIndex = 15
        '
        'ClinicProcProfChargesNppa01
        '
        Me.ClinicProcProfChargesNppa01.EditValue = ""
        Me.ClinicProcProfChargesNppa01.Location = New System.Drawing.Point(193, 22)
        Me.ClinicProcProfChargesNppa01.Name = "ClinicProcProfChargesNppa01"
        Me.ClinicProcProfChargesNppa01.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesNppa01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesNppa01.Size = New System.Drawing.Size(100, 20)
        Me.ClinicProcProfChargesNppa01.TabIndex = 2
        '
        'ClinicProcProfChargesDx1Row02
        '
        Me.ClinicProcProfChargesDx1Row02.EditValue = ""
        Me.ClinicProcProfChargesDx1Row02.Location = New System.Drawing.Point(299, 43)
        Me.ClinicProcProfChargesDx1Row02.Name = "ClinicProcProfChargesDx1Row02"
        Me.ClinicProcProfChargesDx1Row02.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row02.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row02.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row02.TabIndex = 13
        '
        'ClinicProcProfChargesPhys05
        '
        Me.ClinicProcProfChargesPhys05.EditValue = ""
        Me.ClinicProcProfChargesPhys05.Location = New System.Drawing.Point(97, 106)
        Me.ClinicProcProfChargesPhys05.Name = "ClinicProcProfChargesPhys05"
        Me.ClinicProcProfChargesPhys05.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys05.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys05.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys05.TabIndex = 33
        '
        'ClinicProcProfChargesDx3Row01
        '
        Me.ClinicProcProfChargesDx3Row01.EditValue = ""
        Me.ClinicProcProfChargesDx3Row01.Location = New System.Drawing.Point(349, 22)
        Me.ClinicProcProfChargesDx3Row01.Name = "ClinicProcProfChargesDx3Row01"
        Me.ClinicProcProfChargesDx3Row01.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx3Row01.TabIndex = 5
        '
        'ClinicProcProfChargesDx2Row07
        '
        Me.ClinicProcProfChargesDx2Row07.EditValue = ""
        Me.ClinicProcProfChargesDx2Row07.Location = New System.Drawing.Point(324, 148)
        Me.ClinicProcProfChargesDx2Row07.Name = "ClinicProcProfChargesDx2Row07"
        Me.ClinicProcProfChargesDx2Row07.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row07.TabIndex = 52
        '
        'ClinicProcProfChargesDx1Row03
        '
        Me.ClinicProcProfChargesDx1Row03.EditValue = ""
        Me.ClinicProcProfChargesDx1Row03.Location = New System.Drawing.Point(299, 65)
        Me.ClinicProcProfChargesDx1Row03.Name = "ClinicProcProfChargesDx1Row03"
        Me.ClinicProcProfChargesDx1Row03.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row03.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row03.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row03.TabIndex = 23
        '
        'ClinicProcProfChargesPhys06
        '
        Me.ClinicProcProfChargesPhys06.EditValue = ""
        Me.ClinicProcProfChargesPhys06.Location = New System.Drawing.Point(97, 127)
        Me.ClinicProcProfChargesPhys06.Name = "ClinicProcProfChargesPhys06"
        Me.ClinicProcProfChargesPhys06.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys06.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys06.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys06.TabIndex = 41
        '
        'LabelControl53
        '
        Me.LabelControl53.Location = New System.Drawing.Point(399, 3)
        Me.LabelControl53.Name = "LabelControl53"
        Me.LabelControl53.Size = New System.Drawing.Size(26, 13)
        Me.LabelControl53.TabIndex = 369
        Me.LabelControl53.Text = "Mod1"
        '
        'ClinicProcProfChargesDx2Row06
        '
        Me.ClinicProcProfChargesDx2Row06.EditValue = ""
        Me.ClinicProcProfChargesDx2Row06.Location = New System.Drawing.Point(324, 127)
        Me.ClinicProcProfChargesDx2Row06.Name = "ClinicProcProfChargesDx2Row06"
        Me.ClinicProcProfChargesDx2Row06.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row06.TabIndex = 44
        '
        'ClinicProcProfChargesDx1Row04
        '
        Me.ClinicProcProfChargesDx1Row04.EditValue = ""
        Me.ClinicProcProfChargesDx1Row04.Location = New System.Drawing.Point(299, 85)
        Me.ClinicProcProfChargesDx1Row04.Name = "ClinicProcProfChargesDx1Row04"
        Me.ClinicProcProfChargesDx1Row04.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row04.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row04.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row04.TabIndex = 27
        '
        'ClinicProcProfChargesPhys07
        '
        Me.ClinicProcProfChargesPhys07.EditValue = ""
        Me.ClinicProcProfChargesPhys07.Location = New System.Drawing.Point(97, 148)
        Me.ClinicProcProfChargesPhys07.Name = "ClinicProcProfChargesPhys07"
        Me.ClinicProcProfChargesPhys07.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesPhys07.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesPhys07.Size = New System.Drawing.Size(93, 20)
        Me.ClinicProcProfChargesPhys07.TabIndex = 49
        '
        'ClinicProcProfChargesMod1Row07
        '
        Me.ClinicProcProfChargesMod1Row07.EditValue = ""
        Me.ClinicProcProfChargesMod1Row07.Location = New System.Drawing.Point(397, 148)
        Me.ClinicProcProfChargesMod1Row07.Name = "ClinicProcProfChargesMod1Row07"
        Me.ClinicProcProfChargesMod1Row07.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row07.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row07.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row07.TabIndex = 55
        '
        'ClinicProcProfChargesDx2Row05
        '
        Me.ClinicProcProfChargesDx2Row05.EditValue = ""
        Me.ClinicProcProfChargesDx2Row05.Location = New System.Drawing.Point(324, 106)
        Me.ClinicProcProfChargesDx2Row05.Name = "ClinicProcProfChargesDx2Row05"
        Me.ClinicProcProfChargesDx2Row05.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row05.TabIndex = 36
        '
        'ClinicProcProfChargesDx1Row05
        '
        Me.ClinicProcProfChargesDx1Row05.EditValue = ""
        Me.ClinicProcProfChargesDx1Row05.Location = New System.Drawing.Point(299, 106)
        Me.ClinicProcProfChargesDx1Row05.Name = "ClinicProcProfChargesDx1Row05"
        Me.ClinicProcProfChargesDx1Row05.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row05.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row05.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row05.TabIndex = 35
        '
        'LabelControl54
        '
        Me.LabelControl54.Location = New System.Drawing.Point(97, 3)
        Me.LabelControl54.Name = "LabelControl54"
        Me.LabelControl54.Size = New System.Drawing.Size(44, 13)
        Me.LabelControl54.TabIndex = 361
        Me.LabelControl54.Text = "Physician"
        '
        'ClinicProcProfChargesMod1Row06
        '
        Me.ClinicProcProfChargesMod1Row06.EditValue = ""
        Me.ClinicProcProfChargesMod1Row06.Location = New System.Drawing.Point(397, 127)
        Me.ClinicProcProfChargesMod1Row06.Name = "ClinicProcProfChargesMod1Row06"
        Me.ClinicProcProfChargesMod1Row06.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row06.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row06.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row06.TabIndex = 47
        '
        'ClinicProcProfChargesDx2Row04
        '
        Me.ClinicProcProfChargesDx2Row04.EditValue = ""
        Me.ClinicProcProfChargesDx2Row04.Location = New System.Drawing.Point(324, 85)
        Me.ClinicProcProfChargesDx2Row04.Name = "ClinicProcProfChargesDx2Row04"
        Me.ClinicProcProfChargesDx2Row04.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row04.TabIndex = 28
        '
        'ClinicProcProfChargesDx1Row06
        '
        Me.ClinicProcProfChargesDx1Row06.EditValue = ""
        Me.ClinicProcProfChargesDx1Row06.Location = New System.Drawing.Point(299, 127)
        Me.ClinicProcProfChargesDx1Row06.Name = "ClinicProcProfChargesDx1Row06"
        Me.ClinicProcProfChargesDx1Row06.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row06.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row06.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row06.TabIndex = 43
        '
        'ClinicProcProfChargesMod1Row01
        '
        Me.ClinicProcProfChargesMod1Row01.EditValue = ""
        Me.ClinicProcProfChargesMod1Row01.Location = New System.Drawing.Point(397, 22)
        Me.ClinicProcProfChargesMod1Row01.Name = "ClinicProcProfChargesMod1Row01"
        Me.ClinicProcProfChargesMod1Row01.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row01.Properties.ImmediatePopup = True
        Me.ClinicProcProfChargesMod1Row01.Properties.Items.AddRange(New Object() {"", "JJC1", "JJC2"})
        Me.ClinicProcProfChargesMod1Row01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row01.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row01.TabIndex = 7
        '
        'ClinicProcProfChargesMod1Row05
        '
        Me.ClinicProcProfChargesMod1Row05.EditValue = ""
        Me.ClinicProcProfChargesMod1Row05.Location = New System.Drawing.Point(397, 106)
        Me.ClinicProcProfChargesMod1Row05.Name = "ClinicProcProfChargesMod1Row05"
        Me.ClinicProcProfChargesMod1Row05.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row05.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row05.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row05.TabIndex = 39
        '
        'ClinicProcProfChargesDx2Row03
        '
        Me.ClinicProcProfChargesDx2Row03.EditValue = ""
        Me.ClinicProcProfChargesDx2Row03.Location = New System.Drawing.Point(324, 65)
        Me.ClinicProcProfChargesDx2Row03.Name = "ClinicProcProfChargesDx2Row03"
        Me.ClinicProcProfChargesDx2Row03.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row03.TabIndex = 24
        '
        'ClinicProcProfChargesDx1Row07
        '
        Me.ClinicProcProfChargesDx1Row07.EditValue = ""
        Me.ClinicProcProfChargesDx1Row07.Location = New System.Drawing.Point(299, 148)
        Me.ClinicProcProfChargesDx1Row07.Name = "ClinicProcProfChargesDx1Row07"
        Me.ClinicProcProfChargesDx1Row07.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx1Row07.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx1Row07.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx1Row07.TabIndex = 51
        '
        'ClinicProcProfChargesMod1Row02
        '
        Me.ClinicProcProfChargesMod1Row02.EditValue = ""
        Me.ClinicProcProfChargesMod1Row02.Location = New System.Drawing.Point(397, 43)
        Me.ClinicProcProfChargesMod1Row02.Name = "ClinicProcProfChargesMod1Row02"
        Me.ClinicProcProfChargesMod1Row02.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row02.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row02.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row02.TabIndex = 17
        '
        'ClinicProcProfChargesMod1Row04
        '
        Me.ClinicProcProfChargesMod1Row04.EditValue = ""
        Me.ClinicProcProfChargesMod1Row04.Location = New System.Drawing.Point(397, 85)
        Me.ClinicProcProfChargesMod1Row04.Name = "ClinicProcProfChargesMod1Row04"
        Me.ClinicProcProfChargesMod1Row04.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row04.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row04.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row04.TabIndex = 31
        '
        'ClinicProcProfChargesDx2Row02
        '
        Me.ClinicProcProfChargesDx2Row02.EditValue = ""
        Me.ClinicProcProfChargesDx2Row02.Location = New System.Drawing.Point(324, 43)
        Me.ClinicProcProfChargesDx2Row02.Name = "ClinicProcProfChargesDx2Row02"
        Me.ClinicProcProfChargesDx2Row02.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row02.TabIndex = 14
        '
        'ClinicProcProfChargesDx2Row01
        '
        Me.ClinicProcProfChargesDx2Row01.EditValue = ""
        Me.ClinicProcProfChargesDx2Row01.Location = New System.Drawing.Point(324, 22)
        Me.ClinicProcProfChargesDx2Row01.Name = "ClinicProcProfChargesDx2Row01"
        Me.ClinicProcProfChargesDx2Row01.Properties.Mask.EditMask = "[1-7]"
        Me.ClinicProcProfChargesDx2Row01.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ClinicProcProfChargesDx2Row01.Size = New System.Drawing.Size(19, 20)
        Me.ClinicProcProfChargesDx2Row01.TabIndex = 4
        '
        'ClinicProcProfChargesMod1Row03
        '
        Me.ClinicProcProfChargesMod1Row03.EditValue = ""
        Me.ClinicProcProfChargesMod1Row03.Location = New System.Drawing.Point(397, 64)
        Me.ClinicProcProfChargesMod1Row03.Name = "ClinicProcProfChargesMod1Row03"
        Me.ClinicProcProfChargesMod1Row03.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ClinicProcProfChargesMod1Row03.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ClinicProcProfChargesMod1Row03.Size = New System.Drawing.Size(35, 20)
        Me.ClinicProcProfChargesMod1Row03.TabIndex = 27
        '
        'ScratchPad2Form
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1355, 806)
        Me.Controls.Add(Me.ClinicProcProfChargesGrp)
        Me.Controls.Add(Me.ClinicProcIcdxGrp)
        Me.Controls.Add(Me.ClinicProcTestsGrp)
        Me.Controls.Add(Me.ClinicProcLabsGrp)
        Me.Controls.Add(Me.ClinicProcManualEntryGrp)
        Me.Controls.Add(Me.ClinicProcProceduresGrp)
        Me.Controls.Add(Me.ClinicProcFacilityChargesGrp)
        Me.Name = "ScratchPad2Form"
        Me.Text = "ScratchPad2Form"
        CType(Me.ClinicProcIcdxGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcIcdxGrp.ResumeLayout(False)
        Me.ClinicProcIcdxGrp.PerformLayout()
        CType(Me.ClinicProcIcdx07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcIcdx01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcTestsGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcTestsGrp.ResumeLayout(False)
        CType(Me.ClinicProcLabsGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcLabsGrp.ResumeLayout(False)
        CType(Me.ClinicProcManualEntryGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcManualEntryGrp.ResumeLayout(False)
        Me.ClinicProcManualEntryGrp.PerformLayout()
        CType(Me.ClinicProcManualEntryCdmUnits06cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm06cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdmUnits04cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm04cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdmUnits02cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm02cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdmUnits05cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm05cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdmUnits01cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdmUnits03cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm03cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcManualEntryCdm01cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProceduresGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcProceduresGrp.ResumeLayout(False)
        CType(Me.ClinicProcFacilityChargesGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcFacilityChargesGrp.ResumeLayout(False)
        CType(Me.ClinicProcFacilityChargesLb, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesGrp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ClinicProcProfChargesGrp.ResumeLayout(False)
        Me.XtraScrollableControl2.ResumeLayout(False)
        Me.XtraScrollableControl2.PerformLayout()
        CType(Me.ClinicProcProfChargesCpt01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesCpt07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod3Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod2Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx4Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesNppa01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx3Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesPhys07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row06.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row05.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx1Row07.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row04.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row02.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesDx2Row01.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ClinicProcProfChargesMod1Row03.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ClinicProcIcdxGrp As DevExpress.XtraEditors.GroupControl
    Public WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ClinicProcIcdxAutoPopBtn As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel01 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel07 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel02 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel06 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel05 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel04 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdxLabel03 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcIcdx07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx06 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx05 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx04 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx03 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx02 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcIcdx01 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcTestsGrp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ClinicProcTestsSc As DevExpress.XtraEditors.XtraScrollableControl
    Friend WithEvents ClinicProcLabsGrp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ClinicProcLabsSc As DevExpress.XtraEditors.XtraScrollableControl
    Friend WithEvents ClinicProcManualEntryGrp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ClinicProcManualEntryCdmUnits06cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm06cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdmUnits04cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm04cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdmUnits02cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm02cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl87 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl88 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcManualEntryCdmUnits05cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm05cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdmUnits01cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdmUnits03cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm03cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcManualEntryCdm01cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl89 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl90 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProceduresGrp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ClinicProcProceduresSc As DevExpress.XtraEditors.XtraScrollableControl
    Protected WithEvents ClinicProcFacilityChargesGrp As DevExpress.XtraEditors.GroupControl
    Protected WithEvents ClinicProcFacilityChargesRefreshBtn As DevExpress.XtraEditors.SimpleButton
    Protected WithEvents ClinicProcFacilityChargesLb As DevExpress.XtraEditors.CheckedListBoxControl
    Protected WithEvents ClinicProcProfChargesGrp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents XtraScrollableControl2 As DevExpress.XtraEditors.XtraScrollableControl
    Friend WithEvents ClinicProcProfChargesLineLable01 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable07 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable02 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable06 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable05 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable04 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesLineLable03 As DevExpress.XtraEditors.LabelControl
    Protected WithEvents ClinicProcProfChargesCpt01 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt02 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt03 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt04 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt05 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt06 As DevExpress.XtraEditors.TextEdit
    Protected WithEvents ClinicProcProfChargesCpt07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl42 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl43 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesMod3Row07 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row06 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row01 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row05 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row02 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row04 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod3Row03 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row07 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row06 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row01 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row05 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row02 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row04 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod2Row03 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl44 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesPhys01 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl45 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx4Row02 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl47 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx4Row01 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa07 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx4Row03 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl48 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx3Row07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa06 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx4Row04 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl49 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx3Row06 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa05 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx4Row05 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl52 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx3Row05 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa04 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx4Row06 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys02 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx3Row04 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa03 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx4Row07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys03 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx3Row03 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa02 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx1Row01 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys04 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx3Row02 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesNppa01 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx1Row02 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys05 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx3Row01 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx2Row07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx1Row03 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys06 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl53 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesDx2Row06 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx1Row04 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesPhys07 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod1Row07 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx2Row05 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx1Row05 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl54 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClinicProcProfChargesMod1Row06 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx2Row04 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx1Row06 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesMod1Row01 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod1Row05 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx2Row03 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx1Row07 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesMod1Row02 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesMod1Row04 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ClinicProcProfChargesDx2Row02 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesDx2Row01 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ClinicProcProfChargesMod1Row03 As DevExpress.XtraEditors.ComboBoxEdit
End Class

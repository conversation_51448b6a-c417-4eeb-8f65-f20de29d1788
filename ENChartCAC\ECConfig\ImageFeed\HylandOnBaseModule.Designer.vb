﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class HylandOnBaseModule
    Inherits System.Windows.Forms.UserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.grpMapping = New DevExpress.XtraEditors.GroupControl()
        Me.btnRemove = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.gridIndexMappings = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ricbFacilities = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.ricbSettingCategories = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.ricbSettingNames = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.grpImageFeedConfig = New DevExpress.XtraEditors.GroupControl()
        Me.txtFieldDelimiter = New DevExpress.XtraEditors.TextEdit()
        Me.lblFieldDelimiter = New DevExpress.XtraEditors.LabelControl()
        Me.dteStartDate = New DevExpress.XtraEditors.DateEdit()
        Me.lblStartDate = New DevExpress.XtraEditors.LabelControl()
        Me.lblDocumentType = New DevExpress.XtraEditors.LabelControl()
        Me.cboDocumentType = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.txtIndexPath = New DevExpress.XtraEditors.TextEdit()
        Me.lblIndexPath = New DevExpress.XtraEditors.LabelControl()
        Me.txtDocumentPath = New DevExpress.XtraEditors.TextEdit()
        Me.lblDocumentDestination = New DevExpress.XtraEditors.LabelControl()
        CType(Me.grpMapping, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpMapping.SuspendLayout()
        CType(Me.gridIndexMappings, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbFacilities, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbSettingCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbSettingNames, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpImageFeedConfig, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpImageFeedConfig.SuspendLayout()
        CType(Me.txtFieldDelimiter.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dteStartDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dteStartDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboDocumentType.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtIndexPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDocumentPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'grpMapping
        '
        Me.grpMapping.Controls.Add(Me.btnRemove)
        Me.grpMapping.Controls.Add(Me.btnAdd)
        Me.grpMapping.Controls.Add(Me.gridIndexMappings)
        Me.grpMapping.Location = New System.Drawing.Point(3, 200)
        Me.grpMapping.Name = "grpMapping"
        Me.grpMapping.Size = New System.Drawing.Size(864, 280)
        Me.grpMapping.TabIndex = 64
        Me.grpMapping.Text = "Hyland OnBase Image Feed Mappings"
        '
        'btnRemove
        '
        Me.btnRemove.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRemove.Location = New System.Drawing.Point(835, 52)
        Me.btnRemove.Name = "btnRemove"
        Me.btnRemove.Size = New System.Drawing.Size(24, 23)
        Me.btnRemove.TabIndex = 4
        Me.btnRemove.Text = "-"
        '
        'btnAdd
        '
        Me.btnAdd.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAdd.Location = New System.Drawing.Point(835, 23)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(24, 23)
        Me.btnAdd.TabIndex = 3
        Me.btnAdd.Text = "+"
        '
        'gridIndexMappings
        '
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.First.Visible = False
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.Last.Visible = False
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.Next.Visible = False
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.NextPage.Visible = False
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.Prev.Visible = False
        Me.gridIndexMappings.EmbeddedNavigator.Buttons.PrevPage.Visible = False
        Me.gridIndexMappings.Location = New System.Drawing.Point(6, 23)
        Me.gridIndexMappings.MainView = Me.GridView1
        Me.gridIndexMappings.Name = "gridIndexMappings"
        Me.gridIndexMappings.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.ricbFacilities, Me.ricbSettingCategories, Me.ricbSettingNames})
        Me.gridIndexMappings.Size = New System.Drawing.Size(825, 252)
        Me.gridIndexMappings.TabIndex = 2
        Me.gridIndexMappings.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.GridControl = Me.gridIndexMappings
        Me.GridView1.Name = "GridView1"
        Me.GridView1.NewItemRowText = "Add new row"
        Me.GridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'ricbFacilities
        '
        Me.ricbFacilities.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.ricbFacilities.AutoHeight = False
        Me.ricbFacilities.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbFacilities.Name = "ricbFacilities"
        Me.ricbFacilities.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        '
        'ricbSettingCategories
        '
        Me.ricbSettingCategories.AutoHeight = False
        Me.ricbSettingCategories.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbSettingCategories.Name = "ricbSettingCategories"
        '
        'ricbSettingNames
        '
        Me.ricbSettingNames.AutoHeight = False
        Me.ricbSettingNames.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbSettingNames.Name = "ricbSettingNames"
        '
        'grpImageFeedConfig
        '
        Me.grpImageFeedConfig.Controls.Add(Me.txtFieldDelimiter)
        Me.grpImageFeedConfig.Controls.Add(Me.lblFieldDelimiter)
        Me.grpImageFeedConfig.Controls.Add(Me.dteStartDate)
        Me.grpImageFeedConfig.Controls.Add(Me.lblStartDate)
        Me.grpImageFeedConfig.Controls.Add(Me.lblDocumentType)
        Me.grpImageFeedConfig.Controls.Add(Me.cboDocumentType)
        Me.grpImageFeedConfig.Controls.Add(Me.txtIndexPath)
        Me.grpImageFeedConfig.Controls.Add(Me.lblIndexPath)
        Me.grpImageFeedConfig.Controls.Add(Me.txtDocumentPath)
        Me.grpImageFeedConfig.Controls.Add(Me.lblDocumentDestination)
        Me.grpImageFeedConfig.Location = New System.Drawing.Point(3, 3)
        Me.grpImageFeedConfig.Name = "grpImageFeedConfig"
        Me.grpImageFeedConfig.Size = New System.Drawing.Size(864, 191)
        Me.grpImageFeedConfig.TabIndex = 63
        Me.grpImageFeedConfig.Text = "Hyland OnBase Image Feed Configuration"
        '
        'txtFieldDelimiter
        '
        Me.txtFieldDelimiter.Location = New System.Drawing.Point(91, 143)
        Me.txtFieldDelimiter.Name = "txtFieldDelimiter"
        Me.txtFieldDelimiter.Size = New System.Drawing.Size(53, 20)
        Me.txtFieldDelimiter.TabIndex = 54
        '
        'lblFieldDelimiter
        '
        Me.lblFieldDelimiter.Location = New System.Drawing.Point(5, 146)
        Me.lblFieldDelimiter.Name = "lblFieldDelimiter"
        Me.lblFieldDelimiter.Size = New System.Drawing.Size(79, 13)
        Me.lblFieldDelimiter.TabIndex = 53
        Me.lblFieldDelimiter.Text = "* Field Delimiter:"
        '
        'dteStartDate
        '
        Me.dteStartDate.EditValue = Nothing
        Me.dteStartDate.Location = New System.Drawing.Point(91, 111)
        Me.dteStartDate.Name = "dteStartDate"
        Me.dteStartDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dteStartDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.dteStartDate.Size = New System.Drawing.Size(104, 20)
        Me.dteStartDate.TabIndex = 47
        '
        'lblStartDate
        '
        Me.lblStartDate.Location = New System.Drawing.Point(6, 114)
        Me.lblStartDate.Name = "lblStartDate"
        Me.lblStartDate.Size = New System.Drawing.Size(54, 13)
        Me.lblStartDate.TabIndex = 25
        Me.lblStartDate.Text = "Start Date:"
        '
        'lblDocumentType
        '
        Me.lblDocumentType.Location = New System.Drawing.Point(5, 88)
        Me.lblDocumentType.Name = "lblDocumentType"
        Me.lblDocumentType.Size = New System.Drawing.Size(79, 13)
        Me.lblDocumentType.TabIndex = 24
        Me.lblDocumentType.Text = "Document Type:"
        '
        'cboDocumentType
        '
        Me.cboDocumentType.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboDocumentType.EditValue = "PDF"
        Me.cboDocumentType.Location = New System.Drawing.Point(91, 85)
        Me.cboDocumentType.Name = "cboDocumentType"
        Me.cboDocumentType.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.cboDocumentType.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboDocumentType.Properties.ImmediatePopup = True
        Me.cboDocumentType.Properties.Items.AddRange(New Object() {"PDF", "TIF"})
        Me.cboDocumentType.Size = New System.Drawing.Size(53, 20)
        Me.cboDocumentType.TabIndex = 23
        '
        'txtIndexPath
        '
        Me.txtIndexPath.Location = New System.Drawing.Point(154, 49)
        Me.txtIndexPath.Name = "txtIndexPath"
        Me.txtIndexPath.Size = New System.Drawing.Size(705, 20)
        Me.txtIndexPath.TabIndex = 22
        '
        'lblIndexPath
        '
        Me.lblIndexPath.Location = New System.Drawing.Point(5, 52)
        Me.lblIndexPath.Name = "lblIndexPath"
        Me.lblIndexPath.Size = New System.Drawing.Size(123, 13)
        Me.lblIndexPath.TabIndex = 21
        Me.lblIndexPath.Text = "* Index Destination Path:"
        '
        'txtDocumentPath
        '
        Me.txtDocumentPath.Location = New System.Drawing.Point(154, 23)
        Me.txtDocumentPath.Name = "txtDocumentPath"
        Me.txtDocumentPath.Size = New System.Drawing.Size(705, 20)
        Me.txtDocumentPath.TabIndex = 1
        '
        'lblDocumentDestination
        '
        Me.lblDocumentDestination.Location = New System.Drawing.Point(5, 26)
        Me.lblDocumentDestination.Name = "lblDocumentDestination"
        Me.lblDocumentDestination.Size = New System.Drawing.Size(143, 13)
        Me.lblDocumentDestination.TabIndex = 6
        Me.lblDocumentDestination.Text = "* Document Destination Path:"
        '
        'HylandOnBaseModule
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Controls.Add(Me.grpMapping)
        Me.Controls.Add(Me.grpImageFeedConfig)
        Me.Name = "HylandOnBaseModule"
        Me.Size = New System.Drawing.Size(870, 483)
        CType(Me.grpMapping, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpMapping.ResumeLayout(False)
        CType(Me.gridIndexMappings, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbFacilities, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbSettingCategories, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbSettingNames, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpImageFeedConfig, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpImageFeedConfig.ResumeLayout(False)
        Me.grpImageFeedConfig.PerformLayout()
        CType(Me.txtFieldDelimiter.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dteStartDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dteStartDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboDocumentType.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtIndexPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDocumentPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents grpMapping As DevExpress.XtraEditors.GroupControl
    Friend WithEvents btnRemove As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents gridIndexMappings As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ricbFacilities As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents ricbSettingCategories As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents ricbSettingNames As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents grpImageFeedConfig As DevExpress.XtraEditors.GroupControl
    Friend WithEvents lblStartDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblDocumentType As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboDocumentType As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txtIndexPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblIndexPath As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtDocumentPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblDocumentDestination As DevExpress.XtraEditors.LabelControl
    Friend WithEvents dteStartDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents txtFieldDelimiter As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblFieldDelimiter As DevExpress.XtraEditors.LabelControl
End Class

﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class EditTreatmentAreaForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.treatmentAreaTypeComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.cancelButton = New DevExpress.XtraEditors.SimpleButton()
        Me.saveButton = New DevExpress.XtraEditors.SimpleButton()
        Me.enabledCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.chargeMasterTextBox = New System.Windows.Forms.TextBox()
        Me.nameTextBox = New System.Windows.Forms.TextBox()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.treatmentAreaTypeComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.enabledCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.treatmentAreaTypeComboBoxEdit)
        Me.LayoutControl1.Controls.Add(Me.cancelButton)
        Me.LayoutControl1.Controls.Add(Me.saveButton)
        Me.LayoutControl1.Controls.Add(Me.enabledCheckEdit)
        Me.LayoutControl1.Controls.Add(Me.chargeMasterTextBox)
        Me.LayoutControl1.Controls.Add(Me.nameTextBox)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(2, 2)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(341, 126)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'treatmentAreaTypeComboBoxEdit
        '
        Me.treatmentAreaTypeComboBoxEdit.Location = New System.Drawing.Point(83, 60)
        Me.treatmentAreaTypeComboBoxEdit.Name = "treatmentAreaTypeComboBoxEdit"
        Me.treatmentAreaTypeComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.treatmentAreaTypeComboBoxEdit.Properties.Items.AddRange(New Object() {"ED", "OBS"})
        Me.treatmentAreaTypeComboBoxEdit.Size = New System.Drawing.Size(86, 20)
        Me.treatmentAreaTypeComboBoxEdit.StyleController = Me.LayoutControl1
        Me.treatmentAreaTypeComboBoxEdit.TabIndex = 11
        '
        'cancelButton
        '
        Me.cancelButton.Location = New System.Drawing.Point(259, 84)
        Me.cancelButton.Name = "cancelButton"
        Me.cancelButton.Size = New System.Drawing.Size(70, 22)
        Me.cancelButton.StyleController = Me.LayoutControl1
        Me.cancelButton.TabIndex = 10
        Me.cancelButton.Text = "Cancel"
        '
        'saveButton
        '
        Me.saveButton.Location = New System.Drawing.Point(173, 84)
        Me.saveButton.Name = "saveButton"
        Me.saveButton.Size = New System.Drawing.Size(82, 22)
        Me.saveButton.StyleController = Me.LayoutControl1
        Me.saveButton.TabIndex = 9
        Me.saveButton.Text = "Save"
        '
        'enabledCheckEdit
        '
        Me.enabledCheckEdit.Location = New System.Drawing.Point(173, 60)
        Me.enabledCheckEdit.Name = "enabledCheckEdit"
        Me.enabledCheckEdit.Properties.Caption = "Enabled"
        Me.enabledCheckEdit.Size = New System.Drawing.Size(156, 19)
        Me.enabledCheckEdit.StyleController = Me.LayoutControl1
        Me.enabledCheckEdit.TabIndex = 8
        '
        'chargeMasterTextBox
        '
        Me.chargeMasterTextBox.Location = New System.Drawing.Point(83, 36)
        Me.chargeMasterTextBox.Name = "chargeMasterTextBox"
        Me.chargeMasterTextBox.Size = New System.Drawing.Size(246, 20)
        Me.chargeMasterTextBox.TabIndex = 5
        '
        'nameTextBox
        '
        Me.nameTextBox.Location = New System.Drawing.Point(83, 12)
        Me.nameTextBox.Name = "nameTextBox"
        Me.nameTextBox.Size = New System.Drawing.Size(246, 20)
        Me.nameTextBox.TabIndex = 4
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.EmptySpaceItem1, Me.LayoutControlItem2, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem7, Me.LayoutControlItem3})
        Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(341, 126)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.nameTextBox
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(321, 24)
        Me.LayoutControlItem1.Text = "Name"
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(68, 13)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 72)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(161, 34)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.chargeMasterTextBox
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(321, 24)
        Me.LayoutControlItem2.Text = "ChargeMaster"
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(68, 13)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.enabledCheckEdit
        Me.LayoutControlItem5.ControlAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.LayoutControlItem5.Location = New System.Drawing.Point(161, 48)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(160, 24)
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.saveButton
        Me.LayoutControlItem6.Location = New System.Drawing.Point(161, 72)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(86, 34)
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.cancelButton
        Me.LayoutControlItem7.Location = New System.Drawing.Point(247, 72)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(74, 34)
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.treatmentAreaTypeComboBoxEdit
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(161, 24)
        Me.LayoutControlItem3.Text = "Type"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(68, 13)
        '
        'EditTreatmentAreaForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(345, 130)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "EditTreatmentAreaForm"
        Me.Padding = New System.Windows.Forms.Padding(2)
        Me.Text = "EditTreatmentAreaForm"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.treatmentAreaTypeComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.enabledCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend Shadows WithEvents cancelButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents saveButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents enabledCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents chargeMasterTextBox As TextBox
    Friend WithEvents nameTextBox As TextBox
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents treatmentAreaTypeComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
End Class

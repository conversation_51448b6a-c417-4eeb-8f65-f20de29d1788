﻿Imports ECConfig

Public Class NoImageFeedModule
    Implements IImageFeedModule

    Const MODULE_NAME As String = "No Image Feed"
    Const MODULE_DESCRIPTION As String = "No Image Feed"
    Const IMAGE_FEED_MODE As String = ""

    Public ReadOnly Property ModuleName As String Implements IImageFeedModule.ModuleName
        Get
            Return MODULE_NAME
        End Get
    End Property

    Public ReadOnly Property ModuleDescription As String Implements IImageFeedModule.ModuleDescription
        Get
            Return MODULE_DESCRIPTION
        End Get
    End Property

    Public ReadOnly Property ImageFeedMode As String Implements IImageFeedModule.ImageFeedMode
        Get
            Return IMAGE_FEED_MODE
        End Get
    End Property

    Public Function LoadConfiguration() As Boolean Implements IImageFeedModule.LoadConfiguration
        Return True
    End Function

    Public Function LoadMappings() As Boolean Implements IImageFeedModule.LoadMappings
        Return True
    End Function

    Public Function SaveConfiguration() As Boolean Implements IImageFeedModule.SaveConfiguration
        Return True
    End Function

    Public Function SaveMappings() As Boolean Implements IImageFeedModule.SaveMappings
        Return True
    End Function

    Public Overrides Function ToString() As String
        Return MODULE_NAME
    End Function
End Class

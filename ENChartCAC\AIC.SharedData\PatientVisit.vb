﻿Namespace CuresAct

    Public Class PatientInfo
        Public Property FirstName As String = ""
        Public Property MiddleName As String = ""
        Public Property LastName As String = ""
        Public Property Suffix As String = ""
        Public Property DateOfBirth As String = ""
        Public Property MRN As String = ""
        Public Property FinancialClass As String = ""

        Public Property Visit As New PatientVisit
        'Public Property Visits As New List(Of PatientVisit)

    End Class

    Public Class PatientInfusion

        'Implements IComparable

        Public Startdate As Date?
        Public StartTime As String
        Public EndDate As Date?
        Public EndTime As String

        Property Medication As String

        Public ReadOnly Property StartDateTime As Date?
            Get
                Return CombineDateAndTime(Startdate, StartTime)
            End Get
        End Property

        'Public ReadOnly Property StopDateTime As Date?
        '    Get
        '        Return CombineDateAndTime(EndDate, EndTime)
        '    End Get
        'End Property

        Private Function CombineDateAndTime(targetDate As Date?,
                                     timeOfDay As String) As Date?
            Dim outDate As Date?
            Try
                If targetDate.HasValue Then
                    outDate = targetDate.Value.Date
                    If String.IsNullOrEmpty(timeOfDay) = False Then
                        Dim tod As Date = timeOfDay
                        outDate += tod.TimeOfDay
                    End If
                End If
            Catch ex As Exception
                Return Nothing
            End Try

            'If outDate.HasValue = False Then
            '    outDate = Date.MaxValue 'for sorting purposes
            'End If
            Return outDate
        End Function
    End Class

    Public Class PatientVisit
        Public Property VisitID As String
        Public Property VisitDate As Date
        Public Property EDChartStatus As String
        Public Property PhysChartStatus As String
        Public Property ObsChartStatus As String
        Public Property UserName As String
        Public Property Disposition As String
        Public Property TriagePhysician As String
        Public Property Items As New List(Of PatientVisitItem)
        Public Property Medications As New List(Of PatientInfusion)
    End Class

    Public Class PatientVisitItem
        Public Property CDM As String
        Public Property Chart As Integer
        Public Property Detail As String
        Public Property HCPCS As String
        Public Property ICD9 As String
        Public Property InsertDate As Date
        Public Property IsPhysicianTypeCode As Boolean
        Public Property Modifier As String
        Public Property Quantity As Integer
        Public Property CodeType As String
        Public Property TreatmentArea As String
    End Class

End Namespace




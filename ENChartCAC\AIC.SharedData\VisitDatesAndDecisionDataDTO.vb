﻿Public Class VisitDatesAndDecisionDataDTO

    'Property EdRequired As Boolean
    'Property ObsRequired As Boolean
    'Property DOS As Date?
    'Property ArrivalDate As Date?
    'Property DispostionDate As Date?
    'Property ObsArrivalDate As Date?
    'Property ObsDispostionDate As Date?


    'Property UserHasChangedEdData As Boolean
    'Property UserHasChangedObsData As Boolean

    'Property VisitStartDate As Date?
    'Property VisitEndDate As Date?

    Property DOS As AICDateTime
    Property Disposition As AICDateTime

    Property ObsStart As AICDateTime
    Property ObsEnd As AICDateTime

End Class

Public Class AICDateTime
    Public Sub New([Date] As Date, Time As String, Optional Nd As Boolean? = False)


        Me.Date = [Date]
        Me.Time = Time
        Me.ND = Nd
    End Sub
    Public Property [Date] As Date
    Public Property Time As String
    Public Property ND As Boolean?
End Class

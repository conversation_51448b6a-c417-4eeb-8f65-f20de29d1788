﻿Imports System.Xml
Imports System.IO
Imports McKesson.HIC.ColdFeed
Imports DevExpress.Xpo
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Menu
Imports DevExpress.Utils.Menu
Imports EnchartDOLib
Imports System.Data

Public Class HylandOnBaseModule
    Implements IImageFeedModule

    Const MODULE_NAME As String = "Hyland OnBase Image Feed"
    Const MODULE_DESCRIPTION As String = "Batches of image documents exported with single delimited index file containing chart information."
    Const IMAGE_FEED_MODE As String = "Hyland"

    Public ReadOnly Property ModuleName As String Implements IImageFeedModule.ModuleName
        Get
            Return MODULE_NAME
        End Get
    End Property

    Public ReadOnly Property ModuleDescription As String Implements IImageFeedModule.ModuleDescription
        Get
            Return MODULE_DESCRIPTION
        End Get
    End Property

    Public ReadOnly Property ImageFeedMode As String Implements IImageFeedModule.ImageFeedMode
        Get
            Return IMAGE_FEED_MODE
        End Get
    End Property

    Public Function LoadConfiguration() As Boolean Implements IImageFeedModule.LoadConfiguration
        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeIndexPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""DetailsOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")
        Dim nodeDocumentType As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFileType""]/value")

        txtDocumentPath.Text = nodeDocumentPath.InnerText
        txtIndexPath.Text = nodeIndexPath.InnerText
        dteStartDate.DateTime = CDate(nodeStartDate.InnerText)
        cboDocumentType.Text = nodeDocumentType.InnerText

        Return True
    End Function

    Public Function LoadMappings() As Boolean Implements IImageFeedModule.LoadMappings
        Try
            Dim maps As ColdFeedFieldMaps

            Using fs As New FileStream(".\ColdFeedFieldExportMaps.xml", FileMode.Open)
                Dim x As New Xml.Serialization.XmlSerializer(GetType(ColdFeedFieldMaps))
                maps = TryCast(x.Deserialize(fs), ColdFeedFieldMaps)
            End Using

            For Each cs As CustomSetting In GetCustomSettings(maps)
                If cs.key = "FieldDelimiter" Then txtFieldDelimiter.Text = cs.value
            Next

            LoadMappingsGrid(maps)

        Catch ex As Exception
            ' If there's a problem loading the file, notify the user but carry on since the most common problems are a missing file or missing settings,
            ' which will get created when saved anyway

            MessageBox.Show("There was a problem reading ColdFeedFieldExportMaps.xml")
        End Try

        Return True
    End Function

    Private Function GetCustomSettings(ByVal cffMaps As ColdFeedFieldMaps) As List(Of CustomSetting)
        If cffMaps.Count > 0 Then
            Return cffMaps(0).CustomSettings
        End If

        Return Nothing
    End Function

    Private Function GetCustomSettings(ByVal facilityOID As Integer, ByVal cffmaps As ColdFeedFieldMaps) As List(Of CustomSetting)
        For Each map As ColdFeedFieldMap In cffmaps
            If map.FacilityOID = facilityOID Then Return map.CustomSettings
        Next

        Return Nothing
    End Function

    Private Function GetFieldMapsDictionary(ByVal cff_maps As ColdFeedFieldMaps) As Dictionary(Of Integer, List(Of DTOColdFeedFieldMap))
        Dim return_dict As New Dictionary(Of Integer, List(Of DTOColdFeedFieldMap))

        For Each fm As ColdFeedFieldMap In cff_maps
            return_dict(fm.FacilityOID) = fm.FieldDefList
        Next

        Return return_dict
    End Function

    Private Sub LoadMappingsGrid(ByVal maps As ColdFeedFieldMaps)
        Dim fmd As Dictionary(Of Integer, List(Of DTOColdFeedFieldMap)) = GetFieldMapsDictionary(maps)
        Dim dt As New DataTable()

        Dim allColumns As New List(Of String)

        For Each mapsList As List(Of DTOColdFeedFieldMap) In fmd.Values
            For Each map As DTOColdFeedFieldMap In mapsList
                If (String.IsNullOrEmpty(map.OutPutFieldName)) OrElse (map.TableName.ToUpper = "HARDCODE") Then
                    If (Not allColumns.Contains(map.FieldName)) Then allColumns.Add(map.FieldName)
                Else
                    If (Not allColumns.Contains(map.OutPutFieldName)) Then allColumns.Add(map.OutPutFieldName)
                End If
            Next
        Next

        dt.Columns.Add(New DataColumn() With {.ColumnName = "FacilityOID", .Caption = "Facility OID"})
        dt.Columns.Add(New DataColumn() With {.ColumnName = "FacilityName", .Caption = "Facility Name"})
        dt.Columns.Add(New DataColumn() With {.ColumnName = "IndexPrefix", .Caption = "Index Prefix"})
        For Each colName As String In allColumns
            Dim dc As New DataColumn With {.ColumnName = $"Mapping{colName}", .Caption = colName}
            dt.Columns.Add(dc)
        Next

        Try
            Dim facilities As New XPCollection(Of DOFacility)

            For Each fac As DOFacility In facilities
                Dim dr As DataRow = dt.NewRow()

                dr("FacilityOID") = fac.Oid
                dr("FacilityName") = fac.LongName

                Dim customSettings As List(Of CustomSetting) = GetCustomSettings(fac.Oid, maps)
                If customSettings IsNot Nothing Then
                    For Each cs As CustomSetting In customSettings
                        If cs.key = "IndexFilePrefix" Then
                            dr("IndexPrefix") = cs.value
                            Exit For
                        End If
                    Next
                End If

                If fmd.ContainsKey(fac.Oid) Then
                    For Each map As DTOColdFeedFieldMap In fmd(fac.Oid)
                        If String.IsNullOrEmpty(map.OutPutFieldName) OrElse (map.TableName.ToUpper = "HARDCODE") Then
                            dr($"Mapping{map.FieldName}") = $"{map.TableName}.{map.FieldName}"
                        Else
                            dr($"Mapping{map.OutPutFieldName}") = $"{map.TableName}.{map.FieldName}"
                        End If
                    Next
                End If

                dt.Rows.Add(dr)
            Next

        Catch ex As Exception
            MessageBox.Show(ex.ToString)
        End Try

        GridView1.Columns.Clear()
        gridIndexMappings.DataSource = Nothing
        gridIndexMappings.DataSource = dt
        GridView1.RefreshData()

        ResetGridColumns()
    End Sub

    Private Sub ResetGridColumns()
        GridView1.PopulateColumns()
        GridView1.Columns(0).Visible = False
        GridView1.Columns(0).OptionsColumn.AllowEdit = False
        GridView1.Columns(1).OptionsColumn.AllowEdit = False
    End Sub

    Public Function SaveConfiguration() As Boolean Implements IImageFeedModule.SaveConfiguration
        If String.IsNullOrEmpty(txtDocumentPath.Text) Then
            MessageBox.Show("Document Path Cannot Be Empty", "Invalid Document Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtDocumentPath.Select()
            Return False
        End If

        If String.IsNullOrEmpty(txtIndexPath.Text) Then
            MessageBox.Show("Index Path Cannot Be Empty", "Invalid Index Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtIndexPath.Select()
            Return False
        End If

        If String.IsNullOrEmpty(txtFieldDelimiter.Text) Then
            MessageBox.Show("Field Delimiter Cannot Be Empty", "Invalid Field Delimiter", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtDocumentPath.Select()
            Return False
        End If

        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeIndexPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""DetailsOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")
        Dim nodeDocumentType As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFileType""]/value")
        Dim nodeImageFeedMode As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFeedMode""]/value")

        nodeDocumentPath.InnerText = txtDocumentPath.Text
        nodeIndexPath.InnerText = txtIndexPath.Text
        nodeStartDate.InnerText = dteStartDate.DateTime.ToString("yyyy-MM-dd")
        nodeDocumentType.InnerText = cboDocumentType.Text
        nodeImageFeedMode.InnerText = ImageFeedMode

        xdoc.Save(".\ColdFeedExport.exe.config")

        Return True
    End Function

    Public Function SaveMappings() As Boolean Implements IImageFeedModule.SaveMappings
        Return SaveMappingsToFile()
    End Function

    Private Function SaveMappingsToFile(Optional ByVal newColumnName As String = "") As Boolean
        Dim facilities As New XPCollection(Of DOFacility)
        Dim cffMaps As New ColdFeedFieldMaps

        For i As Integer = 0 To GridView1.DataRowCount - 1
            Dim fMap As New ColdFeedFieldMap

            fMap.FacilityOID = GridView1.GetRowCellValue(i, "FacilityOID").ToString()

            fMap.CustomSettings = New List(Of CustomSetting)
            fMap.FieldDefList = New List(Of DTOColdFeedFieldMap)

            fMap.CustomSettings.Add(New CustomSetting() With {.key = "FieldDelimiter", .value = txtFieldDelimiter.Text})
            fMap.CustomSettings.Add(New CustomSetting() With {.key = "IndexFilePrefix", .value = GridView1.GetRowCellValue(i, "IndexPrefix").ToString()})

            For Each col As GridColumn In GridView1.Columns
                If col.Name.StartsWith("colMapping") Then
                    Dim colVal As String = GridView1.GetRowCellValue(i, col).ToString
                    If Not String.IsNullOrEmpty(colVal) Then
                        Dim valParts() As String = colVal.Split(".")

                        If valParts.Length = 2 Then
                            Dim newMap As New DTOColdFeedFieldMap With {.TableName = valParts(0), .FieldName = valParts(1), .OutPutFieldName = col.Name.Substring(10)}
                            fMap.FieldDefList.Add(newMap)
                        Else
                            MessageBox.Show("Invalid mapping value. Should look like <TableName>.<FieldName>", "Mapping Value Problem")
                            Return False
                        End If
                    Else
                        MessageBox.Show("Mapping values can't be blank.", "Mapping Value Problem")
                        Return False
                    End If
                End If
            Next

            If Not String.IsNullOrEmpty(newColumnName) Then
                Dim newMap As New DTOColdFeedFieldMap
                newMap.TableName = ""
                newMap.FieldName = ""
                newMap.OutPutFieldName = newColumnName
                fMap.FieldDefList.Add(newMap)
            End If

            cffMaps.Add(fMap)
        Next i

        Using fs As New FileStream(".\ColdFeedFieldExportMaps.xml", FileMode.Create)
            Dim x As New Xml.Serialization.XmlSerializer(GetType(ColdFeedFieldMaps))
            x.Serialize(fs, cffMaps)
        End Using

        Return True
    End Function

    Private Function GetFieldMappingNames() As List(Of String)
        Dim returnList As New List(Of String)

        For Each col As GridColumn In GridView1.Columns
            If col.Name.StartsWith("colMapping") Then
                returnList.Add(col.Name.Substring(10))
            End If
        Next

        Return returnList
    End Function

    Public Overrides Function ToString() As String
        Return MODULE_NAME
    End Function

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Dim afd As New AddFieldDialog
        If afd.ShowDialog() = DialogResult.OK Then
            SaveMappingsToFile(afd.FieldMappingName)
            LoadMappings()
        End If
    End Sub

    Private Sub btnRemove_Click(sender As Object, e As EventArgs) Handles btnRemove.Click
        Dim rfd As New RemoveFieldDialog(GetFieldMappingNames)
        If rfd.ShowDialog = DialogResult.OK Then
            For Each col As GridColumn In GridView1.Columns
                If col.Name = $"colMapping{rfd.FieldMappingName}" Then
                    GridView1.Columns.Remove(col)
                    Exit For
                End If
            Next
        End If
        GridView1.RefreshData()
    End Sub
End Class

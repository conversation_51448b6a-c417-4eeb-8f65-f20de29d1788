Imports System.IO
Imports System.Net
Imports EnchartDOLib

Imports System.Text
Imports System.Threading.Tasks
Imports System.Runtime.Serialization
Imports System.Xml.Serialization
Imports System.Data

Public Class WebServiceReportViewer
    Inherits BaseReportViewer
    Implements IReportViewer

    Public Property ReqReport As IRequestReport
    Private responseFormat As String = Nothing
    Public Sub New(reqReport As IRequestReport)

        ' This call is required by the designer.
        InitializeComponent()
        Me.ReqReport = reqReport

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Public Sub New(reqReport As IRequestReport, responseFormat As String)

        ' This call is required by the designer.
        InitializeComponent()
        Me.ReqReport = reqReport
        Me.responseFormat = responseFormat

    End Sub

    Public Overrides Sub Show_Report(facilityReport As DOFacilityReport, treatment_area As String, mindate As Date, maxdate As Date, datetype As String, pf As ReportStartup)
        ParentReportStartup = pf

        Dim params As New Dictionary(Of String, Object)
        params("requestID") = Guid.NewGuid.ToString
        params("userID") = pf.CurrentUser.Oid
        params("facility") = facilityReport.ReportCategory.Facility.Oid
        params("treatmentArea") = If(Not String.IsNullOrEmpty(treatment_area), treatment_area, "ALL")
        params("reportFilename") = If(Not String.IsNullOrEmpty(facilityReport.ReportFilename), facilityReport.ReportFilename, "")
        params("reportID") = facilityReport.Oid
        params("minDate") = mindate
        params("maxDate") = maxdate
        params("dateType") = If(Not String.IsNullOrEmpty(datetype), datetype, "DOS")

        'If responseFormat IsNot Nothing Then
        '    params("ResponseFormat") = "PDF"
        'End If


        If facilityReport.ReportType.IsObservationReport Then
            params("reportType") = "OBS"
        ElseIf facilityReport.ReportType.IsPhysicianReport Then
            params("reportType") = "PHYSICIAN"
        Else
            params("reportType") = "FACILITY"
        End If


        RegisterButtonHandlers()
        BuildSelectionCriteriaString(params)
        AuditLogger.Reports.AdminReportsLogRun(SelectionCriteria)

        Dim url As String = GetServiceUrl()
        If Not String.IsNullOrEmpty(url) Then
            Dim tempReportFileName As String
            Try
                tempReportFileName = RequestReportFromESS(ReqReport, url, params)
            Catch web_ex As WebException
                Dim msg As String =
                     "There was an error receiving the report from the report server." & vbCrLf & vbCrLf &
                     "Please call the Allscripts Intelligent Coding Support team at **************, Option 8 to report this error." & vbCrLf & vbCrLf &
                     "Error code: AICRPT101" & vbCrLf & vbCrLf &
                     "Reason: " & web_ex.Message
                MessageBox.Show(msg, "Error Receiving Report", MessageBoxButtons.OK, MessageBoxIcon.Error)
                'ECLog.WriteEntry(msg, TraceEventType.Error)
                Return
            Catch ex As Exception
                Dim msg As String =
                     "There was an error receiving the report from the report server." & vbCrLf & vbCrLf &
                     "Please wait a minute and try running this report again. Then, if this error persists, please call the Allscripts Intelligent Coding Support team at **************, Option 8 to report this error." & vbCrLf & vbCrLf &
                     "Error code : AICRPT102" & vbCrLf & vbCrLf &
                     "Reason: " & ex.Message
                MessageBox.Show(msg, "Error Receiving Report", MessageBoxButtons.OK, MessageBoxIcon.Error)
                '  ECLog.WriteEntry(msg, TraceEventType.Error)
                Return
            End Try

            If tempReportFileName = Nothing Then
                OpenReportAsDataSet(ds)

            ElseIf Not String.IsNullOrEmpty(tempReportFileName) AndAlso File.Exists(tempReportFileName) Then
                If responseFormat Is Nothing Then

                    LoadReport(tempReportFileName)
                    File.Delete(tempReportFileName)
                Else
                    ' Dim blah = System.IO.MemoryMappedFiles.MemoryMappedFile.CreateFromFile(tempReportFileName)
                    'OpenPdfDoc(tempReportFileName)
                    ''OpenPdfDoc(tempReportFileName)
                    'File.Delete(tempReportFileName)
                End If

            Else
                Dim msg As String = "There was an error loading the report that was returned from the server." & vbCrLf & vbCrLf &
                "Please call the Allscripts Intelligent Coding Support team at **************, Option 8 to report this error." & vbCrLf & vbCrLf &
                "Error code : AICRPT201"
                MessageBox.Show(msg, "Error Loading Report", MessageBoxButtons.OK, MessageBoxIcon.Error)
                '   ECLog.WriteEntry(msg, TraceEventType.Error)
                Return
            End If
        Else
            Dim msg As String = "There was an error communicating with the report server." & vbCrLf & vbCrLf &
                 "Please call the Allscripts Intelligent Coding Support team at **************, Option 8 to report this error." & vbCrLf & vbCrLf &
                 "Error code : AICRPT301"
            MessageBox.Show(msg, "Error Requesting Report", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '   ECLog.WriteEntry(msg, TraceEventType.Error)
        End If
    End Sub

    Private Sub OpenReportAsDataSet(ds As DataSet)
        Dim f As New DatasetViewer(ds)
        f.Show()
    End Sub

    Public Function OpenPdfDoc(docpath) As Boolean
        Try
            Dim viewer As New PdfViewerForm
            viewer.StartPosition = FormStartPosition.CenterScreen
            viewer.LoadDoc(docpath)
            'viewer.LoadDo
            viewer.StartPosition = FormStartPosition.CenterParent
            viewer.Show(Me)
            'viewer.Close()
            Return True
        Catch ex As Exception
            Dim msg = $"An error occured trying to open '{docpath}'. {vbCr}{vbCrLf}Error: {ex.Message}"
            MessageBox.Show(msg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try

    End Function

    Private Sub BuildSelectionCriteriaString(ByVal parameters As Dictionary(Of String, Object))
        Dim sb As New StringBuilder

        With sb
            For Each key As String In parameters.Keys
                .Append(String.Format("{0}:{1}, ", key, parameters(key)))
            Next
            .Remove(sb.Length - 2, 2)
        End With

        SelectionCriteria = sb.ToString
    End Sub

    Private Function RequestReport(ByVal url As String, ByVal queryParameterList As Dictionary(Of String, Object)) As String
        Dim returnFilename As String = ""
        Dim theUri As New Uri(url + "?" + AppendQueryParameters(queryParameterList).Remove(0, 1))
        Dim httpRequest As HttpWebRequest = DirectCast(HttpWebRequest.Create(theUri), HttpWebRequest)

        Dim responsesize As Long = httpRequest.GetResponse().ContentLength
        If (responsesize > 0) Then
            Dim buffer As Byte() = New Byte(responsesize) {}

            Dim str As Stream = httpRequest.GetResponse().GetResponseStream()

            Dim bytesread As Int32
            Dim ms As New MemoryStream()
            Dim byteslefttoread = responsesize
            Do
                bytesread = str.Read(buffer, 0, buffer.Length)
                ms.Write(buffer, 0, bytesread)
                byteslefttoread = byteslefttoread - bytesread
            Loop While byteslefttoread > 0

            ms.Position = 0

            returnFilename = CreateTempReportName(queryParameterList("requestID"))
            Dim streamwriter As FileStream = New FileStream(returnFilename, FileMode.Create, FileAccess.ReadWrite)
            streamwriter.Write(ms.ToArray(), 0, ms.Length)
            ms.Close()
            streamwriter.Close()
            str.Close()
        End If
        Return returnFilename
    End Function

    Private ds As New DataSet

    Public Function RequestReportFromESS(repRequest As IRequestReport, ByVal url As String, ByVal queryParameterList As Dictionary(Of String, Object)) As String
        'Passed URL is not used ... 02.20.19
        Dim returnedBytes As Byte() = ReqReport.RequestReport(url, queryParameterList)


        Dim memStream As New MemoryStream '= New FileStream(returnfilename, FileMode.Create, FileAccess.ReadWrite)
        Dim rf As String = ""
        ' If queryParameterList.TryGetValue("ResponseFormat", rf) Then
        '       If rf = "PDF" Then
        Dim returnfilename = CreateTempReportName(queryParameterList("requestID"))
            'Dim ds2 As New DataSet
            'ds2.ReadXmlSchema(returnedBytes, XmlWriteMode.DiffGram)
            Dim streamwriter As FileStream = New FileStream(returnfilename, FileMode.Create, FileAccess.ReadWrite)
            streamwriter.Write(returnedBytes, 0, returnedBytes.Length)
            streamwriter.Close()

            Return returnfilename
        '     End If
        '    End If

        memStream.Write(returnedBytes, 0, returnedBytes.Length)
        memStream.Position = 0
        ds.ReadXml(memStream) ', XmlWriteMode.DiffGram)
        Return Nothing


    End Function



    Private Function AppendQueryParameters(ByVal queryParameters As Dictionary(Of String, Object)) As String
        Dim returnString As String = ""
        For Each key As KeyValuePair(Of String, Object) In queryParameters
            returnString = returnString + String.Format("&{0}={1}", key.Key, key.Value)
        Next
        Return returnString
    End Function

    Private Function CreateTempReportName(requestID As String) As String
        If Not Directory.Exists(Application.StartupPath & "\reports\") Then
            Directory.CreateDirectory(Application.StartupPath & "\reports\")
        End If
        Return Application.StartupPath & "\reports\" & requestID & "_temp.rpt"
    End Function

    Private Function GetServiceUrl()
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("ServiceURL")
        Return setting.SettingValue + "Report"
    End Function

End Class
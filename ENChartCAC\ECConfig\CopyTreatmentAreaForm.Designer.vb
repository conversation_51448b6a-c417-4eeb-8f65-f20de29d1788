﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CopyTreatmentAreaForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.cboFrom = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.fromTreatmeantArea = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        Me.toTA = New DevExpress.XtraEditors.TextEdit()
        Me.progressControl = New DevExpress.XtraEditors.ProgressBarControl()
        Me.ceImportCDM = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.cboTo = New DevExpress.XtraEditors.ComboBoxEdit()
        CType(Me.cboFrom.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.fromTreatmeantArea.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.toTA.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.progressControl.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.ceImportCDM.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.cboTo.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(36, 160)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(110, 16)
        Me.LabelControl2.TabIndex = 7
        Me.LabelControl2.Text = "To Treatment Area"
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(36, 78)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(125, 16)
        Me.LabelControl1.TabIndex = 5
        Me.LabelControl1.Text = "From Treatment Area"
        '
        'cboFrom
        '
        Me.cboFrom.Location = New System.Drawing.Point(209, 34)
        Me.cboFrom.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.cboFrom.Name = "cboFrom"
        Me.cboFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboFrom.Properties.ImmediatePopup = True
        Me.cboFrom.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboFrom.Size = New System.Drawing.Size(367, 22)
        Me.cboFrom.TabIndex = 0
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(36, 38)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(73, 16)
        Me.LabelControl3.TabIndex = 9
        Me.LabelControl3.Text = "From Facility"
        '
        'fromTreatmeantArea
        '
        Me.fromTreatmeantArea.Location = New System.Drawing.Point(209, 74)
        Me.fromTreatmeantArea.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.fromTreatmeantArea.Name = "fromTreatmeantArea"
        Me.fromTreatmeantArea.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.fromTreatmeantArea.Properties.ImmediatePopup = True
        Me.fromTreatmeantArea.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.fromTreatmeantArea.Size = New System.Drawing.Size(367, 22)
        Me.fromTreatmeantArea.TabIndex = 1
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(476, 245)
        Me.btnCancel.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(100, 28)
        Me.btnCancel.TabIndex = 5
        Me.btnCancel.Text = "Exit"
        '
        'btnOK
        '
        Me.btnOK.Location = New System.Drawing.Point(349, 245)
        Me.btnOK.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(100, 28)
        Me.btnOK.TabIndex = 4
        Me.btnOK.Text = "Copy"
        '
        'toTA
        '
        Me.toTA.Location = New System.Drawing.Point(209, 151)
        Me.toTA.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.toTA.Name = "toTA"
        Me.toTA.Size = New System.Drawing.Size(367, 22)
        Me.toTA.TabIndex = 3
        '
        'progressControl
        '
        Me.progressControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.progressControl.EditValue = "0"
        Me.progressControl.Location = New System.Drawing.Point(36, 201)
        Me.progressControl.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.progressControl.Name = "progressControl"
        Me.progressControl.Properties.EndColor = System.Drawing.Color.ForestGreen
        Me.progressControl.Properties.ProgressViewStyle = DevExpress.XtraEditors.Controls.ProgressViewStyle.Solid
        Me.progressControl.Properties.StartColor = System.Drawing.Color.Red
        Me.progressControl.Size = New System.Drawing.Size(540, 22)
        Me.progressControl.TabIndex = 64
        '
        'ceImportCDM
        '
        Me.ceImportCDM.Location = New System.Drawing.Point(33, 242)
        Me.ceImportCDM.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ceImportCDM.Name = "ceImportCDM"
        Me.ceImportCDM.Properties.Caption = "Import CDM Table Records"
        Me.ceImportCDM.Size = New System.Drawing.Size(268, 20)
        Me.ceImportCDM.TabIndex = 65
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(36, 117)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(58, 16)
        Me.LabelControl4.TabIndex = 67
        Me.LabelControl4.Text = "To Facility"
        '
        'cboTo
        '
        Me.cboTo.Location = New System.Drawing.Point(209, 113)
        Me.cboTo.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.cboTo.Name = "cboTo"
        Me.cboTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboTo.Properties.ImmediatePopup = True
        Me.cboTo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboTo.Size = New System.Drawing.Size(367, 22)
        Me.cboTo.TabIndex = 2
        '
        'CopyTreatmentAreaForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(628, 287)
        Me.Controls.Add(Me.LabelControl4)
        Me.Controls.Add(Me.cboTo)
        Me.Controls.Add(Me.ceImportCDM)
        Me.Controls.Add(Me.progressControl)
        Me.Controls.Add(Me.toTA)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.LabelControl3)
        Me.Controls.Add(Me.fromTreatmeantArea)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.cboFrom)
        Me.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Name = "CopyTreatmentAreaForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "TA Copy"
        CType(Me.cboFrom.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.fromTreatmeantArea.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.toTA.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.progressControl.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.ceImportCDM.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.cboTo.Properties,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)
        Me.PerformLayout

End Sub
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboFrom As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents fromTreatmeantArea As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents toTA As DevExpress.XtraEditors.TextEdit
    Friend WithEvents progressControl As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents ceImportCDM As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboTo As DevExpress.XtraEditors.ComboBoxEdit
End Class

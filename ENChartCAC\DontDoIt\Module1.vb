﻿Imports EnchartDOLib

Module Module1

    Sub Main()
        EnchartDOLib.ConnectToECDataBase()

        Dim micAdmin = DOUser.GetUserByID("admin")
        If micAdmin IsNot Nothing Then
            micAdmin.HashedPassword = "3GSTwQ0/y66ahg+dBLuPqEBB73EYIUDhfHVBIt1sQAy9A2uG57iXGGq9aUYYGpbR6sjRD6PDWc8GvSGlB6693iX8R5kI8zKAAr8aO8fHy4jTrWQnpuuunF4kCIE8KOzJOLnlu7KJKAi7v/sZXj/4t7QZ"
            micAdmin.Save()
        End If
    End Sub

End Module

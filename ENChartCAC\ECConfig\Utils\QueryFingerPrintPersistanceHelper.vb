﻿Imports System.IO
Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Imports Newtonsoft.Json

Public Class QueryFingerPrintPersistanceHelper

    Private Const FilterString As String = "Query Fingerprints File|*.json"
    Private Const DefaultFileName As String = "QueryFingerprints.json"
    Public Shared Sub ExportFingerPrints()
        Try
            Dim fpList As New XPCollection(Of DOQueryFingerPrint) '(Session.DefaultSession)
            Dim filePath As String = GetFilePath()

            If Not String.IsNullOrWhiteSpace(filePath) Then
                WriteFingerPrintsToFile(fpList, filePath)
                MessageBox.Show($"{fpList.Count()} fingerprints exported successfully to {filePath}", "Status")
            End If
        Catch ex As Exception
            MessageBox.Show($"An error occurred: {ex.Message}", "Error")
        End Try
    End Sub

    Private Shared Function GetFilePath() As String
        Using sfd As New SaveFileDialog
            sfd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            '    sfd.InitialDirectory = Environment.SpecialFolder.MyDocuments '
            sfd.Filter = FilterString
            sfd.FileName = DefaultFileName

            Return If(sfd.ShowDialog() = DialogResult.OK, sfd.FileName, String.Empty)
        End Using
    End Function

    Private Shared Sub WriteFingerPrintsToFile(fpList As XPCollection(Of DOQueryFingerPrint), filePath As String)
        'Convert fplist to list of poco objects
        Dim fplistDTO As New List(Of DOQueryFingerPrintDTO)
        For Each fp In fpList
            fplistDTO.Add(New DOQueryFingerPrintDTO(fp))
        Next
        'serialize to disk as json
        Using fs As FileStream = New FileStream(filePath, FileMode.Create, FileAccess.Write)
            Dim json As String = JsonConvert.SerializeObject(fplistDTO, Formatting.Indented)
            Using sw As New StreamWriter(fs)
                sw.Write(json)
            End Using
        End Using
    End Sub

    Public Shared Sub ImportFingerPrints()
        Try
            Dim filePath As String = GetFilePathToOpen()
            If Not String.IsNullOrWhiteSpace(filePath) Then
                Dim fpListDTO As List(Of DOQueryFingerPrintDTO) = ReadFingerPrintsFromFile(filePath)
                ' Convert DTOs to DOQueryFingerPrint if needed
                Dim fpList As New List(Of DOQueryFingerPrint)
                For Each fpDTO In fpListDTO
                    Dim fp As DOQueryFingerPrint = Session.DefaultSession.FindObject(Of DOQueryFingerPrint)(CriteriaOperator.Parse($"[Oid] = '{fpDTO.Oid}'"))
                    If fp Is Nothing Then
                        'fp = New DOQueryFingerPrint(Session.DefaultSession)
                        fp = New DOQueryFingerPrint(fpDTO)
                    Else fp.MapFrom(fpDTO)
                    End If
                    fpList.Add(fp)
                Next
                'Save to DB
                Session.DefaultSession.Save(fpList)

                MessageBox.Show("File imported successfully", "Status")
            End If
        Catch ex As Exception
            MessageBox.Show($"An error occurred: {ex.Message}", "Error")
        End Try
    End Sub

    Private Shared Function GetFilePathToOpen() As String
        Const FilterString As String = "Query Fingerprints File|*.json"
        Using ofd As New OpenFileDialog
            ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            ofd.Filter = FilterString
            ofd.FileName = DefaultFileName

            Return If(ofd.ShowDialog() = DialogResult.OK, ofd.FileName, String.Empty)
        End Using
    End Function

    Private Shared Function ReadFingerPrintsFromFile(filePath As String) As List(Of DOQueryFingerPrintDTO)
        Using fs As FileStream = New FileStream(filePath, FileMode.Open, FileAccess.Read)
            Using sr As New StreamReader(fs)
                Dim json As String = sr.ReadToEnd()
                Dim fingerprints = JsonConvert.DeserializeObject(Of DOQueryFingerPrintDTO())(json)
                Return fingerprints.ToList()
            End Using
        End Using
    End Function
End Class

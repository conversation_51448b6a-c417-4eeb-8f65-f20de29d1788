Public Class DoingSomethingMsgForm 

    Private Sub DoingSomethingMsgForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Public Sub UpDateProgress(ByVal msg As String, ByVal progress As Short)
        Me.lbMsg.Text = msg
        Me.ProgressBar.Position = progress
        Me.lbMsg.Refresh()
        Me.ProgressBar.Refresh()
    End Sub

    Public Sub Progress(ByVal p As Short)
        Me.ProgressBar.Position = p
        Me.ProgressBar.Refresh()
    End Sub
End Class
Imports DevExpress.Xpo





Public Class formEditControlOverrides

    Private Sub formEditControlOverrides_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Session.DefaultSession.BeginTransaction()
    End Sub

    Private Sub btnDone_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDone.Click
        Session.DefaultSession.CommitTransaction()
        Me.Close()
        'Session.DefaultSession.RollbackTransaction()
    End Sub



    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Session.DefaultSession.RollbackTransaction()
        Me.Close()
    End Sub
End Class
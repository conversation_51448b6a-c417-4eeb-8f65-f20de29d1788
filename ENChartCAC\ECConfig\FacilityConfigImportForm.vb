Imports ECConfig.MainForm
Imports System.IO
Imports System.Xml
Imports System.Xml.Serialization

Imports DevExpress.Xpo
Imports Timeout
Imports ENChartCAC
Imports System.Threading.Tasks

Public Class FacilityConfigImportForm

    Private Sub ButtonEdit1_ButtonClick(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles ButtonEdit1.ButtonClick
        GetFileToLoad()
    End Sub

    Private Sub GetFileToLoad()
        Dim ofd As New OpenFileDialog

        ofd.DefaultExt = "FacilityConfig.hfc"
        ofd.Filter = "HIC Facility Config (*.hfc) | *.hfc"
        ' ofd.InitialDirectory = ECGlobals.ComboBoxListDir
        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub

        'Dim ffile As New FileInfo(ofd.FileName)
        'ofd.Dispose() '???

        Me.ButtonEdit1.EditValue = ofd.FileName
        ofd.Dispose()
    End Sub

    Private Sub ButtonEdit1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonEdit1.DoubleClick
        GetFileToLoad()
    End Sub

    Private Sub ButtonEdit1_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEdit1.EditValueChanged
        SelectionsChanged()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
    Private Sub FacilityConfigImportForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim fcol As New XPCollection(GetType(DOFacility))
        fcol.Reload()
        If fcol IsNot Nothing Then
            For Each facility In fcol
                cboTo.Properties.Items.Add(facility)
            Next
        End If

    End Sub

    Private Sub SelectionsChanged()
        If Me.cboTo.EditValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(Me.ButtonEdit1.EditValue) Then
            Me.btnOK.Enabled = True
        Else
            Me.btnOK.Enabled = False
        End If
    End Sub

    Private Sub cboTo_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboTo.SelectedIndexChanged
        SelectionsChanged()
    End Sub

    'Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
    '    Try
    '        Dim configFileToImport As New FileInfo(Me.ButtonEdit1.EditValue)
    '        If Not configFileToImport.Exists Then
    '            MessageBox.Show(String.Format("File '{0}' can't be found. Please try again", configFileToImport.FullName), "Unexpected Error", MessageBoxButtons.OK)
    '            Return
    '        End If

    '        Dim toFac As DOFacility = cboTo.EditValue
    '        Dim ConfigInstance As DOConfigInstance = toFac.ConfigInstanceVersion
    '        Dim facilityProxyObj As FacilityProxy '(toFac, Nothing, Nothing)

    '        Using fs As New FileStream(configFileToImport.FullName, FileMode.Open)
    '            Dim xmlSettings As New XmlReaderSettings
    '            'Am hoping this next line will satisfy vericode XXE attack warning ...
    '            ' xmlSettings.ProhibitDtd = True (jjc - this is now obsolete... changed to the below line)
    '            xmlSettings.DtdProcessing = DtdProcessing.Prohibit

    '            Using reader = XmlReader.Create(fs, xmlSettings)
    '                Dim x As New Xml.Serialization.XmlSerializer(GetType(FacilityProxy))
    '                facilityProxyObj = x.Deserialize(reader)
    '            End Using
    '        End Using

    '        Me.btnOK.Enabled = False
    '        Me.btnCancel.Enabled = False
    '        Me.ButtonEdit1.Enabled = False
    '        Me.cboTo.Enabled = False

    '        BasicTimeoutWatcher.Suspend()

    '        Me.Cursor = Cursors.WaitCursor
    '        Utils.LoadConfigFromProxy(toFac, ConfigInstance, facilityProxyObj, AddressOf UpDateProgress, ceImportCDM.Checked, ceImportReports.Checked)

    '        'Im not certain that this will reload everything....
    '        ECGlobals.CurrentFacility.Reload()

    '        Cursor.Current = Cursors.Default
    '        BasicTimeoutWatcher.Resume()
    '        MessageBox.Show(Me, "Configuration successfully imported!", "Be sure to test!", MessageBoxButtons.OK, MessageBoxIcon.Information)
    '        Me.Close()
    '    Finally
    '        BasicTimeoutWatcher.Resume()
    '    End Try

    'End Sub

    Private Async Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Try
            Dim configFileToImport As New FileInfo(Me.ButtonEdit1.EditValue)
            If Not configFileToImport.Exists Then
                MessageBox.Show(String.Format("File '{0}' can't be found. Please try again", configFileToImport.FullName), "Unexpected Error", MessageBoxButtons.OK)
                Return
            End If

            Dim toFac As DOFacility = cboTo.EditValue
            Dim ConfigInstance As DOConfigInstance = toFac.ConfigInstanceVersion
            Dim facilityProxyObj As FacilityProxy '(toFac, Nothing, Nothing)

            Using fs As New FileStream(configFileToImport.FullName, FileMode.Open)
                Dim xmlSettings As New XmlReaderSettings
                'Am hoping this next line will satisfy vericode XXE attack warning ...
                ' xmlSettings.ProhibitDtd = True (jjc - this is now obsolete... changed to the below line)
                xmlSettings.DtdProcessing = DtdProcessing.Prohibit

                Using reader = XmlReader.Create(fs, xmlSettings)
                    Dim x As New Xml.Serialization.XmlSerializer(GetType(FacilityProxy))
                    facilityProxyObj = x.Deserialize(reader)
                End Using
            End Using

            Me.btnOK.Enabled = False
            Me.btnCancel.Enabled = False
            Me.ButtonEdit1.Enabled = False
            Me.cboTo.Enabled = False

            BasicTimeoutWatcher.Suspend()

            Me.Cursor = Cursors.WaitCursor
            Dim progress As New Progress(Of Integer)(AddressOf UpDateProgress)
            'Dim tt = Task.Run(Sub() Utils.LoadConfigFromProxy(toFac, ConfigInstance, facilityProxyObj, AddressOf UpDateProgress, ceImportCDM.Checked, ceImportReports.Checked))
            Await Task.Run(Sub() Utils.LoadConfigFromProxy(toFac, ConfigInstance, facilityProxyObj, progress, ceImportCDM.Checked, ceImportReports.Checked))
            'Await tt

            'Im not certain that this will reload everything....
            ECGlobals.CurrentFacility.Reload()

                                  Cursor.Current = Cursors.Default
                                  BasicTimeoutWatcher.Resume()
                                  MessageBox.Show(Me, "Configuration successfully imported!", "Be sure to test!", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                  Me.Close()
        Finally
            BasicTimeoutWatcher.Resume()
        End Try

    End Sub

    Dim progressRefreshTimer As New Stopwatch
    Public Sub UpDateProgress(ByVal pDone As Integer)
        'If progressRefreshTimer.IsRunning = False Then
        '    progressRefreshTimer.Start()
        'End If

        Me.progressControl.Position = pDone
        'If progressRefreshTimer.Elapsed.TotalSeconds >= 1 Then
        '    progressRefreshTimer.Reset()
        '    '   Me.Refresh()
        '    'Application.DoEvents()
        'End If
    End Sub

End Class
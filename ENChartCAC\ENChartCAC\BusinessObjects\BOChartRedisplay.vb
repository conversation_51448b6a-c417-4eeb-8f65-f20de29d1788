﻿Imports DevExpress.Xpo
Imports EnchartDOLib
Public Class BOChartRedisplay
    Inherits BusinessObject
    Public Overridable Property Chart As Integer
    Public Overridable Property ItemName As String
    Public Overridable Property ItemValue As String

    Public Shared Widening Operator CType(ByVal boCR As BOChartRedisplay) As DOChartRedisplay
        If boCR Is Nothing Then Return Nothing
        Dim doCR As DOChartRedisplay
        If boCR.Oid > NEW_OBJ_OID Then
            doCR = XpoDefault.Session.GetObjectByKey(Of DOChartRedisplay)(boCR.Oid)
        Else
            doCR = New DOChartRedisplay() With {.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCR.Chart)}
        End If

        doCR.ItemName = boCR.ItemName
        doCR.ItemValue = boCR.ItemValue
        Return doCR
    End Operator

    Public Shared Narrowing Operator CType(ByVal doCR As DOChartRedisplay) As BOChartRedisplay
        If doCR Is Nothing Then Return Nothing
        Dim boCR As New BOChartRedisplay
        boCR.Oid = doCR.Oid
        boCR.Chart = doCR.Chart.Oid
        boCR.ItemName = doCR.ItemName
        boCR.ItemValue = doCR.ItemValue
        Return boCR
    End Operator
End Class

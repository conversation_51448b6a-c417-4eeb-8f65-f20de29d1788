﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>%24/VIC/Experimental/SourceCode/CodingReport/CodingReport</SccProjectName>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <SccAuxPath>http://tfs.paragon.mckesson.com:8080/tfs/collection1-eis</SccAuxPath>
    <SccLocalPath>.</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <StartupObject>Sub Main</StartupObject>
    <MyType>WindowsForms</MyType>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>false</UseWPF>
    
    <!--<Configurations>Debug;Release</Configurations>
    <Platforms>AnyCPU;x86</Platforms>-->
    <!--<GenerateAssemblyInfo>false</GenerateAssemblyInfo>-->
    <!--<ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>-->
  </PropertyGroup>

  <!--<ItemGroup>
    <PackageReference Include="DevExpress.Win.Design" Version="22.1.*-*" />
  </ItemGroup>-->
  
   <ItemGroup>
    <PackageReference Include="DevExpress.Reporting.Core" Version="22.1.6" />
    <PackageReference Include="DevExpress.Win.Reporting" Version="22.1.6" />
    <PackageReference Include="Microsoft.CodeAnalysis" Version="4.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ENChartCAC\EnchartDOLib\EnchartDOLib.vbproj" />
  </ItemGroup>

  <!--<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>-->
  
  <!--<ItemGroup>
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>-->
  <!--<ItemGroup>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Update="RetrievalMessage.Designer.vb">
      <DependentUpon>RetrievalMessage.vb</DependentUpon>
    </Compile>
    <Compile Update="XtraCodingReport.Designer.vb">
      <DependentUpon>XtraCodingReport.vb</DependentUpon>
    </Compile>
    <Compile Update="XtraCodingReport.vb">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>-->

  <ItemGroup>
    <Content Include="appsettings.json.old" />
    <Content Include="ChangeNotes.txt" />
    <Content Include="document.ico" />
  </ItemGroup>
 
  <ItemGroup>
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Remove="CodingReportDataSet1.Designer.vb" />
    <Compile Remove="CodingReportDataSet1.vb" />
    <Compile Remove="My Project\AssemblyInfo.vb" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Form1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

 <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
</Project>
﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="ImageOutputPath" Type="System.String" Scope="Application">
      <Value Profile="(Default)">c:\apps\eccoder\ColdFeedExportOutFiles</Value>
    </Setting>
    <Setting Name="DetailsOutputPath" Type="System.String" Scope="Application">
      <Value Profile="(Default)">c:\apps\eccoder\ColdFeedExportOutFiles</Value>
    </Setting>
    <Setting Name="TriggerMethod" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Exportstatus</Value>
    </Setting>
    <Setting Name="BatchLabel" Type="System.String" Scope="Application">
      <Value Profile="(Default)">HIC Coldfeed</Value>
    </Setting>
    <Setting Name="ImageFileType" Type="System.String" Scope="Application">
      <Value Profile="(Default)">pdf</Value>
    </Setting>
    <Setting Name="StartDate" Type="System.String" Scope="Application">
      <Value Profile="(Default)">2009-04-01</Value>
    </Setting>
    <Setting Name="MultiExport" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="DateFormat" Type="System.String" Scope="Application">
      <Value Profile="(Default)">yyyyMMdd hh:mmtt</Value>
    </Setting>
    <Setting Name="FacilityID" Type="System.String" Scope="Application">
      <Value Profile="(Default)">all</Value>
    </Setting>
    <Setting Name="UseFullPathPData" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ImageFeedMode" Type="System.String" Scope="Application">
      <Value Profile="(Default)">OneContent</Value>
    </Setting>
  </Settings>
</SettingsFile>
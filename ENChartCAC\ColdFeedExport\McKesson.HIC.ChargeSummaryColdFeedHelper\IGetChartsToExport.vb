Imports EnchartDOLib
Imports System.Collections.Specialized
Imports System.Data
Imports System.Reflection

Namespace ColdFeed
    Public Interface IGetChartsToExport
        Property TriggerMethod() As String
        'return the connection object from the devExpres dataLayer
        'Function GetCharts(ByVal dtoInputParams As DTOGetChartsInputParams) As ChartWrapperDictionary
        Function GetCharts(ByVal facility As DOFacility, ByVal triggerMethod As String) As ChartWrapperDictionary
        Function GetdbConnection() As IDbConnection

        Property CFHelper() As ColdFeedHelper

        Function ShouldExport(ByVal chartWrapper As ChartObjWrapper, ByVal IsPhysEnabled As Boolean) As Boolean

        'Function GetCharts(ByVal pFacilityOID As Integer, ByVal pQuery As String) As ChartWrapperDictionary
    End Interface
End Namespace

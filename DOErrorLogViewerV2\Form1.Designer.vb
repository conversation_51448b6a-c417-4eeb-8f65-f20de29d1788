﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim ToolTipSeparatorItem1 As DevExpress.Utils.ToolTipSeparatorItem = New DevExpress.Utils.ToolTipSeparatorItem()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Me.RepositoryItemMemoEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
        Me.RepositoryItemTimeEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTimeEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.XpCollection2 = New DevExpress.Xpo.XPCollection(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colOid = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colApplication = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHttpRequestId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChartInfoOid = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChartOId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colVisitId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSourceContext = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colLogEventProperties = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMsgType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFacilityID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDateTime = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMachineName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colWindowsUserName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colECUserID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colErrorCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMessage = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Time = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnGetDataButton = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAllowEdit = New DevExpress.XtraEditors.SimpleButton()
        Me.toDate = New DevExpress.XtraEditors.DateEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.quickDate = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.fromDate = New DevExpress.XtraEditors.DateEdit()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.chkVerbose = New DevExpress.XtraEditors.CheckEdit()
        Me.chkInformation = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.SimpleButton4 = New DevExpress.XtraEditors.SimpleButton()
        Me.twLoadFromFile = New DevExpress.XtraEditors.ToggleSwitch()
        Me.btnLoadFromFile = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.teLoadFromFile = New DevExpress.XtraEditors.TextEdit()
        Me.BarAndDockingController1 = New DevExpress.XtraBars.BarAndDockingController(Me.components)
        Me.btnQuickExport = New DevExpress.XtraEditors.SimpleButton()
        Me.btnToggleWindowSize = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTimeEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.toDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.toDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.quickDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.fromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.fromDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkVerbose.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkInformation.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.twLoadFromFile.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teLoadFromFile.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarAndDockingController1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RepositoryItemMemoEdit1
        '
        Me.RepositoryItemMemoEdit1.Name = "RepositoryItemMemoEdit1"
        Me.RepositoryItemMemoEdit1.ReadOnly = True
        '
        'RepositoryItemTimeEdit1
        '
        Me.RepositoryItemTimeEdit1.AutoHeight = False
        Me.RepositoryItemTimeEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemTimeEdit1.DisplayFormat.FormatString = "T"
        Me.RepositoryItemTimeEdit1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.RepositoryItemTimeEdit1.Name = "RepositoryItemTimeEdit1"
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.DataSource = Me.XpCollection2
        Me.GridControl1.Location = New System.Drawing.Point(18, 63)
        Me.GridControl1.LookAndFeel.SkinName = "Office 2016 Dark"
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(962, 446)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'XpCollection2
        '
        Me.XpCollection2.ObjectType = GetType(EnchartDOLib.DOErrorLog)
        '
        'GridView1
        '
        Me.GridView1.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView1.Appearance.Row.Font = New System.Drawing.Font("Tahoma", 9.75!)
        Me.GridView1.Appearance.Row.Options.UseFont = True
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colOid, Me.colApplication, Me.colHttpRequestId, Me.colChartInfoOid, Me.colChartOId, Me.colVisitId, Me.colSourceContext, Me.colLogEventProperties, Me.colMsgType, Me.colFacilityID, Me.colDateTime, Me.colMachineName, Me.colWindowsUserName, Me.colECUserID, Me.colErrorCode, Me.colMessage, Me.Time})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "", Nothing, "")})
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsFind.AlwaysVisible = True
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.RowAutoHeight = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colDateTime, DevExpress.Data.ColumnSortOrder.Descending)})
        '
        'colOid
        '
        Me.colOid.FieldName = "Oid"
        Me.colOid.Name = "colOid"
        Me.colOid.OptionsColumn.FixedWidth = True
        Me.colOid.OptionsColumn.ReadOnly = True
        Me.colOid.Width = 33
        '
        'colApplication
        '
        Me.colApplication.FieldName = "Application"
        Me.colApplication.Name = "colApplication"
        Me.colApplication.OptionsColumn.FixedWidth = True
        Me.colApplication.OptionsColumn.ReadOnly = True
        Me.colApplication.Visible = True
        Me.colApplication.VisibleIndex = 7
        Me.colApplication.Width = 51
        '
        'colHttpRequestId
        '
        Me.colHttpRequestId.FieldName = "HttpRequestId"
        Me.colHttpRequestId.Name = "colHttpRequestId"
        Me.colHttpRequestId.OptionsColumn.ReadOnly = True
        Me.colHttpRequestId.Width = 138
        '
        'colChartInfoOid
        '
        Me.colChartInfoOid.Caption = "CInfo"
        Me.colChartInfoOid.FieldName = "ChartInfoOid"
        Me.colChartInfoOid.Name = "colChartInfoOid"
        Me.colChartInfoOid.OptionsColumn.FixedWidth = True
        Me.colChartInfoOid.OptionsColumn.ReadOnly = True
        Me.colChartInfoOid.Visible = True
        Me.colChartInfoOid.VisibleIndex = 11
        Me.colChartInfoOid.Width = 46
        '
        'colChartOId
        '
        Me.colChartOId.Caption = "Chart"
        Me.colChartOId.FieldName = "ChartOId"
        Me.colChartOId.Name = "colChartOId"
        Me.colChartOId.OptionsColumn.FixedWidth = True
        Me.colChartOId.OptionsColumn.ReadOnly = True
        Me.colChartOId.Visible = True
        Me.colChartOId.VisibleIndex = 10
        Me.colChartOId.Width = 53
        '
        'colVisitId
        '
        Me.colVisitId.FieldName = "VisitId"
        Me.colVisitId.Name = "colVisitId"
        Me.colVisitId.OptionsColumn.ReadOnly = True
        Me.colVisitId.Visible = True
        Me.colVisitId.VisibleIndex = 8
        Me.colVisitId.Width = 99
        '
        'colSourceContext
        '
        Me.colSourceContext.FieldName = "SourceContext"
        Me.colSourceContext.Name = "colSourceContext"
        Me.colSourceContext.OptionsColumn.ReadOnly = True
        Me.colSourceContext.Width = 366
        '
        'colLogEventProperties
        '
        Me.colLogEventProperties.ColumnEdit = Me.RepositoryItemMemoEdit1
        Me.colLogEventProperties.FieldName = "LogEventProperties"
        Me.colLogEventProperties.Name = "colLogEventProperties"
        Me.colLogEventProperties.OptionsColumn.ReadOnly = True
        Me.colLogEventProperties.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains
        Me.colLogEventProperties.Width = 402
        '
        'colMsgType
        '
        Me.colMsgType.FieldName = "MsgType"
        Me.colMsgType.Name = "colMsgType"
        Me.colMsgType.OptionsColumn.FixedWidth = True
        Me.colMsgType.OptionsColumn.ReadOnly = True
        Me.colMsgType.Visible = True
        Me.colMsgType.VisibleIndex = 0
        Me.colMsgType.Width = 64
        '
        'colFacilityID
        '
        Me.colFacilityID.Caption = "FacilityOID"
        Me.colFacilityID.FieldName = "FacilityID"
        Me.colFacilityID.Name = "colFacilityID"
        Me.colFacilityID.OptionsColumn.ReadOnly = True
        Me.colFacilityID.Visible = True
        Me.colFacilityID.VisibleIndex = 9
        Me.colFacilityID.Width = 101
        '
        'colDateTime
        '
        Me.colDateTime.DisplayFormat.FormatString = "d"
        Me.colDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colDateTime.FieldName = "DateTime"
        Me.colDateTime.Name = "colDateTime"
        Me.colDateTime.OptionsColumn.FixedWidth = True
        Me.colDateTime.OptionsColumn.ReadOnly = True
        Me.colDateTime.Visible = True
        Me.colDateTime.VisibleIndex = 1
        Me.colDateTime.Width = 81
        '
        'colMachineName
        '
        Me.colMachineName.FieldName = "MachineName"
        Me.colMachineName.Name = "colMachineName"
        Me.colMachineName.OptionsColumn.ReadOnly = True
        Me.colMachineName.Visible = True
        Me.colMachineName.VisibleIndex = 4
        Me.colMachineName.Width = 99
        '
        'colWindowsUserName
        '
        Me.colWindowsUserName.FieldName = "WindowsUserName"
        Me.colWindowsUserName.Name = "colWindowsUserName"
        Me.colWindowsUserName.OptionsColumn.ReadOnly = True
        Me.colWindowsUserName.Visible = True
        Me.colWindowsUserName.VisibleIndex = 5
        Me.colWindowsUserName.Width = 99
        '
        'colECUserID
        '
        Me.colECUserID.FieldName = "ECUserID"
        Me.colECUserID.Name = "colECUserID"
        Me.colECUserID.OptionsColumn.ReadOnly = True
        Me.colECUserID.Visible = True
        Me.colECUserID.VisibleIndex = 6
        Me.colECUserID.Width = 84
        '
        'colErrorCode
        '
        Me.colErrorCode.FieldName = "ErrorCode"
        Me.colErrorCode.Name = "colErrorCode"
        Me.colErrorCode.OptionsColumn.ReadOnly = True
        '
        'colMessage
        '
        Me.colMessage.ColumnEdit = Me.RepositoryItemMemoEdit1
        Me.colMessage.FieldName = "Message"
        Me.colMessage.Name = "colMessage"
        Me.colMessage.OptionsColumn.ReadOnly = True
        Me.colMessage.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains
        Me.colMessage.Visible = True
        Me.colMessage.VisibleIndex = 3
        Me.colMessage.Width = 600
        '
        'Time
        '
        Me.Time.Caption = "Time"
        Me.Time.ColumnEdit = Me.RepositoryItemTimeEdit1
        Me.Time.DisplayFormat.FormatString = "T"
        Me.Time.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.Time.FieldName = "DateTime"
        Me.Time.Name = "Time"
        Me.Time.OptionsColumn.FixedWidth = True
        Me.Time.OptionsColumn.ReadOnly = True
        Me.Time.Visible = True
        Me.Time.VisibleIndex = 2
        Me.Time.Width = 86
        '
        'btnGetDataButton
        '
        Me.btnGetDataButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnGetDataButton.Location = New System.Drawing.Point(789, 34)
        Me.btnGetDataButton.Name = "btnGetDataButton"
        Me.btnGetDataButton.Size = New System.Drawing.Size(191, 23)
        Me.btnGetDataButton.TabIndex = 226
        Me.btnGetDataButton.Text = "Refresh"
        '
        'btnAllowEdit
        '
        Me.btnAllowEdit.AllowFocus = False
        Me.btnAllowEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnAllowEdit.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnAllowEdit.Location = New System.Drawing.Point(362, 551)
        Me.btnAllowEdit.Name = "btnAllowEdit"
        Me.btnAllowEdit.Size = New System.Drawing.Size(87, 23)
        ToolTipTitleItem1.Text = "Allow Edit"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "This will allow you select and copy speicific text from any cell for easier filte" &
    "ring." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "You can not actually change the data." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "*Defualts to off for easier sc" &
    "rolling."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.btnAllowEdit.SuperTip = SuperToolTip1
        Me.btnAllowEdit.TabIndex = 227
        Me.btnAllowEdit.Text = "Allow Edit"
        Me.btnAllowEdit.ToolTip = "What are you hoping to Reset? Click it! I dare you!"
        '
        'toDate
        '
        Me.toDate.EditValue = Nothing
        Me.toDate.EnterMoveNextControl = True
        Me.toDate.Location = New System.Drawing.Point(335, 7)
        Me.toDate.Name = "toDate"
        Me.toDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.toDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.toDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.toDate.Properties.Mask.EditMask = "G"
        Me.toDate.Properties.Mask.ShowPlaceHolders = False
        Me.toDate.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.toDate.Size = New System.Drawing.Size(199, 20)
        Me.toDate.TabIndex = 243
        Me.toDate.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        Me.toDate.ToolTipTitle = "Date Entry"
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(18, 11)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(50, 13)
        Me.LabelControl6.TabIndex = 241
        Me.LabelControl6.Text = "From Date"
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(291, 11)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(38, 13)
        Me.LabelControl3.TabIndex = 240
        Me.LabelControl3.Text = "To Date"
        '
        'quickDate
        '
        Me.quickDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.quickDate.Location = New System.Drawing.Point(789, 7)
        Me.quickDate.Name = "quickDate"
        Me.quickDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.quickDate.Properties.DropDownRows = 15
        Me.quickDate.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.quickDate.Size = New System.Drawing.Size(191, 20)
        Me.quickDate.TabIndex = 228
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl5.Location = New System.Drawing.Point(730, 11)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(52, 13)
        Me.LabelControl5.TabIndex = 229
        Me.LabelControl5.Text = "Quick Date"
        '
        'fromDate
        '
        Me.fromDate.EditValue = Nothing
        Me.fromDate.EnterMoveNextControl = True
        Me.fromDate.Location = New System.Drawing.Point(75, 7)
        Me.fromDate.Name = "fromDate"
        Me.fromDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.fromDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.fromDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.fromDate.Properties.Mask.EditMask = "G"
        Me.fromDate.Properties.Mask.ShowPlaceHolders = False
        Me.fromDate.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.fromDate.Size = New System.Drawing.Size(199, 20)
        Me.fromDate.TabIndex = 242
        Me.fromDate.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        Me.fromDate.ToolTipTitle = "Date Entry"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.AllowFocus = False
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.SimpleButton1.Location = New System.Drawing.Point(894, 550)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(87, 23)
        Me.SimpleButton1.TabIndex = 230
        Me.SimpleButton1.Text = "Exit"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.AllowFocus = False
        Me.SimpleButton2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton2.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.SimpleButton2.Location = New System.Drawing.Point(146, 551)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(87, 23)
        ToolTipTitleItem2.Text = "Filtered Export"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Will explort all of the records currently in the grid. " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "This allows a custom d" &
    "ate range as well as just exporting a subset of records."
        ToolTipTitleItem3.LeftIndent = 6
        ToolTipTitleItem3.Text = "File also copied to Clipboard"
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        SuperToolTip2.Items.Add(ToolTipTitleItem3)
        Me.SimpleButton2.SuperTip = SuperToolTip2
        Me.SimpleButton2.TabIndex = 231
        Me.SimpleButton2.Text = "Filtered Export"
        Me.SimpleButton2.ToolTip = "Not yet!"
        '
        'SimpleButton3
        '
        Me.SimpleButton3.AllowFocus = False
        Me.SimpleButton3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton3.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.SimpleButton3.Location = New System.Drawing.Point(696, 551)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(87, 23)
        Me.SimpleButton3.TabIndex = 246
        Me.SimpleButton3.Text = "Print"
        Me.SimpleButton3.ToolTip = "Don't do it!"
        Me.SimpleButton3.Visible = False
        '
        'chkVerbose
        '
        Me.chkVerbose.Location = New System.Drawing.Point(75, 34)
        Me.chkVerbose.Name = "chkVerbose"
        Me.chkVerbose.Properties.Caption = "Verbose"
        Me.chkVerbose.Size = New System.Drawing.Size(75, 19)
        Me.chkVerbose.TabIndex = 247
        Me.chkVerbose.ToolTip = "Applied as db Query"
        '
        'chkInformation
        '
        Me.chkInformation.Location = New System.Drawing.Point(137, 34)
        Me.chkInformation.Name = "chkInformation"
        Me.chkInformation.Properties.Caption = "Information"
        Me.chkInformation.Size = New System.Drawing.Size(75, 19)
        Me.chkInformation.TabIndex = 248
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(18, 37)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(35, 13)
        Me.LabelControl1.TabIndex = 249
        Me.LabelControl1.Text = "Include"
        '
        'SimpleButton4
        '
        Me.SimpleButton4.AllowFocus = False
        Me.SimpleButton4.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.SimpleButton4.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.SimpleButton4.Location = New System.Drawing.Point(789, 551)
        Me.SimpleButton4.Name = "SimpleButton4"
        Me.SimpleButton4.Size = New System.Drawing.Size(87, 23)
        Me.SimpleButton4.TabIndex = 250
        Me.SimpleButton4.Text = "Best fit All Cols"
        Me.SimpleButton4.ToolTip = "I wouldn't ..."
        Me.SimpleButton4.Visible = False
        '
        'twLoadFromFile
        '
        Me.twLoadFromFile.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.twLoadFromFile.Location = New System.Drawing.Point(102, 521)
        Me.twLoadFromFile.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.twLoadFromFile.Name = "twLoadFromFile"
        Me.twLoadFromFile.Properties.AllowFocused = False
        Me.twLoadFromFile.Properties.LookAndFeel.SkinName = "Office 2013 Dark Gray"
        Me.twLoadFromFile.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.twLoadFromFile.Properties.OffText = "Off"
        Me.twLoadFromFile.Properties.OnText = "On"
        Me.twLoadFromFile.Size = New System.Drawing.Size(105, 24)
        Me.twLoadFromFile.TabIndex = 252
        '
        'btnLoadFromFile
        '
        Me.btnLoadFromFile.AllowFocus = False
        Me.btnLoadFromFile.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnLoadFromFile.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnLoadFromFile.Enabled = False
        Me.btnLoadFromFile.Location = New System.Drawing.Point(801, 516)
        Me.btnLoadFromFile.Name = "btnLoadFromFile"
        Me.btnLoadFromFile.Size = New System.Drawing.Size(87, 23)
        Me.btnLoadFromFile.TabIndex = 253
        Me.btnLoadFromFile.Text = "Load From File"
        Me.btnLoadFromFile.ToolTip = "I wouldn't ..."
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Location = New System.Drawing.Point(21, 525)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(69, 13)
        Me.LabelControl2.TabIndex = 254
        Me.LabelControl2.Text = "Load From File"
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'teLoadFromFile
        '
        Me.teLoadFromFile.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.teLoadFromFile.Enabled = False
        Me.teLoadFromFile.Location = New System.Drawing.Point(212, 520)
        Me.teLoadFromFile.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.teLoadFromFile.Name = "teLoadFromFile"
        Me.teLoadFromFile.Size = New System.Drawing.Size(585, 20)
        Me.teLoadFromFile.TabIndex = 255
        '
        'BarAndDockingController1
        '
        Me.BarAndDockingController1.PropertiesBar.AllowLinkLighting = False
        Me.BarAndDockingController1.PropertiesBar.DefaultGlyphSize = New System.Drawing.Size(16, 16)
        Me.BarAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = New System.Drawing.Size(32, 32)
        '
        'btnQuickExport
        '
        Me.btnQuickExport.AllowFocus = False
        Me.btnQuickExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnQuickExport.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnQuickExport.Location = New System.Drawing.Point(21, 551)
        Me.btnQuickExport.Name = "btnQuickExport"
        Me.btnQuickExport.Size = New System.Drawing.Size(104, 23)
        ToolTipTitleItem4.Text = "Quick Export"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Will export 1 month of Error Log records (Ignoring any of the filters on the form" &
    " (for example, ""Verbose"", ""Information"", ect.)"
        ToolTipTitleItem5.LeftIndent = 6
        ToolTipTitleItem5.Text = "File is also copied to Clipboard."
        SuperToolTip3.Items.Add(ToolTipTitleItem4)
        SuperToolTip3.Items.Add(ToolTipItem3)
        SuperToolTip3.Items.Add(ToolTipSeparatorItem1)
        SuperToolTip3.Items.Add(ToolTipTitleItem5)
        Me.btnQuickExport.SuperTip = SuperToolTip3
        Me.btnQuickExport.TabIndex = 256
        Me.btnQuickExport.Text = "Quick Export"
        Me.btnQuickExport.ToolTip = "1 Month"
        Me.btnQuickExport.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'btnToggleWindowSize
        '
        Me.btnToggleWindowSize.AllowFocus = False
        Me.btnToggleWindowSize.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnToggleWindowSize.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnToggleWindowSize.Location = New System.Drawing.Point(468, 551)
        Me.btnToggleWindowSize.Name = "btnToggleWindowSize"
        Me.btnToggleWindowSize.Size = New System.Drawing.Size(87, 23)
        ToolTipTitleItem6.Text = "Allow Edit"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "This will allow you select and copy speicific text from any cell for easier filte" &
    "ring." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "You can not actually change the data." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "*Defualts to off for easier sc" &
    "rolling."
        SuperToolTip4.Items.Add(ToolTipTitleItem6)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.btnToggleWindowSize.SuperTip = SuperToolTip4
        Me.btnToggleWindowSize.TabIndex = 257
        Me.btnToggleWindowSize.Text = "Window Size"
        Me.btnToggleWindowSize.ToolTip = "What are you hoping to Reset? Click it! I dare you!"
        '
        'Form1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(992, 583)
        Me.Controls.Add(Me.btnToggleWindowSize)
        Me.Controls.Add(Me.btnQuickExport)
        Me.Controls.Add(Me.teLoadFromFile)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.btnLoadFromFile)
        Me.Controls.Add(Me.twLoadFromFile)
        Me.Controls.Add(Me.SimpleButton4)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.chkInformation)
        Me.Controls.Add(Me.chkVerbose)
        Me.Controls.Add(Me.SimpleButton3)
        Me.Controls.Add(Me.SimpleButton2)
        Me.Controls.Add(Me.btnAllowEdit)
        Me.Controls.Add(Me.btnGetDataButton)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.LabelControl5)
        Me.Controls.Add(Me.quickDate)
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.toDate)
        Me.Controls.Add(Me.LabelControl3)
        Me.Controls.Add(Me.fromDate)
        Me.Controls.Add(Me.LabelControl6)
        Me.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.Shadow
        Me.MinimumSize = New System.Drawing.Size(1008, 485)
        Me.Name = "Form1"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "DOErrorLog Viewer 2.3"
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTimeEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.toDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.toDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.quickDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.fromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.fromDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkVerbose.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkInformation.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.twLoadFromFile.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teLoadFromFile.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarAndDockingController1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents XpCollection2 As DevExpress.Xpo.XPCollection
    Friend WithEvents btnGetDataButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAllowEdit As DevExpress.XtraEditors.SimpleButton
    Public WithEvents toDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents quickDate As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Public WithEvents fromDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents chkVerbose As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents chkInformation As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents SimpleButton4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents colOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colApplication As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHttpRequestId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChartInfoOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChartOId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colVisitId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSourceContext As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colLogEventProperties As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMsgType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFacilityID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDateTime As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMachineName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colWindowsUserName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colECUserID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colErrorCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMessage As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Time As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents twLoadFromFile As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents btnLoadFromFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents teLoadFromFile As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RepositoryItemMemoEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit
    Friend WithEvents RepositoryItemTimeEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTimeEdit
    Friend WithEvents BarAndDockingController1 As DevExpress.XtraBars.BarAndDockingController
    Friend WithEvents btnQuickExport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnToggleWindowSize As DevExpress.XtraEditors.SimpleButton
End Class

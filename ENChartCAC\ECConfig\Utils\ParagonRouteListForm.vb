﻿Imports System.IO
Imports DevExpress.Xpo
Imports System.Linq
Imports System.Text

Public Class ParagonRouteListForm

    Private xpcollection2 As New XPCollection(Of DOParagonRoute)

    Private Sub ParagonRouteListForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        GridControl1.DataSource = xpcollection2
        xpcollection2.DeleteObjectOnRemove = True
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        UnitOfWork1.CommitChanges()
        Close()
    End Sub

    Public Sub SaveRouteListToDiskAsCsv()
        Cursor.Current = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "Route List Config File|*.csv"
            'fsa.DefaultExt = "*.txt"
            fsa.FileName = "RouteList.csv"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)
            Dim sb As New StringBuilder
            For Each med In DOParagonRoute.GetAllAsDTOs()
                sb.Length = 0
                sb.Append(med.MedOrderRoute + ",")
                sb.Append(med.ICRoute.ToString)

                sw.WriteLine(sb.ToString)
            Next
            sw.Close()

            MessageBox.Show("Route List successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub
    Private Sub btnDeleteAll_Click(sender As Object, e As EventArgs) Handles btnDeleteAll.Click
        Cursor.Current = Cursors.WaitCursor
        Try
            If DialogResult.OK <> MessageBox.Show("You are about to permanently delete all records from the DOParagonRoute table. Are you sure you want to continue?", "Are you sure?", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) Then
                Exit Sub
            End If

            '_helper.DeleteAll()
            XpoDefault.Session.Delete(DOParagonRoute.GetAllAsDOs)
        Finally
            Cursor.Current = Cursors.Default
            'UpdateDialogInfo()
            xpcollection2.Reload()
        End Try
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        SaveRouteListToDiskAsCsv()
    End Sub

    Private Function GetFileToImport() As FileInfo
        Dim ofd As New OpenFileDialog
        ofd.Filter = "Route List Config File|*.csv"
        ofd.FileName = "RouteList.csv"

        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then
            Return Nothing 'Exit Function
        End If

        Dim ffile As New FileInfo(ofd.FileName)
        Return ffile
    End Function

    Private Sub btnImport_Click(sender As Object, e As EventArgs) Handles btnImport.Click
        Dim fInfo = GetFileToImport()
        If fInfo Is Nothing Then Return
        Dim routeList As New List(Of DOParagonRoute)
        Dim lines = File.ReadAllLines(fInfo.FullName)
        Dim firstLine = True

        For Each line In lines
            '    why Is this here? - crc
            '    If firstLine Then
            '        firstLine = False
            '        Continue For
            '    End If

            Dim fields = line.Split(",")
            Dim newRouteMapping As New DOParagonRoute
            newRouteMapping.MedOrderRoute = fields(0)
            newRouteMapping.ICRoute = fields(1)
            routeList.Add(newRouteMapping)
        Next

        XpoDefault.Session.Save(routeList)
        xpcollection2.Reload()
    End Sub
End Class
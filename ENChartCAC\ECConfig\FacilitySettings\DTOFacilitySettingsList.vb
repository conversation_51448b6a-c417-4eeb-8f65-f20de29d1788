﻿Public Class DTOFacilitySettingsList
    Public Property Settings As List(Of DTOFacilitySetting)
    Public Sub New()

    End Sub

    Public Function GetCategories() As List(Of String)
        Dim return_list As New List(Of String)

        For Each setting As DTOFacilitySetting In Settings
            If Not return_list.Contains(setting.Category) Then
                return_list.Add(setting.Category)
            End If
        Next

        return_list.Sort()
        Return return_list
    End Function

    Public Function GetSettingNames() As List(Of String)
        Dim return_list As New List(Of String)

        For Each setting As DTOFacilitySetting In Settings
            If Not return_list.Contains(setting.Name) Then
                return_list.Add(setting.Name)
            End If
        Next

        return_list.Sort()
        Return return_list
    End Function
End Class

﻿Imports System.Windows.Forms
Imports EnchartDOLib
Imports DevExpress.Xpo
Imports DevExpress.Data.Filtering
Imports System.Text

Public Class CDMEditDialog
    Private Property CDM As DOChargeMaster
    Private Property DescendantCDMs As XPCollection(Of DOChargeMaster)
    Private Property EditSession As New Session
    Private Property OriginalCDMRoot As String
    Private Property OriginalPhysicianCDMRoot As String
    Private Property HasCDMChanged As Boolean
    Private Property HasPhysicianCDMChanged As Boolean
    Private Property CurrentUser As DOUser

    Public Sub New(ByVal cdm As DOChargeMaster, ByVal current_user As DOUser)
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        Me.CDM = cdm
        CurrentUser = current_user
        EditSession.BeginTransaction()
        LoadCDMValues()
        'SetupAuditLogger() 'JJC 07.30.19 Should already be initialized
    End Sub

    Private Sub LoadCDMValues()
        grpEditHCPCS.Text = String.Format("HCPCS: {0} - Facility: {1} - TA: {2}", GetRoot(CDM.HCPCS), CDM.Facility.LongName, CDM.TreatmentArea)

        OriginalCDMRoot = GetRoot(CDM.CDM)
        txtCDM.Text = GetRoot(CDM.CDM)

        OriginalPhysicianCDMRoot = GetRoot(CDM.PhysicianCDM)
        txtPhysicianCDM.Text = GetRoot(CDM.PhysicianCDM)

        LoadDescendantCDMs()
    End Sub

    Private Function GetRoot(ByVal cdm As String) As String
        If String.IsNullOrEmpty(cdm) Then Return ""

        Dim mod_loc As Integer = cdm.IndexOf("x")
        If mod_loc < 0 Then mod_loc = cdm.Length

        If cdm.Contains("-") Then
            Return cdm.Substring(0, mod_loc)
        Else
            Return cdm.Substring(0, mod_loc)
        End If
    End Function

    Private Function GetQuantityString(ByVal hcpcs As String) As String
        If hcpcs.Contains("x") Then
            Dim x_loc As Integer = hcpcs.LastIndexOf("x")
            Return hcpcs.Substring(x_loc)
        Else
            Return ""
        End If
    End Function

    Private Sub LoadDescendantCDMs()
        GridControl1.DataSource = Nothing

        Dim descendant_criteria As New StringBuilder

        'JJC 08.1.19 I'm trying to make editing the G0378 code not update the G0378_Int code ... I believe
        'that the use of the underscore is unique to this HCPCS code, but i'm not sure ...

        'Dim filter = String.Format("Facility.Oid = {0} And TreatmentArea = '{1}' AND (HCPCS LIKE '{2}%' AND HCPCS NOT LIKE '{2}-%' AND HCPCS NOT LIKE '{2}_%')", CDM.Facility.Oid, CDM.TreatmentArea, GetRoot(CDM.HCPCS))
        'FYI kids ... the underscore character has special meaning in a like statement so it must be escaped ...
        descendant_criteria.AppendLine(String.Format("Facility.Oid = {0} And TreatmentArea = '{1}' AND (HCPCS LIKE '{2}%' AND HCPCS NOT LIKE '{2}-%' AND HCPCS NOT LIKE '{2}[_]%')", CDM.Facility.Oid, CDM.TreatmentArea, GetRoot(CDM.HCPCS)))

        'descendant_criteria.AppendLine(String.Format("Facility.Oid = {0} And TreatmentArea = '{1}' AND (HCPCS LIKE '{2}%' AND HCPCS NOT LIKE '{2}-%')", CDM.Facility.Oid, CDM.TreatmentArea, GetRoot(CDM.HCPCS)))


        DescendantCDMs = New XPCollection(Of DOChargeMaster)(EditSession, CriteriaOperator.Parse(descendant_criteria.ToString))

        GridControl1.DataSource = DescendantCDMs
        lblDescendantCount.Text = String.Format("{0} record(s) will be affected by this change", DescendantCDMs.Count)
    End Sub

    Private Sub DisplayUpdatedDescendantCDMS()
        If DescendantCDMs IsNot Nothing Then

            GridControl1.DataSource = Nothing

            For Each cdm As DOChargeMaster In DescendantCDMs
                If chkCDMReplace.Checked And HasCDMChanged Then
                    cdm.CDM = txtCDM.Text
                End If

                If chkPhysicianCDMReplace.Checked And HasPhysicianCDMChanged Then
                    cdm.PhysicianCDM = txtPhysicianCDM.Text
                End If
            Next

            GridControl1.DataSource = DescendantCDMs

        End If
    End Sub

    Private Sub SaveChanges()
        If DescendantCDMs IsNot Nothing Then
            For Each cdm As DOChargeMaster In DescendantCDMs
                If chkCDMReplace.Checked And HasCDMChanged Then
                    cdm.CDM = txtCDM.Text
                End If

                If chkPhysicianCDMReplace.Checked And HasPhysicianCDMChanged Then
                    cdm.PhysicianCDM = txtPhysicianCDM.Text
                End If

                cdm.Save()
            Next
        End If
    End Sub

    Private Sub LogCDMChange()
        Dim change_string As New StringBuilder

        change_string.Append(String.Format("Updating root HCPCS {0} for Facility {1}: ", GetRoot(CDM.HCPCS), CDM.Facility.Oid))

        If chkCDMReplace.Checked And HasCDMChanged Then
            change_string.Append(String.Format("CDM values set to '{0}', ", txtCDM.Text))
        Else
            change_string.Append("No CDM codes updated, ")
        End If

        If chkPhysicianCDMReplace.Checked And HasPhysicianCDMChanged Then
            change_string.Append(String.Format("Physician CDM values set to '{0}', ", txtPhysicianCDM.Text))
        Else
            change_string.Append("No Physician CDM codes updated")
        End If

        AuditLogger.CDMEditor.LogEdit(change_string.ToString)
    End Sub

#Region "Audit Logging"
    Protected Friend Sub SetupAuditLogger()
        Dim log_helper As New BasicAuditLogHelper(CurrentUser, CDM.Facility)
        Dim log_writer As New DefaultAuditLogWriter()

        Try
            AuditLogger.Init(log_helper, log_writer)
            AuditLogger.SetIsRemoteSession(SystemInformation.TerminalServerSession)
        Catch ex As Exception

        End Try
    End Sub
#End Region

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        EditSession.CommitTransaction()
        LogCDMChange()
        SaveChanges()
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        EditSession.RollbackTransaction()
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub chkCDMReplace_CheckedChanged(sender As Object, e As EventArgs) Handles chkCDMReplace.CheckedChanged
        txtCDM.Enabled = chkCDMReplace.Checked
        txtCDM.Text = OriginalCDMRoot
    End Sub

    Private Sub chkPhysicianCDMReplace_CheckedChanged(sender As Object, e As EventArgs) Handles chkPhysicianCDMReplace.CheckedChanged
        txtPhysicianCDM.Enabled = chkPhysicianCDMReplace.Checked
        txtPhysicianCDM.Text = OriginalPhysicianCDMRoot
    End Sub

    Private Sub txtCDM_KeyUp(sender As Object, e As EventArgs) Handles txtCDM.KeyUp
        HasCDMChanged = True
        DisplayUpdatedDescendantCDMS()
    End Sub

    Private Sub txtPhysicianCDM_KeyUp(sender As Object, e As EventArgs) Handles txtPhysicianCDM.KeyUp
        HasPhysicianCDMChanged = True
        DisplayUpdatedDescendantCDMS()
    End Sub
End Class

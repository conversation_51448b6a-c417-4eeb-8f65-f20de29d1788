﻿Option Infer On

Public Class InfusionControlNamesHelper
    'Public ReadOnly Property ControlDict() As Dictionary(Of String, Control)
    Private FacilityInfusionControlNamesList As New List(Of List(Of String))
    Private ObsInfusionControlNamesList As New List(Of List(Of String))

    Public Sub New()
        InitRowControlNameLists()
    End Sub

    Private Sub InitRowControlNameLists()
        FacilityInfusionControlNamesList = GetFacilityInfusionControlNamesList()
        ObsInfusionControlNamesList = GetObsInfusionControlNamesList()
    End Sub

    Public Function GetFacilityInfusionControlNamesList() As List(Of List(Of String))
        Dim outFacilityInfusionControlNamesList As New List(Of List(Of String))

        For irow = 1 To 7
            Dim fieldNamesList = GetListOfFacilityInfusionRowFieldNames(irow, FacilityInfusionFieldNamesList)
            outFacilityInfusionControlNamesList.Add(fieldNamesList)
        Next

        For irow = 1 To 5
            Dim fieldNamesList = GetListOfFacilityInfusionRowFieldNames(irow, FacilityInfusionTitratedFieldNamesList)
            outFacilityInfusionControlNamesList.Add(fieldNamesList)
        Next
        Return outFacilityInfusionControlNamesList
    End Function

    Public Function GetObsInfusionControlNamesList() As List(Of List(Of String))
        Dim outObsInfusionControlNamesList As New List(Of List(Of String))

        For irow = 1 To 30
            Dim fieldNamesList = GetListOfFacilityInfusionRowFieldNames(irow, ObsInfusionFieldNamesList)
            outObsInfusionControlNamesList.Add(fieldNamesList)
        Next

        Return outObsInfusionControlNamesList
    End Function

    'Infusions_TitratedMedicationsStartDate01_dte
    Private Function GetListOfFacilityInfusionRowFieldNames(rowNumber As Short, fieldNamesList As String()) As List(Of String)
        Dim outList As New List(Of String)
        For Each field As String In fieldNamesList
            outList.Add(field.Replace("01", rowNumber.ToString("D2")))
        Next

        Return outList
    End Function
    Private FacilityInfusionFieldNamesList = {"Infusion_InfusedMedicationsStartDate01_dte",
        "Infusion_InfusedMedicationsStartTime01_txt",
        "Infusion_InfusedMedicationsEndDate01_dte",
        "Infusion_InfusedMedicationsEndTime01_txt",
        "Infusion_InfusedMedicationsSite01_cbo",
        "Infusion_InfusedMedicationsMedication01_cbo"
    }
    Private FacilityInfusionTitratedFieldNamesList = {"Infusions_TitratedMedicationsStartDate01_dte",
        "Infusions_TitratedMedicationsStartTime01_txt",
    "Infusions_TitratedMedicationsEndDate01_dte",
    "Infusions_TitratedMedicationsEndTime01_txt",
    "Infusions_TitratedMedicationsSite01_cbo",
    "Infusions_TitratedMedicationsMedication01_cbo"
        }
    Private ObsInfusionFieldNamesList = {"ObsMedsInfusedStartDate_dte_01",
        "ObsMedsInfusedStartTime_txt_01",
        "ObsMedsInfusedEndDate_dte_01",
        "ObsMedsInfusedEndTime_txt_01",
        "ObsMedsInfusedSite_cbo_01",
        "ObsMedsInfusedMed_cbo_01"
    }
End Class

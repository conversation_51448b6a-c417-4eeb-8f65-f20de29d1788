﻿Imports System.IO
Imports System.Threading
Imports System.Threading.Tasks
Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Imports ECConfig

Public Class UpdateModifier25Form
    Private tokenSource As CancellationTokenSource
    Private _fileName As String = String.Empty
    Private EspCodesToUpdateList As New List(Of DOESPCode2018)
    Private EspCodesToResetMod25FlagList As New List(Of DOESPCode2018)

    Public Class ProgressMessage
        Public Sub New(progress As Integer, message As String)
            Me.Progress = progress
            Me.Message = message
            Me.Status = ""
        End Sub

        Public Sub New(progress As Integer, message As String, status As String)
            Me.Progress = progress
            Me.Message = message
            Me.Status = status
        End Sub

        Public Property Progress As Integer
        Public Property Message As String
        Public Property Status As String
    End Class

    Private Async Sub LoadButton_Click(sender As Object, e As EventArgs) Handles LoadButton.Click

        InitVars()

        UpDateProgress(New ProgressMessage(0, ""))
        _fileName = ChooseFileTextBox.Text

        CancelBtn.Enabled = True
        LoadButton.Enabled = False
        CloseButton.Enabled = False
        ChooseFileButton.Enabled = False
        ChooseFileTextBox.Enabled = False
        cbSimulate.Enabled = False

        Dim progressIndicator = New Progress(Of ProgressMessage)(AddressOf UpDateProgress)
        tokenSource = New CancellationTokenSource
        Try
            Await Task.Run(Sub() CreateDBRecords(progressIndicator, tokenSource.Token))
        Catch ex As OperationCanceledException
            Status2Label.Text = "*** Cancelled ***"
        Catch ex As Exception

            Me.ProgressBar1.Value = 0
        End Try

        Beep()
        CancelBtn.Enabled = False
        LoadButton.Enabled = True
        CloseButton.Enabled = True
        ChooseFileButton.Enabled = True
        ChooseFileTextBox.Enabled = True
        cbSimulate.Enabled = True
        Dim totalUpdated As Integer = EspCodesToResetMod25FlagList.Count + EspCodesToUpdateList.Count
        If cbSimulate.Checked Then
            totalUpdated = 0
        End If
        Dim SummaryText = $"Total to Clear: {EspCodesToResetMod25FlagList.Count}, Total to Flag:{EspCodesToUpdateList.Count}, NotFound: {notfoundCount} (of {TotalCMSRecs}), Used: {TotalCMSRecs - notfoundCount}, Total Actually Updated: {totalUpdated}"
        tb1.Text = $"{SummaryText}{vbCr}{vbCr}{tb1.Text}"
    End Sub

    Private Property HcpcsList As List(Of String)
        Get
            If _hcpcsListAIC Is Nothing Then
                _hcpcsListAIC = New List(Of String)
            End If
            Return _hcpcsListAIC
        End Get
        Set
            _hcpcsListAIC = Value
        End Set
    End Property

    Private Sub InitVars()
        EspCodesToUpdateList.Clear()
        EspCodesToResetMod25FlagList.Clear()
        notfoundCount = 0
        HcpcsList = InitHcpsList()
        _hcpcsListCMS = New List(Of String)
        tb1.Text = ""
    End Sub

    Private Function InitHcpsList() As List(Of String)
        ' Dim criteriaStartsWith As CriteriaOperator = New FunctionOperator(FunctionOperatorType.StartsWith, New OperandProperty("HCPCS"), mod25Rec.HCPCS)
        Dim espcodes As New XPCollection(Of DOESPCode2018)() ' CriteriaOperator.Parse($"HCPCS StartsWith '{mod25Rec.HCPCS}'"))
        If espcodes?.Count <= 0 Then
            notfoundCount += 1
            Return HcpcsList
        End If

        HcpcsList.Clear()
        For Each espcode In espcodes
            'If Not String.IsNullOrEmpty(espcode.HCPCS) AndAlso espcode.HCPCS.StartsWith("27222") Then
            '    Debug.WriteLine("break")
            'End If
            Dim root = GetRoot(espcode.HCPCS)
            If HcpcsList.Contains(root) = False Then
                HcpcsList.Add(root)
            End If

            'If HcpcsList.Contains(espcode.HCPCS) = False Then
            '    HcpcsList.Add(espcode.HCPCS)
            'End If
        Next
        Debug.WriteLine($"hcpcsList.count = {HcpcsList.Count}")
        Return HcpcsList
    End Function

    Private Function GetRoot(ByVal cdm As String) As String
        If String.IsNullOrEmpty(cdm) Then Return ""

        Dim mod_loc As Integer = cdm.IndexOf("x")
        If mod_loc < 0 Then mod_loc = cdm.Length

        If cdm.Contains("-") Then
            mod_loc = cdm.IndexOf("-")
            Return cdm.Substring(0, mod_loc)
        Else
            Return cdm.Substring(0, mod_loc)
        End If
    End Function

    Private Sub UpDateProgress(msg As ProgressMessage)
        If msg.Progress > 0 Then
            Me.ProgressBar1.Value = msg.Progress
            StatusLabel.Text = (msg.Progress / 100).ToString("#0.##%")
        End If


        If Not String.IsNullOrEmpty(msg.Message) Then
            'Status2Label.Text = msg.Message
            tb1.Text = $"{msg.Message}{vbCrLf}{tb1.Text}"
        End If

        If msg.Status IsNot Nothing Then
            Status2Label.Text = msg.Status
        End If


    End Sub

    Private Class Mod25Info
        Private _Description As Object

        Public Sub New(line As String)
            Dim f = Split(line, ",")
            Parse(f(0), f(1), f(2), f(3))
        End Sub
        Public Sub Parse(hcpcs As String, description As String, ColumnC As String, columnD As String)
            Me.HCPCS = hcpcs
            Me.Description = description
            Me.ColumnC = ColumnC
            Me.ColumnD = columnD
        End Sub

        Public Property HCPCS As String
        Public Property Description
            Get
                Return _Description
            End Get
            Set
                _Description = Trim(Value)
            End Set
        End Property

        Public Property ColumnC As String
        Public Property ColumnD As String

        Public Function Trim(line As String) As String
            If line Is Nothing Then Return String.Empty
            line = line.Replace(vbTab, "").Replace("""", "")
            Return line
        End Function
        Public Function ShouldApplyMod25() As Boolean
            If ColumnD = "S" Then Return True
            If ColumnD = "T" Then Return True
            Return False
        End Function
    End Class

    Private Property PercentComplete As Integer
    Private Property lastPercentComplete As Integer
    Private Property TotalCMSRecs As Integer

    Public Sub CreateDBRecords(progress As IProgress(Of ProgressMessage), cancelToken As CancellationToken)
        Dim lines As String()
        Dim ffile As New FileInfo(_fileName)
        lines = File.ReadAllLines(ffile.FullName)
        Dim lineCount = lines.Length
        TotalCMSRecs = lineCount

        Dim keepCount As Integer = 0
        Dim TotalSoFar As Integer = 0
        Dim uow As New UnitOfWork()

        For Each line In lines
            cancelToken.ThrowIfCancellationRequested()

            keepCount += 1
            TotalSoFar += 1
            lastPercentComplete = PercentComplete
            PercentComplete = (TotalSoFar * 100 / lineCount)

            If keepCount = 100 Then
                'progress.Report(New ProgressMessage(PercentComplete, "")) ' mod25Rec.Description))
                Application.DoEvents()
                keepCount = 0
                If uow IsNot Nothing Then
                    uow.CommitChanges()
                End If
                uow = New UnitOfWork()
            End If

            Dim mod25Rec As New Mod25Info(line)
            If mod25Rec.HCPCS = "" Then
                Continue For
            End If
            If Not _hcpcsListCMS.Contains(mod25Rec.HCPCS) Then
                _hcpcsListCMS.Add(mod25Rec.HCPCS)
            End If
            If mod25Rec.HCPCS.StartsWith("27222") Then
                Debug.WriteLine("dbreak")
            End If

            If HcpcsList.Contains(mod25Rec.HCPCS) = False Then
                notfoundCount += 1
                If PercentComplete <> lastPercentComplete Then
                    progress.Report(New ProgressMessage(PercentComplete, "", mod25Rec.Description)) ' mod25Rec.Description))
                End If
                Continue For
            End If
            progress.Report(New ProgressMessage(PercentComplete, "", mod25Rec.Description)) ' mod25Rec.Description))
            UpdateMatchingEspcodes(uow, mod25Rec, progress)
        Next

        'ShowPotentiallyObsoleteCodes(uow, progress)

        progress.Report(New ProgressMessage(100, "", "")) ' mod25Rec.Description))
        If uow IsNot Nothing Then
            uow.CommitChanges()
            uow.Dispose()
        End If
    End Sub

    Private Sub ShowPotentiallyObsoleteCodes(uow As UnitOfWork, progress As IProgress(Of ProgressMessage))
        'not really useful as there are way too many AIC custome ones (that for ex. start with HIC) that are not official
        Dim espcodes = New XPCollection(Of DOESPCode2018)(uow) ', criteriaStartsWith) 
        For Each espcode In espcodes
            Dim hcpcs = If(espcode.HCPCS, "")
            hcpcs = GetRoot(hcpcs)
            If String.IsNullOrEmpty(hcpcs) Then Continue For
            If Not _hcpcsListCMS.Contains(hcpcs) Then
                progress.Report(New ProgressMessage(-1, $"OBSOLETE WARING - {FormatProgressMsg(espcode)}"))
            End If
        Next
    End Sub

    Private notfoundCount As Integer = 0
    Private _hcpcsListAIC As List(Of String)
    Private _hcpcsListCMS As List(Of String)

    Private espcodes As New XPCollection(Of DOESPCode2018)

    Private Sub UpdateMatchingEspcodes(uow As UnitOfWork, mod25Rec As Mod25Info, progress As IProgress(Of ProgressMessage))
        Try
            Dim criteriaStartsWith As CriteriaOperator = New FunctionOperator(FunctionOperatorType.StartsWith, New OperandProperty("HCPCS"), mod25Rec.HCPCS)
            espcodes = New XPCollection(Of DOESPCode2018)(uow, criteriaStartsWith) ' CriteriaOperator.Parse($"HCPCS StartsWith '{mod25Rec.HCPCS}'"))
            'If espocodes?.Count <= 0 Then
            '    notfoundCount += 1
            '    Return
            'End If


            For Each espcode In espcodes
                If mod25Rec.ShouldApplyMod25 Then
                    If espcode.Special <> 1 Then
                        progress.Report(New ProgressMessage(-1, FormatProgressMsg(espcode)))
                        If Not cbSimulate.Checked Then
                            espcode.Special = 1
                            espcode.Save()
                        End If
                        EspCodesToUpdateList.Add(espcode)

                    End If
                Else
                    If espcode.Special = 1 Then
                        progress.Report(New ProgressMessage(-1, FormatProgressMsg(espcode)))
                        If Not cbSimulate.Checked Then
                            espcode.Special = 0
                            espcode.Save()
                        End If
                        EspCodesToResetMod25FlagList.Add(espcode)

                    End If
                End If
            Next

        Catch ex As Exception
            Throw
            Debug.WriteLine($"UpdateMatchingEspcodes Exception ............")
        End Try
    End Sub

    Private Function FormatProgressMsg(rec As DOESPCode2018) As String
        Return $"Facility = {IIf(rec?.Facility Is Nothing, "Shared", rec.Facility)}, HCPCS = {rec.HCPCS}, Mod25 = {rec.Special}, Description = {rec?.LongName}"
    End Function

    Private Sub ChooseFileButton_Click(sender As Object, e As EventArgs) Handles ChooseFileButton.Click
        Dim ofd As New OpenFileDialog
        ofd.Title = "JJCRules"
        ofd.DefaultExt = "*.csv"
        ofd.Filter = String.Format("NFRM Addendum B (*.csv) | *.*")
        ofd.InitialDirectory = My.Computer.FileSystem.CurrentDirectory 'ECGlobals.ComboBoxListDir
        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub
        Me.ChooseFileTextBox.Text = ofd.FileName

        If Not String.IsNullOrWhiteSpace(Me.ChooseFileTextBox.Text) Then
            LoadButton.Enabled = True
        End If
        ofd.Dispose()
    End Sub

    Private Sub CancelBtn_Click(sender As Object, e As EventArgs) Handles CancelBtn.Click
        ProgressBar1.Value = 0
        If tokenSource IsNot Nothing Then
            tokenSource.Cancel()
        End If
    End Sub

    Private Sub UpdateModifier25Form_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub ChooseFileTextBox_TextChanged(sender As Object, e As EventArgs) Handles ChooseFileTextBox.TextChanged
        If String.IsNullOrWhiteSpace(ChooseFileTextBox.Text) Then
            LoadButton.Enabled = False
        Else
            LoadButton.Enabled = True
        End If
    End Sub

    Private Sub Label2_Click(sender As Object, e As EventArgs) Handles Label2.Click

    End Sub
End Class
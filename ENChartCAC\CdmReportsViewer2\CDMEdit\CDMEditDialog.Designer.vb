﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CDMEditDialog
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        TableLayoutPanel1 = New TableLayoutPanel()
        btnSave = New Button()
        Cancel_Button = New Button()
        GridControl1 = New DevExpress.XtraGrid.GridControl()
        GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        colCDMHCPCS = New DevExpress.XtraGrid.Columns.GridColumn()
        colCDMCode = New DevExpress.XtraGrid.Columns.GridColumn()
        colPhysicianCDMCode = New DevExpress.XtraGrid.Columns.GridColumn()
        colQuantity = New DevExpress.XtraGrid.Columns.GridColumn()
        colCDMLongName = New DevExpress.XtraGrid.Columns.GridColumn()
        lblDescendantCount = New DevExpress.XtraEditors.LabelControl()
        txtPhysicianCDM = New DevExpress.XtraEditors.TextEdit()
        txtCDM = New DevExpress.XtraEditors.TextEdit()
        chkCDMReplace = New DevExpress.XtraEditors.CheckEdit()
        chkPhysicianCDMReplace = New DevExpress.XtraEditors.CheckEdit()
        grpEditHCPCS = New DevExpress.XtraEditors.GroupControl()
        TableLayoutPanel1.SuspendLayout()
        CType(GridControl1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridView1, ComponentModel.ISupportInitialize).BeginInit()
        CType(txtPhysicianCDM.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(txtCDM.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(chkCDMReplace.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(chkPhysicianCDMReplace.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(grpEditHCPCS, ComponentModel.ISupportInitialize).BeginInit()
        grpEditHCPCS.SuspendLayout()
        SuspendLayout()
        ' 
        ' TableLayoutPanel1
        ' 
        TableLayoutPanel1.Anchor = AnchorStyles.Bottom Or AnchorStyles.Right
        TableLayoutPanel1.ColumnCount = 2
        TableLayoutPanel1.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 50F))
        TableLayoutPanel1.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 50F))
        TableLayoutPanel1.Controls.Add(Cancel_Button, 1, 0)
        TableLayoutPanel1.Controls.Add(btnSave, 0, 0)
        TableLayoutPanel1.Location = New Point(422, 386)
        TableLayoutPanel1.Margin = New Padding(4, 3, 4, 3)
        TableLayoutPanel1.Name = "TableLayoutPanel1"
        TableLayoutPanel1.RowCount = 1
        TableLayoutPanel1.RowStyles.Add(New RowStyle(SizeType.Percent, 50F))
        TableLayoutPanel1.Size = New Size(170, 33)
        TableLayoutPanel1.TabIndex = 0
        ' 
        ' btnSave
        ' 
        btnSave.Anchor = AnchorStyles.Bottom Or AnchorStyles.Right
        btnSave.AutoSizeMode = AutoSizeMode.GrowAndShrink
        btnSave.Location = New Point(4, 3)
        btnSave.Margin = New Padding(4, 3, 4, 3)
        btnSave.Name = "btnSave"
        btnSave.Size = New Size(77, 27)
        btnSave.TabIndex = 4
        btnSave.Text = "Save"
        ' 
        ' Cancel_Button
        ' 
        Cancel_Button.Anchor = AnchorStyles.Bottom Or AnchorStyles.Right
        Cancel_Button.AutoSizeMode = AutoSizeMode.GrowAndShrink
        Cancel_Button.DialogResult = DialogResult.Cancel
        Cancel_Button.Location = New Point(89, 3)
        Cancel_Button.Margin = New Padding(4, 3, 4, 3)
        Cancel_Button.Name = "Cancel_Button"
        Cancel_Button.Size = New Size(77, 27)
        Cancel_Button.TabIndex = 5
        Cancel_Button.Text = "Cancel"
        ' 
        ' GridControl1
        ' 
        GridControl1.Dock = DockStyle.Fill
        GridControl1.EmbeddedNavigator.Margin = New Padding(4, 3, 4, 3)
        GridControl1.Location = New Point(2, 23)
        GridControl1.MainView = GridView1
        GridControl1.Margin = New Padding(4, 3, 4, 3)
        GridControl1.Name = "GridControl1"
        GridControl1.Size = New Size(574, 310)
        GridControl1.TabIndex = 6
        GridControl1.TabStop = False
        GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {GridView1})
        ' 
        ' GridView1
        ' 
        GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colCDMHCPCS, colCDMCode, colPhysicianCDMCode, colQuantity, colCDMLongName})
        GridView1.CustomizationFormBounds = New Rectangle(972, 616, 243, 220)
        GridView1.DetailHeight = 404
        GridView1.GridControl = GridControl1
        GridView1.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "", Nothing, "")})
        GridView1.Name = "GridView1"
        GridView1.OptionsBehavior.Editable = False
        GridView1.OptionsDetail.EnableMasterViewMode = False
        GridView1.OptionsEditForm.PopupEditFormWidth = 933
        GridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full
        GridView1.OptionsView.ShowAutoFilterRow = True
        GridView1.OptionsView.ShowGroupPanel = False
        ' 
        ' colCDMHCPCS
        ' 
        colCDMHCPCS.Caption = "HCPCS"
        colCDMHCPCS.FieldName = "HCPCS"
        colCDMHCPCS.MinWidth = 23
        colCDMHCPCS.Name = "colCDMHCPCS"
        colCDMHCPCS.Visible = True
        colCDMHCPCS.VisibleIndex = 0
        colCDMHCPCS.Width = 157
        ' 
        ' colCDMCode
        ' 
        colCDMCode.Caption = "CDM"
        colCDMCode.FieldName = "CDM"
        colCDMCode.MinWidth = 23
        colCDMCode.Name = "colCDMCode"
        colCDMCode.Visible = True
        colCDMCode.VisibleIndex = 1
        colCDMCode.Width = 157
        ' 
        ' colPhysicianCDMCode
        ' 
        colPhysicianCDMCode.Caption = "Phys. CDM"
        colPhysicianCDMCode.FieldName = "PhysicianCDM"
        colPhysicianCDMCode.MinWidth = 23
        colPhysicianCDMCode.Name = "colPhysicianCDMCode"
        colPhysicianCDMCode.Visible = True
        colPhysicianCDMCode.VisibleIndex = 2
        colPhysicianCDMCode.Width = 157
        ' 
        ' colQuantity
        ' 
        colQuantity.AppearanceCell.Options.UseTextOptions = True
        colQuantity.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        colQuantity.Caption = "Qty."
        colQuantity.FieldName = "Quantity"
        colQuantity.MinWidth = 23
        colQuantity.Name = "colQuantity"
        colQuantity.Visible = True
        colQuantity.VisibleIndex = 3
        colQuantity.Width = 37
        ' 
        ' colCDMLongName
        ' 
        colCDMLongName.Caption = "Long Name"
        colCDMLongName.FieldName = "LongName"
        colCDMLongName.MinWidth = 23
        colCDMLongName.Name = "colCDMLongName"
        colCDMLongName.Width = 233
        ' 
        ' lblDescendantCount
        ' 
        lblDescendantCount.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left
        lblDescendantCount.Location = New Point(14, 386)
        lblDescendantCount.Margin = New Padding(4, 3, 4, 3)
        lblDescendantCount.Name = "lblDescendantCount"
        lblDescendantCount.Size = New Size(12, 13)
        lblDescendantCount.TabIndex = 16
        lblDescendantCount.Text = "..."
        ' 
        ' txtPhysicianCDM
        ' 
        txtPhysicianCDM.Location = New Point(457, 12)
        txtPhysicianCDM.Margin = New Padding(4, 3, 4, 3)
        txtPhysicianCDM.Name = "txtPhysicianCDM"
        txtPhysicianCDM.Size = New Size(117, 20)
        txtPhysicianCDM.TabIndex = 3
        ' 
        ' txtCDM
        ' 
        txtCDM.Location = New Point(123, 12)
        txtCDM.Margin = New Padding(4, 3, 4, 3)
        txtCDM.Name = "txtCDM"
        txtCDM.Size = New Size(117, 20)
        txtCDM.TabIndex = 1
        ' 
        ' chkCDMReplace
        ' 
        chkCDMReplace.EditValue = True
        chkCDMReplace.Location = New Point(16, 12)
        chkCDMReplace.Margin = New Padding(4, 3, 4, 3)
        chkCDMReplace.Name = "chkCDMReplace"
        chkCDMReplace.Properties.Caption = "Update CDM"
        chkCDMReplace.Size = New Size(100, 20)
        chkCDMReplace.TabIndex = 0
        ' 
        ' chkPhysicianCDMReplace
        ' 
        chkPhysicianCDMReplace.EditValue = True
        chkPhysicianCDMReplace.Location = New Point(298, 12)
        chkPhysicianCDMReplace.Margin = New Padding(4, 3, 4, 3)
        chkPhysicianCDMReplace.Name = "chkPhysicianCDMReplace"
        chkPhysicianCDMReplace.Properties.Caption = "Update Physician CDM"
        chkPhysicianCDMReplace.Size = New Size(152, 20)
        chkPhysicianCDMReplace.TabIndex = 2
        ' 
        ' grpEditHCPCS
        ' 
        grpEditHCPCS.Anchor = AnchorStyles.Top Or AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        grpEditHCPCS.Controls.Add(GridControl1)
        grpEditHCPCS.Location = New Point(14, 38)
        grpEditHCPCS.Margin = New Padding(4, 3, 4, 3)
        grpEditHCPCS.Name = "grpEditHCPCS"
        grpEditHCPCS.Size = New Size(578, 335)
        grpEditHCPCS.TabIndex = 26
        grpEditHCPCS.Text = "..."
        ' 
        ' CDMEditDialog
        ' 
        AcceptButton = btnSave
        AutoScaleDimensions = New SizeF(7F, 15F)
        AutoScaleMode = AutoScaleMode.Font
        CancelButton = Cancel_Button
        ClientSize = New Size(607, 427)
        ControlBox = False
        Controls.Add(grpEditHCPCS)
        Controls.Add(txtPhysicianCDM)
        Controls.Add(lblDescendantCount)
        Controls.Add(chkPhysicianCDMReplace)
        Controls.Add(chkCDMReplace)
        Controls.Add(TableLayoutPanel1)
        Controls.Add(txtCDM)
        Margin = New Padding(4, 3, 4, 3)
        MaximizeBox = False
        MinimizeBox = False
        Name = "CDMEditDialog"
        ShowInTaskbar = False
        SizeGripStyle = SizeGripStyle.Show
        StartPosition = FormStartPosition.CenterParent
        Text = "Edit CDM"
        TableLayoutPanel1.ResumeLayout(False)
        CType(GridControl1, ComponentModel.ISupportInitialize).EndInit()
        CType(GridView1, ComponentModel.ISupportInitialize).EndInit()
        CType(txtPhysicianCDM.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(txtCDM.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(chkCDMReplace.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(chkPhysicianCDMReplace.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(grpEditHCPCS, ComponentModel.ISupportInitialize).EndInit()
        grpEditHCPCS.ResumeLayout(False)
        ResumeLayout(False)
        PerformLayout()
    End Sub
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents btnSave As Button
    Friend WithEvents Cancel_Button As Button
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colCDMCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDMLongName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPhysicianCDMCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDMHCPCS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colQuantity As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents lblDescendantCount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtPhysicianCDM As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtCDM As DevExpress.XtraEditors.TextEdit
    Friend WithEvents chkCDMReplace As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents chkPhysicianCDMReplace As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents grpEditHCPCS As DevExpress.XtraEditors.GroupControl
End Class

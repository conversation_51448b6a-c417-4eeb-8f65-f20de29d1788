<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FacilityConfigImportForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.progressControl = New DevExpress.XtraEditors.ProgressBarControl()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.cboTo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonEdit1 = New DevExpress.XtraEditors.ButtonEdit()
        Me.ceImportCDM = New DevExpress.XtraEditors.CheckEdit()
        Me.ceImportReports = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.progressControl.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceImportCDM.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceImportReports.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'progressControl
        '
        Me.progressControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.progressControl.Location = New System.Drawing.Point(23, 104)
        Me.progressControl.Name = "progressControl"
        Me.progressControl.Size = New System.Drawing.Size(517, 18)
        Me.progressControl.TabIndex = 17
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.Location = New System.Drawing.Point(465, 138)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 16
        Me.btnCancel.Text = "Cancel"
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.Enabled = False
        Me.btnOK.Location = New System.Drawing.Point(370, 138)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(75, 23)
        Me.btnOK.TabIndex = 15
        Me.btnOK.Text = "OK"
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(23, 66)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(99, 13)
        Me.LabelControl2.TabIndex = 14
        Me.LabelControl2.Text = "Facility to import to: "
        '
        'cboTo
        '
        Me.cboTo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboTo.Location = New System.Drawing.Point(153, 63)
        Me.cboTo.Name = "cboTo"
        Me.cboTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboTo.Properties.ImmediatePopup = True
        Me.cboTo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboTo.Size = New System.Drawing.Size(387, 20)
        Me.cboTo.TabIndex = 13
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(23, 26)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(126, 13)
        Me.LabelControl1.TabIndex = 12
        Me.LabelControl1.Text = "Config file to import from: "
        '
        'ButtonEdit1
        '
        Me.ButtonEdit1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonEdit1.Location = New System.Drawing.Point(153, 23)
        Me.ButtonEdit1.Name = "ButtonEdit1"
        Me.ButtonEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.ButtonEdit1.Size = New System.Drawing.Size(387, 20)
        Me.ButtonEdit1.TabIndex = 18
        '
        'ceImportCDM
        '
        Me.ceImportCDM.Location = New System.Drawing.Point(21, 136)
        Me.ceImportCDM.Name = "ceImportCDM"
        Me.ceImportCDM.Properties.Caption = "Import CDM Table Records"
        Me.ceImportCDM.Size = New System.Drawing.Size(184, 19)
        Me.ceImportCDM.TabIndex = 19
        '
        'ceImportReports
        '
        Me.ceImportReports.Location = New System.Drawing.Point(21, 161)
        Me.ceImportReports.Name = "ceImportReports"
        Me.ceImportReports.Properties.Caption = "Beta Import Admin Report Records"
        Me.ceImportReports.Size = New System.Drawing.Size(215, 19)
        Me.ceImportReports.TabIndex = 20
        '
        'FacilityConfigImportForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(553, 187)
        Me.ControlBox = False
        Me.Controls.Add(Me.ceImportReports)
        Me.Controls.Add(Me.ceImportCDM)
        Me.Controls.Add(Me.ButtonEdit1)
        Me.Controls.Add(Me.progressControl)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.cboTo)
        Me.Controls.Add(Me.LabelControl1)
        Me.MaximizeBox = False
        Me.Name = "FacilityConfigImportForm"
        Me.ShowIcon = False
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Import Facility Configuration"
        CType(Me.progressControl.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceImportCDM.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceImportReports.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents progressControl As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboTo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonEdit1 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ceImportCDM As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceImportReports As DevExpress.XtraEditors.CheckEdit
End Class

Imports Microsoft.Web.Services2.Dime
Imports System.IO
Imports System.Threading
Imports Timeout

Public Class Form1
    'change the web references/ECWS/Reference.map/Reference.vb 
    'partial public class service inherits line to  the following
    'if the web reference is updated
    'Inherits Microsoft.Web.Services2.WebServicesClientProtocol

    Dim file_extensions_array() As String = {"txt", "xml"}
    Dim file_extensions_list As New List(Of String)


    Dim service_url As String

    Dim send_continuous As Boolean = False
    Dim send_continuous_count As Integer = 0
    Dim send_continuous_account_number As String
    Dim send_continuous_thread As New Thread(AddressOf SendContinuous)

    Dim single_drop As Boolean

#Region "Form Events"
    Private Sub Form1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        SetupIdleTimeout()

        My.Application.Log.DefaultFileLogWriter.CustomLocation = System.Windows.Forms.Application.StartupPath & "\Logs"
        My.Application.Log.DefaultFileLogWriter.BaseFileName = "datalinkclient-" & Format(Now, "MM-dd-yy")
        My.Application.Log.DefaultFileLogWriter.AutoFlush = True
        My.Application.Log.DefaultFileLogWriter.Append = True

        InitializeFileTypeList(file_extensions_array)

        If My.Application.CommandLineArgs.Count = 3 Then
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Command Line Arguments Passed - Running In Automatic Send Mode", TraceEventType.Information)

            Dim forward_address As String = My.Application.CommandLineArgs(0)
            Dim info_file As String = My.Application.CommandLineArgs(1)
            Dim xml_file As String = My.Application.CommandLineArgs(2)

            service_url = forward_address
            SetTextFile(info_file)
            SetDataFile(xml_file)

            Dim resultinfo As ECWS.ENCodingResult = SendChart()

            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Result of Automatic Send: " & resultinfo.resultCode & "-" & resultinfo.resultText, TraceEventType.Information)
            Me.Close()
        Else
            service_url = My.Settings.DIMEClient01_localhost_Service

            stplblURL.Text = service_url

            send_continuous_thread.Start()
        End If

    End Sub
    Private Sub Form1_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        send_continuous_thread.Abort()
    End Sub
#End Region

#Region "Timeout Functionality"
    Private Sub SetupIdleTimeout()
        BasicTimeoutWatcher.Init(Me, AddressOf HandleIdleTimeoutShutdown)
    End Sub
    Private Sub HandleIdleTimeoutShutdown()
        Me.Close()
    End Sub
#End Region

#Region "Control Events"
    Private Sub btnSend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSend.Click
        BasicTimeoutWatcher.Suspend()

        Dim resultinfo As ECWS.ENCodingResult = SendChart()
        MessageBox.Show(resultinfo.resultCode & ":" & resultinfo.resultText, "Results", MessageBoxButtons.OK, MessageBoxIcon.Information)

        BasicTimeoutWatcher.Resume()
    End Sub
    Private Sub btnSendX_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSendX.Click
        BasicTimeoutWatcher.Suspend()

        Dim i As Integer = 0
        Dim success_count As Integer = 0
        Dim failure_count As Integer = 0

        For i = 1 To CInt(txtSendX.Text)

            If SendChart().resultCode = 0 Then
                success_count += 1
            Else
                failure_count += 1
            End If

        Next

        MessageBox.Show("Successes: " & success_count & ", Failures: " & failure_count, "Results", MessageBoxButtons.OK, MessageBoxIcon.Information)

        BasicTimeoutWatcher.Resume()
    End Sub
    Private Sub btnContinuous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnContinuous.Click

        If Not send_continuous Then
            send_continuous_count = 0
            send_continuous_account_number = txtAccountNumber.Text

            btnContinuous.Text = "Stop"

            tmrContinuous.Interval = CInt(txtContinuousMins.Text) * 60 * 1000
            tmrContinuous.Start()

            BasicTimeoutWatcher.Suspend()
        Else
            btnContinuous.Text = "Continuous"
            txtAccountNumber.Text = send_continuous_account_number

            BasicTimeoutWatcher.Resume()
        End If

        send_continuous = Not send_continuous

    End Sub
    Private Sub btnSendFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSendFolder.Click
        BasicTimeoutWatcher.Suspend()

        Dim sfd As New SendFolderDialog
        Dim results As New SendResults

        If sfd.ShowDialog = System.Windows.Forms.DialogResult.OK Then
            results = SendFolder(sfd.SendFolder)
        End If

        MessageBox.Show("Successes: " & results.Successess & ", Failures: " & results.Failures, "Results", MessageBoxButtons.OK, MessageBoxIcon.Information)

        BasicTimeoutWatcher.Resume()
    End Sub

    Private Function SendFolder(ByVal send_folder As String) As SendResults
        My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Sending charts from folder: " & send_folder, TraceEventType.Information)
        Dim results As New SendResults

        Dim sub_folders() As String = Directory.GetDirectories(send_folder)
        Dim all_files() As String = Directory.GetFiles(send_folder, "info-*.txt")

        For Each info_file As String In all_files
            Dim xml_file As String = info_file.Replace("info-", "data-")
            xml_file = xml_file.Replace(".txt", ".xml")

            If File.Exists(xml_file) Then

                SetDataFile(xml_file)
                SetTextFile(info_file)

                If SendChart().resultCode = 0 Then
                    results.AddSuccess()
                Else
                    results.AddFailure()
                End If

            Else
                My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Could not find match for file: " & info_file, TraceEventType.Information)
            End If
        Next

        My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & ControlChars.Tab & "Results from this folder: " & results.ToString, TraceEventType.Information)

        For Each folder As String In sub_folders
            results.AddResults(SendFolder(folder))
        Next

        Return results
    End Function

    Private Sub btnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClear.Click
        For Each ctrl As Control In Me.Controls
            If ctrl.GetType Is GetType(DevExpress.XtraEditors.TextEdit) Then
                Dim tb As DevExpress.XtraEditors.TextEdit = CType(ctrl, DevExpress.XtraEditors.TextEdit)
                tb.Text = ""
            End If
        Next

        txtSendX.Text = 50
        txtContinuousMins.Text = 60
    End Sub

    Private Sub stplblIP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles stplblURL.Click
        Dim url_dialog As New ServiceURLDialog
        Dim result As DialogResult = url_dialog.ShowDialog(service_url)
        If result = System.Windows.Forms.DialogResult.OK Then
            service_url = url_dialog.WebServiceURL
            stplblURL.Text = service_url
        End If
    End Sub
    Private Sub stplblLogFile_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles stplblLogFile.Click
        Process.Start("notepad.exe", My.Application.Log.DefaultFileLogWriter.FullLogFileName)
    End Sub

    Private Sub txtXMLFile_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtXMLFile.DoubleClick
        txtXMLFile.SelectAll()
    End Sub

    Private Sub tmrContinuous_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrContinuous.Tick
        send_continuous = False
        tmrContinuous.Stop()
        btnContinuous.Text = "Continuous"
    End Sub
#End Region

#Region "File Handling"
    Private Sub Form1_DragEnter(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles Me.DragEnter
        If e.Data.GetDataPresent(DataFormats.FileDrop) Then
            e.Effect = DragDropEffects.Move
        Else
            e.Effect = DragDropEffects.None
        End If
    End Sub
    Private Sub Form1_DragDrop(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles Me.DragDrop
        If e.Data.GetDataPresent(DataFormats.FileDrop) Then
            Dim files() As String = CType(e.Data.GetData(DataFormats.FileDrop), String())
            single_drop = (files.Length = 1)
            ProcessFiles(files)
        End If
    End Sub
    Private Sub ProcessFiles(ByVal files() As String)
        For Each f As String In files
            If IsExtensionOK(f) Then
                If FileHasExtension(f, "xml") Then
                    SetDataFile(f)

                    If single_drop Then
                        Dim possible_text_file As String = f.Replace("data-", "info-")
                        possible_text_file = possible_text_file.Replace(".xml", ".txt")

                        If File.Exists(possible_text_file) Then
                            SetTextFile(possible_text_file)
                        End If
                    End If

                ElseIf FileHasExtension(f, "txt") Then
                    SetTextFile(f)

                    If single_drop Then
                        Dim possible_data_file As String = f.Replace("info-", "data-")
                        possible_data_file = possible_data_file.Replace(".txt", ".xml")

                        Console.WriteLine(possible_data_file)

                        If File.Exists(possible_data_file) Then
                            SetDataFile(possible_data_file)
                        End If
                    End If
                End If
            End If
        Next
    End Sub
    Private Sub SetDataFile(ByVal f As String)
        txtXMLFile.Text = f
    End Sub
    Private Sub SetTextFile(ByVal f As String)
        PopulateDemographicInfo(New StreamReader(f))
    End Sub
    Private Sub PopulateDemographicInfo(ByVal info_reader As StreamReader)
        Dim line As String
        While Not info_reader.EndOfStream
            line = info_reader.ReadLine
            Dim colon_loc As Integer = line.IndexOf(":")

            If colon_loc >= 0 Then
                Dim key As String = line.Substring(0, colon_loc)
                Dim value As String = line.Substring(colon_loc + 1)

                Select Case key.ToUpper
                    Case "FACILITY"
                        txtFacility.Text = value
                    Case "TREATMENTAREA"
                        txtTreatmentArea.Text = value
                    Case "ACCOUNTNO"
                        txtAccountNumber.Text = value
                    Case "DATEOFSERVICE"
                        txtDOS.Text = value
                        'Case "CHARTVIEWURL"
                    Case "FIRSTNAME"
                        txtFirstName.Text = value
                    Case "MIDDLENAME"
                        txtMiddleName.Text = value
                    Case "LASTNAME"
                        txtLastName.Text = value
                    Case "SUFFIXNAME"
                        txtSuffix.Text = value
                    Case "DOB"
                        txtDOB.Text = value
                    Case "GENDER"
                        txtGender.Text = value
                    Case "MRN"
                        txtMRN.Text = value
                    Case "SS"
                        txtSSN.Text = value
                    Case "FINANCIALCLASS"
                        txtFinancialClass.Text = value
                    Case "ADDRESSLINE1"
                        txtAddress1.Text = value
                    Case "ADDRESSLINE2"
                        txtAddress2.Text = value
                    Case "CITY"
                        txtCity.Text = value
                    Case "STATE"
                        txtState.Text = value
                    Case "ZIPCODE"
                        txtZip.Text = value
                End Select
            End If
        End While
    End Sub

    Private Sub InitializeFileTypeList(ByVal extensions() As String)
        For Each ext As String In extensions
            file_extensions_list.Add(ext)
        Next
    End Sub
    Private Function IsExtensionOK(ByVal file As String) As Boolean
        Return file_extensions_list.Contains(Path.GetExtension(file).Substring(1).ToLower)
    End Function
    Private Function FileHasExtension(ByVal file As String, ByVal ext As String) As Boolean
        Return file.ToUpper.EndsWith(ext.ToUpper)
    End Function
#End Region

    Private Sub SendContinuous()
        Dim random_obj As Random

        Do While True

            If send_continuous Then

                SetText(txtAccountNumber, send_continuous_account_number & send_continuous_count.ToString.PadLeft(4, "0"))

                random_obj = New Random
                Dim interval_secs As Integer = random_obj.Next(150)
                Dim interval_mils = interval_secs * 1000

                My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Sending Next Chart in " & (interval_secs) & " seconds.", TraceEventType.Information)

                Thread.Sleep(interval_mils)

                SendChart()

                send_continuous_count += 1

            Else

                Thread.Sleep(100)

            End If

        Loop

    End Sub
    Private Function SendChart() As ECWS.ENCodingResult
        Dim resultInfo As New ECWS.ENCodingResult

        Try
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Sending Chart " & txtAccountNumber.Text, TraceEventType.Information)
            Dim ecws As New ECWS.Service(service_url)
            Dim filePath As String = txtXMLFile.Text
            Dim patChartinfo As New ECWS.ENPatientChartingInfo
            Dim patDemoInfo As New ECWS.ENPatientDemographicInfo


            Dim dimeAttach As New DimeAttachment("data/XML", TypeFormat.MediaType, filePath)

            patChartinfo = PopulateChartInfo()
            patDemoInfo = PopulateDemoInfo()

            ecws.RequestSoapContext.Attachments.Add(dimeAttach)
            resultInfo = ecws.sendChart(patChartinfo, patDemoInfo)

            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Chart " & txtAccountNumber.Text & " Result Code: " & resultInfo.resultCode & ", Text: " & resultInfo.resultText, TraceEventType.Information)

        Catch ex As Exception
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Error: " & ex.ToString, TraceEventType.Information)
            My.Application.Log.WriteEntry(Format(Now, "MM-dd-yy HH:mm:ss") & ControlChars.Tab & "Chart Result Code: " & resultInfo.resultCode & ", Text: " & resultInfo.resultText, TraceEventType.Information)
        End Try

        Return resultInfo
    End Function
    Private Function PopulateChartInfo() As ECWS.ENPatientChartingInfo
        Dim ReturnVal As New ECWS.ENPatientChartingInfo

        ReturnVal.accountNo = txtAccountNumber.Text
        ReturnVal.chartViewURL = "http://pep:12309/chartview/showview?acctNo=********&viewName=EDView"
        ReturnVal.dateOfService = txtDOS.Text
        ReturnVal.facility = txtFacility.Text
        ReturnVal.treatmentArea = txtTreatmentArea.Text

        Return ReturnVal
    End Function
    Private Function PopulateDemoInfo() As ECWS.ENPatientDemographicInfo
        Dim ReturnVal As New ECWS.ENPatientDemographicInfo

        ReturnVal.firstName = txtFirstName.Text
        ReturnVal.middleName = txtMiddleName.Text
        ReturnVal.lastName = txtLastName.Text
        ReturnVal.suffixName = txtSuffix.Text
        ReturnVal.DOB = txtDOB.Text
        ReturnVal.gender = txtGender.Text
        ReturnVal.MRN = txtMRN.Text
        ReturnVal.SS = txtSSN.Text
        ReturnVal.financialClass = txtFinancialClass.Text
        ReturnVal.addressLine1 = txtAddress1.Text
        ReturnVal.addressLine2 = txtAddress2.Text
        ReturnVal.city = txtCity.Text
        ReturnVal.state = txtState.Text
        ReturnVal.zipCode = txtZip.Text

        Return ReturnVal
    End Function


#Region "Delegates"
    Private del_text As New TextCallback(AddressOf DoSetText)
    Private Delegate Sub TextCallback(ByVal o As Object, ByVal val As Object)
    Private Sub DoSetText(ByVal o As Object, ByVal val As Object)
        o.Text = val
    End Sub
    Public Sub SetText(ByVal o As Object, ByVal val As Object)
        If o.InvokeRequired Then
            Me.Invoke(del_text, New Object() {o, val})
        Else
            o.Text = val
        End If
    End Sub
#End Region
End Class

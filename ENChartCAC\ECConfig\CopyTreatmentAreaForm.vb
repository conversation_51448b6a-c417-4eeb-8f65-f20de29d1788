﻿Option Infer On

Imports ECConfig.MainForm
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports System.ComponentModel
Imports System.Collections.Specialized
Imports Timeout
Imports System.Threading.Tasks

Public Class CopyTreatmentAreaForm

    Private Sub CopyTreatmentAreaForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim fcol As New XPCollection(GetType(DOFacility))
        fcol.Reload()
        If fcol IsNot Nothing Then
            For Each facility In fcol
                cboFrom.Properties.Items.Add(facility)
                cboTo.Properties.Items.Add(facility)
            Next
        End If
    End Sub

    Private Sub cboFrom_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboFrom.SelectedIndexChanged
        Dim facilityName = cboFrom.Text
        If String.IsNullOrEmpty(facilityName) Then
            Return
        End If
        Dim facility As DOFacility = cboFrom.EditValue
        Dim sc As StringCollection = EnchartDOLib.Utils.GetTreatmentAreasAsListByFacility(facility)

        fromTreatmeantArea.Properties.Items.Clear()
        For Each ta In sc
            fromTreatmeantArea.Properties.Items.Add(ta)
        Next

        cboTo.EditValue = cboFrom.EditValue
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Async Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If String.IsNullOrEmpty(cboFrom.Text) Or String.IsNullOrEmpty(fromTreatmeantArea.Text) Or String.IsNullOrEmpty(toTA.Text) Then
            MessageBox.Show("Think harder!")
            Return
        End If

        Try
            BasicTimeoutWatcher.Suspend()
            Me.btnOK.Enabled = False
            Me.btnCancel.Enabled = False
            Me.cboFrom.Enabled = False
            cboTo.Enabled = False

            fromTreatmeantArea.Enabled = False
            toTA.Enabled = False

            Me.Text = "TA Copy"

            Dim fromFac As DOFacility = cboFrom.EditValue
            Dim toFac As DOFacility = cboTo.EditValue
            Dim taList As New StringCollection
            taList.Add(fromTreatmeantArea.EditValue)

            Dim fp As New FacilityProxy(fromFac, taList, Nothing)
            UpDateProgress(-1)
            'Change Name of treatmeant area in Source to new name
            Dim toTAName As String = toTA.EditValue
            fp.TreatmentAreas(0).TreatmentArea = toTAName
            Dim pi As New Progress(Of Integer)(AddressOf UpDateProgress)
            Dim tt = Task.Run(Sub() Utils.LoadConfigFromProxy(toFac, fromFac.ConfigInstanceVersion, fp, pi,
                                  ceImportCDM.Checked, bLoadReports:=False, onlyLoadTAs:=True,
                                  sourceTAName:=fromTreatmeantArea.EditValue))
            Await tt

            'Utils.LoadConfigFromProxy(toFac, fromFac.ConfigInstanceVersion, fp, AddressOf UpDateProgress,
            '                      ceImportCDM.Checked, bLoadReports:=False, onlyLoadTAs:=True,
            '                      sourceTAName:=fromTreatmeantArea.EditValue)
            UpDateProgress(100)

            Me.Text = "TA Copy"
            BasicTimeoutWatcher.Resume()
            MessageBox.Show("Treatmean Area successfully copied", "Be sure to test!", MessageBoxButtons.OK, MessageBoxIcon.Information)
            'Me.Close()

            Me.btnOK.Enabled = True
            Me.btnCancel.Enabled = True
            Me.cboFrom.Enabled = True
            cboTo.Enabled = True

            fromTreatmeantArea.Enabled = True
            toTA.Enabled = True
        Finally
            BasicTimeoutWatcher.Resume()
        End Try

    End Sub

    Private _nextRefreshePos As Integer = 0
    Public Sub UpDateProgress(ByVal pDone As Integer)
        Me.progressControl.Position = pDone

        If pDone = -1 Then
            _nextRefreshePos = 1
        End If

        If pDone >= _nextRefreshePos Then
            _nextRefreshePos += 1
            Me.Refresh()
            Application.DoEvents()
        End If

    End Sub

    Private Sub cboTo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboTo.SelectedIndexChanged
        Try
            If cboTo.EditValue = cboFrom.EditValue = False Then
                toTA.EditValue = fromTreatmeantArea.EditValue
            End If
        Catch
        End Try
    End Sub
End Class
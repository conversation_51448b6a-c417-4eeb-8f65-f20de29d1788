﻿Imports System.Data

Public Class DatasetViewer

    'Public Sub New()

    '    ' This call is required by the designer.
    '    InitializeComponent()

    '    ' Add any initialization after the InitializeComponent() call.

    'End Sub

    Public Sub New(ds As DataSet)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        If ds.Tables.Count > 0 Then
            GridControl1.DataSource = ds.Tables(0)
            GridView1.PopulateColumns()
        End If

    End Sub
End Class
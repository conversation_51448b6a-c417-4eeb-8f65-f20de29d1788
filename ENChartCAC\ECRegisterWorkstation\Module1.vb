

Imports EnchartDOLib
Imports System.Configuration
Imports DevExpress.Xpo
Imports DevExpress.Data
Imports DevExpress.Xpo.DB
Imports DevExpress.Data.Filtering
Imports System.IO
Imports System.Security
Imports System.Security.Permissions
Imports System.Security.Principal
Imports System.Collections.Specialized
Module Module1
    Dim id As String = "$ID"

    Sub Main(ByVal cmdArgs() As String)
        Try

            If cmdArgs.Length <> 2 Then
                Console.WriteLine("Please enter the password followed by a space and the comment.")
                Exit Sub
            End If

            Dim password As String = cmdArgs(0)
            Dim comment As String = cmdArgs(1)

            If password <> "esp!" Then
                Console.WriteLine("Invalid password!")
                Exit Sub
            End If

            If String.IsNullOrEmpty(comment) Then
                Console.WriteLine("You must enter some type of comment, in quotes")
                Exit Sub
            End If

            Utils.ConnectToECDataBase()
            UpdateDBSchema()

            If Not IsValidWorkstation() Then

                Dim xpcol As New XPCollection(Of DOWLV)
                If xpcol.Count >= WorkStationValidationCount() Then
                    Console.WriteLine("Error - The license limit of " & WorkStationValidationCount.ToString & " has already been met, workstation not added")
                    Exit Sub
                End If

                Dim ThisMachine As New DOWLV
                ThisMachine.WorkstationName = My.Computer.Name
                ThisMachine.Comment = comment
                ThisMachine.Save()
                Console.WriteLine(My.Computer.Name & " Successfully  registered")

            Else
                Console.WriteLine(My.Computer.Name & " Is already registered")
            End If
        Finally
            Console.WriteLine("Press any key to continue...")
            Console.ReadKey()

        End Try

    End Sub


    Sub UpdateDBSchema()
        Try
            'After trying a number of different things... the below call appears to be all that is
            'needed to ask XPO to make sure there is table for each of our Data Objects...
            Session.DefaultSession.UpdateSchema()
        Catch ex As Exception
            Dim msg As String = String.Format("An error occured while trying to update the database schema.")
            ECLogFileLogWrapper.WriteEntry(msg, TraceEventType.Error)

            'MessageBox.Show(msg, "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Function WorkStationValidationCount() As Integer
        Dim xpcol As New XPCollection(Of DODBLicValidation)
        If xpcol Is Nothing Then Return False


        If xpcol.Count = 1 Then
            Return xpcol(0).MaxLic
        End If

        Return False

        'If xpcol.Count <> 1 Then Return False

        'Dim wkve As DODBLicValidation = xpcol(0)
        'If wkve.Enabled Then Return True

    End Function

    Function IsValidWorkstation() As Boolean
        Try
            Dim wksname As String = My.Computer.Name
            Dim xpcol As New XPCollection(Of DOWLV)(CriteriaOperator.Parse(String.Format("WorkstationName = '{0}'", wksname)))

            If xpcol Is Nothing Then
                Console.WriteLine("Unexpeted error, machine name not added")
                'ECLog.WriteEntry("Could not find workstation name in databae", TraceEventType.Error)
            End If

            If xpcol.Count = 1 Then Return True

            Return False
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
    End Function

    Sub ConnectToECDataBase()
        Dim fm As New System.Configuration.ExeConfigurationFileMap
        fm.ExeConfigFilename = ".\ENChartCAC.exe.config"

        Dim config As Configuration = ConfigurationManager.OpenMappedExeConfiguration(fm, ConfigurationUserLevel.None)
        'Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
        Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
        cs += ";uid=dba;pwd=sql"

        XpoDefault.DataLayer = XpoDefault.GetDataLayer(cs, AutoCreateOption.DatabaseAndSchema)
    End Sub

End Module

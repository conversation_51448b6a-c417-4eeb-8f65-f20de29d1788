Imports System.Text.RegularExpressions

Public Class VerifyChartIDForm

#Region "Fields"

    Private _ChartID As String
    Private _ChartIDMask As String

#End Region 'Fields

#Region "Properties"

    Public Property ChartID() As String
        Get
            Return _ChartID
        End Get
        Set(ByVal value As String)
            _ChartID = value
            '            Me.txtVisitID.EditValue = value
        End Set
    End Property

    Public Property ChartIDMask() As String
        Get
            Return _ChartIDMask
        End Get
        Set(ByVal value As String)
            _ChartIDMask = value

        End Set
    End Property

#End Region 'Properties

#Region "Methods"

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.ChartID = Me.txtVisitID.EditValue
    End Sub

    Private Sub txtVisitID_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVisitID.EditValueChanged
        If String.IsNullOrEmpty(Me.txtVisitID.EditValue) OrElse String.IsNullOrEmpty(Me.ChartIDMask) Then
            Me.txtVisitID.ForeColor = Color.Red
            Me.btnOK.Enabled = False
            Return
        End If

        'Use the '^' to mark begging of line and '$' to mark end of imput...
        If Regex.IsMatch(Me.txtVisitID.EditValue, "^" & Me.ChartIDMask & "$") = False Then
            Me.txtVisitID.ForeColor = Color.Red
            Me.btnOK.Enabled = False
        Else
            Me.txtVisitID.ForeColor = Color.Black
            Me.btnOK.Enabled = True
        End If
    End Sub

    '    If String.IsNullOrEmpty(Me.ChartID) Then
    '        Me.btnOK.Enabled = False
    '    Else
    '        If Regex.IsMatch(Me.txtVisitID.EditValue, Me.ChartIDMask) Then
    '            Me.txtVisitID.ForeColor = Color.Red
    '            Me.btnOK.Enabled = False
    '        Else
    '            Me.txtVisitID.ForeColor = Color.Black
    '            Me.btnOK.Enabled = True
    '        End If
    '    End If
    'End Sub
    Private Sub txtVisitID_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVisitID.Enter
        Me.txtVisitID.SelectionStart = 0
        If Me.txtVisitID.EditValue IsNot Nothing Then
            Me.txtVisitID.SelectionLength = Me.txtVisitID.EditValue.ToString.Length
        End If
    End Sub

    Private Sub VerifyChartIDForm_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        Me.txtVisitID.Focus()
    End Sub

    Private Sub VerifyChartIDForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If String.IsNullOrEmpty(Me.ChartID) Then
            Me.btnOK.Enabled = False
        Else

            If Regex.IsMatch(Me.ChartID, Me.ChartIDMask) = False Then
                Me.txtVisitID.ForeColor = Color.Red
                Me.btnOK.Enabled = False
            Else
                Me.txtVisitID.ForeColor = Color.Black
                Me.btnOK.Enabled = True
            End If
        End If
        ' Me.LabelControl1.Focus()
        Me.txtVisitID.EditValue = Me.ChartID
        ' Me.txtVisitID.Focus()
    End Sub

#End Region 'Methods

End Class
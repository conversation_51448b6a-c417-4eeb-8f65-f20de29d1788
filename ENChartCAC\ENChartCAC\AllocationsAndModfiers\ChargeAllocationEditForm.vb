﻿Imports System.ComponentModel
Imports System.Linq
Imports AIC.SharedData
Imports AIC.SharedData.CAM2
Imports DevExpress.Data
Imports DevExpress.Xpo
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Repository
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports EnchartDOLib

Partial Public Class ChargeAllocationEditForm

    Public Property SaveChanges As Boolean = False
    Private CurrentView As Views.Grid.GridView
    Private modifiersControls As New List(Of ComboBoxEdit)

    Public Class TreatementAreaNurseUnit
        Private _TA As String

        Public Property TA As String
            Get
                Return _TA
            End Get
            Set
                _TA = Value
            End Set
        End Property

        Public Property TAType As String
        Public Property CDM As String
    End Class

    Public taBindingList As New BindingList(Of TreatementAreaNurseUnit)

    Private Charge As Charge
    Private ReadOnly bShowFacility As Boolean
    Private ReadOnly bShowObs As Boolean
    Public AllocationsList As BindingList(Of ChargeAllocation)
    Public ChargeBackup As Charge
    Public Sub New(ByRef charge As Charge, bShowFacility As Boolean, bShowObs As Boolean)
        ' This call is required by the designer.
        InitializeComponent()
        Me.CurrentView = Me.gridControl.MainView

        ' Add any initialization after the InitializeComponent() call.
        ChargeBackup = New Charge(charge.Hcpcs)
        ChargeBackup.ChargeAllocations = New List(Of ChargeAllocation)(charge.ChargeAllocations)
        ChargeBackup.Modifiers = New List(Of ChargeModifier)(charge.Modifiers)

        Me.Charge = charge
        Me.bShowFacility = bShowFacility
        Me.bShowObs = bShowObs
        ChargeBindingSource.DataSource = charge

        AllocationsList = New BindingList(Of ChargeAllocation)(charge.ChargeAllocations)
        AllocationsList.AllowNew = True
        AllocationsList.RaiseListChangedEvents = True
        AddHandler AllocationsList.AddingNew, AddressOf AddingNewRow
        AddHandler AllocationsList.ListChanged, AddressOf ListChanged
        gridControl.DataSource = AllocationsList

        CreateModControlsList()
        InitModLists()
        UpdateModifiersUIControls()
        SetupEventHandlersForModifierControls()
        PopulatePhysicianLists()

        DOSDateEdit.Properties.MinValue = charge.OriginalBillingdate.Date

        AddHandler gridView.CustomRowCellEdit, AddressOf GridView_CustomRowCellEdit

    End Sub

    Private Sub GridView_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs)
        Dim view As GridView = TryCast(sender, GridView)
        If e.Column.FieldName = "Physician" AndAlso view IsNot Nothing Then
            Dim rowHandle As Integer = e.RowHandle
            ' Get the value of the other column
            Dim otherColumnValue As Object = view.GetRowCellValue(rowHandle, "TreatmentAreaType")
            If otherColumnValue IsNot Nothing AndAlso otherColumnValue.ToString() = "Obs" Then
                Dim itemComboBox As New RepositoryItemComboBox()
                itemComboBox.Items.AddRange(obsPhysList)
                itemComboBox.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
                e.RepositoryItem = itemComboBox
            Else
                Dim itemComboBox As New RepositoryItemComboBox()
                itemComboBox.Items.AddRange(facilityPhysList)
                itemComboBox.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
                e.RepositoryItem = itemComboBox
            End If
        End If
    End Sub

    Private maxUnits As Int16
    Private Sub XtraForm1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DOSDateEdit.EditValue = Charge.OriginalBillingdate

        taList = GetTAList(Charge.CodeType)
        SearchLookUpEdit1.Properties.DataSource = taList

        Dim taBAList As New BindingList(Of TreatementAreaNurseUnit)(taList)

        RepositoryItemGridLookUpEdit1.DataSource = taBAList 'taList
        RepositoryItemGridLookUpEdit1.DisplayMember = "TA"
        RepositoryItemGridLookUpEdit1.ValueMember = "TA"

        AddNewRowbtn.Enabled = False

        If String.IsNullOrWhiteSpace(DOSDateEdit.Text) Then
            DOSDateEdit.EditValue = OriginalBillingdateTextBox.Text
        End If

        SearchLookUpEdit1.EditValue = TreatmentAreaTextBox.Text

        UnitsSpinEdit.Properties.MinValue = 1
        maxUnits = TotalUnitsTextBox.Text
        UpdateUnitsRemaining()

        EditGroupControl.Enabled = True
        DOSDateEdit.Select()
    End Sub

    Private Function GetCdm(treatmentArea As String) As String
        If treatmentArea Is Nothing Then Return Nothing
        If hcpcsList Is Nothing Then Return "N/A"
        For Each item In hcpcsList
            If item.TreatmentArea.ToUpper() = treatmentArea.ToUpper() Then
                Return item.CDM
            End If
        Next
        Return Nothing
    End Function

    Private hcpcsList As List(Of DOChargeMaster)
    Private taList As List(Of TreatementAreaNurseUnit)


    Private Function GetTAList(codeType) As List(Of TreatementAreaNurseUnit)
        Dim includeObsOnly As Boolean = False
        If codeType = "Obs" Then
            includeObsOnly = True
        End If

        Dim hcpcs = Charge.Hcpcs.Substring(0, 5)
        If hcpcs IsNot Nothing Then
            hcpcsList = (From esp In New XPQuery(Of DOChargeMaster)(XpoDefault.Session)
                         Where esp.Facility = ECGlobals.CurrentFacility And esp.HCPCS = hcpcs
                         Select esp).ToList
        End If

        Dim returnlist As New List(Of TreatementAreaNurseUnit)

        Dim doTa = GetTreatmentAreasByFacility(ECGlobals.CurrentFacility)
        For Each dotaItem In doTa
            If dotaItem.Enabled = False Then Continue For
            Dim newOne = New TreatementAreaNurseUnit() With {
                .TA = dotaItem.Name,
                .TAType = GetTaType(dotaItem),
                .CDM = GetCdm(dotaItem.ChargeMaster)}

            If includeObsOnly AndAlso newOne.TAType <> "Obs" Then Continue For
            If newOne.TAType = "Obs" AndAlso bShowObs = False Then Continue For
            If newOne.TAType = "Facility" AndAlso bShowFacility = False Then Continue For
            returnlist.Add(newOne)
        Next

        Return returnlist
    End Function
    Public Function GetTaType(ta As DOTreatmentArea) As String
        Dim taType As String
        If ta?.TreatmentAreaType?.ToUpper = "OBS" Then
            taType = "Obs"
        Else
            taType = "Facility"
        End If
        Return taType
    End Function


    Private currentChargeAllocation As CAM2.ChargeAllocation

    Private Sub gridView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gridView.FocusedRowChanged
        Debug.Print("gridView_FocusedRowChanged")
        currentChargeAllocation = gridView.GetFocusedRow
        If currentChargeAllocation IsNot Nothing Then
            bbiEdit.Enabled = True
            bbiDelete.Enabled = True
        Else
            bbiEdit.Enabled = False
            bbiDelete.Enabled = True
            Return
        End If

        Debug.Print($"{currentChargeAllocation.TreatmentArea}")
    End Sub

    Private EditingMode As Boolean = False

    Private Sub bbiExit_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiExit.ItemClick
        If bbiSave.Enabled = False Then
            Me.Close()
            Exit Sub
        End If

        'Rollback changes to lists ...
        If MessageBox.Show(Me, $"You have unsaved changes. Are you sure you want to exit without saving changes?", "Save Changes?", vbOKCancel, MessageBoxIcon.Warning) = DialogResult.Cancel Then
            Exit Sub
        End If

        Charge = ChargeBackup
        Me.Close()
    End Sub

    Private Sub bbiSave_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiSave.ItemClick
        Debug.Print("bbiSave_ItemClick")
        If gridView.IsEditing Then
            If gridView.ValidateEditor() = False Then
                Return
            End If
            gridView.CloseEditor()
            gridView.UpdateCurrentRow()
        End If

        Dim unitsRemaining = UpdateUnitsRemaining()
        If unitsRemaining <> maxUnits Then

            If unitsRemaining > 0 Then
                If MessageBox.Show(Me, $"You have {unitsRemaining} unallocated unit(s). Please complete allocation!", "Not all units allocated", MessageBoxButtons.OK, MessageBoxIcon.Error) = DialogResult.OK Then
                    Exit Sub
                End If
            End If
        End If

        PersistModifiers()
        bbiSave.Enabled = False
        SaveChanges = True
        Me.Close()
        Exit Sub
    End Sub

    Private Sub bbiDelete_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiDelete.ItemClick
        Debug.Print("bbiDelete_ItemClick")
        If Charge.ChargeAllocations.Contains(currentChargeAllocation) = False Then
            currentChargeAllocation = gridView.GetFocusedRow
            If Charge.ChargeAllocations.Contains(currentChargeAllocation) = False Then
                Debug.Assert(False, "Houston, we have a problem!")
            End If
        End If
        Charge.ChargeAllocations.Remove(currentChargeAllocation)
        gridControl.RefreshDataSource()
        If Charge.ChargeAllocations.Count = 0 Then
            bbiDelete.Enabled = False
        End If
        gridControl.MainView.RefreshData()
        UpdateAddButtonState()
    End Sub


    Private Sub EditCurrentRow()
        DOSDateEdit.EditValue = currentChargeAllocation.UserDefinedBillingdate
        UnitsSpinEdit.EditValue = currentChargeAllocation.Units
        SearchLookUpEdit1.EditValue = currentChargeAllocation.TreatmentArea

        'EditingMode = True
        'bbiSave.Enabled = True

        DOSDateEdit.Select()
    End Sub


    Private Sub GridControl1_MouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gridControl.MouseDoubleClick
        ECLog.WriteEntry("GridControl1_MouseDoubleClick")
        Dim HitInfo As DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo = Me.CurrentView.CalcHitInfo(New Point(e.X, e.Y))

        If Not HitInfo.InDataRow Then Return 'if they didn't click an actual row, then do nothing
        EditCurrentRow()

    End Sub

    Private Function GetCurrentRow() As CAM2.Charge
        If Me.CurrentView.FocusedRowHandle < 0 Then Return Nothing
        Return Me.CurrentView.GetRow(Me.CurrentView.FocusedRowHandle)
    End Function


    Public Sub UpdateAddButtonState()
        Try
            UpdateUnitsRemaining()
            bbiSave.Enabled = True
        Catch ex As Exception

        End Try
    End Sub

    Private Function UpdateUnitsRemaining() As Integer

        Dim totalSoFar As Int16 = GetTotalUnitsSoFar() 'UnitsSpinEdit.Text
        Dim unitsRemaining = maxUnits - totalSoFar
        If unitsRemaining > 0 Then
            UnitsSpinEdit.EditValue = unitsRemaining
            UnitsSpinEdit.Properties.MaxValue = unitsRemaining
            AddNewRowbtn.Enabled = True
        Else
            UnitsSpinEdit.EditValue = 1
            UnitsSpinEdit.Properties.MaxValue = 1
            AddNewRowbtn.Enabled = False
        End If

        Return unitsRemaining
    End Function

    Private Function GetTotalUnitsSoFar() As Integer
        Dim total As Integer = 0
        For Each allocItem In AllocationsList
            total += allocItem.Units
        Next
        Return total
    End Function

    Private Sub gridView_InvalidValueException(sender As Object, e As DevExpress.XtraEditors.Controls.InvalidValueExceptionEventArgs) Handles gridView.InvalidValueException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.DisplayError
    End Sub

    Private Sub gridView_InvalidRowException(sender As Object, e As InvalidRowExceptionEventArgs) Handles gridView.InvalidRowException
        Debug.Print("gridView_ValidatingEditor")
    End Sub

    Private Sub gridView_ValidatingEditor(sender As Object, e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles gridView.ValidatingEditor
        Debug.Print("gridView_ValidatingEditor")
        Dim gv As GridView = sender
        'colUnits
        'colTreatmentArea
        'colUserDefinedBillingdate

        If gv.FocusedColumn.Name = colUserDefinedBillingdate.Name Then
            Dim beingChangedToDate? As Date = CType(e.Value, Date?)

            If beingChangedToDate < Date.Parse(OriginalBillingdateTextBox.Text) Then
                e.Valid = False
                e.ErrorText = $"Must be greater than or equal to the original billing date of {Date.Parse(OriginalBillingdateTextBox.Text).ToShortDateString}"
            End If
            'Dim orderDate? As Date = CType(gv.GetRowCellValue(gv.FocusedRowHandle, colUserDefinedBillingdate), Date?)
            'If requiredDate < orderDate Then
            '    e.Valid = False
            '    e.ErrorText = "Required Date is earlier than the order date"
            'End If
        ElseIf gv.FocusedColumn.Name = colUnits.Name Then
            Dim unitsRemaining = UpdateUnitsRemaining()
            If unitsRemaining < 0 Then
                unitsRemaining = 0
            End If
            Dim units As Integer = CType(gv.GetRowCellValue(gv.FocusedRowHandle, colUnits), Integer?)
            unitsRemaining = unitsRemaining + units

            If e.Value > unitsRemaining Then
                e.Valid = False
                Dim maxUnits As Int16 = TotalUnitsTextBox.Text
                e.ErrorText = $"{unitsRemaining} units remaining. The total for all allocations must be equal to {maxUnits}"
            End If
        End If
    End Sub

    Private Sub gridView_ValidateRow(sender As Object, e As ValidateRowEventArgs) Handles gridView.ValidateRow
        Debug.Print("gridView_ValidateRow")
    End Sub

    Private Sub gridView_ShowingEditor(sender As Object, e As CancelEventArgs) Handles gridView.ShowingEditor
        Debug.Print("gridView_ShowingEditor")
    End Sub

    Private Sub gridView_CellValueChanging(sender As Object, e As CellValueChangedEventArgs) Handles gridView.CellValueChanging
        Debug.Print("gridView_CellValueChanging")
        bbiSave.Enabled = True
    End Sub

    Private Sub gridView_RowUpdated(sender As Object, e As RowObjectEventArgs) Handles gridView.RowUpdated
        Debug.Print("gridView_RowUpdated")
    End Sub

    Private Sub gridView_HiddenEditor(sender As Object, e As EventArgs) Handles gridView.HiddenEditor
        Debug.Print("gridView_HiddenEditor")
    End Sub

    Private Sub DOSDateEdit_ButtonPressed(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles DOSDateEdit.ButtonPressed
        Dim newDate As Date = DOSDateEdit.EditValue
        If e.Button.Caption = "Plus" Then
            Dim tempdate As Date = DOSDateEdit.EditValue
            newDate = tempdate.AddDays(1).ToShortDateString()
            DOSDateEdit.EditValue = newDate
        ElseIf e.Button.Caption = "Minus" Then
            Dim tempdate As Date = DOSDateEdit.EditValue
            newDate = tempdate.AddDays(-1).ToShortDateString()
            If newDate > DOSDateEdit.Properties.MinValue Then
                DOSDateEdit.EditValue = newDate
            Else
                Beep()
            End If
        End If

    End Sub


    Private Sub AddNewRowbtn_EnabledChanged(sender As Object, e As EventArgs) Handles AddNewRowbtn.EnabledChanged
        ' EditGroupControl.Enabled = AddNewRowbtn.Enabled
    End Sub

    Private Sub DOSDateEdit_Enter(sender As Object, e As EventArgs) Handles DOSDateEdit.Enter
        Dim edit As DateEdit = TryCast(sender, DateEdit)
        BeginInvoke(New MethodInvoker(Sub()
                                          edit.SelectionStart = 2
                                          edit.SelectionLength = 2
                                      End Sub))
    End Sub

#Region "Modifers" '--------------------------------------------------------------------------------------------------------------------------------'
    Private Sub SetupEventHandlersForModifierControls()
        For Each modControl In modifiersControls
            AddHandler modControl.EditValueChanged, AddressOf ModifierEditValueChanged
        Next

        Dim view As ColumnView = gridView
        AddHandler view.RowUpdated, AddressOf RowUpdated
        '     AddHandler view.InitNewRow, AddressOf InitNewRow
        AddHandler view.RowDeleted, AddressOf RowDeleted
    End Sub

    Private Sub ListChanged(sender As Object, e As ListChangedEventArgs)
        Debug.Print("ListChanged")
        UpdateAddButtonState()
    End Sub

    Private Sub AddingNewRow(sender As Object, e As AddingNewEventArgs)
        Debug.Print("AddingNewRow")
        UpdateAddButtonState()
    End Sub

    Private Sub RowUpdated(sender As Object, e As RowObjectEventArgs)
        Debug.Print("RowUpdated")
        UpdateAddButtonState()
    End Sub

    Private Sub RowDeleted(sender As Object, e As RowDeletedEventArgs)
        Debug.Print("RowDeleted")
        UpdateAddButtonState()
    End Sub

    Private Sub InitModLists()
        'added for 2021 release 26, 58, 63
        Dim modifiers As String() = {"", "22", "24", "25", "26", "27", "50", "51", "52", "53", "54", "57", "58", "59", "63", "76", "77", "78", "79", "91", "99", "LT", "RT", "E1", "E2", "E3", "E4", "FA", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "TA", "T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "GC",
            "GE", "XE", "XP", "XS", "XU"}

        For Each cbo In modifiersControls
            cbo.Properties.Items.AddRange(modifiers)
        Next
    End Sub

    Private Sub CreateModControlsList()
        Dim mc = modifiersControls
        mc.Add(ObsMods_Mod1_cbo_01)
        mc.Add(ObsMods_Mod2_cbo_01)
        mc.Add(ObsMods_Mod3_cbo_01)
        mc.Add(ObsMods_Mod4_cbo_01)
        mc.Add(ObsMods_Mod5_cbo_01)
    End Sub

    Private Sub UpdateModifiersUIControls()
        Dim index As Integer = 0
        For Each modItem In Charge.Modifiers
            modifiersControls(index).EditValue = modItem.Modifier
            index += 1
        Next
    End Sub

    Private Sub PersistModifiers()
        Dim modList As New List(Of ChargeModifier)

        For Each mc In modifiersControls
            Dim modText = mc.Text
            If Not String.IsNullOrWhiteSpace(modText) Then
                modList.Add(New ChargeModifier(modText))
            End If
        Next

        Charge.Modifiers = modList
    End Sub

    'Private Sub bbiModifiersUpdate_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiModifiersUpdate.ItemClick
    '    PersistModifiers()
    '    ClearModifiersUIControls()
    '    UpdateModifiersUIControls()
    '    bbiModifiersUpdate.Enabled = False
    'End Sub

    Private Sub bbiModifiersClearAll_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiModifiersClearAll.ItemClick
        DeleteModifiers()
        ClearModifiersUIControls()
    End Sub

    Private Sub DeleteModifiers()
        Dim newList = New List(Of ChargeModifier)
        Charge.Modifiers = newList
    End Sub

    Private Sub ClearModifiersUIControls()
        For Each controlItem In modifiersControls
            controlItem.Text = String.Empty
        Next

        UpdateModifiersUIControls()
        bbiModifiersUpdate.Enabled = False
    End Sub

    Private Sub ModifierEditValueChanged(sender As Object, e As EventArgs)
        bbiModifiersUpdate.Enabled = True
        bbiSave.Enabled = True
    End Sub

    Private Sub AddEditAddBtn_Click(sender As Object, e As EventArgs) Handles AddNewRowbtn.Click
        Dim blah As TreatementAreaNurseUnit = SearchLookUpEdit1.GetSelectedDataRow()
        AllocationsList.Add(New ChargeAllocation() With {
                            .UserDefinedBillingdate = DOSDateEdit.EditValue,
                            .Units = UnitsSpinEdit.EditValue,
                            .TreatmentArea = blah.TA,
                            .TreatmentAreaType = blah.TAType,
                            .Physician = PhysicianCbo.EditValue
                            })

        UpdateAddButtonState()
    End Sub


    Private Sub RepositoryItemGridLookUpEdit1_Modified(sender As Object, e As EventArgs)
        Debug.Print("RepositoryItemGridLookUpEdit1_Modified")
    End Sub

    Private Sub RepositoryItemGridLookUpEdit1_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs)
        Debug.Print("RepositoryItemGridLookUpEdit1_CloseUp")
    End Sub

    Private Sub RepositoryItemGridLookUpEdit1_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles RepositoryItemGridLookUpEdit1.EditValueChanging
        'Dim blah As GridLookUpEdit = sender
        'Dim sdr = blah.EditValue
        'Dim dr = gridView.GetFocusedRow

        'Dim nv = e.NewValue
        'Dim cv = gridView.GetFocusedRowCellValue("TreatmentArea")
    End Sub

    Private Sub RepositoryItemGridLookUpEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles RepositoryItemGridLookUpEdit1.EditValueChanged
        Debug.Print("RepositoryItemGridLookUpEdit1_EditValueChanged")
        Dim blah As GridLookUpEdit = sender
        Dim sdr As TreatementAreaNurseUnit = blah.GetSelectedDataRow
        Dim oldTAType As String = gridView.GetFocusedRowCellValue("TreatmentAreaType")
        Dim dr = gridView.GetFocusedRow
        gridView.SetFocusedRowCellValue("TreatmentAreaType", sdr.TAType)
        'gridView.SetFocusedRowCellValue("Physician", "")
        If oldTAType <> sdr.TAType Then
            gridView.SetFocusedRowCellValue("Physician", "")
        End If
        UpdateAddButtonState()
    End Sub
    Private facilityPhysList As New List(Of String)
    Private obsPhysList As New List(Of String)

    Private Sub PopulatePhysicianLists()
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("EMLevel_Physician_cbo")
        For Each cboItem In cboList.GetListItems()
            facilityPhysList.Add(cboItem)
        Next

        If bShowObs = True Then
            Dim cboList2 As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("ObsTimesFinalBillHoursPhys_cbo")
            For Each cboItem In cboList2.GetListItems()
                obsPhysList.Add(cboItem)
            Next
        End If

        'facilityPhysList.Clear()
        'facilityPhysList.Add("jjc")
        'facilityPhysList.Add("jonathan")

        'obsPhysList.Clear()
        'obsPhysList.Add("obsJJC")
        'obsPhysList.Add("obsJonathan")
    End Sub
    Private Sub SearchLookUpEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles SearchLookUpEdit1.EditValueChanged
        Dim physList As List(Of String)
        Dim sdr As TreatementAreaNurseUnit = SearchLookUpEdit1.GetSelectedDataRow()
        If sdr.TAType = "Obs" Then
            physList = obsPhysList
            Debug.WriteLine("Obs")
        Else
            'PhysicianCbo.DataBind
            physList = facilityPhysList
            Debug.WriteLine("Not Obs")
        End If

        PhysicianCbo.Properties.Items.Clear()
        PhysicianCbo.Properties.Items.AddRange(physList)

        'if physciancbo.editvalue is not null or emtpty then clear editvalud if editvalue is not in list
        If Not String.IsNullOrWhiteSpace(PhysicianCbo.EditValue) Then
            If Not physList.Contains(PhysicianCbo.EditValue) Then
                PhysicianCbo.EditValue = ""
            End If
        End If

    End Sub


#End Region
End Class

﻿Imports System.Text.RegularExpressions
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports System.Linq

Public Class ESPCode2018Migrator

    Public Function MigrateEspocodesTo2018VersionsV1(statusCallback As IProgress(Of String), progressBar As IProgress(Of Integer)) As List(Of DOESPCode2018)
        statusCallback.Report("Getting coffee! Hold your horses...")
        'copy escodes to new table ...
        ' Dim shellControlNameMap = CreateGridAltNameDic()

        Dim sharedFacilityDict As New Dictionary(Of String, DOESPCode2018)
        Dim facilitySpecificList As New List(Of DOESPCode2018)

        Dim oldCodes As New XPCollection(Of DOESPCode)
        Dim sorting As New SortingCollection From {
            New SortProperty("Facility", DB.SortingDirection.Descending),
            New SortProperty("TreatmentArea", DB.SortingDirection.Descending)
        }
        oldCodes.Sorting = sorting
        Dim counter As Integer = 0
        For Each rec In oldCodes
            counter += 1

            If counter Mod 100 = 0 Then
                statusCallback.Report($"{counter}({facilitySpecificList.Count}): {rec.ESPVALUE}")
                Dim percentDone As Integer = oldCodes.Count / counter
                Dim p2 As Integer = (counter * 100) / oldCodes.Count
                progressBar.Report(p2)
            End If

            If GetTargetQuantity(rec.ESPVALUE) > 1 Then Continue For
            If rec.ESPVALUE.ToLower().StartsWith("obsmedinj") Then Continue For
            'If rec.ESPVALUE.ToLower().StartsWith("obsallocateproc") Then Continue For
            If IsObsProcedureOhTwoOrGreater(rec.ESPVALUE) Then Continue For
            If IsOldSetIDXXRec(rec.ESPVALUE) Then Continue For

            Dim newRec = New DOESPCode2018(rec)

            If IsMiscCheckBox(rec.ESPVALUE) OrElse rec.ESPVALUE.ToLower.StartsWith("QualityIndicators_".ToLower()) OrElse rec.ESPVALUE.StartsWith("EMShell_") Then
                newRec.Facility = rec.Facility
                If DoesntExistForFacility(facilitySpecificList, rec) Then
                    facilitySpecificList.Add(newRec)
                End If
                Continue For
            End If

            If IsDifferentFromSharedFacility(facilitySpecificList, newRec) Then
                newRec.Facility = rec.Facility
                If DoesntExistForFacility(facilitySpecificList, rec) Then
                    facilitySpecificList.Add(newRec)
                Else
                    FavorHcpcsAndUpdate(facilitySpecificList, newRec)
                End If
                Continue For
            End If

            If DoesntExistForSharedFacility(facilitySpecificList, rec) Then
                facilitySpecificList.Add(newRec)
            Else
                FavorHcpcsAndUpdate(facilitySpecificList, newRec)
            End If
        Next

        Return facilitySpecificList
    End Function

    Private Function IsOld09Rec(eSPVALUE As String) As Boolean
        Dim regex = New Regex("_09$|_09_")
        If regex.IsMatch(eSPVALUE) Then
            Debug.WriteLine($"IsOld09Rec: {eSPVALUE}")
            Return True
        End If

        Return False
    End Function

    Private Function IsOldSetIDXXRec(eSPVALUE As String) As Boolean
        'If eSPVALUE = "IV_Medication_Init_Hour_VS2_mod59x01SetID03_11" Then
        '    Debug.WriteLine("uhoh")
        'End If
        Dim regex = New Regex("SetID\d{1,2}_")
        If regex.IsMatch(eSPVALUE) Then
            Return True
        End If

        Return False
    End Function

    Private Shared Function DoesntExistForFacility(facilitySpecificList As List(Of DOESPCode2018), rec As DOESPCode) As Boolean
        Return Not facilitySpecificList.Exists(Function(n) n.ESPVALUE = rec.ESPVALUE And (n.Facility.HasValue AndAlso n.Facility = rec.Facility) And n.ActiveFrom = rec.ActiveFrom)
    End Function

    Private Function DoesntExistForSharedFacility(facilitySpecificList As List(Of DOESPCode2018), rec As DOESPCode) As Boolean
        Return Not facilitySpecificList.Exists(Function(n) n.ESPVALUE = rec.ESPVALUE And n.Facility Is Nothing And n.ActiveFrom = rec.ActiveFrom)
    End Function

    Private Shared Function IsDifferentFromSharedFacility(facilitySpecificList As List(Of DOESPCode2018), newRec As DOESPCode2018) As Boolean
        Return facilitySpecificList.Exists(Function(n) n.ESPVALUE = newRec.ESPVALUE And n.Facility Is Nothing And n.ActiveFrom = newRec.ActiveFrom And (n.HCPCS <> newRec.HCPCS OrElse n.Points <> newRec.Points))
    End Function

    Private Shared Sub FavorHcpcsAndUpdate(facilitySpecificList As List(Of DOESPCode2018), newRec As DOESPCode2018)
        If newRec.Facility.HasValue Then
            For Each recItem In facilitySpecificList.Where(Function(n) n.ESPVALUE = newRec.ESPVALUE And ((n.Facility.HasValue AndAlso n.Facility = newRec.Facility)) And n.ActiveFrom = newRec.ActiveFrom)
                'should only be one ...
                recItem = UpdateHcpcsAndPoints(newRec, recItem)
            Next
        Else
            For Each recItem In facilitySpecificList.Where(Function(n) n.ESPVALUE = newRec.ESPVALUE And (n.Facility Is Nothing) And n.ActiveFrom = newRec.ActiveFrom)
                'should only be one ...
                recItem = UpdateHcpcsAndPoints(newRec, recItem)
            Next
        End If
    End Sub

    Private Shared Function UpdateHcpcsAndPoints(newRec As DOESPCode2018, recItem As DOESPCode2018) As DOESPCode2018

        If recItem.HCPCS <> newRec.HCPCS Then
            recItem.MigrationError = True
            If String.IsNullOrWhiteSpace(recItem.HCPCS) Then
                recItem.HCPCS = newRec.HCPCS
                recItem.Points = newRec.Points
                recItem.Special = newRec.Special

                Return recItem
            End If
        End If

        If recItem.Points <> newRec.Points Then
            recItem.MigrationError = True
            If String.IsNullOrWhiteSpace(recItem.Points) Then
                recItem.Points = newRec.Points
                recItem.HCPCS = newRec.HCPCS
                recItem.Special = newRec.Special
                Return recItem
            End If
        End If

        If Not recItem.LongName.Equals(newRec.LongName, StringComparison.CurrentCultureIgnoreCase) Then
            recItem.MigrationError = True
        End If

        Return recItem
    End Function

    Public Function TokenizeEspcodes(espcodeRecords As List(Of DOESPCode2018)) As List(Of DOESPCode2018)
        Dim returnlist As New List(Of DOESPCode2018)
        For Each espRecItem In espcodeRecords
            If espRecItem.ESPVALUE = "SADDDBLK_50" Then
                '        Debugger.Break()
            End If
            ReplaceQuantitiesInHcpcsWithTokens(espRecItem)
            ReplaceQuantitiesInDescriptionWithTokens(espRecItem)

            '2. Replace TA/Nursing Unit
            espRecItem.LongName = ReplaceNursingUnitWithToken(espRecItem.LongName, espRecItem.TreatmentArea)

            returnlist.Add(espRecItem)
        Next

        Return returnlist
    End Function

    Private Sub ReplaceQuantitiesInHcpcsWithTokens(espRec As DOESPCode2018)
        Try

            If String.IsNullOrWhiteSpace(espRec.HCPCS) Then
                Exit Sub
            End If

            Dim targetQuantity As Integer = GetTargetQuantity(espRec.ESPVALUE)
            If targetQuantity < 1 Then Return
            Dim rgx1 As New Regex("(?<units>x\d{1,3})")
            If rgx1.IsMatch(espRec.HCPCS) Then
                espRec.HCPCS = espRec.HCPCS.ReplaceNamedGroup(rgx1, "units", "{{QTY}}")
            End If
        Catch ex As Exception
            Debugger.Break()
        End Try

    End Sub

    Private Sub ReplaceQuantitiesInDescriptionWithTokens(espRec As DOESPCode2018)
        Try
            Dim oldLongName = espRec.LongName
            'we are likely to have up to 3 or more places
            '1. in some format of 'Quantity - 1
            '2. in some form of '- 1 Additional Hour
            '3. as part of the hcpcs which i think will always be 'x01' ish
            Dim targetQuantity As Integer = GetTargetQuantity(espRec.ESPVALUE)
            If targetQuantity < 1 Then
                Exit Sub
            End If

            Dim rgx1 As Regex = Nothing

            rgx1 = New Regex("[x\-:]\s?(?<units>\d{3})")
            If rgx1.IsMatch(espRec.LongName) Then
                espRec.LongName = espRec.LongName.ReplaceNamedGroup(rgx1, "units", "{{QTY-XXX}}")
            End If

            rgx1 = New Regex("[x\-:]\s?(?<units>\d{2})")
            If rgx1.IsMatch(espRec.LongName) Then
                espRec.LongName = espRec.LongName.ReplaceNamedGroup(rgx1, "units", "{{QTY-XX}}")
            End If

            rgx1 = New Regex("[x\-:]\s?(?<units>\d{1})")
            If rgx1.IsMatch(espRec.LongName) Then
                espRec.LongName = espRec.LongName.ReplaceNamedGroup(rgx1, "units", "{{QTY}}")
            End If

            rgx1 = New Regex("(?<!ICD\s?)\((?<units>\d{1,2})\)")
            '     rgx1 = New Regex("\((?<units>\d{1,2})\)")
            If rgx1.IsMatch(espRec.LongName) Then
                espRec.LongName = espRec.LongName.ReplaceNamedGroup(rgx1, "units", "{{QTY}}")
            End If

            'Dim rgx2 As New Regex("\s-\s(\d{1,3})\s+")
            'If rgx2.IsMatch(espRec.LongName) Then
            '    Debugger.Break()
            '    espRec.LongName = rgx2.Replace(espRec.LongName, "{{QTY}}")
            'End If

            'Dim rgx3 As New Regex("\s-\s(\d{1,3})\s+")
            'If rgx3.IsMatch(espRec.LongName) Then
            '    Debugger.Break()
            '    espRec.LongName = rgx3.Replace(espRec.LongName, "{{QTY}}")
            'End If

            If espRec.LongName <> oldLongName Then
                If targetQuantity < 1 Then
                    espRec.MigrationError = True
                End If
            End If
        Catch ex As ArgumentException
            Debugger.Break()
        End Try

    End Sub



    Public Function GetTargetQuantity(espcode As String) As Integer
        Try
            Dim Match = Regex.Match(espcode, "x(\d{1,3})")
            If Match.Success Then
                Return Match.Groups(1).Value
            End If

            Match = Regex.Match(espcode, "ObservationReportableUnits(\d{1,3})")
            If Match.Success Then
                Return Match.Groups(1).Value
            End If

            Return 0
        Catch ex As Exception
            Throw
        End Try

        Return 0
    End Function

    Private Function ReplaceNursingUnitWithToken(ByRef description As String, originalTA As String) As String
        If originalTA Is Nothing Then Return description
        Dim targetTA As String = "{{TREATMENT_AREA}}"

        Dim returnValue = description.Replace(originalTA, targetTA)
        If description <> returnValue Then
            '      Debugger.Break()
        End If
        Return returnValue
    End Function


    Private Function CreateGridAltNameDic() As Dictionary(Of Integer, Dictionary(Of String, String))

        Dim gridControlNameDic As New Dictionary(Of Integer, Dictionary(Of String, String))

        For Each facility In DOFacility.GetAllFacilitesAsList()
            Dim ci = facility.ConfigInstanceVersion
            Dim facilityControlNameDic = New Dictionary(Of String, String)
            gridControlNameDic(facility.Oid) = facilityControlNameDic

            For Each cboListItem In GetEMShellListNameDic()
                Dim gridGroup = cboListItem.Value 'cboListItem.Key
                Dim gridListName = cboListItem.Value
                Dim cboList = ci.GetComboBoxList(gridListName)
                If cboList Is Nothing Then
                    Continue For
                End If
                'EMShell_EMLevel
                '   Dim namePrefix = $"{gridGroup}"
                Dim listMapping = GetEMShellListMappingDic(cboList, cboListItem.Key, cboListItem.Value)
                If listMapping IsNot Nothing Then

                    For Each mapping In listMapping
                        gridControlNameDic(facility.Oid)(mapping.Key) = mapping.Value
                    Next
                End If

                'calc old espcode based off position in list
                'calc new espcode based off of Displayed item name
                'add dic entry
            Next
        Next
        Return gridControlNameDic
    End Function

    Private Function MigrateEspocodesTo2018VersionsV2(statusCallback As IProgress(Of String)) As List(Of DOESPCode2018)
        statusCallback.Report("Getting coffee! Hold your horses...")
        'copy escodes to new table ...
        Dim shellControlNameMap = CreateGridAltNameDic()

        Dim sharedFacilityDict As New Dictionary(Of String, DOESPCode2018)
        Dim facilitySpecificList As New List(Of DOESPCode2018)

        Dim oldCodes As New XPCollection(Of DOESPCode)
        Dim counter As Integer = 0
        For Each rec In oldCodes
            counter += 1
            '99201NewLevel100
            '  If rec.ESPVALUE.ToLower().Contains("emshell_emlevel_lvl4_sc_item2") Then
            If rec.ESPVALUE.Contains("99201NewLevel100") Then
                '       Debugger.Break()
            End If
            If counter Mod 100 = 0 Then
                statusCallback.Report($"{counter}({facilitySpecificList.Count}): {rec.ESPVALUE}")
            End If

            If GetTargetQuantity(rec.ESPVALUE) > 1 Then Continue For
            If rec.ESPVALUE.ToLower().StartsWith("obsmedinj") Then Continue For
            If IsObsProcedureOhTwoOrGreater(rec.ESPVALUE) Then Continue For


            Dim newRec As DOESPCode2018 = Nothing '= New DOESPCode2018()

            If GetTargetQuantity(rec.ESPVALUE) = 1 Then
                sharedFacilityDict(rec.ESPVALUE) = newRec
                Continue For
            End If

            If IsMiscCheckBox(rec.ESPVALUE) Then
                If Not facilitySpecificList.Exists(Function(n) n.ESPVALUE.ToLower = newRec.ESPVALUE.ToLower And n.Facility = rec.Facility And n.ActiveFrom = rec.ActiveFrom) Then
                    newRec = New DOESPCode2018(rec) With {.Facility = rec.Facility}
                    facilitySpecificList.Add(newRec)
                End If
                Continue For
            End If

            If rec.ESPVALUE.ToLower.StartsWith("QualityIndicators_".ToLower()) Then
                If Not facilitySpecificList.Exists(Function(n) n.ESPVALUE.ToLower = rec.ESPVALUE.ToLower And n.Facility = rec.Facility And n.ActiveFrom = rec.ActiveFrom) Then
                    newRec = New DOESPCode2018(rec) With {.Facility = rec.Facility}
                    facilitySpecificList.Add(newRec)
                End If
                Continue For
            End If

            If rec.ESPVALUE.StartsWith("EMShell_") Then
                If Not facilitySpecificList.Exists(Function(n) n.ESPVALUE.ToLower = rec.ESPVALUE.ToLower And n.Facility = rec.Facility And n.ActiveFrom = rec.ActiveFrom) Then
                    newRec = New DOESPCode2018(rec) With {.Facility = rec.Facility}
                    facilitySpecificList.Add(newRec)
                End If
                Continue For
            End If

            If facilitySpecificList.Exists(Function(n) n.ESPVALUE = rec.ESPVALUE And n.Facility Is Nothing And n.ActiveFrom = rec.ActiveFrom And (n.HCPCS <> rec.HCPCS Or n.Points <> rec.Points)) Then
                If Not facilitySpecificList.Exists(Function(n) n.ESPVALUE = rec.ESPVALUE And n.Facility = rec.Facility) Then
                    newRec = New DOESPCode2018(rec) With {.Facility = rec.Facility}
                    facilitySpecificList.Add(newRec)
                End If
                Continue For
            End If

            If Not facilitySpecificList.Exists(Function(n) n.ESPVALUE = rec.ESPVALUE And n.Facility Is Nothing) Then
                newRec = New DOESPCode2018(rec) With {.Facility = rec.Facility}
                facilitySpecificList.Add(newRec)
            End If
        Next

        'Return facilitySpecificList
        Return sharedFacilityDict.Values.ToList
    End Function




    Private Function GetEMShellListNameDic() As Dictionary(Of String, String)
        Dim d = New Dictionary(Of String, String)

        d("Lvl1") = "EMShellLevel1"
        d("Lvl2") = "EMShellLevel2"
        d("Lvl3") = "EMShellLevel3"
        d("Lvl4") = "EMShellLevel4"
        d("Lvl5") = "EMShellLevel5"

        d("CC") = "EMShellLevelCriticalCare"
        d("NoCharge") = "EMShellNoCharge"

        d("ChargableProcs") = "EMShellChargableProcs"
        d("Ancillary1") = "EMShellAncillary1"
        d("Ancillary2") = "EMShellAncillary2"
        d("Ancillary3") = "EMShellAncillary3"

        d("QI") = "EMShellQI"

        Return d
    End Function

    Private Function GetEMShellListMappingDic(ByVal ItemList As DOConfigComboBoxList, ByVal controlNamePrefixOld As String, ByVal controlNamePrefixNew As String) As Dictionary(Of String, String)

        Dim facilityControlNameDic = New Dictionary(Of String, String)
        Dim EMShell_EMLevel As New Dictionary(Of String, Integer) 'used to count multiple occurances of a list item

        Dim iterationCounter = -1
        For Each item As DOConfigComboBoxListsItem In ItemList.ListItems
            iterationCounter += 1

            Dim controlNamePart As String
            Dim displayValue As String

            If String.IsNullOrEmpty(item.ItemDisplayName) Then
                Continue For
            Else
                controlNamePart = item.ItemDisplayName
            End If

            If Not String.IsNullOrEmpty(item.ItemSelectedValue) Then 'allow default control name to be overwritten
                controlNamePart = item.ItemSelectedValue
            End If
            controlNamePart = StripValueButAllowDash(controlNamePart)
            If controlNamePart Is Nothing Then Continue For

            displayValue = item.ItemDisplayName


            Dim occurance As String = String.Empty
            Dim iOccurance As Integer = 1
            If EMShell_EMLevel.ContainsKey(controlNamePart) Then
                iOccurance = EMShell_EMLevel(controlNamePart)
                iOccurance += 1
                EMShell_EMLevel(controlNamePart) = iOccurance
                occurance = "_" & iOccurance.ToString
            Else
                EMShell_EMLevel(controlNamePart) = 1
            End If
            Dim altSchemaName = String.Format("EMShell_{0}_{1}{2}", controlNamePrefixNew, controlNamePart, occurance)
            Dim oldSchemeName = String.Format("EMShell_EMLevel_{0}_sc_Item{1}", controlNamePrefixOld, iterationCounter)
            facilityControlNameDic(oldSchemeName) = altSchemaName
        Next
        Return facilityControlNameDic
    End Function


    Function StripValueButAllowDash(ByVal value As String) As String
        'combo box list - replace special chars
        value = Replace(value, " ", "")
        ' value = Replace(value, "-", "")
        value = Replace(value, "/", "")
        value = Replace(value, ",", "")
        value = Replace(value, "&", "")
        value = Replace(value, "`", "")
        value = Replace(value, "@", "")
        value = Replace(value, "#", "")
        value = Replace(value, "%", "")
        value = Replace(value, "*", "")
        value = Replace(value, "\", "")
        value = Replace(value, "|", "")
        value = Replace(value, ".", "")
        value = Replace(value, ">=", "GTE")
        value = Replace(value, "<=", "LTE")
        value = Replace(value, ">", "GT")
        value = Replace(value, "<", "LT")
        value = Replace(value, "=", "")
        value = Replace(value, "(", "")
        value = Replace(value, ")", "")
        value = Replace(value, "!", "")
        Return value
    End Function

    Public Function IsMiscCheckBox(espcode As String) As Boolean
        Dim pattern = "miscellaneous_UD_\d{2}_chk_True"
        If Regex.IsMatch(espcode, pattern, RegexOptions.IgnoreCase) Then
            If espcode.ToLower().Contains("cbo") Then
                Debugger.Break()
            End If
            Return True
        End If
        Return False
    End Function

    Public Function IsObsProcedureOhTwoOrGreater(espcode As String) As Integer
        Dim returnInt As Integer = 0
        'If espcode.StartsWith("ObsTimesProceduresProc_cbo_") Then
        '    Debug.WriteLine("ohoh")
        'End If
        Dim pattern = "ObsTimesProceduresProc_cbo_(\d{1,2})"
        Dim m = Regex.Match(espcode, pattern, RegexOptions.IgnoreCase)
        If m.Success Then
            returnInt = CInt(m.Groups(1).Value)
        Else
            returnInt = -1
        End If

        If returnInt > 1 Then
            Return True
        Else
            Return False
        End If

    End Function
End Class

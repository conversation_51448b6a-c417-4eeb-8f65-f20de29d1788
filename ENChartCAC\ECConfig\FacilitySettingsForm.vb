﻿Imports DevExpress.Xpo
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Base


Public Class FacilitySettingsForm

    Dim facilityDict As New Dictionary(Of Integer, DOFacility)
    Dim firstFacility As Integer = 0

    Dim _allExistingSettings As XPCollection(Of DOFacilitySettings)
    Dim _baselineSettings As DTOFacilitySettingsList
    Dim _missingSettings As Dictionary(Of Integer, List(Of DOFacilitySettings))

    Private Sub FacilitySettingsForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        ToggleSafeMode()
        PopulateFacilitiesDropdown()

        _allExistingSettings = FacilitySettingsHelper.LoadExistingSettings()
        Me.gridFacilitySettings.DataSource = _allExistingSettings

        _baselineSettings = FacilitySettingsHelper.LoadBaselineSettings()
        ricbSettingCategories.Items.AddRange(_baselineSettings.GetCategories)

        _missingSettings = FacilitySettingsHelper.DetermineMissingBaselineSettings(facilityDict)
        DisplayMissingSettingsCount(_missingSettings)
    End Sub

    Private Sub DisplayMissingSettingsCount(ByVal missingSettings As Dictionary(Of Integer, List(Of DOFacilitySettings)))
        If missingSettings Is Nothing Then
            tsslMissingDefaultSettings.IsLink = False
            tsslMissingDefaultSettings.Text = "All settings have been loaded"
        Else
            Dim total As Integer = 0
            For Each fsl As List(Of DOFacilitySettings) In missingSettings.Values
                total += fsl.Count
            Next

            tsslMissingDefaultSettings.Text = $"{total} default settings are missing, click here for more information"
        End If

    End Sub
    Private Sub PopulateFacilitiesDropdown()
        ' Populate facilityDict with the list of Facility OID/Facility IDs
        Dim facilities As New XPCollection(Of DOFacility)

        For Each fac As DOFacility In facilities
            facilityDict.Add(fac.Oid, fac)
            ricbFacilities.Items.Add(fac.Oid)

            If firstFacility = 0 Then firstFacility = fac.Oid
        Next
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Me.Close()
    End Sub

    Private Sub chkSafeMode_CheckedChanged(sender As Object, e As EventArgs) Handles chkSafeMode.CheckedChanged
        ToggleSafeMode()
    End Sub

    Private Sub ToggleSafeMode()
        Dim safemode As Boolean = chkSafeMode.Checked

        For Each gc As GridColumn In GridView1.Columns
            gc.OptionsColumn.AllowEdit = Not safemode
        Next

        gridFacilitySettings.EmbeddedNavigator.Buttons.Append.Enabled = Not safemode
        gridFacilitySettings.EmbeddedNavigator.Buttons.Remove.Enabled = Not safemode

        colFacilityID.OptionsColumn.AllowEdit = False
        colFacilityName.OptionsColumn.AllowEdit = False
    End Sub

    Private Sub ricbFacilities_EditValueChanged(sender As Object, e As EventArgs) Handles ricbFacilities.EditValueChanged
        GridView1.PostEditor()
    End Sub

    Private Sub GridView1_CellValueChanged(sender As Object, e As CellValueChangedEventArgs) Handles GridView1.CellValueChanged

        If e.Column.Name = "colFacility" Then
            ' Get current row
            Dim facility_oid As Integer = CInt(e.Value)

            ' Lookup the Facility ID for the selected Facility OID
            ' Set the Facility ID of the current row to the looked up value
            GridView1.SetRowCellValue(e.RowHandle, colFacilityID, facilityDict(facility_oid))
        End If

    End Sub

    Private Sub gridFacilitySettings_EmbeddedNavigator_ButtonClick(sender As Object, e As DevExpress.XtraEditors.NavigatorButtonClickEventArgs) Handles gridFacilitySettings.EmbeddedNavigator.ButtonClick
        Try
            Select Case e.Button.ButtonType
                Case DevExpress.XtraEditors.NavigatorButtonType.Remove
                    Dim bm As BindingManagerBase = gridFacilitySettings.BindingContext(gridFacilitySettings.FocusedView.DataSource)
                    Dim xpo As DOFacilitySettings = CType(bm.Current, DOFacilitySettings)

                    If xpo IsNot Nothing Then
                        xpo.Session.Delete(xpo)
                        AuditLogger.FacilitySettings.LogSettingDeleted(xpo.Facility, xpo.RuleSetting)
                        e.Handled = True
                    End If

                Case DevExpress.XtraEditors.NavigatorButtonType.CancelEdit
                    e.Handled = True

                Case DevExpress.XtraEditors.NavigatorButtonType.EndEdit
                    gridFacilitySettings.FocusedView.PostEditor()
                    gridFacilitySettings.FocusedView.UpdateCurrentRow()

                    Dim bm As BindingManagerBase = gridFacilitySettings.BindingContext(gridFacilitySettings.FocusedView.DataSource)
                    Dim xpo As DOFacilitySettings = CType(bm.Current, DOFacilitySettings)

                    If xpo IsNot Nothing Then
                        xpo.Session.Save(xpo)
                        AuditLogger.FacilitySettings.LogSettingSaved(xpo.Facility, xpo.RuleSetting, xpo.RuleSettingValue)
                        e.Handled = True
                    End If

                    gridFacilitySettings.FocusedView.CloseEditor()
            End Select
        Catch ex As Exception

        End Try
    End Sub

    Private Sub GridView1_InitNewRow(sender As Object, e As InitNewRowEventArgs) Handles GridView1.InitNewRow
        GridView1.SetRowCellValue(e.RowHandle, colFacilityOID, firstFacility)
        GridView1.SetRowCellValue(e.RowHandle, colFacilityID, facilityDict(firstFacility))
        GridView1.SetRowCellValue(e.RowHandle, colRuleSetting, "EditTimeoutSeconds")
        GridView1.SetRowCellValue(e.RowHandle, colRuleSettingCategory, "AdminReports")
        GridView1.SetRowCellValue(e.RowHandle, colRuleSettingValue, "300")
    End Sub
    Private Sub tsslMissingDefaultSettings_Click(sender As Object, e As EventArgs) Handles tsslMissingDefaultSettings.Click
        Dim fsmdf As New FacilitySettingsMissingDefaultsForm(_missingSettings)

        If fsmdf.ShowDialog() = DialogResult.OK Then

            _missingSettings = FacilitySettingsHelper.DetermineMissingBaselineSettings(facilityDict)
            DisplayMissingSettingsCount(_missingSettings)

        End If
    End Sub

    Private Sub GridView1_CustomColumnDisplayText(sender As Object, e As CustomColumnDisplayTextEventArgs) Handles GridView1.CustomColumnDisplayText
        Dim view As ColumnView = TryCast(sender, ColumnView)

        If e.Column.Name = "colFacilityName" AndAlso e.ListSourceRowIndex <> DevExpress.XtraGrid.GridControl.InvalidRowHandle Then

            Dim facility_oid As Integer = CInt(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Facility"))
            e.DisplayText = facilityDict(facility_oid).LongName

        End If
    End Sub
End Class

﻿Imports System.IO
Imports CodingReport

Imports System.Drawing

Namespace ColdFeed

    'Note this dll is not reference anywhere... it's loaded dynamically at runtime
    Public Class CustomFilenameImageFileHandler
        Implements IExportChargeSummaryImageFile


        Public Function CreateImageFile(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryImageFile.CreateImageFile
            Dim outPath As String = CFHelper.OptionsDict("ImageOutputPath")
            Dim fileType As String = CFHelper.OptionsDict("ImageFileType")

            Dim visitID As String = chart.Chart.VisitID
            Dim docType As String = CFHelper.OptionsDict("BatchLabel")
            Dim dateOfService As String = GetFormattedDate(chart.Chart.DateOfService)

            Dim filename As String = String.Format($"{visitID}_{docType}_{dateOfService}.{fileType}")


            Dim fullPath = Path.Combine(My.Application.Info.DirectoryPath, outPath)
            fullPath = Path.Combine(fullPath, filename)

            Dim report As New XtraCodingReport

            report.Area51CodingReport(chart.Chart.Oid.ToString, fullPath, fileType)


            If File.Exists(fullPath) Then
                chart.ImageExportSucceeded = True
            Else
                chart.ImageExportSucceeded = False
            End If

            Return chart.ImageExportSucceeded
        End Function

        Private Function GetFormattedDate(ByVal dateToConvert As DateTime) As String
            Try
                Return dateToConvert.ToString("yyyyMMddhhmm")
            Catch ex As Exception
                Return "DATEERR"
            End Try
        End Function

        Public Sub Init() Implements IExportChargeSummaryImageFile.Init

        End Sub

        Private _cFHelper As ColdFeedHelper
        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryImageFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property
    End Class
End Namespace
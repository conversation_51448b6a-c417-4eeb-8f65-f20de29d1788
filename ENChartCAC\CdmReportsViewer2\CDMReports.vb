Imports DevExpress.XtraPrinting
Imports System.IO
Imports DevExpress.Xpo
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.Data
Imports DevExpress.Data.Filtering
Imports System.Linq
Imports EnchartDOLib
Imports DevExpress.XtraGrid.Views.Base
Imports Timeout
Imports MICCustomDataProviders

Public Class CDMReports

#Region "Properties"
    Private Property CurrentUser As DOUser
    Private Property CurrentFacility As DOFacility
    Private Property CurrentCDM As XPCollection(Of DOChargeMaster)
    Private Property PassedFacility As DOFacility = Nothing
    Private Property FLU As New List(Of FacLookUp)
#End Region

    Private Const ADMIN_USER_OID As Integer = 1
    Private _initialized As Boolean = False

    Private Sub Form1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Not _initialized Then
            '    MessageBox.Show("This program must be run from the AIC Client app.")
            Throw New InvalidOperationException("!!!This program must be run from the AIC Client app.")
            Application.Exit()
        End If

        InitFacilitySelect()
        LoadLayoutList()
    End Sub

    Public Sub InitFromUI(CurrentUser As DOUser, currentfacility As DOFacility)
        Me.CurrentUser = CurrentUser
        Me.CurrentFacility = currentfacility
        PassedFacility = currentfacility
        _initialized = True

        Try
            Me.Text += " V." & My.Application.Info.Version.ToString()
        Catch ex As Exception

        End Try


    End Sub


    'Private Sub Init()
    '    '   Utils.ConnectToECDataBase()
    '    If Not _initialized Then
    '        'If My.Application.CommandLineArgs.Count = 2 Then
    '        '    Try
    '        '        PassedFacility = DOFacility.GetFacilityByOid(My.Application.CommandLineArgs(0))
    '        '        CurrentUser = DOUser.GetUserByOID(My.Application.CommandLineArgs(1))
    '        '    Catch ex As Exception
    '        '        MessageBox.Show("Error looking up facility or User")
    '        '        Application.Exit()
    '        '    End Try
    '        'Else
    '        '    CurrentUser = DOUser.GetUserByOID(ADMIN_USER_OID)
    '        'End If

    '    End If
    '    Try
    '        Me.Text += " V." & My.Application.Info.Version.ToString()
    '    Catch ex As Exception

    '    End Try

    '    InitFacilitySelect()
    '    LoadLayoutList()
    '    SetupIdleTimeout()
    'End Sub

    Sub InitFacilitySelect()
        If CurrentUser.IsTheAICAdmin Then
            Dim fcol As New XPCollection(GetType(DOFacility))
            For Each f As DOFacility In fcol
                FLU.Add(New FacLookUp(f))
            Next
        Else
            If CurrentUser.IsInRole("Admin", PassedFacility) Then
                FLU.Add(New FacLookUp(PassedFacility))
            Else
                MessageBox.Show("You don't have permission to edit CDM values for this facility.", "Cannot Edit CDM", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Application.Exit()
            End If
        End If

        beiFacLookUp.Properties.DataSource = FLU
        beiFacLookUp.Properties.ValueMember = "FacilityPointer"

        If FLU.Count > 0 Then
            beiFacLookUp.ItemIndex = 1
        End If
    End Sub

    Private Sub LoadData()
        Dim treatmentArea As String = cboTreatmentArea.EditValue

        If (CurrentFacility Is Nothing) Or (String.IsNullOrEmpty(treatmentArea)) Then Exit Sub

        Dim hideQty As String = IIf(chkHideQuantity.Checked, " AND Quantity = 1", "")

        CurrentCDM = New XPCollection(Of DOChargeMaster) _
            (CriteriaOperator.Parse(String.Format("Facility.Oid = {0} AND TreatmentArea = '{1}' {2}", CurrentFacility.Oid, treatmentArea, hideQty)))

        SyncEMLevel(CurrentFacility, treatmentArea.Trim)

        GridControl1.DataSource = CurrentCDM 'GetTreatmentAreasAsListByFacility(CurrentFacility)
    End Sub

    Private Sub SyncEMLevel(ByVal facility As DOFacility, ByVal treatmentArea As String)

        ' Get all of the current facility/treatment area's EMLevel entries from the FacilitySettings table

        Dim facility_settings As New XPCollection(Of DOFacilitySettings) _
            (CriteriaOperator.Parse(String.Format("Facility = {0} AND TreatmentArea = '{1}' AND RuleSettingCategory = 'emlevel' AND (RuleSetting LIKE 'level%points%' OR RuleSetting LIKE 'emgridlevel%points%')", facility.Oid, treatmentArea)))

        For Each fs As DOFacilitySettings In facility_settings
            Dim matching_hcpcs As List(Of DOChargeMaster) = CurrentCDM.Where(Function(cdm) cdm.HCPCS = fs.HCPCS).ToList

            If matching_hcpcs.Count > 0 Then
                ' Update the existing CDM record
                Dim cdm_to_update As DOChargeMaster = matching_hcpcs(0)
                cdm_to_update.CDM = fs.CDM
                cdm_to_update.LongName = fs.RuleSettingDescription
                cdm_to_update.Save()
            Else
                ' Create a new CDM record
                Dim new_cdm As New DOChargeMaster
                new_cdm.Facility = facility
                new_cdm.TreatmentArea = treatmentArea
                new_cdm.HCPCS = fs.HCPCS
                new_cdm.CDM = fs.CDM
                new_cdm.LongName = fs.RuleSettingDescription
                new_cdm.Quantity = 1
                new_cdm.PhysicianCDM = Nothing
                new_cdm.Save()
            End If
        Next
    End Sub

#Region "Event Handlers"
    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim ps As New PrintingSystem()
        ' Create a link that will print a control.
        Dim link As New PrintableComponentLink(ps)
        ' Specify the control to be printed.
        '        link.Component = CustomersGridControl
        link.Component = GridControl1
        ' Subscribe to the CreateReportHeaderArea event used to generate the report header.
        AddHandler link.CreateReportHeaderArea, AddressOf PrintableComponentLink1_CreateReportHeaderArea
        link.Margins.Left = 50
        link.Margins.Right = 50
        ' Generate the report.
        'link.Landscape = True
        link.CreateDocument()
        ' Show the report.
        link.ShowPreview()
    End Sub

    Private Sub PrintableComponentLink1_CreateReportHeaderArea(sender As Object, e As CreateAreaEventArgs)
        Dim reportHeader As String = "CDM - " & Now.ToString("F")
        Dim font As New Font("Tahoma", 14, FontStyle.Bold)

        ' Use the correct TextAlignment enum from DevExpress
        Dim format As New BrickStringFormat()
        format = format.ChangeAlignment(DevExpress.XtraPrinting.TextAlignment.MiddleCenter)

        ' Create a TextBrick and assign the format
        Dim brick As TextBrick = e.Graph.DrawString(reportHeader, Color.Black, New RectangleF(0, 0, e.Graph.ClientPageSize.Width, 50), BorderSide.None)
        brick.StringFormat = format
        brick.Font = font
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub
    Private Sub btnCollapse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCollapse.Click
        GridView1.CollapseAllGroups()
    End Sub
    Private Sub btnExpand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExpand.Click
        GridView1.ExpandAllGroups()
    End Sub
    Private Sub beiFacLookUp_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles beiFacLookUp.EditValueChanged
        CurrentFacility = DirectCast(beiFacLookUp.EditValue, DOFacility)

        cboTreatmentArea.Properties.Items.Clear()
        cboTreatmentArea.Properties.Items.AddRange(Utils.GetTreatmentAreasAsListByFacility(CurrentFacility))
        cboTreatmentArea.SelectedIndex = -1
        Try
            If cboTreatmentArea.Properties.Items.Count > 0 Then
                cboTreatmentArea.SelectedIndex = 0
            End If
        Catch ex As Exception ' just in case...
            'hide error!
        End Try
    End Sub
    Private Sub cboTreatmentArea_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles cboTreatmentArea.EditValueChanged
        LoadData()
    End Sub
    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Close()
    End Sub
    Private Sub bntSaveLayout_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bntSaveLayout.Click
        cboLayout.EditValue = Nothing

        Dim s As String
        'Me.SetLayoutData(Me.GridView1, s)
        s = GetLayoutData(GridView1)

        Dim sname As String = teLayoutName.EditValue
        If sname = Nothing Then
            Beep()
            Return
        End If

        Dim rec As CdmLayoutRecord = LookUpLayout(sname.Trim)
        If rec Is Nothing Then
            rec = New CdmLayoutRecord
            rec.Name = sname.Trim
        End If


        'Dim sl As New CdmLayoutRecord

        rec.Laybout = s
        rec.Save()

        LoadLayoutList()

        ' GridControl1.MainView.SaveLayoutToXml("CDMReportViewer.xml")
    End Sub
    Private Sub cboLayout_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboLayout.SelectedIndexChanged
        'gridcontrol1.MainView.RestoreLayoutFromStream(
        If cboLayout.EditValue Is Nothing Then Return
        SetLayoutData(GridView1, DirectCast(cboLayout.EditValue, CdmLayoutRecord).Laybout)
        teLayoutName.EditValue = DirectCast(cboLayout.EditValue, CdmLayoutRecord).Name
    End Sub
    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Dim view As GridView = CType(sender, GridView)
        Dim pt As Point = view.GridControl.PointToClient(System.Windows.Forms.Control.MousePosition)
        DoRowDoubleClick(view, pt)
    End Sub
    Private Sub DoRowDoubleClick(ByVal view As GridView, ByVal pt As Point)
        Dim info As ViewInfo.GridHitInfo = view.CalcHitInfo(pt)

        If info.InRow OrElse info.InRowCell Then
            Dim selected_cdm As DOChargeMaster = CType(view.GetRow(info.RowHandle), DOChargeMaster)

            If selected_cdm.HCPCS.StartsWith("992") Or selected_cdm.HCPCS.StartsWith("*") Then
                MessageBox.Show("This row cannot be edited. If you need to make a change to these values, please contact MIC support: **************.  Press 1 For a New Case Or Press 2 For an Existing Case" & vbCrLf & vbCrLf & "Then Press 6 For the Ventus Intelligent Coding product.", "Row Cannot Be Edited", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Else
                Dim ced As New CDMEditDialog(selected_cdm, CurrentUser)
                If ced.ShowDialog() = DialogResult.OK Then
                    LoadData()
                End If
            End If
        End If
    End Sub
    Private Sub chkHideQuantity_CheckedChanged(sender As Object, e As EventArgs) Handles chkHideQuantity.CheckedChanged
        LoadData()
    End Sub
#End Region

#Region "Layout"
    Private Function GetLayoutData(ByVal View As BaseView) As String
        Dim s As New MemoryStream()
        View.SaveLayoutToStream(s)
        s.Position = 0
        Dim r As New StreamReader(s)
        Return r.ReadToEnd()
    End Function
    Private Sub SetLayoutData(ByVal View As BaseView, ByVal Data As String)
        If Data Is Nothing OrElse Data.Length = 0 Then Exit Sub

        Using s As New MemoryStream()
            Using w As New StreamWriter(s)
                w.AutoFlush = True
                w.Write(Data)
                s.Position = 0
                Try
                    View.RestoreLayoutFromStream(s)
                Catch ex As Exception
                    Throw New Exception("Wrong data format", ex)
                End Try
            End Using
        End Using
    End Sub
    Sub LoadLayoutList()
        Try
            cboLayout.Properties.Items.Clear()
            ' Using session As New Session
            Dim xpcol As New XPCollection(Of CdmLayoutRecord) '(session)

            If xpcol Is Nothing OrElse xpcol.Count < 1 Then
                    Return
                End If

                Dim bfirsttime As Boolean = True

                For Each e As CdmLayoutRecord In xpcol
                    cboLayout.Properties.Items.Add(New DevExpress.XtraEditors.Controls.ComboBoxItem(e))
                Next
            '    End Using
        Catch
            'i don't care
        End Try


    End Sub
    ''' <summary>
    ''' Search database for cdmlayoutrecord with the passed name
    ''' </summary>
    ''' <param name="name"></param>
    ''' <returns></returns>
    Function LookUpLayout(ByVal name As String) As CdmLayoutRecord
        Dim xpcol As New XPCollection(Of CdmLayoutRecord)((CriteriaOperator.Parse(String.Format("Name = '{0}'", name))))

        If (xpcol Is Nothing) OrElse (xpcol.Count < 1) Then
            Return Nothing
        End If

        Return xpcol(0)
    End Function
#End Region

#Region "Idle Timeout"
    Private Sub SetupIdleTimeout()
        BasicTimeoutWatcher.Init(Me, AddressOf HandleIdleTimeoutShutdown)
    End Sub

    Private Sub HandleIdleTimeoutShutdown()
        Close()
    End Sub
#End Region

    Private Class FacLookUp
        Public Property FacilityPointer() As DOFacility
        Public Property EID() As String
        Public Property Company() As String
        Public Property CompanyClient() As String
        Public Property Facility() As String
        Public Property FacilityID() As String

        Public Sub New(ByVal pfac As DOFacility)
            FacilityPointer = pfac
            EID = pfac.CompanyClient.Company.EnchartID.LongName
            Company = pfac.CompanyClient.Company.LongName
            CompanyClient = pfac.CompanyClient.LongName
            Facility = pfac.LongName
            FacilityID = pfac.FacilityID
        End Sub
    End Class
End Class
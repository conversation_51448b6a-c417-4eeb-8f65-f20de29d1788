Imports System.IO
Imports System.Net
Imports System.Net.Sockets
Imports System.Text.RegularExpressions

Imports EnchartDOLib


Public Class UtilCreateChartsForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Private Sub btnCreateCharts_Click(sender As Object, e As EventArgs) Handles btnCreateCharts.Click

    'End Sub

#Region "Fields"

    Private bCancelPending As Boolean
    Private visitIDBase As String
    Private visitIDNumber As Integer

#End Region 'Fields

#Region "Constructors"

    Public Sub New()
        MyBase.New()

        'This call is required by the Windows Form Designer.
        InitializeComponent()

        'Add any initialization after the InitializeComponent() call
    End Sub

#End Region 'Constructors

#Region "Methods"

    Public Property SourceChartInfoList As List(Of DOChartInfo)


    Protected Function SendChartToCodingEngine(ByRef pchart As DOChart) As Boolean
        Dim ipe As IPEndPoint = Nothing
        Dim ConfigSetting As DOConfigSetting
        Dim port As Integer = 13843 'We'll use this as default
        Dim bUseMachineName As Boolean = False

        'Note that we are using the current config instance for the currently active facility instead of
        'the config instance for the current chart. The reason for this is to avoid the case where the
        'ipaddress or port has changed from an older configuration and we might be using that config instance
        'instead of the current one with the new correct ip address.
        Dim sclient As New Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp)
        Try
            ConfigSetting = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetConfigSettingGroup("Application")("Translator Listening IPAddress")

            If ConfigSetting("Port").Enabled Then
                port = CInt(ConfigSetting("Port").PropertyValue)
            End If

            'ipe = New IPEndPoint(IPAddress.Parse(ConfigSetting.SettingValue), port)
            'ipe As New IPEndPoint
            '
            If Regex.IsMatch(ConfigSetting.SettingValue, "\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}") Then 'would also match 999.999.999.999 but close enough
                ipe = New IPEndPoint(IPAddress.Parse(ConfigSetting.SettingValue), port)
                'sclient = New Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp)
            Else
                bUseMachineName = True
            End If

        Catch ex As Exception
            MessageBox.Show("A configuration error has occured. Please make sure the 'Translator Listening IPAddress' and 'Port' Settings are defined correctly", "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try

        'Dim sclient As New Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp)

        Try
            If Not bUseMachineName Then
                sclient.Connect(ipe)
            Else
                sclient.Connect(ConfigSetting.SettingValue, port)
            End If
        Catch ex As Exception

            Dim mbresult As DialogResult = MessageBox.Show( _
            "The following error occred while trying to connect the Coding Engine Translator with the below IPAddress and Port: " _
            & Environment.NewLine _
            & Environment.NewLine _
            & ex.Message _
            & Environment.NewLine & Environment.NewLine _
            & String.Format("IPAddress : {0}", ConfigSetting.SettingValue) _
            & Environment.NewLine _
            & String.Format("Port :{0}", port), _
            "Socket Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
        'sclient.SendFile("C:\Documents and Settings\Jonathan\My Documents\Visual Studio 2005\Projects\ENChartCAC\ENChartCAC\bin\Debug\Triageoutput.txt")

        Dim ms As New MemoryStream
        Dim w As New StreamWriter(ms)

        Try
            w.Write(ChrW(&HB)) 'Start block character
            w.WriteLine("FacilityOID|{0}", pchart.ChartInfo.Facility.Oid)
            w.WriteLine("Chart_ID|{0}", pchart.VisitID)
            w.WriteLine("ChartObj_ID|{0}", pchart.Oid)
            w.WriteLine("ChartVersion|{0}", pchart.Version)
            w.WriteLine("ChartStatus|{0}", pchart.ChartStatus)
            w.WriteLine("FacilityID|{0}", ECGlobals.CurrentFacility.FacilityID)
            w.WriteLine("TreatmentArea|{0}", pchart.TreatmentArea)

            'OLD TODO: Change Chart to pChart
            For Each rrecord As DOChartRedisplay In pchart.ChartRedisplay
                w.WriteLine("{0}|{1}", rrecord.ItemName, rrecord.ItemValue)
            Next

            w.WriteLine(ChrW(&H1C)) 'End Block Character
            w.Flush() 'don't think think this is neccesary
            sclient.Send(ms.GetBuffer, ms.GetBuffer.Length, SocketFlags.None)
            'File.WriteAllBytes("output.txt", ms.GetBuffer)

            ms.Close()
            w.Close()
            sclient.Shutdown(SocketShutdown.Both)
            sclient.Close()
        Catch ex As Exception
            MessageBox.Show("The following error occured while sending chart data to the Coding Engine Translator: " _
            & ex.Message, "Send Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try

        Return True
    End Function

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.bCancelPending = True
    End Sub

    Private Sub btnCreateCharts_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCreateCharts.Click
#If CONFIG = "JJCDebug" Then
        Dim numberOfChartsToCreate As Integer = Integer.Parse(Me.teChartsToCreate.EditValue)
        Dim seedChartsList As List(Of DOChartInfo) = SourceChartInfoList

        If seedChartsList.Count < 1 Then
            MessageBox.Show("Select at least one chart, eh ....")
            Return
        End If

        Dim r As New Random()
        Dim createdChartsCount As Integer = 0
        While Not bCancelPending
            Threading.Thread.Sleep(250)
            Dim strVisitID As String = GetNextVisitID()
            Me.lblMsg.Text = "Creating chart " & strVisitID

            Dim i As Integer = r.Next(seedChartsList.Count)
            Dim scChartInfo = seedChartsList(i)
            Dim ci = DOChartInfo.CloneViaReflection(strVisitID, scChartInfo)

            ci.Save()

            If Me.ceSendToRE.Checked Then
                'Me.SendChartToCodingEngine(ci.chart)
                Utils.CodeChart(ci.Chart)
            End If

            createdChartsCount += 1
            If createdChartsCount = numberOfChartsToCreate Then Exit Sub
            Application.DoEvents()
        End While
#End If
    End Sub

    Function GetNextVisitID() As String
        If visitIDNumber = 0 Then
            'do init
            Me.visitIDNumber = Me.teVisitIDStartNumber.EditValue
            Me.visitIDBase = Me.teVisitIDBase.EditValue
        Else
            Me.visitIDNumber += 1
        End If

        Return Me.visitIDBase + Me.visitIDNumber.ToString
    End Function


#End Region 'Methods

End Class
﻿Imports System.Security

Public Class ESSTokenInfoDTO

    Private _AccessTokenExpiration As Long
    Public Property AccessToken As String
    Public Property AccessTokenExpirationDate As Date
    Public Property AccessTokenExpirationSeconds As Long
        Get
            Return _AccessTokenExpiration
        End Get
        Set
            _AccessTokenExpiration = Value
            AccessTokenExpirationDate = Now.AddSeconds(Value)
        End Set
    End Property
    Public Property RefreshToken() As String

    Public Property ErrorDescription As String
    Public Property ErrorCode As String

    Public Function IsError() As Boolean
        Return Not String.IsNullOrEmpty(ErrorDescription)
    End Function

    Public Function IsSuceess() As Boolean
        Return Not IsError()
    End Function

    Public Function UserCancelled() As Boolean
        Return ErrorCode = "user_cancelled"
    End Function
End Class

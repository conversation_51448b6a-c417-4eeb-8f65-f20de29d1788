Imports EnchartDOLib
Imports System.Collections.Specialized
Imports System.Reflection

Namespace ColdFeed
    Public Class ChartObjWrapper

        Public Sub New()

        End Sub

        Public Sub New(ByVal chart As DOChart)
            Me.Chart = chart
        End Sub

        Private _chart As DOChart
        Public Property Chart() As DOChart
            Get
                Return _chart
            End Get
            Set(ByVal value As DOChart)
                _chart = value
            End Set
        End Property


        Private _imageFileName As String
        Public Property ImageFileName() As String
            Get
                Return _imageFileName
            End Get
            Set(ByVal value As String)
                _imageFileName = value
            End Set
        End Property


        Private _ImageExportSucceeded As Boolean
        Public Property ImageExportSucceeded() As Boolean
            Get
                Return _ImageExportSucceeded
            End Get
            Set(ByVal value As Boolean)
                _ImageExportSucceeded = value
            End Set
        End Property


        Public Enum ReasonEnum
            UserRequested

            FacilityChartStatus
            PhysicianChartStatus
            BothChartStatus

            FacilityBillingExportStatus
            PhysicianBillingExportStatus
            BothBillingExportStatus

        End Enum



        Public ReasonForExport As ReasonEnum
        Public PhysicianEnabled As Boolean

        Public bSetFacToExported As Boolean = False
        Public bSetPhysToExported As Boolean = False
        Public bSetObsToExported As Boolean = False

    End Class
End Namespace

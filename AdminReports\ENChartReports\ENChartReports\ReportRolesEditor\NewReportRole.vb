Imports System.Windows.Forms

Public Class NewReportRole

    Public Function GetRoleName As String
            Return txtNewReportRole.text
    End Function

    Private Sub NewReportRole_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        txtNewReportRole.Focus()
    End Sub

    Private Sub OK_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OK_Button.Click
        'parent_form.AddReportRole(txtNewReportRole.Text)
        'txtNewReportRole.Text = ""

        Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        txtNewReportRole.Text = ""

        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub txtNewReportRole_EditValueChanged(sender As Object, e As EventArgs) Handles txtNewReportRole.EditValueChanged
        OK_Button.Enabled = Not String.IsNullOrEmpty(txtNewReportRole.EditValue)
    End Sub
End Class

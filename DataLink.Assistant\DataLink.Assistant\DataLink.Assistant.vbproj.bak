﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2DA2AFCE-C7A0-44A4-9CB1-2EA8E3D7B464}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>DataLink.Assistant</RootNamespace>
    <AssemblyName>DataLink.Assistant</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <RuntimeIdentifier>win</RuntimeIdentifier>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>DataLink.Assistant.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>DataLink.Assistant.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AIC.SharedData, Version=2021.1.1.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\SharedLibs\HIC\AIC.SharedData.dll</HintPath>
    </Reference>
    <Reference Include="DataLink">
      <HintPath>..\..\..\SharedLibs\HIC\DataLink.dll</HintPath>
    </Reference>
    <Reference Include="DataLink.Paragon, Version=2020.1.0.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\SharedLibs\HIC\DataLink.Paragon.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v20.2, Version=20.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="EnchartDOLib">
      <HintPath>..\..\..\SharedLibs\HIC\EnchartDOLib.dll</HintPath>
    </Reference>
    <Reference Include="McKessonIntelligentCoding.Data">
      <HintPath>..\..\..\SharedLibs\HIC\McKessonIntelligentCoding.Data.dll</HintPath>
    </Reference>
    <Reference Include="McKessonIntelligentCoding.ServiceLib">
      <HintPath>..\..\..\SharedLibs\HIC\McKessonIntelligentCoding.ServiceLib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataLinkResponse.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="SingleChartProcessor.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Mapster">
      <Version>7.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions">
      <Version>6.0.4</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Options">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Primitives">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
    </PackageReference>
    <PackageReference Include="System.ComponentModel.Annotations">
      <Version>4.5.0</Version>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.4</Version>
    </PackageReference>
    <PackageReference Include="System.Numerics.Vectors">
      <Version>4.5.0</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>4.5.3</Version>
    </PackageReference>
    <PackageReference Include="System.ValueTuple">
      <Version>4.5.0</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>
﻿Imports DevExpress.Xpo
Imports EnchartDOLib

<DebuggerDisplay("Detail{_Detail}, HCPCS={_HCPCS}")>
Public Class BOCodingReportRecord
    Inherits BusinessObject

    Public Property CDM As String
    Public Property Chart As Integer
    Public Property Detail As String
    Public Property HCPCS As String
    Public Property ICD9 As String
    Public Property InsertDate As Date
    Public Property IsPhysicianTypeCode As Boolean
    Public Property Modifier As String
    Public Property Points As Integer
    Public Property Quantity As Integer
    Public Property ReportDisplayOrder As Integer
    Public Property CodeType As String
    Public Property TreatmentArea As String

    Public Shared Widening Operator CType(ByVal boCRR As BOCodingReportRecord) As DOCodingReportRecord
        If boCRR Is Nothing Then Return Nothing
        Dim doCRR As DOCodingReportRecord
        If boCRR.Oid > NEW_OBJ_OID Then
            doCRR = XpoDefault.Session.GetObjectByKey(Of DOCodingReportRecord)(boCRR.Oid)
        Else
            doCRR = New DOCodingReportRecord() ' With {.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCRR.Chart)}
        End If

        doCRR.CDM = boCRR.CDM
        'doCRR.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCRR.Chart)
        doCRR.Detail = boCRR.Detail
        doCRR.HCPCS = boCRR.HCPCS
        doCRR.ICD9 = boCRR.ICD9
        doCRR.InsertDate = boCRR.InsertDate
        doCRR.IsPhysicianTypeCode = boCRR.IsPhysicianTypeCode
        doCRR.Modifier = boCRR.Modifier
        doCRR.Points = boCRR.Points
        doCRR.Quantity = boCRR.Quantity
        doCRR.ReportDisplayOrder = boCRR.ReportDisplayOrder
        doCRR.CodeType = boCRR.CodeType
        doCRR.TreatmentArea = boCRR.TreatMentArea
        Return doCRR
    End Operator


    Public Shared Narrowing Operator CType(ByVal doCRR As DOCodingReportRecord) As BOCodingReportRecord
        If doCRR Is Nothing Then Return Nothing
        Dim boCRR As New BOCodingReportRecord
        boCRR.Oid = doCRR.Oid
        boCRR.CDM = doCRR.CDM
        boCRR.Chart = If(doCRR.Chart IsNot Nothing, doCRR.Chart.Oid, NEW_OBJ_OID)
        boCRR.Detail = doCRR.Detail
        boCRR.HCPCS = doCRR.HCPCS
        boCRR.ICD9 = doCRR.ICD9
        boCRR.InsertDate = doCRR.InsertDate
        boCRR.IsPhysicianTypeCode = doCRR.IsPhysicianTypeCode
        boCRR.Modifier = doCRR.Modifier
        boCRR.Points = doCRR.Points
        boCRR.Quantity = doCRR.Quantity
        boCRR.ReportDisplayOrder = doCRR.ReportDisplayOrder
        boCRR.CodeType = doCRR.CodeType
        boCRR.TreatMentArea = doCRR.TreatmentArea
        Return boCRR
    End Operator
End Class

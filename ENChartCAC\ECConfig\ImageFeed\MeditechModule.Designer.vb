﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class MeditechModule
    Inherits System.Windows.Forms.UserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.grpMapping = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.grpImageFeedConfig = New DevExpress.XtraEditors.GroupControl()
        Me.dteStartDate = New DevExpress.XtraEditors.DateEdit()
        Me.chkAppendVersionNumber = New DevExpress.XtraEditors.CheckEdit()
        Me.txtTempFilePath = New DevExpress.XtraEditors.TextEdit()
        Me.lblTempFilePath = New DevExpress.XtraEditors.LabelControl()
        Me.cboMeditechDeleteFlag = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.lblMeditechDeleteFlag = New DevExpress.XtraEditors.LabelControl()
        Me.txtMeditechFormID = New DevExpress.XtraEditors.TextEdit()
        Me.lblMeditechFormID = New DevExpress.XtraEditors.LabelControl()
        Me.lblStartDate = New DevExpress.XtraEditors.LabelControl()
        Me.txtDocumentPath = New DevExpress.XtraEditors.TextEdit()
        Me.lblDocumentDestination = New DevExpress.XtraEditors.LabelControl()
        CType(Me.grpMapping, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpMapping.SuspendLayout()
        CType(Me.grpImageFeedConfig, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpImageFeedConfig.SuspendLayout()
        CType(Me.dteStartDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dteStartDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkAppendVersionNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtTempFilePath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboMeditechDeleteFlag.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtMeditechFormID.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDocumentPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'grpMapping
        '
        Me.grpMapping.Controls.Add(Me.LabelControl2)
        Me.grpMapping.Location = New System.Drawing.Point(4, 334)
        Me.grpMapping.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.grpMapping.Name = "grpMapping"
        Me.grpMapping.Size = New System.Drawing.Size(1296, 405)
        Me.grpMapping.TabIndex = 66
        Me.grpMapping.Text = "Meditech Image Feed Mappings"
        '
        'LabelControl2
        '
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl2.Location = New System.Drawing.Point(9, 52)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(264, 19)
        Me.LabelControl2.TabIndex = 6
        Me.LabelControl2.Text = "No mappings for Meditech Data Link"
        '
        'grpImageFeedConfig
        '
        Me.grpImageFeedConfig.Controls.Add(Me.dteStartDate)
        Me.grpImageFeedConfig.Controls.Add(Me.chkAppendVersionNumber)
        Me.grpImageFeedConfig.Controls.Add(Me.txtTempFilePath)
        Me.grpImageFeedConfig.Controls.Add(Me.lblTempFilePath)
        Me.grpImageFeedConfig.Controls.Add(Me.cboMeditechDeleteFlag)
        Me.grpImageFeedConfig.Controls.Add(Me.lblMeditechDeleteFlag)
        Me.grpImageFeedConfig.Controls.Add(Me.txtMeditechFormID)
        Me.grpImageFeedConfig.Controls.Add(Me.lblMeditechFormID)
        Me.grpImageFeedConfig.Controls.Add(Me.lblStartDate)
        Me.grpImageFeedConfig.Controls.Add(Me.txtDocumentPath)
        Me.grpImageFeedConfig.Controls.Add(Me.lblDocumentDestination)
        Me.grpImageFeedConfig.Location = New System.Drawing.Point(4, 5)
        Me.grpImageFeedConfig.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.grpImageFeedConfig.Name = "grpImageFeedConfig"
        Me.grpImageFeedConfig.Size = New System.Drawing.Size(1296, 320)
        Me.grpImageFeedConfig.TabIndex = 65
        Me.grpImageFeedConfig.Text = "Meditech Image Feed Configuration"
        '
        'dteStartDate
        '
        Me.dteStartDate.EditValue = Nothing
        Me.dteStartDate.Location = New System.Drawing.Point(231, 94)
        Me.dteStartDate.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.dteStartDate.Name = "dteStartDate"
        Me.dteStartDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dteStartDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.dteStartDate.Size = New System.Drawing.Size(144, 26)
        Me.dteStartDate.TabIndex = 59
        '
        'chkAppendVersionNumber
        '
        Me.chkAppendVersionNumber.Location = New System.Drawing.Point(9, 265)
        Me.chkAppendVersionNumber.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.chkAppendVersionNumber.Name = "chkAppendVersionNumber"
        Me.chkAppendVersionNumber.Properties.Caption = "Append Version Number"
        Me.chkAppendVersionNumber.Size = New System.Drawing.Size(214, 23)
        Me.chkAppendVersionNumber.TabIndex = 58
        '
        'txtTempFilePath
        '
        Me.txtTempFilePath.Location = New System.Drawing.Point(231, 225)
        Me.txtTempFilePath.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.txtTempFilePath.Name = "txtTempFilePath"
        Me.txtTempFilePath.Size = New System.Drawing.Size(1058, 26)
        Me.txtTempFilePath.TabIndex = 57
        '
        'lblTempFilePath
        '
        Me.lblTempFilePath.Location = New System.Drawing.Point(8, 229)
        Me.lblTempFilePath.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.lblTempFilePath.Name = "lblTempFilePath"
        Me.lblTempFilePath.Size = New System.Drawing.Size(211, 19)
        Me.lblTempFilePath.TabIndex = 56
        Me.lblTempFilePath.Text = "* Temporary Document Path:"
        '
        'cboMeditechDeleteFlag
        '
        Me.cboMeditechDeleteFlag.EditValue = "Y"
        Me.cboMeditechDeleteFlag.Location = New System.Drawing.Point(231, 185)
        Me.cboMeditechDeleteFlag.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.cboMeditechDeleteFlag.Name = "cboMeditechDeleteFlag"
        Me.cboMeditechDeleteFlag.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.cboMeditechDeleteFlag.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboMeditechDeleteFlag.Properties.ImmediatePopup = True
        Me.cboMeditechDeleteFlag.Properties.Items.AddRange(New Object() {"Y", "N"})
        Me.cboMeditechDeleteFlag.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboMeditechDeleteFlag.Size = New System.Drawing.Size(58, 26)
        Me.cboMeditechDeleteFlag.TabIndex = 55
        '
        'lblMeditechDeleteFlag
        '
        Me.lblMeditechDeleteFlag.Location = New System.Drawing.Point(8, 189)
        Me.lblMeditechDeleteFlag.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.lblMeditechDeleteFlag.Name = "lblMeditechDeleteFlag"
        Me.lblMeditechDeleteFlag.Size = New System.Drawing.Size(151, 19)
        Me.lblMeditechDeleteFlag.TabIndex = 54
        Me.lblMeditechDeleteFlag.Text = "Meditech Delete Flag:"
        '
        'txtMeditechFormID
        '
        Me.txtMeditechFormID.Location = New System.Drawing.Point(231, 145)
        Me.txtMeditechFormID.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.txtMeditechFormID.Name = "txtMeditechFormID"
        Me.txtMeditechFormID.Size = New System.Drawing.Size(324, 26)
        Me.txtMeditechFormID.TabIndex = 52
        '
        'lblMeditechFormID
        '
        Me.lblMeditechFormID.Location = New System.Drawing.Point(8, 149)
        Me.lblMeditechFormID.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.lblMeditechFormID.Name = "lblMeditechFormID"
        Me.lblMeditechFormID.Size = New System.Drawing.Size(132, 19)
        Me.lblMeditechFormID.TabIndex = 51
        Me.lblMeditechFormID.Text = "Meditech Form ID:"
        '
        'lblStartDate
        '
        Me.lblStartDate.Location = New System.Drawing.Point(9, 98)
        Me.lblStartDate.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.lblStartDate.Name = "lblStartDate"
        Me.lblStartDate.Size = New System.Drawing.Size(76, 19)
        Me.lblStartDate.TabIndex = 49
        Me.lblStartDate.Text = "Start Date:"
        '
        'txtDocumentPath
        '
        Me.txtDocumentPath.Location = New System.Drawing.Point(231, 48)
        Me.txtDocumentPath.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.txtDocumentPath.Name = "txtDocumentPath"
        Me.txtDocumentPath.Size = New System.Drawing.Size(1058, 26)
        Me.txtDocumentPath.TabIndex = 46
        '
        'lblDocumentDestination
        '
        Me.lblDocumentDestination.Location = New System.Drawing.Point(8, 52)
        Me.lblDocumentDestination.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.lblDocumentDestination.Name = "lblDocumentDestination"
        Me.lblDocumentDestination.Size = New System.Drawing.Size(212, 19)
        Me.lblDocumentDestination.TabIndex = 45
        Me.lblDocumentDestination.Text = "* Document Destination Path:"
        '
        'MeditechModule
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 20.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Controls.Add(Me.grpMapping)
        Me.Controls.Add(Me.grpImageFeedConfig)
        Me.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.Name = "MeditechModule"
        Me.Size = New System.Drawing.Size(1305, 743)
        CType(Me.grpMapping, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpMapping.ResumeLayout(False)
        CType(Me.grpImageFeedConfig, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpImageFeedConfig.ResumeLayout(False)
        Me.grpImageFeedConfig.PerformLayout()
        CType(Me.dteStartDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dteStartDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkAppendVersionNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtTempFilePath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboMeditechDeleteFlag.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtMeditechFormID.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDocumentPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents grpMapping As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents grpImageFeedConfig As DevExpress.XtraEditors.GroupControl
    Friend WithEvents lblMeditechDeleteFlag As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtMeditechFormID As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblMeditechFormID As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblStartDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtDocumentPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblDocumentDestination As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboMeditechDeleteFlag As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txtTempFilePath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblTempFilePath As DevExpress.XtraEditors.LabelControl
    Friend WithEvents chkAppendVersionNumber As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents dteStartDate As DevExpress.XtraEditors.DateEdit
End Class

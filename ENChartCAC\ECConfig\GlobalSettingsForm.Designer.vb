﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class GlobalSettingsForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        components = New ComponentModel.Container()
        XpCollection1 = New DevExpress.Xpo.XPCollection(components)
        GridControl1 = New DevExpress.XtraGrid.GridControl()
        GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        colOid = New DevExpress.XtraGrid.Columns.GridColumn()
        colSettingName = New DevExpress.XtraGrid.Columns.GridColumn()
        colSettingValue = New DevExpress.XtraGrid.Columns.GridColumn()
        SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        ceUseEnhancedPassword = New DevExpress.XtraEditors.CheckEdit()
        teDefaultDomain = New DevExpress.XtraEditors.TextEdit()
        LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        ceUseActiveDirectory = New DevExpress.XtraEditors.CheckEdit()
        GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        ceCollectQueryFingerPrints = New DevExpress.XtraEditors.CheckEdit()
        ceUse2018ChargeAllocationLogic = New DevExpress.XtraEditors.CheckEdit()
        ceUse2018CodingReport = New DevExpress.XtraEditors.CheckEdit()
        ceUse2018ESPCodeLogic = New DevExpress.XtraEditors.CheckEdit()
        seAutoLogOffTimeOutMins = New DevExpress.XtraEditors.SpinEdit()
        LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        teMMMsg = New DevExpress.XtraEditors.TextEdit()
        LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        ceEnableMaintenanceMode = New DevExpress.XtraEditors.CheckEdit()
        CType(XpCollection1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridView1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GroupControl1, ComponentModel.ISupportInitialize).BeginInit()
        GroupControl1.SuspendLayout()
        CType(ceUseEnhancedPassword.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(teDefaultDomain.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceUseActiveDirectory.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(GroupControl2, ComponentModel.ISupportInitialize).BeginInit()
        GroupControl2.SuspendLayout()
        CType(ceCollectQueryFingerPrints.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceUse2018ChargeAllocationLogic.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceUse2018CodingReport.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceUse2018ESPCodeLogic.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(seAutoLogOffTimeOutMins.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(GroupControl3, ComponentModel.ISupportInitialize).BeginInit()
        GroupControl3.SuspendLayout()
        CType(CheckEdit1.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(teMMMsg.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceEnableMaintenanceMode.Properties, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' XpCollection1
        ' 
        XpCollection1.ObjectType = GetType(DOGlobalSetting)
        ' 
        ' GridControl1
        ' 
        GridControl1.DataSource = XpCollection1
        GridControl1.Location = New Point(12, 12)
        GridControl1.MainView = GridView1
        GridControl1.Name = "GridControl1"
        GridControl1.Size = New Size(595, 442)
        GridControl1.TabIndex = 0
        GridControl1.UseEmbeddedNavigator = True
        GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {GridView1})
        ' 
        ' GridView1
        ' 
        GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colOid, colSettingName, colSettingValue})
        GridView1.GridControl = GridControl1
        GridView1.Name = "GridView1"
        GridView1.NewItemRowText = "Add new row"
        GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(colSettingName, DevExpress.Data.ColumnSortOrder.Ascending)})
        ' 
        ' colOid
        ' 
        colOid.Caption = "Oid"
        colOid.FieldName = "Oid"
        colOid.Name = "colOid"
        colOid.Width = 78
        ' 
        ' colSettingName
        ' 
        colSettingName.Caption = "SettingName"
        colSettingName.FieldName = "SettingName"
        colSettingName.Name = "colSettingName"
        colSettingName.Visible = True
        colSettingName.VisibleIndex = 0
        colSettingName.Width = 290
        ' 
        ' colSettingValue
        ' 
        colSettingValue.Caption = "SettingValue"
        colSettingValue.FieldName = "SettingValue"
        colSettingValue.Name = "colSettingValue"
        colSettingValue.Visible = True
        colSettingValue.VisibleIndex = 1
        colSettingValue.Width = 297
        ' 
        ' SimpleButton1
        ' 
        SimpleButton1.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        SimpleButton1.Location = New Point(266, 588)
        SimpleButton1.Name = "SimpleButton1"
        SimpleButton1.Size = New Size(384, 23)
        SimpleButton1.TabIndex = 1
        SimpleButton1.Text = "OK"
        ' 
        ' GroupControl1
        ' 
        GroupControl1.Controls.Add(ceUseEnhancedPassword)
        GroupControl1.Controls.Add(teDefaultDomain)
        GroupControl1.Controls.Add(LabelControl1)
        GroupControl1.Controls.Add(ceUseActiveDirectory)
        GroupControl1.Location = New Point(639, 12)
        GroupControl1.Name = "GroupControl1"
        GroupControl1.Size = New Size(265, 147)
        GroupControl1.TabIndex = 2
        GroupControl1.Text = "Log In / Authentication"
        ' 
        ' ceUseEnhancedPassword
        ' 
        ceUseEnhancedPassword.Enabled = False
        ceUseEnhancedPassword.Location = New Point(16, 116)
        ceUseEnhancedPassword.Name = "ceUseEnhancedPassword"
        ceUseEnhancedPassword.Properties.Caption = "Use Enhanced Passwords"
        ceUseEnhancedPassword.Size = New Size(233, 19)
        ceUseEnhancedPassword.TabIndex = 3
        ' 
        ' teDefaultDomain
        ' 
        teDefaultDomain.Location = New Point(18, 76)
        teDefaultDomain.Name = "teDefaultDomain"
        teDefaultDomain.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False
        teDefaultDomain.Properties.MaxLength = 64
        teDefaultDomain.Size = New Size(231, 20)
        teDefaultDomain.TabIndex = 2
        ' 
        ' LabelControl1
        ' 
        LabelControl1.Location = New Point(18, 57)
        LabelControl1.Name = "LabelControl1"
        LabelControl1.Size = New Size(174, 14)
        LabelControl1.TabIndex = 1
        LabelControl1.Text = "Default Domain Name For LogIn"
        ' 
        ' ceUseActiveDirectory
        ' 
        ceUseActiveDirectory.Location = New Point(16, 31)
        ceUseActiveDirectory.Name = "ceUseActiveDirectory"
        ceUseActiveDirectory.Properties.Caption = "Use Active Directory For Authentication"
        ceUseActiveDirectory.Size = New Size(233, 19)
        ceUseActiveDirectory.TabIndex = 0
        ' 
        ' GroupControl2
        ' 
        GroupControl2.Controls.Add(ceCollectQueryFingerPrints)
        GroupControl2.Controls.Add(ceUse2018ChargeAllocationLogic)
        GroupControl2.Controls.Add(ceUse2018CodingReport)
        GroupControl2.Controls.Add(ceUse2018ESPCodeLogic)
        GroupControl2.Controls.Add(seAutoLogOffTimeOutMins)
        GroupControl2.Controls.Add(LabelControl4)
        GroupControl2.Controls.Add(LabelControl3)
        GroupControl2.Location = New Point(639, 165)
        GroupControl2.Name = "GroupControl2"
        GroupControl2.Size = New Size(268, 289)
        GroupControl2.TabIndex = 4
        GroupControl2.Text = "Misc."
        ' 
        ' ceCollectQueryFingerPrints
        ' 
        ceCollectQueryFingerPrints.Location = New Point(4, 136)
        ceCollectQueryFingerPrints.Name = "ceCollectQueryFingerPrints"
        ceCollectQueryFingerPrints.Properties.Caption = "Collect Query FingerPrints"
        ceCollectQueryFingerPrints.Size = New Size(244, 19)
        ceCollectQueryFingerPrints.TabIndex = 14
        ' 
        ' ceUse2018ChargeAllocationLogic
        ' 
        ceUse2018ChargeAllocationLogic.Location = New Point(4, 111)
        ceUse2018ChargeAllocationLogic.Name = "ceUse2018ChargeAllocationLogic"
        ceUse2018ChargeAllocationLogic.Properties.Caption = "Use 2018 Obs. Charge Allocation Logic"
        ceUse2018ChargeAllocationLogic.Size = New Size(244, 19)
        ceUse2018ChargeAllocationLogic.TabIndex = 13
        ' 
        ' ceUse2018CodingReport
        ' 
        ceUse2018CodingReport.Location = New Point(5, 89)
        ceUse2018CodingReport.Name = "ceUse2018CodingReport"
        ceUse2018CodingReport.Properties.Caption = "Use 2018 Coding Report Logic"
        ceUse2018CodingReport.Size = New Size(244, 19)
        ceUse2018CodingReport.TabIndex = 12
        ' 
        ' ceUse2018ESPCodeLogic
        ' 
        ceUse2018ESPCodeLogic.Location = New Point(5, 66)
        ceUse2018ESPCodeLogic.Name = "ceUse2018ESPCodeLogic"
        ceUse2018ESPCodeLogic.Properties.Caption = "Use 2018 ESPCode Logic"
        ceUse2018ESPCodeLogic.Size = New Size(244, 19)
        ceUse2018ESPCodeLogic.TabIndex = 11
        ' 
        ' seAutoLogOffTimeOutMins
        ' 
        seAutoLogOffTimeOutMins.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        seAutoLogOffTimeOutMins.Location = New Point(148, 30)
        seAutoLogOffTimeOutMins.Name = "seAutoLogOffTimeOutMins"
        seAutoLogOffTimeOutMins.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        seAutoLogOffTimeOutMins.Properties.IsFloatValue = False
        seAutoLogOffTimeOutMins.Properties.Mask.EditMask = "N00"
        seAutoLogOffTimeOutMins.Properties.MaxValue = New Decimal(New Integer() {1440, 0, 0, 0})
        seAutoLogOffTimeOutMins.Size = New Size(67, 20)
        seAutoLogOffTimeOutMins.TabIndex = 9
        seAutoLogOffTimeOutMins.ToolTip = "Set to 0 to disable"
        ' 
        ' LabelControl4
        ' 
        LabelControl4.Location = New Point(218, 33)
        LabelControl4.Name = "LabelControl4"
        LabelControl4.Size = New Size(23, 14)
        LabelControl4.TabIndex = 3
        LabelControl4.Text = "Mins"
        ' 
        ' LabelControl3
        ' 
        LabelControl3.Location = New Point(5, 34)
        LabelControl3.Name = "LabelControl3"
        LabelControl3.Size = New Size(124, 14)
        LabelControl3.TabIndex = 1
        LabelControl3.Text = "Auto Log off Timeout "
        ' 
        ' GroupControl3
        ' 
        GroupControl3.Controls.Add(CheckEdit1)
        GroupControl3.Controls.Add(teMMMsg)
        GroupControl3.Controls.Add(LabelControl5)
        GroupControl3.Controls.Add(ceEnableMaintenanceMode)
        GroupControl3.Location = New Point(12, 460)
        GroupControl3.Name = "GroupControl3"
        GroupControl3.Size = New Size(892, 112)
        GroupControl3.TabIndex = 5
        GroupControl3.Text = "Maintenance Mode"
        ' 
        ' CheckEdit1
        ' 
        CheckEdit1.Location = New Point(16, 116)
        CheckEdit1.Name = "CheckEdit1"
        CheckEdit1.Properties.Caption = "Use Enhanced Passwords"
        CheckEdit1.Size = New Size(233, 19)
        CheckEdit1.TabIndex = 3
        ' 
        ' teMMMsg
        ' 
        teMMMsg.Location = New Point(16, 76)
        teMMMsg.Name = "teMMMsg"
        teMMMsg.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False
        teMMMsg.Properties.EditValueChangedDelay = 1000
        teMMMsg.Properties.MaxLength = 120
        teMMMsg.Size = New Size(860, 20)
        teMMMsg.TabIndex = 2
        ' 
        ' LabelControl5
        ' 
        LabelControl5.Location = New Point(18, 57)
        LabelControl5.Name = "LabelControl5"
        LabelControl5.Size = New Size(322, 14)
        LabelControl5.TabIndex = 1
        LabelControl5.Text = "Message - (Should include some estimation of down time)."
        ' 
        ' ceEnableMaintenanceMode
        ' 
        ceEnableMaintenanceMode.Location = New Point(16, 31)
        ceEnableMaintenanceMode.Name = "ceEnableMaintenanceMode"
        ceEnableMaintenanceMode.Properties.Caption = "Maintenance Mode"
        ceEnableMaintenanceMode.Size = New Size(233, 19)
        ceEnableMaintenanceMode.TabIndex = 0
        ' 
        ' GlobalSettingsForm
        ' 
        AutoScaleMode = AutoScaleMode.None
        ClientSize = New Size(916, 623)
        Controls.Add(GroupControl3)
        Controls.Add(GroupControl2)
        Controls.Add(GroupControl1)
        Controls.Add(SimpleButton1)
        Controls.Add(GridControl1)
        FormBorderStyle = FormBorderStyle.FixedDialog
        MaximizeBox = False
        MinimizeBox = False
        Name = "GlobalSettingsForm"
        ShowIcon = False
        SizeGripStyle = SizeGripStyle.Hide
        StartPosition = FormStartPosition.CenterParent
        Text = "Global Settings"
        TopMost = True
        CType(XpCollection1, ComponentModel.ISupportInitialize).EndInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).EndInit()
        CType(GridView1, ComponentModel.ISupportInitialize).EndInit()
        CType(GroupControl1, ComponentModel.ISupportInitialize).EndInit()
        GroupControl1.ResumeLayout(False)
        GroupControl1.PerformLayout()
        CType(ceUseEnhancedPassword.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(teDefaultDomain.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceUseActiveDirectory.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(GroupControl2, ComponentModel.ISupportInitialize).EndInit()
        GroupControl2.ResumeLayout(False)
        GroupControl2.PerformLayout()
        CType(ceCollectQueryFingerPrints.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceUse2018ChargeAllocationLogic.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceUse2018CodingReport.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceUse2018ESPCodeLogic.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(seAutoLogOffTimeOutMins.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(GroupControl3, ComponentModel.ISupportInitialize).EndInit()
        GroupControl3.ResumeLayout(False)
        GroupControl3.PerformLayout()
        CType(CheckEdit1.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(teMMMsg.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceEnableMaintenanceMode.Properties, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
    End Sub
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSettingName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSettingValue As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents teDefaultDomain As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceUseActiveDirectory As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUseEnhancedPassword As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents seAutoLogOffTimeOutMins As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents teMMMsg As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceEnableMaintenanceMode As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ESPCodeLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018CodingReport As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ChargeAllocationLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceCollectQueryFingerPrints As DevExpress.XtraEditors.CheckEdit
End Class

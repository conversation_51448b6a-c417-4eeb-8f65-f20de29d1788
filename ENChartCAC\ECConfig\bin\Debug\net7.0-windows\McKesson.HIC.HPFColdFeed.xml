﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
McKesson.HIC.HPFColdFeed
</name>
</assembly>
<members>
<member name="T:McKesson.HIC.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:McKesson.HIC.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:McKesson.HIC.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="T:McKesson.HIC.ColdFeed.DefaulGetChartsToExport">
 <summary>
 Return List of DOChart.Oids
 </summary>
 <remarks>
 By using ado.net to get our list of DOChart Oids that need to ColdFeed export, we are primarily making
 it easier to accommodate all possible queries that come up, without having to be devexpress guru...
 
 </remarks>
</member>
<member name="M:McKesson.HIC.ColdFeed.DefaulGetChartsToExport.GetdbConnection">
 <summary>
 Extract a database connection (in the current case, an ASADbConnection object) from the DevExpress DataLayer
 </summary>
 <returns>IDbConection</returns>
 <remarks>This is so we can use ado.net without needing another connection string</remarks>
</member>
<member name="M:McKesson.HIC.ColdFeed.DefaulGetChartsToExport.ShouldExport(McKesson.HIC.ColdFeed.ChartObjWrapper,System.Boolean)">
 <summary>
 Determine if this Visit should be exported
 </summary>
 <param name="chartWrapper"></param>
 <param name="physicianModuleEnabled"></param>
 <returns></returns>
 <remarks>
 
 Regarding useRequrested exports, we will only do an export if at least 1 chartstatus is "Complete"
 
 Note - The CFEExportXXXXXChartVersion fields may or maynot be the actual version that was billing exported.
 These fields are used to manage when we are in a new condition that requires an export, and of course to document
 the version of the chart when said condition is met.
  </remarks>
</member>
</members>
</doc>

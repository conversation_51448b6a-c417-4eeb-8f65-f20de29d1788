﻿Public Class BOCodedOutput
    Public Property ChartInfo As BOChartInfo
    Public Property Facility As Integer
    Public Property FacilityID As String
    Public Property ChartSummary As BOChartSummary
    Public Property ChargeExports As IEnumerable(Of BOChargeExport)
    Public Property CodingReportRecords As IEnumerable(Of BOCodingReportRecord)
    Public Property ChartHCPCSInfos As IEnumerable(Of BOChartHCPCSInfo)
    Public Property [Error] As Boolean
    Public Property ErrorMessage As String

    'Public Property ChargeAllocations As CAM2.UserAllocationsCollection
End Class

﻿Imports DevExpress.XtraEditors.Controls
Imports ENChartCAC
Imports EnchartDOLib

Public Class GlobalSettingsForm

    Private Sub XpCollection1_ListChanged(sender As Object, e As System.ComponentModel.ListChangedEventArgs) Handles XpCollection1.ListChanged

    End Sub

    Private _isLoading As Boolean = False

    Private Sub GlobalSettingsForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        _isLoading = True

        ceUseActiveDirectory.Checked = DOGlobalSetting.GetSetting("UseActiveDirectoryLogIn").SettingValueAsBool
        teDefaultDomain.EditValue = DOGlobalSetting.GetSetting("DefaultDomainNameForLogIn").SettingValue

        ceUseEnhancedPassword.Checked = DOGlobalSetting.GetSetting("UseEnhancedPasswords").SettingValueAsBool

        Dim daysPassValidSetting As DOGlobalSetting = DOGlobalSetting.GetSetting("DaysPasswordValid")
        Dim hasEnhancedPasswordBeenPreConfigured As Boolean = daysPassValidSetting.Oid > 0
        ceUseEnhancedPassword.Enabled = hasEnhancedPasswordBeenPreConfigured

        If ceUseActiveDirectory.Checked Then
            ceUseEnhancedPassword.Checked = False
        End If

        If String.IsNullOrEmpty(DOGlobalSetting.GetSetting("AutoLogOffTimeOut").SettingValue) Then
            seAutoLogOffTimeOutMins.EditValue = 0
        Else
            seAutoLogOffTimeOutMins.EditValue = DOGlobalSetting.GetSetting("AutoLogOffTimeOut").SettingValue
        End If

        ceEnableMaintenanceMode.Checked = DOGlobalSetting.GetSetting("MaintenanceModeEnable").SettingValueAsBool
        ceUse2018ESPCodeLogic.Checked = ECGlobals.Use2018EspcodeLogic
        ceUse2018CodingReport.Checked = ECGlobals.Use2018CodingReport
        ceUse2018ChargeAllocationLogic.Checked = DOGlobalSetting.GetSetting("Use2018ChargeAllocationLogic").SettingValueAsBool

        teMMMsg.EditValue = DOGlobalSetting.GetSetting("MaintenanceModeMessage").SettingValue
        _isLoading = False
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Me.Close()
    End Sub


    Private Sub teDefaultDomain_EditValueChanged(sender As Object, e As EventArgs) Handles teDefaultDomain.EditValueChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("DefaultDomainNameForLogIn")
        setting.SettingValue = teDefaultDomain.EditValue
        setting.Save()
    End Sub

    Private Sub teUseEnhancedPassword_CheckedChanged(sender As Object, e As EventArgs) Handles ceUseEnhancedPassword.CheckedChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("UseEnhancedPasswords")
        setting.SettingValue = ceUseEnhancedPassword.Checked.ToString
        setting.Save()

        If Not _isLoading Then
            If ceUseEnhancedPassword.Checked Then
                ceUseActiveDirectory.Checked = False
            End If
        End If

    End Sub

    Private Sub seAutoLogOffTimeOutMins_EditValueChanged(sender As Object, e As EventArgs) Handles seAutoLogOffTimeOutMins.EditValueChanged
        'DOGlobalSetting.GetSetting("AutoLogOffTimeOut").SettingValue = seAutoLogOffTimeOutMins.EditValue

        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("AutoLogOffTimeOut")
        setting.SettingValue = seAutoLogOffTimeOutMins.EditValue
        setting.Save()
    End Sub

    Private Sub ceUseActiveDirectory_EditValueChanging(sender As Object, e As ChangingEventArgs) Handles ceUseActiveDirectory.EditValueChanging
        If _isLoading Then Return

        Dim User As DOUser = DOUser.GetUserByID("Admin")
        If User IsNot Nothing Then
            User.HashedPassword = Nothing
        End If

        If e.NewValue = True Then
            PromptForNewAdminPassword(User)
            If Not User.HasHashedPassword Then
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub PromptForNewAdminPassword(ByVal User As DOUser)
        Dim _editUserForm As Form

        Dim f As New ENChartCAC.UserConfigAddUserActiveDirectory
        f.Owner = Me
        f.User = User
        f.Text = "Please Define a New Admin Password"
        _editUserForm = f
        f.TopMost = True
        f.ShowDialog()

    End Sub
    Private Sub UseActiveDirectory_CheckedChanged(sender As Object, e As EventArgs) Handles ceUseActiveDirectory.CheckedChanged

        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("UseActiveDirectoryLogIn")
        setting.SettingValue = ceUseActiveDirectory.Checked.ToString
        setting.Save()

        If ceUseActiveDirectory.Checked Then
            ceUseEnhancedPassword.Checked = False
        End If
    End Sub

    Private Sub ceEnableMaintenanceMode_CheckedChanged(sender As Object, e As EventArgs) Handles ceEnableMaintenanceMode.CheckedChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("MaintenanceModeEnable")
        setting.SettingValue = ceEnableMaintenanceMode.Checked.ToString
        setting.Save()
    End Sub

    Private Sub teMMMsg_EditValueChanged(sender As Object, e As EventArgs) Handles teMMMsg.EditValueChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("MaintenanceModeMessage")
        setting.SettingValue = teMMMsg.EditValue.ToString
        setting.Save()
    End Sub

    Private Sub ceUse2018ESPCodeLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ESPCodeLogic.CheckedChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018EspcodeLogic")
        setting.SettingValue = ceUse2018ESPCodeLogic.Checked.ToString
        setting.Save()
    End Sub

    Private Sub ceUse2018CodingReport_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018CodingReport.CheckedChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018CodingReport")
        setting.SettingValue = ceUse2018CodingReport.Checked.ToString
        setting.Save()
    End Sub

    Private Sub ceUse2018ChargeAllocationLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ChargeAllocationLogic.CheckedChanged
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018ChargeAllocationLogic")
        setting.SettingValue = ceUse2018ChargeAllocationLogic.Checked.ToString
        setting.Save()
    End Sub
End Class
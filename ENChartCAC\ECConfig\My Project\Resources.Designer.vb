﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("ECConfig.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to IF NOT EXISTS(SELECT 1 FROM SYSOBJECTS WHERE NAME = &apos;CDMTableName&apos; AND TYPE = &apos;U&apos;)
        '''BEGIN
        '''
        '''CREATE TABLE &quot;DBA&quot;.&quot;CDMTableName&quot;
        '''(
        '''	&quot;HCPCS&quot; 			varchar(25) NOT NULL,
        '''	&quot;CDM&quot;   			varchar(25) NULL,
        '''	&quot;LongName&quot;      		varchar(256) NULL,
        '''	&quot;Quantity&quot;      		integer NULL DEFAULT 1,
        '''	&quot;PhysicianCDM&quot;  		varchar(25) NULL
        ''')
        '''
        '''END
        '''.
        '''</summary>
        Friend ReadOnly Property CreateCDMTableQuery() As String
            Get
                Return ResourceManager.GetString("CreateCDMTableQuery", resourceCulture)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized string similar to 
        '''IF (NOT EXISTS (SELECT * 
        '''                 FROM INFORMATION_SCHEMA.TABLES 
        '''                 WHERE TABLE_NAME = &apos;CDMTableName&apos;))
        '''BEGIN
        '''
        '''CREATE TABLE &quot;DBO&quot;.&quot;CDMTableName&quot;
        '''(
        '''	&quot;HCPCS&quot; 			nvarchar(25) NOT NULL,
        '''	&quot;CDM&quot;   			nvarchar(25) NULL,
        '''	&quot;LongName&quot;      		nvarchar(256) NULL,
        '''	&quot;Quantity&quot;      		integer NULL DEFAULT 1,
        '''	&quot;PhysicianCDM&quot;  		nvarchar(25) NULL
        ''')
        '''
        '''END.
        '''</summary>
        Friend ReadOnly Property NewCreateCDMTableQuery() As String
            Get
                Return ResourceManager.GetString("NewCreateCDMTableQuery", resourceCulture)
            End Get
        End Property

        Friend ReadOnly Property BaselineFacilitySettings() As String
            Get
                Return ResourceManager.GetString("BaselineFacilitySettings", resourceCulture)
            End Get
        End Property

        Friend ReadOnly Property NewFacilitySettings() As String
            Get
                Return ResourceManager.GetString("NewFacilitySettings", resourceCulture)
            End Get
        End Property
    End Module
End Namespace

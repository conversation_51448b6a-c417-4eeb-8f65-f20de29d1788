﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="ENChartReports.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
            <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings />
    <!-- system.diagnostics section is not supported on .NET 6 (see https://github.com/dotnet/runtime/issues/23937)-->
    <!--<system.diagnostics>
  <sources>
    <!- - This section defines the logging configuration for My.Application.Log - ->
    <source name="DefaultSource" switchName="DefaultSwitch">
      <listeners>
        <add name="FileLog" />
        <!- - Uncomment the below section to write to the Application Event Log - ->
        <!- -<add name="EventLog"/>- ->
      </listeners>
    </source>
  </sources>
  <switches>
    <add name="DefaultSwitch" value="Information" />
  </switches>
  <sharedListeners>
    <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
    <!- - Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log - ->
    <!- -<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> - ->
  </sharedListeners>
</system.diagnostics>-->
    <applicationSettings>
        <ENChartReports.Settings>
            <setting name="ReportViewerClass" serializeAs="String">
                <value>ENChartReports.WebServiceReportViewer</value>
            </setting>
        </ENChartReports.Settings>
        <DevExpress.LookAndFeel.Design.AppSettings>
            <setting name="DefaultAppSkin" serializeAs="String">
                <value></value>
            </setting>
            <setting name="DefaultPalette" serializeAs="String">
                <value></value>
            </setting>
            <setting name="TouchUI" serializeAs="String">
                <value></value>
            </setting>
            <setting name="CompactUI" serializeAs="String">
                <value></value>
            </setting>
            <setting name="TouchScaleFactor" serializeAs="String">
                <value></value>
            </setting>
            <setting name="DirectX" serializeAs="String">
                <value></value>
            </setting>
            <setting name="RegisterUserSkins" serializeAs="String">
                <value></value>
            </setting>
            <setting name="RegisterBonusSkins" serializeAs="String">
                <value></value>
            </setting>
            <setting name="FontBehavior" serializeAs="String">
                <value></value>
            </setting>
            <setting name="DefaultAppFont" serializeAs="String">
                <value>Tahoma;9</value>
            </setting>
            <setting name="DPIAwarenessMode" serializeAs="String">
                <value></value>
            </setting>
            <setting name="CustomPaletteCollection" serializeAs="Xml">
                <value />
            </setting>
        </DevExpress.LookAndFeel.Design.AppSettings>
    </applicationSettings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
</configuration>
﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputType>WinExe</OutputType>
    <UseWPF>False</UseWPF>
    <StartupObject>ColdFeedExport.My.MyApplication</StartupObject>
    <MyType>WindowsForms</MyType>

    
    <ApplicationIcon>snowman.ico</ApplicationIcon>
    <RootNamespace>ColdFeedExport</RootNamespace>
     
    <UseWindowsForms>true</UseWindowsForms>
    <Configurations>Debug;Release;JJCDebug</Configurations>
    <Platforms>AnyCPU;</Platforms>
    
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    
    <!--<ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>-->
  </PropertyGroup>
  <!--<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\x86\Debug\</OutputPath>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\x86\Release\</OutputPath>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>-->
  <ItemGroup>
    <Import Include="EnchartDOLib" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Update="ColdFeedFieldExportMaps.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\EnchartDOLib\EnchartDOLib.vbproj" />
    <ProjectReference Include="..\McKeesson.HIC.HPFColdFeed.dll\McKesson.HIC.HPFColdFeed.vbproj" />
    <ProjectReference Include="..\McKesson.HIC.ChargeSummaryColdFeedHelper\McKesson.HIC.ChargeSummaryColdFeedHelper.vbproj" />
  </ItemGroup>
    <PropertyGroup>
    <!--<PostBuildEvent>copy "$(TargetPath)"  "C:\apps\eccoder\$(TargetFileName)</PostBuildEvent>-->
    <ImportedNamespaces>EnchartDOLib=False,System.Drawing=False,System.Windows.Forms=False,Microsoft.VisualBasic=True,System=True,System.Collections=True,System.Collections.Generic=True,System.Diagnostics=True,System.Linq=True,System.Xml.Linq=True,System.Threading.Tasks=True,ColdFeedExport=True</ImportedNamespaces>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="DevExpress.Win.Design" Version="23.1.4" />
    <PackageReference Include="DevExpress.Xpo" Version="23.1.4" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualBasic" Version="10.4.0-preview.18571.3" />
  </ItemGroup>
</Project>
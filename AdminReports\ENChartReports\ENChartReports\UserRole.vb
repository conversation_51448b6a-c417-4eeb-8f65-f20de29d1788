Public Class UserRole
    Private _oid As Integer
    Private _facility As Integer
    Private _role_name As String

    Public ReadOnly Property OID() As Integer
        Get
            Return _oid
        End Get
    End Property

    Public Property Facility() As Integer
        Get
            Return _facility
        End Get
        Set(ByVal value As Integer)
            _facility = value
        End Set
    End Property

    Public Property RoleName() As String
        Get
            Return _role_name
        End Get
        Set(ByVal value As String)
            _role_name = value
        End Set
    End Property

    Public Sub New(ByVal oid As Integer, ByVal facility As Integer, ByVal role_name As String)
        _oid = oid
        _facility = facility
        _role_name = role_name
    End Sub

    Public Overrides Function ToString() As String
        Return RoleName
    End Function
End Class

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target Name="PostBuild" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Exec Condition="'$(OutputType)' == 'Library'"
          Command="call $(MSBuildThisFileDirectory)SignCode.bat &quot;$(TargetPath)&quot;" />
    <Exec Condition="'$(OutputType)' != 'Library'"
          Command="call $(MSBuildThisFileDirectory)SignCode.bat &quot;$(TargetDir)$(TargetName).dll&quot;" />
    <Exec Condition="'$(OutputType)' != 'Library'"
          Command="call $(MSBuildThisFileDirectory)SignCode.bat &quot;$(TargetDir)$(TargetName).exe&quot;" />
  </Target>
</Project>

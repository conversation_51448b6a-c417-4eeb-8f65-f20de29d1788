<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class AddNewFacilityForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.teFacilityID = New DevExpress.XtraEditors.TextEdit
        Me.teTechContact = New DevExpress.XtraEditors.TextEdit
        Me.teEDContact = New DevExpress.XtraEditors.TextEdit
        Me.teLongName = New DevExpress.XtraEditors.TextEdit
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl
        Me.teComment = New DevExpress.XtraEditors.TextEdit
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl
        Me.teCompanyClient = New DevExpress.XtraEditors.TextEdit
        CType(Me.teFacilityID.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teTechContact.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teEDContact.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teLongName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teComment.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCompanyClient.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'teFacilityID
        '
        Me.teFacilityID.Location = New System.Drawing.Point(141, 40)
        Me.teFacilityID.Name = "teFacilityID"
        Me.teFacilityID.Size = New System.Drawing.Size(301, 20)
        Me.teFacilityID.TabIndex = 1
        '
        'teTechContact
        '
        Me.teTechContact.Location = New System.Drawing.Point(141, 118)
        Me.teTechContact.Name = "teTechContact"
        Me.teTechContact.Size = New System.Drawing.Size(301, 20)
        Me.teTechContact.TabIndex = 4
        '
        'teEDContact
        '
        Me.teEDContact.Location = New System.Drawing.Point(141, 92)
        Me.teEDContact.Name = "teEDContact"
        Me.teEDContact.Size = New System.Drawing.Size(301, 20)
        Me.teEDContact.TabIndex = 3
        '
        'teLongName
        '
        Me.teLongName.Location = New System.Drawing.Point(141, 66)
        Me.teLongName.Name = "teLongName"
        Me.teLongName.Size = New System.Drawing.Size(301, 20)
        Me.teLongName.TabIndex = 2
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(12, 47)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(47, 13)
        Me.LabelControl1.TabIndex = 4
        Me.LabelControl1.Text = "Facility ID"
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(12, 73)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(49, 13)
        Me.LabelControl2.TabIndex = 5
        Me.LabelControl2.Text = "Longname"
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(12, 99)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(54, 13)
        Me.LabelControl3.TabIndex = 6
        Me.LabelControl3.Text = "ED Contact"
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(12, 121)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(64, 13)
        Me.LabelControl4.TabIndex = 7
        Me.LabelControl4.Text = "Tech Contact"
        '
        'LabelControl5
        '
        Me.LabelControl5.Location = New System.Drawing.Point(12, 147)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl5.TabIndex = 9
        Me.LabelControl5.Text = "Comment"
        '
        'teComment
        '
        Me.teComment.Location = New System.Drawing.Point(141, 144)
        Me.teComment.Name = "teComment"
        Me.teComment.Size = New System.Drawing.Size(301, 20)
        Me.teComment.TabIndex = 5
        '
        'btnOK
        '
        Me.btnOK.Location = New System.Drawing.Point(272, 188)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(75, 23)
        Me.btnOK.TabIndex = 6
        Me.btnOK.Text = "OK"
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(367, 188)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 7
        Me.btnCancel.Text = "Cancel"
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(12, 21)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(75, 13)
        Me.LabelControl6.TabIndex = 13
        Me.LabelControl6.Text = "Company Client"
        '
        'teCompanyClient
        '
        Me.teCompanyClient.Enabled = False
        Me.teCompanyClient.Location = New System.Drawing.Point(141, 14)
        Me.teCompanyClient.Name = "teCompanyClient"
        Me.teCompanyClient.Size = New System.Drawing.Size(301, 20)
        Me.teCompanyClient.TabIndex = 0
        '
        'AddNewFacilityForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(486, 250)
        Me.Controls.Add(Me.LabelControl6)
        Me.Controls.Add(Me.teCompanyClient)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.LabelControl5)
        Me.Controls.Add(Me.teComment)
        Me.Controls.Add(Me.LabelControl4)
        Me.Controls.Add(Me.LabelControl3)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.teLongName)
        Me.Controls.Add(Me.teEDContact)
        Me.Controls.Add(Me.teTechContact)
        Me.Controls.Add(Me.teFacilityID)
        Me.MaximizeBox = False
        Me.Name = "AddNewFacilityForm"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Add New Facility"
        CType(Me.teFacilityID.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teTechContact.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teEDContact.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teLongName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teComment.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCompanyClient.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents teFacilityID As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teTechContact As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teEDContact As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teLongName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents teComment As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents teCompanyClient As DevExpress.XtraEditors.TextEdit
End Class

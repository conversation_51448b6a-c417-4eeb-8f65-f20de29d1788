<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ComboBoxListEditorFormv2
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.lblRealListName = New DevExpress.XtraEditors.LabelControl
        Me.lblListName = New DevExpress.XtraEditors.LabelControl
        Me.teNewItem = New DevExpress.XtraEditors.TextEdit
        Me.btnDone = New DevExpress.XtraEditors.SimpleButton
        Me.btnAddNew = New DevExpress.XtraEditors.SimpleButton
        Me.TreeList1 = New DevExpress.XtraTreeList.TreeList
        Me.colEnabled = New DevExpress.XtraTreeList.Columns.TreeListColumn
        Me.colItemDisplayName = New DevExpress.XtraTreeList.Columns.TreeListColumn
        Me.colItemDisplayOrder = New DevExpress.XtraTreeList.Columns.TreeListColumn
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection
        Me.UserRowBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton
        Me.btnUp = New DevExpress.XtraEditors.SimpleButton
        Me.btnDown = New DevExpress.XtraEditors.SimpleButton
        Me.RadioGroup1 = New DevExpress.XtraEditors.RadioGroup
        Me.btnMoveToTop = New DevExpress.XtraEditors.SimpleButton
        Me.btnMoveToBottom = New DevExpress.XtraEditors.SimpleButton
        Me.btnDelete = New DevExpress.XtraEditors.SimpleButton
        Me.btnDeleteDisabled = New DevExpress.XtraEditors.SimpleButton
        Me.ceAllowEdit = New DevExpress.XtraEditors.CheckEdit
        Me.btnSaveToDisk = New DevExpress.XtraEditors.SimpleButton
        Me.btnEspEdit = New DevExpress.XtraEditors.SimpleButton
        CType(Me.teNewItem.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TreeList1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UserRowBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAllowEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblRealListName
        '
        Me.lblRealListName.Appearance.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Italic)
        Me.lblRealListName.Appearance.Options.UseFont = True
        Me.lblRealListName.Appearance.Options.UseTextOptions = True
        Me.lblRealListName.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.lblRealListName.Location = New System.Drawing.Point(207, 351)
        Me.lblRealListName.Name = "lblRealListName"
        Me.lblRealListName.Size = New System.Drawing.Size(83, 16)
        Me.lblRealListName.TabIndex = 11
        Me.lblRealListName.Text = "Rea List Name"
        '
        'lblListName
        '
        Me.lblListName.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblListName.Appearance.Options.UseFont = True
        Me.lblListName.Location = New System.Drawing.Point(46, 21)
        Me.lblListName.Name = "lblListName"
        Me.lblListName.Size = New System.Drawing.Size(114, 19)
        Me.lblListName.TabIndex = 9
        Me.lblListName.Text = "LabelControl1"
        '
        'teNewItem
        '
        Me.teNewItem.Location = New System.Drawing.Point(46, 313)
        Me.teNewItem.Name = "teNewItem"
        Me.teNewItem.Size = New System.Drawing.Size(381, 20)
        Me.teNewItem.TabIndex = 0
        '
        'btnDone
        '
        Me.btnDone.Location = New System.Drawing.Point(540, 281)
        Me.btnDone.Name = "btnDone"
        Me.btnDone.Size = New System.Drawing.Size(115, 23)
        Me.btnDone.TabIndex = 2
        Me.btnDone.Text = "Save Changes"
        '
        'btnAddNew
        '
        Me.btnAddNew.Location = New System.Drawing.Point(444, 310)
        Me.btnAddNew.Name = "btnAddNew"
        Me.btnAddNew.Size = New System.Drawing.Size(75, 23)
        Me.btnAddNew.TabIndex = 1
        Me.btnAddNew.Text = "Add New"
        '
        'TreeList1
        '
        Me.TreeList1.Columns.AddRange(New DevExpress.XtraTreeList.Columns.TreeListColumn() {Me.colEnabled, Me.colItemDisplayName, Me.colItemDisplayOrder})
        Me.TreeList1.Location = New System.Drawing.Point(46, 48)
        Me.TreeList1.Name = "TreeList1"
        Me.TreeList1.OptionsBehavior.PopulateServiceColumns = True
        Me.TreeList1.Size = New System.Drawing.Size(473, 207)
        Me.TreeList1.TabIndex = 6
        '
        'colEnabled
        '
        Me.colEnabled.Caption = "Show"
        Me.colEnabled.FieldName = "Enabled"
        Me.colEnabled.Name = "colEnabled"
        Me.colEnabled.OptionsColumn.AllowSort = False
        Me.colEnabled.Visible = True
        Me.colEnabled.VisibleIndex = 0
        Me.colEnabled.Width = 54
        '
        'colItemDisplayName
        '
        Me.colItemDisplayName.Caption = "List Item"
        Me.colItemDisplayName.FieldName = "ItemDisplayName"
        Me.colItemDisplayName.Name = "colItemDisplayName"
        Me.colItemDisplayName.OptionsColumn.AllowEdit = False
        Me.colItemDisplayName.OptionsColumn.AllowMove = False
        Me.colItemDisplayName.OptionsColumn.AllowMoveToCustomizationForm = False
        Me.colItemDisplayName.OptionsColumn.AllowSort = False
        Me.colItemDisplayName.Visible = True
        Me.colItemDisplayName.VisibleIndex = 1
        Me.colItemDisplayName.Width = 398
        '
        'colItemDisplayOrder
        '
        Me.colItemDisplayOrder.Caption = "Sort Order"
        Me.colItemDisplayOrder.FieldName = "ItemDisplayOrder"
        Me.colItemDisplayOrder.Name = "colItemDisplayOrder"
        Me.colItemDisplayOrder.OptionsColumn.AllowEdit = False
        Me.colItemDisplayOrder.Width = 72
        '
        'XpCollection1
        '
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOConfigComboBoxListsItem)
        '
        'UserRowBindingSource
        '
        Me.UserRowBindingSource.DataSource = GetType(ENChartCAC.UserConfigUserForm.UserRow)
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(540, 310)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(115, 23)
        Me.btnCancel.TabIndex = 3
        Me.btnCancel.Text = "Exit Without Saving"
        '
        'btnUp
        '
        Me.btnUp.Location = New System.Drawing.Point(540, 78)
        Me.btnUp.Name = "btnUp"
        Me.btnUp.Size = New System.Drawing.Size(115, 23)
        Me.btnUp.TabIndex = 5
        Me.btnUp.Text = "Move Up"
        '
        'btnDown
        '
        Me.btnDown.Location = New System.Drawing.Point(540, 203)
        Me.btnDown.Name = "btnDown"
        Me.btnDown.Size = New System.Drawing.Size(115, 23)
        Me.btnDown.TabIndex = 7
        Me.btnDown.Text = "Move Down"
        '
        'RadioGroup1
        '
        Me.RadioGroup1.Location = New System.Drawing.Point(46, 261)
        Me.RadioGroup1.Name = "RadioGroup1"
        Me.RadioGroup1.Properties.Items.AddRange(New DevExpress.XtraEditors.Controls.RadioGroupItem() {New DevExpress.XtraEditors.Controls.RadioGroupItem("ASCENDING", "Ascending"), New DevExpress.XtraEditors.Controls.RadioGroupItem("DESCENDING", "Descending"), New DevExpress.XtraEditors.Controls.RadioGroupItem("USE SORT ORDER", "Use Sort Order")})
        Me.RadioGroup1.Size = New System.Drawing.Size(473, 28)
        Me.RadioGroup1.TabIndex = 15
        '
        'btnMoveToTop
        '
        Me.btnMoveToTop.Location = New System.Drawing.Point(540, 49)
        Me.btnMoveToTop.Name = "btnMoveToTop"
        Me.btnMoveToTop.Size = New System.Drawing.Size(115, 23)
        Me.btnMoveToTop.TabIndex = 4
        Me.btnMoveToTop.Text = "Move To Top"
        '
        'btnMoveToBottom
        '
        Me.btnMoveToBottom.Location = New System.Drawing.Point(540, 232)
        Me.btnMoveToBottom.Name = "btnMoveToBottom"
        Me.btnMoveToBottom.Size = New System.Drawing.Size(115, 23)
        Me.btnMoveToBottom.TabIndex = 8
        Me.btnMoveToBottom.Text = "Move To Bottom"
        '
        'btnDelete
        '
        Me.btnDelete.Location = New System.Drawing.Point(540, 127)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(115, 23)
        Me.btnDelete.TabIndex = 6
        Me.btnDelete.Text = "Delete"
        '
        'btnDeleteDisabled
        '
        Me.btnDeleteDisabled.Location = New System.Drawing.Point(540, 156)
        Me.btnDeleteDisabled.Name = "btnDeleteDisabled"
        Me.btnDeleteDisabled.Size = New System.Drawing.Size(115, 23)
        Me.btnDeleteDisabled.TabIndex = 16
        Me.btnDeleteDisabled.Text = "Delete All Disabled"
        '
        'ceAllowEdit
        '
        Me.ceAllowEdit.Location = New System.Drawing.Point(44, 350)
        Me.ceAllowEdit.Name = "ceAllowEdit"
        Me.ceAllowEdit.Properties.Caption = "Edit Mode"
        Me.ceAllowEdit.Size = New System.Drawing.Size(115, 19)
        Me.ceAllowEdit.TabIndex = 17
        Me.ceAllowEdit.ToolTip = "Must be manually enabled to avoid unintentional changes."
        '
        'btnSaveToDisk
        '
        Me.btnSaveToDisk.Location = New System.Drawing.Point(540, 339)
        Me.btnSaveToDisk.Name = "btnSaveToDisk"
        Me.btnSaveToDisk.Size = New System.Drawing.Size(115, 23)
        Me.btnSaveToDisk.TabIndex = 18
        Me.btnSaveToDisk.Text = "Save To File"
        Me.btnSaveToDisk.Visible = False
        '
        'btnEspEdit
        '
        Me.btnEspEdit.Location = New System.Drawing.Point(540, 12)
        Me.btnEspEdit.Name = "btnEspEdit"
        Me.btnEspEdit.Size = New System.Drawing.Size(115, 23)
        Me.btnEspEdit.TabIndex = 19
        Me.btnEspEdit.Text = "ESPEdit"
        Me.btnEspEdit.Visible = False
        '
        'ComboBoxListEditorFormv2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(679, 381)
        Me.ControlBox = False
        Me.Controls.Add(Me.btnEspEdit)
        Me.Controls.Add(Me.btnSaveToDisk)
        Me.Controls.Add(Me.ceAllowEdit)
        Me.Controls.Add(Me.btnDeleteDisabled)
        Me.Controls.Add(Me.btnDelete)
        Me.Controls.Add(Me.btnMoveToBottom)
        Me.Controls.Add(Me.btnMoveToTop)
        Me.Controls.Add(Me.RadioGroup1)
        Me.Controls.Add(Me.btnDown)
        Me.Controls.Add(Me.btnUp)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.lblRealListName)
        Me.Controls.Add(Me.lblListName)
        Me.Controls.Add(Me.teNewItem)
        Me.Controls.Add(Me.btnDone)
        Me.Controls.Add(Me.btnAddNew)
        Me.Controls.Add(Me.TreeList1)
        Me.Name = "ComboBoxListEditorFormv2"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Edit List"
        CType(Me.teNewItem.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TreeList1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UserRowBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAllowEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Public WithEvents lblRealListName As DevExpress.XtraEditors.LabelControl
    Public WithEvents lblListName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents teNewItem As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnDone As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAddNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TreeList1 As DevExpress.XtraTreeList.TreeList
    Friend WithEvents colEnabled As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents colItemDisplayName As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents colItemDisplayOrder As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents UserRowBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnUp As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnDown As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents RadioGroup1 As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents btnMoveToTop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnMoveToBottom As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnDeleteDisabled As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ceAllowEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents btnSaveToDisk As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnEspEdit As DevExpress.XtraEditors.SimpleButton
End Class

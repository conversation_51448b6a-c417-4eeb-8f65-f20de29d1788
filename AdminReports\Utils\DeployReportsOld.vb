Public Sub Main()
   Dim folder As String = PARENTFOLDER
   Dim name As String = RDLNAME
   Dim dbserver As String = DATABASESERVER
   Dim data As String = DATABASE
   Dim parent As String = "/" + folder
   Dim location As String = name + ".rdl"
   Dim overwrite As Boolean = True
   Dim reportContents As Byte() = Nothing

   Dim fullpath As String = parent + "/" + name
   
     Try

		Console.Writeline("")
		Console.WriteLine("Deploying Report: {0}", location)
        Dim stream As FileStream = File.OpenRead(location)
        reportContents = New [Byte](stream.Length-1) {}
        stream.Read(reportContents, 0, CInt(stream.Length))
        stream.Close()

        Dim warnings As Warning() = RS.CreateReport(name, parent, overwrite, reportContents, GetProps())
  
        If Not (warnings Is Nothing) Then
           Dim warning As Warning
           For Each warning In warnings
              Console.WriteLine(Warning.Message)
           Next warning
        Else
           Console.WriteLine("Report: {0} published successfully with no warnings", name)
        End If

        Console.WriteLine("Report: {0} published successfully", name)

    Catch e As IOException
       Console.WriteLine(e.Message)
    Catch e As SoapException
       Console.WriteLine("Error : " + e.Detail.Item("ErrorCode").InnerText + " (" + e.Detail.Item("Message").InnerText + ")")
       Console.WriteLine("Report: {0} published with error", name)
    End Try
  
End Sub

private function GetProps() as [Property]()

   Dim descprop As New [Property]
   descprop.Name = "Description"
   descprop.Value = ""
   Dim hiddenprop As New [Property]
   hiddenprop.Name = "Hidden"
   hiddenprop.Value = "False"

    Dim props(1) As [Property]
    props(0) = descprop
    props(1) = hiddenprop
	
	return props
end function
﻿Imports McKesson.HIC.ColdFeed
Imports System.IO
Imports CodingReport


Namespace ColdFeed

    'Note this dll is not reference anywhere... it's loaded dynamically at runtime
    Public Class HPFColdFeedImageFileHandler
        Implements IExportChargeSummaryImageFile


        Public Function CreateImageFile(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryImageFile.CreateImageFile
            Dim outPath As String = CFHelper.OptionsDict("ImageOutputPath")
            Dim FileType As String = CFHelper.OptionsDict("ImageFileType") '"tiff" 
            Dim fileName = String.Format("{0}_{1}v{3}.{2}", chart.Chart.ChartInfo.Facility.FacilityID,
                                        chart.Chart.VisitID, FileType, chart.Chart.Version)

            Dim fullPath = Path.Combine(My.Application.Info.DirectoryPath, outPath)

            If Not Directory.Exists(fullPath) Then
                Directory.CreateDirectory(fullPath)
            End If

            fullPath = Path.Combine(fullPath, fileName)

            Dim report As New XtraCodingReport
            Dim sw As New Stopwatch
            sw.Start()

            report.Area51CodingReport(chart.Chart.Oid.ToString, fullPath, FileType)

            sw.Stop()

            Debug.WriteLine(sw.ElapsedMilliseconds)
            chart.ImageFileName = Path.GetFileName(fullPath)

            If File.Exists(fullPath) Then
                chart.ImageExportSucceeded = True
            Else
                chart.ImageExportSucceeded = False
            End If

            Return chart.ImageExportSucceeded
        End Function

        Public Sub Init() Implements IExportChargeSummaryImageFile.Init

        End Sub

        Private _cFHelper As ColdFeedHelper
        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryImageFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property
    End Class
End Namespace
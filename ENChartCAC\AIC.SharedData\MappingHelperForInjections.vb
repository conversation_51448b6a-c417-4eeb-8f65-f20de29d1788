﻿Option Infer On
Imports System.Linq
Imports AIC.SharedData.CuresAct

Namespace CuresAct
    Public Class MappingHelperForInjectionsFromRedisplay

        Public ReadOnly Property ObsEnabled As Boolean
        Private FacilityInjectionControlNamesList As New List(Of List(Of String))
        Private ObsInjectionControlNamesList As New List(Of List(Of String))

        Public ReadOnly Property redisplayDict As Dictionary(Of String, String)

        Public Sub New(redisplayDict As Dictionary(Of String, String), obsEnabled As Boolean)
            Me.ObsEnabled = obsEnabled
            InitControlNamesLists()
            Me.redisplayDict = redisplayDict
        End Sub

        Public Function GetInjectionsFromRedisplay() As List(Of PatientInfusion)
            InjectionsList.AddRange(CopyFacilityInjectionRedisplayDataToModels)
            InjectionsList.AddRange(CopyObsInjectionRedisplayDataToModels)

            Dim outlist = InjectionsList.Where(Function(Item) String.IsNullOrEmpty(Item.Medication) = False).ToList()
            Return outlist
        End Function

        Public Function SortInjections(InjectionsToSortList As List(Of PatientInfusion)) As List(Of PatientInfusion)

            Dim outlist = (From Injection In InjectionsToSortList
                           Order By Injection.StartDateTime, Injection.Medication
                           Select Injection).ToList()

            Return outlist
        End Function

        Private Sub InitControlNamesLists()
            FacilityInjectionControlNamesList = GetFacilityInjectionControlNamesList()
            ObsInjectionControlNamesList = GetObsInjectionControlNamesList()
        End Sub

        Private Function GetFacilityInjectionControlNamesList() As List(Of List(Of String))
            Dim outFacilityInjectionControlNamesList As New List(Of List(Of String))

            For irow = 1 To 15
                Dim fieldNamesList = GetListOfFacilityInjectionRowFieldNames(irow, FacilityInjectionFieldNamesList)
                outFacilityInjectionControlNamesList.Add(fieldNamesList)
            Next

            Return outFacilityInjectionControlNamesList
        End Function

        Private Function GetObsInjectionControlNamesList() As List(Of List(Of String))
            Dim outObsInjectionControlNamesList As New List(Of List(Of String))
            If ObsEnabled = False Then Return outObsInjectionControlNamesList
            For irow = 1 To 50
                Dim fieldNamesList = GetListOfFacilityInjectionRowFieldNames(irow, ObsInjectionFieldNamesList)
                outObsInjectionControlNamesList.Add(fieldNamesList)
            Next

            Return outObsInjectionControlNamesList
        End Function
        Private Function GetControlValueByName(controlName As String) As String
            Dim outString As String = ""

            If redisplayDict.ContainsKey(controlName) Then
                outString = redisplayDict(controlName)
            End If

            Return outString
        End Function

        Public Property InjectionsList As New List(Of PatientInfusion)

        Private FacilityInjectionFieldNamesList = {"Medication_InjectionsDate01_dte",
        "Medication_InjectionsTime01_txt",
        "Medication_InjectionsMed01_cbo",
        "Medication_Injections01_cbo" 'route
    }

        Private ObsInjectionFieldNamesList = {"ObsMedication_Injections_Date_01",
        "ObsMedication_Injections_Time_01",
        "ObsMedication_Injections_Medication_01",
        "ObsMedication_Injections_Route_01"
    }

        'Injections_TitratedMedicationsStartDate01_dte
        Private Function GetListOfFacilityInjectionRowFieldNames(rowNumber As Short, fieldNamesList As String()) As List(Of String)
            Dim outList As New List(Of String)
            For Each field As String In fieldNamesList
                outList.Add(field.Replace("01", rowNumber.ToString("D2")))
            Next

            Return outList
        End Function

        Private Function CopyFacilityInjectionRedisplayDataToModels() As List(Of PatientInfusion)
            Dim outInjections As New List(Of PatientInfusion)
            Dim count As Short = 0
            For Each row In FacilityInjectionControlNamesList
                count += 1
                Dim Injection = CopyInjectionsRedisplayRowToModel(row)
                outInjections.Add(Injection)
                ' Injection.OriginalRow = count
            Next

            Return outInjections
        End Function

        Private Function CopyObsInjectionRedisplayDataToModels() As List(Of PatientInfusion)
            Dim outInjections As New List(Of PatientInfusion)
            If ObsEnabled = False Then Return outInjections
            Dim count As Short = 0
            For Each row In ObsInjectionControlNamesList
                count += 1
                Dim Injection = CopyInjectionsRedisplayRowToModel(row)
                outInjections.Add(Injection)
                'Injection.IsObs = True
                'Injection.OriginalRow = count
            Next

            Return outInjections
        End Function

        Private Function CopyInjectionsRedisplayRowToModel(fieldNamesList As List(Of String)) As PatientInfusion

            'Dim outInjection As New PatientInfusion With {
            '.Startdate = GetControlValueByName(fieldNamesList(0)),
            '.StartTime = GetControlValueByName(fieldNamesList(1)),
            '.Medication = GetControlValueByName(fieldNamesList(2))
            '}

            'Return outInjection

            Dim outInfusion As New PatientInfusion()
            Dim tempDate As Date

            If Date.TryParse(GetControlValueByName(fieldNamesList(0)), tempDate) Then
                outInfusion.Startdate = tempDate
            Else
                outInfusion.Startdate = Nothing
            End If

            outInfusion.StartTime = GetControlValueByName(fieldNamesList(1))

            'If Date.TryParse(GetControlValueByName(fieldNamesList(2)), tempDate) Then
            '    outInfusion.EndDate = tempDate
            'Else
            '    outInfusion.EndDate = Nothing
            'End If

            'outInfusion.EndTime = GetControlValueByName(fieldNamesList(3))
            outInfusion.Medication = GetControlValueByName(fieldNamesList(2))

            Return outInfusion
        End Function

        Public Function CombineDateAndTime(targetDate As Date?, timeOfDay As String) As Date?
            Dim outDate As Date?
            Try
                If targetDate.HasValue Then
                    outDate = targetDate.Value
                    If String.IsNullOrEmpty(timeOfDay) = False Then
                        Dim tod As Date = timeOfDay
                        outDate += tod.TimeOfDay
                    End If
                End If
            Catch ex As Exception
                Return Nothing
            End Try

            If outDate.HasValue = False Then
                outDate = Date.MaxValue 'for sorting purposes
            End If
            Return outDate
        End Function

        Protected Overrides Sub Finalize()
            MyBase.Finalize()
        End Sub
    End Class
End Namespace
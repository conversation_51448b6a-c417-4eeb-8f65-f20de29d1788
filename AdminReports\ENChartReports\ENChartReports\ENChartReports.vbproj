﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputType>Library</OutputType>
    <StartupObject>ENChartReports.My.MyApplication</StartupObject>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>search4doc.ico</ApplicationIcon>
    <!--<IsWebBootstrapper>true</IsWebBootstrapper>
    <PublishUrl>http://localhost/ENChartReports/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>-->
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <!--<GenerateAssemblyInfo>false</GenerateAssemblyInfo>-->
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>false</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <ValidateExecutableReferencesMatchSelfContained>false</ValidateExecutableReferencesMatchSelfContained>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>ENChartReports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355,BC40027,BC40028</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>ENChartReports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355,BC40027,BC40028</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DocumentationFile>ENChartReports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355,BC40027,BC40028</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DocumentationFile>ENChartReports.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355,BC40027,BC40028</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="ReportViewer\**" />
    <EmbeddedResource Remove="ReportViewer\**" />
    <None Remove="ReportViewer\**" />
    <Page Remove="ReportViewer\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="CrystalReportsUpdateDialog.Designer.vb" />
    <Compile Remove="CrystalReportsUpdateDialog.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Remove="CrystalReportsUpdateDialog.resx" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="DatasetViewer.Designer.vb">
      <DependentUpon>DatasetViewer.vb</DependentUpon>
    </Compile>
    <Compile Update="FormNewCategory.Designer.vb">
      <DependentUpon>FormNewCategory.vb</DependentUpon>
    </Compile>
    <Compile Update="FormNewReport.Designer.vb">
      <DependentUpon>FormNewReport.vb</DependentUpon>
    </Compile>
    <Compile Update="PdfViewerForm.Designer.vb">
      <DependentUpon>PdfViewerForm.vb</DependentUpon>
    </Compile>
    <Compile Update="ReportRolesEditor\ReportRolesEditorV3.Designer.vb">
      <DependentUpon>ReportRolesEditorV3.vb</DependentUpon>
    </Compile>
    <Compile Update="ReportRolesEditor\NewReportRole.Designer.vb">
      <DependentUpon>NewReportRole.vb</DependentUpon>
    </Compile>
    <Compile Update="ReportRolesEditor\ReportRolesEditor.Designer.vb">
      <DependentUpon>ReportRolesEditor.vb</DependentUpon>
    </Compile>
    <Compile Update="ReportStartup.Designer.vb">
      <DependentUpon>ReportStartup.vb</DependentUpon>
    </Compile>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Update="XMLImport\FormXMLImport.Designer.vb">
      <DependentUpon>FormXMLImport.vb</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ChangeNotes.txt" />
    <Content Include="Migration\MigrationScript.sql" />
    <Content Include="search4doc.ico" />
  </ItemGroup>
 
  <ItemGroup>
    <ProjectReference Include="..\..\..\ENChartCAC\EnchartDOLib\EnchartDOLib.vbproj" />
  </ItemGroup>
   <ItemGroup>
    <PackageReference Include="DevExpress.Document.Processor" Version="23.1.5" />
    <PackageReference Include="DevExpress.Win.Design" Version="23.1.5" />
    <PackageReference Include="DevExpress.Xpo" Version="23.1.5" />
    <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
    <!--<PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>-->
  </ItemGroup>
  <Import Project="..\..\..\build\CustomPostBuild.targets" />
</Project>
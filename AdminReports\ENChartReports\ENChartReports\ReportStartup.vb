Imports System.Collections.Specialized
Imports System.IO
Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB
Imports DevExpress.XtraReports.Wizards
Imports DevExpress.XtraSplashScreen
Imports DevExpress.XtraTreeList
Imports DevExpress.XtraTreeList.Nodes
Imports EnchartDOLib
Imports EnchartServer.Data.DTO

Public Interface IRequestReport
    ' Function RequestReport(ByVal url As String, ByVal queryParameterList As Dictionary(Of String, Object)) As Byte()
    Function RequestReport(ByVal reportInfo As DTOReportInfo, Optional parentForm As Form = Nothing) As Byte()
End Interface

Public Class ReportStartup

    Dim seconds_til_timeout As Integer
    Dim edit_timeout_seconds As Integer
    Dim is_forced_logoff As Boolean = False

    Private Enum NodeType
        Facility
        Category
        Report
    End Enum

    Private Enum UpdateAction
        Ignore
        NoUpdate
        Update
        Delete
    End Enum

    Private Property ReportLock As DOFacilityReportLock
    Public Property CurrentUser As DOUser
    Public Property CurrentUserReportRole As DOUserRolesList
    Public Property IsMICAdmin As Boolean

    ''' <summary>
    ''' The ActiveFacility is the DOFacility corresponding to the facility OID that is passed into this application via command line, 
    ''' if no command line parameter is passed, ActiveFacility will be Nothing
    ''' </summary>
    ''' <returns></returns>
    Public Property ActiveFacility As DOFacility

    ''' <summary>
    ''' The CurrentFacility is the facility of the node that is currently selected in treeReports
    ''' </summary>
    ''' <returns></returns>
    Public ReadOnly Property CurrentFacility As DOFacility
        Get
            Try
                Dim facNode As TreeListNode = GetFacilityNode(treeReports.FocusedNode)
                Return facNode.GetValue("colObj")
            Catch ex As Exception
                Return Nothing
            End Try
        End Get
    End Property

    Private Property Editing As Boolean = False
    Private Property EditNode As TreeListNode
    Private Property CopiedReport As DOFacilityReport
    Private Property PendingChanges As Boolean = False
    Public ReadOnly Property ReqReport As IRequestReport
    Public ReadOnly Property ReportsDirPath As String
    Private Const COL_UPDATE_ACTION = "colUpdateAction"

#Region "New version"

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        Exit Sub
    End Sub

    Private _mainUIIdleTimer As Timer
    Private _ReportNamesList As List(Of String)
    ' This is the constructor that is called when this application is launched from the AIC application
    Public Sub New(user As DOUser, facility As DOFacility, idleTimer As Timer, reqReport As IRequestReport, reportsDirPath As String)
        MyBase.New

        _mainUIIdleTimer = idleTimer
        Me.ReqReport = reqReport
        Me.ReportsDirPath = reportsDirPath
        CurrentUser = user
        ActiveFacility = facility

        ' This call is required by the designer.
        InitializeComponent()
        'Me.StartPosition = FormStartPosition.CenterScreen

        '' Add any initialization after the InitializeComponent() call.
        'Try
        '    Me.Cursor = Cursors.WaitCursor
        '    LoadBrandingImage()

        '    CurrentUser = user
        '    ActiveFacility = facility

        '    If CurrentUser Is Nothing Then
        '        MsgBox("Could not find the specified user.", MsgBoxStyle.Exclamation, "User Not Found")
        '        Me.Close()
        '        Exit Sub
        '    End If

        '    IsMICAdmin = CurrentUser.Oid = 1

        '    If IsMICAdmin Then
        '        AdminToolStripMenuItem.Visible = True
        '        XMLImportToolStripMenuItem.Visible = True
        '    Else
        '        For Each p As DOUserPermission In CurrentUser.Permissions
        '            If p.Role.ToUpper = "ADMIN" AndAlso p.Enabled AndAlso p.Facility.Oid = ActiveFacility?.Oid Then
        '                AdminToolStripMenuItem.Visible = True
        '            End If
        '        Next

        '        Dim role_name As String = CurrentUser.GetUserSetting("ReportRole", ActiveFacility)

        '        If (role_name Is Nothing) OrElse (role_name = "") Then
        '            MsgBox("You are not assigned a role to view reports, please contact an administrator to have one assigned.", MsgBoxStyle.OkOnly, "No Report Role Assigned")
        '            btnEdit.Enabled = False
        '        End If

        '        CurrentUserReportRole = GetRole(ActiveFacility, role_name)
        '    End If

        '    Max_dte.EditValue = Today
        '    Min_dte.EditValue = Date.Today.AddMonths(-1)

        '    PopulateNodes()
        '    ClearExpiredLocks()
        '    Me.Cursor = Cursors.Arrow
        'Catch ex As Exception
        '    MessageBox.Show("Problems Loading Form")
        'End Try
    End Sub


    Private Sub RunReport_btn_Click(ByVal sender As Object, ByVal e As EventArgs) Handles RunReport_btn.Click
        'If key = Keys.Shift Then
        'If Form.ModifierKeys = Keys.Shift Then

        _mainUIIdleTimer.Stop()
        Me.Cursor = Cursors.WaitCursor

        Application.DoEvents()
        Dim overlayHandle As IOverlaySplashScreenHandle = Nothing
        Dim pdfViewer As PdfViewerForm = Nothing
        Dim xcelViewer As ExcelViewer = Nothing
        Try
            'JJC 2024.28.2 The overlay form is causing the report to open behind the main window. So I'm commenting it out for now.
            overlayHandle = SplashScreenManager.ShowOverlayForm(Me)
            Me.Cursor = Cursors.WaitCursor
            Dim reportFormat = ddlReportFormat.Text
            Dim fromDate = Min_dte.EditValue
            Dim toDate = Max_dte.EditValue
            Dim treatmentArea = ddlTreatmentArea.Text
            Dim report As DOFacilityReport = CType(treeReports.FocusedNode.GetValue("colObj"), DOFacilityReport)

            Dim reportInfo = BuildReportInfo(report)
            Dim returnedBytes = ReqReport.RequestReport(reportInfo, Me)
            SplashScreenManager.CloseOverlayForm(overlayHandle)

            Select Case reportFormat
                Case "EXCEL"
                    xcelViewer = New ExcelViewer(returnedBytes)
                    xcelViewer.Show()
                Case "PDF"
                    Using mstream As New MemoryStream(returnedBytes)
                        pdfViewer = New PdfViewerForm()
                        pdfViewer.LoadDoc(mstream)
                        'pdfViewer.TopMost = True
                        pdfViewer.Show()
                        'pdfViewer.TopMost = False
                    End Using
            End Select

        Catch ex As Exception
            SplashScreenManager.CloseOverlayForm(overlayHandle)
            'Throw New Exception("Error running report", ex)
            Console.WriteLine(ex.ToString())
        Finally
            _mainUIIdleTimer.Start()
            Cursor = Cursors.Default

            If pdfViewer IsNot Nothing Then
                Application.DoEvents() 'without this, the pdfViewer will not be brought to the front
                pdfViewer.BringToFront()
                pdfViewer = Nothing
            End If

            If xcelViewer IsNot Nothing Then
                Application.DoEvents() 'without this, the pdfViewer will not be brought to the front
                xcelViewer.BringToFront()
                xcelViewer = Nothing
            End If
        End Try

        'BasicTimeoutWatcher.Resume()
    End Sub

    Private Function BuildReportInfo(report As DOFacilityReport) As DTOReportInfo
        Dim reportInfo As New DTOReportInfo
        reportInfo.ReportID = report.Oid
        reportInfo.ReportFilename = report.ReportFilename
        reportInfo.MinDate = Min_dte.Text
        reportInfo.MaxDate = Max_dte.Text
        reportInfo.FacilityOid = ActiveFacility.Oid
        reportInfo.TreatmentArea = ddlTreatmentArea.Text
        reportInfo.ResponseFormat = ddlReportFormat.Text

        Return reportInfo
    End Function
    Private Function GetRole(ByVal facility As DOFacility, ByVal role_name As String) As DOUserRolesList
        For Each role As DOUserRolesList In facility.UserRolesList
            If role.Role = role_name Then
                Return role
            End If
        Next
        Return Nothing
    End Function

    Private Function LoadFacilityReportSettings(ByVal facility As DOFacility) As FacilityReportSettings
        Dim returnObj As New FacilityReportSettings
        Dim timeoutSettings As New XPCollection(Of DOFacilitySettings)(CriteriaOperator.Parse(String.Format("Facility = {0} AND RuleSetting = '{1}'",
            facility.Oid, "EditTimeoutSeconds")))

        Dim roleSettings As New XPCollection(Of DOFacilitySettings)(CriteriaOperator.Parse(String.Format("Facility = {0} AND RuleSetting = '{1}'",
            facility.Oid, "AllowNewUserRoles")))

        If timeoutSettings?.Count = 0 AndAlso roleSettings?.Count = 0 Then
            MessageBox.Show("The report settings could not be loaded for facility " & facility?.Oid & ", loading default settings. Please contact Horizon Intelligent Coding support.", "Error Loading Report Settings", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            returnObj.EditTimeoutSeconds = 300
            returnObj.AllowNewUserRoles = False
        Else
            returnObj.EditTimeoutSeconds = timeoutSettings(0).RuleSettingValue
            returnObj.AllowNewUserRoles = CBool(roleSettings(0).RuleSettingValue)
        End If

        Console.WriteLine(String.Format("Facility: {0} EditTimeoutSeconds: {1} AllowNewUserRoles {2}", facility.Oid, returnObj.EditTimeoutSeconds, returnObj.AllowNewUserRoles))
        Return returnObj
    End Function

    Private Sub LoadBrandingImage()
        'Try
        '    'PictureBox1.Image = Image.FromFile(Application.StartupPath & "\crimage2.jpg")
        '    'Dim logoFileFullName As String = Application.StartupPath & "\crimage.jpg"
        '    Dim logoFileFullName As String = Application.StartupPath & "\ventuslogo2.png"
        '    Dim imgFile As New FileInfo(logoFileFullName)
        '    If imgFile.Exists Then
        '        Dim img As Image
        '        Using bmpTemp As New Bitmap(logoFileFullName)
        '            img = New Bitmap(bmpTemp)
        '        End Using
        '        PictureBox1.Image = img 'Image.FromFile(Application.StartupPath & "\crimage.jpg")
        '    End If

        'Catch ex As IO.FileNotFoundException
        '    ' If the image can't be loaded, just carry on
        'End Try
    End Sub


#Region "Nodes"

    ''' <summary>
    ''' This method essentially just loads a big portion of the reports configuration so that it is stored in the
    ''' devexpress cache on the client machine.
    ''' </summary>
    ''' <remarks>
    ''' note, this may also help refresh some lists after editing categories and reports, but i'm not sure.</remarks>
    ''' <param name="facility"></param>
    Private Sub BulkLoadReportConfig(facility As DOFacility)
        Dim repCats As New XPCollection(Of DOFacilityReportCategory)(CriteriaOperator.Parse($"Facility = {facility.Oid}"))
        XpoDefault.Session.PreFetch(Of DOFacilityReportCategory)(repCats, {"FacilityReports", "SubCategories", "CategoryRoles"})

    End Sub

    Public Sub PopulateNodes()

        Dim nothingNode As TreeListNode = Nothing ' Need to have a node that represents nothing

        treeReports.ClearNodes() ' Clear the tree (important when this is called after adding new reports)
        'report_dict.Clear() ' ditto for the dictionary

        Dim facilities As List(Of DOFacility) = DOFacility.GetAllFacilitesAsList()
        For Each facility As DOFacility In facilities
            If (ActiveFacility Is Nothing) OrElse (facility.Oid = ActiveFacility.Oid) Then
                BulkLoadReportConfig(facility)
                PopulateFacilityNode(facility, nothingNode)
            End If
        Next
        treeReports.SetFocusedNode(treeReports.Nodes(0))
    End Sub

    Private Sub PopulateFacilityNode(ByVal facility As DOFacility, ByVal parentnode As TreeListNode)
        Dim facilityNode As TreeListNode = treeReports.AppendNode(facility, parentnode, NodeType.Facility)
        facilityNode.SetValue("colName", "FACILITY - " & facility.LongName)
        facilityNode.SetValue("colObj", facility)
        facilityNode.SetValue("colFacilitySettings", LoadFacilityReportSettings(facility))
        facilityNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Ignore)


        For Each category As DOFacilityReportCategory In DOFacilityReportCategory.GetRootCategoriesByFacility(facility)
            If CurrentUser?.IsTheAICAdmin Then
                PopulateCategoryNode(category, facilityNode)
            Else
                For Each categoryRole As DOFacilityReportCategoryRole In category.CategoryRoles

                    If categoryRole.UserRole.Oid = CurrentUserReportRole.Oid Then
                        PopulateCategoryNode(category, facilityNode)
                        Exit For
                    End If
                Next
            End If
        Next
        facilityNode.Expanded = True
    End Sub

    Private Sub PopulateCategoryNode(ByVal category As DOFacilityReportCategory, ByVal parentNode As TreeListNode)
        If category.CategoryType.ToUpper.Equals("NEWREPORTS") AndAlso category.FacilityReports.Count = 0 Then
            Return
        End If

        Dim categoryNode As TreeListNode = treeReports.AppendNode(category, parentNode, NodeType.Category)
        categoryNode.SetValue("colName", If(category.CategoryType.ToUpper.Equals("NEWREPORTS"), String.Format("{0} ({1})", category.CategoryName, category.FacilityReports.Count), category.CategoryName))
        categoryNode.SetValue("colObj", category)
        categoryNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)

        For Each subCategory As DOFacilityReportCategory In category.SubCategories
            PopulateCategoryNode(subCategory, categoryNode)
        Next

        Dim reports As XPCollection(Of DOFacilityReport) = category.FacilityReports

        'Dim actualReportNames As List(Of String) = GetActualReportNamesFromDisk()


        If Not category.CategoryType.ToUpper.Equals("USER") Then
            Dim reportSortCollection As SortingCollection = New SortingCollection()
            reportSortCollection.Add(New SortProperty("ReportDescription", SortingDirection.Ascending))
            reports.Sorting = reportSortCollection
        End If

        For Each report As DOFacilityReport In reports
            PopulateReportNode(report, categoryNode)
        Next

    End Sub
    Public Property ReportNamesList As List(Of String)
        Get
            If _ReportNamesList Is Nothing Then
                _ReportNamesList = GetActualReportNamesFromDisk()
            End If
            Return _ReportNamesList
        End Get
        Set
            _ReportNamesList = Value
        End Set
    End Property

    Private Function GetActualReportNamesFromDisk() As List(Of String)
        Dim fileNamesWithoutExt As New List(Of String)
        If Directory.Exists(ReportsDirPath) = False Then
            Return fileNamesWithoutExt
        End If
        Dim files As String() = Directory.GetFiles(ReportsDirPath, "*.rdl")

        For Each file In files
            fileNamesWithoutExt.Add(Path.GetFileNameWithoutExtension(file))
        Next

        Return fileNamesWithoutExt
    End Function

    Private Sub PopulateReportNode(ByVal report As DOFacilityReport, ByVal parentNode As TreeListNode)
        Dim bRemoveReport As Boolean = False
        Dim reportNameToAdd = Path.GetFileNameWithoutExtension(report.ReportFilename)
        If bDeleteReportsWithNoFile AndAlso Not GetActualReportNamesFromDisk().Contains(reportNameToAdd) Then
            bRemoveReport = True
        End If

        If CurrentUser.IsTheAICAdmin Then
            Dim reportNode As TreeListNode = treeReports.AppendNode(report, parentNode, NodeType.Report)
            reportNode.SetValue("colName", report.ReportDescription)
            reportNode.SetValue("colObj", report)
            reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)
            'reportNode.SetValue("colID", report.Oid)
            'reportNode.SetValue("colIsFacility", False)
            'reportNode.SetValue("colIsCategory", False)
            If bRemoveReport Then
                reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Delete)
            End If
            Return
        ElseIf report?.ReportRoles.Count > 0 Then
            For Each reportRole As DOFacilityReportRole In report.ReportRoles
                If reportRole.Enabled AndAlso (CurrentUserReportRole.Oid = reportRole.UserRole.Oid) Then
                    Dim reportNode As TreeListNode = treeReports.AppendNode(report, parentNode, NodeType.Report)
                    reportNode.SetValue("colName", report.ReportDescription)
                    reportNode.SetValue("colObj", report)
                    reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)

                    If bRemoveReport Then
                        reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Delete)
                    End If
                    Return
                End If
            Next
        Else
            For Each categoryRole As DOFacilityReportCategoryRole In report?.ReportCategory?.CategoryRoles
                If categoryRole.Enabled AndAlso (CurrentUserReportRole.Oid = categoryRole.UserRole.Oid) Then
                    Dim reportNode As TreeListNode = treeReports.AppendNode(report, parentNode, NodeType.Report)
                    reportNode.SetValue("colName", report.ReportDescription)
                    reportNode.SetValue("colObj", report)
                    reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)

                    If bRemoveReport Then
                        reportNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Delete)
                    End If
                    Return
                End If
            Next
        End If
    End Sub

    Private Function GetFacilityNode(ByVal node As TreeListNode) As TreeListNode
        If node?.Tag?.Equals(NodeType.Facility) Then
            Return node
        ElseIf Not node?.ParentNode Is Nothing Then
            Return GetFacilityNode(node.ParentNode)
        Else
            Return Nothing
        End If
    End Function

    Private Function IsNodeDraggable(ByVal node As TreeListNode) As Boolean
        ' A node is draggable if it's not a facility, fixed category or new report category
        Select Case node?.Tag
            Case NodeType.Facility
                Return False
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node.GetValue("colObj")
                Return Not IsFixedCategory(category) 'TODO admin can drag the category node
            Case NodeType.Report
                'All reports should be draggable 
                Return True
            Case Else
                Return False
        End Select
        Return False
    End Function

    Private Function IsNodeCopyable(ByVal node As TreeListNode) As Boolean
        ' A node is copyable if it's not a facility, fixed category or new report category, or if the user is using admin functionality
        If node Is Nothing Then Return False
        Select Case node.Tag
            Case NodeType.Facility
                Return False
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node.GetValue("colObj")
                Return Not IsFixedCategory(category) 'TODO admin can copy the node
            Case NodeType.Report
                Return True
            Case Else
                Return False
        End Select
    End Function

    Private Function IsNodeMovable(ByVal node As TreeListNode) As Boolean
        ' A node is movable if:
        ' 1) It's not a facility, fixed category or new report category
        ' 2) It's ancestors are not fixed categories or new report categories
        ' 3) The user is using admin functionality

        If node Is Nothing Then Return False
        Select Case node.Tag
            Case NodeType.Facility
                Return False
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node.GetValue("colObj")
                Return Not IsFixedCategory(category)  'TODO admin can move the node
            Case NodeType.Report
                Dim category As DOFacilityReportCategory = node.ParentNode?.GetValue("colObj")
                If IsFixedCategory(category) Then
                    Return False
                Else
                    Return CurrentUser.IsTheAICAdmin
                End If
            Case Else
                Return False
        End Select
    End Function

    Private Function IsEditable(ByVal node As TreeListNode) As Boolean
        Select Case node?.Tag
            Case NodeType.Facility
                Return False
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node.GetValue("colObj")
                Return Not IsFixedCategory(category) 'TODO admin can edit node
            Case NodeType.Report
                Return True
            Case Else
                Return False
        End Select
    End Function

    Private Function CanDropIntoNode(ByVal node As TreeListNode) As Boolean
        ' A node can be copied into if:
        ' 1) It's not a facility, fixed category or new report category
        ' 2) It's ancestors are not fixed categories or new report categories
        ' 3) The user is using admin functionality

        If node Is Nothing Then Return False
        Select Case node.Tag
            Case NodeType.Facility
                Return False
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node.GetValue("colObj")
                Return Not IsFixedCategory(category) 'TODO admin can perform this action
            Case NodeType.Report
                Dim category As DOFacilityReportCategory = node.ParentNode?.GetValue("colObj")
                If IsFixedCategory(category) Then
                    Return CurrentUser.IsTheAICAdmin
                Else
                    Return True
                End If
            Case Else
                Return False
        End Select
    End Function

    Private Function IsFixedCategory(ByVal category As DOFacilityReportCategory) As Boolean
        Return category?.CategoryType?.ToUpper.Equals("FIXED") OrElse category?.CategoryType?.ToUpper.Equals("NEWREPORTS")
    End Function

    Private Sub PopulateTreatmentAreas(ByVal facility As DOFacility)
        ddlTreatmentArea.Properties.Items.Clear()
        ddlTreatmentArea.Properties.Items.Add("All")

        If Not facility Is Nothing Then
            Dim treatment_areas As StringCollection = Utils.GetTreatmentAreasAsListByFacility(facility)
            For Each ta As String In treatment_areas
                ddlTreatmentArea.Properties.Items.Add(ta)
            Next
        End If
        ddlTreatmentArea.SelectedIndex = 0

    End Sub

#End Region

#Region "Reports"
    Private Sub EditReports()
        Editing = Not Editing

        If Editing Then
            'If IsEditLocked() Then
            If DOFacilityReportLock.GetLockCount(DOFacilityReportLock.ReportLockType.AdminReports) > 0 Then
                Dim name As String = DOFacilityReportLock.GetLockUser(DOFacilityReportLock.ReportLockType.AdminReports)

                MessageBox.Show("Editing Is currently locked by " & name & ".", "Editing Locked", MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                SetLockTooltips(name)

                Editing = False
            Else
                'RefreshReports()
                ClearLockTooltips()
                btnEdit.Image = My.Resources.cancel_32x32
                btnEdit.ToolTip = "Cancel Editing"
                btnSave.Enabled = True
                btnDeleteReportsWithNoFile.Visible = True

                Dim settings As FacilityReportSettings = GetFacilityNode(treeReports.FocusedNode)?.GetValue("colFacilitySettings")
                Me.ReportLock = SetEditLock(Me.ReportLock, settings)

                seconds_til_timeout = settings.EditTimeoutSeconds 'edit_timeout_seconds
                timerEditTimeout.Enabled = True
                timerUpdateLock.Enabled = True
            End If
        Else
            lblTimeoutWarning.Visible = False
            timerEditTimeout.Enabled = False
            timerUpdateLock.Enabled = False

            btnEdit.Image = My.Resources.edit_32x32
            btnEdit.ToolTip = "Edit Reports"
            Me.ReportLock = ReleaseEditLock(Me.ReportLock)
            PopulateNodes()
            timerEditTimeout.Enabled = False
            timerUpdateLock.Enabled = False

            'changes_made = False 'lblTest.Text = "Save Complete!"
            'recent_changes_made = False
            PendingChanges = False
            Editing = False
        End If

        btnSave.Visible = Editing
        If CurrentUser.IsTheAICAdmin Then
            btnDeleteReportsWithNoFile.Visible = Editing
        Else
            btnDeleteReportsWithNoFile.Visible = False
        End If
    End Sub

    Private Sub MarkForUpdate(ByVal node As TreeListNode)
        node.SetValue(COL_UPDATE_ACTION, UpdateAction.Update)
        node.SetValue("colName", String.Format("{0} *", node.GetValue("colName")?.replace("*", "")))
        PendingChanges = True
        btnSave.Enabled = True
    End Sub

    Private Sub MarkForDelete(ByVal node As TreeListNode)
        node.SetValue(COL_UPDATE_ACTION, UpdateAction.Delete)
        PendingChanges = True
        btnSave.Enabled = True
    End Sub

    Private Sub UpdateRunReportButton(ByVal node As TreeListNode)
        Dim enabled As Boolean = False
        Select Case node?.Tag
            Case NodeType.Facility
                enabled = False
            Case NodeType.Category
                enabled = False
            Case NodeType.Report
                enabled = True
        End Select
        RunReport_btn.Enabled = enabled
    End Sub

    Private Function SaveChanges() As Boolean
        Try
            For Each childNode As TreeListNode In treeReports.Nodes
                ApplyNodeChanges(childNode)
            Next

            Return True
        Catch ex As Exception
            MessageBox.Show(ex.ToString)
            Return False
        End Try

    End Function

    Private Sub ApplyNodeChanges(ByVal node As TreeListNode)
        Dim siteAdminRole As DOUserRolesList = GetRole(CurrentFacility, "Site Admin")

        Select Case node?.Tag
            Case NodeType.Facility
                For Each childNode As TreeListNode In node.Nodes
                    ApplyNodeChanges(childNode)
                Next
            Case NodeType.Category
                Dim category As DOFacilityReportCategory = node("colObj")

                If node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Update Then
                    For Each childNode As TreeListNode In node.Nodes
                        ApplyNodeChanges(childNode)
                    Next
                    category.CategoryOrder = treeReports.GetNodeIndex(node)
                    category.Save()

                    AddCategoryRoles(category, siteAdminRole)

                    ' If the current user doesn't have a report role, they're the MIC admin user
                    If Not (CurrentUserReportRole?.Oid = siteAdminRole.Oid) Then
                        AddCategoryRoles(category, CurrentUserReportRole)
                    End If

                ElseIf node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Delete Then
                    node.Visible = False
                    category.Delete()
                End If

                If node.HasChildren Then
                    For Each childNode As TreeListNode In node.Nodes
                        ApplyNodeChanges(childNode)
                    Next
                End If

            Case NodeType.Report
                Dim report As DOFacilityReport = node("colObj")
                Dim category As DOFacilityReportCategory = node.ParentNode("colObj")

                If node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Update Then
                    If Not report.ReportCategory.Equals(category) Then
                        report.ReportCategory = category
                        category.FacilityReports.Add(report)
                    End If

                    report.ReportListOrder = treeReports.GetNodeIndex(node)
                    report.ReportType.Save()
                    report.Save()

                    AddReportRoles(report, siteAdminRole)

                    ' If the current user doesn't have a report role, they're the MIC admin user
                    If Not (CurrentUserReportRole?.Oid = siteAdminRole.Oid) Then
                        AddReportRoles(report, CurrentUserReportRole)
                    End If

                ElseIf node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Delete Then
                    node.Visible = False
                    report.Delete()
                End If
        End Select
        node.SetValue("colName", node.GetValue("colName")?.replace("*", ""))
        node.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)
    End Sub

    Private Sub AddCategoryRoles(ByVal category As DOFacilityReportCategory, ByVal role As DOUserRolesList)
        ' Check for a CategoryRole for the current user's role and the Site Admin role, and create or enable as needed
        Dim catRoleFound As Boolean = False

        For Each catRole As DOFacilityReportCategoryRole In DOFacilityReportCategoryRole.GetByUserRole(role)
            If category.Oid = catRole.Category.Oid Then
                ' Enable It!
                catRole.Enabled = True
                catRole.Save()
                Exit For
            End If
        Next

        If Not catRoleFound Then
            ' Create One!
            Dim newCatRole As New DOFacilityReportCategoryRole()
            newCatRole.Category = category
            newCatRole.UserRole = role
            newCatRole.Enabled = True
            newCatRole.Save()
        End If
    End Sub

    Private Sub AddReportRoles(ByVal report As DOFacilityReport, ByVal role As DOUserRolesList)
        ' Check for a ReportRole for the current user's role and the Site Admin role, and create or enable as needed
        Dim rptRoleFound As Boolean = False

        For Each rptRole As DOFacilityReportRole In DOFacilityReportRole.GetByUserRole(role)
            If report.Oid = rptRole.Report.Oid Then
                ' Enable It!
                rptRole.Enabled = True
                rptRole.Save()
                Exit For
            End If
        Next

        If Not rptRoleFound Then
            ' Create One!
            Dim newRptRole As New DOFacilityReportRole()
            newRptRole.Report = report
            newRptRole.UserRole = role
            newRptRole.Enabled = True
            newRptRole.Save()
        End If
    End Sub
#End Region

#Region "Lock"

    Private Function SetEditLock(reportLock As DOFacilityReportLock, settings As FacilityReportSettings) As DOFacilityReportLock
        If reportLock Is Nothing Then
            reportLock = New DOFacilityReportLock With {
                .LockingUser = CurrentUser.Oid,
                .LockType = If(CurrentUser.IsTheAICAdmin, DOFacilityReportLock.ReportLockType.AdminReports, DOFacilityReportLock.ReportLockType.UserRoles)
            }
        End If
        reportLock.IsLocked = True
        reportLock.LockCreated = Now
        reportLock.LockExpires = Now.AddSeconds(settings?.EditTimeoutSeconds)
        reportLock.Save()
        Return reportLock
    End Function

    Private Function ReleaseEditLock(reportLock As DOFacilityReportLock) As DOFacilityReportLock
        If Not reportLock Is Nothing Then
            reportLock.Delete()
            reportLock = Nothing
        End If
        Return Nothing
    End Function

    Private Function UpdateLock(reportLock As DOFacilityReportLock, additionalSeconds As Integer) As DOFacilityReportLock
        reportLock.LockExpires = Now.AddSeconds(additionalSeconds)
        reportLock.Save()
        Return reportLock
    End Function

    Private Sub ClearExpiredLocks()
        For Each lock As DOFacilityReportLock In DOFacilityReportLock.GetLocks()
            If lock.LockExpires < Now Then
                lock.IsLocked = False
                lock.Save()
            End If
        Next
    End Sub

    Private Sub ClearLockTooltips()
        btnEdit.ToolTip = "Edit Reports"
    End Sub

    Private Sub SetLockTooltips(ByVal name As String)
        btnEdit.ToolTip = "Editing  Is currently locked by " & name
    End Sub

#End Region

    Private Sub ReportStartup_FormClosing(ByVal sender As Object, ByVal e As FormClosingEventArgs) Handles MyBase.FormClosing
        If PendingChanges And (Not is_forced_logoff) Then
            Dim result As MsgBoxResult = MessageBox.Show("Changes have been made To the report list." & ControlChars.NewLine & ControlChars.NewLine & "Do you want To save the changes", "AIC Reporting", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning)

            Select Case result
                Case MsgBoxResult.Yes
                    SaveChanges()
                Case MsgBoxResult.Cancel
                    e.Cancel = True
                Case MsgBoxResult.No
            End Select
        End If
        ReleaseEditLock(Me.ReportLock)
    End Sub

    Private Sub btnSave_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSave.Click
        If SaveChanges() Then
            lblTimeoutWarning.Visible = False
            timerEditTimeout.Enabled = False
            timerUpdateLock.Enabled = False

            btnEdit.ToolTip = "Edit Reports"
            btnEdit.Image = My.Resources.edit_32x32
            ReleaseEditLock(Me.ReportLock)
            timerEditTimeout.Enabled = False
            timerUpdateLock.Enabled = False

            PendingChanges = False
            Editing = False
            btnSave.Visible = False
            btnDeleteReportsWithNoFile.Visible = False
        End If

        'jjc
        PopulateNodes()
    End Sub


    Private Sub treeReports_CustomDrawNodeCell(ByVal sender As Object, ByVal e As CustomDrawNodeCellEventArgs) Handles treeReports.CustomDrawNodeCell
        Try
            ' Make things pretty
            Dim my_tree As TreeList = CType(sender, TreeList)

            Dim is_same As Boolean = e.Node.Equals(my_tree.FocusedNode)

            Dim treeFont As Font = treeReports.Font
            Dim treeBrush As Brush = SystemBrushes.WindowText

            Select Case e.Node.Tag
                Case NodeType.Facility
                    treeBrush = Brushes.Green
                    treeFont = New Font(treeReports.Font, FontStyle.Bold)
                Case NodeType.Category
                    If e.Node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Delete Then
                        treeFont = New Font(treeReports.Font, FontStyle.Bold Or FontStyle.Strikeout)
                        treeBrush = Brushes.LightGray
                    Else
                        treeFont = New Font(treeReports.Font, FontStyle.Bold)
                    End If

                    Dim category As DOFacilityReportCategory = e.Node.GetValue("colObj")
                    If category?.CategoryType?.ToUpper.Equals("NEWREPORTS") Then
                        If e.Node.HasChildren Then
                            treeBrush = Brushes.DarkRed
                        End If
                    ElseIf category?.CategoryType?.ToUpper.Equals("USER") Then
                        treeBrush = Brushes.Black
                    Else
                        treeBrush = Brushes.Blue
                    End If
                Case NodeType.Report
                    If e.Node.GetValue(COL_UPDATE_ACTION) = UpdateAction.Delete Then
                        treeFont = New Font(treeReports.Font, FontStyle.Regular Or FontStyle.Strikeout)
                        treeBrush = Brushes.LightGray
                    Else
                        treeFont = New Font(treeReports.Font, FontStyle.Regular)
                    End If
                Case Else

            End Select


            ' Redraw the box when selected to fit the text and not the width of the TreeList
            Dim rect As Rectangle = New Rectangle(e.EditViewInfo.ContentRect.Left, e.EditViewInfo.ContentRect.Top, Convert.ToInt32(e.Graphics.MeasureString(e.CellText, treeFont).Width + 1), Convert.ToInt32(e.Graphics.MeasureString(e.CellText, treeFont).Height))

            If is_same Then
                e.Graphics.FillRectangle(SystemBrushes.Window, e.Bounds)
                e.Graphics.FillRectangle(SystemBrushes.Highlight, rect)
                e.Graphics.DrawString(e.CellText, treeFont, SystemBrushes.HighlightText, rect)
                e.Handled = True
            Else
                e.Graphics.DrawString(e.CellText, treeFont, treeBrush, rect)
                e.Handled = True
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub treeReports_FocusedNodeChanged(ByVal sender As Object, ByVal e As FocusedNodeChangedEventArgs) Handles treeReports.FocusedNodeChanged
        Select Case e?.Node?.Tag
            Case NodeType.Report
                Dim report As DOFacilityReport = CType(e.Node("colObj"), DOFacilityReport)

        End Select

        Dim facNode As TreeListNode = GetFacilityNode(e.Node)
        If Not facNode Is Nothing Then PopulateTreatmentAreas(CType(facNode.GetValue("colObj"), DOFacility))
        UpdateRunReportButton(e.Node)
    End Sub

    Private Sub treeReports_MouseDown(ByVal sender As Object, ByVal e As MouseEventArgs) Handles treeReports.MouseDown
        Dim tl As TreeList = CType(sender, TreeList)
        Dim p As Point = New Point(e.X, e.Y)
        Dim selectedNode As TreeListNode = tl.CalcHitInfo(p).Node ' Set the node that's clicked on as context_node

        If Not Editing Then
            'tl.OptionsBehavior.DragNodes = False
            tl.OptionsDragAndDrop.DragNodesMode = DragNodesMode.None
            DisableContext()
            Return
        End If


        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            ' Can't drag a facility, fixed category or new reports category
            tl.OptionsDragAndDrop.DragNodesMode = IsNodeDraggable(selectedNode)
            Return
        End If

        If e.Button = System.Windows.Forms.MouseButtons.Right Then

            ' Setup context menu info
            DisableContext() ' Start from scratch

            Dim move_index As Integer = tl.GetNodeIndex(selectedNode)


            If Not selectedNode Is Nothing Then
                tl.SetFocusedNode(selectedNode) ' Select it

                If selectedNode.Tag = NodeType.Facility Then ' If a facility has been clicked
                    ' Can only create a new User Category beneath a facility
                    cmenuTreeReports.Items("cmitemNewCategory").Enabled = True
                ElseIf selectedNode.Tag = NodeType.Category AndAlso selectedNode.ParentNode?.Tag = NodeType.Facility Then
                    '@Question - who else would be editing this node if it is not an admin?
                    If IsEditable(selectedNode) Or CurrentUser.IsTheAICAdmin Then ' If an editable category has been clicked or user is admin

                        cmenuTreeReports.Items("cmitemNewReport").Enabled = True ' Can only create a new report in a User Category
                        cmenuTreeReports.Items("cmitemNewCategory").Enabled = True ' Can only add a new category on a category (facility)
                        cmenuTreeReports.Items("cmitemDeleteItem").Enabled = True
                        cmenuTreeReports.Items("cmitemEditItem").Enabled = True


                        If Not CopiedReport Is Nothing Then
                            cmenuTreeReports.Items("cmitemPasteReport").Enabled = True ' Can only paste if a report is copied
                        End If

                    End If

                Else ' If a report or a subcategory has been clicked
                    'Dim move_index As Integer = tl.GetNodeIndex(selectedNode)
                    Dim max_move_index As Integer = selectedNode.ParentNode.Nodes.Count - 1

                    ' Check the report's parent's editablity
                    If IsEditable(selectedNode.ParentNode) Or CurrentUser.IsTheAICAdmin Then ' If an editable report has been clicked
                        cmenuTreeReports.Items("cmitemNewReport").Enabled = True ' Can only create a new report in a User Category
                        cmenuTreeReports.Items("cmitemDeleteItem").Enabled = True
                        cmenuTreeReports.Items("cmitemEditItem").Enabled = True

                        If Not CopiedReport Is Nothing Then
                            cmenuTreeReports.Items("cmitemPasteReport").Enabled = True ' Can only paste if a report is copied
                        End If
                    End If

                    If IsEditable(selectedNode.ParentNode) Then
                        cmenuTreeReports.Items("cmitemMoveUp").Enabled = (move_index > 0)
                        cmenuTreeReports.Items("cmitemMoveDown").Enabled = (move_index < max_move_index)
                    Else
                        cmenuTreeReports.Items("cmitemMoveUp").Enabled = False
                        cmenuTreeReports.Items("cmitemMoveDown").Enabled = False
                    End If

                    cmenuTreeReports.Items("cmitemCopyReport").Enabled = True ' Can only copy reports
                End If

                If Not CurrentUser.IsTheAICAdmin Then
                    cmenuTreeReports.Items("cmitemEditItem").Visible = False
                    cmenuTreeReports.Items("cmitemNewReport").Visible = False
                End If

                If selectedNode.GetValue(COL_UPDATE_ACTION) = UpdateAction.Delete Then
                    cmenuTreeReports.Items("cmItemUndoDelete").Visible = True
                    cmenuTreeReports.Items("cmItemUndoDelete").Enabled = True
                Else
                    cmenuTreeReports.Items("cmItemUndoDelete").Visible = False
                    cmenuTreeReports.Items("cmItemUndoDelete").Enabled = False
                End If

            End If
        End If

    End Sub

    Private Sub treeReports_DragOver(ByVal sender As Object, ByVal e As DragEventArgs) Handles treeReports.DragOver
        If Not Editing Then
            e.Effect = DragDropEffects.None
            Return
        End If

        'Drag around the selected item
        Dim tl As TreeList = CType(sender, TreeList)
        Dim p As Point = tl.PointToClient(New Point(e.X, e.Y))
        Dim dragNode As TreeListNode = e.Data.GetData(GetType(TreeListNode))
        Dim targetNode As TreeListNode = tl.CalcHitInfo(p).Node

        If CanDropIntoNode(targetNode) Then
            If IsNodeMovable(dragNode) Then
                e.Effect = DragDropEffects.Move ' Set the "cursor" to move
            ElseIf IsNodeCopyable(dragNode) Then
                e.Effect = DragDropEffects.Copy
            Else
                e.Effect = DragDropEffects.None
            End If
        Else
            e.Effect = DragDropEffects.None
        End If
    End Sub

    Private Sub treeReports_DragDrop(ByVal sender As Object, ByVal e As DragEventArgs) Handles treeReports.DragDrop
        Dim tl As TreeList = CType(sender, TreeList)
        Dim p As Point = tl.PointToClient(New Point(e.X, e.Y))

        Dim targetNode As TreeListNode = tl.CalcHitInfo(p).Node
        Dim sourceNode As TreeListNode = e.Data.GetData(GetType(TreeListNode))
        Dim newNode As TreeListNode = Nothing
        Select Case targetNode?.Tag
            Case NodeType.Facility

            Case NodeType.Category
                If CanDropIntoNode(targetNode) AndAlso sourceNode?.Tag = NodeType.Report Then
                    If e.Effect = DragDropEffects.Copy Then
                        newNode = CopyReportNode(tl, targetNode, sourceNode)
                    Else
                        tl.MoveNode(sourceNode, targetNode)
                        MarkForUpdate(sourceNode)
                        MarkForUpdate(targetNode)
                    End If
                End If
            Case NodeType.Report
                newNode = Nothing
                If CanDropIntoNode(targetNode) AndAlso sourceNode?.Tag = NodeType.Report Then
                    If e.Effect = DragDropEffects.Copy Then
                        'newNode = tl.CopyNode(sourceNode, targetNode.ParentNode, False)
                        newNode = CopyReportNode(tl, targetNode.ParentNode, sourceNode)
                        tl.SetNodeIndex(newNode, tl.GetNodeIndex(targetNode))
                    Else
                        tl.MoveNode(sourceNode, targetNode.ParentNode)
                        tl.SetNodeIndex(sourceNode, tl.GetNodeIndex(targetNode))
                    End If

                    MarkForUpdate(sourceNode)
                    If newNode IsNot Nothing Then
                        MarkForUpdate(newNode)
                    End If
                End If
            Case Else
        End Select
        e.Effect = DragDropEffects.None
    End Sub

    Private Function CopyReportNode(tl As TreeList, targetNode As TreeListNode, sourceNode As TreeListNode) As TreeListNode
        Dim newNode As TreeListNode = tl.AppendNode(sourceNode, targetNode, NodeType.Report)
        newNode.SetValue("colName", sourceNode("colName"))
        newNode.SetValue("colID", sourceNode("colID"))
        Dim oldreport As DOFacilityReport = sourceNode("colObj")
        Dim newReport As New DOFacilityReport() With
            {
            .ReportCategory = oldreport.ReportCategory,
            .ReportDescription = oldreport.ReportDescription,
            .ReportFilename = oldreport.ReportFilename,
            .ReportType = oldreport.ReportType
        }

        newNode("colObj") = newReport
        'We really should mark other stuff so the positions will be retained.
        MarkForUpdate(targetNode)
        MarkForUpdate(newNode)
        Return newNode
    End Function

    Private Sub treeReports_KeyDown(ByVal sender As Object, ByVal e As KeyEventArgs) Handles treeReports.KeyDown
        If Editing Then
            Dim focusedNode As TreeListNode = treeReports.FocusedNode()
            If IsNodeMovable(focusedNode) Then
                If e.KeyCode = Keys.Delete Then
                    Console.WriteLine("Fired")
                    If focusedNode.Tag = NodeType.Category Then
                        ' Prompt user if trying to delete a category since it will delete all underlying reports
                        Dim result As MsgBoxResult = MessageBox.Show("Deleting this category will delete all reports within it including hidden reports. Are you sure you want To Do this?", "Confirm Category Remove", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                        If result = MsgBoxResult.No Then
                            Return
                        End If
                    Else
                        Dim result As MsgBoxResult = MessageBox.Show("Are you sure you want To delete this report?", "Confirm Report Remove", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                        If result = MsgBoxResult.No Then
                            Return
                        End If
                    End If

                    MarkForDelete(focusedNode)
                    MarkForUpdate(focusedNode.ParentNode)
                End If
            End If
        End If
    End Sub

#Region "Right-Click Menu"

    Private Sub cmitemEditItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemEditItem.Click
        If treeReports.FocusedNode?.Tag = NodeType.Category Then 'If it's a category, open the category dialog (editing) with the category name
            Dim category As DOFacilityReportCategory = treeReports.FocusedNode("colObj")
            Dim categoryForm As New FormNewCategory(category)
            AddHandler categoryForm.CategoryApplyChanges, AddressOf EditCategory
            categoryForm.ShowDialog()
        Else ' Or open the report dialog (editing) with report information
            Dim report As DOFacilityReport = treeReports?.FocusedNode("colObj")
            If Not report Is Nothing Then
                Dim isExport = False
                Dim isPhysician = False
                Dim isObservation = False
                Dim isInfusion = False
                Dim formReport As New FormNewReport(report)
                AddHandler formReport.ReportApplyChanges, AddressOf EditReport
                formReport.ShowDialog()
            End If

        End If
    End Sub

    Private Sub cmitemNewReport_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemNewReport.Click
        If treeReports.FocusedNode?.Tag = NodeType.Category Then
            Dim category As DOFacilityReportCategory = CType(treeReports.FocusedNode("colObj"), DOFacilityReportCategory)
            Dim newReport As New DOFacilityReport With {
                .ReportCategory = category,
                .ReportType = New DOFacilityReportType()
            }
            Dim formReport As New FormNewReport(newReport)
            AddHandler formReport.ReportApplyChanges, AddressOf AddReport
            formReport.ShowDialog()
        End If
    End Sub

    Private Sub cmitemMoveUp_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemMoveUp.Click
        Dim fn As TreeListNode = treeReports.FocusedNode()
        If Not fn Is Nothing Then
            MarkForUpdate(fn)
            MarkForUpdate(fn.ParentNode) 'Mark the Category Node for Update

            ' Mark the category's other nodes for update since one report's movement in a category affects the position of the rest
            For Each childNode As TreeListNode In fn.ParentNode.Nodes
                MarkForUpdate(childNode)
            Next

            treeReports.SetNodeIndex(fn, treeReports.GetNodeIndex(fn) - 1)
        End If
    End Sub

    Private Sub cmitemMoveDown_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemMoveDown.Click
        Dim fn As TreeListNode = treeReports.FocusedNode()
        If Not fn Is Nothing Then
            MarkForUpdate(fn)
            MarkForUpdate(fn.ParentNode) 'Mark the Category Node for Update

            ' Mark the category's other nodes for update since one report's movement in a category affects the position of the rest
            For Each childNode As TreeListNode In fn.ParentNode.Nodes
                MarkForUpdate(childNode)
            Next

            treeReports.SetNodeIndex(fn, treeReports.GetNodeIndex(fn) + 1)
        End If

    End Sub

    Private Sub cmitemNewCategory_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemNewCategory.Click
        ' Figure out what the new category is being added to (Facility or Category)
        Dim parentCategory As DOFacilityReportCategory
        If treeReports.FocusedNode?.Tag = NodeType.Category Then
            parentCategory = CType(treeReports.FocusedNode("colObj"), DOFacilityReportCategory)
        Else
            parentCategory = Nothing
        End If

        ' Show the new category dialog (adding)
        Dim newCategory As New DOFacilityReportCategory With {
            .CategoryName = "New Category",
            .CategoryType = "USER",
            .Facility = CurrentFacility,
            .ParentCategory = parentCategory
        }
        Dim categoryForm As New FormNewCategory(newCategory)
        AddHandler categoryForm.CategoryApplyChanges, AddressOf AddCategory
        categoryForm.ShowDialog()
    End Sub

    Private Sub cmitemCopyReport_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemCopyReport.Click
        'EditNode = treeReports.FocusedNode.Clone

        Dim selectedReport As DOFacilityReport = treeReports.FocusedNode("colObj")
        CopiedReport = New DOFacilityReport

        CopiedReport.ReportCategory = selectedReport.ReportCategory
        CopiedReport.ReportDescription = selectedReport.ReportDescription
        CopiedReport.ReportFilename = selectedReport.ReportFilename
        CopiedReport.ReportListOrder = selectedReport.ReportListOrder
        CopiedReport.ReportType = selectedReport.ReportType

        For Each rr As DOFacilityReportRole In selectedReport.ReportRoles
            CopiedReport.ReportRoles.Add(rr)
        Next

        'EditNode.SetValue("colName", CopiedReport.ReportDescription)
        'EditNode.SetValue("colObj", CopiedReport)
        'EditNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Ignore)
    End Sub

    Private Sub cmitemPasteReport_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemPasteReport.Click
        ' Paste a copy of the copy node above the currently selected node (context node) 
        ' Would like to make it so reports can be pasted on categories

        Dim AddedNode As TreeListNode
        If treeReports.FocusedNode?.Tag = NodeType.Category Then
            AddedNode = treeReports.AppendNode(CopiedReport, treeReports.FocusedNode, NodeType.Report)
            AddedNode.SetValue("colName", CopiedReport.ReportDescription)
            AddedNode.SetValue("colObj", CopiedReport)
            AddedNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Update)
        Else
            AddedNode = treeReports.AppendNode(CopiedReport, treeReports.FocusedNode?.ParentNode, NodeType.Report)
            AddedNode.SetValue("colName", CopiedReport.ReportDescription)
            AddedNode.SetValue("colObj", CopiedReport)
            treeReports.SetNodeIndex(AddedNode, treeReports.GetNodeIndex(treeReports.FocusedNode))
            AddedNode.SetValue(COL_UPDATE_ACTION, UpdateAction.Update)

        End If

        MarkForUpdate(AddedNode) 'Mark the Node for Update
        MarkForUpdate(AddedNode?.ParentNode) 'Mark the Category Node for Update
        'treeReports.SetFocusedNode(AddedNode)

        'EditNode = Nothing
        CopiedReport = Nothing
    End Sub

    Private Sub cmitemDeleteReport_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmitemDeleteItem.Click
        ' Delete the node from the TreeList
        Dim focusedNode As TreeListNode = treeReports.FocusedNode
        If focusedNode Is Nothing Then Return

        Select Case focusedNode.Tag
            Case NodeType.Facility
            Case NodeType.Category
                ' Prompt user if trying to delete a category since it will delete all underlying reports
                Dim result As MsgBoxResult = MsgBox("Deleting this category will delete all reports within it including hidden reports. Are you sure you want To Do this?", MsgBoxStyle.YesNo)
                If result = MsgBoxResult.No Then
                    Return
                End If

                For Each childNode As TreeListNode In focusedNode.Nodes
                    MarkForDelete(childNode)
                Next
            Case NodeType.Report
                MarkForUpdate(focusedNode.ParentNode)
        End Select
        MarkForDelete(focusedNode)
    End Sub

    Private Sub cmItemUndoDelete_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmItemUndoDelete.Click
        Dim focusedNode As TreeListNode = treeReports.FocusedNode
        Select Case focusedNode?.Tag
            Case NodeType.Facility
            Case NodeType.Category
                focusedNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)
                For Each childNode As TreeListNode In focusedNode.Nodes
                    childNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)
                Next
            Case NodeType.Report
                focusedNode.SetValue(COL_UPDATE_ACTION, UpdateAction.NoUpdate)
        End Select
    End Sub

    Private Sub EditReport(ByVal report As DOFacilityReport)
        Dim reportNode = treeReports?.FocusedNode
        reportNode.SetValue("colName", report.ReportDescription)
        'reportNode.SetValue("colID", report.Oid)
        reportNode.SetValue("colObj", report)
        MarkForUpdate(reportNode)
    End Sub

    Private Sub AddReport(ByVal report As DOFacilityReport)
        If treeReports.FocusedNode?.Tag = NodeType.Category Then
            Dim category As DOFacilityReportCategory = CType(treeReports.FocusedNode("colObj"), DOFacilityReportCategory)
            treeReports.FocusedNode.SetValue("colName", If(category.CategoryType.ToUpper.Equals("NEWREPORTS"), String.Format("{0} ({1})", category.CategoryName, category.FacilityReports.Count + 1), category.CategoryName))
            Dim reportNode As TreeListNode = treeReports.AppendNode(report, treeReports.FocusedNode, NodeType.Report)
            reportNode.SetValue("colName", report.ReportDescription)
            'reportNode.SetValue("colID", report.Oid)
            reportNode.SetValue("colObj", report)
            MarkForUpdate(reportNode)
            treeReports.FocusedNode.ExpandAll()
        End If
    End Sub

    Private Sub AddCategory(ByVal category As DOFacilityReportCategory)
        Select Case treeReports.FocusedNode?.Tag
            Case NodeType.Facility
                Dim categoryNode As TreeListNode = treeReports.AppendNode(category, treeReports.FocusedNode, NodeType.Category)
                categoryNode.SetValue("colName", category.CategoryName)
                'categoryNode.SetValue("colID", category.Oid)
                categoryNode.SetValue("colObj", category)
                MarkForUpdate(categoryNode)
            Case NodeType.Category
                Dim categoryNode As TreeListNode = treeReports.AppendNode(category, treeReports.FocusedNode, NodeType.Category)
                categoryNode.SetValue("colName", category.CategoryName)
                'categoryNode.SetValue("colID", category.Oid)
                categoryNode.SetValue("colObj", category)
                MarkForUpdate(categoryNode)
            Case NodeType.Report
        End Select
    End Sub

    Private Sub EditCategory(ByVal category As DOFacilityReportCategory)
        Dim categoryNode As TreeListNode = treeReports.FocusedNode
        categoryNode.SetValue("colName", category.CategoryName)
        'categoryNode.SetValue("colID", category.Oid)
        categoryNode.SetValue("colObj", category)
        MarkForUpdate(categoryNode)
    End Sub

    Private Sub DisableContext()
        ' Disable all context menu items
        For Each item As ToolStripItem In cmenuTreeReports.Items
            item.Enabled = False
        Next
    End Sub

#End Region


    Private Sub btnEdit_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnEdit.Click
        EditReports()
    End Sub


    Private Sub EditUserRolesToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles EditUserRolesToolStripMenuItem.Click
        Dim facilities As New List(Of DOFacility)
        For Each facility As DOFacility In DOFacility.GetAllFacilitesAsList()
            If (ActiveFacility Is Nothing) OrElse (facility.Oid = ActiveFacility.Oid) Then
                facilities.Add(facility)
            End If
        Next

        If New ReportRolesEditor(facilities).ShowDialog = DialogResult.OK Then
            PopulateNodes()
        End If
    End Sub
    Private Sub ExitToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles ExitToolStripMenuItem.Click
        Me.Close()
    End Sub
    Private Sub XMLImportToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles XMLImportToolStripMenuItem.Click
        Dim xmlImportForm As New FormXMLImport()
        If xmlImportForm.ShowDialog() = DialogResult.OK Then
            XpoDefault.Session.DropIdentityMap() 'force refresh

            PopulateNodes()
        End If
    End Sub

#End Region

    Private Sub timerCheckEditLock_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles timerClearExpiredLocks.Tick
        ClearExpiredLocks()
    End Sub

    Private Sub PictureBox1_DoubleClick(ByVal sender As Object, ByVal e As EventArgs)
        MsgBox("Application version: " & Application.ProductVersion & "   ", MsgBoxStyle.Information, "Application Version")
    End Sub

#Region "Admin Reports Applicaton Idle Timeout"
    Public Sub HandleIdleTimeoutShutdown()
        is_forced_logoff = True
        Me.Close()
    End Sub
    Private Function GetIdleTimeoutMinutes() As Integer
        Dim itm As Integer

        Dim logoff_timeout_settings As New XPCollection(GetType(DOGlobalSetting), CriteriaOperator.Parse("SettingName = 'AR_AutoLogoffTimeout'"))
        If logoff_timeout_settings.Count > 0 Then
            Try
                itm = Integer.Parse(logoff_timeout_settings(0).SettingValue)
            Catch ex As Exception
                itm = 0
            End Try
        Else
            itm = 0
        End If

        Return itm
    End Function

    Private Sub DateType_rdo_SelectedIndexChanged(sender As Object, e As EventArgs)

    End Sub

    Private Sub ReportStartup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'facilityCbo.Text = $"({CurrentFacility.Oid}) {CurrentFacility.LongName}"
    End Sub

    Private bDeleteReportsWithNoFile As Boolean = False
    Private Sub btnDeleteReportsWithNoFile_Click(sender As Object, e As EventArgs) Handles btnDeleteReportsWithNoFile.Click
        Try
            bDeleteReportsWithNoFile = True
            PopulateNodes()
        Catch ex As Exception
        Finally
            bDeleteReportsWithNoFile = False
        End Try
    End Sub

    Private Sub ReportStartup_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        Me.StartPosition = FormStartPosition.CenterScreen

        ' Add any initialization after the InitializeComponent() call.
        Application.DoEvents()
        Dim overlayHandle As IOverlaySplashScreenHandle = Nothing
        Try
            overlayHandle = SplashScreenManager.ShowOverlayForm(Me)
            Me.Cursor = Cursors.WaitCursor
            LoadBrandingImage()

            'CurrentUser = user
            'ActiveFacility = facility

            If CurrentUser Is Nothing Then
                MsgBox("Could not find the specified user.", MsgBoxStyle.Exclamation, "User Not Found")
                Me.Close()
                Exit Sub
            End If

            IsMICAdmin = CurrentUser.Oid = 1

            If IsMICAdmin Then
                AdminToolStripMenuItem.Visible = True
                XMLImportToolStripMenuItem.Visible = True
            Else
                For Each p As DOUserPermission In CurrentUser.Permissions
                    If p.Role.ToUpper = "ADMIN" AndAlso p.Enabled AndAlso p.Facility.Oid = ActiveFacility?.Oid Then
                        AdminToolStripMenuItem.Visible = True
                    End If
                Next

                Dim role_name As String = CurrentUser.GetUserSetting("ReportRole", ActiveFacility)

                If (role_name Is Nothing) OrElse (role_name = "") Then
                    MsgBox("You are not assigned a role to view reports, please contact an administrator to have one assigned.", MsgBoxStyle.OkOnly, "No Report Role Assigned")
                    btnEdit.Enabled = False
                End If

                CurrentUserReportRole = GetRole(ActiveFacility, role_name)
            End If

            Max_dte.EditValue = Today
            Min_dte.EditValue = Date.Today.AddMonths(-1)

            PopulateNodes()
            ClearExpiredLocks()
            facilityCbo.Text = $"({CurrentFacility.Oid}) {CurrentFacility.LongName}"
            Me.Cursor = Cursors.Arrow
        Catch ex As Exception
            MessageBox.Show("Problems Loading Form")
        Finally
            Cursor = Cursors.Default
            SplashScreenManager.CloseOverlayForm(overlayHandle)
        End Try
    End Sub

    'Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
    '    Dim f As New ExcelViewer
    '    f.Show()
    'End Sub

#End Region
End Class

Option Infer On

Imports EnchartDOLib
Imports DevExpress.Xpo
Imports System.IO
Imports System.Threading.Tasks
Imports Microsoft.Win32
Imports System.Linq
Imports System.Net.NetworkInformation
Imports System.Net
Imports System.Text
Imports System.Net.Http
Imports Newtonsoft.Json
Imports System.Reflection


Public Class ECUpdaterForm4
    Private bFirstTime As Boolean = True
    Dim UpdatePath As String = Nothing

    Private Shared Function GetEssUrlSetting() As String
        Dim essUrl As String

        Try
            essUrl = MicSettingsBuilder.Settings.ESSUrl
        Catch ex As Exception
            essUrl = Nothing
        End Try

        If String.IsNullOrEmpty(essUrl) Then
            Dim msgBuilder As New StringBuilder($"There was an error trying to read the configuration file (MicCustomSettings.json).")
            msgBuilder.Append("Verify that it exists in the same dir as EnchartCac.exe and that the contents are correct.")
            msgBuilder.Append($"{vbCrLf + vbCr}")
            msgBuilder.Append("This file is used to store the URL of ESS.")

            Environment.Exit(0) 'do ugly forced shutdown
        End If

        Return essUrl
    End Function

    Public Class BusinessObject
        Public Property Oid As Integer = NEW_OBJ_OID
        Protected Const NEW_OBJ_OID As Integer = 0
    End Class

    Public Class BOGlobalSetting
        Inherits BusinessObject

        Public Property SettingName As String
        Public Property SettingValue As String
    End Class

    Private Sub Form1_Activated(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Activated
        My.Application.DoEvents()
        If Me.bFirstTime Then
            Me.bFirstTime = False
            'msgbox("Form Activated")
            Timer1.Interval = 250
            Timer1.Start()
        End If
    End Sub

    Public Sub msgbox(msg As String)
        ' MessageBox.Show(msg)
    End Sub

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        'Dim version = FileVersionInfo.GetVersionInfo(System.Reflection.Assembly.GetExecutingAssembly().Location).FileVersion
        Text = GetVersionString() ' String.Format("{0} Ver: {1}", My.Application.Info.AssemblyName, version)
        Me.Text = My.Application.Info.AssemblyName & " Ver: " & GetVersionString()
    End Sub

    Private GlobalSettings As Dictionary(Of String, String)

    Private Sub Process()
        Try
            GlobalSettings = GetGlobalSettings()
        Catch ex As Exception
            MessageBox.Show("(ECUpdaterForm) Error connecting to database: " & ex.ToString, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ExitUpdaterAndKillLauncher()
        End Try

        'Await ExitIfInMaintenanceMode()
        'If Await AbortIfRunningOnServerAsync() = True Then
        '    MessageBox.Show($"AbortIfRunningOnServerAsync() = True")
        '    Exit Sub
        'End If

        Try
            Using fs As FileStream = New FileStream(Path.Combine(GetSafeExePath(), "ecupdater.log"), FileMode.Create)
                Using sw As StreamWriter = New StreamWriter(fs)
                    sw.WriteLine(Text)
                    UpdateFiles(sw)
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("(ECUpdaterForm->DoSomeWork) Error : " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Public Function GetGlobalSettings() As Dictionary(Of String, String) 'Implements IRequestReport.RequestReport
        Dim _baseUrl = GetEssUrlSetting()
        Dim suffix = "api/Configuration/GetStartUpGlobalSettings"
        Dim url = $"{_baseUrl}/{suffix}"
        Dim settingsDict As New Dictionary(Of String, String)

        Try
            Dim _httpClient As New HttpClient
            _httpClient.Timeout = TimeSpan.FromSeconds(60) 'JJC 04.10.20 changed from 20 to 60
            Dim aResponse As HttpResponseMessage = _httpClient.GetAsync(url).Result

            If aResponse.StatusCode = System.Net.HttpStatusCode.OK Then
                Try
                    Dim responseContent = aResponse.Content.ReadAsStringAsync().Result

                    Dim returnObj = JsonConvert.DeserializeObject(Of IEnumerable(Of BOGlobalSetting))(responseContent)
                    settingsDict = New Dictionary(Of String, String)
                    For Each setting In returnObj
                        settingsDict(setting.SettingName) = setting.SettingValue
                    Next
                    Return settingsDict
                Catch ex As Exception
                    Throw New Exception($"Error trying to read global settings: {ex.ToString()}")
                End Try
            Else
                aResponse.StatusCode = System.Net.HttpStatusCode.InternalServerError
                Throw New Exception($"Error trying to read global settings.")
            End If

            Return Nothing
        Catch ex As Exception
            Throw New Exception($"Error trying to read global settings: {ex.ToString()}")
        End Try
        Return Nothing
    End Function


    Private Sub UpdateFiles(sw As StreamWriter)
        Dim UpdateDir As DirectoryInfo = GetUpdateDir()
        Dim timer As New Stopwatch
        timer.Start()

        CopyDir(sw, UpdateDir)
        timer.Stop()
        sw.WriteLine($"It took {timer.Elapsed} to sync files.")

        If LauncherNeedsUpdate() Then
            StartLauncherUpdater()
            ExitUpdaterAndKillLauncher()
        End If

        Close() 'exit app ... hmmm
    End Sub
    Private Sub CopyDir(sw As StreamWriter, ByVal sourceDir As DirectoryInfo) ', ByVal pBaseDi As DirectoryInfo)
        Try
            Dim destinationDir As DirectoryInfo = Nothing
            Try
                destinationDir = CalcDestinationDir(sourceDir)
            Catch ex As Exception
                Dim msg = String.Format("(CopyDir) Unexpected Error : {0}", ex.Message)
                MessageBox.Show(msg)
                ExitUpdaterAndKillLauncher()
            End Try

            If destinationDir.Exists = False Then
                destinationDir.Create()
            End If
            Me.msg.Text = "Copying Files..." '& sfile.FullName
            Me.msg.Refresh()

            For Each sourceFile As FileInfo In sourceDir.GetFiles
                'msg.Invoke(Sub() msg.Text = $"CopyDir - Processing file: {sourceFile.Name}")
                If OutOfDate(sw, sourceFile) Then
                    Try
                        If sourceFile.Name = "ECLauncher.exe" Then
                            sourceFile.CopyTo(Path.Combine(destinationDir.FullName, "ECLauncherTemp.exe"), True)
                        ElseIf sourceFile.Name = "ECUpdater4.exe" Then
                            sourceFile.CopyTo(Path.Combine(destinationDir.FullName, "ECUpdater4_update.exe"), True)
                            MessageBox.Show("ECUpdater4_update.exe created")
                        Else
                            sourceFile.CopyTo(Path.Combine(destinationDir.FullName, sourceFile.Name), True)
                        End If
                    Catch
                    End Try
                End If
            Next

            For Each ldir As DirectoryInfo In sourceDir.GetDirectories
                CopyDir(sw, ldir)
            Next
        Catch ex As Exception
            Dim msg = String.Format("Error Copying Directory({0}) : {1}", sourceDir.FullName, ex.Message)
            MessageBox.Show(msg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    Private Shared Sub ExitUpdaterAndKillLauncher()
        Environment.Exit(0)
    End Sub

    Private Sub mmsg(msgText As String)
        'msg.Invoke(Sub()
        '               msg.Text = msgText
        '               Threading.Thread.Sleep(1000)
        '           End Sub)

        ''MessageBox.Show(msgText)

    End Sub

    Private Function GetUpdateDir() As DirectoryInfo
        If GlobalSettings.TryGetValue("UpdatePath", UpdatePath) Then

        End If

        If String.IsNullOrEmpty(UpdatePath) Then
            MessageBox.Show("The update source directory path has Not been set", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ExitUpdaterAndKillLauncher()
        End If

        Dim UpdateDir As New DirectoryInfo(UpdatePath)
        If UpdateDir.Exists = False Then
            MessageBox.Show(String.Format("Invalid Or missing update path.  Please contact McKesson HIC support.", UpdateDir.FullName), "System Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ExitUpdaterAndKillLauncher()
        End If
        Return UpdateDir
    End Function

    Private UpdateFilesOnServerOverride = False
    Private Async Function AbortIfRunningOnServerAsync() As Task(Of Boolean)
        Dim msg = "Server install detected - skipping update process"
        Dim runningOnServer = False

        'We test this first because we wont't it to check and warn if the ESSUrl 
        'doesn't match the FQDM for this machine.
        If ESSAddressMatchesMachinename() Then
            runningOnServer = True
        End If

        If checkInstalled("Allscripts Intelligent Coding Server") = True Then
            runningOnServer = True
            updateAnywayBtn.Visible = True
            updateAnywayBtn.Enabled = True
        End If

        If runningOnServer Then
            Me.msg.Text = msg
            If UpdateFilesOnServerOverride Then
                Await Task.Delay(12000)
                Return False
            End If
            Close()
            Return True
        End If

        Return False
    End Function


    Public Function ESSAddressMatchesMachinename() As Boolean
        Dim baseUrl As String = String.Empty

        Try
            baseUrl = MicSettingsBuilder.Settings.ESSUrl
        Catch ex As Exception
            MessageBox.Show(Me, $"An error was encountered trying to read the ESSUrl setting from the MICCustomSettings.json file. {vbCrLf}{vbCrLf} {ex.Message}{If(ex.InnerException IsNot Nothing, ex.InnerException.Message, "")}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Asterisk)
            ExitUpdaterAndKillLauncher()
        End Try

        If baseUrl IsNot Nothing Then
            'JJC 12.13.2019 Rewrote this to pass fortify complaint about calling Dns.GetHostName() which use to be called
            'from the GetFQDN function.
            Dim hostName = New Uri(baseUrl).DnsSafeHost?.ToUpper()
            If hostName Is Nothing Then Return False

            Dim parts() = hostName.Split(".")
            If parts Is Nothing Then Return False

            Dim machineNameFromConfig = parts(0)
            Dim machineName = Environment.MachineName.ToUpper()

            If machineName = machineNameFromConfig Then
                Return True
            End If

            'If running in dev with IIS cert lets abort update as well
            If Directory.Exists(Path.Combine(GetSafeExePath, "ESS")) Then
                If hostName.ToLower = "localhost" Then
                    Return True
                Else
                    Return False '5.13.19
                End If
            End If
        End If

        Return False
    End Function

    Private MaintenanceModeSettings As MaintenanceSettingsHelper
    Private Async Function ExitIfInMaintenanceMode() As Task
        MaintenanceModeSettings = MaintenanceSettingsHelper.GetSettings()
        If MaintenanceModeSettings.IsOn Then
            Me.Text = MaintenanceModeSettings.Message ' "Currently undergoing maintenance"
            Me.Hide()
            msg.ForeColor = Color.DarkRed
            If Not String.IsNullOrEmpty(MaintenanceModeSettings.Message) Then
                msg.Text = MaintenanceModeSettings.Message
            End If

            Beep()
            Dim dlg = New MaintenanceModeMsgForm(MaintenanceModeSettings.Message)
            dlg.ShowDialog()

            Await Task.Delay(20)
            ExitUpdaterAndKillLauncher()
        End If
    End Function

    Private Sub ECUpdaterForm4_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If MaintenanceModeSettings IsNot Nothing AndAlso MaintenanceModeSettings.IsOn Then
            ExitUpdaterAndKillLauncher()
        End If
    End Sub

    Public Shared Function checkInstalled(ByVal c_name As String) As Boolean
        Dim displayName As String
        Dim registryKey As String = "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"
        Dim key As RegistryKey = Registry.LocalMachine.OpenSubKey(registryKey)

        If key IsNot Nothing Then

            For Each subkey As RegistryKey In key.GetSubKeyNames().[Select](Function(keyName) key.OpenSubKey(keyName))
                displayName = TryCast(subkey.GetValue("DisplayName"), String)

                If displayName IsNot Nothing AndAlso displayName.Contains(c_name) Then
                    Return True
                End If
            Next

            key.Close()
        End If

        registryKey = "SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        key = Registry.LocalMachine.OpenSubKey(registryKey)

        If key IsNot Nothing Then

            For Each subkey As RegistryKey In key.GetSubKeyNames().[Select](Function(keyName) key.OpenSubKey(keyName))
                displayName = TryCast(subkey.GetValue("DisplayName"), String)

                If displayName IsNot Nothing AndAlso displayName.Contains(c_name) Then
                    Return True
                End If
            Next

            key.Close()
        End If

        Return False
    End Function


    ''' <summary>
    ''' Return the directory the currently executing application is started from.
    ''' Note: this avoids the issues of files that are shadow copied before
    ''' being executed.
    ''' </summary>
    ''' <returns></returns>
    Private Function GetSafeExePath() As String
        '#If DEBUG
        '    Return "c:\apps\eccoder"
        '#else
        '    return AppDomain.CurrentDomain.SetupInformation.ApplicationBase()
        '#end if
        'MessageBox.Show(AppDomain.CurrentDomain.SetupInformation.ApplicationBase())
        'Return AppDomain.CurrentDomain.SetupInformation.ApplicationBase()
        ' System.Reflection.Assembly.GetAssembly(typeof(DaoTests)).Location

        Return Path.GetDirectoryName(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName)
    End Function

    Private Function GetVersionString() As String
        '  Return Environment.Version.ToString
        'Dim ver = System.Diagnostics.Process.GetCurrentProcess().MainModule.FileVersionInfo.FileVersion
        ' Dim v2 = AppContext.get


        Dim assembly As Assembly = assembly.GetExecutingAssembly()
        Dim version As Version = assembly.GetName().Version
        Return version.ToString
    End Function

    Private Function CalcDestinationDir(ByVal sourceDir As DirectoryInfo) As DirectoryInfo
        Dim RelativeDir As String = Nothing
        If sourceDir.FullName = New DirectoryInfo(Me.UpdatePath).FullName Then
            RelativeDir = ""
        Else
            RelativeDir = sourceDir.FullName.Substring(Me.UpdatePath.Length + 1, sourceDir.FullName.Length - Me.UpdatePath.Length - 1)
        End If

        Return New DirectoryInfo(Path.Combine(GetSafeExePath(), RelativeDir))
    End Function


    Private Function OutOfDate(sw As StreamWriter, ByVal sourceFileInfo As FileInfo) As Boolean
        'Dim timer As New Stopwatch
        'timer.Start()

        Try
            Dim RelativeDir As String = Nothing

            Me.msg.Text = "Checking " & sourceFileInfo.Name & " ..."
            If sourceFileInfo.Directory.FullName = New DirectoryInfo(Me.UpdatePath).FullName Then
                RelativeDir = ""
            Else
                RelativeDir = sourceFileInfo.DirectoryName.Substring(Me.UpdatePath.Length + 1, sourceFileInfo.DirectoryName.Length - Me.UpdatePath.Length - 1)
            End If

            Dim toDir = Path.Combine(Environment.CurrentDirectory, RelativeDir)
            Dim destinationFileInfo As FileInfo = New FileInfo(Path.Combine(toDir, sourceFileInfo.Name))

            If destinationFileInfo.Exists = False Then
                Return True
            End If
            Dim test = $"{Now.ToLongTimeString}: "
            sw.Write(test)
            'If sourceFileInfo.Extension = ".exe" OrElse sourceFileInfo.Extension = ".dll" Then
            '    Dim sourceFileVersionInfo As FileVersionInfo = FileVersionInfo.GetVersionInfo(sourceFileInfo.FullName)
            '    Dim destinationFileVersionInfo As FileVersionInfo = FileVersionInfo.GetVersionInfo(destinationFileInfo.FullName)

            '    If sourceFileVersionInfo.ProductVersion.ToString <> destinationFileVersionInfo.ProductVersion.ToString Then
            '        sw.WriteLine(String.Format("(EXE DLL) *FAILED* Version Test) 'File {0} Ver {1}' <> File {2} Ver {3}", sourceFileInfo.Name, sourceFileVersionInfo.ProductVersion.ToString, destinationFileInfo.Name, destinationFileVersionInfo.ProductVersion.ToString))
            '        Return True
            '    Else
            '        sw.WriteLine(String.Format("(EXE DLL) *PASSED* Version Test) 'File {0} Ver {1}' <> File {2} Ver {3}", sourceFileInfo.Name, sourceFileVersionInfo.ProductVersion.ToString, destinationFileInfo.Name, destinationFileVersionInfo.ProductVersion.ToString))
            '    End If
            'Else

            If sourceFileInfo.LastWriteTime <> destinationFileInfo.LastWriteTime Then
                sw.WriteLine(String.Format("(NON EXE) *FAILED* LastWriteTime Test) 'File {0} LastWriteTime {1}' <> File {2} LastWriteTime {3}", sourceFileInfo.Name, sourceFileInfo.LastWriteTime, destinationFileInfo.Name, destinationFileInfo.LastWriteTime))
                sw.WriteLine(String.Format("(NON EXE) *FAILED* CreationTime  Test) 'File {0} CreationTime  {1}' <> File {2} CreationTime  {3}", sourceFileInfo.Name, sourceFileInfo.CreationTime, destinationFileInfo.Name, destinationFileInfo.CreationTime))
                Return True
            Else
                sw.WriteLine(String.Format("(NON EXE) *PASSED* LastWriteTime Test) 'File {0} LastWriteTime {1}' <> File {2} LastWriteTime {3}", sourceFileInfo.Name, sourceFileInfo.LastWriteTime, destinationFileInfo.Name, destinationFileInfo.LastWriteTime))
            End If

            If sourceFileInfo.Length <> destinationFileInfo.Length Then
                sw.WriteLine(String.Format("(NON EXE) *FAILED*        Length Test) 'File {0} Length {1}' <> File {2} Length {3}", sourceFileInfo.Name, sourceFileInfo.Length, destinationFileInfo.Name, destinationFileInfo.Length))
                Return True
            Else
                sw.WriteLine(String.Format("(NON EXE) *PASSED*        Length Test) 'File {0} Length {1}' <> File {2} Length {3}", sourceFileInfo.Name, sourceFileInfo.Length, destinationFileInfo.Name, destinationFileInfo.Length))
            End If
            '   End If

            Return False
        Catch
            Return True
        Finally
            'timer.Stop()
            'sw.WriteLine($"It took {timer.Elapsed} to sync files.")
        End Try

    End Function

    ''' <summary>
    ''' Compare ECLauncher to ECLauncherTemp in the client eccoder directory
    ''' </summary>
    ''' <returns></returns>
    Private Function LauncherNeedsUpdate() As Boolean
        Try
            If My.Application.CommandLineArgs.Count = 1 Then
                Dim oldLauncherVer = My.Application.CommandLineArgs(0)

                Dim fi As New FileInfo(Path.Combine(GetSafeExePath(), "ECLauncherTemp.exe"))
                If fi.Exists = False Then
                    Return False
                End If
                Dim curLauncherVer = FileVersionInfo.GetVersionInfo(fi.FullName).ProductVersion.ToString()

                If oldLauncherVer <> curLauncherVer Then
                    Return True
                End If

                Return False
            End If
        Catch ex As Exception
            Return False
        End Try

        Return False
    End Function

    Private Sub StartLauncherUpdater()
        Diagnostics.Process.Start("ECLauncherUpdater.exe")
        'MessageBox.Show("starting ECLauncherUpdater")
    End Sub

    Public Function GetUpdaterObj() As DOUpdater
        Dim u As New XPCollection(Of DOUpdater)
        If u.Count > 1 Then
            MessageBox.Show("There needs to be one and only one record in the DOUpdater Table")
            Application.Exit()
        End If

        If u.Count = 1 Then
            Return u(0)
        Else
            Return Nothing
        End If

    End Function

    Private Sub Timer1_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles Timer1.Tick
        Timer1.Stop()
        Me.Process()
        Close()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles updateAnywayBtn.Click
        UpdateFilesOnServerOverride = True
        Process()
    End Sub
End Class

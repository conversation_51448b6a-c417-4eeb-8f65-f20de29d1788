﻿Public Class ExcelViewer
    Public Sub New(docAsByteArray As Byte())

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        SpreadsheetControl1.LoadDocument(docAsByteArray)

    End Sub
    Private Sub SpreadsheetControl1_Click(sender As Object, e As EventArgs) Handles SpreadsheetControl1.Click

    End Sub

    'Private Sub SimpleButton1_Click(sender As Object, e As EventArgs)

    '    SpreadsheetControl1.LoadDocument("C:\Users\<USER>\OneDrive - Allscripts Healthcare, LLC\Documents\Master Report Usage.xlsx")
    'End Sub
End Class
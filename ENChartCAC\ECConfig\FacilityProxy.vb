Imports DevExpress.Xpo
Imports System.Collections.Specialized
Imports System.Xml.Serialization
Imports System.Linq
Imports ENChartCAC

<XmlRoot("FacilityConfig")>
Public Class FacilityProxy
    Private _ci As DOConfigInstance
    Public FormVersion As String

    'Need blank constructor for serialization 
    Public Sub New()

    End Sub

    Sub New(ByVal f As DOFacility, pTreatmentAreaList As StringCollection, pUserList As List(Of Integer), Optional loadCdms As Boolean = True)
        _ci = f.ConfigInstanceVersion
        FormVersion = f.ConfigInstanceVersion.ActiveFormClassName

        For Each cbo In _ci.ComboBoxLists
            Dim cboProxy As New DTOConfigComboBoxList(cbo)
            ComboBoxLists.Add(cboProxy)
        Next

        For Each cor In _ci.ControlOverrides
            Dim corProxy As New DTOControlConfigGroup(cor)
            ControlOverrides.Add(corProxy)
        Next

        For Each med In _ci.Medications
            Dim medProxy As New DTOMedication(med)
            Medications.Add(medProxy)
        Next

        For Each codingReportText As DOCodingReportText In _ci.CodingReportTexts
            Dim lNewDoCodingReportText As New DTOCodingReportText(codingReportText)
            CodingReportTexts.Add(lNewDoCodingReportText)
        Next

        For Each physicianCodeFilter As DOPhysicianCodesFilter In _ci.PhysicianCodesFilter
            Dim newCode As New DTOPhysicianCodesFilter(physicianCodeFilter)
            PhysicianCodesFilter.Add(newCode)
        Next

        For Each ConfigGroup In _ci.ConfigSettingsGroups()
            If ConfigGroup.GroupName = "Application" Then
                For Each ConfigSetting In ConfigGroup.Settings
                    Dim configSettingProxy As New DTOConfigSetting(ConfigSetting)
                    ConfigSettings.Add(configSettingProxy)
                Next
            End If
        Next

        For Each SupplyCatMap In _ci.SupplyCatMap
            Dim supplyCatMapProxy As New DTOSupplyCatMap(SupplyCatMap)
            SupplyCatMappings.Add(supplyCatMapProxy)
        Next

        Dim taList = GetTreatmentAreasAsListByFacility(f)
        Dim doTaList = GetTreatmentAreasByFacility(f).ToList()

        If pTreatmentAreaList IsNot Nothing Then
            taList = pTreatmentAreaList
        End If

        For Each TA In taList
            Dim treatmentArea As DTOTreatmentArea = Nothing
            For Each dota In doTaList
                If dota.Name = TA Then
                    treatmentArea = New DTOTreatmentArea(dota)
                    Exit For
                End If
            Next
            If treatmentArea Is Nothing Then
                treatmentArea = New DTOTreatmentArea(TA)
            End If

            ' Dim TreatArea As New DTOTreatmentArea(TA)
            If Not ECGlobals.Use2018EspcodeLogic Then
                ' ReSharper disable once IdentifierTypo
                Dim espcodesList = DOESPCode.GetESPCodesList(f.FacilityID, TA)
                If espcodesList IsNot Nothing Then
                    For Each _doespcode In espcodesList
                        treatmentArea.ESPCodes.Add(New DTOESPCode(_doespcode))
                    Next
                End If
            End If

            If loadCdms Then
                treatmentArea.CDMs = GetCdmRecords(f.Oid, TA)
            End If

            Dim facilitySettings = DOFacilitySettings.GetFacilitySettings(f, TA)
            If facilitySettings IsNot Nothing Then
                For Each _doFacSetting In facilitySettings
                    Dim dtoFacSetting As New DTOFacilitySettings(_doFacSetting)
                    treatmentArea.FacilitySettings.Add(dtoFacSetting)
                Next
            End If

            TreatmentAreas.Add(treatmentArea)
        Next

        'TODO Get DOEspcode2018 records by facility
        If ECGlobals.Use2018EspcodeLogic Then
            Using uow As New UnitOfWork
                'fyi - i know there are more concise ways to write this logic
                'but i feel this is more readable (at least to old timers)
                Dim espCodes = (From esp In New XPQuery(Of DOESPCode2018)(uow)
                                Where esp.Facility = f.Oid
                                Select esp)
                'Dim EspCodes = (From esp In New XPQuery(Of DOESPCode2018)(uow)
                '                Where esp.Facility Is Nothing
                '                Select esp)


                For Each oldEspCode In espCodes
                    EspCodes2018.Add(New DTOESPCode2018(oldEspCode))
                Next
            End Using
        End If

        Dim emptyFacilitySettings = DOFacilitySettings.GetFacilitySettingsWithEmptyTA(f)
        If emptyFacilitySettings IsNot Nothing Then
            For Each _doFacSetting In emptyFacilitySettings
                Dim dtoFacSetting As New DTOFacilitySettings(_doFacSetting)
                FacilityAllTa.Add(dtoFacSetting)
            Next
        End If

        Dim modGrpList = DOModifierGroups.GetModifierGroups
        If modGrpList IsNot Nothing Then
            For Each modgrp In modGrpList
                Dim dtoModGrp As New DTOModifierGroups(modgrp)
                ModifierGroups.Add(dtoModGrp)
            Next
        End If

        Try
            DtoAdminReports = New DTOAdminReports(f)
        Catch ex As Exception
            MessageBox.Show("The following errror occured trying to get the Admin Reports to export: " & vbCrLf & ex.ToString, "Unexpected Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Dim userCollection As New XPCollection(Of DOUser)
        For Each user In userCollection
            If pUserList IsNot Nothing Then 'If a UserList (of useer OIDs) was passed in, only copy those users
                If pUserList.Contains(user.Oid) Then
                    Dim dtoUser As New DTOUser(user, f.Oid)
                    Me.Users.Add(dtoUser)
                End If
            Else
                Dim dtoUser As New DTOUser(user, f.Oid)
                Me.Users.Add(dtoUser)
            End If
        Next

        For Each deductedTime In _ci.DeductedTimes
            Dim deductedTimeProxy As New DTODeductedTimes(deductedTime)
            DeductedTimes.Add(deductedTimeProxy)
        Next

        For Each mappping In _ci.FinancialClassMappings
            Dim mappingProxy As New DTOFinancialClassMapping(mappping)
            FinancialClassMappings.Add(mappingProxy)
        Next

        Dim sharedCi = f.SharedConfigInstanceVersion
        For Each cbo In sharedCi.ComboBoxLists
            Dim cboProxy As New DTOConfigComboBoxList(cbo)
            SharedComboBoxLists.Add(cboProxy)
        Next


    End Sub

    Private Function GetCdmRecords(ByVal facility As Integer, ByVal treatmentArea As String) As List(Of DTOChargeMaster)
        Dim returnList As New List(Of DTOChargeMaster)
        Dim chargeMasterList = From cm In New XPQuery(Of DOChargeMaster)(XpoDefault.Session)
                               Where cm.Facility.Oid = facility And cm.TreatmentArea = treatmentArea
                               Select cm

        For Each cm In chargeMasterList
            returnList.Add(New DTOChargeMaster(cm))
        Next

        Return returnList
    End Function

    'This facilityAllTA list is for DOFacilitySetting records with a blank or null TreatmentArea
    Public FacilityAllTa As New List(Of DTOFacilitySettings)

    Public ComboBoxLists As New List(Of DTOConfigComboBoxList)

    Public SharedComboBoxLists As New List(Of DTOConfigComboBoxList)

    Public ControlOverrides As New List(Of DTOControlConfigGroup)

    Public Medications As New List(Of DTOMedication)

    Public ConfigSettings As New List(Of DTOConfigSetting)

    Public SupplyCatMappings As New List(Of DTOSupplyCatMap)

    'DTOTreatmentArea is for espcodes
    Public TreatmentAreas As New List(Of DTOTreatmentArea)

    Public CodingReportTexts As New List(Of DTOCodingReportText)

    Public PhysicianCodesFilter As New List(Of DTOPhysicianCodesFilter)

    Public ModifierGroups As New List(Of DTOModifierGroups)

    Public Users As New List(Of DTOUser)

    Public DtoAdminReports As DTOAdminReports

    Public DeductedTimes As New List(Of DTODeductedTimes)

    Public FinancialClassMappings As New List(Of DTOFinancialClassMapping)

    Public EspCodes2018 As New List(Of DTOESPCode2018)

End Class

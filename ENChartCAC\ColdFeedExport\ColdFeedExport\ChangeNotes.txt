﻿**************  (v. 3.0.0.0) 2016-03-31
	* US37244: SAP 17 Upgrade - Upgrade ColdFeedExport Project
	* Upgraded solution to .NET 4.0
	* Upgraded DevExpress to v15.2

**************  (v. 2.1.1.0) 2015-11-18
	* Reversioning to correspond with MIC release version

**************  (v. 1.0.0.13) 2014-08-21
	* US7914: WI 2051 - MIC Image Feed: Configuration to Execute Using a Domain User Account
	* Add new configuration settings to allow the Cold Feed to impersonate another user on the domain when writing files
		Settings
			- Added new settings:
				o ExportUsingDomainAccount - Boolean setting to toggle using the confgured domain account or not
				o DomainAccountDomain - String setting for the domain of the domain account
				o DomainAccountUsername - String setting for the username of the domain account
				o DomainAccountPassword - String setting for the password of the domain account
		MainForm.vb
			- In the function LoadDefaultOptionsFromConfigFile, load the new configuration settings into optionsDictionary (~lines 383-387)
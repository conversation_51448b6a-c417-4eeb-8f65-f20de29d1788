Imports McKesson.HIC.ColdFeed
Imports System.IO

Namespace ColdFeed
    Public Class HPFColdFeedImageAsTextFileHandler
        Implements IExportChargeSummaryImageFile


        Public Function CreateImageFile(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryImageFile.CreateImageFile
            Dim outPath As String = CFHelper.OptionsDict("ImageOutputPath")
            Dim FileType As String = CFHelper.OptionsDict("ImageFileType")
            Dim fileName = String.Format("{0}_{1}v{3}.{2}", chart.Chart.ChartInfo.Facility.FacilityID, _
                                        chart.Chart.VisitID, FileType, chart.Chart.Version)

            Dim fullPath = Path.Combine(My.Application.Info.DirectoryPath, outPath)

            If Not Directory.Exists(fullPath) Then
                Directory.CreateDirectory(fullPath)
            End If

            fullPath = Path.Combine(fullPath, fileName)

            chart.ImageFileName = Path.GetFileName(fullPath)

            If File.Exists(fullPath) Then
                chart.ImageExportSucceeded = True
            Else
                chart.ImageExportSucceeded = False
            End If

            If chart.ImageExportSucceeded = False Then
                ' Me.CFHelper.ShowMessageAndLog("Log Extra Errors Here ...")
            End If

            Return chart.ImageExportSucceeded
        End Function

        Public Sub Init() Implements IExportChargeSummaryImageFile.Init

        End Sub

        Private _cFHelper As ColdFeedHelper
        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryImageFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property
    End Class

End Namespace

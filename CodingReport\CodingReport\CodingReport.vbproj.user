﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectView>ShowAllFiles</ProjectView>
    <PublishUrlHistory />
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <StartWorkingDirectory>c:\apps\eCCoder</StartWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartWorkingDirectory>C:\Apps\ECCoder\</StartWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <StartWorkingDirectory>C:\Apps\ECCoder\</StartWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <StartWorkingDirectory>c:\apps\eCCoder</StartWorkingDirectory>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="Form1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="RetrievalMessage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="XtraCodingReport.vb">
      <SubType>XtraReport</SubType>
    </Compile>
  </ItemGroup>
</Project>
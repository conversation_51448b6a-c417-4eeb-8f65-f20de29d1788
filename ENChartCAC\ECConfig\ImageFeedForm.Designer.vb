﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ImageFeedForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.ssImageFeedConfig = New System.Windows.Forms.StatusStrip()
        Me.fpnlModule = New System.Windows.Forms.FlowLayoutPanel()
        Me.cboModule = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.lblModule = New DevExpress.XtraEditors.LabelControl()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.cboModule.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'ssImageFeedConfig
        '
        Me.ssImageFeedConfig.Location = New System.Drawing.Point(0, 564)
        Me.ssImageFeedConfig.Name = "ssImageFeedConfig"
        Me.ssImageFeedConfig.Size = New System.Drawing.Size(904, 22)
        Me.ssImageFeedConfig.TabIndex = 13
        '
        'fpnlModule
        '
        Me.fpnlModule.Location = New System.Drawing.Point(12, 38)
        Me.fpnlModule.Margin = New System.Windows.Forms.Padding(0)
        Me.fpnlModule.Name = "fpnlModule"
        Me.fpnlModule.Size = New System.Drawing.Size(883, 497)
        Me.fpnlModule.TabIndex = 14
        '
        'cboModule
        '
        Me.cboModule.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboModule.Location = New System.Drawing.Point(116, 12)
        Me.cboModule.Name = "cboModule"
        Me.cboModule.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.cboModule.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboModule.Properties.ImmediatePopup = True
        Me.cboModule.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboModule.Size = New System.Drawing.Size(296, 20)
        Me.cboModule.TabIndex = 15
        '
        'lblModule
        '
        Me.lblModule.Location = New System.Drawing.Point(12, 15)
        Me.lblModule.Name = "lblModule"
        Me.lblModule.Size = New System.Drawing.Size(98, 13)
        Me.lblModule.TabIndex = 16
        Me.lblModule.Text = "Image Feed Module:"
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.Location = New System.Drawing.Point(820, 538)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 18
        Me.btnCancel.Text = "Cancel"
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.Location = New System.Drawing.Point(739, 538)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(75, 23)
        Me.btnOK.TabIndex = 17
        Me.btnOK.Text = "OK"
        '
        'ImageFeedForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(904, 586)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.lblModule)
        Me.Controls.Add(Me.cboModule)
        Me.Controls.Add(Me.fpnlModule)
        Me.Controls.Add(Me.ssImageFeedConfig)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "ImageFeedForm"
        Me.ShowIcon = False
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Image Feed Configuration"
        CType(Me.cboModule.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents ssImageFeedConfig As StatusStrip
    Friend WithEvents fpnlModule As FlowLayoutPanel
    Friend WithEvents cboModule As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents lblModule As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
End Class

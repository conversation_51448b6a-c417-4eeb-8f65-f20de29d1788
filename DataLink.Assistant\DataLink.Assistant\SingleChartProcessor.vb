﻿Imports System
Imports System.Security.Claims
Imports AIC.SharedData
Imports DataLink.Paragon
Imports DataLink.Paragon.Data
Imports DevExpress.Xpo
Imports McKessonIntelligentCoding.Data
Imports McKessonIntelligentCoding.Data.DTO
'Imports McKessonIntelligentCoding.ServiceLib
Imports Microsoft.AspNetCore.DataProtection
Imports Microsoft.Extensions.Logging
Imports VIC

Public Class SingleChartProcessor

    Public Shared Function GetParagonApiDataForChart(
           GlobalSettigns As Dictionary(Of String, String),
           ByVal args As Dictionary(Of String, Object),
           ByVal logger As ILogger,
           ByVal sess As Session,
           dpProvider As IDataProtectionProvider) As GetParagonApiDataForChartResult

        Dim getParagonApiDataForChartResult As New GetParagonApiDataForChartResult

        If sess Is Nothing Then
            Throw New ArgumentNullException(NameOf(sess))
        End If

        If logger Is Nothing Then
            Throw New ArgumentNullException(NameOf(logger))
        End If

        If args Is Nothing Then
            Throw New ArgumentNullException(NameOf(args))
        End If

        Dim dlr As New DataLinkResponse


        ' Step 1) Create the appropriate IDataLinkChartCollector object based on configuraion, we’ll pass the arguments 
        ' to its Collect function which will make the calls to the Paragon Mobile Physician API. What comes back is an 
        ' IDataLinkMappable Object.

        ' Currently hardcoded to use a ParagonDataCollector
        Dim paragonUser As AICParagonUser = Nothing
        If args.ContainsKey("User") Then
            paragonUser = args("User")
        Else
            '**************************** HOW ARE WE GONNA HANDLE THIS? **************************************
        End If
        Dim papiResults
        Try

            Dim pdc As New ParagonDataCollector(sess, paragonUser, GlobalSettigns, dpProvider)
            pdc.SetCollectCallResults(True)
            Dim pVisit As IDataLinkMappable = pdc.Collect(args)
            If pVisit IsNot Nothing Then
                papiResults = pdc.GetPapiResults()
                getParagonApiDataForChartResult.PapiResults = papiResults
                getParagonApiDataForChartResult.ReturnStatusCode = 200
                getParagonApiDataForChartResult.ReturnStatusDescription = "JJCRules"
            Else
                getParagonApiDataForChartResult.ReturnStatusCode = 300
                getParagonApiDataForChartResult.ReturnStatusDescription = "Unknown Error"
            End If
        Catch ex As ParagonDatalinkAuthenticationException
            getParagonApiDataForChartResult.ReturnStatusCode = 300
            getParagonApiDataForChartResult.ReturnStatusDescription = $"Exception was thrown: {ex.Message}"
        Catch ex As ParagonDatalinkTokenExpiredException
            getParagonApiDataForChartResult.ReturnStatusCode = 300
            getParagonApiDataForChartResult.ReturnStatusDescription = $"Exception was thrown: {ex.Message}"
        Catch ex As Exception
            getParagonApiDataForChartResult.ReturnStatusCode = 300
            getParagonApiDataForChartResult.ReturnStatusDescription = $"Exception was thrown: {ex.Message}"
        End Try
        Return getParagonApiDataForChartResult
    End Function

    Public Shared Function ProcessChart(
            GlobalSettigns As Dictionary(Of String, String),
            ByVal args As Dictionary(Of String, Object),
            ByVal logger As ILogger,
            ByVal sess As Session,
            dpProvider As IDataProtectionProvider) As DataLinkResponse

        If sess Is Nothing Then
            Throw New ArgumentNullException(NameOf(sess))
        End If

        If logger Is Nothing Then
            Throw New ArgumentNullException(NameOf(logger))
        End If

        If args Is Nothing Then
            Throw New ArgumentNullException(NameOf(args))
        End If

        Dim dataLinkResponseResult As New DataLinkResponse


        ' Step 1) Create the appropriate IDataLinkChartCollector object based on configuraion, we’ll pass the arguments 
        ' to its Collect function which will make the calls to the Paragon Mobile Physician API. What comes back is an 
        ' IDataLinkMappable Object.

        ' Currently hardcoded to use a ParagonDataCollector
        Dim paragonUser As AICParagonUser = Nothing
        If args.ContainsKey("User") Then
            paragonUser = args("User")
        Else
            '**************************** HOW ARE WE GONNA HANDLE THIS? **************************************
        End If

        Dim pdc As New ParagonDataCollector(sess, paragonUser, GlobalSettigns, dpProvider)
        pdc.SetCollectCallResults(False)
        Dim pVisit As IDataLinkMappable = pdc.Collect(args)

        ' Check to see if an object was returned by Collect. If so we can move to the mapping step, if not we can check
        ' the IDataLinkChartCollector's ErrorText field to see what went wrong.

        If pVisit IsNot Nothing Then
            ' Step 2) Create a BaseChartMapper class to (hopefully) turn the collected data into a DTOChartObject. We’ll 
            ' pass the IDataLinkMappable object we got in Step 1 into the MapChart function of the BaseChartMapper. A 
            ' DTOChartResponseWrapper Is returned which contains the fresh DTOChartObject, ResponseCode and 
            ' ResponseDescription fields we can use to make sure everything went okay. If everything did go okay the 
            ' Response object will be null which I'm not a huge fan of currently and could very well change how to 
            ' check for success. 

            'Dim mapper As New BaseChartMapper(sess)

            'Dim FacilityRepo = New FacilityRepository(sessionToUse)
            'Dim DataLinkFacilityMappingRepo = New DataLinkFacilityMappingRepository(sessionToUse)
            'Dim DataLinkLookupRepo = New DataLinkLookupRepository(sessionToUse)
            'Dim DataLinkMatchRepo = New DataLinkMatchRepository(sessionToUse)

            'Dim mapper As New ParagonChartMapper(sess)
            Dim mapper As New ParagonChartMapper(New ParagonRepositoryFactory(sess))
            Dim mapResponse As DTOChartResponseWrapper = mapper.MapChart(pVisit)

            ' If the Response is null then the chart was mapped successfully and we can proceed to Step 3, saving the chart in AIC. If mapping failed, we can check the Response object’s ResponseCode and ResponseDescription for troubleshooting info, etc.
            If mapResponse.Response Is Nothing Then

                ' Step 3) Create a ChartService object to take the DTOChartObject we got in Step 2 and use that to determine if and how the AIC chart should be updated or created. The SendChart function takes a DTOChartObject and returns a DTOChartResponse which can be used to check the save attempt status. The call to the ChartService constructor makes a call to ENChartDOLib.Utils.ConnectToECDataBase().
                Dim cs As New ChartService.ChartService(sess)
                Dim saveResponse As DTOChartResponse = cs.SendChart(mapResponse.Chart)

                ' Step 3 success or failure, do something with the resultant status of the save attempt
                dataLinkResponseResult.ResponseCode = saveResponse.ResponseCode
                dataLinkResponseResult.ResponseDescription = saveResponse.ResponseDescription
            Else
                ' Step 2 failure, we can refer to the Response object for details 
                dataLinkResponseResult.ResponseCode = mapResponse.Response.ResponseCode
                dataLinkResponseResult.ResponseDescription = mapResponse.Response.ResponseDescription
            End If
        ElseIf pdc.ErrorCode <> 0 Then
            dataLinkResponseResult.ResponseCode = pdc.ErrorCode
            If pdc.ErrorText IsNot Nothing Then
                dataLinkResponseResult.ResponseDescription = pdc.ErrorText
            End If
        Else
            If pdc.ErrorText = "N/A" Then
                dataLinkResponseResult.ResponseCode = 100
                dataLinkResponseResult.ResponseDescription = "Skipped"
            Else
                ' Step 1 failure, we can refer to the ErrorText of our ParagonDataCollector to notify the user, log, etc.
                dataLinkResponseResult.ResponseCode = 300
                dataLinkResponseResult.ResponseDescription = $"Error retrieving chart data from Paragon: {pdc.ErrorText}"
            End If
        End If

        Return dataLinkResponseResult
    End Function
End Class

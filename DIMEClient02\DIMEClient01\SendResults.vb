﻿Public Class SendResults
    Private _successes As Integer = 0
    Private _failures As Integer = 0

    Public ReadOnly Property Successess As Integer
        Get
            Return _successes
        End Get
    End Property

    Public ReadOnly Property Failures As Integer
        Get
            Return _failures
        End Get
    End Property

    Public ReadOnly Property Total As Integer
        Get
            Return (_successes + _failures)
        End Get
    End Property

    Public Sub AddSuccess()
        _successes += 1
    End Sub

    Public Sub AddFailure()
        _failures += 1
    End Sub

    Public Sub AddResults(ByVal additional_results As SendResults)
        _successes += additional_results.Successess
        _failures += additional_results.Failures
    End Sub

    Public Overrides Function ToString() As String
        Return "Successess: " & Successess & " - Failures: " & Failures
    End Function
End Class

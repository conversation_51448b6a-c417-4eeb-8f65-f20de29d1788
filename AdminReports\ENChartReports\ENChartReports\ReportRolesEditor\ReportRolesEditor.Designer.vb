<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ReportRolesEditor
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.listAllReports = New DevExpress.XtraEditors.ListBoxControl()
        Me.lblAllReports = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddOne = New DevExpress.XtraEditors.SimpleButton()
        Me.btnRemoveOne = New DevExpress.XtraEditors.SimpleButton()
        Me.listRoleReports = New DevExpress.XtraEditors.ListBoxControl()
        Me.btnSave = New DevExpress.XtraEditors.SimpleButton()
        Me.cboRoles = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnNewRole = New DevExpress.XtraEditors.SimpleButton()
        Me.cboFacility = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.lblFacility = New DevExpress.XtraEditors.LabelControl()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.lblReportRole = New DevExpress.XtraEditors.LabelControl()
        CType(Me.listAllReports, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.listRoleReports, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboRoles.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboFacility.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'listAllReports
        '
        Me.listAllReports.Cursor = System.Windows.Forms.Cursors.Default
        Me.listAllReports.Location = New System.Drawing.Point(18, 184)
        Me.listAllReports.Margin = New System.Windows.Forms.Padding(4)
        Me.listAllReports.Name = "listAllReports"
        Me.listAllReports.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended
        Me.listAllReports.Size = New System.Drawing.Size(412, 523)
        Me.listAllReports.TabIndex = 0
        '
        'lblAllReports
        '
        Me.lblAllReports.Location = New System.Drawing.Point(18, 155)
        Me.lblAllReports.Margin = New System.Windows.Forms.Padding(4)
        Me.lblAllReports.Name = "lblAllReports"
        Me.lblAllReports.Size = New System.Drawing.Size(140, 19)
        Me.lblAllReports.TabIndex = 1
        Me.lblAllReports.Text = "Master Reports List:"
        '
        'btnAddOne
        '
        Me.btnAddOne.Location = New System.Drawing.Point(440, 367)
        Me.btnAddOne.Margin = New System.Windows.Forms.Padding(4)
        Me.btnAddOne.Name = "btnAddOne"
        Me.btnAddOne.Size = New System.Drawing.Size(45, 44)
        Me.btnAddOne.TabIndex = 2
        Me.btnAddOne.Text = ">"
        Me.btnAddOne.ToolTip = "Add Selected Report(s) To Role"
        '
        'btnRemoveOne
        '
        Me.btnRemoveOne.Location = New System.Drawing.Point(440, 460)
        Me.btnRemoveOne.Margin = New System.Windows.Forms.Padding(4)
        Me.btnRemoveOne.Name = "btnRemoveOne"
        Me.btnRemoveOne.Size = New System.Drawing.Size(45, 44)
        Me.btnRemoveOne.TabIndex = 4
        Me.btnRemoveOne.Text = "<"
        Me.btnRemoveOne.ToolTip = "Remove Selected Report(s) From Role"
        '
        'listRoleReports
        '
        Me.listRoleReports.Cursor = System.Windows.Forms.Cursors.Default
        Me.listRoleReports.Location = New System.Drawing.Point(494, 184)
        Me.listRoleReports.Margin = New System.Windows.Forms.Padding(4)
        Me.listRoleReports.Name = "listRoleReports"
        Me.listRoleReports.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended
        Me.listRoleReports.Size = New System.Drawing.Size(412, 523)
        Me.listRoleReports.TabIndex = 6
        '
        'btnSave
        '
        Me.btnSave.Enabled = False
        Me.btnSave.Location = New System.Drawing.Point(598, 716)
        Me.btnSave.Margin = New System.Windows.Forms.Padding(4)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(150, 34)
        Me.btnSave.TabIndex = 7
        Me.btnSave.Text = "Save Changes"
        '
        'cboRoles
        '
        Me.cboRoles.Location = New System.Drawing.Point(494, 146)
        Me.cboRoles.Margin = New System.Windows.Forms.Padding(4)
        Me.cboRoles.Name = "cboRoles"
        Me.cboRoles.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboRoles.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboRoles.Size = New System.Drawing.Size(338, 26)
        Me.cboRoles.TabIndex = 8
        '
        'btnNewRole
        '
        Me.btnNewRole.Location = New System.Drawing.Point(842, 139)
        Me.btnNewRole.Margin = New System.Windows.Forms.Padding(4)
        Me.btnNewRole.Name = "btnNewRole"
        Me.btnNewRole.Size = New System.Drawing.Size(64, 34)
        Me.btnNewRole.TabIndex = 9
        Me.btnNewRole.Text = "New"
        '
        'cboFacility
        '
        Me.cboFacility.Location = New System.Drawing.Point(18, 79)
        Me.cboFacility.Margin = New System.Windows.Forms.Padding(4)
        Me.cboFacility.Name = "cboFacility"
        Me.cboFacility.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboFacility.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboFacility.Size = New System.Drawing.Size(412, 26)
        Me.cboFacility.TabIndex = 10
        '
        'lblFacility
        '
        Me.lblFacility.Location = New System.Drawing.Point(18, 51)
        Me.lblFacility.Margin = New System.Windows.Forms.Padding(4)
        Me.lblFacility.Name = "lblFacility"
        Me.lblFacility.Size = New System.Drawing.Size(54, 19)
        Me.lblFacility.TabIndex = 11
        Me.lblFacility.Text = "Facility:"
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(758, 716)
        Me.btnCancel.Margin = New System.Windows.Forms.Padding(4)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(150, 34)
        Me.btnCancel.TabIndex = 12
        Me.btnCancel.Text = "Close"
        '
        'lblReportRole
        '
        Me.lblReportRole.Location = New System.Drawing.Point(494, 113)
        Me.lblReportRole.Margin = New System.Windows.Forms.Padding(4)
        Me.lblReportRole.Name = "lblReportRole"
        Me.lblReportRole.Size = New System.Drawing.Size(89, 19)
        Me.lblReportRole.TabIndex = 13
        Me.lblReportRole.Text = "Report Role:"
        '
        'ReportRolesEditor
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 19.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(926, 766)
        Me.Controls.Add(Me.lblReportRole)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.lblFacility)
        Me.Controls.Add(Me.cboFacility)
        Me.Controls.Add(Me.btnNewRole)
        Me.Controls.Add(Me.cboRoles)
        Me.Controls.Add(Me.btnSave)
        Me.Controls.Add(Me.listRoleReports)
        Me.Controls.Add(Me.btnRemoveOne)
        Me.Controls.Add(Me.btnAddOne)
        Me.Controls.Add(Me.lblAllReports)
        Me.Controls.Add(Me.listAllReports)
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "ReportRolesEditor"
        Me.Text = "Report Roles Editor"
        CType(Me.listAllReports, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.listRoleReports, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboRoles.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboFacility.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents listAllReports As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents lblAllReports As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddOne As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnRemoveOne As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents listRoleReports As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents btnSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents cboRoles As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnNewRole As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents cboFacility As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents lblFacility As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblReportRole As DevExpress.XtraEditors.LabelControl
End Class

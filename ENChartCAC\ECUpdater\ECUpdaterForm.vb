Imports System
'Imports EnchartDOLib
Imports DevExpress
Imports DevExpress.Data
Imports DevExpress.Xpo.DB
Imports DevExpress.Xpo
Imports System.Configuration
Imports System.IO
Imports System.Diagnostics
Imports System.Reflection

Public Class ECUpdaterForm
    Private bFirstTime As Boolean = True
    Dim UpdatePath As String = Nothing

    Private Sub Form1_Activated(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Activated
        My.Application.DoEvents()
        If Me.bFirstTime Then
            Me.bFirstTime = False
            Timer1.Interval = 250
            Timer1.Start()
        End If
    End Sub

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        Text = String.Format("{0} Ver: {1}", My.Application.Info.AssemblyName, My.Application.Info.Version)
    End Sub

    Private Sub DoSomeWork()
         try
            'MessageBox.Show("DOSomeWork b4 ConnectToDataBase()")
            ConnectToDataBase()
        Catch ex As Exception
            MessageBox.Show("(ECUpdaterForm->DoSomeWork) Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(0)
        End try
        Try
            Using fs As FileStream = New FileStream(Path.Combine(Environment.CurrentDirectory, "ecupdater.log"), FileMode.Create)
                Using sw As StreamWriter = New StreamWriter(fs)
                    sw.WriteLine(Text)
                    Dim lShouldReturn As Boolean
                    UpdateFiles(sw, lShouldReturn)
                    If lShouldReturn Then
                        Return
                    End If
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("(ECUpdaterForm->DoSomeWork) Error : " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(0)
        End Try
    End Sub

    'Private Sub UpdateUpdater() 
    '    Dim fi As New FileInfo(Path.Combine(UpdatePath, "ECUpdater.exe"))
    '    If fi.Exists Then

    '        Dim fiv As FileVersionInfo = FileVersionInfo.GetVersionInfo(fi.FullName)
    '        Dim serverFileVersion As String = fiv.ProductVersion.ToString
    '        Dim localFileVersion As String = My.Application.Info.Version.ToString

    '        If serverFileVersion = localFileVersion Then
    '            LabelControl1.Text = "ECUpdater is up to date."
    '           return
    '        End If

    '        LabelControl1.Text = "Copying Files" '& fi.FullName
    '        LabelControl1.Refresh()
    '        Try
    '            fi.CopyTo(Path.Combine(GetSafeExePath(), "ECUpdater.exe"), True)
    '        Catch
    '            'ignore
    '        End Try
    '        Close()
    '    End If
    'End Sub

    ''' <summary>
    ''' Return the directory the currently executing application is started from.
    ''' Note: this avoids the issues of files that are shadow copied before
    ''' being executed.
    ''' </summary>
    ''' <returns></returns>
    Private Function GetSafeExePath() As String
        Dim path As String = Nothing
        'use the below instead of "Environment.CurrentDirectory()
        'this may be more secure and pass vericode scan?
        'Dim tpath = (New System.Uri(Assembly.GetExecutingAssembly().CodeBase)).AbsolutePath
        path = AppDomain.CurrentDomain.SetupInformation.ApplicationBase()
        Return path
    End Function
    Private Shared Sub ExitUpdaterAndKillLauncher()
        Environment.Exit(0)
    End Sub
    Private Sub UpdateFiles(sw As StreamWriter, ByRef shouldReturn As Boolean)
        shouldReturn = False
        Dim UpdaterObj As DOUpdater = GetUpdaterObj()
        If UpdaterObj Is Nothing Then
            MessageBox.Show("An error occured while trying to read from the DOUpdater table. Try running the PublishUpdate.exe util.")
            ExitUpdaterAndKillLauncher()
        End If

        UpdatePath = UpdaterObj.UpdatePath
        If String.IsNullOrEmpty(UpdatePath) Then
            MessageBox.Show("The update source directory path has not been set", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ExitUpdaterAndKillLauncher()
        End If

        Dim UpdateDir As New DirectoryInfo(UpdatePath)
        If UpdateDir.Exists = False Then
           ExitUpdaterAndKillLauncher()
        End If

        PullDownAllOutOfDateFiles(sw, UpdateDir)

        If LauncherNeedsUpdate() Then
           StartLauncherUpdater()
           Environment.Exit(0)
        End If

        Close() 'exit app ... hmmm
    End Sub
    Private Sub PullDownAllOutOfDateFiles(ByVal sw As StreamWriter, ByVal UpdateDir As DirectoryInfo)
        For Each sfile As FileInfo In UpdateDir.GetFiles
            If OutOfDate(sw, sfile) Then
                LabelControl1.Text = "Copying Files" '& sfile.FullName
                LabelControl1.Refresh()
                Try
                    sfile.CopyTo(Path.Combine(GetSafeExePath(), sfile.Name), True)
                Catch ex As Exception
                    Dim msg As String = String.Format("The following error occured trying to update {0}:" & vbCrLf & vbCrLf & ex.Message & vbCrLf & vbCrLf & "Please notify Technical Support", sfile.FullName, ex)
                    sw.WriteLine(msg)
                    'If MessageBox.Show(msg, "Update Error", MessageBoxButtons.OKCancel, MessageBoxIcon.Error) = DialogResult.Cancel Then
                    '    Exit Sub
                    'End If
                End Try
            End If
        Next

        'Work through the sub directories and pull down the out of date files
        For Each ldir As DirectoryInfo In UpdateDir.GetDirectories
            CopyDir(sw, ldir)
        Next
    End Sub
    Private Function LauncherNeedsUpdate() As Boolean
        Return true
    End Function

    Private Sub StartLauncherUpdater()
        Process.Start("ECLauncherUpdater.exe")
    End Sub


    Private Sub CopyDir(sw As StreamWriter, ByVal pDi As DirectoryInfo) ', ByVal pBaseDi As DirectoryInfo)

        Try '09.02.09 - adding try catch block around this whole thing...
            Dim RelativeDir = pDi.FullName.Substring(Me.UpdatePath.Length + 1, pDi.FullName.Length - Me.UpdatePath.Length - 1)
            Dim toDir = Path.Combine(GetSafeExePath(), RelativeDir)
            Dim toDI As New DirectoryInfo(toDir)

            If toDI.Exists = False Then
                toDI.Create()
            End If

            For Each sfile As FileInfo In pDi.GetFiles
                If OutOfDate(sw, sfile) Then
                    Me.LabelControl1.Text = "Copying Files" '& sfile.FullName
                    Me.LabelControl1.Refresh()
                    Try
                        sfile.CopyTo(Path.Combine(toDir, sfile.Name), True)
                    Catch
                    End Try
                End If
            Next

            For Each ldir As DirectoryInfo In pDi.GetDirectories
                CopyDir(sw, ldir)
            Next
        Catch ex As Exception
            MessageBox.Show("(ECUpdaterForm->CopyDir) The following erorr occured in CopyDir : " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    Private Function OutOfDate(sw As StreamWriter, ByVal fi1 As FileInfo) As Boolean
        Try
            Dim RelativeDir As String = Nothing
            Dim toDir As String = Nothing
            Dim toDI As DirectoryInfo = Nothing

            If fi1.Directory.FullName = New DirectoryInfo(Me.UpdatePath).FullName Then
                RelativeDir = "" 'New DirectoryInfo(Me.UpdatePath).FullName 'would be hard write worse code...
            Else
                RelativeDir = fi1.DirectoryName.Substring(Me.UpdatePath.Length + 1, fi1.DirectoryName.Length - Me.UpdatePath.Length - 1)
            End If
            toDir = Path.Combine(Environment.CurrentDirectory, RelativeDir)
            toDI = New DirectoryInfo(toDir)

            If fi1.Extension = ".exe" OrElse fi1.Extension = ".dll" Then
                Me.LabelControl1.Text = "Checking " & fi1.Name & " ..."
                Dim fi2 As FileInfo = New FileInfo(Path.Combine(toDir, fi1.Name))
                'if fi1 doesn't exist at this point... "Houston, we have a problem..."
                If fi1.Exists = False OrElse fi2.Exists = False Then
                    Return True
                End If

                Dim fiv1 As FileVersionInfo = FileVersionInfo.GetVersionInfo(fi1.FullName)
                Dim fiv2 As FileVersionInfo = FileVersionInfo.GetVersionInfo(fi2.FullName)
                If fiv1.ProductVersion.ToString <> fiv2.ProductVersion.ToString Then
                    sw.WriteLine(String.Format("(EXE DLL) *FAILED* Version Test) 'File {0} Ver {1}' <> File {2} Ver {3}", fi1.Name, fiv1.ProductVersion.ToString, fi2.Name, fiv2.ProductVersion.ToString))
                    Return True
                Else
                    sw.WriteLine(String.Format("(EXE DLL) *PASSED* Version Test) 'File {0} Ver {1}' <> File {2} Ver {3}", fi1.Name, fiv1.ProductVersion.ToString, fi2.Name, fiv2.ProductVersion.ToString))
                End If
            Else
                Dim fi2 As FileInfo = New FileInfo(Path.Combine(toDir, fi1.Name))
                If fi2.Exists = False Then Return True

                If fi1.LastWriteTime <> fi2.LastWriteTime Then
                    sw.WriteLine(String.Format("(NON EXE) *FAILED* LastWriteTime Test) 'File {0} LastWriteTime {1}' <> File {2} LastWriteTime {3}", fi1.Name, fi1.LastWriteTime, fi2.Name, fi2.LastWriteTime))
                    sw.WriteLine(String.Format("(NON EXE) *FAILED* CreationTime  Test) 'File {0} CreationTime  {1}' <> File {2} CreationTime  {3}", fi1.Name, fi1.CreationTime, fi2.Name, fi2.CreationTime))

                    Return True
                Else
                    sw.WriteLine(String.Format("(NON EXE) *PASSED* LastWriteTime Test) 'File {0} LastWriteTime {1}' <> File {2} LastWriteTime {3}", fi1.Name, fi1.LastWriteTime, fi2.Name, fi2.LastWriteTime))
                End If

                If fi1.Length <> fi2.Length Then
                    sw.WriteLine(String.Format("(NON EXE) *FAILED*        Length Test) 'File {0} Length {1}' <> File {2} Length {3}", fi1.Name, fi1.Length, fi2.Name, fi2.Length))
                    Return True
                Else
                    sw.WriteLine(String.Format("(NON EXE) *PASSED*        Length Test) 'File {0} Length {1}' <> File {2} Length {3}", fi1.Name, fi1.Length, fi2.Name, fi2.Length))
                End If
            End If

            Return False
        Catch
            Return false
        End Try

    End Function

    Public Function GetUpdaterObj() As DOUpdater
        Dim u As New XPCollection(Of DOUpdater)
        If u.Count > 1 Then
            MessageBox.Show("There needs to be one and only one record in the DOUpdater Table")
            Application.Exit()
        End If

        If u.Count = 1 Then
            Return u(0)
        Else
            Return Nothing
        End If

    End Function

    Public Class DOUpdater
    Inherits XPObject

    #Region "Fields"

    Private _CurAppVer As String
    Private _UpdateMessage As String
    Private _UpdatePath As String
    Private _UpdateVer As Integer

    #End Region 'Fields

    #Region "Constructors"

    Public Sub New()
        MyBase.New()
        ' This constructor is used when an object is loaded from a persistent storage.
        ' Do not place any code here.
    End Sub

    Public Sub New(ByVal session As Session)
        MyBase.New(session)
        ' This constructor is used when an object is loaded from a persistent storage.
        ' Do not place any code here.
    End Sub

    #End Region 'Constructors

    #Region "Properties"

    Public Property CurAppVer() As String
        Get
            Return _CurAppVer
        End Get
        Set(ByVal value As String)
            _CurAppVer = value
        End Set
    End Property

    <Size(1000)> _
    Public Property UpdateMessage() As String
        Get
            Return _UpdateMessage
        End Get
        Set(ByVal value As String)
            _UpdateMessage = value
        End Set
    End Property

    <Size(255)> _
    Public Property UpdatePath() As String
        Get
            Return _UpdatePath
        End Get
        Set(ByVal value As String)
            _UpdatePath = value
        End Set
    End Property

    Public Property UpdateVer() As Integer
        Get
            Return _UpdateVer
        End Get
        Set(ByVal value As Integer)
            _UpdateVer = value
        End Set
    End Property

    #End Region 'Properties

    #Region "Methods"

    Public Overrides Sub AfterConstruction()
        MyBase.AfterConstruction()
        ' Use this constructor when you want to create a new object.
        ' Place here your initialization code.
    End Sub

    Public Function IncrementUpdateVer() As DOUpdater
        If UpdateVer < 0 Then
            UpdateVer = 1
        Else
            UpdateVer += 1
        End If

        Return Me
    End Function

End Class
    #End Region 'Methods
    
    Private Sub ConnectToDataBase()
        Dim fm As New System.Configuration.ExeConfigurationFileMap
        fm.ExeConfigFilename = ".\ENChartCAC.exe.config"

        Dim config As Configuration = ConfigurationManager.OpenMappedExeConfiguration(fm, ConfigurationUserLevel.None)
        Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString

        cs = cs.Replace("Asa9","Asa")
        cs = cs.Replace("Asa17","Asa")

       
        If cs.ToUpper.Contains("INITIAL CATALOG") Then
            cs += ";User Id=ICAdmin;Password=Ibywftptt$14"
        else If cs.toupper().contains("XPOPROVIDER")  then
            cs += ";uid=dba;pwd=sql"
        end if
                
        XpoDefault.DataLayer = XpoDefault.GetDataLayer(cs, AutoCreateOption.DatabaseAndSchema)
    End Sub

    Private Sub Timer1_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles Timer1.Tick
        Timer1.Stop()
        '09.15.09 I can't fathom why i'm doing this here.... in a timer event...  must of been some sort of refresh timing thing, huh...
        Me.DoSomeWork()
    End Sub
End Class

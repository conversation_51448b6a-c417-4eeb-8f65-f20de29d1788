﻿if NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = object_id(N'[dbo].[usp_overnight]') AND OBJECTPROPERTY(object_id, N'IsProcedure') = 1)
BEGIN
	-- If the Stored Procedure doesn't exist, first create a simple version so that it can be altered later in the script
	DE<PERSON><PERSON><PERSON> @strSQL nvarchar(200);
	SELECT @strSQL='CREATE PROC [dbo].[usp_overnight] as SELECT 1';
	EXEC sp_executesql @strSQL;
END

/* IC 2018 Release - Check for an Intubation column and create it if it doesn't exist */
IF NOT EXISTS(SELECT 1 FROM sys.columns WHERE Name = N'Intubation' AND Object_ID = Object_ID(N'DBO.Overnight_Data'))
BEGIN
	ALTER TABLE dbo.Overnight_Data ADD Intubation VARCHAR(25) NULL
END
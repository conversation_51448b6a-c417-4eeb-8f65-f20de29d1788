Public Sub Main()
    CreateVicFolderIfNeeded()
    CreateSharedDataSource()
End Sub

Private Sub CreateSharedDataSource()
    Dim folder As String = PARENTFOLDER
    Dim dbserver As String = DATABASESERVER
    Dim data As String = DATABASE
    Dim parent As String = "/" + folder
    Dim overwrite As Boolean = True
    Dim reportContents As Byte() = Nothing
    Dim warnings As Warning() = Nothing

    Dim definition As New DataSourceDefinition()
    definition.CredentialRetrieval = CredentialRetrievalEnum.Integrated
    definition.ConnectString = "Data Source=" + dbserver + ";Initial Catalog=" + data
    definition.CredentialRetrieval = CredentialRetrievalEnum.Store
    definition.Enabled = True
    definition.EnabledSpecified = True
    definition.Extension = "SQL"
    definition.ImpersonateUserSpecified = False
    definition.Prompt = Nothing
    definition.WindowsCredentials = False
    definition.UserName = "VICReportViewer"
    'definition.Password = "ICAdmin"

    Try
        rs.CreateDataSource("VICShared", parent, True, definition, Nothing)
    Catch ex As SoapException
        Console.WriteLine(ex.Detail.InnerXml.ToString())
    End Try
End Sub


Private Sub CreateVicFolderIfNeeded() ', rs As ReportingService2010)
    Dim folder As String = PARENTFOLDER
    'Common CatalogItem properties
    Dim descprop As New [Property]
    descprop.Name = "Description"
    descprop.Value = ""
    Dim hiddenprop As New [Property]
    hiddenprop.Name = "Hidden"
    hiddenprop.Value = "False"

    Dim items As CatalogItem() = rs.ListChildren("/", False)
    Dim catItem As CatalogItem
    Dim found As Boolean = False
    For Each catItem In items
        If catItem.Name = folder Then
            found = True
        End If
    Next

    If Not found Then
        Dim newProp As New [Property]()
        newProp.Name = "Application"
        newProp.Value = "VIC"
        Dim foldProps(0) As [Property]
        foldProps(0) = newProp
        'Create the "Ventus Intelligent Coding" folder on reports server
        rs.CreateFolder(folder, "/", foldProps)
    End If
End Sub
﻿**************  (v. 3.0.0.0) 2016-03-31
	* US37244: SAP 17 Upgrade - Upgrade ColdFeedExport Project
	* Upgraded solution to .NET 4.0
	* Upgraded DevExpress to v15.2
	
**************  (v. 2.1.1.0) 2015-11-18
	* Reversioning to correspond with MIC release version

**************  (v. 1.0.1.4) 2013-11-08
	* US5690: WI 1866: MIC Image Feed - Custom Index File Output for OnBase
	* Need to expand the capability of the creation of index files. The two main pieces of this are 1) Add new methods to setup and wrap up the work a 
	  plug-in will do, and 2) Add the capability for each plug-in to be able to load settings and values that are tied to the plug-in and not the application
		IExportChargeSummaryDetailsFile.vb
			- Removed existing, unused properties and methods (e.g. Init)
			- Added new methods, Initialize and WrapUp, that will be called before and after (respectively) the batch of charts is processed so setup and
			  tear down work can be performed if necessary (~lines 7, 12)
		ColdFeedHelper.vb
			- Added a new Dictionary(Of String, String) variable, _CustomSettingsDictionary, that will be used to hold settings loaded from the Export Maps
			  XML file (~line 40)
			- Added a corresponding property for _CustomSettingsDictionary (~lines 157-164)
			- Added calls to the new Initialize and WrapUp methods added to the IExportChargeSummaryDetailsFile interface. These calls are made before and 
			  after the batch of charts is processed (~lines 285, 317)
			- Changed function GetMappedFieldsDict (returns a dictionary) to GetMappedFieldValuePairs (returns a list) so that the order of the mapped field-
			  value pairs can be guaranteed. Made several changes throughout this function to accomodate the new return type (~lines 322-323, 335, 379, 381, 
			  388, 399)
			- Added a new option, IMAGEFILENAME, that can be used in the Export Maps XML file to output the corresponding image's filename (~lines 389-390)
			- The ExportChartDetail function now logs the exceptions ToString instead of Message when an exception is caught (~line 423)
			- In InitColdFeedMap, added a new variable cff_maps of type ColdFeedFieldMaps that will be used in a slight change to the way ColdFeedMappingsDict
			  is called (~lines 435-437)
			- In InitColdFeedMap, load the custom settings from the Export Maps XML file (~lines 468-477)
			- Changed function GetFieldMapDictFromXmlFile to LoadXmlFile to simplify the job it does to just loading the ColdFeedFieldMaps from the Export
			  Maps XML file (~lines 487-510)
			- Added new function, GetFieldMapsDictionary, that loads the field-value mappings read in from the Export Maps XML file into a dictionary keyed 
			  on facility id (~lines 512-520)
			- Added new function, GetCustomSettingsDictionary, that loads the custom settings read in from the Export Maps XML file into a dictionary keyed
			  on facility id (~lines 522-530)
			- Added new function, GetCustomSettingValue, that checks the current facility's custom settings for an entry with the passed key. If it exists
			  return the value, otherwise return an empty string. This function was created so that a plug-in looking for a custom setting won't have to
			  be concerned with missing key errors (~lines 532-538)
			- Added new class, CustomSetting, that represents a key-value pair for a setting and defines the way they'll be serialized and deserialzed from
			  the Export Maps XML file (~lines 543-546)
			- Added new class, MapFieldValuePair, that represents a field-value pair for index file mappings read in from the Export Maps XML file (~lines 
			  548-556)
			- Added a property, CustomSettings, to the ColdFeedFieldMap class that will hold the custom settings read in from the Export Maps XML file
			  (~line 564)
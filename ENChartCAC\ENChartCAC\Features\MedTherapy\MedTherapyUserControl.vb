﻿Imports System.ComponentModel
Imports AIC.SharedData
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraEditors.Repository
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports EnchartDOLib

Public Class MedTherapyUserControl
    Implements ICStatusChanged

    Public Event ErrorStatusChanged(ByVal sender As Object, ByVal e As ErrorStatusChangedEventArgs) Implements ICStatusChanged.ErrorStatusChanged
    Public Event ListChanged(sender As Object, e As ListChangedEventArgs) Implements ICStatusChanged.ListChanged
    Public Event InfusionMedicationChanged(sender As Object, e As InfusionMedicationChangedEventArgs) Implements ICStatusChanged.InfusionMedicationChanged
    ' Private Event ICStatusChanged_ErrorStatusChanged(sender As Object, e As ErrorStatusChangedEventArgs)
#Region "Properties"

    Private _obsMode As Boolean
    Public Property ObsMode As Boolean
        Get
            Return _obsMode
        End Get
        Set(value As Boolean)
            If _obsMode <> value Then
                _obsMode = value
                Debug.WriteLine($"ObsMode changed to {value}, updating validation ranges")
                UpdateValidDateRanges()
            End If
        End Set
    End Property

    Public ReadOnly Property TherapyList As ErrorAwareBindingList(Of MedTherapyBindable)
        Get
            Return _therapyList
        End Get
    End Property

    Public ReadOnly Property HasErrors As Boolean
        Get
            Return _therapyList.HasErrors
        End Get
    End Property

    Public Property HasData As Boolean Implements ICStatusChanged.HasData
        Get
            Return _therapyList.Count > 0
        End Get
        Set(value As Boolean)
            '    Throw New NotImplementedException()
        End Set
    End Property

    Public Property IsValid As Boolean Implements ICStatusChanged.IsValid
        Get
            Return HasErrors = False
        End Get
        Set(value As Boolean)
            ' _isValid = value
        End Set
    End Property

    Public Property WorkingDay As DateTime
        Get
            If _lastDateTime Is Nothing Then
                _lastDateTime = DateTime.Now
            End If
            'return the date part only
            Return _lastDateTime.Value.Date


        End Get
        Set(value As DateTime)
            _lastDateTime = value
            Debug.WriteLine($"WorkingDay:{_lastDateTime} changed")
        End Set
    End Property

    Private _validDateRangeProvider As IValidDateRangeProvider
    Public Property ValidDateRangeProvider() As IValidDateRangeProvider
        Get
            Return _validDateRangeProvider
        End Get
        Set(ByVal value As IValidDateRangeProvider)
            _validDateRangeProvider = value
            If _validDateRangeProvider Is Nothing Then Return
            AddHandler _validDateRangeProvider.ValidDateRangeChanged, AddressOf OnValidDateRangeChanged
            ' UpdateValidDateRanges()
        End Set
    End Property

    Private Sub OnValidDateRangeChanged(sender As Object, e As EventArgs)
        UpdateValidDateRanges()
    End Sub

    Public Sub UpdateValidDateRanges()
        If _validDateRangeProvider Is Nothing Then Return

        If ObsMode = False Then
            _therapyList.MinValidDate = _validDateRangeProvider.GetValidFacilityStartDate()
            _therapyList.MaxValidDate = _validDateRangeProvider.GetValidFacilityEndDate()
            Debug.WriteLine($"Updated MedTherapy validation range (Facility): {_therapyList.MinValidDate} to {_therapyList.MaxValidDate}")
        Else
            _therapyList.MinValidDate = _validDateRangeProvider.GetValidObsStartDate()
            _therapyList.MaxValidDate = _validDateRangeProvider.GetValidObsEndDate()
            Debug.WriteLine($"Updated MedTherapy validation range (Obs): {_therapyList.MinValidDate} to {_therapyList.MaxValidDate}")
        End If

        ' Validate all items with the new date ranges
        _therapyList.ValidateAllItems()

        ' Refresh the grid to immediately show validation states
        GridView1.RefreshData()
        GridControl1.Refresh()
        ' Removed Application.DoEvents() as it can lead to re-entrancy issues
    End Sub
#End Region 'Properties
#Region "Constructors"
    Public Sub New()
        InitializeComponent()

        AddHandler _therapyList.ListChanged, AddressOf OnListChanged
        AddHandler _therapyList.ErrorStatusChanged, AddressOf OnErrorStatusChanged

        GridControl1.DataSource = _therapyList
        GridView1.OptionsView.ShowAutoFilterRow = False
        GridView1.OptionsBehavior.Editable = True
        ' Set this property to True to show grouped columns within the grid view
        GridView1.OptionsView.ShowGroupedColumns = True

        'RepositoryItemGridLookUpEdit1.DataSource = MedicationsList 'New List 'MedicationBindingList()
        RepositoryItemGridLookUpEdit1.DisplayMember = "Name"
        RepositoryItemGridLookUpEdit1.ValueMember = "MedId"

        SetMedicationsLookupProperties(RepositoryItemGridLookUpEdit1)
        RepositoryItemGridLookUpEdit1.DataSource = MedicationsList 'New List 'MedicationBindingList()

        'RaiseEvent InfusionMedicationChanged(Me, New InfusionMedicationChangedEventArgs("MedTherapy", "doesn't matter"))
    End Sub

#End Region 'Constructors
#Region "Fields"
    Private _therapyList As New ErrorAwareBindingList(Of MedTherapyBindable)()
    Private _isValid As Boolean
    Public MedicationsList As New List(Of SimpleMedRec)

    Private _lastDateTime As DateTime? ' = DateTime.Now
#End Region 'Fields

    'iterate through the list and determine if any infusion medications are critical care (CC)
    Public Function HasCCMedications() As Boolean
        Dim hasCCMeds As Boolean = False
        For Each med In _therapyList
            If med.TherapyType = MedTherapyType.Infusion Then
                'use MedicationsList to determine if the medication is a CC med
                Dim medRec = MedicationsList.Find(Function(m) m.MedName = med.Medication)
                If medRec IsNot Nothing AndAlso medRec.CC Then
                    hasCCMeds = True
                    Exit For
                End If
            End If
        Next
        Return hasCCMeds
    End Function

    Private Function ICStatusChanged_Validate() As Boolean Implements ICStatusChanged.Validate
        Return IsValid()
    End Function

    Private Sub OnErrorStatusChanged(sender As Object, e As ErrorStatusChangedEventArgs)
        _isValid = Not e.IsError
        RaiseEvent ErrorStatusChanged(Me, New ErrorStatusChangedEventArgs(e.IsError))
    End Sub

    Private Sub OnListChanged(sender As Object, e As ListChangedEventArgs)
        RaiseEvent ListChanged(Me, e)
    End Sub

    'Private Sub UpdateGroupControlHeader()
    '    Dim hasErrors As Boolean = False
    '    For Each row In _therapyList
    '        If row.RowHasErrors() Then
    '            hasErrors = True
    '            'Exit For
    '        End If
    '    Next

    '    'If hasErrors Then
    '    '    GroupControl1.AppearanceCaption.ForeColor = Color.Red
    '    '    GroupControl1.Text = "Data (Errors Found!)"
    '    'Else
    '    '    GroupControl1.AppearanceCaption.ForeColor = Color.Black
    '    '    GroupControl1.Text = "Data"
    '    'End If
    'End Sub

    ''' <summary>
    ''' Used To Apply Working Day to the Start Date and End Date Columns
    '''     ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Sub GridView1_FocusedColumnChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs) Handles GridView1.FocusedColumnChanged
        Dim view As GridView = CType(sender, GridView)
        Dim focusedRowHandle As Integer = view.FocusedRowHandle
        Dim focusedColumn As GridColumn = view.FocusedColumn

        ' Check if the focused column is "StartDate"
        If focusedColumn IsNot Nothing AndAlso focusedColumn.FieldName = "StartDate" Then
            ' Check if the current value is null
            If IsDBNull(view.GetRowCellValue(focusedRowHandle, "StartDate")) OrElse view.GetRowCellValue(focusedRowHandle, "StartDate") Is Nothing Then
                view.SetRowCellValue(focusedRowHandle, "StartDate", WorkingDay)
                'Debug.WriteLine($"Focused Column Changed: Row {focusedRowHandle}, Column {focusedColumn.FieldName}")
            End If
        End If

        'If focusedColumn IsNot Nothing AndAlso (focusedColumn.Name = "colEndDate" Or focusedColumn.Name = "colEndTime") Then
        If focusedColumn IsNot Nothing AndAlso (focusedColumn.Name = "colEndDate") Then
            Dim therapytype As MedTherapyType = view.GetRowCellValue(focusedRowHandle, "TherapyType")
            If Not (therapytype = MedTherapyType.Injection Or therapytype = MedTherapyType.Immunization) Then
                ' Check if the current value is null
                If IsDBNull(view.GetRowCellValue(focusedRowHandle, "EndDate")) OrElse view.GetRowCellValue(focusedRowHandle, "EndDate") Is Nothing Then
                    view.SetRowCellValue(focusedRowHandle, "EndDate", WorkingDay)
                    'Debug.WriteLine($"Focused Column Changed: Row {focusedRowHandle}, Column {focusedColumn.FieldName}")
                End If
            End If
        End If
    End Sub

    Private Sub GridView1_ShownEditor(sender As Object, e As EventArgs) Handles GridView1.ShownEditor
        Dim view As GridView = CType(sender, GridView)
        Dim focusedRowHandle As Integer = view.FocusedRowHandle
        Dim focusedColumn As GridColumn = view.FocusedColumn

        If focusedRowHandle <> DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
            Return
        End If

        ' If the editor is shown for the first time and the value is empty, set the default value
        If focusedColumn IsNot Nothing AndAlso focusedColumn.FieldName = "StartDate" Then
            Dim editor As BaseEdit = CType(view.ActiveEditor, BaseEdit)
            If editor IsNot Nothing AndAlso String.IsNullOrEmpty(editor.EditValue?.ToString()) Then
                editor.EditValue = WorkingDay
            End If
        End If

        ' Similar logic for "EndDate"
        If focusedColumn IsNot Nothing AndAlso (focusedColumn.Name = "colEndDate") Then
            Dim editor As BaseEdit = CType(view.ActiveEditor, BaseEdit)
            If editor IsNot Nothing AndAlso String.IsNullOrEmpty(editor.EditValue?.ToString()) Then
                editor.EditValue = WorkingDay
            End If
        End If
    End Sub

    Private Sub GridView1_FocusedRowChanged(sender As Object, e As FocusedRowChangedEventArgs) Handles GridView1.FocusedRowChanged
        DisableRowCells()
    End Sub
    Private Sub gridView1_InitNewRow(sender As Object, e As InitNewRowEventArgs) Handles GridView1.InitNewRow
        ResetAllRowCells()
    End Sub

    Private Sub ResetAllRowCells()
        Dim view As GridView = GridView1 'TryCast(sender, GridView)
        If view Is Nothing Then Exit Sub

        Dim focusedRowHandle As Integer = view.FocusedRowHandle
        'Test if there is a focused row
        If focusedRowHandle < 0 Then
            Return
        End If

        DisableColumn("colEndDate", False)
        DisableColumn("colEndTime", False)
        DisableColumn("colMedication", False)
        DisableColumn("colRoute", False)
        DisableColumn("colSite", False)
    End Sub

    Private Sub DisableRowCells()
        Dim view As GridView = GridView1 'TryCast(sender, GridView)
        If view IsNot Nothing Then
            Dim focusedRowHandle As Integer = view.FocusedRowHandle
            'Test if there is a focused row
            If focusedRowHandle < 0 Then
                If focusedRowHandle <> DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
                    Return
                End If
            End If
            'Dim focusedColumn As GridColumn = view.FocusedColumn
            Dim therapytype As MedTherapyType = view.GetRowCellValue(focusedRowHandle, "TherapyType")
            ResetAllRowCells()
            Select Case therapytype
                Case MedTherapyType.Hydration
                    DisableColumn("colEndDate", False)
                    DisableColumn("colEndTime", False)
                    DisableColumn("colMedication", True)
                    DisableColumn("colRoute", False)
                    DisableColumn("colSite", False)

                Case MedTherapyType.Injection
                    DisableColumn("colEndDate", True)
                    DisableColumn("colEndTime", True)
                    DisableColumn("colMedication", False)
                    DisableColumn("colRoute", False)
                    DisableColumn("colSite", True)

                Case MedTherapyType.Infusion
                    DisableColumn("colEndDate", False)
                    DisableColumn("colEndTime", False)
                    DisableColumn("colMedication", False)
                    DisableColumn("colRoute", False)
                    DisableColumn("colSite", False)

                Case MedTherapyType.Immunization
                    DisableColumn("colEndDate", True)
                    DisableColumn("colEndTime", True)
                    DisableColumn("colMedication", False)
                    DisableColumn("colRoute", True)
                    DisableColumn("colSite", True)

                Case Else
                    DisableColumn("colEndDate", False)
                    DisableColumn("colEndTime", False)
                    DisableColumn("colMedication", False)

                    'Dim col = view.Columns("Medication")
                    'If col IsNot Nothing Then
                    '    col.OptionsColumn.AllowFocus = True

                    'End If
            End Select
        End If
    End Sub


    Private Sub DisableColumn(columnName As String, disable As Boolean)
        Dim col = GridView1.Columns.ColumnByName(columnName)
        If col IsNot Nothing Then
            col.OptionsColumn.AllowFocus = Not disable
            'clear the value
            If disable Then
                '    GridView1.SetRowCellValue(GridView1.FocusedRowHandle, col, Nothing)
            End If
        End If
    End Sub


    ''' <summary>
    ''' Fires for every visible Grid cell before this cell is shown. Allows you to modify Appearance settings for this cell.
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        'Debug.WriteLine("RowCellStyle")
        Dim view As GridView = TryCast(sender, GridView)
        If view Is Nothing Then Exit Sub
        Dim MedTherapy As MedTherapyBindable = CType(view.GetRow(e.RowHandle), MedTherapyBindable)
        If MedTherapy Is Nothing Then Exit Sub
        Dim disabledColor As Color = Color.WhiteSmoke 'Color.Gainsboro 'Color.LightGray
        Select Case MedTherapy.TherapyType

            Case MedTherapyType.Immunization
                Select Case e.Column.Name
                    Case "colRoute", "colEndDate", "colEndTime", "colRate", "colSite", "colDuration"
                        e.Appearance.BackColor = disabledColor
                End Select

            Case MedTherapyType.Hydration
                Select Case e.Column.Name
                    Case "colMedication", "colDuration"
                        e.Appearance.BackColor = disabledColor
                End Select
            Case MedTherapyType.Infusion
                Select Case e.Column.Name
                    Case "colRate", "colDuration"
                        e.Appearance.BackColor = disabledColor
                End Select
            Case MedTherapyType.Injection
                Select Case e.Column.Name
                    Case "colEndDate", "colEndTime", "colRate", "colSite", "colDuration"
                        e.Appearance.BackColor = disabledColor
                    Case Else
                End Select
        End Select

        'If e.RowHandle >= 0 Then
        '    Dim rowObject = TryCast(view.GetRow(e.RowHandle), MedTherapyBindable) ' IBindableHydrationModel)

        '    ' Check if the rowObject has any errors
        '    If rowObject IsNot Nothing AndAlso Not rowObject?.RowHasErrors() Then
        '        e.Appearance.ForeColor = Color.Blue
        '    End If
        'End If
    End Sub

    ''' <summary>
    ''' Allows you to assign custom in-place editors to individual cells. To avoid performance issues
    ''' and increased memory consumption, assign repository items that already exist in the 
    ''' GridControl.RepositoryItems collection. Do not create new repository items in this handler.
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Sub GridView1_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEdit
        'ECLog.WriteEntry("CustomRowCellEdit", TraceEventType.Verbose)
        '   Debug.WriteLine("CustomRowCellEdit")
        If e.RowHandle = DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
        Else
            If e.RowHandle < 0 Then
                Exit Sub
            End If
        End If

        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = CType(sender, DevExpress.XtraGrid.Views.Grid.GridView)
        Dim therapytype As MedTherapyType = view.GetRowCellValue(e.RowHandle, "TherapyType")

        Dim disabledRepo = RepositoryItemHypertextLabel1
        Select Case therapytype
            Case MedTherapyType.Immunization
                Select Case e.Column.Name
                    Case "colRoute", "colEndDate", "colRate", "colSite"
                        e.RepositoryItem = disabledRepo
                    Case "colMedication"
                        e.RepositoryItem = RepositoryItemImmunizations
                End Select
            Case MedTherapyType.Hydration
                Select Case e.Column.Name
                    Case "colMedication"
                        e.RepositoryItem = disabledRepo
                    Case "colRoute"
                        e.RepositoryItem = RepositoryItemRouteInfusions
                End Select
            Case MedTherapyType.Infusion
                Select Case e.Column.Name
                    Case "colRate"
                        e.RepositoryItem = disabledRepo
                    Case "colRoute"
                        e.RepositoryItem = RepositoryItemRouteInfusions
                    Case "colMedication"
                        e.RepositoryItem = RepositoryItemGridLookUpEdit1
                End Select
            Case MedTherapyType.Injection
                Select Case e.Column.Name
                    Case "colEndDate", "colRate", "colSite"
                        e.RepositoryItem = disabledRepo
                    Case "colRoute"
                        e.RepositoryItem = RepositoryItemRouteInjection
                    Case "colMedication"
                        e.RepositoryItem = RepositoryItemGridLookUpEdit1
                End Select

        End Select
    End Sub

    ''' <summary>
    ''' Allows you to assign a custom editor to a column for in-place editing and so override the default column editor, 
    ''' which is by default used both in display and edit modes. To avoid performance issues and increased memory consumption, 
    ''' assign repository items that already exist in the GridControl.RepositoryItems collection. Do not create new
    ''' repository items in this handler. This event also allows you to change editors within an Edit Form.
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEditForEditing

    End Sub

    Private Sub DateEdit_ButtonClick(ByVal sender As Object, ByVal e As ButtonPressedEventArgs) Handles RepositoryItemDateEdit1.ButtonClick
        Dim dateEdit As DateEdit = TryCast(sender, DateEdit)
        If dateEdit.IsPopupOpen Then
            dateEdit.ClosePopup()
        Else
            If e.Button.Tag IsNot Nothing Then
                dateEdit.GetPopupEditForm()?.Dispose() ' Dispose popup form to recreate based on CalendarView
                If e.Button.Tag.Equals("CalendarTouch") Then
                    dateEdit.Properties.CalendarView = CalendarView.TouchUI
                ElseIf e.Button.Tag.Equals("CalendarClassic") Then
                    dateEdit.Properties.CalendarView = CalendarView.Classic
                End If
                dateEdit.ShowPopup()
            End If
        End If
    End Sub

    Private Property LoadingTherapyRecords As Boolean = False
    Friend Sub LoadMedTherapyRecords(chart As DOChart)
        _therapyList.Clear()

        For Each itemMed In chart.MedTherapies
            If ObsMode Then
                If Not itemMed.IsObs Then Continue For
            Else
                If itemMed.IsObs Then Continue For
            End If
            ' If itemMed.IsObs Then Continue For
            Dim medTherapy As New MedTherapyBindable()
            medTherapy.Is_obs = itemMed.IsObs
            medTherapy.TherapyType = itemMed.TherapyType
            medTherapy.StartDate = itemMed.StartDate
            medTherapy.EndDate = itemMed.EndDate
            medTherapy.Medication = itemMed.Medication
            medTherapy.Route = itemMed.Route
            medTherapy.Site = itemMed.Site
            medTherapy.Rate = itemMed.Rate
            medTherapy.StartTime = itemMed.StartTime
            medTherapy.EndTime = itemMed.EndTime
            _therapyList.Add(medTherapy)
        Next

        _therapyList.ValidateAllItems()
    End Sub

    Friend Sub ClearAll()
        _therapyList?.ClearAll()
    End Sub

#Region "GridLookupEdit"
    Private _medicationPopupWindowSize As Size?
    Private Sub MedicationClosePopUp(sender As Object, e As EventArgs)
        Dim glue = DirectCast(sender, GridLookUpEdit)
        Dim popup As DevExpress.XtraEditors.Popup.PopupGridLookUpEditForm = glue.GetPopupEditForm
        _medicationPopupWindowSize = popup.Size

        Try
            AICUserSpecificSettingsHelper.Instance.MedicationsPopUpSize = popup.Size
        Catch ex As Exception

            ECLog.WriteExceptionError($"AICUserSpecificSettingsHelper.StoreAICUserSpecificSettings Error: {ex}", ex)
        End Try
    End Sub

    ' Private _medicationPopupWindowSize As Size?
    Private Sub MedicationQueryPopUp(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles RepositoryItemGridLookUpEdit1.QueryPopUp

        If AICUserSpecificSettingsHelper.IsInstanceNull() Then
            _medicationPopupWindowSize = Nothing
        End If
        If _medicationPopupWindowSize.HasValue = False Then
            Try
                '_medicationPopupWindowSize = AICUserSpecificSettingsHelper.RetrieveAICUserSpecificSettings()?.MedicationsPopUpSize
                _medicationPopupWindowSize = AICUserSpecificSettingsHelper.Instance.MedicationsPopUpSize
            Catch ex As Exception
                ECLog.WriteExceptionError($"AICUserSpecificSettingsHelper.StoreAICUserSpecificSettings Error: {ex}", ex)
                Exit Sub
            End Try
        End If
        Dim editor As GridLookUpEdit = DirectCast(sender, GridLookUpEdit)
        editor.Properties.PopupFormSize = _medicationPopupWindowSize
    End Sub

    Private Sub MedicationEditValueChanged(ByVal sender As Object, ByVal e As EventArgs) 'Implements ICStatusChanged.ListChanged
        ' IsValidMedication(Of sender, New CancelEventArgs) 'is this really needed?

        If ObsMode Then Exit Sub 'No need to check for cc medications in Obs Mode
        Dim cellMedication As String = DirectCast(sender, GridLookUpEdit).EditValue
        Dim view As GridView = GridView1
        Dim focusedRowHandle As Integer = view.FocusedRowHandle
        Dim focusedColumn As GridColumn = view.FocusedColumn
        Dim therapytype As MedTherapyType = view.GetRowCellValue(focusedRowHandle, "TherapyType")
        'Dim cellMedication As String = view.GetRowCellValue(focusedRowHandle, "Medication")
        Dim rowObject = TryCast(view.GetRow(focusedRowHandle), MedTherapyBindable)
        If rowObject Is Nothing Then Exit Sub
        If rowObject.TherapyType = MedTherapyType.Infusion Then
            Debug.WriteLine($"We got CC Meds:{rowObject.Medication}!!!")
            RaiseEvent InfusionMedicationChanged(Me, New InfusionMedicationChangedEventArgs("MedTherapy", cellMedication))
        End If
    End Sub

    Private Sub IsValidMedication(sender As Object, e As CancelEventArgs) 'Handles Medication_InjectionsMed01_cbo.Validating
        Dim c = DirectCast(sender, GridLookUpEdit)
        Dim ml = DirectCast(c.Properties.DataSource, List(Of SimpleMedRec))
        c.ErrorText = ""
        If c.EditValue IsNot Nothing Then
            If c.EditValue = "" Then
                Return
            End If
            For Each med In ml
                If med.MedName = c.EditValue Then
                    Return
                End If
            Next
            e.Cancel = True

            c.ErrorText = $"{c.EditValue} Is not a valid medication"
        End If

    End Sub

    Private Sub SetMedicationsLookupProperties(repoItemLookUpEdit As RepositoryItemGridLookUpEdit)
        repoItemLookUpEdit.PopupWidthMode = PopupWidthMode.UseEditorWidth
        repoItemLookUpEdit.BestFitMode = BestFitMode.BestFit
        repoItemLookUpEdit.DisplayMember = "MedName"
        repoItemLookUpEdit.ValueMember = "MedName"
        repoItemLookUpEdit.PopupFilterMode = PopupFilterMode.Contains
        repoItemLookUpEdit.View.OptionsView.ShowAutoFilterRow = False
        repoItemLookUpEdit.ImmediatePopup = True
        repoItemLookUpEdit.AllowNullInput = True
        repoItemLookUpEdit.TextEditStyle = TextEditStyles.Standard
        repoItemLookUpEdit.ValidateOnEnterKey = True
        repoItemLookUpEdit.SearchMode = GridLookUpSearchMode.AutoSearch

        AddHandler repoItemLookUpEdit.EditValueChanged, AddressOf MedicationEditValueChanged
        AddHandler repoItemLookUpEdit.Validating, AddressOf IsValidMedication

        Try
            repoItemLookUpEdit.AcceptEditorTextAsNewValue = True
        Catch ex As Exception
            Debug.WriteLine($"SetMedicationsLookupProperties:{repoItemLookUpEdit.Name}")
        End Try
    End Sub

    Public Sub SetMedicationsList(medsList As List(Of SimpleMedRec))
        MedicationsList = medsList
        RepositoryItemGridLookUpEdit1.DataSource = MedicationsList 'New List 'MedicationBindingList()

    End Sub

    Private Sub MedTherapyUserControl_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'AddHandler RepositoryItemDateEdit1.EditValueChanged, AddressOf DateEditChanged
        AddHandler RepositoryItemDateEdit1.EditValueChanged, AddressOf DateEditChanged
        AddHandler RepositoryItemTimeEdit1.EditValueChanged, AddressOf TimeEditChanged
    End Sub

    Private Sub DateEditChanged(sender As Object, e As EventArgs)
        Dim dateEdit As DateEdit = TryCast(sender, DateEdit)
        UpdateLastDateTime(dateEdit.EditValue)
        Debug.WriteLine($"_lastDateTime:{_lastDateTime} changed")
    End Sub

    Private Sub TimeEditChanged(sender As Object, e As EventArgs)
        Dim timeEdit As TimeEdit = TryCast(sender, TimeEdit)
        'UpdateLastDateTime(timeEdit.EditValue)
        Debug.WriteLine($"_lastDateTime:{_lastDateTime} changed")
    End Sub

    Public Sub UpdateLastDateTime(newDateTime As DateTime)
        If newDateTime = Nothing OrElse newDateTime = DateTime.MinValue Then
            Return
        End If

        WorkingDay = newDateTime
        Debug.WriteLine($"UpdateLastDateTime: {newDateTime} updating validation ranges")
        UpdateValidDateRanges()
        _therapyList.ValidateAllItems()
    End Sub

    Private Sub GridView1_CellValueChanged(sender As Object, e As CellValueChangedEventArgs) Handles GridView1.CellValueChanged
        DisableRowCells()
    End Sub

    Private Sub ToolStripButton2_Click(sender As Object, e As EventArgs) Handles ToolStripButton2.Click
        _therapyList.Clear()
    End Sub
#End Region '"GridLookupEdit"

End Class

Public Interface ICStatusChanged
    Event ErrorStatusChanged(ByVal sender As Object, ByVal e As ErrorStatusChangedEventArgs)
    Event ListChanged(sender As Object, e As ListChangedEventArgs)
    Event InfusionMedicationChanged(sender As Object, e As InfusionMedicationChangedEventArgs)
    Property HasData As Boolean
    Property IsValid As Boolean
    Function Validate() As Boolean
End Interface



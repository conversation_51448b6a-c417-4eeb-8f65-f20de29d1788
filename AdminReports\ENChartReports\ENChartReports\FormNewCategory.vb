Imports EnchartDOLib

Public Class FormNewCategory

    Private Property category As DOFacilityReportCategory
    Public Event CategoryApplyChanges(ByVal category As DOFacilityReportCategory)

    Public Sub New(byval category As DOFacilityReportCategory)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        Me.category = category
        txtCategoryName.EditValue = Me.Category.CategoryName
    End Sub

    Private Sub OnCategoryApplyChanges(Byval category as DOFacilityReportCategory) 
        RaiseEvent CategoryApplyChanges(category)
    End Sub

    Private Sub btnAddCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddCategory.Click
        category.CategoryName = txtCategoryName.EditValue
        OnCategoryApplyChanges(category)
        DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub txtCategoryName_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtCategoryName.TextChanged
        btnAddCategory.Enabled = (txtCategoryName.Text <> "")
    End Sub

    Private Sub FormNewCategory_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Me.Dispose()
    End Sub
End Class
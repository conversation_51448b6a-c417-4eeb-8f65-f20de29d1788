﻿Imports System.Xml
Imports System.IO
Imports DevExpress.Xpo
Imports DevExpress.XtraGrid.Columns
Imports McKesson.HIC.ColdFeed

Public Class OneContentFilenameModule
    Implements IImageFeedModule

    Const MODULE_NAME As String = "OneContent Filename Image Feed"
    Const MODULE_DESCRIPTION As String = "Single documents exported with a formatted filename <VisitID>_<BatchLabel>_<DateOfService:yyyyMMddhhmm>.<FileType>"
    Const IMAGE_FEED_MODE As String = "CustomFilename"

    Public ReadOnly Property ModuleName As String Implements IImageFeedModule.ModuleName
        Get
            Return MODULE_NAME
        End Get
    End Property

    Public ReadOnly Property ModuleDescription As String Implements IImageFeedModule.ModuleDescription
        Get
            Return MODULE_DESCRIPTION
        End Get
    End Property

    Public ReadOnly Property ImageFeedMode As String Implements IImageFeedModule.ImageFeedMode
        Get
            Return IMAGE_FEED_MODE
        End Get
    End Property

    Public Function LoadConfiguration() As Boolean Implements IImageFeedModule.LoadConfiguration
        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeIndexPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""DetailsOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")
        Dim nodeDocumentType As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFileType""]/value")
        Dim nodeBatchLabel As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""BatchLabel""]/value")

        txtDocumentPath.Text = nodeDocumentPath.InnerText
        txtIndexPath.Text = nodeIndexPath.InnerText
        dteStartDate.DateTime = CDate(nodeStartDate.InnerText)
        cboDocumentType.Text = nodeDocumentType.InnerText
        txtBatchLabel.Text = nodeBatchLabel.InnerText

        Return True
    End Function

    Public Function LoadMappings() As Boolean Implements IImageFeedModule.LoadMappings
        ' No mappings to load/save for this module
        Return False
    End Function

    Public Function SaveConfiguration() As Boolean Implements IImageFeedModule.SaveConfiguration
        If String.IsNullOrEmpty(txtDocumentPath.Text) Then
            MessageBox.Show("Document Path Cannot Be Empty", "Invalid Document Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtDocumentPath.Select()
            Return False
        End If

        If String.IsNullOrEmpty(txtIndexPath.Text) Then
            MessageBox.Show("Index Path Cannot Be Empty", "Invalid Index Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtIndexPath.Select()
            Return False
        End If

        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeIndexPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""DetailsOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")
        Dim nodeDocumentType As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFileType""]/value")
        Dim nodeBatchLabel As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""BatchLabel""]/value")
        Dim nodeUseFullPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""UseFullPathPData""]/value")
        Dim nodeImageFeedMode As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFeedMode""]/value")

        nodeDocumentPath.InnerText = txtDocumentPath.Text
        nodeIndexPath.InnerText = txtIndexPath.Text
        nodeStartDate.InnerText = dteStartDate.DateTime.ToString("yyyy-MM-dd")
        nodeDocumentType.InnerText = cboDocumentType.Text
        nodeUseFullPath.InnerText = "False"
        nodeImageFeedMode.InnerText = ImageFeedMode

        xdoc.Save(".\ColdFeedExport.exe.config")

        Return True
    End Function

    Public Function SaveMappings() As Boolean Implements IImageFeedModule.SaveMappings
        ' No mappings to load/save for this module
        Return False
    End Function
End Class

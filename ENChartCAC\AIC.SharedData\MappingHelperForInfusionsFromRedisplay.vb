﻿Option Infer On
Imports AIC.SharedData
Imports AIC.SharedData.CuresAct

Namespace CuresAct
    Public Class MappingHelperForInfusionsFromRedisplay

        Public ReadOnly Property ObsEnabled As Boolean
        Private FacilityInfusionControlNamesList As List(Of List(Of String))
        Private ObsInfusionControlNamesList As List(Of List(Of String))

        Public Property InfusionsList As New List(Of PatientInfusion)
        Public ReadOnly Property redisplayDict As Dictionary(Of String, String)

        Public Sub New(redisplayDict As Dictionary(Of String, String), obsEnabled As Boolean)
            Me.ObsEnabled = obsEnabled
            InitControlNamesLists()
            Me.redisplayDict = redisplayDict
        End Sub


        Private Sub InitControlNamesLists()
            Dim controlNamesHelper As New InfusionControlNamesHelper()
            FacilityInfusionControlNamesList = controlNamesHelper.GetFacilityInfusionControlNamesList()
            ObsInfusionControlNamesList = controlNamesHelper.GetObsInfusionControlNamesList()
        End Sub

#Region "Build From Redisplay Records"

        Public Function GetInfusionsFromRedisplay() As List(Of PatientInfusion)
            InfusionsList.AddRange(CopyFacilityInfusionRedisplayDataToModels)
            If ObsEnabled Then
                InfusionsList.AddRange(CopyObsInfusionRedisplayDataToModels)
            End If
            Dim outlist = InfusionsList.Where(Function(Item) String.IsNullOrEmpty(Item.Medication) = False).ToList()
            Return outlist
        End Function

        Private Function GetControlValueByName(controlName As String) As String
            Dim outString As String = ""

            If redisplayDict.ContainsKey(controlName) Then
                outString = redisplayDict(controlName)
            End If

            Return outString
        End Function

        Private Function CopyFacilityInfusionRedisplayDataToModels() As List(Of PatientInfusion)
            Dim outInfusions As New List(Of PatientInfusion)
            Dim count As Short = 0
            For Each row In FacilityInfusionControlNamesList
                count += 1
                Dim infusion = CopyInfusionsRedisplayRowToModel(row)
                outInfusions.Add(infusion)
                'infusion.OriginalRow = count
            Next

            Return outInfusions
        End Function

        Private Function CopyObsInfusionRedisplayDataToModels() As List(Of PatientInfusion)

            Dim outInfusions As New List(Of PatientInfusion)
            If ObsEnabled = False Then
                Return outInfusions
            End If
            Dim count As Short = 0
            For Each row In ObsInfusionControlNamesList
                count += 1
                Dim infusion = CopyInfusionsRedisplayRowToModel(row)
                outInfusions.Add(infusion)
                'infusion.IsObs = True
                'infusion.OriginalRow = count
            Next

            Return outInfusions
        End Function

        Private Function CopyInfusionsRedisplayRowToModel(fieldNamesList As List(Of String)) As PatientInfusion
            Dim outInfusion As New PatientInfusion()
            Dim tempDate As Date

            If Date.TryParse(GetControlValueByName(fieldNamesList(0)), tempDate) Then
                outInfusion.Startdate = tempDate
            Else
                outInfusion.Startdate = Nothing
            End If

            outInfusion.StartTime = GetControlValueByName(fieldNamesList(1))

            If Date.TryParse(GetControlValueByName(fieldNamesList(2)), tempDate) Then
                outInfusion.EndDate = tempDate
            Else
                outInfusion.EndDate = Nothing
            End If

            outInfusion.EndTime = GetControlValueByName(fieldNamesList(3))
            outInfusion.Medication = GetControlValueByName(fieldNamesList(5))

            Return outInfusion
        End Function

#End Region
    End Class
End Namespace

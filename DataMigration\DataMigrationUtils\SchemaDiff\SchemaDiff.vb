﻿'Imports EnchartDOLib
Imports System.IO
'Imports DevExpress.Xpo
'Imports DevExpress.Xpo.DB
Imports ExportSchema
Imports System.Text
Imports ExportSchema.DBSchema

Module SchemaDiff

    Dim passedFileName As String = Nothing

    Public Sub Main(ByVal args As String())
        Try
            If args.Length > 0 Then
                passedFileName = args(0)
            End If

            Dim dbInfoSQL As DBSchema = LoadSqlSchemaFromFile()
            Dim dbInfoSybase As DBSchema = LoadSybaseSchemaFromFile()

            Comparefiles(dbInfoSQL, dbInfoSybase)
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Console.ReadKey()
        End Try

    End Sub

    Private outStringList As New List(Of String)

    Private Function AddString(osl As String) As String
        Console.WriteLine(osl)
        outStringList.Add(osl)
        Return osl
    End Function


    Sub Comparefiles(dbInfoSql As DBSchema, dbInfoSybase As DBSchema)
        'Find files that only exist in Sybase
        'Find fields that exist in sybase that are missing from SQL
        'Find fields that differ in type or length

        AddString("Tables in Sybase that are not in SQL ...")
        AddString("")
        For Each sybaseTi In dbInfoSybase.TableList
            Dim bfound = False
            For Each sqlTi In dbInfoSql.TableList
                If sqlTi.TableName.ToUpper = sybaseTi.TableName.ToUpper Then
                    bfound = True
                    Exit For
                End If
            Next
            If Not bfound Then
                If Not IsCdmTable(sybaseTi) Then
                    AddString($"{sybaseTi.TableName,-40} Missing")      ', sybaseTi.TableName))
                End If
            End If
        Next

        AddString("")
        'AddString("Press any key to continue...")
        'Console.ReadKey()

        AddString("")
        'AddString("Missing fields ...")
        AddString("Fields in Sybase that are not in SQL ...")
        AddString("")


        Dim sb As New StringBuilder()

        Dim typeSizeMismatchList As New List(Of String)
        Dim problems As Integer = 0
        For Each sybaseTi In dbInfoSybase.TableList
            Dim bfound = False
            For Each sqlTi In dbInfoSql.TableList
                If sqlTi.TableName = sybaseTi.TableName Then
                    bfound = True

                    For Each sybaseField In sybaseTi.fields
                        Dim bFieldFound = False
                        For Each sqlField In sqlTi.fields
                            If sybaseField.FieldName.ToUpper = sqlField.FieldName.ToUpper Then
                                bFieldFound = True
                                Dim typeSizeMismatchString = CheckForTypeOfSizeIssues(sybaseTi.TableName, sybaseField, sqlField)
                                'Debug.WriteLine($"TableName:{sybaseTi.TableName}")
                                If Not String.IsNullOrEmpty(typeSizeMismatchString) Then
                                    'AddString(typeSizeMismatchString)
                                    typeSizeMismatchList.Add(typeSizeMismatchString)
                                End If
                                Exit For
                            End If
                        Next
                        If Not bFieldFound Then
                            Dim fullName = $"{sybaseTi.TableName}.{sybaseField.FieldName}"
                            AddString($"{fullName,-40} Missing")

                        End If
                    Next
                    Exit For
                End If
            Next
        Next

        AddString("")
        ' AddString("Press any key to continue...")
        ' Console.ReadKey()

        AddString("")
        AddString("Field Type or Size Issues ...")
        AddString("")
        For Each line In typeSizeMismatchList
            'AddString(line)
            AddString(line)
        Next

        AddString("")
        Console.WriteLine("Press any key to continue...")
        Console.ReadKey()

        File.WriteAllLines("ExportDiff.txt", outStringList)
    End Sub

    Public Function IsCdmTable(sybaseFields As TableInfo) As Boolean 'tableName As String) As Boolean
        Dim cdmTableFieldList As New List(Of String)({"HCPCS", "CDM", "LONGNAME", "QUANTITY", "PHYSICIANCDM"})
        'Dim sybaseTableFieldList As List(Of String) = GetFieldListFromSybase(tableName)


        For Each field In sybaseFields.fields
            If cdmTableFieldList.Contains(field.FieldName.ToUpper) = False Then
                Return False
            End If
        Next

        If cdmTableFieldList.Count <> sybaseFields.FieldCount Then
            Return False
        End If
        Return True
    End Function

    Public Function buildTypeOrSizeIssuesString(tableName As String, sybaseField As FieldInfo, sqlField As FieldInfo) As String
        Debug.WriteLine($"TableName:{tableName}")
        Return String.Format("{0} - Sybase:{1}({2}), SQL:{3}({4})", tableName & "." & sqlField.FieldName,
                                                     sybaseField.FieldTypeName, sybaseField.FieldLength,
                                                     sqlField.FieldTypeName, sqlField.GetDBAdjustedFieldLength())
    End Function

    Public Function CheckForTypeOfSizeIssues(tableName As String, sybaseField As FieldInfo, sqlField As FieldInfo) As String
        Debug.WriteLine($"TableName:{tableName}")
        Dim returnString As String = Nothing
        Select Case sybaseField.FieldTypeName
            Case "varchar"
                If sqlField.FieldTypeName = "nvarchar" Then
                    If sybaseField.FieldLength <> sqlField.GetDBAdjustedFieldLength() Then
                        returnString = buildTypeOrSizeIssuesString(tableName, sybaseField, sqlField)
                    End If
                Else
                    returnString = buildTypeOrSizeIssuesString(tableName, sybaseField, sqlField)
                End If
            Case "text"
                If sqlField.FieldTypeName <> "ntext" Then 'ntext means nvarchar(max)
                    returnString = buildTypeOrSizeIssuesString(tableName, sybaseField, sqlField)
                    'Else
                    '    returnString = buildTypeOrSizeIssuesString(tableName, sybaseField, sqlField)
                End If
            Case Else
                If sybaseField.FieldType <> sqlField.FieldType Then
                    returnString = buildTypeOrSizeIssuesString(tableName, sybaseField, sqlField)
                End If
        End Select


        Return returnString
        'If sybaseField.FieldTypeName = "varchar" Then
        '    If sqlField.FieldTypeName = "varchar" Then
        '        sb.AppendFormat("ALTER TABLE [{0}] ALTER COLUMN [{1}]  nvarchar({2})", sqlTi.TableName, sqlField.FieldName, sybaseField.FieldLength)
        '        sb.AppendLine()

        '        'Console.WriteLine("ALTER TABLE [{0}] ALTER COLUMN [{1}]  nvarchar({2})", sqlTi.TableName, sqlField.FieldName, sybaseField.FieldLength)
        '        'If sybaseField.FieldLength * 2 <> sqlField.FieldLength Then
        '        '    sb.AppendFormat("ALTER TABLE [{0}] ALTER COLUMN [{1}]  nvarchar({2})", sqlTi.TableName, sqlField.FieldName, sybaseField.FieldLength)
        '        '    sb.AppendLine()
        '        '    Console.WriteLine("ALTER TABLE [{0}] ALTER COLUMN [{1}]  nvarchar({2})", sqlTi.TableName, sqlField.FieldName, sybaseField.FieldLength)



        '        '    'If badTables.Contains(sybase) Then
        '        '    Console.WriteLine("{0}.{1} Size mismatch -------------------", sybaseTi.TableName, sybaseField.FieldName)
        '        '    problems += 1
        '        'End If
        '    ElseIf sqlField.FieldTypeName = "nvarchar" Then
        '        If sybaseField.FieldLength * 2 <> sqlField.FieldLength Then
        '            sb.AppendFormat("ALTER TABLE [{0}] ALTER COLUMN [{1}]  nvarchar({2})", sqlTi.TableName, sqlField.FieldName, sybaseField.FieldLength)
        '            sb.AppendLine()
        '        End If
        '    Else
        '        problems += 1
        '    End If
        'End If
    End Function


    Function GetFileName() As String
        Return "DbSchemaForSybase.xml.xml"
    End Function

    Function GetFileNameSQL() As String
        If passedFileName Is Nothing Then
            Return "DbSchemaForSybase.xml"
        Else
            Return passedFileName
        End If

    End Function

    Private Function LoadSybaseSchemaFromFile() As DBSchema
        Dim dbInfoSybase As DBSchema
        Dim fs As New FileStream(GetFileNameSQL, FileMode.Open)
        Dim x As New Xml.Serialization.XmlSerializer(GetType(DBSchema))

        dbInfoSybase = x.Deserialize(fs)

        fs.Close()

        Return dbInfoSybase
    End Function

    Private Function LoadSqlSchemaFromFile() As DBSchema
        Dim dbInfo As DBSchema
        Dim fs As New FileStream("DbSchemaForSqlServer.xml", FileMode.Open)
        Dim x As New Xml.Serialization.XmlSerializer(GetType(DBSchema))

        dbInfo = x.Deserialize(fs)

        fs.Close()

        Return dbInfo
    End Function


End Module

﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ParagonMedIdForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridLevelNode2 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colEnabled = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMedName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCC = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDedicatedLine = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChemo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHormonal = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection(Me.components)
        Me.UnitOfWork1 = New DevExpress.Xpo.UnitOfWork(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colMedId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnDone = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UnitOfWork1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridView2
        '
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEnabled, Me.colMedName, Me.colUID, Me.colCC, Me.colDedicatedLine, Me.colChemo, Me.colHormonal})
        Me.GridView2.DetailHeight = 377
        Me.GridView2.GridControl = Me.GridControl1
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsBehavior.AllowIncrementalSearch = True
        Me.GridView2.PreviewFieldName = "MedName"
        Me.GridView2.VertScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Never
        '
        'colEnabled
        '
        Me.colEnabled.FieldName = "Enabled"
        Me.colEnabled.MinWidth = 23
        Me.colEnabled.Name = "colEnabled"
        Me.colEnabled.Visible = True
        Me.colEnabled.VisibleIndex = 0
        Me.colEnabled.Width = 56
        '
        'colMedName
        '
        Me.colMedName.FieldName = "MedName"
        Me.colMedName.MinWidth = 23
        Me.colMedName.Name = "colMedName"
        Me.colMedName.Visible = True
        Me.colMedName.VisibleIndex = 1
        Me.colMedName.Width = 519
        '
        'colUID
        '
        Me.colUID.FieldName = "UID"
        Me.colUID.MinWidth = 23
        Me.colUID.Name = "colUID"
        Me.colUID.Visible = True
        Me.colUID.VisibleIndex = 2
        Me.colUID.Width = 345
        '
        'colCC
        '
        Me.colCC.FieldName = "CC"
        Me.colCC.MinWidth = 23
        Me.colCC.Name = "colCC"
        Me.colCC.Visible = True
        Me.colCC.VisibleIndex = 3
        Me.colCC.Width = 41
        '
        'colDedicatedLine
        '
        Me.colDedicatedLine.FieldName = "DedicatedLine"
        Me.colDedicatedLine.MinWidth = 23
        Me.colDedicatedLine.Name = "colDedicatedLine"
        Me.colDedicatedLine.Visible = True
        Me.colDedicatedLine.VisibleIndex = 4
        Me.colDedicatedLine.Width = 47
        '
        'colChemo
        '
        Me.colChemo.FieldName = "Chemo"
        Me.colChemo.MinWidth = 23
        Me.colChemo.Name = "colChemo"
        Me.colChemo.Visible = True
        Me.colChemo.VisibleIndex = 6
        Me.colChemo.Width = 54
        '
        'colHormonal
        '
        Me.colHormonal.FieldName = "Hormonal"
        Me.colHormonal.MinWidth = 23
        Me.colHormonal.Name = "colHormonal"
        Me.colHormonal.Visible = True
        Me.colHormonal.VisibleIndex = 5
        Me.colHormonal.Width = 48
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.DataSource = Me.XpCollection1
        GridLevelNode1.LevelTemplate = Me.GridView2
        GridLevelNode2.RelationName = "MedIds"
        GridLevelNode1.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode2})
        GridLevelNode1.RelationName = "MedicationMaster"
        Me.GridControl1.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1})
        Me.GridControl1.Location = New System.Drawing.Point(27, 13)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(1107, 600)
        Me.GridControl1.TabIndex = 8
        Me.GridControl1.UseEmbeddedNavigator = True
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1, Me.GridView2})
        '
        'XpCollection1
        '
        Me.XpCollection1.DisplayableProperties = "MedId;MedicationMaster"
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOParagonMedIdMapping)
        Me.XpCollection1.Session = Me.UnitOfWork1
        Me.XpCollection1.Sorting.AddRange(New DevExpress.Xpo.SortProperty() {New DevExpress.Xpo.SortProperty("[MedId]", DevExpress.Xpo.DB.SortingDirection.Ascending)})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colMedId, Me.GridColumn1})
        Me.GridView1.DetailHeight = 377
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.AllowIncrementalSearch = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.OptionsView.ShowPreviewRowLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.PreviewFieldName = "MedicationMaster.MedName"
        Me.GridView1.PreviewLineCount = 1
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colMedId, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'colMedId
        '
        Me.colMedId.AppearanceCell.Options.UseTextOptions = True
        Me.colMedId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.colMedId.FieldName = "MedId"
        Me.colMedId.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.colMedId.MinWidth = 23
        Me.colMedId.Name = "colMedId"
        Me.colMedId.Visible = True
        Me.colMedId.VisibleIndex = 0
        Me.colMedId.Width = 50
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "(IC) Medication Name"
        Me.GridColumn1.FieldName = "MedicationMaster.MedName"
        Me.GridColumn1.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.GridColumn1.MinWidth = 23
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 1
        Me.GridColumn1.Width = 87
        '
        'btnDone
        '
        Me.btnDone.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDone.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.btnDone.Location = New System.Drawing.Point(1004, 619)
        Me.btnDone.Name = "btnDone"
        Me.btnDone.Size = New System.Drawing.Size(129, 25)
        Me.btnDone.TabIndex = 7
        Me.btnDone.Text = "Done"
        '
        'ParagonMedIdForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 14.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1148, 657)
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.btnDone)
        Me.Name = "ParagonMedIdForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "ParagonMedIdForm"
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UnitOfWork1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents btnDone As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents UnitOfWork1 As DevExpress.Xpo.UnitOfWork
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colMedId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colEnabled As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMedName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCC As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDedicatedLine As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChemo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHormonal As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
End Class

﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.ceUse2018ChargeAllocationLogic = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018CodingReport = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018ESPCodeLogic = New DevExpress.XtraEditors.CheckEdit()
        Me.lblMsg = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018EspcodeLogiclbl = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018CodingReportLogiclbl = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018ObsChargeAlloclbl = New DevExpress.XtraEditors.LabelControl()
        Me.EspcodeMigrationlbl = New DevExpress.XtraEditors.LabelControl()
        Me.progressBar = New DevExpress.XtraEditors.ProgressBarControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ceCodeMigration = New DevExpress.XtraEditors.CheckEdit()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.ceConvertProviderListslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceConvertProviderLists = New DevExpress.XtraEditors.CheckEdit()
        Me.CreateRealTimeSettinglbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceCreateRealtimeChargesSetting = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.TurnOnMaintenanceModelbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceEnableMainenanceMode = New DevExpress.XtraEditors.CheckEdit()
        Me.FetalMonlbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceFetalMon = New DevExpress.XtraEditors.CheckEdit()
        Me.UpdateOvernightScriptslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceUpdateDbScripts = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.progressBar.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceCodeMigration.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceConvertProviderLists.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceCreateRealtimeChargesSetting.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceEnableMainenanceMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFetalMon.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUpdateDbScripts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Location = New System.Drawing.Point(485, 311)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(150, 23)
        Me.SimpleButton1.TabIndex = 0
        Me.SimpleButton1.Text = "Exit"
        '
        'ceUse2018ChargeAllocationLogic
        '
        Me.ceUse2018ChargeAllocationLogic.Location = New System.Drawing.Point(12, 125)
        Me.ceUse2018ChargeAllocationLogic.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018ChargeAllocationLogic.Name = "ceUse2018ChargeAllocationLogic"
        Me.ceUse2018ChargeAllocationLogic.Properties.AllowFocused = False
        Me.ceUse2018ChargeAllocationLogic.Properties.Caption = "Use 2018 Obs. Charge Allocation Logic"
        Me.ceUse2018ChargeAllocationLogic.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018ChargeAllocationLogic.Properties.ReadOnly = True
        Me.ceUse2018ChargeAllocationLogic.Size = New System.Drawing.Size(361, 22)
        Me.ceUse2018ChargeAllocationLogic.TabIndex = 16
        '
        'ceUse2018CodingReport
        '
        Me.ceUse2018CodingReport.Location = New System.Drawing.Point(13, 97)
        Me.ceUse2018CodingReport.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018CodingReport.Name = "ceUse2018CodingReport"
        Me.ceUse2018CodingReport.Properties.AllowFocused = False
        Me.ceUse2018CodingReport.Properties.Caption = "Use 2018 Coding Report Logic"
        Me.ceUse2018CodingReport.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018CodingReport.Properties.ReadOnly = True
        Me.ceUse2018CodingReport.Size = New System.Drawing.Size(361, 22)
        Me.ceUse2018CodingReport.TabIndex = 15
        '
        'ceUse2018ESPCodeLogic
        '
        Me.ceUse2018ESPCodeLogic.Location = New System.Drawing.Point(13, 69)
        Me.ceUse2018ESPCodeLogic.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018ESPCodeLogic.Name = "ceUse2018ESPCodeLogic"
        Me.ceUse2018ESPCodeLogic.Properties.AllowFocused = False
        Me.ceUse2018ESPCodeLogic.Properties.Caption = "Use 2018 ESPCode Logic"
        Me.ceUse2018ESPCodeLogic.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018ESPCodeLogic.Properties.ReadOnly = True
        Me.ceUse2018ESPCodeLogic.Size = New System.Drawing.Size(361, 22)
        Me.ceUse2018ESPCodeLogic.TabIndex = 14
        '
        'lblMsg
        '
        Me.lblMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblMsg.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.2!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblMsg.Appearance.Options.UseFont = True
        Me.lblMsg.Appearance.Options.UseTextOptions = True
        Me.lblMsg.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.lblMsg.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.lblMsg.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.lblMsg.AutoEllipsis = True
        Me.lblMsg.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.lblMsg.CausesValidation = False
        Me.lblMsg.Cursor = System.Windows.Forms.Cursors.WaitCursor
        Me.lblMsg.Location = New System.Drawing.Point(12, 341)
        Me.lblMsg.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(1030, 24)
        Me.lblMsg.TabIndex = 17
        Me.lblMsg.Text = "Put your msg here ..."
        Me.lblMsg.UseMnemonic = False
        Me.lblMsg.UseWaitCursor = True
        '
        'Use2018EspcodeLogiclbl
        '
        Me.Use2018EspcodeLogiclbl.Location = New System.Drawing.Point(380, 71)
        Me.Use2018EspcodeLogiclbl.Name = "Use2018EspcodeLogiclbl"
        Me.Use2018EspcodeLogiclbl.Size = New System.Drawing.Size(24, 16)
        Me.Use2018EspcodeLogiclbl.TabIndex = 18
        Me.Use2018EspcodeLogiclbl.Text = "......"
        '
        'Use2018CodingReportLogiclbl
        '
        Me.Use2018CodingReportLogiclbl.Location = New System.Drawing.Point(380, 99)
        Me.Use2018CodingReportLogiclbl.Name = "Use2018CodingReportLogiclbl"
        Me.Use2018CodingReportLogiclbl.Size = New System.Drawing.Size(24, 16)
        Me.Use2018CodingReportLogiclbl.TabIndex = 19
        Me.Use2018CodingReportLogiclbl.Text = "......"
        '
        'Use2018ObsChargeAlloclbl
        '
        Me.Use2018ObsChargeAlloclbl.Location = New System.Drawing.Point(380, 129)
        Me.Use2018ObsChargeAlloclbl.Name = "Use2018ObsChargeAlloclbl"
        Me.Use2018ObsChargeAlloclbl.Size = New System.Drawing.Size(24, 16)
        Me.Use2018ObsChargeAlloclbl.TabIndex = 20
        Me.Use2018ObsChargeAlloclbl.Text = "......"
        '
        'EspcodeMigrationlbl
        '
        Me.EspcodeMigrationlbl.Location = New System.Drawing.Point(381, 265)
        Me.EspcodeMigrationlbl.Name = "EspcodeMigrationlbl"
        Me.EspcodeMigrationlbl.Size = New System.Drawing.Size(24, 16)
        Me.EspcodeMigrationlbl.TabIndex = 22
        Me.EspcodeMigrationlbl.Text = "......"
        '
        'progressBar
        '
        Me.progressBar.Location = New System.Drawing.Point(573, 257)
        Me.progressBar.Name = "progressBar"
        Me.progressBar.Properties.EndColor = System.Drawing.Color.Green
        Me.progressBar.Properties.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Style3D
        Me.progressBar.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.progressBar.Properties.StartColor = System.Drawing.Color.Red
        Me.progressBar.Size = New System.Drawing.Size(588, 26)
        Me.progressBar.TabIndex = 23
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(380, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(115, 16)
        Me.LabelControl1.TabIndex = 24
        Me.LabelControl1.Text = "... Trying to connect"
        '
        'ceCodeMigration
        '
        Me.ceCodeMigration.Location = New System.Drawing.Point(13, 263)
        Me.ceCodeMigration.Margin = New System.Windows.Forms.Padding(4)
        Me.ceCodeMigration.Name = "ceCodeMigration"
        Me.ceCodeMigration.Properties.AllowFocused = False
        Me.ceCodeMigration.Properties.Caption = "2018 Espcode Migration"
        Me.ceCodeMigration.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceCodeMigration.Properties.ReadOnly = True
        Me.ceCodeMigration.Size = New System.Drawing.Size(361, 22)
        Me.ceCodeMigration.TabIndex = 25
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.BarAnimationElementThickness = 2
        Me.ProgressPanel1.Location = New System.Drawing.Point(864, 161)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(246, 66)
        Me.ProgressPanel1.TabIndex = 26
        Me.ProgressPanel1.Text = "ProgressPanel1"
        Me.ProgressPanel1.Visible = False
        '
        'ceConvertProviderListslbl
        '
        Me.ceConvertProviderListslbl.Location = New System.Drawing.Point(381, 239)
        Me.ceConvertProviderListslbl.Name = "ceConvertProviderListslbl"
        Me.ceConvertProviderListslbl.Size = New System.Drawing.Size(24, 16)
        Me.ceConvertProviderListslbl.TabIndex = 28
        Me.ceConvertProviderListslbl.Text = "......"
        '
        'ceConvertProviderLists
        '
        Me.ceConvertProviderLists.Location = New System.Drawing.Point(13, 235)
        Me.ceConvertProviderLists.Margin = New System.Windows.Forms.Padding(4)
        Me.ceConvertProviderLists.Name = "ceConvertProviderLists"
        Me.ceConvertProviderLists.Properties.AllowFocused = False
        Me.ceConvertProviderLists.Properties.Caption = "Convert Provider Lists"
        Me.ceConvertProviderLists.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceConvertProviderLists.Properties.ReadOnly = True
        Me.ceConvertProviderLists.Size = New System.Drawing.Size(361, 22)
        Me.ceConvertProviderLists.TabIndex = 27
        '
        'CreateRealTimeSettinglbl
        '
        Me.CreateRealTimeSettinglbl.Location = New System.Drawing.Point(381, 210)
        Me.CreateRealTimeSettinglbl.Name = "CreateRealTimeSettinglbl"
        Me.CreateRealTimeSettinglbl.Size = New System.Drawing.Size(24, 16)
        Me.CreateRealTimeSettinglbl.TabIndex = 30
        Me.CreateRealTimeSettinglbl.Text = "......"
        '
        'ceCreateRealtimeChargesSetting
        '
        Me.ceCreateRealtimeChargesSetting.Location = New System.Drawing.Point(13, 206)
        Me.ceCreateRealtimeChargesSetting.Margin = New System.Windows.Forms.Padding(4)
        Me.ceCreateRealtimeChargesSetting.Name = "ceCreateRealtimeChargesSetting"
        Me.ceCreateRealtimeChargesSetting.Properties.AllowFocused = False
        Me.ceCreateRealtimeChargesSetting.Properties.Caption = "Create RealTime Charges Setting"
        Me.ceCreateRealtimeChargesSetting.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceCreateRealtimeChargesSetting.Properties.ReadOnly = True
        Me.ceCreateRealtimeChargesSetting.Size = New System.Drawing.Size(361, 22)
        Me.ceCreateRealtimeChargesSetting.TabIndex = 29
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(13, 12)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(110, 16)
        Me.LabelControl2.TabIndex = 32
        Me.LabelControl2.Text = "Connection String: "
        '
        'TurnOnMaintenanceModelbl
        '
        Me.TurnOnMaintenanceModelbl.Location = New System.Drawing.Point(379, 43)
        Me.TurnOnMaintenanceModelbl.Name = "TurnOnMaintenanceModelbl"
        Me.TurnOnMaintenanceModelbl.Size = New System.Drawing.Size(24, 16)
        Me.TurnOnMaintenanceModelbl.TabIndex = 34
        Me.TurnOnMaintenanceModelbl.Text = "......"
        '
        'ceEnableMainenanceMode
        '
        Me.ceEnableMainenanceMode.Location = New System.Drawing.Point(12, 41)
        Me.ceEnableMainenanceMode.Margin = New System.Windows.Forms.Padding(4)
        Me.ceEnableMainenanceMode.Name = "ceEnableMainenanceMode"
        Me.ceEnableMainenanceMode.Properties.AllowFocused = False
        Me.ceEnableMainenanceMode.Properties.Caption = "Turn On Maintenance Mode"
        Me.ceEnableMainenanceMode.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceEnableMainenanceMode.Properties.ReadOnly = True
        Me.ceEnableMainenanceMode.Size = New System.Drawing.Size(361, 22)
        Me.ceEnableMainenanceMode.TabIndex = 33
        '
        'FetalMonlbl
        '
        Me.FetalMonlbl.Location = New System.Drawing.Point(381, 154)
        Me.FetalMonlbl.Name = "FetalMonlbl"
        Me.FetalMonlbl.Size = New System.Drawing.Size(24, 16)
        Me.FetalMonlbl.TabIndex = 36
        Me.FetalMonlbl.Text = "......"
        '
        'ceFetalMon
        '
        Me.ceFetalMon.Location = New System.Drawing.Point(13, 150)
        Me.ceFetalMon.Margin = New System.Windows.Forms.Padding(4)
        Me.ceFetalMon.Name = "ceFetalMon"
        Me.ceFetalMon.Properties.AllowFocused = False
        Me.ceFetalMon.Properties.Caption = "Update Description for Mon_Fetal Espocodes"
        Me.ceFetalMon.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceFetalMon.Properties.ReadOnly = True
        Me.ceFetalMon.Size = New System.Drawing.Size(361, 22)
        Me.ceFetalMon.TabIndex = 35
        '
        'UpdateOvernightScriptslbl
        '
        Me.UpdateOvernightScriptslbl.Location = New System.Drawing.Point(380, 182)
        Me.UpdateOvernightScriptslbl.Name = "UpdateOvernightScriptslbl"
        Me.UpdateOvernightScriptslbl.Size = New System.Drawing.Size(24, 16)
        Me.UpdateOvernightScriptslbl.TabIndex = 38
        Me.UpdateOvernightScriptslbl.Text = "......"
        '
        'ceUpdateDbScripts
        '
        Me.ceUpdateDbScripts.Location = New System.Drawing.Point(12, 178)
        Me.ceUpdateDbScripts.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUpdateDbScripts.Name = "ceUpdateDbScripts"
        Me.ceUpdateDbScripts.Properties.AllowFocused = False
        Me.ceUpdateDbScripts.Properties.Caption = "Update Overnight Reports DB Scripts"
        Me.ceUpdateDbScripts.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUpdateDbScripts.Properties.ReadOnly = True
        Me.ceUpdateDbScripts.Size = New System.Drawing.Size(361, 22)
        Me.ceUpdateDbScripts.TabIndex = 37
        '
        'Form1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1184, 372)
        Me.Controls.Add(Me.UpdateOvernightScriptslbl)
        Me.Controls.Add(Me.ceUpdateDbScripts)
        Me.Controls.Add(Me.FetalMonlbl)
        Me.Controls.Add(Me.ceFetalMon)
        Me.Controls.Add(Me.TurnOnMaintenanceModelbl)
        Me.Controls.Add(Me.ceEnableMainenanceMode)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.CreateRealTimeSettinglbl)
        Me.Controls.Add(Me.ceCreateRealtimeChargesSetting)
        Me.Controls.Add(Me.ceConvertProviderListslbl)
        Me.Controls.Add(Me.ceConvertProviderLists)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.Controls.Add(Me.ceCodeMigration)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.progressBar)
        Me.Controls.Add(Me.EspcodeMigrationlbl)
        Me.Controls.Add(Me.Use2018ObsChargeAlloclbl)
        Me.Controls.Add(Me.Use2018CodingReportLogiclbl)
        Me.Controls.Add(Me.Use2018EspcodeLogiclbl)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.ceUse2018ChargeAllocationLogic)
        Me.Controls.Add(Me.ceUse2018CodingReport)
        Me.Controls.Add(Me.ceUse2018ESPCodeLogic)
        Me.Controls.Add(Me.SimpleButton1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "Form1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "2018 Config Update Utility"
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.progressBar.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceCodeMigration.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceConvertProviderLists.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceCreateRealtimeChargesSetting.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceEnableMainenanceMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFetalMon.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUpdateDbScripts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents ButtonEdit1 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ceUse2018ChargeAllocationLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018CodingReport As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ESPCodeLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents lblMsg As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018EspcodeLogiclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018CodingReportLogiclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018ObsChargeAlloclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents EspcodeMigrationlbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents progressBar As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceCodeMigration As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents ceConvertProviderListslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceConvertProviderLists As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CreateRealTimeSettinglbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceCreateRealtimeChargesSetting As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TurnOnMaintenanceModelbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceEnableMainenanceMode As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FetalMonlbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceFetalMon As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents UpdateOvernightScriptslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceUpdateDbScripts As DevExpress.XtraEditors.CheckEdit
End Class

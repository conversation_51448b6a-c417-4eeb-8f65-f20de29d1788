﻿Option Infer On

Imports System.IO
Imports System.Reflection
Imports DevExpress.Xpo
Imports DevExpress.XtraEditors
Imports ENChartCAC
Imports EnchartDOLib

Public Class Form1
    Private Sub Button1_Click(sender As Object, e As EventArgs)

    End Sub

    Private Sub UpdateProgressBar(percentDone As Integer)
        ProgressPanel1.Hide()
        progressBar.Position = percentDone
    End Sub

    Private Async Sub ConvertEspcodesTo2018()
        'Dim userResponse = MessageBox.Show("Are you sure you want to migrate espcodes to new DOEspCodes2018 Table?", "EsPCode Migration", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning)
        'If userResponse <> DialogResult.OK Then Exit Sub

        EspcodeMigrationlbl.Text = "...IN PROGRESS"

        'DOESPCode2018.DeleteAllEspcodes()

        Dim progressIndicator As New Progress(Of String)(Sub(msg) lblMsg.Text = msg)
        Dim progressBarIndicator As New Progress(Of Integer)(AddressOf UpdateProgressBar) '(Sub(percentDone) progressBar.Position = percentDone)

        Try
            Dim result = Await Task.Run(Function() MigrateEspocodes(progressIndicator, progressBarIndicator))
            '     DOESPCode2018.DeleteAllEspcodes()
        Catch ex As Exception
            'ECLog.WriteExceptionError("ConvertEspcodesTo2018", ex, True)
            EspcodeMigrationlbl.Text = "ERROR"
            MessageBox.Show(ex?.Message)
            Exit Sub
        Finally

        End Try

        EspcodeMigrationlbl.Text = "COMPLETED Successfully"
        ceCodeMigration.Checked = True
        lblMsg.Text = ""
        '  progressBar.Visible = False
    End Sub

    Private Function MigrateEspocodes(progressIndicator As IProgress(Of String), progressBarIndicator As IProgress(Of Integer)) As List(Of DOESPCode2018)
        Dim codeMigrationEngine = New ESPCode2018Migrator()
        Dim results = codeMigrationEngine.MigrateEspocodesTo2018VersionsV1(progressIndicator, progressBarIndicator)

        Dim updatedEspCodesList = codeMigrationEngine.TokenizeEspcodes(results)
        progressIndicator.Report("Saving Results To DB ... Please wait") ' .Msg = "Saving Results To DB ... Please wait"
        Try
            DOESPCode2018.DeleteAllEspcodes()
            '  DbPollingTimer.Stop()
            XpoDefault.Session.Save(updatedEspCodesList)

        Finally
            '      DbPollingTimer.Start()
        End Try

        Return updatedEspCodesList
    End Function

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        'ConvertEspcodesTo2018()
        Close()

    End Sub

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Process()
    End Sub

    Private Async Sub Process()
        Await Task.Delay(1000)

        ConnectToECDataBase()
        ProgressPanel1.Visible = True
        Application.DoEvents()

        UpdateOvernightReports()
        Application.DoEvents()


        Application.DoEvents()
        LabelControl1.Text = GetConnectionString()
        Application.DoEvents()
        SetupGlobalConfigOptions()
        Application.DoEvents()
        UpdateFetalStressTestDescription()
        Application.DoEvents()
        ConvertProviderLists()
        Application.DoEvents()
        ConvertEspcodesTo2018()
    End Sub

    Private Sub UpdateOvernightReports()
        _assembly = [Assembly].GetExecutingAssembly()
        Try
            Dim cs = EnchartDOLib.GetConnectionString()

            If cs.ToUpper().Contains("ASA17") Then
                UpdateSybaseDbScripts()
            ElseIf cs.ToUpper.Contains("INITIAL CATALOG") Then 'if sql server
                UpdateSqlDbScripts()
            End If
        Catch ex As Exception
            UpdateOvernightScriptslbl.Text = "ERROR"
            Return
        End Try

        ceUpdateDbScripts.Checked = True
        UpdateOvernightScriptslbl.Text = "UPDATED"
    End Sub

    Private Sub UpdateSqlDbScripts()
        Try
            '     Dim dbScript = File.ReadAllText("Overnight.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.usp_overnight '_assembly.GetManifestResourceStream 'Resources.Overnight_New 'dbScript
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'usp_overnight.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try

        'Overnight_New.sql

        Try
            ' Dim dbScript = File.ReadAllText("Overnight_New.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.usp_overnight_rebuild
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'usp_overnight_rebuild.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try
    End Sub

    Dim _assembly As [Assembly]

    Private Sub UpdateSybaseDbScripts()
        Try
            '     Dim dbScript = File.ReadAllText("Overnight.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.Overnight '_assembly.GetManifestResourceStream 'Resources.Overnight_New 'dbScript
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'Overnight.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try

        'Overnight_New.sql

        Try
            ' Dim dbScript = File.ReadAllText("Overnight_New.sql")

            Dim cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            cmd.CommandText = My.Resources.Overnight_New
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show($"Error trying to execute 'Overnight_New.sql' {vbCrLf}{vbCrLf} {ex.Message}")
            Throw
        End Try
    End Sub


    Private Sub UpdateFetalStressTestDescription()
        Try
            Dim espcodesToUpdate = (From esp In New XPQuery(Of DOESPCode)(XpoDefault.Session)
                                    Where esp.ESPVALUE = "Mon_Fetal"
                                    Select esp)

            For Each esp In espcodesToUpdate
                esp.LongName = "Fetal Non Stress Test"
                esp.Special = 1
                esp.Save()
            Next

            ceFetalMon.Checked = True
            FetalMonlbl.Text = $"UPDATED {espcodesToUpdate.Count} Records to 'Fetal Non Stress Test'"
        Catch ex As Exception
            FetalMonlbl.Text = $"ERROR"
            MessageBox.Show($"(UpdateFetalStressTestDescription )Error trying to update espcodes. {vbCrLf}{vbCrLf} {ex.Message}")
        End Try
    End Sub



    Private Sub SetupGlobalConfigOptions()
        'MaintenanceModeMessage
        Dim msg = CheckMaintenanceModeMessage()
        If String.IsNullOrEmpty(msg) Then
            msg = "TURNED ON"
        Else
            msg = $"TURNED ON - {msg}"
        End If
        UpdateSetting("MaintenanceModeEnable", ceEnableMainenanceMode, TurnOnMaintenanceModelbl, "True", msg)

        UpdateSetting("Use2018EspcodeLogic", ceUse2018ESPCodeLogic, Use2018EspcodeLogiclbl)

        UpdateSetting("Use2018CodingReport", ceUse2018CodingReport, Use2018CodingReportLogiclbl)

        UpdateSetting("Use2018ChargeAllocationLogic", ceUse2018ChargeAllocationLogic, Use2018ObsChargeAlloclbl)

        UpdateSetting("RealtimeCharges", ceCreateRealtimeChargesSetting, CreateRealTimeSettinglbl, "False", "UPDATED (Set to False)")

    End Sub
    Public Sub UpdateSetting(settingName As String, checkbox As CheckEdit, label As LabelControl, Optional state As String = "True", Optional result As String = "UPDATED")
        Try
            UpdateGlobalSetting(settingName, state)
            checkbox.Checked = True

            label.Text = result
        Catch ex As Exception
            label.Text = "ERROR"
        End Try

    End Sub

    Private Function CheckMaintenanceModeMessage() As String
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("MaintenanceModeMessage")
        Dim msg As String = setting.SettingValue
        If String.IsNullOrEmpty(msg) Then
            setting.SettingValue = "The IC Server will back up shortly"
            setting.Save()
            Return "MaintenanceModeMessage Setting set to default"
        End If
        Return String.Empty
    End Function

    Private Shared Sub UpdateGlobalSetting(settingName As String, state As String)
        Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting(settingName)
        setting.SettingValue = state
        setting.Save()
    End Sub

    Private Sub ConvertProviderLists()
        Try
            For Each facility In New XPCollection(GetType(DOFacility))
                MoveNonCiListsToSharedCi(facility)
            Next
        Catch ex As Exception
            ceConvertProviderListslbl.Text = "ERROR"
            MessageBox.Show(ex?.Message)
        End Try

        ceConvertProviderLists.Checked = True
        ceConvertProviderListslbl.Text = "COMPLETED Successfully"
    End Sub

    Private Sub MoveNonCiListsToSharedCi(facility As DOFacility)
        For Each cboList In facility.ConfigInstanceVersion.ComboBoxLists
            If IsSharedCboList(cboList) Then
                If facility.SharedConfigInstanceVersion.GetComboBoxList(cboList.ListName) Is Nothing Then
                    cboList.ConfigInstance = facility.SharedConfigInstanceVersion
                    cboList.Save()
                End If
            End If
        Next
    End Sub


    Public Function ConnectToDb() As Boolean
        EnchartDOLib.ConnectToECDataBase()
        Return False
    End Function

    Private Sub btnRun_Click(sender As Object, e As EventArgs)
        Process()
    End Sub

    'Private Sub ceUse2018ESPCodeLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ESPCodeLogic.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018EspcodeLogic")
    '    setting.SettingValue = ceUse2018ESPCodeLogic.Checked.ToString
    '    setting.Save()
    'End Sub

    'Private Sub ceUse2018CodingReport_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018CodingReport.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018CodingReport")
    '    setting.SettingValue = ceUse2018CodingReport.Checked.ToString
    '    setting.Save()
    'End Sub

    'Private Sub ceUse2018ChargeAllocationLogic_CheckedChanged(sender As Object, e As EventArgs) Handles ceUse2018ChargeAllocationLogic.CheckedChanged
    '    Dim setting As DOGlobalSetting = DOGlobalSetting.GetSetting("Use2018ChargeAllocationLogic")
    '    setting.SettingValue = ceUse2018ChargeAllocationLogic.Checked.ToString
    '    setting.Save()
    'End Sub


End Class

@echo off
::Script Variables
SET LOGFILE="RSScripterLog.txt"
SET REPORTSERVER=http://localhost/ReportServer
SET PARENTFOLDER=VIC
SET P2="%PARENTFOLDER%"
SET DATABASESERVER=localhost
SET DATABASE=enchartplustest
SET TIMEOUT=60
SET RSPath="C:\Program Files\Microsoft SQL Server Reporting Services\Shared Tools\"


::Clear Log file
IF EXIST %logfile% DEL %logfile%

::Create Shared DataSource 'VICShared'
%RSPath%rs.exe -i CreateSharedDataSource.vb -s %REPORTSERVER% -l %TIMEOUT% -v PARENTFOLDER=%P2% -v DATABASESERVER=%DATABASESERVER% -v DATABASE=%DATABASE%  
ECHO "Shared DataSource 'VICShared' created
::TIMEOUT /T 9

::Deploy all the SSRS rdl files in the directory.  Note - 0x22's are qoute wrappers for the parameters
ECHO "Report files uploading to %REPORTSERVER% ..."
::FORFILES /M *.rdl /C "cmd /c rs -i DeployReports.vb -s %REPORTSERVER% -l %TIMEOUT% -v PARENTFOLDER=0x22%PARENTFOLDER%0x22 -v RDLNAME=@fname -v DATABASESERVER=0x22%DATABASESERVER%0x22 -v DATABASE=0x22%DATABASE%0x22" 
%RSPath%rs -i DeployReports.vb -s %REPORTSERVER% -l %TIMEOUT% -v PARENTFOLDER=%P2% -v DATABASESERVER=%DATABASESERVER% -v DATABASE=%DATABASE%  

ECHO. >>%LOGFILE%
ECHO. >>%LOGFILE%
ECHO Finished Load at %DATE% %TIME% >>%LOGFILE%
ECHO. >>%LOGFILE%
TIMEOUT /T 30

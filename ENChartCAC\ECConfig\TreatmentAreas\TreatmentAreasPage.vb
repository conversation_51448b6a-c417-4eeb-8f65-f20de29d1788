﻿Imports System.Linq
Imports DevExpress.Data.Filtering
Imports DevExpress.Utils
Imports DevExpress.Xpo
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports EnchartDOLib

Public Class TreatmentAreasPage

    Dim facility As DOFacility

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Public Sub InitTreatmentAreasForFacility(ByVal facility As DOFacility)
        Me.facility = facility
        Dim treatmentAreas = New XPCollection(Of DOTreatmentArea)().ToList().Where(Function(x) x.Facility.Oid = Me.facility.Oid)
        treatmentAreasGridControl.DataSource = treatmentAreas
        revenueCenterRepositoryItemLookUpEdit.DataSource = treatmentAreas

        ' We only want to show the button if the treatment areas have not been migrated
        ' If a treatment area exist then we will assume they have been migrated
        migrateButton.Visible = Not treatmentAreas.Count > 0
    End Sub

    ''' <summary>
    ''' This method will migrate the current treatment areas based on the combobox list that
    ''' exist for the current config instance of the facility
    ''' </summary>
    Private Sub MigrateLegacyTreatmentAreas()
        Dim treatmentAreas = New XPCollection(Of DOTreatmentArea)().ToList().Where(Function(ta) ta.Facility.Oid = Me.facility.Oid)

        Dim treatmentAreaItems = TreatmentAreaComboBoxList()
        If treatmentAreaItems IsNot Nothing Then
            For Each ta In treatmentAreaItems.ListItems
                ' If the treatment area is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ta.ItemDisplayName) Is Nothing Then
                    MigrateLegacyTreatmentArea(ta, "ED")
                End If
            Next
        End If

        Dim nursingStationItems = NursingStationComboBoxList()
        If nursingStationItems IsNot Nothing Then
            For Each ns In nursingStationItems.ListItems
                ' If the nursingstation is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ns.ItemDisplayName) Is Nothing Then
                    MigrateLegacyTreatmentArea(ns, "OBS")
                End If
            Next
        End If

        InitTreatmentAreasForFacility(Me.facility)
    End Sub

    Private Sub MigrateAllFacilities()
        For Each facility In DOFacility.GetAllFacilitesAsList()
            MigrateLegacyTreatmentAreas(facility.Oid)
        Next
    End Sub

    Private Sub MigrateLegacyTreatmentAreas(facilityOID As Integer)
        Dim treatmentAreas = New XPCollection(Of DOTreatmentArea)().ToList().Where(Function(ta) ta.Facility.Oid = facilityOID)

        Dim treatmentAreaItems = TreatmentAreaComboBoxList()
        If treatmentAreaItems IsNot Nothing Then
            For Each ta In treatmentAreaItems.ListItems
                ' If the treatment area is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ta.ItemDisplayName) Is Nothing Then
                    MigrateLegacyTreatmentArea(ta, "ED")
                End If
            Next
        End If

        Dim nursingStationItems = NursingStationComboBoxList()
        If nursingStationItems IsNot Nothing Then
            For Each ns In nursingStationItems.ListItems
                ' If the nursingstation is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ns.ItemDisplayName) Is Nothing Then
                    MigrateLegacyTreatmentArea(ns, "OBS")
                End If
            Next
        End If

        '  InitTreatmentAreasForFacility(Me.facility)
    End Sub


    Private Function TreatmentAreaComboBoxList() As DOConfigComboBoxList
        Return Me.facility.ConfigInstanceVersion.GetComboBoxListByControlName("TreatmentArea_cbo")
    End Function

    Private Function NursingStationComboBoxList() As DOConfigComboBoxList
        Return Me.facility.ConfigInstanceVersion.GetComboBoxListByControlName("TreatmentAreaObsTab_cbo")
    End Function

    Private Sub AddTreatmentArea()
        Dim newTreatmentArea As New DOTreatmentArea(Session.DefaultSession) With {
            .Facility = facility, .Enabled = True
        }

        Dim editTreatmentAreaForm As New EditTreatmentAreaForm(newTreatmentArea)
        If editTreatmentAreaForm.ShowDialog() = DialogResult.OK Then
            newTreatmentArea.Save()
        End If
        newTreatmentArea.Reload()
        treatmentAreasGridControl.DataSource = New XPCollection(Of DOTreatmentArea)().ToList().Where(Function(x) x.Facility.Oid = facility.Oid)
    End Sub

    Private Sub EditTreatmentArea(treatmentArea As DOTreatmentArea)
        'Dim treatmentAreaBeforeEdit As New DOTreatmentArea With {
        '    .Name = treatmentArea.Name,
        '    .ChargeMaster = treatmentArea.ChargeMaster,
        '    .TreatmentAreaType = treatmentArea.TreatmentAreaType,
        '    .Enabled = treatmentArea.Enabled
        '}

        Dim editTreatmentAreaForm As New EditTreatmentAreaForm(treatmentArea)
        If editTreatmentAreaForm.ShowDialog() = DialogResult.OK Then
            treatmentArea.Save()
        End If
        treatmentArea.Reload()
        treatmentAreasGridControl.RefreshDataSource()
    End Sub

    Private Sub MigrateLegacyTreatmentArea(treatmentArea As DOConfigComboBoxListsItem, treatmentAreaType As String)
        Dim newTreatmentArea As New DOTreatmentArea With {
                    .Facility = Me.facility,
                    .Name = treatmentArea.ItemDisplayName,
                    .ChargeMaster = treatmentArea.ItemDisplayName,
                    .TreatmentAreaType = treatmentAreaType,
                    .Enabled = treatmentArea.Enabled
            }
        newTreatmentArea.Save()
    End Sub


    Private Sub addButton_Click(sender As Object, e As EventArgs) Handles addButton.Click
        AddTreatmentArea()
    End Sub

    Private Sub editButton_Click(sender As Object, e As EventArgs) Handles editButton.Click
        EditTreatmentArea(GridView1.GetFocusedRow)
    End Sub

    Private Sub migrateButton_Click(sender As Object, e As EventArgs) Handles migrateButton.Click
        MigrateLegacyTreatmentAreas()
    End Sub

    'Private Sub GridView1_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GridView1.FocusedRowChanged
    '    Dim treatmentArea As DOTreatmentArea = GridView1.GetFocusedRow
    '    facilitySettingsGridControl.DataSource = Nothing
    '    If treatmentArea Is Nothing Then Return
    '    Dim facilitySettings = New XPCollection(Of DOFacilitySettings)().ToList().Where(Function(x) x.Facility = Me.facility.Oid And x.TreatmentArea = treatmentArea.Name)
    '    facilitySettingsGridControl.DataSource = facilitySettings
    'End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Dim ea As DXMouseEventArgs = TryCast(e, DXMouseEventArgs)
        Dim view As GridView = TryCast(sender, GridView)
        Dim info As GridHitInfo = view.CalcHitInfo(ea.Location)
        If info.InRow OrElse info.InRowCell Then
            EditTreatmentArea(GridView1.GetRow(info.RowHandle))
        End If
    End Sub
End Class

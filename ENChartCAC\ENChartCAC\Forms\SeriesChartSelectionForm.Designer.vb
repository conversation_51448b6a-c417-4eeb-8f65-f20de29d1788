<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SeriesChartSelectionForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnOpenExisting = New DevExpress.XtraEditors.SimpleButton()
        Me.btnNewSeriesChart = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.colVisitID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsSeries = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ListView1 = New System.Windows.Forms.ListView()
        Me.VisitIDCol = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
        Me.DateCol = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
        Me.DOScol = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
        Me.ChartStatus = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
        Me.SuspendLayout()
        '
        'btnOpenExisting
        '
        Me.btnOpenExisting.DialogResult = System.Windows.Forms.DialogResult.Yes
        Me.btnOpenExisting.Enabled = False
        Me.btnOpenExisting.Location = New System.Drawing.Point(282, 238)
        Me.btnOpenExisting.Name = "btnOpenExisting"
        Me.btnOpenExisting.Size = New System.Drawing.Size(91, 22)
        Me.btnOpenExisting.TabIndex = 0
        Me.btnOpenExisting.Text = "Open Existing"
        '
        'btnNewSeriesChart
        '
        Me.btnNewSeriesChart.DialogResult = System.Windows.Forms.DialogResult.No
        Me.btnNewSeriesChart.Location = New System.Drawing.Point(379, 238)
        Me.btnNewSeriesChart.Name = "btnNewSeriesChart"
        Me.btnNewSeriesChart.Size = New System.Drawing.Size(100, 22)
        Me.btnNewSeriesChart.TabIndex = 1
        Me.btnNewSeriesChart.Text = "New Series Chart"
        Me.btnNewSeriesChart.ToolTip = "Create another chart with the same VisitID"
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(485, 238)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(91, 22)
        Me.btnCancel.TabIndex = 2
        Me.btnCancel.Text = "Cancel"
        '
        'colVisitID
        '
        Me.colVisitID.Caption = "VisitID"
        Me.colVisitID.FieldName = "VisitID"
        Me.colVisitID.Name = "colVisitID"
        Me.colVisitID.Visible = True
        Me.colVisitID.VisibleIndex = 3
        '
        'colIsSeries
        '
        Me.colIsSeries.Caption = "IsSeries"
        Me.colIsSeries.FieldName = "IsSeries"
        Me.colIsSeries.Name = "colIsSeries"
        Me.colIsSeries.Visible = True
        Me.colIsSeries.VisibleIndex = 7
        '
        'ListView1
        '
        Me.ListView1.Columns.AddRange(New System.Windows.Forms.ColumnHeader() {Me.VisitIDCol, Me.DateCol, Me.DOScol, Me.ChartStatus})
        Me.ListView1.FullRowSelect = True
        Me.ListView1.Location = New System.Drawing.Point(12, 12)
        Me.ListView1.MultiSelect = False
        Me.ListView1.Name = "ListView1"
        Me.ListView1.Size = New System.Drawing.Size(573, 216)
        Me.ListView1.TabIndex = 4
        Me.ListView1.UseCompatibleStateImageBehavior = False
        Me.ListView1.View = System.Windows.Forms.View.Details
        '
        'VisitIDCol
        '
        Me.VisitIDCol.Text = "Visit ID"
        Me.VisitIDCol.Width = 104
        '
        'DateCol
        '
        Me.DateCol.Text = "Creation Date"
        Me.DateCol.Width = 187
        '
        'DOScol
        '
        Me.DOScol.Text = "DOS"
        Me.DOScol.Width = 147
        '
        'ChartStatus
        '
        Me.ChartStatus.Text = "Facility Chart Status"
        Me.ChartStatus.Width = 131
        '
        'SeriesChartSelectionForm
        '
        Me.AcceptButton = Me.btnOpenExisting
        Me.ClientSize = New System.Drawing.Size(588, 272)
        Me.ControlBox = False
        Me.Controls.Add(Me.ListView1)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnNewSeriesChart)
        Me.Controls.Add(Me.btnOpenExisting)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "SeriesChartSelectionForm"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Select Series Chart"
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnOpenExisting As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnNewSeriesChart As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents colVisitID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsSeries As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ListView1 As System.Windows.Forms.ListView
    Friend WithEvents VisitIDCol As System.Windows.Forms.ColumnHeader
    Friend WithEvents DateCol As System.Windows.Forms.ColumnHeader
    Friend WithEvents DOScol As System.Windows.Forms.ColumnHeader
    Friend WithEvents ChartStatus As ColumnHeader
End Class

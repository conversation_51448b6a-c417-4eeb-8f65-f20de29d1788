﻿Imports System.IO
Imports System.Linq
Imports System.Threading
Imports System.Threading.Tasks
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports MICMiscUtilCSharp

Public Class LoadNcciEditsForm
    Private Sub ProgressBarForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ProgressBar1.Minimum = 0
        ProgressBar1.Maximum = 100

        ' ChooseFileTextBox.Text = "c:\\apps\\MicNCCIEditsjjc.csv"
        LoadButton.Enabled = False
    End Sub

    Private Sub UpDateProgress(progress As Integer)
        If progress = -1 Then
            Status2Label.Text = "Please wait while old records are deleted ... "
            Exit Sub
        Else
            Status2Label.Text = ""
        End If

        Me.ProgressBar1.Value = progress
        StatusLabel.Text = (progress / 100).ToString("#0.##%")

    End Sub

    Private _fileName As String = String.Empty
    Private Sub ChooseFileButton_Click(sender As Object, e As EventArgs) Handles ChooseFileButton.Click
        Dim ofd As New OpenFileDialog
        ofd.DefaultExt = "*.csv"
        ofd.Filter = String.Format("NCCI Edits (*.csv) | *.*")
        ofd.InitialDirectory = My.Computer.FileSystem.CurrentDirectory 'ECGlobals.ComboBoxListDir
        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub
        Me.ChooseFileTextBox.Text = ofd.FileName

        ofd.Dispose()
    End Sub
    Private tokenSource As CancellationTokenSource
    Private Async Sub LoadButton_Click(sender As Object, e As EventArgs) Handles LoadButton.Click
        UpDateProgress(0)
        _fileName = ChooseFileTextBox.Text

        CancelBtn.Enabled = True
        LoadButton.Enabled = False
        CloseButton.Enabled = False
        ChooseFileButton.Enabled = False
        ChooseFileTextBox.Enabled = False
        SaveButton.Enabled = False
        OptionsPanel.Enabled = False

        Dim progressIndicator = New Progress(Of Integer)(AddressOf UpDateProgress)
        tokenSource = New CancellationTokenSource
        Try
            Await Task.Run(Sub() CreateDBRecords(progressIndicator, deleteOldRadioButton.Checked, MergeRadioButton.Checked, tokenSource.Token))
        Catch ex As OperationCanceledException
            Status2Label.Text = "*** Cancelled ***"
        Catch ex As Exception
            If deleteOldRadioButton.Checked = False Then
                MessageBox.Show($"An unexpected error has occured. Try deleting the old records first.")
            Else
                MessageBox.Show($"An unexpected error has occured. Error Message : {ex?.Message}")
            End If
            Me.ProgressBar1.Value = 0
        End Try

        Beep()
        CancelBtn.Enabled = False
        LoadButton.Enabled = True
        CloseButton.Enabled = True
        ChooseFileButton.Enabled = True
        ChooseFileTextBox.Enabled = True
        SaveButton.Enabled = True
        OptionsPanel.Enabled = True

    End Sub


    Public Sub deleteRecs()
        Dim sw As New Stopwatch
        sw.Start()

        Using uow As New UnitOfWork
            'Dim stuff = New XPCollection(Of DONCCIEdit)(uow)
            'uow.Delete(stuff)
            'uow.CommitChanges()
            'uow.PurgeDeletedObjects()

            uow.DeleteByCriteria(Of DONCCIEdit)(Nothing)
            uow.CommitChanges()

            'uow.ExecuteNonQuery("truncate 'DONcciEdit'")
            'uow.ExecuteNonQuery("DELETE FROM DONCCIEdit")
        End Using
        sw.Stop()
        Debug.WriteLine(sw.ElapsedMilliseconds)

    End Sub

    Public Sub CreateDBRecords(progress As IProgress(Of Integer), deleteOldFirst As Boolean, mergeData As Boolean, cancelToken As CancellationToken)
        If deleteOldFirst Then
            progress.Report(-1)
            deleteRecs()
        End If

        Dim lines As String()
        Dim ffile As New FileInfo(_fileName)
        lines = File.ReadAllLines(ffile.FullName)
        Dim lineCount = lines.Length

        Dim drugCodes As New List(Of String)({"96360", "96361", "96365", "96366", "96367", "96368", "96372", "96374", "96375", "96376", "96401", "96402", "96409", "96411", "96413", "96415", "96417"})
        Dim keepCount As Integer = 0
        Dim TotalSoFar As Integer = 0
        Dim uow As New UnitOfWork()
        For Each line In lines
            cancelToken.ThrowIfCancellationRequested()

            keepCount += 1
            TotalSoFar += 1
            progress.Report((TotalSoFar * 100 / lineCount))

            If keepCount = 1000 Then
                keepCount = 0
                If uow IsNot Nothing Then
                    uow.CommitChanges()
                End If
                uow = New UnitOfWork()
            End If

            If line.Contains("Column 1") Then Continue For
            If line = "" Then Continue For
            If line.StartsWith("Procedure") Then Continue For

            Dim IsTabDelemited As Boolean = False
            Dim offset = 0
            Dim columns = Split(line, ",")
            If columns.Length < 5 Then 'we assume we are not loading a comma delimmeted file
                columns = Split(line, vbTab)
                IsTabDelemited = True
            End If

            Dim EffectiveDateCol = 3 '2 + offset
            Dim DeletedDateCol = 4   '3 + offset

            If columns(0).Length > 5 Then Continue For
            If String.IsNullOrWhiteSpace(columns(0)) Then Continue For

            Dim codePairRec As DONCCIEdit = Nothing
            If drugCodes.Contains(columns(1)) Then
                If columns(EffectiveDateCol) = columns(DeletedDateCol) Then
                    Continue For
                End If
                If columns.Length >= 6 AndAlso columns(5) = 9 Then
                    Continue For '9 means not allowed
                End If

                If mergeData Then
                    codePairRec = (From codePair In New XPQuery(Of DONCCIEdit)(uow)
                                   Where codePair.LeftColumn = columns(0) And codePair.RightColumn = columns(1)
                                   Select codePair).FirstOrDefault
                    If codePairRec IsNot Nothing Then
                        codePairRec.Update(columns(0), columns(1), columns(EffectiveDateCol), columns(DeletedDateCol))
                    End If
                End If

                If codePairRec Is Nothing Then
                    codePairRec = New DONCCIEdit(uow, columns(0), columns(1), columns(EffectiveDateCol), columns(DeletedDateCol))
                End If

                codePairRec.Save()
            End If
        Next

        If uow IsNot Nothing Then
            uow.CommitChanges()
            uow.Dispose()
        End If
    End Sub

    Private Sub CancelButton_Click(sender As Object, e As EventArgs) Handles CancelBtn.Click
        ProgressBar1.Value = 0
        If tokenSource IsNot Nothing Then
            tokenSource.Cancel()
        End If

    End Sub

    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles ChooseFileTextBox.TextChanged
        If String.IsNullOrWhiteSpace(ChooseFileTextBox.Text) Then
            LoadButton.Enabled = False
        Else
            LoadButton.Enabled = True
        End If
    End Sub

    Private Sub CloseButton_Click(sender As Object, e As EventArgs) Handles CloseButton.Click
        'Me.Close()
    End Sub

    Private Sub SaveButton_Click(sender As Object, e As EventArgs) Handles SaveButton.Click
        SaveNcciEditsToCsv()
    End Sub


    Private Sub SaveNcciEditsToCsv()
        ' Dim fileOut = New StreamWriter("c:\\apps\\MicNCCIEditsBig.csv")
        Dim fsa As New SaveFileDialog
        fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
        fsa.Filter = "NCCI Edits|*.csv"
        fsa.DefaultExt = "*.txt"
        fsa.FileName = "MicNCCIEdits.csv"

        Dim result As DialogResult = fsa.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub

        Dim fi As New FileInfo(fsa.FileName)
        Dim fs As FileStream

        If fi.Exists Then
            fs = fi.Open(FileMode.Truncate, FileAccess.Write)
        Else
            fs = fi.OpenWrite
        End If

        Dim fileOut As New StreamWriter(fs)

        ' Dim sb As New StringBuilder
        Dim query = New XPQuery(Of DONCCIEdit)(XpoDefault.Session)
        Const PriorTo96Flag = ""
        For Each row In query
            fileOut.WriteLine($"{row.LeftColumn},{row.RightColumn},{PriorTo96Flag},{row.EffectiveDate.ToString("yyyyMMdd")},{row.DeletedDate?.ToString("yyyyMMdd")}")
        Next
        fileOut.Close()

        MessageBox.Show("Export Successful")
        Return
    End Sub

End Class
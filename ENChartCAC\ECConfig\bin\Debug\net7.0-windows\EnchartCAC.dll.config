﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="ENChartCAC.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <connectionStrings>
    <!--<add name="ENChartCAC.My.MySettings.DefualtConnectionString" connectionString="ESS;User ID=ICAdmin; password=***;server = ************,1433;integrated security=false; initial catalog=enchartplustest;MultipleActiveResultSets=true; " />-->
    <add name="ENChartCAC.My.MySettings.DefualtConnectionString" connectionString="ESS;Server=localhost;integrated security=true; initial catalog=enchartplustest;MultipleActiveResultSets=true;Encrypt=false; TrustServerCertificate=true " />
     </connectionStrings>
  <applicationSettings>
    <ENChartCAC.My.MySettings>
      <setting name="ProductName" serializeAs="String">
        <value>Ventus Intelligent Coding</value>
      </setting>
      <setting name="CompanyName" serializeAs="String">
        <value>Altera</value>
      </setting>
      <setting name="DefaultTreatmentArea" serializeAs="String">
        <value>Emergency Department</value>
      </setting>
      <setting name="DOErrorLogLevel" serializeAs="String">
        <value>All</value>
      </setting>
      <setting name="UploadWorkstationLog" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="test" serializeAs="String">
        <value>***</value>
      </setting>
      <setting name="sunshineAndRoses" serializeAs="String">
        <value>read and blue</value>
      </setting>
    </ENChartCAC.My.MySettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/The Bezier</value>
        <!--<value>Skin/iMaginary</value>-->
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value>Custom/Custom Palette #2</value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>PerMonitorV2</value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value>
          <CustomPaletteCollection>
            <Skin Name="The Bezier">
              <SvgPalette Name="Custom Palette #1">
                <SvgColor Name="Paint" Value="248,250,251" />
                <SvgColor Name="Paint High" Value="255,255,255" />
                <SvgColor Name="Paint Shadow" Value="208,221,255" />
                <SvgColor Name="Paint Deep Shadow" Value="218,218,218" />
                <SvgColor Name="Brush" Value="38,38,38" />
                <SvgColor Name="Brush Light" Value="102,102,102" />
                <SvgColor Name="Brush High" Value="38,38,38" />
                <SvgColor Name="Brush Major" Value="171,171,171" />
                <SvgColor Name="Brush Minor" Value="210,210,210" />
                <SvgColor Name="Accent Paint" Value="52,83,168" />
                <SvgColor Name="Accent Paint Light" Value="209,221,255" />
                <SvgColor Name="Accent Brush" Value="White" />
                <SvgColor Name="Accent Brush Light" Value="112,164,235" />
                <SvgColor Name="Key Paint" Value="41,58,101" />
                <SvgColor Name="Key Brush" Value="255,255,255" />
                <SvgColor Name="Key Brush Light" Value="120,170,226" />
                <SvgColor Name="Red" Value="235,60,0" />
                <SvgColor Name="Green" Value="104,164,144" />
                <SvgColor Name="Blue" Value="77,130,184" />
                <SvgColor Name="Yellow" Value="234,194,130" />
                <SvgColor Name="Black" Value="114,114,114" />
                <SvgColor Name="Gray" Value="175,173,175" />
                <SvgColor Name="White" Value="255,255,255" />
                <SvgColor Name="altRed" Value="235,60,0" />
                <SvgColor Name="altGreen" Value="104,164,144" />
                <SvgColor Name="altBlue" Value="77,130,184" />
                <SvgColor Name="altYellow" Value="234,194,130" />
                <SvgColor Name="altBlack" Value="114,114,114" />
                <SvgColor Name="altWhite" Value="255,255,255" />
              </SvgPalette>
              <SvgPalette Name="Custom Palette #2">
                <SvgColor Name="Paint" Value="248,250,251" />
                <SvgColor Name="Paint High" Value="255,255,255" />
                <SvgColor Name="Paint Shadow" Value="208,221,255" />
                <SvgColor Name="Paint Deep Shadow" Value="218,218,218" />
                <SvgColor Name="Brush" Value="38,38,38" />
                <SvgColor Name="Brush Light" Value="102,102,102" />
                <SvgColor Name="Brush High" Value="38,38,38" />
                <SvgColor Name="Brush Major" Value="171,171,171" />
                <SvgColor Name="Brush Minor" Value="210,210,210" />
                <SvgColor Name="Accent Paint" Value="87,112,204" />
                <SvgColor Name="Accent Paint Light" Value="209,221,255" />
                <SvgColor Name="Accent Brush" Value="White" />
                <SvgColor Name="Accent Brush Light" Value="112,164,235" />
                <SvgColor Name="Key Paint" Value="42,65,132" />
                <SvgColor Name="Key Brush" Value="255,255,255" />
                <SvgColor Name="Key Brush Light" Value="120,170,226" />
                <SvgColor Name="Red" Value="235,60,0" />
                <SvgColor Name="Green" Value="104,164,144" />
                <SvgColor Name="Blue" Value="77,130,184" />
                <SvgColor Name="Yellow" Value="234,194,130" />
                <SvgColor Name="Black" Value="114,114,114" />
                <SvgColor Name="Gray" Value="175,173,175" />
                <SvgColor Name="White" Value="255,255,255" />
                <SvgColor Name="altRed" Value="235,60,0" />
                <SvgColor Name="altGreen" Value="104,164,144" />
                <SvgColor Name="altBlue" Value="77,130,184" />
                <SvgColor Name="altYellow" Value="234,194,130" />
                <SvgColor Name="altBlack" Value="114,114,114" />
                <SvgColor Name="altWhite" Value="255,255,255" />
              </SvgPalette>
            </Skin>
          </CustomPaletteCollection>
        </value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value>Tahoma;10</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value>Custom/Custom Palette #2</value>
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
</configuration>
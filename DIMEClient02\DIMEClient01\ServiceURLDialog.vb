Imports System.Windows.Forms

Public Class ServiceURLDialog

    Dim _service_url As String

    Public Overloads Function ShowDialog(ByVal passed_url As String) As DialogResult
        txtServiceURL.Text = passed_url
        Return Me.ShowDialog()
    End Function

    Public Property WebServiceURL() As String
        Get
            Return _service_url
        End Get
        Set(ByVal value As String)
            _service_url = value
        End Set
    End Property

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.OK

        WebServiceURL = txtServiceURL.Text

        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub btnUseMyIP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUseMyIP.Click
        Dim h As System.Net.IPHostEntry = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName)
        Dim ip As String = CType(h.AddressList.GetValue(0), System.Net.IPAddress).ToString

        txtServiceURL.Text = "http://" & ip & "/WSTest3/ECMcKessonService.asmx"
    End Sub
End Class

﻿NOTE ABOUT THIS PROJECT
---------------------------------

Occasionally when changes are made to this project's web reference, some code in the ECWS\Reference.map\Reference.vb
file is changed and removed. This typically happens when changes are made to the web reference, or when the project
is upgraded. You will know this happened if you see the following errors:

	o Too many arguments to 'Public Sub New()'
	o 'RequestSoapContext' is not a member of 'DIMEClient01.ECWS.Service'

These errors can be resolved by making the following changes to ECWS\Reference.map\Reference.vb:

	o Class Service should inherit 'Microsoft.Web.Services2.WebServicesClientProtocol', not
	  'Inherits System.Web.Services.Protocols.SoapHttpClientProtocol'
	o Class Service should have an additional constructor as follows:
		Public Sub New(ByVal passed_url As String)
            Me.New()
            Me.Url = passed_url
        End Sub

A NOTE ABOUT RESENDING A FOLDER OF CHARTS
---------------------------------

It's best practice not to re-send the Data Link output folders. Instead, charts/folders to be re-sent should
be moved to a temporary folder for the purposes of re-sending. 

If the current day's output folder must be re-sent in addition to other days, be sure to re-send the current day's 
folder first. Otherwise, when the current day's folder is re-sent, that folder will now contain the charts from other 
folders and thus those charts will be sent an additional time.

DIMEClient01 To Do List
---------------------------------

* When sending a folder, add an option to insert a delay (seconds) before sending each chart

DIMEClient01 Revision History
---------------------------------
**************  (v. 3.0.0.0) 2015-11-18
	* US37254: SAP 17 Upgrade - Upgrade HEC Data Link - DIME Client Solution
	* Upgraded project to .NET 4.5.2
	* Upgraded DevExpress to v.15.2

**************  (v. 2.1.1.0) 2015-11-18
	* Reversioning to correspond with MIC release version

**************  (v. 1.0.0.10) 2015-08-27
	* US28790 - Automatic Logoff - HEC Datalink dime client tool
	* Add timeout functionality to this application
		References
			- Added reference to Timeout.dll
		Form1.vb
			- Import Timeout
			- Created sub SetupIdleTimeout to initialize the timeout component (~lines 72-74)
			- Call SetupIdleTimeout on form load (~line 33)
			- Created sub HandleIdleTimeoutShutdown to be called when a timeout occurs (~lines 75-77)
			- Suspend and Resume the timeout component as needed (~lines 82, 87, 90, 108, 121, 126, 133, 144)

**************  (v. 1.0.0.9) 2013-09-26
	* When sending a folder, recursively loop through that folder's sub-folders and send all charts found within
		SendResults.vb
			- This is a new class that represents the result (successes, failures, etc.) of charts being pushed to the Data Link
		Form1.vb
			- Moved much of the code within Sub btnSendFolder_Click to a new Function, SendFolder. Sub btnSendFolder_Click now just makes a call to
			  SendFolder and displays the results
			- New Function SendFolder takes a folder name as a parameter that is the folder of charts to be sent and returns a SendResults object. This
			  function will send to the Data Link all charts within the passed folder and also recursively calls SendFolder with any sub-folders and
			  compiles the results
			- Removed the delay between sending charts added in v.1.0.0.8

**************  (v. 1.0.0.8) 2013-08-12
	* Add a one second delay between each chart sent with the "Send Folder" function
		Form1.vb
			- After each chart is sent, sleep for one second (~line 133)

**************  (v. 1.0.0.7) 2013-05-29
	* Changed the continuous chart interval to a random time between zero and five minutes (~line 305)

**************  (v. 1.0.0.6) 2013-04-18
	* Upgrade project to VS2010
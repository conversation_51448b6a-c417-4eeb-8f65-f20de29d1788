﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class LoadNcciEditsForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ProgressBar1 = New System.Windows.Forms.ProgressBar()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.ChooseFileTextBox = New System.Windows.Forms.TextBox()
        Me.ChooseFileButton = New System.Windows.Forms.Button()
        Me.LoadButton = New System.Windows.Forms.Button()
        Me.CancelBtn = New System.Windows.Forms.Button()
        Me.CloseButton = New System.Windows.Forms.Button()
        Me.StatusLabel = New System.Windows.Forms.Label()
        Me.Status2Label = New System.Windows.Forms.Label()
        Me.SaveButton = New System.Windows.Forms.Button()
        Me.deleteOldRadioButton = New System.Windows.Forms.RadioButton()
        Me.MergeRadioButton = New System.Windows.Forms.RadioButton()
        Me.OptionsPanel = New System.Windows.Forms.Panel()
        Me.AppendRadioButton = New System.Windows.Forms.RadioButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.OptionsPanel.SuspendLayout()
        Me.SuspendLayout()
        '
        'ProgressBar1
        '
        Me.ProgressBar1.Location = New System.Drawing.Point(35, 142)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New System.Drawing.Size(719, 23)
        Me.ProgressBar1.TabIndex = 0
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'ChooseFileTextBox
        '
        Me.ChooseFileTextBox.Location = New System.Drawing.Point(35, 109)
        Me.ChooseFileTextBox.Name = "ChooseFileTextBox"
        Me.ChooseFileTextBox.Size = New System.Drawing.Size(633, 20)
        Me.ChooseFileTextBox.TabIndex = 1
        '
        'ChooseFileButton
        '
        Me.ChooseFileButton.Location = New System.Drawing.Point(681, 106)
        Me.ChooseFileButton.Name = "ChooseFileButton"
        Me.ChooseFileButton.Size = New System.Drawing.Size(75, 23)
        Me.ChooseFileButton.TabIndex = 2
        Me.ChooseFileButton.Text = "Choose File"
        Me.ChooseFileButton.UseVisualStyleBackColor = True
        '
        'LoadButton
        '
        Me.LoadButton.Location = New System.Drawing.Point(509, 188)
        Me.LoadButton.Name = "LoadButton"
        Me.LoadButton.Size = New System.Drawing.Size(75, 23)
        Me.LoadButton.TabIndex = 3
        Me.LoadButton.Text = "Load"
        Me.LoadButton.UseVisualStyleBackColor = True
        '
        'CancelBtn
        '
        Me.CancelBtn.Enabled = False
        Me.CancelBtn.Location = New System.Drawing.Point(594, 188)
        Me.CancelBtn.Name = "CancelBtn"
        Me.CancelBtn.Size = New System.Drawing.Size(75, 23)
        Me.CancelBtn.TabIndex = 4
        Me.CancelBtn.Text = "Cancel"
        Me.CancelBtn.UseVisualStyleBackColor = True
        '
        'CloseButton
        '
        Me.CloseButton.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.CloseButton.Location = New System.Drawing.Point(679, 188)
        Me.CloseButton.Name = "CloseButton"
        Me.CloseButton.Size = New System.Drawing.Size(75, 23)
        Me.CloseButton.TabIndex = 5
        Me.CloseButton.Text = "Close"
        Me.CloseButton.UseVisualStyleBackColor = True
        '
        'StatusLabel
        '
        Me.StatusLabel.AutoSize = True
        Me.StatusLabel.Location = New System.Drawing.Point(354, 168)
        Me.StatusLabel.Name = "StatusLabel"
        Me.StatusLabel.Size = New System.Drawing.Size(21, 13)
        Me.StatusLabel.TabIndex = 7
        Me.StatusLabel.Text = "0%"
        '
        'Status2Label
        '
        Me.Status2Label.AutoSize = True
        Me.Status2Label.Location = New System.Drawing.Point(32, 168)
        Me.Status2Label.Name = "Status2Label"
        Me.Status2Label.Size = New System.Drawing.Size(37, 13)
        Me.Status2Label.TabIndex = 8
        Me.Status2Label.Text = "Status"
        '
        'SaveButton
        '
        Me.SaveButton.Location = New System.Drawing.Point(424, 188)
        Me.SaveButton.Name = "SaveButton"
        Me.SaveButton.Size = New System.Drawing.Size(75, 23)
        Me.SaveButton.TabIndex = 9
        Me.SaveButton.Text = "SaveToDisk"
        Me.SaveButton.UseVisualStyleBackColor = True
        '
        'deleteOldRadioButton
        '
        Me.deleteOldRadioButton.AutoSize = True
        Me.deleteOldRadioButton.Location = New System.Drawing.Point(4, 2)
        Me.deleteOldRadioButton.Name = "deleteOldRadioButton"
        Me.deleteOldRadioButton.Size = New System.Drawing.Size(101, 17)
        Me.deleteOldRadioButton.TabIndex = 11
        Me.deleteOldRadioButton.Text = "Delete Old Data"
        Me.deleteOldRadioButton.UseVisualStyleBackColor = True
        '
        'MergeRadioButton
        '
        Me.MergeRadioButton.AutoSize = True
        Me.MergeRadioButton.Checked = True
        Me.MergeRadioButton.Location = New System.Drawing.Point(157, 2)
        Me.MergeRadioButton.Name = "MergeRadioButton"
        Me.MergeRadioButton.Size = New System.Drawing.Size(55, 17)
        Me.MergeRadioButton.TabIndex = 12
        Me.MergeRadioButton.TabStop = True
        Me.MergeRadioButton.Text = "Merge"
        Me.MergeRadioButton.UseVisualStyleBackColor = True
        '
        'OptionsPanel
        '
        Me.OptionsPanel.Controls.Add(Me.AppendRadioButton)
        Me.OptionsPanel.Controls.Add(Me.deleteOldRadioButton)
        Me.OptionsPanel.Controls.Add(Me.MergeRadioButton)
        Me.OptionsPanel.Location = New System.Drawing.Point(35, 191)
        Me.OptionsPanel.Name = "OptionsPanel"
        Me.OptionsPanel.Size = New System.Drawing.Size(340, 22)
        Me.OptionsPanel.TabIndex = 13
        '
        'AppendRadioButton
        '
        Me.AppendRadioButton.AutoSize = True
        Me.AppendRadioButton.Location = New System.Drawing.Point(264, 3)
        Me.AppendRadioButton.Name = "AppendRadioButton"
        Me.AppendRadioButton.Size = New System.Drawing.Size(62, 17)
        Me.AppendRadioButton.TabIndex = 13
        Me.AppendRadioButton.Text = "Append"
        Me.AppendRadioButton.UseVisualStyleBackColor = True
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(31, 22)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(529, 18)
        Me.Label1.TabIndex = 14
        Me.Label1.Text = "The import file should be a comma or tab delimited file with the following format" &
    ":"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(31, 49)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(353, 18)
        Me.Label2.TabIndex = 15
        Me.Label2.Text = "Code1,DrugCode,Ignored,EffectiveDate,DeletedDate"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(31, 76)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(306, 18)
        Me.Label3.TabIndex = 16
        Me.Label3.Text = "Dates should be in the format of YYYYMMDD"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(35, 238)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(469, 13)
        Me.Label4.TabIndex = 17
        Me.Label4.Text = "*Delete Old Data - Will delete all ""NCCI Edit"" records currently in the database " &
    "(before loading file)."
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(35, 256)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(434, 13)
        Me.Label5.TabIndex = 18
        Me.Label5.Text = "*Merge - Will update any existing ""code pairs"" and add any new ones. (Takes the l" &
    "ongest!)"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(35, 274)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(522, 13)
        Me.Label6.TabIndex = 19
        Me.Label6.Text = "*Append - Use this if you need to load from multiple files (All code pairs must b" &
    "e unique and not already in DB.)"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(35, 220)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(372, 13)
        Me.Label7.TabIndex = 20
        Me.Label7.Text = "*Confused? - Use the default Merge option! (The other options are just faster.)"
        '
        'LoadNcciEditsForm
        '
        Me.AcceptButton = Me.LoadButton
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(808, 328)
        Me.ControlBox = False
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.OptionsPanel)
        Me.Controls.Add(Me.SaveButton)
        Me.Controls.Add(Me.Status2Label)
        Me.Controls.Add(Me.StatusLabel)
        Me.Controls.Add(Me.CloseButton)
        Me.Controls.Add(Me.CancelBtn)
        Me.Controls.Add(Me.LoadButton)
        Me.Controls.Add(Me.ChooseFileButton)
        Me.Controls.Add(Me.ChooseFileTextBox)
        Me.Controls.Add(Me.ProgressBar1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Fixed3D
        Me.Name = "LoadNcciEditsForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "NCCI Edits Manager"
        Me.OptionsPanel.ResumeLayout(False)
        Me.OptionsPanel.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents ProgressBar1 As ProgressBar
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents ChooseFileTextBox As TextBox
    Friend WithEvents ChooseFileButton As Button
    Friend WithEvents LoadButton As Button
    Friend WithEvents CancelBtn As Button
    Friend WithEvents CloseButton As Button
    Friend WithEvents StatusLabel As Label
    Friend WithEvents Status2Label As Label
    Friend WithEvents SaveButton As Button
    Friend WithEvents deleteOldRadioButton As RadioButton
    Friend WithEvents MergeRadioButton As RadioButton
    Friend WithEvents OptionsPanel As Panel
    Friend WithEvents Label1 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents AppendRadioButton As RadioButton
    Friend WithEvents Label6 As Label
    Friend WithEvents Label7 As Label
End Class

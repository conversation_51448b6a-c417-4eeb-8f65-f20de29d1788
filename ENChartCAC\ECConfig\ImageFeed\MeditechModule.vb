﻿Imports System.Xml
Imports System.IO
Imports McKesson.HIC.ColdFeed
Imports DevExpress.Xpo

Public Class MeditechModule
    Implements IImageFeedModule

    Const MODULE_NAME As String = "Meditech Image Feed"
    Const MODULE_DESCRIPTION As String = "Batches of image documents exported with no index file. Document filenames contain chart information."
    Const IMAGE_FEED_MODE As String = "Meditech"

    Public ReadOnly Property ModuleName As String Implements IImageFeedModule.ModuleName
        Get
            Return MODULE_NAME
        End Get
    End Property

    Public ReadOnly Property ModuleDescription As String Implements IImageFeedModule.ModuleDescription
        Get
            Return MODULE_DESCRIPTION
        End Get
    End Property

    Public ReadOnly Property ImageFeedMode As String Implements IImageFeedModule.ImageFeedMode
        Get
            Return IMAGE_FEED_MODE
        End Get
    End Property

    Public Function LoadConfiguration() As Boolean Implements IImageFeedModule.LoadConfiguration
        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")

        txtDocumentPath.Text = nodeDocumentPath.InnerText
        dteStartDate.DateTime = CDate(nodeStartDate.InnerText)

        Return True
    End Function

    Public Function LoadMappings() As Boolean Implements IImageFeedModule.LoadMappings
        Try
            Dim maps As ColdFeedFieldMaps

            Using fs As New FileStream(".\ColdFeedFieldExportMaps.xml", FileMode.Open)
                Dim x As New Xml.Serialization.XmlSerializer(GetType(ColdFeedFieldMaps))
                maps = TryCast(x.Deserialize(fs), ColdFeedFieldMaps)
            End Using

            For Each cs As CustomSetting In GetCustomSettings(maps)
                If cs.key = "MeditechFormID" Then txtMeditechFormID.Text = cs.value
                If cs.key = "MeditechDeleteFlag" Then cboMeditechDeleteFlag.SelectedText = cs.value
                If cs.key = "TemporaryFilePath" Then txtTempFilePath.Text = cs.value
                If cs.key = "AppendVersionNumber" Then chkAppendVersionNumber.Checked = CBool(cs.value)
            Next
        Catch ex As Exception
            ' If there's a problem loading the file, notify the user but carry on since the most common problems are a missing file or missing settings,
            ' which will get created when saved anyway

            MessageBox.Show("There was a problem reading ColdFeedFieldExportMaps.xml")
        End Try

        Return True
    End Function

    Private Function GetCustomSettings(ByVal cffMaps As ColdFeedFieldMaps) As List(Of CustomSetting)
        If cffMaps.Count > 0 Then
            Return cffMaps(0).CustomSettings
        End If

        Return Nothing
    End Function

    Public Function SaveConfiguration() As Boolean Implements IImageFeedModule.SaveConfiguration
        If String.IsNullOrEmpty(txtDocumentPath.Text) Then
            MessageBox.Show("Document Path Cannot Be Empty", "Invalid Document Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtDocumentPath.Select()
            Return False
        End If

        Dim xdoc As New XmlDocument()
        xdoc.XmlResolver = Nothing
        xdoc.Load(".\ColdFeedExport.exe.config")

        Dim nodeDocumentPath As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageOutputPath""]/value")
        Dim nodeStartDate As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""StartDate""]/value")
        Dim nodeDocumentType As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFileType""]/value")
        Dim nodeImageFeedMode As XmlNode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFeedMode""]/value")

        nodeDocumentPath.InnerText = txtDocumentPath.Text
        nodeStartDate.InnerText = dteStartDate.DateTime.ToString("yyyy-MM-dd")
        nodeDocumentType.InnerText = "TIFF"
        nodeImageFeedMode.InnerText = ImageFeedMode

        xdoc.Save(".\ColdFeedExport.exe.config")

        Return True
    End Function

    Public Function SaveMappings() As Boolean Implements IImageFeedModule.SaveMappings
        If String.IsNullOrEmpty(txtDocumentPath.Text) Then
            MessageBox.Show("Temporary File Path Cannot Be Empty", "Invalid Temporary File Path", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtTempFilePath.Select()
            Return False
        End If

        Dim facilities As New XPCollection(Of DOFacility)
        Dim maps As New ColdFeedFieldMaps

        For Each fac As DOFacility In facilities
            Dim cffMap As New ColdFeedFieldMap
            cffMap.FacilityOID = fac.Oid

            cffMap.CustomSettings = New List(Of CustomSetting)
            cffMap.CustomSettings.Add(New CustomSetting() With {.key = "MeditechFormID", .value = txtMeditechFormID.Text})
            cffMap.CustomSettings.Add(New CustomSetting() With {.key = "MeditechDeleteFlag", .value = cboMeditechDeleteFlag.SelectedText})
            cffMap.CustomSettings.Add(New CustomSetting() With {.key = "TemporaryFilePath", .value = txtTempFilePath.Text})
            cffMap.CustomSettings.Add(New CustomSetting() With {.key = "AppendVersionNumber", .value = chkAppendVersionNumber.Checked.ToString})

            maps.Add(cffMap)
        Next

        Using fs As New FileStream(".\ColdFeedFieldExportMaps.xml", FileMode.Create)
            Try
                Dim x As New Xml.Serialization.XmlSerializer(GetType(ColdFeedFieldMaps))
                x.Serialize(fs, maps)
            Catch ex As Exception
                Return False
            End Try
        End Using

        Return True
    End Function

    Public Overrides Function ToString() As String
        Return MODULE_NAME
    End Function
End Class

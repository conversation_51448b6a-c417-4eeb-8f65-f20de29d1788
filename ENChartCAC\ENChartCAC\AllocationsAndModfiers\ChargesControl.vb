﻿Imports AIC.SharedData
Imports AIC.SharedData.CAM2
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid

Public Class ChargesControl
    Private CurrentView As Views.Grid.GridView
    Private bShowFacility As Boolean
    Private bShowObs As Boolean
    Public Property Charges As List(Of CAM2.Charge)

    Public Property SaveChanges As Boolean = False
    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Public Sub New(ByRef ChargesList As List(Of CAM2.Charge), bShowFacility As Boolean, bShowObs As Boolean)
        InitializeComponent()
        Me.CurrentView = Me.GridControl1.MainView

        Me.Charges = ChargesList
        Me.bShowFacility = bShowFacility
        Me.bShowObs = bShowObs
    End Sub

    Public Sub Init(ByRef ChargesList As List(Of CAM2.Charge), bShowFacility As Boolean, bShowObs As Boolean)
        Me.CurrentView = Me.GridControl1.MainView

        Me.Charges = ChargesList
        Me.bShowFacility = bShowFacility
        Me.bShowObs = bShowObs
    End Sub

    Private Sub ChargeAllocationsForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        GridControl1.DataSource = Charges
        GridView1.ExpandAllGroups()
    End Sub

    Private Sub GridControl1_MouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles GridControl1.MouseDoubleClick
        ECLog.WriteEntry("GridControl1_MouseDoubleClick")
        Dim HitInfo As DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo = Me.CurrentView.CalcHitInfo(New Point(e.X, e.Y))

        If Not HitInfo.InDataRow Then Return 'if they didn't click an actual row, then do nothing

        Dim rowData = GetCurrentRow()
        EditCharge()

    End Sub


    Private Function GetCurrentRow() As CAM2.Charge
        If Me.CurrentView.FocusedRowHandle < 0 Then Return Nothing
        Return Me.CurrentView.GetRow(Me.CurrentView.FocusedRowHandle)
    End Function

    Private Sub EditBtn_Click(sender As Object, e As EventArgs) Handles EditBtn.Click
        EditCharge()
    End Sub

    Private Sub EditCharge()
        Try
            Dim charge = GetCurrentRow()
            Debug.WriteLine($"{charge.Hcpcs}")

            Dim f As New ChargeAllocationEditForm(charge, bShowFacility, bShowObs)
            f.ShowDialog()
            If f.SaveChanges Then
                SaveChanges = SaveChanges Or f.SaveChanges
            End If


            GridControl1.RefreshDataSource()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As Views.Grid.RowStyleEventArgs) Handles GridView1.RowStyle
        Dim gv As GridView = sender
        Dim blah As Charge = gv.GetRow(e.RowHandle)
        If blah?.ChargeAllocations.Count > 0 OrElse blah?.Modifiers.Count > 0 Then
            'e.Appearance.BackColor = Color.Green
            e.Appearance.ForeColor = Color.Blue
            e.Appearance.Font = New Font(e.Appearance.Font, FontStyle.Bold)
        End If
    End Sub
End Class


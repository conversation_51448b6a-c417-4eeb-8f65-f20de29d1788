﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class DataViewer
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.tabs = New DevExpress.XtraTab.XtraTabControl()
        Me.tabpLongitudinal = New DevExpress.XtraTab.XtraTabPage()
        Me.txtLongRecURL = New DevExpress.XtraEditors.TextEdit()
        Me.memoLongRec = New DevExpress.XtraEditors.MemoEdit()
        Me.tabpOrders = New DevExpress.XtraTab.XtraTabPage()
        Me.txtOrdersURL = New DevExpress.XtraEditors.TextEdit()
        Me.memoOrders = New DevExpress.XtraEditors.MemoEdit()
        Me.tabpMedOrders = New DevExpress.XtraTab.XtraTabPage()
        Me.txtMedOrdersURL = New DevExpress.XtraEditors.TextEdit()
        Me.memoMedOrders = New DevExpress.XtraEditors.MemoEdit()
        Me.tabpMedAdmins = New DevExpress.XtraTab.XtraTabPage()
        Me.txtMedAdminsURL = New DevExpress.XtraEditors.TextEdit()
        Me.memoMedAdmins = New DevExpress.XtraEditors.MemoEdit()
        Me.tabpVisitEvents = New DevExpress.XtraTab.XtraTabPage()
        Me.txtVisitEventsURL = New DevExpress.XtraEditors.TextEdit()
        Me.memoVisitEvents = New DevExpress.XtraEditors.MemoEdit()
        Me.tabpIVFluids = New DevExpress.XtraTab.XtraTabPage()
        Me.memoIVFluids = New DevExpress.XtraEditors.MemoEdit()
        Me.txtIVFluidURL = New DevExpress.XtraEditors.TextEdit()
        Me.tabpTimers = New DevExpress.XtraTab.XtraTabPage()
        Me.memoTimers = New DevExpress.XtraEditors.MemoEdit()
        Me.lblVisitID = New DevExpress.XtraEditors.LabelControl()
        Me.txtVisitID = New DevExpress.XtraEditors.TextEdit()
        Me.btnFetch = New DevExpress.XtraEditors.SimpleButton()
        Me.chkBasicInfo = New DevExpress.XtraEditors.CheckEdit()
        Me.btnMap = New DevExpress.XtraEditors.SimpleButton()
        Me.lblStatus = New DevExpress.XtraEditors.LabelControl()
        Me.cboVisitID = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.btnLoadFromFile = New DevExpress.XtraEditors.SimpleButton()
        Me.FileToUseCbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnMapDataFromFile = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        CType(Me.tabs, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabs.SuspendLayout()
        Me.tabpLongitudinal.SuspendLayout()
        CType(Me.txtLongRecURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.memoLongRec.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpOrders.SuspendLayout()
        CType(Me.txtOrdersURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.memoOrders.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpMedOrders.SuspendLayout()
        CType(Me.txtMedOrdersURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.memoMedOrders.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpMedAdmins.SuspendLayout()
        CType(Me.txtMedAdminsURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.memoMedAdmins.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpVisitEvents.SuspendLayout()
        CType(Me.txtVisitEventsURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.memoVisitEvents.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpIVFluids.SuspendLayout()
        CType(Me.memoIVFluids.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtIVFluidURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tabpTimers.SuspendLayout()
        CType(Me.memoTimers.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtVisitID.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkBasicInfo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboVisitID.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FileToUseCbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        Me.SuspendLayout()
        '
        'tabs
        '
        Me.tabs.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tabs.Location = New System.Drawing.Point(12, 103)
        Me.tabs.Name = "tabs"
        Me.tabs.Padding = New System.Windows.Forms.Padding(13)
        Me.tabs.SelectedTabPage = Me.tabpLongitudinal
        Me.tabs.Size = New System.Drawing.Size(933, 681)
        Me.tabs.TabIndex = 0
        Me.tabs.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.tabpLongitudinal, Me.tabpOrders, Me.tabpMedOrders, Me.tabpMedAdmins, Me.tabpVisitEvents, Me.tabpIVFluids, Me.tabpTimers})
        '
        'tabpLongitudinal
        '
        Me.tabpLongitudinal.Controls.Add(Me.txtLongRecURL)
        Me.tabpLongitudinal.Controls.Add(Me.memoLongRec)
        Me.tabpLongitudinal.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpLongitudinal.Name = "tabpLongitudinal"
        Me.tabpLongitudinal.Size = New System.Drawing.Size(794, 689)
        Me.tabpLongitudinal.Text = "Longitudinal Data"
        '
        'txtLongRecURL
        '
        Me.txtLongRecURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtLongRecURL.Location = New System.Drawing.Point(3, 3)
        Me.txtLongRecURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtLongRecURL.Name = "txtLongRecURL"
        Me.txtLongRecURL.Properties.ReadOnly = True
        Me.txtLongRecURL.Size = New System.Drawing.Size(484, 20)
        Me.txtLongRecURL.TabIndex = 1
        '
        'memoLongRec
        '
        Me.memoLongRec.Location = New System.Drawing.Point(0, 27)
        Me.memoLongRec.Margin = New System.Windows.Forms.Padding(2)
        Me.memoLongRec.Name = "memoLongRec"
        Me.memoLongRec.Size = New System.Drawing.Size(1036, 604)
        Me.memoLongRec.TabIndex = 0
        '
        'tabpOrders
        '
        Me.tabpOrders.Controls.Add(Me.txtOrdersURL)
        Me.tabpOrders.Controls.Add(Me.memoOrders)
        Me.tabpOrders.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpOrders.Name = "tabpOrders"
        Me.tabpOrders.Size = New System.Drawing.Size(925, 689)
        Me.tabpOrders.Text = "Orders"
        '
        'txtOrdersURL
        '
        Me.txtOrdersURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtOrdersURL.Location = New System.Drawing.Point(3, 3)
        Me.txtOrdersURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtOrdersURL.Name = "txtOrdersURL"
        Me.txtOrdersURL.Properties.ReadOnly = True
        Me.txtOrdersURL.Size = New System.Drawing.Size(1006, 20)
        Me.txtOrdersURL.TabIndex = 2
        '
        'memoOrders
        '
        Me.memoOrders.Location = New System.Drawing.Point(-3, 27)
        Me.memoOrders.Margin = New System.Windows.Forms.Padding(2)
        Me.memoOrders.Name = "memoOrders"
        Me.memoOrders.Size = New System.Drawing.Size(1041, 606)
        Me.memoOrders.TabIndex = 1
        '
        'tabpMedOrders
        '
        Me.tabpMedOrders.Controls.Add(Me.txtMedOrdersURL)
        Me.tabpMedOrders.Controls.Add(Me.memoMedOrders)
        Me.tabpMedOrders.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpMedOrders.Name = "tabpMedOrders"
        Me.tabpMedOrders.Size = New System.Drawing.Size(925, 651)
        Me.tabpMedOrders.Text = "Med Orders"
        '
        'txtMedOrdersURL
        '
        Me.txtMedOrdersURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtMedOrdersURL.Location = New System.Drawing.Point(3, 3)
        Me.txtMedOrdersURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtMedOrdersURL.Name = "txtMedOrdersURL"
        Me.txtMedOrdersURL.Properties.ReadOnly = True
        Me.txtMedOrdersURL.Size = New System.Drawing.Size(828, 20)
        Me.txtMedOrdersURL.TabIndex = 2
        '
        'memoMedOrders
        '
        Me.memoMedOrders.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.memoMedOrders.Location = New System.Drawing.Point(0, 23)
        Me.memoMedOrders.Margin = New System.Windows.Forms.Padding(2)
        Me.memoMedOrders.Name = "memoMedOrders"
        Me.memoMedOrders.Size = New System.Drawing.Size(1167, 570)
        Me.memoMedOrders.TabIndex = 1
        '
        'tabpMedAdmins
        '
        Me.tabpMedAdmins.Controls.Add(Me.txtMedAdminsURL)
        Me.tabpMedAdmins.Controls.Add(Me.memoMedAdmins)
        Me.tabpMedAdmins.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpMedAdmins.Name = "tabpMedAdmins"
        Me.tabpMedAdmins.Size = New System.Drawing.Size(794, 689)
        Me.tabpMedAdmins.Text = "Med Admins"
        '
        'txtMedAdminsURL
        '
        Me.txtMedAdminsURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtMedAdminsURL.Location = New System.Drawing.Point(3, 3)
        Me.txtMedAdminsURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtMedAdminsURL.Name = "txtMedAdminsURL"
        Me.txtMedAdminsURL.Properties.ReadOnly = True
        Me.txtMedAdminsURL.Size = New System.Drawing.Size(697, 20)
        Me.txtMedAdminsURL.TabIndex = 2
        '
        'memoMedAdmins
        '
        Me.memoMedAdmins.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.memoMedAdmins.Location = New System.Drawing.Point(0, 23)
        Me.memoMedAdmins.Margin = New System.Windows.Forms.Padding(2)
        Me.memoMedAdmins.Name = "memoMedAdmins"
        Me.memoMedAdmins.Size = New System.Drawing.Size(1036, 608)
        Me.memoMedAdmins.TabIndex = 1
        '
        'tabpVisitEvents
        '
        Me.tabpVisitEvents.Controls.Add(Me.txtVisitEventsURL)
        Me.tabpVisitEvents.Controls.Add(Me.memoVisitEvents)
        Me.tabpVisitEvents.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpVisitEvents.Name = "tabpVisitEvents"
        Me.tabpVisitEvents.Size = New System.Drawing.Size(794, 689)
        Me.tabpVisitEvents.Text = "Visit Events"
        '
        'txtVisitEventsURL
        '
        Me.txtVisitEventsURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtVisitEventsURL.Location = New System.Drawing.Point(3, 3)
        Me.txtVisitEventsURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtVisitEventsURL.Name = "txtVisitEventsURL"
        Me.txtVisitEventsURL.Properties.ReadOnly = True
        Me.txtVisitEventsURL.Size = New System.Drawing.Size(697, 20)
        Me.txtVisitEventsURL.TabIndex = 2
        '
        'memoVisitEvents
        '
        Me.memoVisitEvents.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.memoVisitEvents.Location = New System.Drawing.Point(0, 23)
        Me.memoVisitEvents.Margin = New System.Windows.Forms.Padding(2)
        Me.memoVisitEvents.Name = "memoVisitEvents"
        Me.memoVisitEvents.Size = New System.Drawing.Size(1036, 608)
        Me.memoVisitEvents.TabIndex = 1
        '
        'tabpIVFluids
        '
        Me.tabpIVFluids.Controls.Add(Me.memoIVFluids)
        Me.tabpIVFluids.Controls.Add(Me.txtIVFluidURL)
        Me.tabpIVFluids.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpIVFluids.Name = "tabpIVFluids"
        Me.tabpIVFluids.Size = New System.Drawing.Size(794, 689)
        Me.tabpIVFluids.Text = "IV Fluids"
        '
        'memoIVFluids
        '
        Me.memoIVFluids.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.memoIVFluids.Location = New System.Drawing.Point(3, 23)
        Me.memoIVFluids.Margin = New System.Windows.Forms.Padding(2)
        Me.memoIVFluids.Name = "memoIVFluids"
        Me.memoIVFluids.Size = New System.Drawing.Size(1033, 608)
        Me.memoIVFluids.TabIndex = 1
        '
        'txtIVFluidURL
        '
        Me.txtIVFluidURL.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtIVFluidURL.Location = New System.Drawing.Point(3, 3)
        Me.txtIVFluidURL.Margin = New System.Windows.Forms.Padding(2)
        Me.txtIVFluidURL.Name = "txtIVFluidURL"
        Me.txtIVFluidURL.Properties.ReadOnly = True
        Me.txtIVFluidURL.Size = New System.Drawing.Size(697, 20)
        Me.txtIVFluidURL.TabIndex = 0
        '
        'tabpTimers
        '
        Me.tabpTimers.Controls.Add(Me.memoTimers)
        Me.tabpTimers.Margin = New System.Windows.Forms.Padding(2)
        Me.tabpTimers.Name = "tabpTimers"
        Me.tabpTimers.Size = New System.Drawing.Size(794, 689)
        Me.tabpTimers.Text = "Timers"
        '
        'memoTimers
        '
        Me.memoTimers.Dock = System.Windows.Forms.DockStyle.Fill
        Me.memoTimers.Location = New System.Drawing.Point(0, 0)
        Me.memoTimers.Margin = New System.Windows.Forms.Padding(2)
        Me.memoTimers.Name = "memoTimers"
        Me.memoTimers.Size = New System.Drawing.Size(794, 689)
        Me.memoTimers.TabIndex = 2
        '
        'lblVisitID
        '
        Me.lblVisitID.Location = New System.Drawing.Point(12, 20)
        Me.lblVisitID.Name = "lblVisitID"
        Me.lblVisitID.Size = New System.Drawing.Size(42, 14)
        Me.lblVisitID.TabIndex = 1
        Me.lblVisitID.Text = "Visit ID:"
        '
        'txtVisitID
        '
        Me.txtVisitID.Location = New System.Drawing.Point(649, 14)
        Me.txtVisitID.Name = "txtVisitID"
        Me.txtVisitID.Size = New System.Drawing.Size(150, 20)
        Me.txtVisitID.TabIndex = 0
        Me.txtVisitID.Visible = False
        '
        'btnFetch
        '
        Me.btnFetch.Location = New System.Drawing.Point(280, 17)
        Me.btnFetch.Name = "btnFetch"
        Me.btnFetch.Size = New System.Drawing.Size(60, 20)
        Me.btnFetch.TabIndex = 3
        Me.btnFetch.Text = "Fetch"
        '
        'chkBasicInfo
        '
        Me.chkBasicInfo.EditValue = True
        Me.chkBasicInfo.Location = New System.Drawing.Point(422, 18)
        Me.chkBasicInfo.Name = "chkBasicInfo"
        Me.chkBasicInfo.Properties.Caption = "Show Basic Info Only"
        Me.chkBasicInfo.Size = New System.Drawing.Size(160, 19)
        Me.chkBasicInfo.TabIndex = 4
        '
        'btnMap
        '
        Me.btnMap.Enabled = False
        Me.btnMap.Location = New System.Drawing.Point(345, 17)
        Me.btnMap.Name = "btnMap"
        Me.btnMap.Size = New System.Drawing.Size(60, 20)
        Me.btnMap.TabIndex = 5
        Me.btnMap.Text = "Map"
        '
        'lblStatus
        '
        Me.lblStatus.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblStatus.Location = New System.Drawing.Point(14, 789)
        Me.lblStatus.Margin = New System.Windows.Forms.Padding(2)
        Me.lblStatus.Name = "lblStatus"
        Me.lblStatus.Size = New System.Drawing.Size(12, 14)
        Me.lblStatus.TabIndex = 6
        Me.lblStatus.Text = "..."
        '
        'cboVisitID
        '
        Me.cboVisitID.Location = New System.Drawing.Point(70, 17)
        Me.cboVisitID.Margin = New System.Windows.Forms.Padding(2)
        Me.cboVisitID.Name = "cboVisitID"
        Me.cboVisitID.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboVisitID.Properties.Items.AddRange(New Object() {"000100312052", "000100312053"})
        Me.cboVisitID.Size = New System.Drawing.Size(205, 20)
        Me.cboVisitID.TabIndex = 7
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'btnLoadFromFile
        '
        Me.btnLoadFromFile.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnLoadFromFile.Enabled = False
        Me.btnLoadFromFile.Location = New System.Drawing.Point(586, 19)
        Me.btnLoadFromFile.Name = "btnLoadFromFile"
        Me.btnLoadFromFile.Size = New System.Drawing.Size(115, 20)
        Me.btnLoadFromFile.TabIndex = 9
        Me.btnLoadFromFile.Text = "Load Data From File"
        Me.btnLoadFromFile.ToolTip = "Reload .json file"
        '
        'FileToUseCbo
        '
        Me.FileToUseCbo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.FileToUseCbo.Location = New System.Drawing.Point(6, 19)
        Me.FileToUseCbo.Name = "FileToUseCbo"
        Me.FileToUseCbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FileToUseCbo.Properties.NullValuePrompt = "Open a previously saved .json file"
        Me.FileToUseCbo.Properties.ShowToolTipForTrimmedText = DevExpress.Utils.DefaultBoolean.[True]
        Me.FileToUseCbo.Size = New System.Drawing.Size(574, 20)
        Me.FileToUseCbo.TabIndex = 10
        '
        'btnMapDataFromFile
        '
        Me.btnMapDataFromFile.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnMapDataFromFile.Enabled = False
        Me.btnMapDataFromFile.Location = New System.Drawing.Point(708, 19)
        Me.btnMapDataFromFile.Name = "btnMapDataFromFile"
        Me.btnMapDataFromFile.Size = New System.Drawing.Size(75, 20)
        Me.btnMapDataFromFile.TabIndex = 11
        Me.btnMapDataFromFile.Text = "Send to UI"
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.FileToUseCbo)
        Me.GroupBox1.Controls.Add(Me.btnLoadFromFile)
        Me.GroupBox1.Controls.Add(Me.btnMapDataFromFile)
        Me.GroupBox1.Enabled = False
        Me.GroupBox1.Location = New System.Drawing.Point(16, 43)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(785, 54)
        Me.GroupBox1.TabIndex = 7
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Simulate Making Paragon Mobile Api calls by loading previusly save PMAPI calls fr" &
    "om file."
        '
        'DataViewer
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(954, 848)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.cboVisitID)
        Me.Controls.Add(Me.lblStatus)
        Me.Controls.Add(Me.btnMap)
        Me.Controls.Add(Me.chkBasicInfo)
        Me.Controls.Add(Me.btnFetch)
        Me.Controls.Add(Me.txtVisitID)
        Me.Controls.Add(Me.lblVisitID)
        Me.Controls.Add(Me.tabs)
        Me.Name = "DataViewer"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Paragon Data Viewer"
        CType(Me.tabs, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabs.ResumeLayout(False)
        Me.tabpLongitudinal.ResumeLayout(False)
        CType(Me.txtLongRecURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.memoLongRec.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpOrders.ResumeLayout(False)
        CType(Me.txtOrdersURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.memoOrders.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpMedOrders.ResumeLayout(False)
        CType(Me.txtMedOrdersURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.memoMedOrders.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpMedAdmins.ResumeLayout(False)
        CType(Me.txtMedAdminsURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.memoMedAdmins.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpVisitEvents.ResumeLayout(False)
        CType(Me.txtVisitEventsURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.memoVisitEvents.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpIVFluids.ResumeLayout(False)
        CType(Me.memoIVFluids.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtIVFluidURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tabpTimers.ResumeLayout(False)
        CType(Me.memoTimers.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtVisitID.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkBasicInfo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboVisitID.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FileToUseCbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents tabs As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents tabpLongitudinal As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents tabpOrders As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents lblVisitID As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtVisitID As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnFetch As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents tabpMedAdmins As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents chkBasicInfo As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents tabpMedOrders As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents memoOrders As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents memoMedOrders As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents memoMedAdmins As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents memoLongRec As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents tabpVisitEvents As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents memoVisitEvents As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents tabpTimers As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents memoTimers As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents btnMap As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblStatus As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtLongRecURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtOrdersURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtMedOrdersURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtMedAdminsURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtVisitEventsURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tabpIVFluids As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents memoIVFluids As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents txtIVFluidURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cboVisitID As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents btnLoadFromFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents FileToUseCbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnMapDataFromFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupBox1 As GroupBox
End Class

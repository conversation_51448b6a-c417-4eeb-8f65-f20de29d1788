<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FormXMLImport
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        lblFacilityIDs = New DevExpress.XtraEditors.LabelControl()
        btnCancel = New DevExpress.XtraEditors.SimpleButton()
        btnImport = New DevExpress.XtraEditors.SimpleButton()
        fileSelectFile = New OpenFileDialog()
        txtReportFile = New DevExpress.XtraEditors.TextEdit()
        LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        btnBrowse = New DevExpress.XtraEditors.SimpleButton()
        txtFacilityIDs = New DevExpress.XtraEditors.TextEdit()
        ceDeleteConfig = New DevExpress.XtraEditors.CheckEdit()
        CType(txtReportFile.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(txtFacilityIDs.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ceDeleteConfig.Properties, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' lblFacilityIDs
        ' 
        lblFacilityIDs.Appearance.Font = New Font("Tahoma", 8.25F, FontStyle.Bold, GraphicsUnit.Point)
        lblFacilityIDs.Appearance.Options.UseFont = True
        lblFacilityIDs.Location = New Point(15, 22)
        lblFacilityIDs.Margin = New Padding(4, 3, 4, 3)
        lblFacilityIDs.Name = "lblFacilityIDs"
        lblFacilityIDs.Size = New Size(181, 13)
        lblFacilityIDs.TabIndex = 7
        lblFacilityIDs.Text = "Facility IDs (comma separated):"
        ' 
        ' btnCancel
        ' 
        btnCancel.DialogResult = DialogResult.Cancel
        btnCancel.Location = New Point(239, 132)
        btnCancel.Margin = New Padding(4, 3, 4, 3)
        btnCancel.Name = "btnCancel"
        btnCancel.Size = New Size(88, 25)
        btnCancel.TabIndex = 6
        btnCancel.Text = "Cancel"
        ' 
        ' btnImport
        ' 
        btnImport.Enabled = False
        btnImport.Location = New Point(145, 132)
        btnImport.Margin = New Padding(4, 3, 4, 3)
        btnImport.Name = "btnImport"
        btnImport.Size = New Size(88, 25)
        btnImport.TabIndex = 4
        btnImport.Text = "Import"
        ' 
        ' fileSelectFile
        ' 
        fileSelectFile.RestoreDirectory = True
        ' 
        ' txtReportFile
        ' 
        txtReportFile.Cursor = Cursors.IBeam
        txtReportFile.Location = New Point(15, 92)
        txtReportFile.Margin = New Padding(4, 3, 4, 3)
        txtReportFile.Name = "txtReportFile"
        txtReportFile.Properties.Appearance.BackColor = SystemColors.Window
        txtReportFile.Properties.Appearance.Options.UseBackColor = True
        txtReportFile.Size = New Size(217, 20)
        txtReportFile.TabIndex = 9
        ' 
        ' LabelControl1
        ' 
        LabelControl1.Appearance.Font = New Font("Tahoma", 8.25F, FontStyle.Bold, GraphicsUnit.Point)
        LabelControl1.Appearance.Options.UseFont = True
        LabelControl1.Location = New Point(14, 71)
        LabelControl1.Margin = New Padding(4, 3, 4, 3)
        LabelControl1.Name = "LabelControl1"
        LabelControl1.Size = New Size(151, 13)
        LabelControl1.TabIndex = 10
        LabelControl1.Text = "Select the XML Import File:"
        ' 
        ' btnBrowse
        ' 
        btnBrowse.Location = New Point(239, 88)
        btnBrowse.Margin = New Padding(4, 3, 4, 3)
        btnBrowse.Name = "btnBrowse"
        btnBrowse.Size = New Size(88, 25)
        btnBrowse.TabIndex = 11
        btnBrowse.Text = "Browse"
        ' 
        ' txtFacilityIDs
        ' 
        txtFacilityIDs.Location = New Point(14, 43)
        txtFacilityIDs.Margin = New Padding(4, 3, 4, 3)
        txtFacilityIDs.Name = "txtFacilityIDs"
        txtFacilityIDs.Size = New Size(313, 20)
        txtFacilityIDs.TabIndex = 5
        ' 
        ' ceDeleteConfig
        ' 
        ceDeleteConfig.EditValue = True
        ceDeleteConfig.Location = New Point(15, 163)
        ceDeleteConfig.Name = "ceDeleteConfig"
        ceDeleteConfig.Properties.Caption = "Delete existing config first"
        ceDeleteConfig.Size = New Size(312, 20)
        ceDeleteConfig.TabIndex = 12
        ' 
        ' FormXMLImport
        ' 
        AutoScaleDimensions = New SizeF(7F, 14F)
        AutoScaleMode = AutoScaleMode.Font
        ClientSize = New Size(341, 195)
        Controls.Add(ceDeleteConfig)
        Controls.Add(btnBrowse)
        Controls.Add(LabelControl1)
        Controls.Add(txtReportFile)
        Controls.Add(lblFacilityIDs)
        Controls.Add(btnCancel)
        Controls.Add(txtFacilityIDs)
        Controls.Add(btnImport)
        KeyPreview = True
        Margin = New Padding(4, 3, 4, 3)
        Name = "FormXMLImport"
        StartPosition = FormStartPosition.CenterParent
        Text = "Import From XML"
        CType(txtReportFile.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(txtFacilityIDs.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ceDeleteConfig.Properties, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
        PerformLayout()
    End Sub
    Friend WithEvents lblFacilityIDs As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnImport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents fileSelectFile As OpenFileDialog
    Friend WithEvents txtReportFile As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtFacilityIDs As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ceDeleteConfig As DevExpress.XtraEditors.CheckEdit
End Class

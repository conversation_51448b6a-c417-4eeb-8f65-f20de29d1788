﻿Option Infer On
#Region "Header"

'End Class

#End Region 'Header

Imports DevExpress.XtraEditors
''' <summary>
''' Calculates the complexity of data to be reviewed.
''' </summary>
''' <notes>
''' This was added for the 22.1 release.
''' It replaces the old method starting with chartformV17
''' </notes> 
Public Class ECRuleProfeeV2RiskDataComplexityRuleV2
    Inherits ECRule

#Region "Fields"

    Protected ComplexityControl As TextEdit
    Protected LUD As New Dictionary(Of String, Integer) 'LookUpDictionary

    Private _rulesDic As New Dictionary(Of String, ECRule) 'List(Of ECRule)
    Private _controlsDic As New Dictionary(Of String, Control)

#End Region 'Fields

#Region "Constructors"

    Public Sub New(ByVal pCC As TextEdit, ByVal pGrp As GroupControl)
        Me.ComplexityControl = pCC

        For Each c As Control In pGrp.Controls
            _controlsDic(c.Name) = c
        Next

        '*Using hardcoded text control names is never a good idea. It was done here for readability (and due to time constraints)
        _rulesDic("ReviewPriorNotes") = New ECRuleCheckBoxWithCounter(GetControl("ProfeeV2_RiskDP_1_chk"), GetControl("ProfeeV2_RiskDP_1_cbo"))
        _rulesDic("ReviewTests") = New ECRuleCheckBoxWithCounter(GetControl("ProfeeV2_RiskDP_2_chk"), GetControl("ProfeeV2_RiskDP_2_cbo"))
        _rulesDic("TestsOrdered") = New ECRuleCheckBoxWithCounter(GetControl("ProfeeV2_RiskDP_3_chk"), GetControl("ProfeeV2_RiskDP_3_cbo"))
        _rulesDic("RequiresHistorian") = New ECRuleControlWrapper(GetControl("ProfeeV2_RiskDP_4_chk"))

        _rulesDic("IndependentInterpretation") = New ECRuleControlWrapper(GetControl("ProfeeV2_RiskDP_5_chk"))
        _rulesDic("Discusion") = New ECRuleControlWrapper(GetControl("ProfeeV2_RiskDP_6_chk"))

        For Each rule In _rulesDic.Values
            Me.AddChild(rule)
        Next

    End Sub
#End Region 'Constructors
#Region "Methods"
    Private Function GetControl(controlName As String) As Control
        If String.IsNullOrEmpty(controlName) Then
            Throw New ArgumentException($"'{NameOf(controlName)}' cannot be null or empty.", NameOf(controlName))
        End If

        If _controlsDic.ContainsKey(controlName) Then
            Return _controlsDic(controlName)
        Else
            Return Nothing
        End If

    End Function
    Public Overrides Function HasValue() As Boolean
        Return False
    End Function
    Public Overrides Function Validate() As Boolean
        MyBase.Validate()

        Dim level As Integer ' = CalcComplexityLevel()

        For i = 4 To 0 Step -1
            If QaulifyLevel(i) Then
                level = i
                Exit For
            End If
        Next

        Select Case level
            Case 0
                If String.IsNullOrEmpty(ComplexityControl.EditValue) = False Then
                    If ComplexityControl.EditValue <> "" Then
                        ComplexityControl.EditValue = ""
                    End If
                End If
        '        Me.ComplexityControl.EditValue = ""
            Case 1
                Me.ComplexityControl.EditValue = "Minimal"
            Case 2
                Me.ComplexityControl.EditValue = "Limited"
            Case 3
                Me.ComplexityControl.EditValue = "Moderate"
            Case 4
                Me.ComplexityControl.EditValue = "Extensive"
        End Select

        Return True
    End Function
    Private Function QaulifyLevel(i As Integer) As Boolean

        Dim count As Int16 = 0
        Select Case i
            Case 4 '"extensive"
                count += QaulifyCat3()
                count += QaulifyCat2()
                count += QaulifyCat1()
                If count >= 2 Then Return True
                Return False
            Case 3 'moderate
                count += QaulifyCat3()
                count += QaulifyCat2()
                count += QaulifyCat1()
                If count >= 1 Then Return True
                Return False
            Case 2 'limited
                ' count += QaulifyCat3()
                count += QaulifyCat2a()
                count += QaulifyCat0()
                If count >= 1 Then Return True
                Return False
            Case 1 'Minimal
                If GetTotal() > 0 Then Return True
                Return False
            Case 0
                Return True
            Case Else
                Return False
        End Select
    End Function

    ''' <summary>
    ''' Discussion of Management or Test Interpretation
    ''' </summary>
    ''' <returns></returns>
    Private Function QaulifyCat3() As Integer
        Return GetCountForRule("Discusion")
    End Function

    ''' <summary>
    ''' Independent Interpretation of Test
    ''' </summary>
    ''' <returns></returns>
    Private Function QaulifyCat2() As Integer
        Return GetCountForRule("IndependentInterpretation")
    End Function

    ''' <summary>
    ''' Assessment Requires Independent Historian
    ''' </summary>
    ''' <returns></returns>
    Private Function QaulifyCat2a() As Integer
        Return GetCountForRule("RequiresHistorian")  '0
    End Function
    Private Function QaulifyCat1() As Integer
        Dim controlNames() = {"ProfeeV2_RiskDP_1_chk", "ProfeeV2_RiskDP_2_chk", "ProfeeV2_RiskDP_3_chk", "ProfeeV2_RiskDP_4_chk"}

        Dim count As Integer = 0
        count = QaulifyCat0()
        count += GetCountForRule("RequiresHistorian")

        If count >= 3 Then Return 1
        Return 0
    End Function

    Private Function QaulifyCat0() As Integer
        Dim controlNames() = {"ProfeeV2_RiskDP_1_chk", "ProfeeV2_RiskDP_2_chk", "ProfeeV2_RiskDP_3_chk"}

        Dim count As Int16 = 0

        count = GetCountForRule("ReviewPriorNotes")
        count += GetCountForRule("ReviewTests")
        count += GetCountForRule("TestsOrdered")

        If count >= 2 Then Return count '(yes, return the count, not 1)

        Return 0
    End Function


    Private Function IsChecked(controlName) As Boolean
        Dim total = 0
        For Each child As ECRuleControlWrapper In Children
            If child.InnerControl.Name = controlName Then
                If child.HasValue Then
                    Return True
                Else
                    Return False
                End If
            End If
        Next
        Return False
    End Function

    Private Function GetCountForRule(ruleName As String) As Integer
        Dim returnValue As Integer = 0
        If ruleName Is Nothing Then
            Throw New ArgumentNullException(NameOf(ruleName))
        End If

        Dim rule As ECRule = _rulesDic(ruleName)

        If TypeOf rule Is ECRuleCheckBoxWithCounter Then
            returnValue = DirectCast(rule, ECRuleCheckBoxWithCounter).GetCount
        Else
            returnValue = If(rule.HasValue, 1, 0)
        End If
        Return returnValue
    End Function

    Private Function GetTotal() As Integer
        Dim total = 0
        For Each child As ECRule In Children
            If child.HasValue Then
                total += 1
            End If
        Next
        Return total
    End Function

#End Region 'Methods
End Class

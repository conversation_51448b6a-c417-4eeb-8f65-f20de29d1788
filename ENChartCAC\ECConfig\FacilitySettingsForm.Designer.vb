﻿Imports System.Windows.Forms

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FacilitySettingsForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.gridFacilitySettings = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colOid = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFacilityName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFacilityOID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ricbFacilities = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.colFacilityID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTreatmentArea = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRuleSetting = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ricbSettingNames = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.colRuleSettingValue = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRuleSettingOrder = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRuleSettingDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRuleSettingCategory = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ricbSettingCategories = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.colSupportColumn01 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSupportColumn02 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHCPCS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCDM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        Me.chkSafeMode = New DevExpress.XtraEditors.CheckEdit()
        Me.ssFacilitySettings = New System.Windows.Forms.StatusStrip()
        Me.tsslMissingDefaultSettings = New System.Windows.Forms.ToolStripStatusLabel()
        CType(Me.gridFacilitySettings, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbFacilities, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbSettingNames, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ricbSettingCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkSafeMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ssFacilitySettings.SuspendLayout()
        Me.SuspendLayout()
        '
        'gridFacilitySettings
        '
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.First.Visible = False
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.Last.Visible = False
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.Next.Visible = False
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.NextPage.Visible = False
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.Prev.Visible = False
        Me.gridFacilitySettings.EmbeddedNavigator.Buttons.PrevPage.Visible = False
        Me.gridFacilitySettings.Location = New System.Drawing.Point(18, 12)
        Me.gridFacilitySettings.MainView = Me.GridView1
        Me.gridFacilitySettings.Name = "gridFacilitySettings"
        Me.gridFacilitySettings.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.ricbFacilities, Me.ricbSettingCategories, Me.ricbSettingNames})
        Me.gridFacilitySettings.Size = New System.Drawing.Size(864, 526)
        Me.gridFacilitySettings.TabIndex = 1
        Me.gridFacilitySettings.UseEmbeddedNavigator = True
        Me.gridFacilitySettings.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colOid, Me.colFacilityName, Me.colFacilityOID, Me.colFacilityID, Me.colTreatmentArea, Me.colRuleSetting, Me.colRuleSettingValue, Me.colRuleSettingOrder, Me.colRuleSettingDescription, Me.colRuleSettingCategory, Me.colSupportColumn01, Me.colSupportColumn02, Me.colHCPCS, Me.colCDM})
        Me.GridView1.GridControl = Me.gridFacilitySettings
        Me.GridView1.Name = "GridView1"
        Me.GridView1.NewItemRowText = "Add new row"
        Me.GridView1.OptionsFind.AlwaysVisible = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        '
        'colOid
        '
        Me.colOid.Caption = "Oid"
        Me.colOid.FieldName = "Oid"
        Me.colOid.Name = "colOid"
        Me.colOid.Width = 78
        '
        'colFacilityName
        '
        Me.colFacilityName.Caption = "Facility"
        Me.colFacilityName.FieldName = "Facility"
        Me.colFacilityName.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.colFacilityName.Name = "colFacilityName"
        Me.colFacilityName.OptionsColumn.AllowEdit = False
        Me.colFacilityName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.[True]
        Me.colFacilityName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.[True]
        Me.colFacilityName.Visible = True
        Me.colFacilityName.VisibleIndex = 0
        '
        'colFacilityOID
        '
        Me.colFacilityOID.Caption = "Facility OID"
        Me.colFacilityOID.ColumnEdit = Me.ricbFacilities
        Me.colFacilityOID.FieldName = "Facility"
        Me.colFacilityOID.Name = "colFacilityOID"
        '
        'ricbFacilities
        '
        Me.ricbFacilities.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.ricbFacilities.AutoHeight = False
        Me.ricbFacilities.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbFacilities.Name = "ricbFacilities"
        Me.ricbFacilities.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        '
        'colFacilityID
        '
        Me.colFacilityID.Caption = "Facility ID"
        Me.colFacilityID.FieldName = "FacilityID"
        Me.colFacilityID.Name = "colFacilityID"
        Me.colFacilityID.OptionsColumn.AllowEdit = False
        '
        'colTreatmentArea
        '
        Me.colTreatmentArea.Caption = "Tx Area"
        Me.colTreatmentArea.FieldName = "TreatmentArea"
        Me.colTreatmentArea.Name = "colTreatmentArea"
        Me.colTreatmentArea.Visible = True
        Me.colTreatmentArea.VisibleIndex = 1
        '
        'colRuleSetting
        '
        Me.colRuleSetting.Caption = "Setting"
        Me.colRuleSetting.ColumnEdit = Me.ricbSettingNames
        Me.colRuleSetting.FieldName = "RuleSetting"
        Me.colRuleSetting.Name = "colRuleSetting"
        Me.colRuleSetting.Visible = True
        Me.colRuleSetting.VisibleIndex = 2
        Me.colRuleSetting.Width = 290
        '
        'ricbSettingNames
        '
        Me.ricbSettingNames.AutoHeight = False
        Me.ricbSettingNames.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbSettingNames.Name = "ricbSettingNames"
        '
        'colRuleSettingValue
        '
        Me.colRuleSettingValue.Caption = "Value"
        Me.colRuleSettingValue.FieldName = "RuleSettingValue"
        Me.colRuleSettingValue.Name = "colRuleSettingValue"
        Me.colRuleSettingValue.Visible = True
        Me.colRuleSettingValue.VisibleIndex = 3
        Me.colRuleSettingValue.Width = 297
        '
        'colRuleSettingOrder
        '
        Me.colRuleSettingOrder.Caption = "Order"
        Me.colRuleSettingOrder.FieldName = "RuleSettingOrder"
        Me.colRuleSettingOrder.Name = "colRuleSettingOrder"
        '
        'colRuleSettingDescription
        '
        Me.colRuleSettingDescription.Caption = "Description"
        Me.colRuleSettingDescription.FieldName = "RuleSettingDescription"
        Me.colRuleSettingDescription.Name = "colRuleSettingDescription"
        '
        'colRuleSettingCategory
        '
        Me.colRuleSettingCategory.Caption = "Category"
        Me.colRuleSettingCategory.ColumnEdit = Me.ricbSettingCategories
        Me.colRuleSettingCategory.FieldName = "RuleSettingCategory"
        Me.colRuleSettingCategory.Name = "colRuleSettingCategory"
        Me.colRuleSettingCategory.Visible = True
        Me.colRuleSettingCategory.VisibleIndex = 4
        '
        'ricbSettingCategories
        '
        Me.ricbSettingCategories.AutoHeight = False
        Me.ricbSettingCategories.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ricbSettingCategories.Name = "ricbSettingCategories"
        '
        'colSupportColumn01
        '
        Me.colSupportColumn01.Caption = "Support 1"
        Me.colSupportColumn01.FieldName = "SupportColumn01"
        Me.colSupportColumn01.Name = "colSupportColumn01"
        '
        'colSupportColumn02
        '
        Me.colSupportColumn02.Caption = "Support 2"
        Me.colSupportColumn02.FieldName = "SupportColumn02"
        Me.colSupportColumn02.Name = "colSupportColumn02"
        '
        'colHCPCS
        '
        Me.colHCPCS.Caption = "HCPCS"
        Me.colHCPCS.FieldName = "HCPCS"
        Me.colHCPCS.Name = "colHCPCS"
        '
        'colCDM
        '
        Me.colCDM.Caption = "CDM"
        Me.colCDM.FieldName = "CDM"
        Me.colCDM.Name = "colCDM"
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.Location = New System.Drawing.Point(399, 544)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(97, 23)
        Me.btnOK.TabIndex = 2
        Me.btnOK.Text = "OK"
        '
        'chkSafeMode
        '
        Me.chkSafeMode.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkSafeMode.EditValue = True
        Me.chkSafeMode.Location = New System.Drawing.Point(18, 546)
        Me.chkSafeMode.Name = "chkSafeMode"
        Me.chkSafeMode.Properties.Caption = "Safe Mode"
        Me.chkSafeMode.Size = New System.Drawing.Size(77, 19)
        Me.chkSafeMode.TabIndex = 10
        Me.chkSafeMode.ToolTip = "Enable/Disable Safe Mode"
        '
        'ssFacilitySettings
        '
        Me.ssFacilitySettings.ImageScalingSize = New System.Drawing.Size(24, 24)
        Me.ssFacilitySettings.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsslMissingDefaultSettings})
        Me.ssFacilitySettings.Location = New System.Drawing.Point(0, 570)
        Me.ssFacilitySettings.Name = "ssFacilitySettings"
        Me.ssFacilitySettings.Size = New System.Drawing.Size(894, 22)
        Me.ssFacilitySettings.TabIndex = 12
        '
        'tsslMissingDefaultSettings
        '
        Me.tsslMissingDefaultSettings.IsLink = True
        Me.tsslMissingDefaultSettings.Name = "tsslMissingDefaultSettings"
        Me.tsslMissingDefaultSettings.Size = New System.Drawing.Size(16, 17)
        Me.tsslMissingDefaultSettings.Text = "..."
        '
        'FacilitySettingsForm
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(894, 592)
        Me.Controls.Add(Me.ssFacilitySettings)
        Me.Controls.Add(Me.chkSafeMode)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.gridFacilitySettings)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FacilitySettingsForm"
        Me.ShowIcon = False
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Facility Settings"
        Me.TopMost = True
        CType(Me.gridFacilitySettings, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbFacilities, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbSettingNames, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ricbSettingCategories, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkSafeMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ssFacilitySettings.ResumeLayout(False)
        Me.ssFacilitySettings.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents gridFacilitySettings As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRuleSetting As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRuleSettingValue As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents colFacilityOID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFacilityID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRuleSettingOrder As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRuleSettingDescription As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRuleSettingCategory As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSupportColumn01 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSupportColumn02 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHCPCS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents chkSafeMode As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ricbFacilities As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents ricbSettingCategories As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents ssFacilitySettings As StatusStrip
    Friend WithEvents tsslMissingDefaultSettings As ToolStripStatusLabel
    Friend WithEvents ricbSettingNames As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents colFacilityName As DevExpress.XtraGrid.Columns.GridColumn
End Class

﻿Imports DevExpress.Xpo
Imports EnchartDOLib

Public Class BOChartSummary
    Inherits BusinessObject

    Public Property ObsLOS As Integer
    Public Property ArrivalDate As Date
    Public Property ArrivalMode As String
    Public Property BorderDate As Date
    Public Property Chart As Integer
    Public Property ChartStatus As String
    Public Property CreationDate As Date
    Public Property CriticalCareEndDate As Date
    Public Property CriticalCareStartDate As Date
    Public Property DecisionDate As Date
    Public Property DispositionDate As Date
    Public Property Dispostion As String
    Public Property EMLevel As String
    Public Property EMLevelCDM As String
    Public Property EMLevelHCPCS As String
    Public Property ExceptionBillingRule As String
    Public Property ExceptionValue As String
    Public Property Facility As Integer
    Public Property HourIn As Integer
    Public Property ModifiedDate As Date
    Public Property NPPA As String
    Public Property NurseDisposition As String
    Public Property NurseInfusion As String
    Public Property NurseMedication As String
    Public Property NurseTriage As String
    Public Property ObsChartStatus As String
    Public Property ObsPhysician As String
    Public Property ObsEndDate As Date
    Public Property ObsStartDate As Date
    Public Property ObsTreatmentArea As String
    Public Property PhysChartStatus As String
    Public Property PhysEMLevelBilled As String
    Public Property PhysEMLevelBilledCDM As String
    Public Property PhysEMLevelBilledHCPCS As String
    Public Property PhysEMLevelCalculated As String
    Public Property PhysEMLevelOverride As String
    Public Property PhysExceptionValue As String
    Public Property Physician As String
    Public Property PhysicianMSEDate As Date
    Public Property PointTotal As Integer
    Public Property ProtocolInitiationDate As Date
    Public Property TreatmentArea As String
    Public Property TreatmentDate As Date
    Public Property TriageCategory As String
    Public Property TriageNurseDate As Date
    Public Property TriagePhysicianDate As Date
    Public Property UserIDLast As Integer
    Public Property UserIDOriginal As Integer
    Public Property Version As Integer
    Public Property VisitID As String
    Public Property VisitLength As Integer
    Public Property Exception As String
    Public Property FacilityID As String
    Public Property ValidDates As String
    Public Property BoarderDate As Date


    Public Shared Function UpdateDOFromBo(doCS As DOChartSummary, ByVal boCS As BOChartSummary) As DOChartSummary
        If boCS Is Nothing Then Return Nothing
        '       Dim doCS As DOChartSummary
        'If boCS.Oid > NEW_OBJ_OID Then
        '    doCS = XpoDefault.Session.GetObjectByKey(Of DOChartSummary)(boCS.Oid)
        'Else
        '    doCS = New DOChartSummary() With {.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCS.Chart)}
        'End If

        doCS.ObsLOS = boCS.ObsLOS
        doCS.ArrivalDate = boCS.ArrivalDate
        doCS.ArrivalMode = boCS.ArrivalMode
        doCS.BorderDate = boCS.BorderDate
        '     doCS.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCS.Chart)
        doCS.ChartStatus = boCS.ChartStatus
        doCS.CreationDate = boCS.CreationDate
        doCS.CriticalCareEndDate = boCS.CriticalCareEndDate
        doCS.CriticalCareStartDate = boCS.CriticalCareStartDate
        doCS.DecisionDate = boCS.DecisionDate
        doCS.DispositionDate = boCS.DispositionDate
        doCS.Dispostion = boCS.Dispostion
        doCS.EMLevel = boCS.EMLevel
        doCS.EMLevelCDM = boCS.EMLevelCDM
        doCS.EMLevelHCPCS = boCS.EMLevelHCPCS
        doCS.ExceptionBillingRule = boCS.ExceptionBillingRule
        doCS.ExceptionValue = boCS.ExceptionValue
        doCS.Facility = XpoDefault.Session.GetObjectByKey(Of DOFacility)(boCS.Facility)
        doCS.HourIn = boCS.HourIn
        doCS.ModifiedDate = boCS.ModifiedDate
        doCS.NPPA = boCS.NPPA
        doCS.NurseDisposition = boCS.NurseDisposition
        doCS.NurseInfusion = boCS.NurseInfusion
        doCS.NurseMedication = boCS.NurseMedication
        doCS.NurseTriage = boCS.NurseTriage
        doCS.ObsChartStatus = boCS.ObsChartStatus
        doCS.ObsPhysician = boCS.ObsPhysician
        doCS.ObsEndDate = boCS.ObsEndDate
        doCS.ObsStartDate = boCS.ObsStartDate
        doCS.ObsTreatmentArea = boCS.ObsTreatmentArea
        doCS.PhysChartStatus = boCS.PhysChartStatus
        doCS.PhysEMLevelBilled = boCS.PhysEMLevelBilled
        doCS.PhysEMLevelBilledCDM = boCS.PhysEMLevelBilledCDM
        doCS.PhysEMLevelBilledHCPCS = boCS.PhysEMLevelBilledHCPCS
        doCS.PhysEMLevelCalculated = boCS.PhysEMLevelCalculated
        doCS.PhysEMLevelOverride = boCS.PhysEMLevelOverride
        doCS.PhysExceptionValue = boCS.PhysExceptionValue
        doCS.Physician = boCS.Physician
        doCS.PhysicianMSEDate = boCS.PhysicianMSEDate
        doCS.PointTotal = boCS.PointTotal
        doCS.ProtocolInitiationDate = boCS.ProtocolInitiationDate
        doCS.TreatmentArea = boCS.TreatmentArea
        doCS.TreatmentDate = boCS.TreatmentDate
        doCS.TriageCategory = boCS.TriageCategory
        doCS.TriageNurseDate = boCS.TriageNurseDate
        doCS.TriagePhysicianDate = boCS.TriagePhysicianDate
        doCS.UserIDLast = boCS.UserIDLast
        doCS.UserIDOriginal = boCS.UserIDOriginal
        doCS.Version = boCS.Version
        doCS.VisitID = boCS.VisitID
        doCS.VisitLength = boCS.VisitLength
        doCS.Exception = boCS.Exception
        doCS.FacilityID = boCS.FacilityID
        doCS.ValidDates = boCS.ValidDates
        doCS.BoarderDate = boCS.BoarderDate
        Return doCS
    End Function


    Public Shared Widening Operator CType(ByVal boCS As BOChartSummary) As DOChartSummary
        If boCS Is Nothing Then Return Nothing
        Dim doCS As DOChartSummary
        If boCS.Oid > NEW_OBJ_OID Then
            doCS = XpoDefault.Session.GetObjectByKey(Of DOChartSummary)(boCS.Oid)
        Else
            doCS = New DOChartSummary() With {.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCS.Chart)}
        End If

        doCS.ObsLOS = boCS.ObsLOS
        doCS.ArrivalDate = boCS.ArrivalDate
        doCS.ArrivalMode = boCS.ArrivalMode
        doCS.BorderDate = boCS.BorderDate
        doCS.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCS.Chart)
        doCS.ChartStatus = boCS.ChartStatus
        doCS.CreationDate = boCS.CreationDate
        doCS.CriticalCareEndDate = boCS.CriticalCareEndDate
        doCS.CriticalCareStartDate = boCS.CriticalCareStartDate
        doCS.DecisionDate = boCS.DecisionDate
        doCS.DispositionDate = boCS.DispositionDate
        doCS.Dispostion = boCS.Dispostion
        doCS.EMLevel = boCS.EMLevel
        doCS.EMLevelCDM = boCS.EMLevelCDM
        doCS.EMLevelHCPCS = boCS.EMLevelHCPCS
        doCS.ExceptionBillingRule = boCS.ExceptionBillingRule
        doCS.ExceptionValue = boCS.ExceptionValue
        doCS.Facility = XpoDefault.Session.GetObjectByKey(Of DOFacility)(boCS.Facility)
        doCS.HourIn = boCS.HourIn
        doCS.ModifiedDate = boCS.ModifiedDate
        doCS.NPPA = boCS.NPPA
        doCS.NurseDisposition = boCS.NurseDisposition
        doCS.NurseInfusion = boCS.NurseInfusion
        doCS.NurseMedication = boCS.NurseMedication
        doCS.NurseTriage = boCS.NurseTriage
        doCS.ObsChartStatus = boCS.ObsChartStatus
        doCS.ObsPhysician = boCS.ObsPhysician
        doCS.ObsEndDate = boCS.ObsEndDate
        doCS.ObsStartDate = boCS.ObsStartDate
        doCS.ObsTreatmentArea = boCS.ObsTreatmentArea
        doCS.PhysChartStatus = boCS.PhysChartStatus
        doCS.PhysEMLevelBilled = boCS.PhysEMLevelBilled
        doCS.PhysEMLevelBilledCDM = boCS.PhysEMLevelBilledCDM
        doCS.PhysEMLevelBilledHCPCS = boCS.PhysEMLevelBilledHCPCS
        doCS.PhysEMLevelCalculated = boCS.PhysEMLevelCalculated
        doCS.PhysEMLevelOverride = boCS.PhysEMLevelOverride
        doCS.PhysExceptionValue = boCS.PhysExceptionValue
        doCS.Physician = boCS.Physician
        doCS.PhysicianMSEDate = boCS.PhysicianMSEDate
        doCS.PointTotal = boCS.PointTotal
        doCS.ProtocolInitiationDate = boCS.ProtocolInitiationDate
        doCS.TreatmentArea = boCS.TreatmentArea
        doCS.TreatmentDate = boCS.TreatmentDate
        doCS.TriageCategory = boCS.TriageCategory
        doCS.TriageNurseDate = boCS.TriageNurseDate
        doCS.TriagePhysicianDate = boCS.TriagePhysicianDate
        doCS.UserIDLast = boCS.UserIDLast
        doCS.UserIDOriginal = boCS.UserIDOriginal
        doCS.Version = boCS.Version
        doCS.VisitID = boCS.VisitID
        doCS.VisitLength = boCS.VisitLength
        doCS.Exception = boCS.Exception
        doCS.FacilityID = boCS.FacilityID
        doCS.ValidDates = boCS.ValidDates
        doCS.BoarderDate = boCS.BoarderDate
        Return doCS
    End Operator


    Public Shared Narrowing Operator CType(ByVal doCS As DOChartSummary) As BOChartSummary
        If doCS Is Nothing Then Return Nothing
        Dim boCS As New BOChartSummary
        boCS.ObsLOS = doCS.ObsLOS
        boCS.ArrivalDate = doCS.ArrivalDate
        boCS.ArrivalMode = doCS.ArrivalMode
        boCS.BorderDate = doCS.BorderDate
        boCS.Chart = If(doCS.Chart IsNot Nothing, doCS.Chart.Oid, NEW_OBJ_OID)
        boCS.ChartStatus = doCS.ChartStatus
        boCS.CreationDate = doCS.CreationDate
        boCS.CriticalCareEndDate = doCS.CriticalCareEndDate
        boCS.CriticalCareStartDate = doCS.CriticalCareStartDate
        boCS.DecisionDate = doCS.DecisionDate
        boCS.DispositionDate = doCS.DispositionDate
        boCS.Dispostion = doCS.Dispostion
        boCS.EMLevel = doCS.EMLevel
        boCS.EMLevelCDM = doCS.EMLevelCDM
        boCS.EMLevelHCPCS = doCS.EMLevelHCPCS
        boCS.ExceptionBillingRule = doCS.ExceptionBillingRule
        boCS.ExceptionValue = doCS.ExceptionValue
        boCS.Facility = If(doCS.Facility IsNot Nothing, doCS.Facility.Oid, NEW_OBJ_OID)
        boCS.HourIn = doCS.HourIn
        boCS.ModifiedDate = doCS.ModifiedDate
        boCS.NPPA = doCS.NPPA
        boCS.NurseDisposition = doCS.NurseDisposition
        boCS.NurseInfusion = doCS.NurseInfusion
        boCS.NurseMedication = doCS.NurseMedication
        boCS.NurseTriage = doCS.NurseTriage
        boCS.ObsChartStatus = doCS.ObsChartStatus
        boCS.ObsPhysician = doCS.ObsPhysician
        boCS.ObsEndDate = doCS.ObsEndDate
        boCS.ObsStartDate = doCS.ObsStartDate
        boCS.ObsTreatmentArea = doCS.ObsTreatmentArea
        boCS.PhysChartStatus = doCS.PhysChartStatus
        boCS.PhysEMLevelBilled = doCS.PhysEMLevelBilled
        boCS.PhysEMLevelBilledCDM = doCS.PhysEMLevelBilledCDM
        boCS.PhysEMLevelBilledHCPCS = doCS.PhysEMLevelBilledHCPCS
        boCS.PhysEMLevelCalculated = doCS.PhysEMLevelCalculated
        boCS.PhysEMLevelOverride = doCS.PhysEMLevelOverride
        boCS.PhysExceptionValue = doCS.PhysExceptionValue
        boCS.Physician = doCS.Physician
        boCS.PhysicianMSEDate = doCS.PhysicianMSEDate
        boCS.PointTotal = doCS.PointTotal
        boCS.ProtocolInitiationDate = doCS.ProtocolInitiationDate
        boCS.TreatmentArea = doCS.TreatmentArea
        boCS.TreatmentDate = doCS.TreatmentDate
        boCS.TriageCategory = doCS.TriageCategory
        boCS.TriageNurseDate = doCS.TriageNurseDate
        boCS.TriagePhysicianDate = doCS.TriagePhysicianDate
        boCS.UserIDLast = doCS.UserIDLast
        boCS.UserIDOriginal = doCS.UserIDOriginal
        boCS.Version = doCS.Version
        boCS.VisitID = doCS.VisitID
        boCS.VisitLength = doCS.VisitLength
        boCS.Exception = doCS.Exception
        boCS.FacilityID = doCS.FacilityID
        boCS.ValidDates = doCS.ValidDates
        boCS.BoarderDate = doCS.BoarderDate
        Return boCS
    End Operator
End Class

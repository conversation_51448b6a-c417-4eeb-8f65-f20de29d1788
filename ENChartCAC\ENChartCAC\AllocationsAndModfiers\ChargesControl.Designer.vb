﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ChargesControl
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colHcpcs = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChargeMaster = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTotalUnits = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colOriginalBillingdate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTreatmentArea = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCodeType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colESPValue = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.EditBtn = New System.Windows.Forms.Button()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.Location = New System.Drawing.Point(28, 20)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(877, 470)
        Me.GridControl1.TabIndex = 1
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colHcpcs, Me.colChargeMaster, Me.colTotalUnits, Me.colOriginalBillingdate, Me.colTreatmentArea, Me.colCodeType, Me.colESPValue, Me.colDescription})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colOriginalBillingdate, DevExpress.Data.ColumnSortOrder.Ascending), New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colTreatmentArea, DevExpress.Data.ColumnSortOrder.Ascending), New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colHcpcs, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'colHcpcs
        '
        Me.colHcpcs.FieldName = "Hcpcs"
        Me.colHcpcs.Name = "colHcpcs"
        Me.colHcpcs.Visible = True
        Me.colHcpcs.VisibleIndex = 2
        Me.colHcpcs.Width = 118
        '
        'colChargeMaster
        '
        Me.colChargeMaster.FieldName = "ChargeMaster"
        Me.colChargeMaster.Name = "colChargeMaster"
        Me.colChargeMaster.Visible = True
        Me.colChargeMaster.VisibleIndex = 4
        Me.colChargeMaster.Width = 73
        '
        'colTotalUnits
        '
        Me.colTotalUnits.Caption = "Units"
        Me.colTotalUnits.FieldName = "TotalUnits"
        Me.colTotalUnits.Name = "colTotalUnits"
        Me.colTotalUnits.Visible = True
        Me.colTotalUnits.VisibleIndex = 3
        Me.colTotalUnits.Width = 48
        '
        'colOriginalBillingdate
        '
        Me.colOriginalBillingdate.Caption = "Date"
        Me.colOriginalBillingdate.FieldName = "OriginalBillingdate"
        Me.colOriginalBillingdate.Name = "colOriginalBillingdate"
        Me.colOriginalBillingdate.Visible = True
        Me.colOriginalBillingdate.VisibleIndex = 0
        Me.colOriginalBillingdate.Width = 98
        '
        'colTreatmentArea
        '
        Me.colTreatmentArea.Caption = "Treatment Area/Nursing Unit"
        Me.colTreatmentArea.FieldName = "TreatmentArea"
        Me.colTreatmentArea.Name = "colTreatmentArea"
        Me.colTreatmentArea.Visible = True
        Me.colTreatmentArea.VisibleIndex = 1
        Me.colTreatmentArea.Width = 180
        '
        'colCodeType
        '
        Me.colCodeType.FieldName = "CodeType"
        Me.colCodeType.Name = "colCodeType"
        Me.colCodeType.Visible = True
        Me.colCodeType.VisibleIndex = 5
        Me.colCodeType.Width = 50
        '
        'colESPValue
        '
        Me.colESPValue.FieldName = "ESPValue"
        Me.colESPValue.Name = "colESPValue"
        Me.colESPValue.Visible = True
        Me.colESPValue.VisibleIndex = 7
        Me.colESPValue.Width = 44
        '
        'colDescription
        '
        Me.colDescription.FieldName = "Description"
        Me.colDescription.Name = "colDescription"
        Me.colDescription.Visible = True
        Me.colDescription.VisibleIndex = 6
        Me.colDescription.Width = 237
        '
        'EditBtn
        '
        Me.EditBtn.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EditBtn.Location = New System.Drawing.Point(830, 496)
        Me.EditBtn.Name = "EditBtn"
        Me.EditBtn.Size = New System.Drawing.Size(75, 23)
        Me.EditBtn.TabIndex = 3
        Me.EditBtn.Text = "Edit"
        Me.EditBtn.UseVisualStyleBackColor = True
        '
        'ChargesControl
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.EditBtn)
        Me.Controls.Add(Me.GridControl1)
        Me.Name = "ChargesControl"
        Me.Size = New System.Drawing.Size(922, 548)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colHcpcs As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChargeMaster As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTotalUnits As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colOriginalBillingdate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCodeType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colESPValue As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDescription As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents EditBtn As Button
End Class

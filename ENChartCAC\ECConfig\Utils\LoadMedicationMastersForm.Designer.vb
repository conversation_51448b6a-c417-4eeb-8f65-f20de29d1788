﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class LoadMedicationMastersForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.btnDone = New DevExpress.XtraEditors.SimpleButton()
        Me.btnLoadFile = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.lblGMTUsage = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.btnMerge = New DevExpress.XtraEditors.SimpleButton()
        Me.btnParagonLoad = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSaveJson = New DevExpress.XtraEditors.SimpleButton()
        Me.btnLoadJson = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSaveAsCsv = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.btnDeleteAll = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.btnViewMedIdMappings = New DevExpress.XtraEditors.SimpleButton()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection(Me.components)
        Me.UnitOfWork1 = New DevExpress.Xpo.UnitOfWork(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colEnabled = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMedName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCC = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDedicatedLine = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChemo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHormonal = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colMedId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel1.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UnitOfWork1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btnDone
        '
        Me.btnDone.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnDone.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.btnDone.Location = New System.Drawing.Point(583, 684)
        Me.btnDone.Name = "btnDone"
        Me.btnDone.Size = New System.Drawing.Size(129, 25)
        Me.btnDone.TabIndex = 6
        Me.btnDone.Text = "Save"
        Me.btnDone.ToolTip = "Save changes made in the grid"
        '
        'btnLoadFile
        '
        Me.btnLoadFile.Location = New System.Drawing.Point(6, 52)
        Me.btnLoadFile.Name = "btnLoadFile"
        Me.btnLoadFile.Size = New System.Drawing.Size(129, 25)
        Me.btnLoadFile.TabIndex = 5
        Me.btnLoadFile.Text = "Load From CSV File"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(20, 206)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(674, 38)
        Me.LabelControl1.TabIndex = 7
        Me.LabelControl1.Text = "Columns for importing from *.csv file should be :" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "Medication Name, Critical Care" &
    " Flag, Dedicated Line, Chemo, Hormonal" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'lblGMTUsage
        '
        Me.lblGMTUsage.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblGMTUsage.Appearance.Options.UseFont = True
        Me.lblGMTUsage.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.lblGMTUsage.Location = New System.Drawing.Point(20, 6)
        Me.lblGMTUsage.Name = "lblGMTUsage"
        Me.lblGMTUsage.Size = New System.Drawing.Size(692, 38)
        Me.lblGMTUsage.TabIndex = 8
        Me.lblGMTUsage.Text = "Global Medications Table" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl3.Location = New System.Drawing.Point(3, 9)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(689, 38)
        Me.LabelControl3.TabIndex = 9
        Me.LabelControl3.Text = "*Warning: If the DOMedicationMaster Table already contains data you will have to " &
    "first delete the existing records in the DOMedicationsMaster Table."
        '
        'Panel1
        '
        Me.Panel1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink
        Me.Panel1.Controls.Add(Me.LabelControl4)
        Me.Panel1.Controls.Add(Me.LabelControl3)
        Me.Panel1.Location = New System.Drawing.Point(20, 54)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(695, 146)
        Me.Panel1.TabIndex = 11
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl4.Location = New System.Drawing.Point(3, 83)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(677, 38)
        Me.LabelControl4.TabIndex = 10
        Me.LabelControl4.Text = "**Warning: Once charts have been created using the existing Medications data, med" &
    "ications should never be deleted."
        '
        'btnMerge
        '
        Me.btnMerge.Enabled = False
        Me.btnMerge.Location = New System.Drawing.Point(6, 20)
        Me.btnMerge.Name = "btnMerge"
        Me.btnMerge.Size = New System.Drawing.Size(129, 25)
        Me.btnMerge.TabIndex = 12
        Me.btnMerge.Text = "Merge"
        Me.btnMerge.ToolTip = "Create new Global Medicatons by merging existing facility CI medications."
        '
        'btnParagonLoad
        '
        Me.btnParagonLoad.Location = New System.Drawing.Point(6, 52)
        Me.btnParagonLoad.Name = "btnParagonLoad"
        Me.btnParagonLoad.Size = New System.Drawing.Size(129, 25)
        Me.btnParagonLoad.TabIndex = 13
        Me.btnParagonLoad.Text = "Paragon DataLink"
        Me.btnParagonLoad.ToolTip = "Load from Shawna's Custom file as CSV"
        '
        'btnSaveJson
        '
        Me.btnSaveJson.Location = New System.Drawing.Point(6, 20)
        Me.btnSaveJson.Name = "btnSaveJson"
        Me.btnSaveJson.Size = New System.Drawing.Size(129, 25)
        Me.btnSaveJson.TabIndex = 14
        Me.btnSaveJson.Text = "Save As JSON"
        '
        'btnLoadJson
        '
        Me.btnLoadJson.Location = New System.Drawing.Point(6, 52)
        Me.btnLoadJson.Name = "btnLoadJson"
        Me.btnLoadJson.Size = New System.Drawing.Size(129, 25)
        Me.btnLoadJson.TabIndex = 15
        Me.btnLoadJson.Text = "Load From JSON File"
        '
        'btnSaveAsCsv
        '
        Me.btnSaveAsCsv.Location = New System.Drawing.Point(6, 20)
        Me.btnSaveAsCsv.Name = "btnSaveAsCsv"
        Me.btnSaveAsCsv.Size = New System.Drawing.Size(129, 25)
        Me.btnSaveAsCsv.TabIndex = 16
        Me.btnSaveAsCsv.Text = "Save As CSV"
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.LabelControl6)
        Me.GroupBox1.Controls.Add(Me.LabelControl5)
        Me.GroupBox1.Controls.Add(Me.btnMerge)
        Me.GroupBox1.Controls.Add(Me.btnParagonLoad)
        Me.GroupBox1.Location = New System.Drawing.Point(23, 250)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(693, 89)
        Me.GroupBox1.TabIndex = 17
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Custom"
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(143, 57)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(534, 14)
        Me.LabelControl6.TabIndex = 15
        Me.LabelControl6.Text = "Import Medications and MedIdMappings from Shawna's custom spreadsheet (saved as a" &
    " CSV file)."
        '
        'LabelControl5
        '
        Me.LabelControl5.AutoEllipsis = True
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl5.Location = New System.Drawing.Point(143, 23)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(579, 28)
        Me.LabelControl5.TabIndex = 14
        Me.LabelControl5.Text = "Merge multiple (all) existing Facility CI specific medication lists into the Glob" &
    "al Medicaitons Table (DOMedicaitonsMaster)."
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.LabelControl10)
        Me.GroupBox2.Controls.Add(Me.LabelControl9)
        Me.GroupBox2.Controls.Add(Me.btnSaveAsCsv)
        Me.GroupBox2.Controls.Add(Me.btnLoadFile)
        Me.GroupBox2.Location = New System.Drawing.Point(23, 345)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(693, 87)
        Me.GroupBox2.TabIndex = 18
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Import/Export DOMedicationsMaster"
        '
        'LabelControl10
        '
        Me.LabelControl10.Location = New System.Drawing.Point(142, 57)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(426, 14)
        Me.LabelControl10.TabIndex = 18
        Me.LabelControl10.Text = "Load Medications from DOMedicationsMaster as a CSV (Comma Delimited File)."
        '
        'LabelControl9
        '
        Me.LabelControl9.Location = New System.Drawing.Point(142, 22)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(426, 14)
        Me.LabelControl9.TabIndex = 17
        Me.LabelControl9.Text = "Save Medications from DOMedicationsMaster as a CSV (Comma Delimited File)."
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.LabelControl8)
        Me.GroupBox3.Controls.Add(Me.LabelControl7)
        Me.GroupBox3.Controls.Add(Me.btnSaveJson)
        Me.GroupBox3.Controls.Add(Me.btnLoadJson)
        Me.GroupBox3.Location = New System.Drawing.Point(23, 438)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(693, 92)
        Me.GroupBox3.TabIndex = 19
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Import/Export DOMedicationMaster and Paragon MedIDs"
        '
        'LabelControl8
        '
        Me.LabelControl8.Location = New System.Drawing.Point(143, 57)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(305, 14)
        Me.LabelControl8.TabIndex = 17
        Me.LabelControl8.Text = "Load Medications and MedId mappings from a JSON file."
        '
        'LabelControl7
        '
        Me.LabelControl7.Location = New System.Drawing.Point(143, 31)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(292, 14)
        Me.LabelControl7.TabIndex = 16
        Me.LabelControl7.Text = "Save Medications and MedId mappings to a JSON file."
        '
        'btnDeleteAll
        '
        Me.btnDeleteAll.Location = New System.Drawing.Point(7, 22)
        Me.btnDeleteAll.Name = "btnDeleteAll"
        Me.btnDeleteAll.Size = New System.Drawing.Size(129, 25)
        Me.btnDeleteAll.TabIndex = 20
        Me.btnDeleteAll.Text = "Delete All"
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.LabelControl11)
        Me.GroupBox4.Controls.Add(Me.btnDeleteAll)
        Me.GroupBox4.Location = New System.Drawing.Point(23, 542)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(692, 60)
        Me.GroupBox4.TabIndex = 21
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Delete DOMedicationMaster and Paragon MedIDs"
        '
        'LabelControl11
        '
        Me.LabelControl11.Location = New System.Drawing.Point(142, 27)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(479, 14)
        Me.LabelControl11.TabIndex = 21
        Me.LabelControl11.Text = "Delete all records from the DOMedicationsMaster and DOParagonMedIdMappings tables" &
    "."
        '
        'btnViewMedIdMappings
        '
        Me.btnViewMedIdMappings.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnViewMedIdMappings.Location = New System.Drawing.Point(30, 684)
        Me.btnViewMedIdMappings.Name = "btnViewMedIdMappings"
        Me.btnViewMedIdMappings.Size = New System.Drawing.Size(159, 25)
        Me.btnViewMedIdMappings.TabIndex = 22
        Me.btnViewMedIdMappings.Text = "Open MedId Mappings"
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.DataSource = Me.XpCollection1
        Me.GridControl1.EmbeddedNavigator.Buttons.First.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.Last.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.Next.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.NextPage.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.Prev.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.PrevPage.Visible = False
        Me.GridControl1.EmbeddedNavigator.Buttons.Remove.Enabled = False
        Me.GridControl1.EmbeddedNavigator.Buttons.Remove.Visible = False
        Me.GridControl1.Location = New System.Drawing.Point(722, 7)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(697, 466)
        Me.GridControl1.TabIndex = 23
        Me.GridControl1.UseEmbeddedNavigator = True
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'XpCollection1
        '
        Me.XpCollection1.DeleteObjectOnRemove = True
        Me.XpCollection1.DisplayableProperties = "Enabled;MedName;CC;DedicatedLine;Chemo;Hormonal;UID"
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOMedicationMaster)
        Me.XpCollection1.Session = Me.UnitOfWork1
        Me.XpCollection1.Sorting.AddRange(New DevExpress.Xpo.SortProperty() {New DevExpress.Xpo.SortProperty("[MedName]", DevExpress.Xpo.DB.SortingDirection.Ascending)})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEnabled, Me.colMedName, Me.colCC, Me.colDedicatedLine, Me.colChemo, Me.colHormonal, Me.colUID})
        Me.GridView1.DetailHeight = 377
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.NewItemRowText = "Click here to add a new Medication"
        Me.GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colHormonal, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'colEnabled
        '
        Me.colEnabled.FieldName = "Enabled"
        Me.colEnabled.MinWidth = 23
        Me.colEnabled.Name = "colEnabled"
        Me.colEnabled.Visible = True
        Me.colEnabled.VisibleIndex = 0
        Me.colEnabled.Width = 50
        '
        'colMedName
        '
        Me.colMedName.FieldName = "MedName"
        Me.colMedName.MinWidth = 23
        Me.colMedName.Name = "colMedName"
        Me.colMedName.Visible = True
        Me.colMedName.VisibleIndex = 1
        Me.colMedName.Width = 419
        '
        'colCC
        '
        Me.colCC.FieldName = "CC"
        Me.colCC.MinWidth = 23
        Me.colCC.Name = "colCC"
        Me.colCC.Visible = True
        Me.colCC.VisibleIndex = 2
        Me.colCC.Width = 37
        '
        'colDedicatedLine
        '
        Me.colDedicatedLine.FieldName = "DedicatedLine"
        Me.colDedicatedLine.MinWidth = 23
        Me.colDedicatedLine.Name = "colDedicatedLine"
        Me.colDedicatedLine.Visible = True
        Me.colDedicatedLine.VisibleIndex = 3
        Me.colDedicatedLine.Width = 54
        '
        'colChemo
        '
        Me.colChemo.FieldName = "Chemo"
        Me.colChemo.MinWidth = 23
        Me.colChemo.Name = "colChemo"
        Me.colChemo.Visible = True
        Me.colChemo.VisibleIndex = 4
        Me.colChemo.Width = 47
        '
        'colHormonal
        '
        Me.colHormonal.FieldName = "Hormonal"
        Me.colHormonal.MinWidth = 23
        Me.colHormonal.Name = "colHormonal"
        Me.colHormonal.Visible = True
        Me.colHormonal.VisibleIndex = 5
        Me.colHormonal.Width = 70
        '
        'colUID
        '
        Me.colUID.FieldName = "UID"
        Me.colUID.MinWidth = 23
        Me.colUID.Name = "colUID"
        Me.colUID.OptionsColumn.ReadOnly = True
        Me.colUID.Visible = True
        Me.colUID.VisibleIndex = 6
        Me.colUID.Width = 149
        '
        'GridControl2
        '
        Me.GridControl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl2.DataMember = "MedIds"
        Me.GridControl2.DataSource = Me.XpCollection1
        Me.GridControl2.EmbeddedNavigator.Buttons.First.Visible = False
        Me.GridControl2.EmbeddedNavigator.Buttons.Last.Visible = False
        Me.GridControl2.EmbeddedNavigator.Buttons.Next.Visible = False
        Me.GridControl2.EmbeddedNavigator.Buttons.NextPage.Visible = False
        Me.GridControl2.EmbeddedNavigator.Buttons.Prev.Visible = False
        Me.GridControl2.EmbeddedNavigator.Buttons.PrevPage.Visible = False
        Me.GridControl2.Location = New System.Drawing.Point(722, 480)
        Me.GridControl2.MainView = Me.GridView2
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.ShowOnlyPredefinedDetails = True
        Me.GridControl2.Size = New System.Drawing.Size(697, 240)
        Me.GridControl2.TabIndex = 24
        Me.GridControl2.UseEmbeddedNavigator = True
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colMedId, Me.GridColumn1})
        Me.GridView2.DetailHeight = 377
        Me.GridView2.GridControl = Me.GridControl2
        Me.GridView2.Name = "GridView2"
        Me.GridView2.NewItemRowText = "Click here to add a new MedId Mapping"
        Me.GridView2.OptionsDetail.ShowDetailTabs = False
        Me.GridView2.OptionsDetail.ShowEmbeddedDetailIndent = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        Me.GridView2.OptionsView.ShowAutoFilterRow = True
        '
        'colMedId
        '
        Me.colMedId.FieldName = "MedId"
        Me.colMedId.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.colMedId.MinWidth = 23
        Me.colMedId.Name = "colMedId"
        Me.colMedId.Visible = True
        Me.colMedId.VisibleIndex = 0
        Me.colMedId.Width = 176
        '
        'GridColumn1
        '
        Me.GridColumn1.FieldName = "MedicationMaster!Key"
        Me.GridColumn1.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.GridColumn1.MinWidth = 23
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 1
        Me.GridColumn1.Width = 912
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(432, 684)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(129, 25)
        Me.btnCancel.TabIndex = 25
        Me.btnCancel.Text = "Cancel"
        '
        'LoadMedicationMastersForm
        '
        Me.AcceptButton = Me.btnDone
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 14.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1431, 732)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.GridControl2)
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.btnViewMedIdMappings)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.GroupBox3)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.lblGMTUsage)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.btnDone)
        Me.Name = "LoadMedicationMastersForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Load Global Medications List"
        Me.Panel1.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UnitOfWork1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents btnDone As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnLoadFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblGMTUsage As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnMerge As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnParagonLoad As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSaveJson As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnLoadJson As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSaveAsCsv As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents GroupBox3 As GroupBox
    Friend WithEvents btnDeleteAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupBox4 As GroupBox
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnViewMedIdMappings As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colEnabled As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMedName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCC As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDedicatedLine As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChemo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHormonal As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents UnitOfWork1 As DevExpress.Xpo.UnitOfWork
    Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colMedId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
End Class

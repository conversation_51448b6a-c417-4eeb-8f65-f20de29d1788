﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABc
        IAAAAk1TRnQBSQFMAwEBAAEIAQABCAEAAUABAAFAAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoBAABAQIA
        AUADAAEBAQABEAYAAYD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wAmAAH/AX8BmwF7ATcBewEWAXsBFgF7
        ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7
        ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7
        ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7ARYBewEWAXsBFgF7
        ARYBewEWAXsBFgF7ARcBewFZAXsB/wF//wCPAAH/AX8B6gF5AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AWUBfQH/AX//AI0AAdQBegHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AWUBfQH/AX//AIsAAUQBfQHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwB4AF8AeABfAF6AXv/AIkAAf8BfwHgAXwB4AF8AeABfAHgAXwBTgF+AXABfgFwAX4BcAF+
        AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+
        AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+
        AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+AXABfgFwAX4BcAF+
        AXABfgGGAX0B4AF8AeABfAHgAXwBOAF7/wCLAAFEAX0B4AF8AeABfAHgAXwBsgF+Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Ad4BfwFDAX0B4AF8AeABfAHgAXwBegF7/wCLAAHTAXoB4AF8AeABfAHgAXwBhgF9Ad0BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B3gF/
        AQsBfgHgAXwB4AF8AeABfAFDAX0B/wF//wCLAAHeAX8B4AF8AeABfAHgAXwB4AF8AU4BegH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwHeAXsB8AE1AfABNQH/AX8B/wF/
        Af8BfwH/AX8B/wF/AXsBawGNASkBMgE+Af8BfwH/AX8B/wF/Aa8BLQFsASEBbAEhAWwBIQFsASEBbAEh
        AWwBIQFsASEBbAEhAWwBIQFsASEBEAE2Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BFQF/
        AeABfAHgAXwB4AF8AeABfAGbAXv/AI8AAVgBewHgAXwB4AF8AeABfAHgAXwBNwF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BOgFjAWwBIQFsASEBtQFOAf8BfwH/AX8B/wF/
        Af8BfwF0AUYBbAEhAW0BJQH/AX8B/wF/Af8BfwG2AVIBbAEhAWwBIQFsASEBbAEhAWwBIQFsASEBbAEh
        AWwBIQFsASEBjQElAZwBcwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AcgBfQHgAXwB4AF8
        AeABfAFOAXr/AJMAAQwBegHgAXwB4AF8AeABfAHrAX0B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwGcAW8BbAEhAWwBIQGNASUB/wF/Af8BfwH/AX8B/wF/ATIBPgFsASEBrgEp
        Af8BfwH/AX8B/wF/Ab0BdwFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQEyAT4B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwHUAXoB4AF8AeABfAHgAXwB4AF8AbwBe/8AkwABmwF7
        AeABfAHgAXwB4AF8AQABfQH1AXoB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwGNASkBbAEhAWwBIQF7AWsB/wF/Af8BfwH/AX8B8AE1AWwBIQHwATUB/wF/Af8BfwH/AX8B/wF/
        AbYBUgFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQFsASEBbAEhARkBXwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BegF/AQEBfQHgAXwB4AF8AeABfAGyAXoB/wF//wCVAAEtAXoB4AF8AeABfAHgAXwB4AF8
        AZwBfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BUwFGAWwBIQFsASEB1gFW
        Af8BfwH/AX8B/wF/Aa4BKQFsASEBMgE+Af8BfwH/AX8B/wF/Af8BfwH/AX8BrwEtAWwBIQFsASEBbAEh
        AWwBIQFsASEBbAEhAY0BJQH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AS0BfgHgAXwB4AF8
        AeABfAGGAX3/AJkAAf8BfwGGAX0B4AF8AeABfAHgAXwBbwF+Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwFbAWsBbAEhAWwBIQEyAT4B/wF/Af8BfwH/AX8BbAElAWwBIQF0AUYB/wF/
        Af8BfwH/AX8B/wF/Af8BfwGcAXMB1wFWAY0BJQFsASEBbAEhAWwBIQFsASEBGAFfAf8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwFZAX8BIgF9AeABfAHgAXwB4AF8AVgBe/8AmwABFwF7AeABfAHgAXwB4AF8
        AQEBfQG9AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AY0BKQFsASEBjQEp
        Af8BfwH/AX8BWgFnAWwBIQFsASEBtgFSAf8BfwH/AX8B/wF/Af8BfwH/AX8BGAFfAWwBJQFsASEBbAEh
        AWwBIQFsASEBzwExAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwHeAX8BIwF9AeABfAHgAXwB4AF8
        AWUBfQH/AX//AJsAAf8BfwGGAX0B4AF8AeABfAHgAXwByAF9Ad4BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwERAToBbAEhAWwBIQE5AWMBGAFfAW0BJQFsASEBbAEhARkBXwH/AX8B/wF/
        Af8BfwH4AVoB8AE1AWwBIQFsASEBzwExAfABNQFsASEBbAEhAbYBUgH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BsgF+AeABfAHgAXwB4AF8AUMBfQHdAX//AJ8AAd0BfwHgAXwB4AF8AeABfAHgAXwB0wF+
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AZUBSgFsASEBbAEhAa4BLQFsASEBbAEh
        AWwBIQERAToB/wF/Af8BfwFaAWcBjQElAWwBIQFsASEBbQElAbYBUgH/AX8B3wF7AY0BKQFsASEBnAFz
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwFlAX0B4AF8AeABfAHgAXwBswF6/wCjAAGRAXoB4AF8
        AeABfAHgAXwBhgF9Ad4BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/ARgBXwFsASEBbAEh
        AWwBIQFsASEBbAEhAREBOgHeAXsBGQFfAREBOgFsASUBbAEhAWwBIQFsASEBewFrAf8BfwH/AX8B/wF/
        AVoBZwE5AWMB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AS0BegHgAXwB4AF8AeABfAEiAX0B3gF/
        /wCjAAG9AX8B4AF8AeABfAHgAXwB4AF8AZABegH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        AZwBbwFsASEBbAEhAWwBIQFsASEBbAEhAREBOgGuAS0BbAEhAWwBIQFsASEBbAEhAWwBIQFtASUB/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BNwF/AeABfAHgAXwB4AF8
        AeABfAFZAXv/AKUAAf8BfwEWAXsB4AF8AeABfAHgAXwB4AF8ATgBfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/AY0BKQFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQGuASkBlAFKAVMBRgFsASEBbAEh
        AbYBUgH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwHqAX0B4AF8
        AeABfAHgAXwBDAF6Af8Bf/8AqQAB6QF9AeABfAHgAXwB4AF8AQwBfgH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/AXMBRgFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQFaAWcB/wF/AREBOgFsASEBjQEl
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/ARYBewHgAXwB4AF8
        AeABfAHgAXwBmwF7/wCrAAF6AXsB4AF8AeABfAHgAXwBAQF9ARcBfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Ab0BdwE5AWMBMgE+AWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAfABNQG9AXMBjQElAWwBIQEyAT4B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwF7AX8BIgF9AeABfAHgAXwB4AF8
        AW8BegH/AX//AKsAAf8BfwHJAXkB4AF8AeABfAHgAXwBAQF9Ab0BfwH/AX8B/wF/Af8BfwE6AWMBbAEh
        AWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAa4BLQFsASEBbAEhAdcBVgH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AU4BfgHgAXwB4AF8AeABfAFkAX0B/wF/
        /wCvAAHeAX8BZAF9AeABfAHgAXwB4AF8AZABfgH/AX8B/wF/Af8BfwHXAVYBbAEhAWwBIQFsASEB+AFa
        AdcBVgFsASUBbAEhAWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAZwBbwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8BegF/AUMBfQHgAXwB4AF8AeABfAE3AXv/ALMAAfUBegHgAXwB4AF8
        AeABfAEjAX0B3gF/Af8BfwH/AX8BvQF3AW0BJQFsASEBbAEhAVMBQgH/AX8BUwFGAWwBIQFsASEBbAEh
        AWwBIQFsASEBbAEhAfABNQH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        AYYBfQHgAXwB4AF8AeABfAEiAX0B/wF//wCzAAHeAX8BRAF9AeABfAHgAXwB4AF8AesBfQHeAX8B/wF/
        Af8BfwF8AW8BrgEtAWwBIQFsASUBMgE+AfABNQFsASEBbAEhAWwBIQFsASEBbAEhAWwBIQEZAWMB/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AdMBfgHgAXwB4AF8AeABfAEiAX0BvAF7
        /wC3AAG8AXsB4AF8AeABfAHgAXwB4AF8AfUBfgH/AX8B/wF/Af8BfwH4AVoBbAEhAWwBIQFsASEBbAEh
        AWwBIQFsASEBbAEhAWwBIQFsASEBEQE+Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BhwF9AeABfAHgAXwB4AF8AZEBev8AuwABbwF6AeABfAHgAXwB4AF8AagBfQH/AX8B/wF/
        Af8BfwH/AX8BlQFKAWwBIQFsASEBbAEhAWwBIQFsASEBbAEhAc8BMQH4AVoB/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwFvAXoB4AF8AeABfAHgAXwBAAF9Ab0Bf/8AuwABvAF7
        AeABfAHgAXwB4AF8AeABfAHTAXoB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BGAFbAY0BJQHwATUBvQFzAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AVgBfwHgAXwB4AF8
        AeABfAHgAXwBFgF7/wC9AAH/AX8B0wF6AeABfAHgAXwB4AF8AQEBfQF5AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AfcBWgFsASUBbAEhAWwBIQGNASUBfAFvAf8BfwH/AX8B/wF/
        Af8BfwH/AX8BCwF+AeABfAHgAXwB4AF8AeoBeQH/AX//AMEAAacBfQHgAXwB4AF8AeABfAEtAX4B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AY0BJQFsASEBbAEhAWwBIQFsASEB1gFS
        Af8BfwH/AX8B/wF/Af8BfwE4AXsB4AF8AeABfAHgAXwB4AF8AXoBe/8AwwABWQF7AeABfAHgAXwB4AF8
        ASIBfQFYAX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B8AE1AWwBIQFsASEBbAEh
        AWwBIQH4AVoB/wF/Af8BfwH/AX8BmwF/AUQBfQHgAXwB4AF8AeABfAEMAXoB/wF//wDDAAH/AX8BhQF9
        AeABfAHgAXwB4AF8ASMBfQHeAX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG9AXcBjQEl
        AWwBIQFsASEBjQEpAd4BewH/AX8B/wF/Af8BfwFwAX4B4AF8AeABfAHgAXwBIgF9Af8Bf/8AxwAB3gF/
        AUMBfQHgAXwB4AF8AeABfAGyAX4B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BfAFv
        AdYBUgH4AVoB3gF7Af8BfwH/AX8B/wF/AZwBfwFEAX0B4AF8AeABfAHgAXwB9QF6/wDLAAHTAXoB4AF8
        AeABfAHgAXwBZAF9Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwHJAXkB4AF8AeABfAHgAXwBAAF9Af8Bf/8AywAB3gF/ASIBfQHgAXwB4AF8
        AeABfAEMAXoB/gF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH1AX4B4AF8AeABfAHgAXwBAQF9AZsBe/8AzwABegF7AeABfAHgAXwB4AF8AeABfAEWAX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AcgBfQHgAXwB4AF8
        AeABfAFvAXr/ANEAAf8BfwEtAXoB4AF8AeABfAHgAXwByQF9Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AbEBegHgAXwB4AF8AeABfAHgAXwBvAF7/wDTAAGcAXsB4AF8
        AeABfAHgAXwB4AF8AfUBegH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BeQF/
        AeABfAHgAXwB4AF8AeABfAHTAXr/ANUAAf8BfwFwAXoB4AF8AeABfAHgAXwBIgF9AZoBfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwEtAX4B4AF8AeABfAHgAXwByAF9Af8Bf/8A1wAB/wF/
        AWUBfQHgAXwB4AF8AeABfAFOAX4B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwF6AXsB4AF8
        AeABfAHgAXwB4AF8AVkBe/8A2wABNwF7AeABfAHgAXwB4AF8ASIBfQF6AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BvAF/AYUBfQHgAXwB4AF8AeABfAHqAX0B/wF//wDbAAH/AX8BIwF9AeABfAHgAXwB4AF8
        AWUBfQHeAX8B/wF/Af8BfwH/AX8B/wF/Af8BfwGRAX4B4AF8AeABfAHgAXwBAQF9Af8Bf/8A3wABvQF7
        ASIBfQHgAXwB4AF8AeABfAHTAX4B/wF/Af8BfwH/AX8B/wF/Ab0BfwFkAX0B4AF8AeABfAHgAXwB1AF6
        /wDjAAGyAXoB4AF8AeABfAHgAXwBhgF9Af8BfwH/AX8B/wF/Af8BfwEMAXoB4AF8AeABfAHgAXwB4AF8
        Ad4Bf/8A4wAB3QF/AQEBfQHgAXwB4AF8AeABfAFOAXoB/wF/Af8BfwEWAX8B4AF8AeABfAHgAXwB4AF8
        AXoBe/8A5wABNwF7AeABfAHgAXwB4AF8AeABfAE4AX8B/wF/AeoBfQHgAXwB4AF8AeABfAEtAXr/AOkA
        Af8BfwELAXoB4AF8AeABfAHgAXwB6wF9AdMBegEAAX0B4AF8AeABfAHgAXwBnAF7/wDrAAF6AXsB4AF8
        AeABfAHgAXwB4AF8AeABfAHgAXwB4AF8AeABfAFvAXr/AO0AAf8BfwEtAXoB4AF8AeABfAHgAXwB4AF8
        AeABfAHgAXwBpwF9Af8Bf/8A7wAB/wF/AYYBfQHgAXwB4AF8AeABfAHgAXwB4AF8AVkBe/8A8wABvQF/
        AW8BegFEAX0BIgF9AesCeQF7Af8Bf/8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AMcAAUIBTQE+
        BwABPgMAASgEAAEBAgABQAMAAQEBAAEBBgABCBYAA/8BAAj/GAAI/xgACP8YAAj/GAAI/xgACP8YAAHw
        BgABDxgAAeAGAAEHGAAB4AYAAQMYAAHgBgABAxgAAcAGAAEDGAAB4AYAAQMYAAHgBgABAxgAAeAGAAEH
        GAAB8AYAAQ8YAAH4BgABDxgAAfgGAAEPGAAB/AYAAT8YAAH8BgABPxgAAf4GAAE/GAAB/gYAAX8YAAH/
        BgAB/xgAAf8BgAUAAf8YAAH/AYAEAAEBAf8YAAH/AYAEAAEBAf8YAAH/AeAEAAEDAf8YAAH/AeAEAAED
        Af8YAAH/AeAEAAEHAf8YAAH/AfAEAAEPAf8YAAH/AfgEAAEPAf8YAAH/AfgEAAEfAf8YAAH/AfwEAAE/
        Af8YAAH/Af4EAAE/Af8YAAH/Af4EAAF/Af8YAAH/Af4EAAF/Af8YAAL/AYADAAL/GAAC/wGAAwAC/xgA
        Av8BgAIAAQEC/xgAAv8BwAIAAQMC/xgAAv8B4AIAAQMC/xgAAv8B4AIAAQcC/xgAAv8B8AIAAQ8C/xgA
        Av8B8AIAAQ8C/xgAAv8B+AIAAR8C/xgAAv8B+AIAAR8C/xgAAv8B/AIAAT8C/xgAAv8B/gIAAT8C/xgA
        Av8B/gIAAX8C/xgAA/8CAAP/GAAD/wGAAQAD/xgAA/8BgAEBA/8YAAP/AcABAwP/GAAD/wHAAQMD/xgA
        A/8B4AEHA/8YAAP/AeABBwP/GAAD/wHwAQ8D/xgAA/8B+AEPA/8YAAj/GAAI/xgACP8YAAj/GAAI/xgA
        CP8YAAj/GAAL
</value>
  </data>
  <metadata name="Timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>127, 17</value>
  </metadata>
</root>
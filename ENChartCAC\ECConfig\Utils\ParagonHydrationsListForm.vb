﻿Imports System.IO
Imports DevExpress.Xpo
Imports System.Linq
Imports System.Text
Imports Microsoft.CodeAnalysis.Host

Public Class ParagonHydrationsListForm

    Private xpcollection2 As New XPCollection(Of DOParagonHydration)

    Private Sub ParagonHydrationsListForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        GridControl1.DataSource = xpcollection2
        xpcollection2.DeleteObjectOnRemove = True
    End Sub

    Private Sub LabelControl1_Click(sender As Object, e As EventArgs) Handles LabelControl1.Click

    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        UnitOfWork1.CommitChanges()
        Close()
    End Sub

    Private Sub btnDeleteAll_Click(sender As Object, e As EventArgs) Handles btnDeleteAll.Click
        Cursor.Current = Cursors.WaitCursor
        Try
            If DialogResult.OK <> MessageBox.Show("You are about to permanently delete all records from the DOParagonHydration table. Are you sure you want to continue?", "Are you sure?", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) Then
                Exit Sub
            End If

            '_helper.DeleteAll()
            XpoDefault.Session.Delete(DOParagonHydration.GetAllAsDOs)
        Finally
            Cursor.Current = Cursors.Default
            'UpdateDialogInfo()
            xpcollection2.Reload()
        End Try
    End Sub

    Public Sub SaveHydrationListToDiskAsCsv()
        Cursor.Current = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "Hydration List Config File|*.csv"
            fsa.FileName = "HydrationList.csv"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)
            Dim sb As New StringBuilder
            For Each med In DOParagonHydration.GetAllAsDTOs()
                sb.Length = 0
                sb.Append(med.Description + ",")
                sb.Append(med.MedId.ToString)

                sw.WriteLine(sb.ToString)
            Next
            sw.Close()

            MessageBox.Show("Hydration List successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        SaveHydrationListToDiskAsCsv()
    End Sub

    Private Function GetFileToImport() As FileInfo
        Dim ofd As New OpenFileDialog
        ofd.Filter = "Hydration List Config File|*.csv"
        ofd.FileName = "HydrationList.csv"

        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then
            Return Nothing 'Exit Function
        End If

        Dim ffile As New FileInfo(ofd.FileName)
        Return ffile
    End Function

    Private Sub btnImport_Click(sender As Object, e As EventArgs) Handles btnImport.Click
        Dim fInfo = GetFileToImport()
        If fInfo Is Nothing Then Return
        Dim hydList As New List(Of DOParagonHydration)
        Dim lines = File.ReadAllLines(fInfo.FullName)
        Dim firstLine = True

        For Each line In lines
            ' why is this here?  - crc
            '    If firstLine Then
            '        firstLine = False
            '        Continue For
            '    End If

            Dim fields = line.Split(",")
            Dim newHydrationMapping As New DOParagonHydration
            newHydrationMapping.Description = fields(0)
            newHydrationMapping.MedId = fields(1)
            hydList.Add(newHydrationMapping)
        Next

        XpoDefault.Session.Save(hydList)
        xpcollection2.Reload()
    End Sub
End Class
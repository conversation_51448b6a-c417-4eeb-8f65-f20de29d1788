﻿using System;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security;
using System.Text;
using System.Threading.Tasks;
using Microsoft.VisualBasic;
using Refit;

public class WorkstationLogHelper
{
    public class WorkStationDTO
    {
        public string FileName;
        public string[] Lines;
    }

    public interface IAdminController
    {
        [Get("/shutdown")]
        System.Threading.Tasks.Task<string> Shutdown();

        [Post("/UploadWorkstationLog")]
        Task<bool> UploadWorkstationLog(WorkStationDTO dTO);
    }

    public  void UploadLogToServer()
    {
        try
        {
            DirectoryInfo di = new DirectoryInfo(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"AIC\Logs"));
            Dictionary<string, string[]> fileDic = new Dictionary<string, string[]>();
            foreach (var fi in di.GetFiles())
            {
                var lines = File.ReadAllLines(fi.FullName);
                if (lines != null)
                    fileDic[fi.FullName] = lines;

                        if (UploadToEss(fi.FullName, lines))
                    fi.Delete();
            }

            //shutdown();
        }
        catch (Exception ex)
        {
        }
    }

    public void shutdown()
    {
        var suffix = "api/Admin";

        var url = $"https://localhost:44399/{suffix}";
        var ac = Refit.RestService.For<IAdminController>(url);
        //WorkStationDTO wsd = new WorkStationDTO() { FileName = "ahhhh", Lines = new string };

        // Dim result = ac.UploadWorkStationLog(wsd)
        var blah = ac.Shutdown().Result;

        //return true;

    }

    private  bool UploadToEss(string logFileName, string[] lines)
    {
        var suffix = "api/Admin";
     
        var url = $"https://localhost:44399/{suffix}";
        var ac = Refit.RestService.For<IAdminController>(url);
        WorkStationDTO wsd = new WorkStationDTO() { FileName = logFileName, Lines = lines };

        var result = ac.UploadWorkstationLog(wsd).Result;
        //var blah = ac.Shutdown();

        return true;
      
    }
}



namespace ConsoleApp1
{
    class Program
    {
        static void Main(string[] args)
        {

            var wh = new WorkstationLogHelper();
            //wh.shutdown();

                wh.UploadLogToServer();

            Console.WriteLine("Hello World!");
        }
    }


}

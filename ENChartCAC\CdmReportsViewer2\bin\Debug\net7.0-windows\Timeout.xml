﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Timeout
</name>
</assembly>
<members>
<member name="T:Timeout.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:Timeout.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:Timeout.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="T:Timeout.BasicTimeoutWatcher">
 <summary>
 BasicTimeoutWatcher -
 Provides a basic wrapper around the core TimeoutWatcher logic. Useful for 
 implementing a default concrete implementation that doesn't require
 custom logic.
 </summary>
 
 <remarks>
 This is just a convenience class and does not need to be used to implement
 the timeout logic.
 </remarks>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.#ctor">
 <summary>
 Private constructor so class can't be instantiated externally
 </summary>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.Init(System.Windows.Forms.Form,Timeout.BasicTimeoutWatcher.Action,System.String,System.Int16)">
 <summary>
 Used to Initialize and start the idle TimoutWatcher logic.
 </summary>
 <param name="parentForm">Assigned as the parent form of the WarningDialog</param>
 <param name="shutdownDelegate">Delegate the points to application specific shutdown logic</param>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.Suspend">
 <summary>
 Temporarily prevent timeout. Should be called prior to executing a
 potentially long running process that should not be interrupted.
 </summary>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.Resume">
 <summary>
 Re-enable the timeout logic to initiate an auto shutdown.
 </summary>
</member>
<member name="T:Timeout.BasicTimeoutWatcher.TimeoutHelper">
 <summary>
 A concrete implementation of the ITimeoutable interface.
 </summary>
 <remarks>
 </remarks>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.TimeoutHelper.CustomBringToFront">
 <summary>
 Used to bring the ParentForm of the Warning Dialog to the front prior
 to trying to show the dialog.
 </summary>
 
 <remarks>
 A bit convoluted, but solved a long running problem of the child window
 (the warning dialog) occasionally showing up behind the main application
 form but not accessible because it's modal and hidden.
 
 It should be find to derive a new class from this one and override
 this if this behavior seem not applicable to where/when this is
 being called.
 </remarks>
</member>
<member name="M:Timeout.BasicTimeoutWatcher.TimeoutHelper.TimeoutAction">
 <summary>
 Calls the passed in function to perform the application specific
 shutdown logic.
 </summary>
</member>
</members>
</doc>

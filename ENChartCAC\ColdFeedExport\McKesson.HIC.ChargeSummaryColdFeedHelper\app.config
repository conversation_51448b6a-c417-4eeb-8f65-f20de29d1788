﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="McKesson.HIC.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <!-- system.diagnostics section is not supported on .NET 6 (see https://github.com/dotnet/runtime/issues/23937)-->
    <!--<system.diagnostics>
  <sources>
    <!- - This section defines the logging configuration for My.Application.Log - ->
    <source name="DefaultSource" switchName="DefaultSwitch">
      <listeners>
        <add name="FileLog" />
        <!- - Uncomment the below section to write to the Application Event Log - ->
        <!- -<add name="EventLog"/>- ->
      </listeners>
    </source>
  </sources>
  <switches>
    <add name="DefaultSwitch" value="Information" />
  </switches>
  <sharedListeners>
    <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
    <!- - Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log - ->
    <!- -<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> - ->
  </sharedListeners>
</system.diagnostics>-->
    <applicationSettings>
        <McKesson.HIC.My.MySettings>
            <setting name="BatchLabel" serializeAs="String">
                <value>"Missing Facility Batch Label"</value>
            </setting>
        </McKesson.HIC.My.MySettings>
    </applicationSettings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
    </startup>
</configuration>
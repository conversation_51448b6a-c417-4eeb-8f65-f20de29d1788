﻿Imports DevExpress.XtraEditors.Controls
Imports EnchartDOLib
Imports ENChartReports

Public Class ReportRolesEditorV3

    <DebuggerDisplay("ID={ID}, Name: {Name}, Parent = {ParentID}")>
    Class ReportNode
        'Implements DevExpress.XtraTreeList.

        Private Shared _lastId As Integer = 0
        Property ID As Integer
        Property Name As String
        Public ReadOnly Property ParentID As Integer
            Get
                If Parent IsNot Nothing Then
                    Return Parent.ID
                End If

                Return 0
            End Get
        End Property

        Private Property Parent As ReportNode
        Property Children As List(Of ReportNode)

        Property Allowed As Boolean = False

        Private _nodeType As NodeType
        Private _readonly As Integer

        Private Function GetNextID() As Integer
            _lastId = _lastId + 1
            Return _lastId
        End Function

        Public Sub New(name As String, parent As ReportNode, nodeType As NodeType)
            ID = GetNextID()
            Me.Name = name
            Me.Parent = parent
            _nodeType = nodeType
            Children = New List(Of ReportNode)
        End Sub

        Public Enum NodeType
            Facility
            Category
            Report
        End Enum

    End Class


#Disable Warning BC40041 ' Type is not CLS-compliant
    Public Sub New(ByVal facilities As List(Of DOFacility))
#Enable Warning BC40041 ' Type is not CLS-compliant

        ' This call is required by the designer.
        InitializeComponent()
        TreeList1.ForceInitialize()
        ' Add any initialization after the InitializeComponent() call.
        LoadFacilitiesList(facilities)

        TreeList1.FindNodeByFieldValue("ID", 1)
        TreeList1.CollapseAll()
    End Sub

    Private Sub cboFacility_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboFacility.SelectedIndexChanged

        LoadRoleList()
        BuildReportList(cboFacility.SelectedItem)
    End Sub
    Private Sub BuildReportList(ByVal facility As DOFacility) 'As List(Of Object)
        Dim returnList As New List(Of ReportNode)
        '   Dim TopNode As New ReportNode(facility.LongName, Nothing, ReportNode.NodeType.Facility)
        For Each category As DOFacilityReportCategory In DOFacilityReportCategory.GetRootCategoriesByFacility(facility)
            Dim catNode As New ReportNode(category.CategoryName, Nothing, ReportNode.NodeType.Category)

            'TopNode.Children.Add(catNode)
            'returnList.Add(category)
            For Each report As DOFacilityReport In category.FacilityReports
                Dim repNode As New ReportNode(report.ReportDescription, catNode, ReportNode.NodeType.Report)
                'catNode.Children.Add(repNode)
                returnList.Add(repNode)
            Next
            returnList.Add(catNode)
        Next

        TreeList1.KeyFieldName = "ID"
        TreeList1.ParentFieldName = "ParentID"
        TreeList1.CheckBoxFieldName = "Allowed"
        TreeList1.OptionsBehavior.PopulateServiceColumns = True
        TreeList1.OptionsView.ShowCheckBoxes = True
        'TreeList1.OptionsView.roo
        TreeList1.DataSource = returnList
        TreeList1.OptionsSelection.Reset()

        'Return returnList
    End Sub

    Private Sub LoadFacilitiesList(ByVal facilities As List(Of DOFacility))
        cboFacility.Properties.Items.Clear()
        For Each facility As DOFacility In facilities
            cboFacility.Properties.Items.Add(facility)
        Next
        cboFacility.SelectedIndex = 0
    End Sub

    Private Sub LoadRoleList()
        Dim facility As DOFacility = cboFacility.SelectedItem
        cboRoles.Properties.Items.Clear()
        For Each role As DOUserRolesList In facility.UserRolesList
            cboRoles.Properties.Items.Add(role)
        Next
        cboRoles.SelectedIndex = 0
    End Sub

    Private Sub ReportRolesEditorV3_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
    Private Sub cboRoles_DrawItem(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.ListBoxDrawItemEventArgs) Handles cboRoles.DrawItem
        Dim item As DOUserRolesList = e.Item
        e.Appearance.DrawBackground(e.Cache, e.Bounds)
        e.Graphics.DrawString(item.Role, e.Appearance.Font, e.Appearance.GetForeBrush(e.Cache), e.Bounds.Location)
        e.Handled = True
    End Sub

    Private Sub cboRoles_CustomDisplayText(ByVal sender As Object, ByVal e As CustomDisplayTextEventArgs) Handles cboRoles.CustomDisplayText
        Dim item As DOUserRolesList = e.Value
        If Not item Is Nothing Then
            e.DisplayText = item.Role
        End If
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        '  TreeList1.CollapseAll()
        Dim node = TreeList1.FindNodeByFieldValue("ID", 2)
        TreeList1.FocusedNode = node
    End Sub

    Private Sub ReportRolesEditorV3_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        TreeList1.CollapseAll()
    End Sub


    Private Sub TreeList1_StateChanged(sender As Object, e As EventArgs) Handles TreeList1.StateChanged

    End Sub


    Private Sub TreeList1_NodeChanged(sender As Object, e As DevExpress.XtraTreeList.NodeChangedEventArgs) Handles TreeList1.NodeChanged
        If e.Node.Checked Then
            e.Node.CheckAll()
        Else
            e.Node.UncheckAll()
        End If

        'If e.Node.ParentNode IsNot Nothing Then
        '    e.Node.ParentNode.Checked = False
        'End If
    End Sub
End Class
Imports ENChartCAC

Namespace My

    ' The following events are available for MyApplication:
    ' 
    ' Startup: Raised when the application starts, before the startup form is created.
    ' Shutdown: Raised after all application forms are closed.  This event is not raised if the application terminates abnormally.
    ' UnhandledException: Raised if the application encounters an unhandled exception.
    ' StartupNextInstance: Raised when launching a single-instance application and the application is already active. 
    ' NetworkAvailabilityChanged: Raised when the network connection is connected or disconnected.
    Partial Friend Class MyApplication

        Private Sub MyApplication_Startup(ByVal sender As Object, ByVal e As Microsoft.VisualBasic.ApplicationServices.StartupEventArgs) Handles Me.Startup
            'Dim ass As Reflection.Assembly = Reflection.Assembly.ReflectionOnlyLoadFrom("enchartdolib.dll")
            'Dim assname As Reflection.AssemblyName = ass.GetName
            'Dim assver As New Version(1, 1, 1, 7)
            'If assname.Version < assver Then
            '    MessageBox.Show("This build requires version 1.1.1.6 of EnchartDoLib.dll or later", "hmmm", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '    e.Cancel = True
            '    Return
            'End If

        End Sub

        Private Sub MyApplication_Shutdown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shutdown

            If ECGlobals.CurrentUser IsNot Nothing Then
                ECGlobals.CurrentUser.LogOut()
                AuditLogger.Authentication.UserLogOut()
            End If

            'ECLog.WriteEntry("Shutting down-------------------------------------------------------------", TraceEventType.Information)
            'Application.Log.DefaultFileLogWriter.Flush()
            'Application.Log.DefaultFileLogWriter.Close()
        End Sub
    End Class

End Namespace


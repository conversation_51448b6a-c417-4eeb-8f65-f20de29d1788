﻿using EnchartDOLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace AICConfigCheck
{
    class Program
    {
        static void Main(string[] args)
        {
            //Test Cert config

            //Get ESS URL
            string baseUrl = MicSettingsBuilder.Settings.ESSUrl;
            Console.WriteLine($"ESSUrl: {baseUrl}");
            //Connect To ESS
            Connect(baseUrl);
            Console.ReadKey();
        }

        private static void Connect(string baseUrl)
        {

           var _webRequestHandler = new WebRequestHandler();
            _webRequestHandler.ServerCertificateValidationCallback = CertificateValidationErrorHandler;

            var _httpClient = new HttpClient(_webRequestHandler);
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            var _baseUrl = baseUrl; //"https://localhost:44399";

            var suffix = "api/Configuration/GetStartUpGlobalSettings";

            var url = $"{_baseUrl}/{suffix}";

            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, url);
            HttpResponseMessage aResponse = _httpClient.GetAsync(url).Result;

            if (aResponse.StatusCode == System.Net.HttpStatusCode.OK)
            {
            }



        }

        static protected bool CertificateValidationErrorHandler(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {

            Console.WriteLine($"Certficate:{certificate.ToString()}");
                       
            switch(sslPolicyErrors)
            {
                case SslPolicyErrors.None:
                    Console.WriteLine($"Certficate Looks Good!");
                    break;

                case SslPolicyErrors.RemoteCertificateNotAvailable:
                    Console.WriteLine($"Certficate Error: RemoteCertificateNotAvailable!");
                    break;
                case SslPolicyErrors.RemoteCertificateChainErrors:
                    Console.WriteLine($"Certficate Error: RemoteCertificateChainErrors!");
                    break;

                case SslPolicyErrors.RemoteCertificateNameMismatch:
                    Console.WriteLine($"Certficate Error: RemoteCertificateNameMismatch!");
                    break;

                default:
                   
                    Console.WriteLine($"Certficate Error: ErrorCode= {sslPolicyErrors}");
                    break;

            }

            //if (sslPolicyErrors == SslPolicyErrors.RemoteCertificateNotAvailable)
            //    throw new Exception(SslPolicyErrors.RemoteCertificateNotAvailable.ToString());

            //// 'A certificate chain processed, but terminated in a root certificate which is not trusted by the trust provider.
            //if (sslPolicyErrors == SslPolicyErrors.RemoteCertificateChainErrors)
            //    //if (_baseUrl != null && _baseUrl.ToUpper().Contains("A002MICDEV01.CLOUD.MDRXDEV.COM") || _baseUrl.ToUpper().Contains("A002MICDevQA01.CLOUD.MDRXDEV.COM"))
            //    //    return true;
            //    //else
            //    throw new CommunicationException(SslPolicyErrors.RemoteCertificateChainErrors.ToString());

            return true; //returning true here may cause fortify scan to complain ... (JJC 12.14.19) need to revisit implications of not doing this.
        }


        public void InitDataLayerForUseWithESS(MICCustomDataProviders.IESSTokenHelper tokenHelper = null/* TODO Change to default(_) if this is not a reference type */)
        {
            var baseUrl = string.Empty;

            //MICCustomDataProviders.ESSDataStoreProvider dataStore = new MICCustomDataProviders.ESSDataStoreProvider(baseUrl, tokenHelper);
            //XpoDefault.DataLayer = new SimpleDataLayer(dataStore);
        }

    }
}

﻿Imports EnchartDOLib
Imports System.IO
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB
Imports ExportSchema
Imports System.Text
Imports ExportSchema.DBSchema

Module ExportSqlSchema
    Dim SQLDal As IDataLayer

    Sub Main()
        Utils.ConnectToECDataBase()
        SQLDal = XpoDefault.DataLayer

        If Not XpoDefault.DataLayer.Connection.ConnectionString.ToUpper.Contains("INITIAL") Then
            'in case the latest version of dolib is being used ...
            Console.WriteLine("Error: Please change the connection string in the ENChartCAC.exe.config file to point to the SQL server!")
            Console.WriteLine("Press any key to continue ...")
            Console.ReadKey()
            Return
        End If

        Dim dbInfoSQL As New DBSchema
        Dim tableCount As Integer = 0
        For Each tableName In GetSQLTableNamesAsList()
            Dim ti As New DBSchema.TableInfo
            ti.TableName = tableName
            Dim fieldcount As Integer = 0
            For Each field In GetFieldListFromSQL(tableName)
                ti.fields.Add(field)
                fieldcount += 1
            Next
            ti.FieldCount = fieldcount
            dbInfoSQL.TableList.Add(ti)
            tableCount += 1
        Next
        dbInfoSQL.TableCount = tableCount

        SaveSQLToDisk(dbInfoSQL)

        Console.WriteLine("{0} File successfully created", "DbSchemaForSqlServer.xml")
        Console.WriteLine("Press any key to continue ...")
        Console.ReadKey()
    End Sub

    Sub SaveSQLToDisk(dbInfo As DBSchema)
        Dim fs As New FileStream("DbSchemaForSqlServer.xml", FileMode.Create)
        Dim x As New Xml.Serialization.XmlSerializer(GetType(DBSchema))

        x.Serialize(fs, dbInfo)
        fs.Flush()
        fs.Close()
    End Sub

    Function GetSQLTableNamesAsList() As List(Of String)
        Dim tableNameList As New List(Of String)

        Using cmd = SQLDal.CreateCommand
            cmd.CommandText = "SELECT * FROM information_schema.tables order by TABLE_NAME"
            Using reader = cmd.ExecuteReader
                While reader.Read
                    tableNameList.Add(reader("TABLE_NAME"))
                End While
            End Using
        End Using
        Return tableNameList
    End Function

    Public Function GetFieldListFromSQL(tableName As String) As List(Of DBSchema.FieldInfo)
        Dim fieldList As New List(Of DBSchema.FieldInfo)

        Using cmd As IDbCommand = SQLDal.Connection.CreateCommand()
            cmd.CommandText = "exec sp_columns " & tableName
            Using reader = cmd.ExecuteReader
                While reader.Read
                    Dim fi As New DBSchema.FieldInfo
                    fi.FieldName = reader("column_name")
                    fi.FieldType = reader("data_type")

                    'If sqlTypeMap.Keys.Contains(reader("type_name")) Then
                    '    fi.FieldTypeName = sqlTypeMap(reader("type_name"))
                    'Else
                    '    fi.FieldTypeName = reader("type_name")
                    'End If

                    fi.FieldTypeName = reader("type_name")
                    fi.FieldLength = reader("length")
                    fi.Nullable = reader("nullable")
                    fieldList.Add(fi)
                End While
            End Using
        End Using

        Return fieldList
    End Function

End Module

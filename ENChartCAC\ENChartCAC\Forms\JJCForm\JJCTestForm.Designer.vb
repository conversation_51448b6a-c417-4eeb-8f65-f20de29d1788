﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class JJCTestForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.Infusion_AccessInfusaport_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_AccessInfusaport_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusions_Nurse_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusions_Nurse_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_QualityIndicators_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_QI_TotalInfusNotCoded_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_QI_TotalInfusNotCoded_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_QIOption02_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusion_QIOption01_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusion_SitesND_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusion_IncorrectNursingDocumentation_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusion_IncompleteTimes_chk = New DevExpress.XtraEditors.CheckEdit()
        Me.Infusions_Thrombolytics_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_Thrombolytics_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_BloodAdministration_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_BloodAdminstration_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_IVSites_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_IVSite03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_IVSite02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_IVSite01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_IVSite03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_IVSite02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_IVSite01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedications_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusions_TitratedMedicationsMedication05_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView26 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn131 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn132 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn133 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn134 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn135 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusions_TitratedMedicationsMedication04_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView25 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn126 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn127 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn128 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn129 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn130 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusions_TitratedMedicationsMedication03_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView24 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn121 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn122 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn123 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn124 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn125 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusions_TitratedMedicationsMedication02_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView23 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn116 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn117 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn118 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn119 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn120 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusions_TitratedMedicationsMedication01_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView22 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn111 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn112 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn113 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn114 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn115 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusions_TitratedMedications05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedications04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedications03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedications02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedications01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMedication_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsResponse_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsSite_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsMins_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsEndTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsEndDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsEndTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsEndDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsEndTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsEndDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsEndTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsEndTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsEndTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsEndDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsEndDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsEndDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsStartTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsStartDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusions_TitratedMedicationsStartTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsStartDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsStartTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsStartDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsStartTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsStartTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsStartTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusions_TitratedMedicationsStartDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsStartDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsStartDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusions_TitratedMedicationsResponse05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsResponse04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsResponse03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsResponse02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsResponse01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsSite05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsSite04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsSite03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsSite02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_TitratedMedicationsSite01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_Hydrations_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_Hydrations05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_Hydrations04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_Hydrations03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_Hydrations02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_Hydrations01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsRate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsResponse_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsSite_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsMins_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsEndTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsEndDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsEndTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsEndDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsEndTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsEndDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsEndTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsEndTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsEndTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsEndDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsEndDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsEndDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsStartTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsStartDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_HydrationsStartTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsStartDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsStartTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsStartDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsStartTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsStartTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsStartTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_HydrationsStartDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsStartDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsStartDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_HydrationsResponse05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsResponse04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsResponse03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsResponse02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsResponse01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsRate05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsRate04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsRate03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsRate02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsRate01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsSite05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsSite04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsSite03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsSite02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_HydrationsSite01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusions_InfusedMedications_grp = New DevExpress.XtraEditors.GroupControl()
        Me.Infusion_InfusedMedicationsMedication07_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView19 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn96 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn97 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn98 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn99 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn100 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication06_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView20 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn101 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn102 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn103 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn104 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn105 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication05_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView21 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn106 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn107 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn108 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn109 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn110 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication04_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView17 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn86 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn87 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn88 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn89 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn90 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication03_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView18 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn91 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn92 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn93 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn94 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn95 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication02_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView16 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn81 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn82 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn83 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn84 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn85 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication01_cbo = New DevExpress.XtraEditors.GridLookUpEdit()
        Me.GridView15 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn76 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn77 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn78 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn79 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn80 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Infusion_InfusedMedicationsMedication_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications07_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications06_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedications01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins07_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins06_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins05_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins04_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins03_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins02_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins01_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsResponse_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsSite_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsMins_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsEndTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsEndDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsEndTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndTime07_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndTime06_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsEndDate07_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndDate06_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsEndDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartTime_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsStartDate_lbl = New DevExpress.XtraEditors.LabelControl()
        Me.Infusion_InfusedMedicationsStartTime04_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartDate04_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartTime03_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartDate03_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartTime07_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartTime06_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartTime05_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartTime02_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartTime01_txt = New DevExpress.XtraEditors.TextEdit()
        Me.Infusion_InfusedMedicationsStartDate07_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartDate06_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartDate05_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartDate02_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsStartDate01_dte = New DevExpress.XtraEditors.DateEdit()
        Me.Infusion_InfusedMedicationsResponse07_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite07_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse06_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsResponse01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite06_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite05_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite04_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite03_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite02_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Infusion_InfusedMedicationsSite01_cbo = New DevExpress.XtraEditors.ComboBoxEdit()
        CType(Me.Infusion_AccessInfusaport_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusion_AccessInfusaport_grp.SuspendLayout()
        CType(Me.Infusion_AccessInfusaport_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_Nurse_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_Nurse_grp.SuspendLayout()
        CType(Me.Infusions_Nurse_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_QualityIndicators_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_QualityIndicators_grp.SuspendLayout()
        CType(Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_QI_TotalInfusNotCoded_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_QIOption02_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_QIOption01_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_SitesND_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_IncorrectNursingDocumentation_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_IncompleteTimes_chk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_Thrombolytics_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_Thrombolytics_grp.SuspendLayout()
        CType(Me.Infusion_Thrombolytics_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_BloodAdministration_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_BloodAdministration_grp.SuspendLayout()
        CType(Me.Infusion_BloodAdminstration_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_IVSites_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_IVSites_grp.SuspendLayout()
        CType(Me.Infusion_IVSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_IVSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_IVSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedications_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_TitratedMedications_grp.SuspendLayout()
        CType(Me.Infusions_TitratedMedicationsMedication05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView26, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsMedication04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView25, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsMedication03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView24, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsMedication02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsMedication01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_TitratedMedicationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_Hydrations_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_Hydrations_grp.SuspendLayout()
        CType(Me.Infusion_HydrationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsRate05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsRate04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsRate03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsRate02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsRate01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_HydrationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusions_InfusedMedications_grp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Infusions_InfusedMedications_grp.SuspendLayout()
        CType(Me.Infusion_InfusedMedicationsMedication07_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication06_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsMedication01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime07_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime06_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate07_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate07_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate06_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate06_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime07_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime06_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate07_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate07_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate06_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate06_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse07_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite07_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse06_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite06_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Infusion_InfusedMedicationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Infusion_AccessInfusaport_grp
        '
        Me.Infusion_AccessInfusaport_grp.Controls.Add(Me.Infusion_AccessInfusaport_chk)
        Me.Infusion_AccessInfusaport_grp.Location = New System.Drawing.Point(927, 387)
        Me.Infusion_AccessInfusaport_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_AccessInfusaport_grp.Name = "Infusion_AccessInfusaport_grp"
        Me.Infusion_AccessInfusaport_grp.Size = New System.Drawing.Size(289, 71)
        Me.Infusion_AccessInfusaport_grp.TabIndex = 26
        '
        'Infusion_AccessInfusaport_chk
        '
        Me.Infusion_AccessInfusaport_chk.Location = New System.Drawing.Point(8, 35)
        Me.Infusion_AccessInfusaport_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_AccessInfusaport_chk.Name = "Infusion_AccessInfusaport_chk"
        Me.Infusion_AccessInfusaport_chk.Properties.AllowFocused = False
        Me.Infusion_AccessInfusaport_chk.Properties.Caption = "Access VAD"
        Me.Infusion_AccessInfusaport_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_AccessInfusaport_chk.Size = New System.Drawing.Size(251, 22)
        Me.Infusion_AccessInfusaport_chk.TabIndex = 1
        '
        'Infusions_Nurse_grp
        '
        Me.Infusions_Nurse_grp.Controls.Add(Me.Infusions_Nurse_cbo)
        Me.Infusions_Nurse_grp.Location = New System.Drawing.Point(927, 298)
        Me.Infusions_Nurse_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_Nurse_grp.Name = "Infusions_Nurse_grp"
        Me.Infusions_Nurse_grp.Size = New System.Drawing.Size(289, 87)
        Me.Infusions_Nurse_grp.TabIndex = 24
        Me.Infusions_Nurse_grp.Text = "Nurse"
        '
        'Infusions_Nurse_cbo
        '
        Me.Infusions_Nurse_cbo.Location = New System.Drawing.Point(37, 39)
        Me.Infusions_Nurse_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_Nurse_cbo.Name = "Infusions_Nurse_cbo"
        Me.Infusions_Nurse_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_Nurse_cbo.Properties.Items.AddRange(New Object() {"Patient", "Other"})
        Me.Infusions_Nurse_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_Nurse_cbo.Size = New System.Drawing.Size(201, 24)
        Me.Infusions_Nurse_cbo.TabIndex = 0
        '
        'Infusions_QualityIndicators_grp
        '
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QI_TotalInfusCodedAsIVP_cbo)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QI_TotalInfusCodedAsIVP_lbl)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QI_TotalInfusNotCoded_cbo)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QI_TotalInfusNotCoded_lbl)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QIOption02_chk)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_QIOption01_chk)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_SitesND_chk)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_IncorrectNursingDocumentation_chk)
        Me.Infusions_QualityIndicators_grp.Controls.Add(Me.Infusion_IncompleteTimes_chk)
        Me.Infusions_QualityIndicators_grp.Location = New System.Drawing.Point(927, 462)
        Me.Infusions_QualityIndicators_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_QualityIndicators_grp.Name = "Infusions_QualityIndicators_grp"
        Me.Infusions_QualityIndicators_grp.Size = New System.Drawing.Size(285, 202)
        Me.Infusions_QualityIndicators_grp.TabIndex = 25
        Me.Infusions_QualityIndicators_grp.Text = "Quality Indicators"
        '
        'Infusion_QI_TotalInfusCodedAsIVP_cbo
        '
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Location = New System.Drawing.Point(208, 160)
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Name = "Infusion_QI_TotalInfusCodedAsIVP_cbo"
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Properties.Items.AddRange(New Object() {"", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"})
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Size = New System.Drawing.Size(55, 24)
        Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.TabIndex = 101
        '
        'Infusion_QI_TotalInfusCodedAsIVP_lbl
        '
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.Location = New System.Drawing.Point(8, 164)
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.Name = "Infusion_QI_TotalInfusCodedAsIVP_lbl"
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.Size = New System.Drawing.Size(192, 18)
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.TabIndex = 100
        Me.Infusion_QI_TotalInfusCodedAsIVP_lbl.Text = "Total Infusions Coded As IVP"
        '
        'Infusion_QI_TotalInfusNotCoded_cbo
        '
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Location = New System.Drawing.Point(208, 130)
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Name = "Infusion_QI_TotalInfusNotCoded_cbo"
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Properties.Items.AddRange(New Object() {"", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"})
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_QI_TotalInfusNotCoded_cbo.Size = New System.Drawing.Size(55, 24)
        Me.Infusion_QI_TotalInfusNotCoded_cbo.TabIndex = 99
        '
        'Infusion_QI_TotalInfusNotCoded_lbl
        '
        Me.Infusion_QI_TotalInfusNotCoded_lbl.Location = New System.Drawing.Point(8, 139)
        Me.Infusion_QI_TotalInfusNotCoded_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QI_TotalInfusNotCoded_lbl.Name = "Infusion_QI_TotalInfusNotCoded_lbl"
        Me.Infusion_QI_TotalInfusNotCoded_lbl.Size = New System.Drawing.Size(171, 18)
        Me.Infusion_QI_TotalInfusNotCoded_lbl.TabIndex = 98
        Me.Infusion_QI_TotalInfusNotCoded_lbl.Text = "Total Infusions Not Coded"
        '
        'Infusion_QIOption02_chk
        '
        Me.Infusion_QIOption02_chk.Location = New System.Drawing.Point(8, 116)
        Me.Infusion_QIOption02_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QIOption02_chk.Name = "Infusion_QIOption02_chk"
        Me.Infusion_QIOption02_chk.Properties.AllowFocused = False
        Me.Infusion_QIOption02_chk.Properties.Caption = "Option"
        Me.Infusion_QIOption02_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_QIOption02_chk.Size = New System.Drawing.Size(272, 22)
        Me.Infusion_QIOption02_chk.TabIndex = 4
        '
        'Infusion_QIOption01_chk
        '
        Me.Infusion_QIOption01_chk.Location = New System.Drawing.Point(8, 94)
        Me.Infusion_QIOption01_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_QIOption01_chk.Name = "Infusion_QIOption01_chk"
        Me.Infusion_QIOption01_chk.Properties.AllowFocused = False
        Me.Infusion_QIOption01_chk.Properties.Caption = "Option"
        Me.Infusion_QIOption01_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_QIOption01_chk.Size = New System.Drawing.Size(272, 22)
        Me.Infusion_QIOption01_chk.TabIndex = 3
        '
        'Infusion_SitesND_chk
        '
        Me.Infusion_SitesND_chk.Location = New System.Drawing.Point(8, 72)
        Me.Infusion_SitesND_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_SitesND_chk.Name = "Infusion_SitesND_chk"
        Me.Infusion_SitesND_chk.Properties.AllowFocused = False
        Me.Infusion_SitesND_chk.Properties.Caption = "Sites Not Documented"
        Me.Infusion_SitesND_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_SitesND_chk.Size = New System.Drawing.Size(272, 22)
        Me.Infusion_SitesND_chk.TabIndex = 2
        '
        'Infusion_IncorrectNursingDocumentation_chk
        '
        Me.Infusion_IncorrectNursingDocumentation_chk.Location = New System.Drawing.Point(7, 50)
        Me.Infusion_IncorrectNursingDocumentation_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IncorrectNursingDocumentation_chk.Name = "Infusion_IncorrectNursingDocumentation_chk"
        Me.Infusion_IncorrectNursingDocumentation_chk.Properties.AllowFocused = False
        Me.Infusion_IncorrectNursingDocumentation_chk.Properties.Caption = "Incorrect Nursing Documentation"
        Me.Infusion_IncorrectNursingDocumentation_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_IncorrectNursingDocumentation_chk.Size = New System.Drawing.Size(272, 22)
        Me.Infusion_IncorrectNursingDocumentation_chk.TabIndex = 1
        '
        'Infusion_IncompleteTimes_chk
        '
        Me.Infusion_IncompleteTimes_chk.Location = New System.Drawing.Point(7, 28)
        Me.Infusion_IncompleteTimes_chk.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IncompleteTimes_chk.Name = "Infusion_IncompleteTimes_chk"
        Me.Infusion_IncompleteTimes_chk.Properties.AllowFocused = False
        Me.Infusion_IncompleteTimes_chk.Properties.Caption = "Incomplete Times"
        Me.Infusion_IncompleteTimes_chk.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.Infusion_IncompleteTimes_chk.Size = New System.Drawing.Size(272, 22)
        Me.Infusion_IncompleteTimes_chk.TabIndex = 0
        '
        'Infusions_Thrombolytics_grp
        '
        Me.Infusions_Thrombolytics_grp.Controls.Add(Me.Infusion_Thrombolytics_cbo)
        Me.Infusions_Thrombolytics_grp.Location = New System.Drawing.Point(927, 218)
        Me.Infusions_Thrombolytics_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_Thrombolytics_grp.Name = "Infusions_Thrombolytics_grp"
        Me.Infusions_Thrombolytics_grp.Size = New System.Drawing.Size(285, 79)
        Me.Infusions_Thrombolytics_grp.TabIndex = 23
        Me.Infusions_Thrombolytics_grp.Text = "Thrombolytics"
        '
        'Infusion_Thrombolytics_cbo
        '
        Me.Infusion_Thrombolytics_cbo.Location = New System.Drawing.Point(35, 39)
        Me.Infusion_Thrombolytics_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Thrombolytics_cbo.Name = "Infusion_Thrombolytics_cbo"
        Me.Infusion_Thrombolytics_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_Thrombolytics_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_Thrombolytics_cbo.Size = New System.Drawing.Size(201, 24)
        Me.Infusion_Thrombolytics_cbo.TabIndex = 0
        '
        'Infusions_BloodAdministration_grp
        '
        Me.Infusions_BloodAdministration_grp.Controls.Add(Me.Infusion_BloodAdminstration_cbo)
        Me.Infusions_BloodAdministration_grp.Location = New System.Drawing.Point(927, 138)
        Me.Infusions_BloodAdministration_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_BloodAdministration_grp.Name = "Infusions_BloodAdministration_grp"
        Me.Infusions_BloodAdministration_grp.Size = New System.Drawing.Size(285, 79)
        Me.Infusions_BloodAdministration_grp.TabIndex = 22
        Me.Infusions_BloodAdministration_grp.Text = "Blood Administration"
        '
        'Infusion_BloodAdminstration_cbo
        '
        Me.Infusion_BloodAdminstration_cbo.Location = New System.Drawing.Point(35, 39)
        Me.Infusion_BloodAdminstration_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_BloodAdminstration_cbo.Name = "Infusion_BloodAdminstration_cbo"
        Me.Infusion_BloodAdminstration_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_BloodAdminstration_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_BloodAdminstration_cbo.Size = New System.Drawing.Size(201, 24)
        Me.Infusion_BloodAdminstration_cbo.TabIndex = 0
        '
        'Infusions_IVSites_grp
        '
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite03_lbl)
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite02_lbl)
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite01_lbl)
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite03_cbo)
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite02_cbo)
        Me.Infusions_IVSites_grp.Controls.Add(Me.Infusion_IVSite01_cbo)
        Me.Infusions_IVSites_grp.Location = New System.Drawing.Point(927, 13)
        Me.Infusions_IVSites_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_IVSites_grp.Name = "Infusions_IVSites_grp"
        Me.Infusions_IVSites_grp.Size = New System.Drawing.Size(285, 125)
        Me.Infusions_IVSites_grp.TabIndex = 21
        Me.Infusions_IVSites_grp.Text = "IV Sites"
        '
        'Infusion_IVSite03_lbl
        '
        Me.Infusion_IVSite03_lbl.Location = New System.Drawing.Point(17, 96)
        Me.Infusion_IVSite03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite03_lbl.Name = "Infusion_IVSite03_lbl"
        Me.Infusion_IVSite03_lbl.Size = New System.Drawing.Size(42, 18)
        Me.Infusion_IVSite03_lbl.TabIndex = 213
        Me.Infusion_IVSite03_lbl.Text = "Site 3)"
        '
        'Infusion_IVSite02_lbl
        '
        Me.Infusion_IVSite02_lbl.Location = New System.Drawing.Point(17, 67)
        Me.Infusion_IVSite02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite02_lbl.Name = "Infusion_IVSite02_lbl"
        Me.Infusion_IVSite02_lbl.Size = New System.Drawing.Size(42, 18)
        Me.Infusion_IVSite02_lbl.TabIndex = 212
        Me.Infusion_IVSite02_lbl.Text = "Site 2)"
        '
        'Infusion_IVSite01_lbl
        '
        Me.Infusion_IVSite01_lbl.Location = New System.Drawing.Point(17, 38)
        Me.Infusion_IVSite01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite01_lbl.Name = "Infusion_IVSite01_lbl"
        Me.Infusion_IVSite01_lbl.Size = New System.Drawing.Size(42, 18)
        Me.Infusion_IVSite01_lbl.TabIndex = 211
        Me.Infusion_IVSite01_lbl.Text = "Site 1)"
        '
        'Infusion_IVSite03_cbo
        '
        Me.Infusion_IVSite03_cbo.Location = New System.Drawing.Point(67, 91)
        Me.Infusion_IVSite03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite03_cbo.Name = "Infusion_IVSite03_cbo"
        Me.Infusion_IVSite03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_IVSite03_cbo.Properties.Items.AddRange(New Object() {"Lock", "IV Infusion", "Intraosseous"})
        Me.Infusion_IVSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_IVSite03_cbo.Size = New System.Drawing.Size(172, 24)
        Me.Infusion_IVSite03_cbo.TabIndex = 2
        '
        'Infusion_IVSite02_cbo
        '
        Me.Infusion_IVSite02_cbo.Location = New System.Drawing.Point(67, 61)
        Me.Infusion_IVSite02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite02_cbo.Name = "Infusion_IVSite02_cbo"
        Me.Infusion_IVSite02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_IVSite02_cbo.Properties.Items.AddRange(New Object() {"Lock", "IV Infusion", "Intraosseous"})
        Me.Infusion_IVSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_IVSite02_cbo.Size = New System.Drawing.Size(172, 24)
        Me.Infusion_IVSite02_cbo.TabIndex = 1
        '
        'Infusion_IVSite01_cbo
        '
        Me.Infusion_IVSite01_cbo.Location = New System.Drawing.Point(67, 32)
        Me.Infusion_IVSite01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_IVSite01_cbo.Name = "Infusion_IVSite01_cbo"
        Me.Infusion_IVSite01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_IVSite01_cbo.Properties.Items.AddRange(New Object() {"Lock", "IV Infusion", "Intraosseous"})
        Me.Infusion_IVSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_IVSite01_cbo.Size = New System.Drawing.Size(172, 24)
        Me.Infusion_IVSite01_cbo.TabIndex = 0
        '
        'Infusions_TitratedMedications_grp
        '
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication05_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication04_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication03_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication02_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication01_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedications05_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedications04_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedications03_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedications02_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedications01_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMedication_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins05_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins04_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins03_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins02_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins01_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsMins_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime04_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate04_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime03_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate03_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime05_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime02_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndTime01_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate05_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate02_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsEndDate01_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate_lbl)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime04_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate04_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime03_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate03_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime05_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime02_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartTime01_txt)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate05_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate02_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsStartDate01_dte)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse05_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse04_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse03_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse02_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsResponse01_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite05_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite04_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite03_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite02_cbo)
        Me.Infusions_TitratedMedications_grp.Controls.Add(Me.Infusions_TitratedMedicationsSite01_cbo)
        Me.Infusions_TitratedMedications_grp.Location = New System.Drawing.Point(14, 462)
        Me.Infusions_TitratedMedications_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications_grp.Name = "Infusions_TitratedMedications_grp"
        Me.Infusions_TitratedMedications_grp.Size = New System.Drawing.Size(905, 202)
        Me.Infusions_TitratedMedications_grp.TabIndex = 20
        Me.Infusions_TitratedMedications_grp.Text = "Additional Infused Medications"
        '
        'Infusions_TitratedMedicationsMedication05_cbo
        '
        Me.Infusions_TitratedMedicationsMedication05_cbo.EditValue = ""
        Me.Infusions_TitratedMedicationsMedication05_cbo.Location = New System.Drawing.Point(576, 160)
        Me.Infusions_TitratedMedicationsMedication05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication05_cbo.Name = "Infusions_TitratedMedicationsMedication05_cbo"
        Me.Infusions_TitratedMedicationsMedication05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsMedication05_cbo.Properties.DisplayMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication05_cbo.Properties.NullText = ""
        Me.Infusions_TitratedMedicationsMedication05_cbo.Properties.PopupView = Me.GridView26
        Me.Infusions_TitratedMedicationsMedication05_cbo.Properties.ValueMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication05_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusions_TitratedMedicationsMedication05_cbo.TabIndex = 38
        '
        'GridView26
        '
        Me.GridView26.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn131, Me.GridColumn132, Me.GridColumn133, Me.GridColumn134, Me.GridColumn135})
        Me.GridView26.DetailHeight = 485
        Me.GridView26.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView26.Name = "GridView26"
        Me.GridView26.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView26.OptionsView.ShowGroupPanel = False
        '
        'GridColumn131
        '
        Me.GridColumn131.FieldName = "MedName"
        Me.GridColumn131.MinWidth = 27
        Me.GridColumn131.Name = "GridColumn131"
        Me.GridColumn131.Visible = True
        Me.GridColumn131.VisibleIndex = 0
        Me.GridColumn131.Width = 1104
        '
        'GridColumn132
        '
        Me.GridColumn132.FieldName = "CC"
        Me.GridColumn132.MinWidth = 27
        Me.GridColumn132.Name = "GridColumn132"
        Me.GridColumn132.Visible = True
        Me.GridColumn132.VisibleIndex = 1
        Me.GridColumn132.Width = 116
        '
        'GridColumn133
        '
        Me.GridColumn133.Caption = "Dedicated Line"
        Me.GridColumn133.FieldName = "DedicatedLine"
        Me.GridColumn133.MinWidth = 27
        Me.GridColumn133.Name = "GridColumn133"
        Me.GridColumn133.Visible = True
        Me.GridColumn133.VisibleIndex = 2
        Me.GridColumn133.Width = 139
        '
        'GridColumn134
        '
        Me.GridColumn134.Caption = "Chemo"
        Me.GridColumn134.FieldName = "Chemo"
        Me.GridColumn134.MinWidth = 27
        Me.GridColumn134.Name = "GridColumn134"
        Me.GridColumn134.Visible = True
        Me.GridColumn134.VisibleIndex = 3
        Me.GridColumn134.Width = 111
        '
        'GridColumn135
        '
        Me.GridColumn135.Caption = "Hormonal"
        Me.GridColumn135.FieldName = "Hormonal"
        Me.GridColumn135.MinWidth = 27
        Me.GridColumn135.Name = "GridColumn135"
        Me.GridColumn135.Visible = True
        Me.GridColumn135.VisibleIndex = 4
        Me.GridColumn135.Width = 123
        '
        'Infusions_TitratedMedicationsMedication04_cbo
        '
        Me.Infusions_TitratedMedicationsMedication04_cbo.EditValue = ""
        Me.Infusions_TitratedMedicationsMedication04_cbo.Location = New System.Drawing.Point(576, 133)
        Me.Infusions_TitratedMedicationsMedication04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication04_cbo.Name = "Infusions_TitratedMedicationsMedication04_cbo"
        Me.Infusions_TitratedMedicationsMedication04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsMedication04_cbo.Properties.DisplayMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication04_cbo.Properties.NullText = ""
        Me.Infusions_TitratedMedicationsMedication04_cbo.Properties.PopupView = Me.GridView25
        Me.Infusions_TitratedMedicationsMedication04_cbo.Properties.ValueMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication04_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusions_TitratedMedicationsMedication04_cbo.TabIndex = 30
        '
        'GridView25
        '
        Me.GridView25.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn126, Me.GridColumn127, Me.GridColumn128, Me.GridColumn129, Me.GridColumn130})
        Me.GridView25.DetailHeight = 485
        Me.GridView25.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView25.Name = "GridView25"
        Me.GridView25.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView25.OptionsView.ShowGroupPanel = False
        '
        'GridColumn126
        '
        Me.GridColumn126.FieldName = "MedName"
        Me.GridColumn126.MinWidth = 27
        Me.GridColumn126.Name = "GridColumn126"
        Me.GridColumn126.Visible = True
        Me.GridColumn126.VisibleIndex = 0
        Me.GridColumn126.Width = 1104
        '
        'GridColumn127
        '
        Me.GridColumn127.FieldName = "CC"
        Me.GridColumn127.MinWidth = 27
        Me.GridColumn127.Name = "GridColumn127"
        Me.GridColumn127.Visible = True
        Me.GridColumn127.VisibleIndex = 1
        Me.GridColumn127.Width = 116
        '
        'GridColumn128
        '
        Me.GridColumn128.Caption = "Dedicated Line"
        Me.GridColumn128.FieldName = "DedicatedLine"
        Me.GridColumn128.MinWidth = 27
        Me.GridColumn128.Name = "GridColumn128"
        Me.GridColumn128.Visible = True
        Me.GridColumn128.VisibleIndex = 2
        Me.GridColumn128.Width = 139
        '
        'GridColumn129
        '
        Me.GridColumn129.Caption = "Chemo"
        Me.GridColumn129.FieldName = "Chemo"
        Me.GridColumn129.MinWidth = 27
        Me.GridColumn129.Name = "GridColumn129"
        Me.GridColumn129.Visible = True
        Me.GridColumn129.VisibleIndex = 3
        Me.GridColumn129.Width = 111
        '
        'GridColumn130
        '
        Me.GridColumn130.Caption = "Hormonal"
        Me.GridColumn130.FieldName = "Hormonal"
        Me.GridColumn130.MinWidth = 27
        Me.GridColumn130.Name = "GridColumn130"
        Me.GridColumn130.Visible = True
        Me.GridColumn130.VisibleIndex = 4
        Me.GridColumn130.Width = 123
        '
        'Infusions_TitratedMedicationsMedication03_cbo
        '
        Me.Infusions_TitratedMedicationsMedication03_cbo.EditValue = ""
        Me.Infusions_TitratedMedicationsMedication03_cbo.Location = New System.Drawing.Point(576, 105)
        Me.Infusions_TitratedMedicationsMedication03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication03_cbo.Name = "Infusions_TitratedMedicationsMedication03_cbo"
        Me.Infusions_TitratedMedicationsMedication03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsMedication03_cbo.Properties.DisplayMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication03_cbo.Properties.NullText = ""
        Me.Infusions_TitratedMedicationsMedication03_cbo.Properties.PopupView = Me.GridView24
        Me.Infusions_TitratedMedicationsMedication03_cbo.Properties.ValueMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication03_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusions_TitratedMedicationsMedication03_cbo.TabIndex = 22
        '
        'GridView24
        '
        Me.GridView24.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn121, Me.GridColumn122, Me.GridColumn123, Me.GridColumn124, Me.GridColumn125})
        Me.GridView24.DetailHeight = 485
        Me.GridView24.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView24.Name = "GridView24"
        Me.GridView24.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView24.OptionsView.ShowGroupPanel = False
        '
        'GridColumn121
        '
        Me.GridColumn121.FieldName = "MedName"
        Me.GridColumn121.MinWidth = 27
        Me.GridColumn121.Name = "GridColumn121"
        Me.GridColumn121.Visible = True
        Me.GridColumn121.VisibleIndex = 0
        Me.GridColumn121.Width = 1104
        '
        'GridColumn122
        '
        Me.GridColumn122.FieldName = "CC"
        Me.GridColumn122.MinWidth = 27
        Me.GridColumn122.Name = "GridColumn122"
        Me.GridColumn122.Visible = True
        Me.GridColumn122.VisibleIndex = 1
        Me.GridColumn122.Width = 116
        '
        'GridColumn123
        '
        Me.GridColumn123.Caption = "Dedicated Line"
        Me.GridColumn123.FieldName = "DedicatedLine"
        Me.GridColumn123.MinWidth = 27
        Me.GridColumn123.Name = "GridColumn123"
        Me.GridColumn123.Visible = True
        Me.GridColumn123.VisibleIndex = 2
        Me.GridColumn123.Width = 139
        '
        'GridColumn124
        '
        Me.GridColumn124.Caption = "Chemo"
        Me.GridColumn124.FieldName = "Chemo"
        Me.GridColumn124.MinWidth = 27
        Me.GridColumn124.Name = "GridColumn124"
        Me.GridColumn124.Visible = True
        Me.GridColumn124.VisibleIndex = 3
        Me.GridColumn124.Width = 111
        '
        'GridColumn125
        '
        Me.GridColumn125.Caption = "Hormonal"
        Me.GridColumn125.FieldName = "Hormonal"
        Me.GridColumn125.MinWidth = 27
        Me.GridColumn125.Name = "GridColumn125"
        Me.GridColumn125.Visible = True
        Me.GridColumn125.VisibleIndex = 4
        Me.GridColumn125.Width = 123
        '
        'Infusions_TitratedMedicationsMedication02_cbo
        '
        Me.Infusions_TitratedMedicationsMedication02_cbo.EditValue = ""
        Me.Infusions_TitratedMedicationsMedication02_cbo.Location = New System.Drawing.Point(576, 77)
        Me.Infusions_TitratedMedicationsMedication02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication02_cbo.Name = "Infusions_TitratedMedicationsMedication02_cbo"
        Me.Infusions_TitratedMedicationsMedication02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsMedication02_cbo.Properties.DisplayMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication02_cbo.Properties.NullText = ""
        Me.Infusions_TitratedMedicationsMedication02_cbo.Properties.PopupView = Me.GridView23
        Me.Infusions_TitratedMedicationsMedication02_cbo.Properties.ValueMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication02_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusions_TitratedMedicationsMedication02_cbo.TabIndex = 14
        '
        'GridView23
        '
        Me.GridView23.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn116, Me.GridColumn117, Me.GridColumn118, Me.GridColumn119, Me.GridColumn120})
        Me.GridView23.DetailHeight = 485
        Me.GridView23.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView23.Name = "GridView23"
        Me.GridView23.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView23.OptionsView.ShowGroupPanel = False
        '
        'GridColumn116
        '
        Me.GridColumn116.FieldName = "MedName"
        Me.GridColumn116.MinWidth = 27
        Me.GridColumn116.Name = "GridColumn116"
        Me.GridColumn116.Visible = True
        Me.GridColumn116.VisibleIndex = 0
        Me.GridColumn116.Width = 1104
        '
        'GridColumn117
        '
        Me.GridColumn117.FieldName = "CC"
        Me.GridColumn117.MinWidth = 27
        Me.GridColumn117.Name = "GridColumn117"
        Me.GridColumn117.Visible = True
        Me.GridColumn117.VisibleIndex = 1
        Me.GridColumn117.Width = 116
        '
        'GridColumn118
        '
        Me.GridColumn118.Caption = "Dedicated Line"
        Me.GridColumn118.FieldName = "DedicatedLine"
        Me.GridColumn118.MinWidth = 27
        Me.GridColumn118.Name = "GridColumn118"
        Me.GridColumn118.Visible = True
        Me.GridColumn118.VisibleIndex = 2
        Me.GridColumn118.Width = 139
        '
        'GridColumn119
        '
        Me.GridColumn119.Caption = "Chemo"
        Me.GridColumn119.FieldName = "Chemo"
        Me.GridColumn119.MinWidth = 27
        Me.GridColumn119.Name = "GridColumn119"
        Me.GridColumn119.Visible = True
        Me.GridColumn119.VisibleIndex = 3
        Me.GridColumn119.Width = 111
        '
        'GridColumn120
        '
        Me.GridColumn120.Caption = "Hormonal"
        Me.GridColumn120.FieldName = "Hormonal"
        Me.GridColumn120.MinWidth = 27
        Me.GridColumn120.Name = "GridColumn120"
        Me.GridColumn120.Visible = True
        Me.GridColumn120.VisibleIndex = 4
        Me.GridColumn120.Width = 123
        '
        'Infusions_TitratedMedicationsMedication01_cbo
        '
        Me.Infusions_TitratedMedicationsMedication01_cbo.EditValue = ""
        Me.Infusions_TitratedMedicationsMedication01_cbo.Location = New System.Drawing.Point(576, 49)
        Me.Infusions_TitratedMedicationsMedication01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication01_cbo.Name = "Infusions_TitratedMedicationsMedication01_cbo"
        Me.Infusions_TitratedMedicationsMedication01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsMedication01_cbo.Properties.DisplayMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication01_cbo.Properties.NullText = ""
        Me.Infusions_TitratedMedicationsMedication01_cbo.Properties.PopupView = Me.GridView22
        Me.Infusions_TitratedMedicationsMedication01_cbo.Properties.ValueMember = "MedName"
        Me.Infusions_TitratedMedicationsMedication01_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusions_TitratedMedicationsMedication01_cbo.TabIndex = 6
        '
        'GridView22
        '
        Me.GridView22.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn111, Me.GridColumn112, Me.GridColumn113, Me.GridColumn114, Me.GridColumn115})
        Me.GridView22.DetailHeight = 485
        Me.GridView22.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView22.Name = "GridView22"
        Me.GridView22.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView22.OptionsView.ShowGroupPanel = False
        '
        'GridColumn111
        '
        Me.GridColumn111.FieldName = "MedName"
        Me.GridColumn111.MinWidth = 27
        Me.GridColumn111.Name = "GridColumn111"
        Me.GridColumn111.Visible = True
        Me.GridColumn111.VisibleIndex = 0
        Me.GridColumn111.Width = 1104
        '
        'GridColumn112
        '
        Me.GridColumn112.FieldName = "CC"
        Me.GridColumn112.MinWidth = 27
        Me.GridColumn112.Name = "GridColumn112"
        Me.GridColumn112.Visible = True
        Me.GridColumn112.VisibleIndex = 1
        Me.GridColumn112.Width = 116
        '
        'GridColumn113
        '
        Me.GridColumn113.Caption = "Dedicated Line"
        Me.GridColumn113.FieldName = "DedicatedLine"
        Me.GridColumn113.MinWidth = 27
        Me.GridColumn113.Name = "GridColumn113"
        Me.GridColumn113.Visible = True
        Me.GridColumn113.VisibleIndex = 2
        Me.GridColumn113.Width = 139
        '
        'GridColumn114
        '
        Me.GridColumn114.Caption = "Chemo"
        Me.GridColumn114.FieldName = "Chemo"
        Me.GridColumn114.MinWidth = 27
        Me.GridColumn114.Name = "GridColumn114"
        Me.GridColumn114.Visible = True
        Me.GridColumn114.VisibleIndex = 3
        Me.GridColumn114.Width = 111
        '
        'GridColumn115
        '
        Me.GridColumn115.Caption = "Hormonal"
        Me.GridColumn115.FieldName = "Hormonal"
        Me.GridColumn115.MinWidth = 27
        Me.GridColumn115.Name = "GridColumn115"
        Me.GridColumn115.Visible = True
        Me.GridColumn115.VisibleIndex = 4
        Me.GridColumn115.Width = 123
        '
        'Infusions_TitratedMedications05_lbl
        '
        Me.Infusions_TitratedMedications05_lbl.Location = New System.Drawing.Point(21, 160)
        Me.Infusions_TitratedMedications05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications05_lbl.Name = "Infusions_TitratedMedications05_lbl"
        Me.Infusions_TitratedMedications05_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusions_TitratedMedications05_lbl.TabIndex = 311
        Me.Infusions_TitratedMedications05_lbl.Text = "5)"
        '
        'Infusions_TitratedMedications04_lbl
        '
        Me.Infusions_TitratedMedications04_lbl.Location = New System.Drawing.Point(21, 133)
        Me.Infusions_TitratedMedications04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications04_lbl.Name = "Infusions_TitratedMedications04_lbl"
        Me.Infusions_TitratedMedications04_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusions_TitratedMedications04_lbl.TabIndex = 310
        Me.Infusions_TitratedMedications04_lbl.Text = "4)"
        '
        'Infusions_TitratedMedications03_lbl
        '
        Me.Infusions_TitratedMedications03_lbl.Location = New System.Drawing.Point(21, 105)
        Me.Infusions_TitratedMedications03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications03_lbl.Name = "Infusions_TitratedMedications03_lbl"
        Me.Infusions_TitratedMedications03_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusions_TitratedMedications03_lbl.TabIndex = 309
        Me.Infusions_TitratedMedications03_lbl.Text = "3)"
        '
        'Infusions_TitratedMedications02_lbl
        '
        Me.Infusions_TitratedMedications02_lbl.Location = New System.Drawing.Point(21, 77)
        Me.Infusions_TitratedMedications02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications02_lbl.Name = "Infusions_TitratedMedications02_lbl"
        Me.Infusions_TitratedMedications02_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusions_TitratedMedications02_lbl.TabIndex = 308
        Me.Infusions_TitratedMedications02_lbl.Text = "2)"
        '
        'Infusions_TitratedMedications01_lbl
        '
        Me.Infusions_TitratedMedications01_lbl.Location = New System.Drawing.Point(21, 49)
        Me.Infusions_TitratedMedications01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedications01_lbl.Name = "Infusions_TitratedMedications01_lbl"
        Me.Infusions_TitratedMedications01_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusions_TitratedMedications01_lbl.TabIndex = 307
        Me.Infusions_TitratedMedications01_lbl.Text = "1)"
        '
        'Infusions_TitratedMedicationsMedication_lbl
        '
        Me.Infusions_TitratedMedicationsMedication_lbl.Location = New System.Drawing.Point(608, 27)
        Me.Infusions_TitratedMedicationsMedication_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMedication_lbl.Name = "Infusions_TitratedMedicationsMedication_lbl"
        Me.Infusions_TitratedMedicationsMedication_lbl.Size = New System.Drawing.Size(68, 18)
        Me.Infusions_TitratedMedicationsMedication_lbl.TabIndex = 301
        Me.Infusions_TitratedMedicationsMedication_lbl.Text = "Medication"
        '
        'Infusions_TitratedMedicationsMins05_lbl
        '
        Me.Infusions_TitratedMedicationsMins05_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusions_TitratedMedicationsMins05_lbl.Appearance.Options.UseFont = True
        Me.Infusions_TitratedMedicationsMins05_lbl.Location = New System.Drawing.Point(440, 164)
        Me.Infusions_TitratedMedicationsMins05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins05_lbl.Name = "Infusions_TitratedMedicationsMins05_lbl"
        Me.Infusions_TitratedMedicationsMins05_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusions_TitratedMedicationsMins05_lbl.TabIndex = 36
        Me.Infusions_TitratedMedicationsMins05_lbl.Text = "9999"
        '
        'Infusions_TitratedMedicationsMins04_lbl
        '
        Me.Infusions_TitratedMedicationsMins04_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusions_TitratedMedicationsMins04_lbl.Appearance.Options.UseFont = True
        Me.Infusions_TitratedMedicationsMins04_lbl.Location = New System.Drawing.Point(440, 137)
        Me.Infusions_TitratedMedicationsMins04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins04_lbl.Name = "Infusions_TitratedMedicationsMins04_lbl"
        Me.Infusions_TitratedMedicationsMins04_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusions_TitratedMedicationsMins04_lbl.TabIndex = 28
        Me.Infusions_TitratedMedicationsMins04_lbl.Text = "9999"
        '
        'Infusions_TitratedMedicationsMins03_lbl
        '
        Me.Infusions_TitratedMedicationsMins03_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusions_TitratedMedicationsMins03_lbl.Appearance.Options.UseFont = True
        Me.Infusions_TitratedMedicationsMins03_lbl.Location = New System.Drawing.Point(440, 109)
        Me.Infusions_TitratedMedicationsMins03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins03_lbl.Name = "Infusions_TitratedMedicationsMins03_lbl"
        Me.Infusions_TitratedMedicationsMins03_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusions_TitratedMedicationsMins03_lbl.TabIndex = 20
        Me.Infusions_TitratedMedicationsMins03_lbl.Text = "9999"
        '
        'Infusions_TitratedMedicationsMins02_lbl
        '
        Me.Infusions_TitratedMedicationsMins02_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusions_TitratedMedicationsMins02_lbl.Appearance.Options.UseFont = True
        Me.Infusions_TitratedMedicationsMins02_lbl.Location = New System.Drawing.Point(440, 81)
        Me.Infusions_TitratedMedicationsMins02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins02_lbl.Name = "Infusions_TitratedMedicationsMins02_lbl"
        Me.Infusions_TitratedMedicationsMins02_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusions_TitratedMedicationsMins02_lbl.TabIndex = 12
        Me.Infusions_TitratedMedicationsMins02_lbl.Text = "9999"
        '
        'Infusions_TitratedMedicationsMins01_lbl
        '
        Me.Infusions_TitratedMedicationsMins01_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusions_TitratedMedicationsMins01_lbl.Appearance.Options.UseFont = True
        Me.Infusions_TitratedMedicationsMins01_lbl.Location = New System.Drawing.Point(440, 54)
        Me.Infusions_TitratedMedicationsMins01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins01_lbl.Name = "Infusions_TitratedMedicationsMins01_lbl"
        Me.Infusions_TitratedMedicationsMins01_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusions_TitratedMedicationsMins01_lbl.TabIndex = 4
        Me.Infusions_TitratedMedicationsMins01_lbl.Text = "9999"
        '
        'Infusions_TitratedMedicationsResponse_lbl
        '
        Me.Infusions_TitratedMedicationsResponse_lbl.Location = New System.Drawing.Point(772, 27)
        Me.Infusions_TitratedMedicationsResponse_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse_lbl.Name = "Infusions_TitratedMedicationsResponse_lbl"
        Me.Infusions_TitratedMedicationsResponse_lbl.Size = New System.Drawing.Size(63, 18)
        Me.Infusions_TitratedMedicationsResponse_lbl.TabIndex = 295
        Me.Infusions_TitratedMedicationsResponse_lbl.Text = "Response"
        '
        'Infusions_TitratedMedicationsSite_lbl
        '
        Me.Infusions_TitratedMedicationsSite_lbl.Location = New System.Drawing.Point(509, 27)
        Me.Infusions_TitratedMedicationsSite_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite_lbl.Name = "Infusions_TitratedMedicationsSite_lbl"
        Me.Infusions_TitratedMedicationsSite_lbl.Size = New System.Drawing.Size(23, 18)
        Me.Infusions_TitratedMedicationsSite_lbl.TabIndex = 294
        Me.Infusions_TitratedMedicationsSite_lbl.Text = "Site"
        '
        'Infusions_TitratedMedicationsMins_lbl
        '
        Me.Infusions_TitratedMedicationsMins_lbl.Location = New System.Drawing.Point(443, 27)
        Me.Infusions_TitratedMedicationsMins_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsMins_lbl.Name = "Infusions_TitratedMedicationsMins_lbl"
        Me.Infusions_TitratedMedicationsMins_lbl.Size = New System.Drawing.Size(29, 18)
        Me.Infusions_TitratedMedicationsMins_lbl.TabIndex = 293
        Me.Infusions_TitratedMedicationsMins_lbl.Text = "Mins"
        '
        'Infusions_TitratedMedicationsEndTime_lbl
        '
        Me.Infusions_TitratedMedicationsEndTime_lbl.Location = New System.Drawing.Point(355, 27)
        Me.Infusions_TitratedMedicationsEndTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime_lbl.Name = "Infusions_TitratedMedicationsEndTime_lbl"
        Me.Infusions_TitratedMedicationsEndTime_lbl.Size = New System.Drawing.Size(62, 18)
        Me.Infusions_TitratedMedicationsEndTime_lbl.TabIndex = 292
        Me.Infusions_TitratedMedicationsEndTime_lbl.Text = "End Time"
        '
        'Infusions_TitratedMedicationsEndDate_lbl
        '
        Me.Infusions_TitratedMedicationsEndDate_lbl.Location = New System.Drawing.Point(257, 27)
        Me.Infusions_TitratedMedicationsEndDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate_lbl.Name = "Infusions_TitratedMedicationsEndDate_lbl"
        Me.Infusions_TitratedMedicationsEndDate_lbl.Size = New System.Drawing.Size(60, 18)
        Me.Infusions_TitratedMedicationsEndDate_lbl.TabIndex = 291
        Me.Infusions_TitratedMedicationsEndDate_lbl.Text = "End Date"
        '
        'Infusions_TitratedMedicationsEndTime04_txt
        '
        Me.Infusions_TitratedMedicationsEndTime04_txt.Location = New System.Drawing.Point(357, 133)
        Me.Infusions_TitratedMedicationsEndTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime04_txt.Name = "Infusions_TitratedMedicationsEndTime04_txt"
        Me.Infusions_TitratedMedicationsEndTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsEndTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsEndTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsEndTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsEndTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsEndTime04_txt.TabIndex = 27
        '
        'Infusions_TitratedMedicationsEndDate04_dte
        '
        Me.Infusions_TitratedMedicationsEndDate04_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsEndDate04_dte.Location = New System.Drawing.Point(241, 133)
        Me.Infusions_TitratedMedicationsEndDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate04_dte.Name = "Infusions_TitratedMedicationsEndDate04_dte"
        Me.Infusions_TitratedMedicationsEndDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsEndDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsEndDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsEndDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsEndDate04_dte.TabIndex = 26
        '
        'Infusions_TitratedMedicationsEndTime03_txt
        '
        Me.Infusions_TitratedMedicationsEndTime03_txt.Location = New System.Drawing.Point(357, 105)
        Me.Infusions_TitratedMedicationsEndTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime03_txt.Name = "Infusions_TitratedMedicationsEndTime03_txt"
        Me.Infusions_TitratedMedicationsEndTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsEndTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsEndTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsEndTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsEndTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsEndTime03_txt.TabIndex = 19
        '
        'Infusions_TitratedMedicationsEndDate03_dte
        '
        Me.Infusions_TitratedMedicationsEndDate03_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsEndDate03_dte.Location = New System.Drawing.Point(241, 105)
        Me.Infusions_TitratedMedicationsEndDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate03_dte.Name = "Infusions_TitratedMedicationsEndDate03_dte"
        Me.Infusions_TitratedMedicationsEndDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsEndDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsEndDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsEndDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsEndDate03_dte.TabIndex = 18
        '
        'Infusions_TitratedMedicationsEndTime05_txt
        '
        Me.Infusions_TitratedMedicationsEndTime05_txt.Location = New System.Drawing.Point(357, 160)
        Me.Infusions_TitratedMedicationsEndTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime05_txt.Name = "Infusions_TitratedMedicationsEndTime05_txt"
        Me.Infusions_TitratedMedicationsEndTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsEndTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsEndTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsEndTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsEndTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsEndTime05_txt.TabIndex = 35
        '
        'Infusions_TitratedMedicationsEndTime02_txt
        '
        Me.Infusions_TitratedMedicationsEndTime02_txt.Location = New System.Drawing.Point(357, 77)
        Me.Infusions_TitratedMedicationsEndTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime02_txt.Name = "Infusions_TitratedMedicationsEndTime02_txt"
        Me.Infusions_TitratedMedicationsEndTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsEndTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsEndTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsEndTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsEndTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsEndTime02_txt.TabIndex = 11
        '
        'Infusions_TitratedMedicationsEndTime01_txt
        '
        Me.Infusions_TitratedMedicationsEndTime01_txt.Location = New System.Drawing.Point(357, 49)
        Me.Infusions_TitratedMedicationsEndTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndTime01_txt.Name = "Infusions_TitratedMedicationsEndTime01_txt"
        Me.Infusions_TitratedMedicationsEndTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsEndTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsEndTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsEndTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsEndTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsEndTime01_txt.TabIndex = 3
        '
        'Infusions_TitratedMedicationsEndDate05_dte
        '
        Me.Infusions_TitratedMedicationsEndDate05_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsEndDate05_dte.Location = New System.Drawing.Point(241, 160)
        Me.Infusions_TitratedMedicationsEndDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate05_dte.Name = "Infusions_TitratedMedicationsEndDate05_dte"
        Me.Infusions_TitratedMedicationsEndDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsEndDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsEndDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsEndDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsEndDate05_dte.TabIndex = 34
        '
        'Infusions_TitratedMedicationsEndDate02_dte
        '
        Me.Infusions_TitratedMedicationsEndDate02_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsEndDate02_dte.Location = New System.Drawing.Point(241, 77)
        Me.Infusions_TitratedMedicationsEndDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate02_dte.Name = "Infusions_TitratedMedicationsEndDate02_dte"
        Me.Infusions_TitratedMedicationsEndDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsEndDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsEndDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsEndDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsEndDate02_dte.TabIndex = 10
        '
        'Infusions_TitratedMedicationsEndDate01_dte
        '
        Me.Infusions_TitratedMedicationsEndDate01_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsEndDate01_dte.Location = New System.Drawing.Point(241, 49)
        Me.Infusions_TitratedMedicationsEndDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsEndDate01_dte.Name = "Infusions_TitratedMedicationsEndDate01_dte"
        Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusions_TitratedMedicationsEndDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsEndDate01_dte.TabIndex = 2
        Me.Infusions_TitratedMedicationsEndDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusions_TitratedMedicationsStartTime_lbl
        '
        Me.Infusions_TitratedMedicationsStartTime_lbl.Location = New System.Drawing.Point(160, 27)
        Me.Infusions_TitratedMedicationsStartTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime_lbl.Name = "Infusions_TitratedMedicationsStartTime_lbl"
        Me.Infusions_TitratedMedicationsStartTime_lbl.Size = New System.Drawing.Size(69, 18)
        Me.Infusions_TitratedMedicationsStartTime_lbl.TabIndex = 280
        Me.Infusions_TitratedMedicationsStartTime_lbl.Text = "Start Time"
        '
        'Infusions_TitratedMedicationsStartDate_lbl
        '
        Me.Infusions_TitratedMedicationsStartDate_lbl.Location = New System.Drawing.Point(63, 27)
        Me.Infusions_TitratedMedicationsStartDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate_lbl.Name = "Infusions_TitratedMedicationsStartDate_lbl"
        Me.Infusions_TitratedMedicationsStartDate_lbl.Size = New System.Drawing.Size(67, 18)
        Me.Infusions_TitratedMedicationsStartDate_lbl.TabIndex = 279
        Me.Infusions_TitratedMedicationsStartDate_lbl.Text = "Start Date"
        '
        'Infusions_TitratedMedicationsStartTime04_txt
        '
        Me.Infusions_TitratedMedicationsStartTime04_txt.Location = New System.Drawing.Point(163, 133)
        Me.Infusions_TitratedMedicationsStartTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime04_txt.Name = "Infusions_TitratedMedicationsStartTime04_txt"
        Me.Infusions_TitratedMedicationsStartTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsStartTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsStartTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsStartTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsStartTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsStartTime04_txt.TabIndex = 25
        '
        'Infusions_TitratedMedicationsStartDate04_dte
        '
        Me.Infusions_TitratedMedicationsStartDate04_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsStartDate04_dte.Location = New System.Drawing.Point(47, 133)
        Me.Infusions_TitratedMedicationsStartDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate04_dte.Name = "Infusions_TitratedMedicationsStartDate04_dte"
        Me.Infusions_TitratedMedicationsStartDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsStartDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsStartDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsStartDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsStartDate04_dte.TabIndex = 24
        '
        'Infusions_TitratedMedicationsStartTime03_txt
        '
        Me.Infusions_TitratedMedicationsStartTime03_txt.Location = New System.Drawing.Point(163, 105)
        Me.Infusions_TitratedMedicationsStartTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime03_txt.Name = "Infusions_TitratedMedicationsStartTime03_txt"
        Me.Infusions_TitratedMedicationsStartTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsStartTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsStartTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsStartTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsStartTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsStartTime03_txt.TabIndex = 17
        '
        'Infusions_TitratedMedicationsStartDate03_dte
        '
        Me.Infusions_TitratedMedicationsStartDate03_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsStartDate03_dte.Location = New System.Drawing.Point(47, 105)
        Me.Infusions_TitratedMedicationsStartDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate03_dte.Name = "Infusions_TitratedMedicationsStartDate03_dte"
        Me.Infusions_TitratedMedicationsStartDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsStartDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsStartDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsStartDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsStartDate03_dte.TabIndex = 16
        '
        'Infusions_TitratedMedicationsStartTime05_txt
        '
        Me.Infusions_TitratedMedicationsStartTime05_txt.Location = New System.Drawing.Point(163, 160)
        Me.Infusions_TitratedMedicationsStartTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime05_txt.Name = "Infusions_TitratedMedicationsStartTime05_txt"
        Me.Infusions_TitratedMedicationsStartTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsStartTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsStartTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsStartTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsStartTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsStartTime05_txt.TabIndex = 33
        '
        'Infusions_TitratedMedicationsStartTime02_txt
        '
        Me.Infusions_TitratedMedicationsStartTime02_txt.Location = New System.Drawing.Point(163, 77)
        Me.Infusions_TitratedMedicationsStartTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime02_txt.Name = "Infusions_TitratedMedicationsStartTime02_txt"
        Me.Infusions_TitratedMedicationsStartTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsStartTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsStartTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsStartTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsStartTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsStartTime02_txt.TabIndex = 9
        '
        'Infusions_TitratedMedicationsStartTime01_txt
        '
        Me.Infusions_TitratedMedicationsStartTime01_txt.Location = New System.Drawing.Point(163, 49)
        Me.Infusions_TitratedMedicationsStartTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartTime01_txt.Name = "Infusions_TitratedMedicationsStartTime01_txt"
        Me.Infusions_TitratedMedicationsStartTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusions_TitratedMedicationsStartTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusions_TitratedMedicationsStartTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusions_TitratedMedicationsStartTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusions_TitratedMedicationsStartTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusions_TitratedMedicationsStartTime01_txt.TabIndex = 1
        '
        'Infusions_TitratedMedicationsStartDate05_dte
        '
        Me.Infusions_TitratedMedicationsStartDate05_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsStartDate05_dte.Location = New System.Drawing.Point(47, 160)
        Me.Infusions_TitratedMedicationsStartDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate05_dte.Name = "Infusions_TitratedMedicationsStartDate05_dte"
        Me.Infusions_TitratedMedicationsStartDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsStartDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsStartDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsStartDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsStartDate05_dte.TabIndex = 32
        '
        'Infusions_TitratedMedicationsStartDate02_dte
        '
        Me.Infusions_TitratedMedicationsStartDate02_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsStartDate02_dte.Location = New System.Drawing.Point(47, 77)
        Me.Infusions_TitratedMedicationsStartDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate02_dte.Name = "Infusions_TitratedMedicationsStartDate02_dte"
        Me.Infusions_TitratedMedicationsStartDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsStartDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsStartDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsStartDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsStartDate02_dte.TabIndex = 8
        '
        'Infusions_TitratedMedicationsStartDate01_dte
        '
        Me.Infusions_TitratedMedicationsStartDate01_dte.EditValue = Nothing
        Me.Infusions_TitratedMedicationsStartDate01_dte.Location = New System.Drawing.Point(47, 49)
        Me.Infusions_TitratedMedicationsStartDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsStartDate01_dte.Name = "Infusions_TitratedMedicationsStartDate01_dte"
        Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusions_TitratedMedicationsStartDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusions_TitratedMedicationsStartDate01_dte.TabIndex = 0
        Me.Infusions_TitratedMedicationsStartDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusions_TitratedMedicationsResponse05_cbo
        '
        Me.Infusions_TitratedMedicationsResponse05_cbo.Location = New System.Drawing.Point(741, 160)
        Me.Infusions_TitratedMedicationsResponse05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse05_cbo.Name = "Infusions_TitratedMedicationsResponse05_cbo"
        Me.Infusions_TitratedMedicationsResponse05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsResponse05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsResponse05_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusions_TitratedMedicationsResponse05_cbo.TabIndex = 39
        '
        'Infusions_TitratedMedicationsResponse04_cbo
        '
        Me.Infusions_TitratedMedicationsResponse04_cbo.Location = New System.Drawing.Point(741, 133)
        Me.Infusions_TitratedMedicationsResponse04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse04_cbo.Name = "Infusions_TitratedMedicationsResponse04_cbo"
        Me.Infusions_TitratedMedicationsResponse04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsResponse04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsResponse04_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusions_TitratedMedicationsResponse04_cbo.TabIndex = 31
        '
        'Infusions_TitratedMedicationsResponse03_cbo
        '
        Me.Infusions_TitratedMedicationsResponse03_cbo.Location = New System.Drawing.Point(741, 105)
        Me.Infusions_TitratedMedicationsResponse03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse03_cbo.Name = "Infusions_TitratedMedicationsResponse03_cbo"
        Me.Infusions_TitratedMedicationsResponse03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsResponse03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsResponse03_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusions_TitratedMedicationsResponse03_cbo.TabIndex = 23
        '
        'Infusions_TitratedMedicationsResponse02_cbo
        '
        Me.Infusions_TitratedMedicationsResponse02_cbo.Location = New System.Drawing.Point(741, 77)
        Me.Infusions_TitratedMedicationsResponse02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse02_cbo.Name = "Infusions_TitratedMedicationsResponse02_cbo"
        Me.Infusions_TitratedMedicationsResponse02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsResponse02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsResponse02_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusions_TitratedMedicationsResponse02_cbo.TabIndex = 15
        '
        'Infusions_TitratedMedicationsResponse01_cbo
        '
        Me.Infusions_TitratedMedicationsResponse01_cbo.Location = New System.Drawing.Point(741, 49)
        Me.Infusions_TitratedMedicationsResponse01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsResponse01_cbo.Name = "Infusions_TitratedMedicationsResponse01_cbo"
        Me.Infusions_TitratedMedicationsResponse01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsResponse01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsResponse01_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusions_TitratedMedicationsResponse01_cbo.TabIndex = 7
        '
        'Infusions_TitratedMedicationsSite05_cbo
        '
        Me.Infusions_TitratedMedicationsSite05_cbo.Location = New System.Drawing.Point(488, 160)
        Me.Infusions_TitratedMedicationsSite05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite05_cbo.Name = "Infusions_TitratedMedicationsSite05_cbo"
        Me.Infusions_TitratedMedicationsSite05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsSite05_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusions_TitratedMedicationsSite05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsSite05_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusions_TitratedMedicationsSite05_cbo.TabIndex = 37
        '
        'Infusions_TitratedMedicationsSite04_cbo
        '
        Me.Infusions_TitratedMedicationsSite04_cbo.Location = New System.Drawing.Point(488, 133)
        Me.Infusions_TitratedMedicationsSite04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite04_cbo.Name = "Infusions_TitratedMedicationsSite04_cbo"
        Me.Infusions_TitratedMedicationsSite04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsSite04_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusions_TitratedMedicationsSite04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsSite04_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusions_TitratedMedicationsSite04_cbo.TabIndex = 29
        '
        'Infusions_TitratedMedicationsSite03_cbo
        '
        Me.Infusions_TitratedMedicationsSite03_cbo.Location = New System.Drawing.Point(488, 105)
        Me.Infusions_TitratedMedicationsSite03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite03_cbo.Name = "Infusions_TitratedMedicationsSite03_cbo"
        Me.Infusions_TitratedMedicationsSite03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsSite03_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusions_TitratedMedicationsSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsSite03_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusions_TitratedMedicationsSite03_cbo.TabIndex = 21
        '
        'Infusions_TitratedMedicationsSite02_cbo
        '
        Me.Infusions_TitratedMedicationsSite02_cbo.Location = New System.Drawing.Point(488, 77)
        Me.Infusions_TitratedMedicationsSite02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite02_cbo.Name = "Infusions_TitratedMedicationsSite02_cbo"
        Me.Infusions_TitratedMedicationsSite02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsSite02_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusions_TitratedMedicationsSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsSite02_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusions_TitratedMedicationsSite02_cbo.TabIndex = 13
        '
        'Infusions_TitratedMedicationsSite01_cbo
        '
        Me.Infusions_TitratedMedicationsSite01_cbo.Location = New System.Drawing.Point(488, 49)
        Me.Infusions_TitratedMedicationsSite01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_TitratedMedicationsSite01_cbo.Name = "Infusions_TitratedMedicationsSite01_cbo"
        Me.Infusions_TitratedMedicationsSite01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusions_TitratedMedicationsSite01_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusions_TitratedMedicationsSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusions_TitratedMedicationsSite01_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusions_TitratedMedicationsSite01_cbo.TabIndex = 5
        '
        'Infusions_Hydrations_grp
        '
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_Hydrations05_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_Hydrations04_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_Hydrations03_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_Hydrations02_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_Hydrations01_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins05_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins04_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins03_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins02_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins01_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsMins_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime04_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate04_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime03_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate03_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime05_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime02_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndTime01_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate05_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate02_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsEndDate01_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate_lbl)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime04_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate04_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime03_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate03_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime05_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime02_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartTime01_txt)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate05_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate02_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsStartDate01_dte)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse05_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse04_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse03_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse02_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsResponse01_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate05_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate04_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate03_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate02_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsRate01_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite05_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite04_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite03_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite02_cbo)
        Me.Infusions_Hydrations_grp.Controls.Add(Me.Infusion_HydrationsSite01_cbo)
        Me.Infusions_Hydrations_grp.Location = New System.Drawing.Point(14, 264)
        Me.Infusions_Hydrations_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_Hydrations_grp.Name = "Infusions_Hydrations_grp"
        Me.Infusions_Hydrations_grp.Size = New System.Drawing.Size(905, 196)
        Me.Infusions_Hydrations_grp.TabIndex = 19
        Me.Infusions_Hydrations_grp.Text = "Hydrations"
        '
        'Infusion_Hydrations05_lbl
        '
        Me.Infusion_Hydrations05_lbl.Location = New System.Drawing.Point(21, 167)
        Me.Infusion_Hydrations05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Hydrations05_lbl.Name = "Infusion_Hydrations05_lbl"
        Me.Infusion_Hydrations05_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_Hydrations05_lbl.TabIndex = 306
        Me.Infusion_Hydrations05_lbl.Text = "5)"
        '
        'Infusion_Hydrations04_lbl
        '
        Me.Infusion_Hydrations04_lbl.Location = New System.Drawing.Point(21, 140)
        Me.Infusion_Hydrations04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Hydrations04_lbl.Name = "Infusion_Hydrations04_lbl"
        Me.Infusion_Hydrations04_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_Hydrations04_lbl.TabIndex = 305
        Me.Infusion_Hydrations04_lbl.Text = "4)"
        '
        'Infusion_Hydrations03_lbl
        '
        Me.Infusion_Hydrations03_lbl.Location = New System.Drawing.Point(21, 112)
        Me.Infusion_Hydrations03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Hydrations03_lbl.Name = "Infusion_Hydrations03_lbl"
        Me.Infusion_Hydrations03_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_Hydrations03_lbl.TabIndex = 304
        Me.Infusion_Hydrations03_lbl.Text = "3)"
        '
        'Infusion_Hydrations02_lbl
        '
        Me.Infusion_Hydrations02_lbl.Location = New System.Drawing.Point(21, 84)
        Me.Infusion_Hydrations02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Hydrations02_lbl.Name = "Infusion_Hydrations02_lbl"
        Me.Infusion_Hydrations02_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_Hydrations02_lbl.TabIndex = 303
        Me.Infusion_Hydrations02_lbl.Text = "2)"
        '
        'Infusion_Hydrations01_lbl
        '
        Me.Infusion_Hydrations01_lbl.Location = New System.Drawing.Point(21, 57)
        Me.Infusion_Hydrations01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_Hydrations01_lbl.Name = "Infusion_Hydrations01_lbl"
        Me.Infusion_Hydrations01_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_Hydrations01_lbl.TabIndex = 302
        Me.Infusion_Hydrations01_lbl.Text = "1)"
        '
        'Infusion_HydrationsRate_lbl
        '
        Me.Infusion_HydrationsRate_lbl.Location = New System.Drawing.Point(608, 29)
        Me.Infusion_HydrationsRate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate_lbl.Name = "Infusion_HydrationsRate_lbl"
        Me.Infusion_HydrationsRate_lbl.Size = New System.Drawing.Size(30, 18)
        Me.Infusion_HydrationsRate_lbl.TabIndex = 301
        Me.Infusion_HydrationsRate_lbl.Text = "Rate"
        '
        'Infusion_HydrationsResponse_lbl
        '
        Me.Infusion_HydrationsResponse_lbl.Location = New System.Drawing.Point(717, 29)
        Me.Infusion_HydrationsResponse_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse_lbl.Name = "Infusion_HydrationsResponse_lbl"
        Me.Infusion_HydrationsResponse_lbl.Size = New System.Drawing.Size(63, 18)
        Me.Infusion_HydrationsResponse_lbl.TabIndex = 300
        Me.Infusion_HydrationsResponse_lbl.Text = "Response"
        '
        'Infusion_HydrationsSite_lbl
        '
        Me.Infusion_HydrationsSite_lbl.Location = New System.Drawing.Point(515, 29)
        Me.Infusion_HydrationsSite_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite_lbl.Name = "Infusion_HydrationsSite_lbl"
        Me.Infusion_HydrationsSite_lbl.Size = New System.Drawing.Size(23, 18)
        Me.Infusion_HydrationsSite_lbl.TabIndex = 299
        Me.Infusion_HydrationsSite_lbl.Text = "Site"
        '
        'Infusion_HydrationsMins05_lbl
        '
        Me.Infusion_HydrationsMins05_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_HydrationsMins05_lbl.Appearance.Options.UseFont = True
        Me.Infusion_HydrationsMins05_lbl.Location = New System.Drawing.Point(440, 166)
        Me.Infusion_HydrationsMins05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins05_lbl.Name = "Infusion_HydrationsMins05_lbl"
        Me.Infusion_HydrationsMins05_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_HydrationsMins05_lbl.TabIndex = 36
        Me.Infusion_HydrationsMins05_lbl.Text = "9999"
        '
        'Infusion_HydrationsMins04_lbl
        '
        Me.Infusion_HydrationsMins04_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_HydrationsMins04_lbl.Appearance.Options.UseFont = True
        Me.Infusion_HydrationsMins04_lbl.Location = New System.Drawing.Point(440, 138)
        Me.Infusion_HydrationsMins04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins04_lbl.Name = "Infusion_HydrationsMins04_lbl"
        Me.Infusion_HydrationsMins04_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_HydrationsMins04_lbl.TabIndex = 28
        Me.Infusion_HydrationsMins04_lbl.Text = "9999"
        '
        'Infusion_HydrationsMins03_lbl
        '
        Me.Infusion_HydrationsMins03_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_HydrationsMins03_lbl.Appearance.Options.UseFont = True
        Me.Infusion_HydrationsMins03_lbl.Location = New System.Drawing.Point(440, 111)
        Me.Infusion_HydrationsMins03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins03_lbl.Name = "Infusion_HydrationsMins03_lbl"
        Me.Infusion_HydrationsMins03_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_HydrationsMins03_lbl.TabIndex = 20
        Me.Infusion_HydrationsMins03_lbl.Text = "9999"
        '
        'Infusion_HydrationsMins02_lbl
        '
        Me.Infusion_HydrationsMins02_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_HydrationsMins02_lbl.Appearance.Options.UseFont = True
        Me.Infusion_HydrationsMins02_lbl.Location = New System.Drawing.Point(440, 83)
        Me.Infusion_HydrationsMins02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins02_lbl.Name = "Infusion_HydrationsMins02_lbl"
        Me.Infusion_HydrationsMins02_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_HydrationsMins02_lbl.TabIndex = 12
        Me.Infusion_HydrationsMins02_lbl.Text = "9999"
        '
        'Infusion_HydrationsMins01_lbl
        '
        Me.Infusion_HydrationsMins01_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_HydrationsMins01_lbl.Appearance.Options.UseFont = True
        Me.Infusion_HydrationsMins01_lbl.Location = New System.Drawing.Point(440, 55)
        Me.Infusion_HydrationsMins01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins01_lbl.Name = "Infusion_HydrationsMins01_lbl"
        Me.Infusion_HydrationsMins01_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_HydrationsMins01_lbl.TabIndex = 4
        Me.Infusion_HydrationsMins01_lbl.Text = "9999"
        '
        'Infusion_HydrationsMins_lbl
        '
        Me.Infusion_HydrationsMins_lbl.Location = New System.Drawing.Point(443, 29)
        Me.Infusion_HydrationsMins_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsMins_lbl.Name = "Infusion_HydrationsMins_lbl"
        Me.Infusion_HydrationsMins_lbl.Size = New System.Drawing.Size(29, 18)
        Me.Infusion_HydrationsMins_lbl.TabIndex = 293
        Me.Infusion_HydrationsMins_lbl.Text = "Mins"
        '
        'Infusion_HydrationsEndTime_lbl
        '
        Me.Infusion_HydrationsEndTime_lbl.Location = New System.Drawing.Point(355, 29)
        Me.Infusion_HydrationsEndTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime_lbl.Name = "Infusion_HydrationsEndTime_lbl"
        Me.Infusion_HydrationsEndTime_lbl.Size = New System.Drawing.Size(62, 18)
        Me.Infusion_HydrationsEndTime_lbl.TabIndex = 292
        Me.Infusion_HydrationsEndTime_lbl.Text = "End Time"
        '
        'Infusion_HydrationsEndDate_lbl
        '
        Me.Infusion_HydrationsEndDate_lbl.Location = New System.Drawing.Point(257, 29)
        Me.Infusion_HydrationsEndDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate_lbl.Name = "Infusion_HydrationsEndDate_lbl"
        Me.Infusion_HydrationsEndDate_lbl.Size = New System.Drawing.Size(60, 18)
        Me.Infusion_HydrationsEndDate_lbl.TabIndex = 291
        Me.Infusion_HydrationsEndDate_lbl.Text = "End Date"
        '
        'Infusion_HydrationsEndTime04_txt
        '
        Me.Infusion_HydrationsEndTime04_txt.Location = New System.Drawing.Point(357, 134)
        Me.Infusion_HydrationsEndTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime04_txt.Name = "Infusion_HydrationsEndTime04_txt"
        Me.Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsEndTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsEndTime04_txt.TabIndex = 27
        '
        'Infusion_HydrationsEndDate04_dte
        '
        Me.Infusion_HydrationsEndDate04_dte.EditValue = Nothing
        Me.Infusion_HydrationsEndDate04_dte.Location = New System.Drawing.Point(241, 134)
        Me.Infusion_HydrationsEndDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate04_dte.Name = "Infusion_HydrationsEndDate04_dte"
        Me.Infusion_HydrationsEndDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsEndDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsEndDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsEndDate04_dte.TabIndex = 26
        '
        'Infusion_HydrationsEndTime03_txt
        '
        Me.Infusion_HydrationsEndTime03_txt.Location = New System.Drawing.Point(357, 106)
        Me.Infusion_HydrationsEndTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime03_txt.Name = "Infusion_HydrationsEndTime03_txt"
        Me.Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsEndTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsEndTime03_txt.TabIndex = 19
        '
        'Infusion_HydrationsEndDate03_dte
        '
        Me.Infusion_HydrationsEndDate03_dte.EditValue = Nothing
        Me.Infusion_HydrationsEndDate03_dte.Location = New System.Drawing.Point(241, 106)
        Me.Infusion_HydrationsEndDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate03_dte.Name = "Infusion_HydrationsEndDate03_dte"
        Me.Infusion_HydrationsEndDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsEndDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsEndDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsEndDate03_dte.TabIndex = 18
        '
        'Infusion_HydrationsEndTime05_txt
        '
        Me.Infusion_HydrationsEndTime05_txt.Location = New System.Drawing.Point(357, 162)
        Me.Infusion_HydrationsEndTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime05_txt.Name = "Infusion_HydrationsEndTime05_txt"
        Me.Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsEndTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsEndTime05_txt.TabIndex = 35
        '
        'Infusion_HydrationsEndTime02_txt
        '
        Me.Infusion_HydrationsEndTime02_txt.Location = New System.Drawing.Point(357, 79)
        Me.Infusion_HydrationsEndTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime02_txt.Name = "Infusion_HydrationsEndTime02_txt"
        Me.Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsEndTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsEndTime02_txt.TabIndex = 11
        '
        'Infusion_HydrationsEndTime01_txt
        '
        Me.Infusion_HydrationsEndTime01_txt.Location = New System.Drawing.Point(357, 51)
        Me.Infusion_HydrationsEndTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndTime01_txt.Name = "Infusion_HydrationsEndTime01_txt"
        Me.Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsEndTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsEndTime01_txt.TabIndex = 3
        '
        'Infusion_HydrationsEndDate05_dte
        '
        Me.Infusion_HydrationsEndDate05_dte.EditValue = Nothing
        Me.Infusion_HydrationsEndDate05_dte.Location = New System.Drawing.Point(241, 162)
        Me.Infusion_HydrationsEndDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate05_dte.Name = "Infusion_HydrationsEndDate05_dte"
        Me.Infusion_HydrationsEndDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsEndDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsEndDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsEndDate05_dte.TabIndex = 34
        '
        'Infusion_HydrationsEndDate02_dte
        '
        Me.Infusion_HydrationsEndDate02_dte.EditValue = Nothing
        Me.Infusion_HydrationsEndDate02_dte.Location = New System.Drawing.Point(241, 79)
        Me.Infusion_HydrationsEndDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate02_dte.Name = "Infusion_HydrationsEndDate02_dte"
        Me.Infusion_HydrationsEndDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsEndDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsEndDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsEndDate02_dte.TabIndex = 10
        '
        'Infusion_HydrationsEndDate01_dte
        '
        Me.Infusion_HydrationsEndDate01_dte.EditValue = Nothing
        Me.Infusion_HydrationsEndDate01_dte.Location = New System.Drawing.Point(241, 51)
        Me.Infusion_HydrationsEndDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsEndDate01_dte.Name = "Infusion_HydrationsEndDate01_dte"
        Me.Infusion_HydrationsEndDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsEndDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsEndDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusion_HydrationsEndDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusion_HydrationsEndDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsEndDate01_dte.TabIndex = 2
        Me.Infusion_HydrationsEndDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusion_HydrationsStartTime_lbl
        '
        Me.Infusion_HydrationsStartTime_lbl.Location = New System.Drawing.Point(160, 29)
        Me.Infusion_HydrationsStartTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime_lbl.Name = "Infusion_HydrationsStartTime_lbl"
        Me.Infusion_HydrationsStartTime_lbl.Size = New System.Drawing.Size(69, 18)
        Me.Infusion_HydrationsStartTime_lbl.TabIndex = 280
        Me.Infusion_HydrationsStartTime_lbl.Text = "Start Time"
        '
        'Infusion_HydrationsStartDate_lbl
        '
        Me.Infusion_HydrationsStartDate_lbl.Location = New System.Drawing.Point(63, 29)
        Me.Infusion_HydrationsStartDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate_lbl.Name = "Infusion_HydrationsStartDate_lbl"
        Me.Infusion_HydrationsStartDate_lbl.Size = New System.Drawing.Size(67, 18)
        Me.Infusion_HydrationsStartDate_lbl.TabIndex = 279
        Me.Infusion_HydrationsStartDate_lbl.Text = "Start Date"
        '
        'Infusion_HydrationsStartTime04_txt
        '
        Me.Infusion_HydrationsStartTime04_txt.Location = New System.Drawing.Point(163, 134)
        Me.Infusion_HydrationsStartTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime04_txt.Name = "Infusion_HydrationsStartTime04_txt"
        Me.Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsStartTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsStartTime04_txt.TabIndex = 25
        '
        'Infusion_HydrationsStartDate04_dte
        '
        Me.Infusion_HydrationsStartDate04_dte.EditValue = Nothing
        Me.Infusion_HydrationsStartDate04_dte.Location = New System.Drawing.Point(47, 134)
        Me.Infusion_HydrationsStartDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate04_dte.Name = "Infusion_HydrationsStartDate04_dte"
        Me.Infusion_HydrationsStartDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsStartDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsStartDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsStartDate04_dte.TabIndex = 24
        '
        'Infusion_HydrationsStartTime03_txt
        '
        Me.Infusion_HydrationsStartTime03_txt.Location = New System.Drawing.Point(163, 106)
        Me.Infusion_HydrationsStartTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime03_txt.Name = "Infusion_HydrationsStartTime03_txt"
        Me.Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsStartTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsStartTime03_txt.TabIndex = 17
        '
        'Infusion_HydrationsStartDate03_dte
        '
        Me.Infusion_HydrationsStartDate03_dte.EditValue = Nothing
        Me.Infusion_HydrationsStartDate03_dte.Location = New System.Drawing.Point(47, 106)
        Me.Infusion_HydrationsStartDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate03_dte.Name = "Infusion_HydrationsStartDate03_dte"
        Me.Infusion_HydrationsStartDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsStartDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsStartDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsStartDate03_dte.TabIndex = 16
        '
        'Infusion_HydrationsStartTime05_txt
        '
        Me.Infusion_HydrationsStartTime05_txt.Location = New System.Drawing.Point(163, 162)
        Me.Infusion_HydrationsStartTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime05_txt.Name = "Infusion_HydrationsStartTime05_txt"
        Me.Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsStartTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsStartTime05_txt.TabIndex = 33
        '
        'Infusion_HydrationsStartTime02_txt
        '
        Me.Infusion_HydrationsStartTime02_txt.Location = New System.Drawing.Point(163, 79)
        Me.Infusion_HydrationsStartTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime02_txt.Name = "Infusion_HydrationsStartTime02_txt"
        Me.Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsStartTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsStartTime02_txt.TabIndex = 9
        '
        'Infusion_HydrationsStartTime01_txt
        '
        Me.Infusion_HydrationsStartTime01_txt.Location = New System.Drawing.Point(163, 51)
        Me.Infusion_HydrationsStartTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartTime01_txt.Name = "Infusion_HydrationsStartTime01_txt"
        Me.Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_HydrationsStartTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_HydrationsStartTime01_txt.TabIndex = 1
        '
        'Infusion_HydrationsStartDate05_dte
        '
        Me.Infusion_HydrationsStartDate05_dte.EditValue = Nothing
        Me.Infusion_HydrationsStartDate05_dte.Location = New System.Drawing.Point(47, 162)
        Me.Infusion_HydrationsStartDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate05_dte.Name = "Infusion_HydrationsStartDate05_dte"
        Me.Infusion_HydrationsStartDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsStartDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsStartDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsStartDate05_dte.TabIndex = 32
        '
        'Infusion_HydrationsStartDate02_dte
        '
        Me.Infusion_HydrationsStartDate02_dte.EditValue = Nothing
        Me.Infusion_HydrationsStartDate02_dte.Location = New System.Drawing.Point(47, 79)
        Me.Infusion_HydrationsStartDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate02_dte.Name = "Infusion_HydrationsStartDate02_dte"
        Me.Infusion_HydrationsStartDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsStartDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsStartDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsStartDate02_dte.TabIndex = 8
        '
        'Infusion_HydrationsStartDate01_dte
        '
        Me.Infusion_HydrationsStartDate01_dte.EditValue = Nothing
        Me.Infusion_HydrationsStartDate01_dte.Location = New System.Drawing.Point(47, 51)
        Me.Infusion_HydrationsStartDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsStartDate01_dte.Name = "Infusion_HydrationsStartDate01_dte"
        Me.Infusion_HydrationsStartDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_HydrationsStartDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_HydrationsStartDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusion_HydrationsStartDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusion_HydrationsStartDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_HydrationsStartDate01_dte.TabIndex = 0
        Me.Infusion_HydrationsStartDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusion_HydrationsResponse05_cbo
        '
        Me.Infusion_HydrationsResponse05_cbo.Location = New System.Drawing.Point(691, 162)
        Me.Infusion_HydrationsResponse05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse05_cbo.Name = "Infusion_HydrationsResponse05_cbo"
        Me.Infusion_HydrationsResponse05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsResponse05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsResponse05_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_HydrationsResponse05_cbo.TabIndex = 39
        '
        'Infusion_HydrationsResponse04_cbo
        '
        Me.Infusion_HydrationsResponse04_cbo.Location = New System.Drawing.Point(691, 134)
        Me.Infusion_HydrationsResponse04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse04_cbo.Name = "Infusion_HydrationsResponse04_cbo"
        Me.Infusion_HydrationsResponse04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsResponse04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsResponse04_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_HydrationsResponse04_cbo.TabIndex = 31
        '
        'Infusion_HydrationsResponse03_cbo
        '
        Me.Infusion_HydrationsResponse03_cbo.Location = New System.Drawing.Point(691, 106)
        Me.Infusion_HydrationsResponse03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse03_cbo.Name = "Infusion_HydrationsResponse03_cbo"
        Me.Infusion_HydrationsResponse03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsResponse03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsResponse03_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_HydrationsResponse03_cbo.TabIndex = 23
        '
        'Infusion_HydrationsResponse02_cbo
        '
        Me.Infusion_HydrationsResponse02_cbo.Location = New System.Drawing.Point(691, 79)
        Me.Infusion_HydrationsResponse02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse02_cbo.Name = "Infusion_HydrationsResponse02_cbo"
        Me.Infusion_HydrationsResponse02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsResponse02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsResponse02_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_HydrationsResponse02_cbo.TabIndex = 15
        '
        'Infusion_HydrationsResponse01_cbo
        '
        Me.Infusion_HydrationsResponse01_cbo.Location = New System.Drawing.Point(691, 51)
        Me.Infusion_HydrationsResponse01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsResponse01_cbo.Name = "Infusion_HydrationsResponse01_cbo"
        Me.Infusion_HydrationsResponse01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsResponse01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsResponse01_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_HydrationsResponse01_cbo.TabIndex = 7
        '
        'Infusion_HydrationsRate05_cbo
        '
        Me.Infusion_HydrationsRate05_cbo.Location = New System.Drawing.Point(588, 162)
        Me.Infusion_HydrationsRate05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate05_cbo.Name = "Infusion_HydrationsRate05_cbo"
        Me.Infusion_HydrationsRate05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsRate05_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Me.Infusion_HydrationsRate05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsRate05_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsRate05_cbo.TabIndex = 38
        '
        'Infusion_HydrationsRate04_cbo
        '
        Me.Infusion_HydrationsRate04_cbo.Location = New System.Drawing.Point(588, 134)
        Me.Infusion_HydrationsRate04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate04_cbo.Name = "Infusion_HydrationsRate04_cbo"
        Me.Infusion_HydrationsRate04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsRate04_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Me.Infusion_HydrationsRate04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsRate04_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsRate04_cbo.TabIndex = 30
        '
        'Infusion_HydrationsRate03_cbo
        '
        Me.Infusion_HydrationsRate03_cbo.Location = New System.Drawing.Point(588, 106)
        Me.Infusion_HydrationsRate03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate03_cbo.Name = "Infusion_HydrationsRate03_cbo"
        Me.Infusion_HydrationsRate03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsRate03_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Me.Infusion_HydrationsRate03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsRate03_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsRate03_cbo.TabIndex = 22
        '
        'Infusion_HydrationsRate02_cbo
        '
        Me.Infusion_HydrationsRate02_cbo.Location = New System.Drawing.Point(588, 79)
        Me.Infusion_HydrationsRate02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate02_cbo.Name = "Infusion_HydrationsRate02_cbo"
        Me.Infusion_HydrationsRate02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsRate02_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Me.Infusion_HydrationsRate02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsRate02_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsRate02_cbo.TabIndex = 14
        '
        'Infusion_HydrationsRate01_cbo
        '
        Me.Infusion_HydrationsRate01_cbo.Location = New System.Drawing.Point(588, 51)
        Me.Infusion_HydrationsRate01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsRate01_cbo.Name = "Infusion_HydrationsRate01_cbo"
        Me.Infusion_HydrationsRate01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsRate01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsRate01_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsRate01_cbo.TabIndex = 6
        '
        'Infusion_HydrationsSite05_cbo
        '
        Me.Infusion_HydrationsSite05_cbo.Location = New System.Drawing.Point(489, 162)
        Me.Infusion_HydrationsSite05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite05_cbo.Name = "Infusion_HydrationsSite05_cbo"
        Me.Infusion_HydrationsSite05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsSite05_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_HydrationsSite05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsSite05_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsSite05_cbo.TabIndex = 37
        '
        'Infusion_HydrationsSite04_cbo
        '
        Me.Infusion_HydrationsSite04_cbo.Location = New System.Drawing.Point(489, 134)
        Me.Infusion_HydrationsSite04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite04_cbo.Name = "Infusion_HydrationsSite04_cbo"
        Me.Infusion_HydrationsSite04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsSite04_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_HydrationsSite04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsSite04_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsSite04_cbo.TabIndex = 29
        '
        'Infusion_HydrationsSite03_cbo
        '
        Me.Infusion_HydrationsSite03_cbo.Location = New System.Drawing.Point(489, 106)
        Me.Infusion_HydrationsSite03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite03_cbo.Name = "Infusion_HydrationsSite03_cbo"
        Me.Infusion_HydrationsSite03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsSite03_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_HydrationsSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsSite03_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsSite03_cbo.TabIndex = 21
        '
        'Infusion_HydrationsSite02_cbo
        '
        Me.Infusion_HydrationsSite02_cbo.Location = New System.Drawing.Point(489, 79)
        Me.Infusion_HydrationsSite02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite02_cbo.Name = "Infusion_HydrationsSite02_cbo"
        Me.Infusion_HydrationsSite02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsSite02_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_HydrationsSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsSite02_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsSite02_cbo.TabIndex = 13
        '
        'Infusion_HydrationsSite01_cbo
        '
        Me.Infusion_HydrationsSite01_cbo.Location = New System.Drawing.Point(489, 51)
        Me.Infusion_HydrationsSite01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_HydrationsSite01_cbo.Name = "Infusion_HydrationsSite01_cbo"
        Me.Infusion_HydrationsSite01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_HydrationsSite01_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_HydrationsSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_HydrationsSite01_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_HydrationsSite01_cbo.TabIndex = 5
        '
        'Infusions_InfusedMedications_grp
        '
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication07_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication06_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication05_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication04_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication03_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication02_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication01_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMedication_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications07_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications06_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications05_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications04_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications03_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications02_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedications01_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins07_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins06_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins05_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins04_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins03_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins02_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins01_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsMins_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime04_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate04_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime03_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate03_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime07_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime06_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime05_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime02_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndTime01_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate07_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate06_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate05_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate02_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsEndDate01_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate_lbl)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime04_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate04_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime03_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate03_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime07_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime06_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime05_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime02_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartTime01_txt)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate07_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate06_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate05_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate02_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsStartDate01_dte)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse07_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite07_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse06_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse05_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse04_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse03_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse02_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsResponse01_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite06_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite05_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite04_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite03_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite02_cbo)
        Me.Infusions_InfusedMedications_grp.Controls.Add(Me.Infusion_InfusedMedicationsSite01_cbo)
        Me.Infusions_InfusedMedications_grp.Location = New System.Drawing.Point(13, 13)
        Me.Infusions_InfusedMedications_grp.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusions_InfusedMedications_grp.Name = "Infusions_InfusedMedications_grp"
        Me.Infusions_InfusedMedications_grp.Size = New System.Drawing.Size(905, 250)
        Me.Infusions_InfusedMedications_grp.TabIndex = 18
        Me.Infusions_InfusedMedications_grp.Text = "Infused Medications"
        '
        'Infusion_InfusedMedicationsMedication07_cbo
        '
        Me.Infusion_InfusedMedicationsMedication07_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication07_cbo.Location = New System.Drawing.Point(579, 217)
        Me.Infusion_InfusedMedicationsMedication07_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication07_cbo.Name = "Infusion_InfusedMedicationsMedication07_cbo"
        Me.Infusion_InfusedMedicationsMedication07_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication07_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication07_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication07_cbo.Properties.PopupView = Me.GridView19
        Me.Infusion_InfusedMedicationsMedication07_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication07_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication07_cbo.TabIndex = 48
        '
        'GridView19
        '
        Me.GridView19.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn96, Me.GridColumn97, Me.GridColumn98, Me.GridColumn99, Me.GridColumn100})
        Me.GridView19.DetailHeight = 485
        Me.GridView19.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView19.Name = "GridView19"
        Me.GridView19.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView19.OptionsView.ShowGroupPanel = False
        '
        'GridColumn96
        '
        Me.GridColumn96.FieldName = "MedName"
        Me.GridColumn96.MinWidth = 27
        Me.GridColumn96.Name = "GridColumn96"
        Me.GridColumn96.Visible = True
        Me.GridColumn96.VisibleIndex = 0
        Me.GridColumn96.Width = 1104
        '
        'GridColumn97
        '
        Me.GridColumn97.FieldName = "CC"
        Me.GridColumn97.MinWidth = 27
        Me.GridColumn97.Name = "GridColumn97"
        Me.GridColumn97.Visible = True
        Me.GridColumn97.VisibleIndex = 1
        Me.GridColumn97.Width = 116
        '
        'GridColumn98
        '
        Me.GridColumn98.Caption = "Dedicated Line"
        Me.GridColumn98.FieldName = "DedicatedLine"
        Me.GridColumn98.MinWidth = 27
        Me.GridColumn98.Name = "GridColumn98"
        Me.GridColumn98.Visible = True
        Me.GridColumn98.VisibleIndex = 2
        Me.GridColumn98.Width = 139
        '
        'GridColumn99
        '
        Me.GridColumn99.Caption = "Chemo"
        Me.GridColumn99.FieldName = "Chemo"
        Me.GridColumn99.MinWidth = 27
        Me.GridColumn99.Name = "GridColumn99"
        Me.GridColumn99.Visible = True
        Me.GridColumn99.VisibleIndex = 3
        Me.GridColumn99.Width = 111
        '
        'GridColumn100
        '
        Me.GridColumn100.Caption = "Hormonal"
        Me.GridColumn100.FieldName = "Hormonal"
        Me.GridColumn100.MinWidth = 27
        Me.GridColumn100.Name = "GridColumn100"
        Me.GridColumn100.Visible = True
        Me.GridColumn100.VisibleIndex = 4
        Me.GridColumn100.Width = 123
        '
        'Infusion_InfusedMedicationsMedication06_cbo
        '
        Me.Infusion_InfusedMedicationsMedication06_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication06_cbo.Location = New System.Drawing.Point(579, 190)
        Me.Infusion_InfusedMedicationsMedication06_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication06_cbo.Name = "Infusion_InfusedMedicationsMedication06_cbo"
        Me.Infusion_InfusedMedicationsMedication06_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication06_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication06_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication06_cbo.Properties.PopupView = Me.GridView20
        Me.Infusion_InfusedMedicationsMedication06_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication06_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication06_cbo.TabIndex = 41
        '
        'GridView20
        '
        Me.GridView20.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn101, Me.GridColumn102, Me.GridColumn103, Me.GridColumn104, Me.GridColumn105})
        Me.GridView20.DetailHeight = 485
        Me.GridView20.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView20.Name = "GridView20"
        Me.GridView20.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView20.OptionsView.ShowGroupPanel = False
        '
        'GridColumn101
        '
        Me.GridColumn101.FieldName = "MedName"
        Me.GridColumn101.MinWidth = 27
        Me.GridColumn101.Name = "GridColumn101"
        Me.GridColumn101.Visible = True
        Me.GridColumn101.VisibleIndex = 0
        Me.GridColumn101.Width = 1104
        '
        'GridColumn102
        '
        Me.GridColumn102.FieldName = "CC"
        Me.GridColumn102.MinWidth = 27
        Me.GridColumn102.Name = "GridColumn102"
        Me.GridColumn102.Visible = True
        Me.GridColumn102.VisibleIndex = 1
        Me.GridColumn102.Width = 116
        '
        'GridColumn103
        '
        Me.GridColumn103.Caption = "Dedicated Line"
        Me.GridColumn103.FieldName = "DedicatedLine"
        Me.GridColumn103.MinWidth = 27
        Me.GridColumn103.Name = "GridColumn103"
        Me.GridColumn103.Visible = True
        Me.GridColumn103.VisibleIndex = 2
        Me.GridColumn103.Width = 139
        '
        'GridColumn104
        '
        Me.GridColumn104.Caption = "Chemo"
        Me.GridColumn104.FieldName = "Chemo"
        Me.GridColumn104.MinWidth = 27
        Me.GridColumn104.Name = "GridColumn104"
        Me.GridColumn104.Visible = True
        Me.GridColumn104.VisibleIndex = 3
        Me.GridColumn104.Width = 111
        '
        'GridColumn105
        '
        Me.GridColumn105.Caption = "Hormonal"
        Me.GridColumn105.FieldName = "Hormonal"
        Me.GridColumn105.MinWidth = 27
        Me.GridColumn105.Name = "GridColumn105"
        Me.GridColumn105.Visible = True
        Me.GridColumn105.VisibleIndex = 4
        Me.GridColumn105.Width = 123
        '
        'Infusion_InfusedMedicationsMedication05_cbo
        '
        Me.Infusion_InfusedMedicationsMedication05_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication05_cbo.Location = New System.Drawing.Point(579, 162)
        Me.Infusion_InfusedMedicationsMedication05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication05_cbo.Name = "Infusion_InfusedMedicationsMedication05_cbo"
        Me.Infusion_InfusedMedicationsMedication05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication05_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication05_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication05_cbo.Properties.PopupView = Me.GridView21
        Me.Infusion_InfusedMedicationsMedication05_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication05_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication05_cbo.TabIndex = 34
        '
        'GridView21
        '
        Me.GridView21.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn106, Me.GridColumn107, Me.GridColumn108, Me.GridColumn109, Me.GridColumn110})
        Me.GridView21.DetailHeight = 485
        Me.GridView21.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView21.Name = "GridView21"
        Me.GridView21.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView21.OptionsView.ShowGroupPanel = False
        '
        'GridColumn106
        '
        Me.GridColumn106.FieldName = "MedName"
        Me.GridColumn106.MinWidth = 27
        Me.GridColumn106.Name = "GridColumn106"
        Me.GridColumn106.Visible = True
        Me.GridColumn106.VisibleIndex = 0
        Me.GridColumn106.Width = 1104
        '
        'GridColumn107
        '
        Me.GridColumn107.FieldName = "CC"
        Me.GridColumn107.MinWidth = 27
        Me.GridColumn107.Name = "GridColumn107"
        Me.GridColumn107.Visible = True
        Me.GridColumn107.VisibleIndex = 1
        Me.GridColumn107.Width = 116
        '
        'GridColumn108
        '
        Me.GridColumn108.Caption = "Dedicated Line"
        Me.GridColumn108.FieldName = "DedicatedLine"
        Me.GridColumn108.MinWidth = 27
        Me.GridColumn108.Name = "GridColumn108"
        Me.GridColumn108.Visible = True
        Me.GridColumn108.VisibleIndex = 2
        Me.GridColumn108.Width = 139
        '
        'GridColumn109
        '
        Me.GridColumn109.Caption = "Chemo"
        Me.GridColumn109.FieldName = "Chemo"
        Me.GridColumn109.MinWidth = 27
        Me.GridColumn109.Name = "GridColumn109"
        Me.GridColumn109.Visible = True
        Me.GridColumn109.VisibleIndex = 3
        Me.GridColumn109.Width = 111
        '
        'GridColumn110
        '
        Me.GridColumn110.Caption = "Hormonal"
        Me.GridColumn110.FieldName = "Hormonal"
        Me.GridColumn110.MinWidth = 27
        Me.GridColumn110.Name = "GridColumn110"
        Me.GridColumn110.Visible = True
        Me.GridColumn110.VisibleIndex = 4
        Me.GridColumn110.Width = 123
        '
        'Infusion_InfusedMedicationsMedication04_cbo
        '
        Me.Infusion_InfusedMedicationsMedication04_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication04_cbo.Location = New System.Drawing.Point(579, 134)
        Me.Infusion_InfusedMedicationsMedication04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication04_cbo.Name = "Infusion_InfusedMedicationsMedication04_cbo"
        Me.Infusion_InfusedMedicationsMedication04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication04_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication04_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication04_cbo.Properties.PopupView = Me.GridView17
        Me.Infusion_InfusedMedicationsMedication04_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication04_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication04_cbo.TabIndex = 27
        '
        'GridView17
        '
        Me.GridView17.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn86, Me.GridColumn87, Me.GridColumn88, Me.GridColumn89, Me.GridColumn90})
        Me.GridView17.DetailHeight = 485
        Me.GridView17.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView17.Name = "GridView17"
        Me.GridView17.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView17.OptionsView.ShowGroupPanel = False
        '
        'GridColumn86
        '
        Me.GridColumn86.FieldName = "MedName"
        Me.GridColumn86.MinWidth = 27
        Me.GridColumn86.Name = "GridColumn86"
        Me.GridColumn86.Visible = True
        Me.GridColumn86.VisibleIndex = 0
        Me.GridColumn86.Width = 1104
        '
        'GridColumn87
        '
        Me.GridColumn87.FieldName = "CC"
        Me.GridColumn87.MinWidth = 27
        Me.GridColumn87.Name = "GridColumn87"
        Me.GridColumn87.Visible = True
        Me.GridColumn87.VisibleIndex = 1
        Me.GridColumn87.Width = 116
        '
        'GridColumn88
        '
        Me.GridColumn88.Caption = "Dedicated Line"
        Me.GridColumn88.FieldName = "DedicatedLine"
        Me.GridColumn88.MinWidth = 27
        Me.GridColumn88.Name = "GridColumn88"
        Me.GridColumn88.Visible = True
        Me.GridColumn88.VisibleIndex = 2
        Me.GridColumn88.Width = 139
        '
        'GridColumn89
        '
        Me.GridColumn89.Caption = "Chemo"
        Me.GridColumn89.FieldName = "Chemo"
        Me.GridColumn89.MinWidth = 27
        Me.GridColumn89.Name = "GridColumn89"
        Me.GridColumn89.Visible = True
        Me.GridColumn89.VisibleIndex = 3
        Me.GridColumn89.Width = 111
        '
        'GridColumn90
        '
        Me.GridColumn90.Caption = "Hormonal"
        Me.GridColumn90.FieldName = "Hormonal"
        Me.GridColumn90.MinWidth = 27
        Me.GridColumn90.Name = "GridColumn90"
        Me.GridColumn90.Visible = True
        Me.GridColumn90.VisibleIndex = 4
        Me.GridColumn90.Width = 123
        '
        'Infusion_InfusedMedicationsMedication03_cbo
        '
        Me.Infusion_InfusedMedicationsMedication03_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication03_cbo.Location = New System.Drawing.Point(579, 107)
        Me.Infusion_InfusedMedicationsMedication03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication03_cbo.Name = "Infusion_InfusedMedicationsMedication03_cbo"
        Me.Infusion_InfusedMedicationsMedication03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication03_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication03_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication03_cbo.Properties.PopupView = Me.GridView18
        Me.Infusion_InfusedMedicationsMedication03_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication03_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication03_cbo.TabIndex = 20
        '
        'GridView18
        '
        Me.GridView18.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn91, Me.GridColumn92, Me.GridColumn93, Me.GridColumn94, Me.GridColumn95})
        Me.GridView18.DetailHeight = 485
        Me.GridView18.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView18.Name = "GridView18"
        Me.GridView18.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView18.OptionsView.ShowGroupPanel = False
        '
        'GridColumn91
        '
        Me.GridColumn91.FieldName = "MedName"
        Me.GridColumn91.MinWidth = 27
        Me.GridColumn91.Name = "GridColumn91"
        Me.GridColumn91.Visible = True
        Me.GridColumn91.VisibleIndex = 0
        Me.GridColumn91.Width = 1104
        '
        'GridColumn92
        '
        Me.GridColumn92.FieldName = "CC"
        Me.GridColumn92.MinWidth = 27
        Me.GridColumn92.Name = "GridColumn92"
        Me.GridColumn92.Visible = True
        Me.GridColumn92.VisibleIndex = 1
        Me.GridColumn92.Width = 116
        '
        'GridColumn93
        '
        Me.GridColumn93.Caption = "Dedicated Line"
        Me.GridColumn93.FieldName = "DedicatedLine"
        Me.GridColumn93.MinWidth = 27
        Me.GridColumn93.Name = "GridColumn93"
        Me.GridColumn93.Visible = True
        Me.GridColumn93.VisibleIndex = 2
        Me.GridColumn93.Width = 139
        '
        'GridColumn94
        '
        Me.GridColumn94.Caption = "Chemo"
        Me.GridColumn94.FieldName = "Chemo"
        Me.GridColumn94.MinWidth = 27
        Me.GridColumn94.Name = "GridColumn94"
        Me.GridColumn94.Visible = True
        Me.GridColumn94.VisibleIndex = 3
        Me.GridColumn94.Width = 111
        '
        'GridColumn95
        '
        Me.GridColumn95.Caption = "Hormonal"
        Me.GridColumn95.FieldName = "Hormonal"
        Me.GridColumn95.MinWidth = 27
        Me.GridColumn95.Name = "GridColumn95"
        Me.GridColumn95.Visible = True
        Me.GridColumn95.VisibleIndex = 4
        Me.GridColumn95.Width = 123
        '
        'Infusion_InfusedMedicationsMedication02_cbo
        '
        Me.Infusion_InfusedMedicationsMedication02_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication02_cbo.Location = New System.Drawing.Point(579, 79)
        Me.Infusion_InfusedMedicationsMedication02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication02_cbo.Name = "Infusion_InfusedMedicationsMedication02_cbo"
        Me.Infusion_InfusedMedicationsMedication02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication02_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication02_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication02_cbo.Properties.PopupView = Me.GridView16
        Me.Infusion_InfusedMedicationsMedication02_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication02_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication02_cbo.TabIndex = 13
        '
        'GridView16
        '
        Me.GridView16.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn81, Me.GridColumn82, Me.GridColumn83, Me.GridColumn84, Me.GridColumn85})
        Me.GridView16.DetailHeight = 485
        Me.GridView16.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView16.Name = "GridView16"
        Me.GridView16.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView16.OptionsView.ShowGroupPanel = False
        '
        'GridColumn81
        '
        Me.GridColumn81.FieldName = "MedName"
        Me.GridColumn81.MinWidth = 27
        Me.GridColumn81.Name = "GridColumn81"
        Me.GridColumn81.Visible = True
        Me.GridColumn81.VisibleIndex = 0
        Me.GridColumn81.Width = 1104
        '
        'GridColumn82
        '
        Me.GridColumn82.FieldName = "CC"
        Me.GridColumn82.MinWidth = 27
        Me.GridColumn82.Name = "GridColumn82"
        Me.GridColumn82.Visible = True
        Me.GridColumn82.VisibleIndex = 1
        Me.GridColumn82.Width = 116
        '
        'GridColumn83
        '
        Me.GridColumn83.Caption = "Dedicated Line"
        Me.GridColumn83.FieldName = "DedicatedLine"
        Me.GridColumn83.MinWidth = 27
        Me.GridColumn83.Name = "GridColumn83"
        Me.GridColumn83.Visible = True
        Me.GridColumn83.VisibleIndex = 2
        Me.GridColumn83.Width = 139
        '
        'GridColumn84
        '
        Me.GridColumn84.Caption = "Chemo"
        Me.GridColumn84.FieldName = "Chemo"
        Me.GridColumn84.MinWidth = 27
        Me.GridColumn84.Name = "GridColumn84"
        Me.GridColumn84.Visible = True
        Me.GridColumn84.VisibleIndex = 3
        Me.GridColumn84.Width = 111
        '
        'GridColumn85
        '
        Me.GridColumn85.Caption = "Hormonal"
        Me.GridColumn85.FieldName = "Hormonal"
        Me.GridColumn85.MinWidth = 27
        Me.GridColumn85.Name = "GridColumn85"
        Me.GridColumn85.Visible = True
        Me.GridColumn85.VisibleIndex = 4
        Me.GridColumn85.Width = 123
        '
        'Infusion_InfusedMedicationsMedication01_cbo
        '
        Me.Infusion_InfusedMedicationsMedication01_cbo.EditValue = ""
        Me.Infusion_InfusedMedicationsMedication01_cbo.Location = New System.Drawing.Point(579, 51)
        Me.Infusion_InfusedMedicationsMedication01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication01_cbo.Name = "Infusion_InfusedMedicationsMedication01_cbo"
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.DisplayMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.ImmediatePopup = True
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.NullText = ""
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.PopupView = Me.GridView15
        Me.Infusion_InfusedMedicationsMedication01_cbo.Properties.ValueMember = "MedName"
        Me.Infusion_InfusedMedicationsMedication01_cbo.Size = New System.Drawing.Size(160, 24)
        Me.Infusion_InfusedMedicationsMedication01_cbo.TabIndex = 6
        '
        'GridView15
        '
        Me.GridView15.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn76, Me.GridColumn77, Me.GridColumn78, Me.GridColumn79, Me.GridColumn80})
        Me.GridView15.DetailHeight = 485
        Me.GridView15.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView15.Name = "GridView15"
        Me.GridView15.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView15.OptionsView.ShowGroupPanel = False
        '
        'GridColumn76
        '
        Me.GridColumn76.FieldName = "MedName"
        Me.GridColumn76.MinWidth = 27
        Me.GridColumn76.Name = "GridColumn76"
        Me.GridColumn76.Visible = True
        Me.GridColumn76.VisibleIndex = 0
        Me.GridColumn76.Width = 1104
        '
        'GridColumn77
        '
        Me.GridColumn77.FieldName = "CC"
        Me.GridColumn77.MinWidth = 27
        Me.GridColumn77.Name = "GridColumn77"
        Me.GridColumn77.Visible = True
        Me.GridColumn77.VisibleIndex = 1
        Me.GridColumn77.Width = 116
        '
        'GridColumn78
        '
        Me.GridColumn78.Caption = "Dedicated Line"
        Me.GridColumn78.FieldName = "DedicatedLine"
        Me.GridColumn78.MinWidth = 27
        Me.GridColumn78.Name = "GridColumn78"
        Me.GridColumn78.Visible = True
        Me.GridColumn78.VisibleIndex = 2
        Me.GridColumn78.Width = 139
        '
        'GridColumn79
        '
        Me.GridColumn79.Caption = "Chemo"
        Me.GridColumn79.FieldName = "Chemo"
        Me.GridColumn79.MinWidth = 27
        Me.GridColumn79.Name = "GridColumn79"
        Me.GridColumn79.Visible = True
        Me.GridColumn79.VisibleIndex = 3
        Me.GridColumn79.Width = 111
        '
        'GridColumn80
        '
        Me.GridColumn80.Caption = "Hormonal"
        Me.GridColumn80.FieldName = "Hormonal"
        Me.GridColumn80.MinWidth = 27
        Me.GridColumn80.Name = "GridColumn80"
        Me.GridColumn80.Visible = True
        Me.GridColumn80.VisibleIndex = 4
        Me.GridColumn80.Width = 123
        '
        'Infusion_InfusedMedicationsMedication_lbl
        '
        Me.Infusion_InfusedMedicationsMedication_lbl.Location = New System.Drawing.Point(612, 30)
        Me.Infusion_InfusedMedicationsMedication_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMedication_lbl.Name = "Infusion_InfusedMedicationsMedication_lbl"
        Me.Infusion_InfusedMedicationsMedication_lbl.Size = New System.Drawing.Size(68, 18)
        Me.Infusion_InfusedMedicationsMedication_lbl.TabIndex = 307
        Me.Infusion_InfusedMedicationsMedication_lbl.Text = "Medication"
        '
        'Infusion_InfusedMedications07_lbl
        '
        Me.Infusion_InfusedMedications07_lbl.Location = New System.Drawing.Point(25, 222)
        Me.Infusion_InfusedMedications07_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications07_lbl.Name = "Infusion_InfusedMedications07_lbl"
        Me.Infusion_InfusedMedications07_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications07_lbl.TabIndex = 284
        Me.Infusion_InfusedMedications07_lbl.Text = "7)"
        '
        'Infusion_InfusedMedications06_lbl
        '
        Me.Infusion_InfusedMedications06_lbl.Location = New System.Drawing.Point(25, 194)
        Me.Infusion_InfusedMedications06_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications06_lbl.Name = "Infusion_InfusedMedications06_lbl"
        Me.Infusion_InfusedMedications06_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications06_lbl.TabIndex = 283
        Me.Infusion_InfusedMedications06_lbl.Text = "6)"
        '
        'Infusion_InfusedMedications05_lbl
        '
        Me.Infusion_InfusedMedications05_lbl.Location = New System.Drawing.Point(25, 166)
        Me.Infusion_InfusedMedications05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications05_lbl.Name = "Infusion_InfusedMedications05_lbl"
        Me.Infusion_InfusedMedications05_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications05_lbl.TabIndex = 282
        Me.Infusion_InfusedMedications05_lbl.Text = "5)"
        '
        'Infusion_InfusedMedications04_lbl
        '
        Me.Infusion_InfusedMedications04_lbl.Location = New System.Drawing.Point(25, 138)
        Me.Infusion_InfusedMedications04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications04_lbl.Name = "Infusion_InfusedMedications04_lbl"
        Me.Infusion_InfusedMedications04_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications04_lbl.TabIndex = 281
        Me.Infusion_InfusedMedications04_lbl.Text = "4)"
        '
        'Infusion_InfusedMedications03_lbl
        '
        Me.Infusion_InfusedMedications03_lbl.Location = New System.Drawing.Point(25, 111)
        Me.Infusion_InfusedMedications03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications03_lbl.Name = "Infusion_InfusedMedications03_lbl"
        Me.Infusion_InfusedMedications03_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications03_lbl.TabIndex = 280
        Me.Infusion_InfusedMedications03_lbl.Text = "3)"
        '
        'Infusion_InfusedMedications02_lbl
        '
        Me.Infusion_InfusedMedications02_lbl.Location = New System.Drawing.Point(25, 83)
        Me.Infusion_InfusedMedications02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications02_lbl.Name = "Infusion_InfusedMedications02_lbl"
        Me.Infusion_InfusedMedications02_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications02_lbl.TabIndex = 279
        Me.Infusion_InfusedMedications02_lbl.Text = "2)"
        '
        'Infusion_InfusedMedications01_lbl
        '
        Me.Infusion_InfusedMedications01_lbl.Location = New System.Drawing.Point(25, 55)
        Me.Infusion_InfusedMedications01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedications01_lbl.Name = "Infusion_InfusedMedications01_lbl"
        Me.Infusion_InfusedMedications01_lbl.Size = New System.Drawing.Size(14, 18)
        Me.Infusion_InfusedMedications01_lbl.TabIndex = 278
        Me.Infusion_InfusedMedications01_lbl.Text = "1)"
        '
        'Infusion_InfusedMedicationsMins07_lbl
        '
        Me.Infusion_InfusedMedicationsMins07_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins07_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins07_lbl.Location = New System.Drawing.Point(441, 224)
        Me.Infusion_InfusedMedicationsMins07_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins07_lbl.Name = "Infusion_InfusedMedicationsMins07_lbl"
        Me.Infusion_InfusedMedicationsMins07_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins07_lbl.TabIndex = 52
        Me.Infusion_InfusedMedicationsMins07_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins06_lbl
        '
        Me.Infusion_InfusedMedicationsMins06_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins06_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins06_lbl.Location = New System.Drawing.Point(441, 197)
        Me.Infusion_InfusedMedicationsMins06_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins06_lbl.Name = "Infusion_InfusedMedicationsMins06_lbl"
        Me.Infusion_InfusedMedicationsMins06_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins06_lbl.TabIndex = 44
        Me.Infusion_InfusedMedicationsMins06_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins05_lbl
        '
        Me.Infusion_InfusedMedicationsMins05_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins05_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins05_lbl.Location = New System.Drawing.Point(441, 169)
        Me.Infusion_InfusedMedicationsMins05_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins05_lbl.Name = "Infusion_InfusedMedicationsMins05_lbl"
        Me.Infusion_InfusedMedicationsMins05_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins05_lbl.TabIndex = 36
        Me.Infusion_InfusedMedicationsMins05_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins04_lbl
        '
        Me.Infusion_InfusedMedicationsMins04_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins04_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins04_lbl.Location = New System.Drawing.Point(441, 141)
        Me.Infusion_InfusedMedicationsMins04_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins04_lbl.Name = "Infusion_InfusedMedicationsMins04_lbl"
        Me.Infusion_InfusedMedicationsMins04_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins04_lbl.TabIndex = 28
        Me.Infusion_InfusedMedicationsMins04_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins03_lbl
        '
        Me.Infusion_InfusedMedicationsMins03_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins03_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins03_lbl.Location = New System.Drawing.Point(441, 114)
        Me.Infusion_InfusedMedicationsMins03_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins03_lbl.Name = "Infusion_InfusedMedicationsMins03_lbl"
        Me.Infusion_InfusedMedicationsMins03_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins03_lbl.TabIndex = 20
        Me.Infusion_InfusedMedicationsMins03_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins02_lbl
        '
        Me.Infusion_InfusedMedicationsMins02_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins02_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins02_lbl.Location = New System.Drawing.Point(441, 86)
        Me.Infusion_InfusedMedicationsMins02_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins02_lbl.Name = "Infusion_InfusedMedicationsMins02_lbl"
        Me.Infusion_InfusedMedicationsMins02_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins02_lbl.TabIndex = 12
        Me.Infusion_InfusedMedicationsMins02_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsMins01_lbl
        '
        Me.Infusion_InfusedMedicationsMins01_lbl.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Infusion_InfusedMedicationsMins01_lbl.Appearance.Options.UseFont = True
        Me.Infusion_InfusedMedicationsMins01_lbl.Location = New System.Drawing.Point(441, 58)
        Me.Infusion_InfusedMedicationsMins01_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins01_lbl.Name = "Infusion_InfusedMedicationsMins01_lbl"
        Me.Infusion_InfusedMedicationsMins01_lbl.Size = New System.Drawing.Size(28, 13)
        Me.Infusion_InfusedMedicationsMins01_lbl.TabIndex = 4
        Me.Infusion_InfusedMedicationsMins01_lbl.Text = "9999"
        '
        'Infusion_InfusedMedicationsResponse_lbl
        '
        Me.Infusion_InfusedMedicationsResponse_lbl.Location = New System.Drawing.Point(769, 30)
        Me.Infusion_InfusedMedicationsResponse_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse_lbl.Name = "Infusion_InfusedMedicationsResponse_lbl"
        Me.Infusion_InfusedMedicationsResponse_lbl.Size = New System.Drawing.Size(63, 18)
        Me.Infusion_InfusedMedicationsResponse_lbl.TabIndex = 263
        Me.Infusion_InfusedMedicationsResponse_lbl.Text = "Response"
        '
        'Infusion_InfusedMedicationsSite_lbl
        '
        Me.Infusion_InfusedMedicationsSite_lbl.Location = New System.Drawing.Point(524, 30)
        Me.Infusion_InfusedMedicationsSite_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite_lbl.Name = "Infusion_InfusedMedicationsSite_lbl"
        Me.Infusion_InfusedMedicationsSite_lbl.Size = New System.Drawing.Size(23, 18)
        Me.Infusion_InfusedMedicationsSite_lbl.TabIndex = 262
        Me.Infusion_InfusedMedicationsSite_lbl.Text = "Site"
        '
        'Infusion_InfusedMedicationsMins_lbl
        '
        Me.Infusion_InfusedMedicationsMins_lbl.Location = New System.Drawing.Point(441, 30)
        Me.Infusion_InfusedMedicationsMins_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsMins_lbl.Name = "Infusion_InfusedMedicationsMins_lbl"
        Me.Infusion_InfusedMedicationsMins_lbl.Size = New System.Drawing.Size(29, 18)
        Me.Infusion_InfusedMedicationsMins_lbl.TabIndex = 261
        Me.Infusion_InfusedMedicationsMins_lbl.Text = "Mins"
        '
        'Infusion_InfusedMedicationsEndTime_lbl
        '
        Me.Infusion_InfusedMedicationsEndTime_lbl.Location = New System.Drawing.Point(359, 30)
        Me.Infusion_InfusedMedicationsEndTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime_lbl.Name = "Infusion_InfusedMedicationsEndTime_lbl"
        Me.Infusion_InfusedMedicationsEndTime_lbl.Size = New System.Drawing.Size(62, 18)
        Me.Infusion_InfusedMedicationsEndTime_lbl.TabIndex = 260
        Me.Infusion_InfusedMedicationsEndTime_lbl.Text = "End Time"
        '
        'Infusion_InfusedMedicationsEndDate_lbl
        '
        Me.Infusion_InfusedMedicationsEndDate_lbl.Location = New System.Drawing.Point(261, 30)
        Me.Infusion_InfusedMedicationsEndDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate_lbl.Name = "Infusion_InfusedMedicationsEndDate_lbl"
        Me.Infusion_InfusedMedicationsEndDate_lbl.Size = New System.Drawing.Size(60, 18)
        Me.Infusion_InfusedMedicationsEndDate_lbl.TabIndex = 259
        Me.Infusion_InfusedMedicationsEndDate_lbl.Text = "End Date"
        '
        'Infusion_InfusedMedicationsEndTime04_txt
        '
        Me.Infusion_InfusedMedicationsEndTime04_txt.Location = New System.Drawing.Point(361, 134)
        Me.Infusion_InfusedMedicationsEndTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime04_txt.Name = "Infusion_InfusedMedicationsEndTime04_txt"
        Me.Infusion_InfusedMedicationsEndTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime04_txt.TabIndex = 25
        '
        'Infusion_InfusedMedicationsEndDate04_dte
        '
        Me.Infusion_InfusedMedicationsEndDate04_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate04_dte.Location = New System.Drawing.Point(245, 134)
        Me.Infusion_InfusedMedicationsEndDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate04_dte.Name = "Infusion_InfusedMedicationsEndDate04_dte"
        Me.Infusion_InfusedMedicationsEndDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate04_dte.TabIndex = 24
        '
        'Infusion_InfusedMedicationsEndTime03_txt
        '
        Me.Infusion_InfusedMedicationsEndTime03_txt.Location = New System.Drawing.Point(361, 107)
        Me.Infusion_InfusedMedicationsEndTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime03_txt.Name = "Infusion_InfusedMedicationsEndTime03_txt"
        Me.Infusion_InfusedMedicationsEndTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime03_txt.TabIndex = 18
        '
        'Infusion_InfusedMedicationsEndDate03_dte
        '
        Me.Infusion_InfusedMedicationsEndDate03_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate03_dte.Location = New System.Drawing.Point(245, 107)
        Me.Infusion_InfusedMedicationsEndDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate03_dte.Name = "Infusion_InfusedMedicationsEndDate03_dte"
        Me.Infusion_InfusedMedicationsEndDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate03_dte.TabIndex = 17
        '
        'Infusion_InfusedMedicationsEndTime07_txt
        '
        Me.Infusion_InfusedMedicationsEndTime07_txt.Location = New System.Drawing.Point(361, 217)
        Me.Infusion_InfusedMedicationsEndTime07_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime07_txt.Name = "Infusion_InfusedMedicationsEndTime07_txt"
        Me.Infusion_InfusedMedicationsEndTime07_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime07_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime07_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime07_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime07_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime07_txt.TabIndex = 46
        '
        'Infusion_InfusedMedicationsEndTime06_txt
        '
        Me.Infusion_InfusedMedicationsEndTime06_txt.Location = New System.Drawing.Point(361, 190)
        Me.Infusion_InfusedMedicationsEndTime06_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime06_txt.Name = "Infusion_InfusedMedicationsEndTime06_txt"
        Me.Infusion_InfusedMedicationsEndTime06_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime06_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime06_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime06_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime06_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime06_txt.TabIndex = 39
        '
        'Infusion_InfusedMedicationsEndTime05_txt
        '
        Me.Infusion_InfusedMedicationsEndTime05_txt.Location = New System.Drawing.Point(361, 162)
        Me.Infusion_InfusedMedicationsEndTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime05_txt.Name = "Infusion_InfusedMedicationsEndTime05_txt"
        Me.Infusion_InfusedMedicationsEndTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime05_txt.TabIndex = 32
        '
        'Infusion_InfusedMedicationsEndTime02_txt
        '
        Me.Infusion_InfusedMedicationsEndTime02_txt.Location = New System.Drawing.Point(361, 79)
        Me.Infusion_InfusedMedicationsEndTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime02_txt.Name = "Infusion_InfusedMedicationsEndTime02_txt"
        Me.Infusion_InfusedMedicationsEndTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime02_txt.TabIndex = 11
        '
        'Infusion_InfusedMedicationsEndTime01_txt
        '
        Me.Infusion_InfusedMedicationsEndTime01_txt.Location = New System.Drawing.Point(361, 51)
        Me.Infusion_InfusedMedicationsEndTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndTime01_txt.Name = "Infusion_InfusedMedicationsEndTime01_txt"
        Me.Infusion_InfusedMedicationsEndTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsEndTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsEndTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsEndTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsEndTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsEndTime01_txt.TabIndex = 3
        '
        'Infusion_InfusedMedicationsEndDate07_dte
        '
        Me.Infusion_InfusedMedicationsEndDate07_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate07_dte.Location = New System.Drawing.Point(245, 217)
        Me.Infusion_InfusedMedicationsEndDate07_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate07_dte.Name = "Infusion_InfusedMedicationsEndDate07_dte"
        Me.Infusion_InfusedMedicationsEndDate07_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate07_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate07_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate07_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate07_dte.TabIndex = 45
        '
        'Infusion_InfusedMedicationsEndDate06_dte
        '
        Me.Infusion_InfusedMedicationsEndDate06_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate06_dte.Location = New System.Drawing.Point(245, 190)
        Me.Infusion_InfusedMedicationsEndDate06_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate06_dte.Name = "Infusion_InfusedMedicationsEndDate06_dte"
        Me.Infusion_InfusedMedicationsEndDate06_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate06_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate06_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate06_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate06_dte.TabIndex = 38
        '
        'Infusion_InfusedMedicationsEndDate05_dte
        '
        Me.Infusion_InfusedMedicationsEndDate05_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate05_dte.Location = New System.Drawing.Point(245, 162)
        Me.Infusion_InfusedMedicationsEndDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate05_dte.Name = "Infusion_InfusedMedicationsEndDate05_dte"
        Me.Infusion_InfusedMedicationsEndDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate05_dte.TabIndex = 31
        '
        'Infusion_InfusedMedicationsEndDate02_dte
        '
        Me.Infusion_InfusedMedicationsEndDate02_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate02_dte.Location = New System.Drawing.Point(245, 79)
        Me.Infusion_InfusedMedicationsEndDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate02_dte.Name = "Infusion_InfusedMedicationsEndDate02_dte"
        Me.Infusion_InfusedMedicationsEndDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate02_dte.TabIndex = 10
        '
        'Infusion_InfusedMedicationsEndDate01_dte
        '
        Me.Infusion_InfusedMedicationsEndDate01_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsEndDate01_dte.Location = New System.Drawing.Point(245, 51)
        Me.Infusion_InfusedMedicationsEndDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsEndDate01_dte.Name = "Infusion_InfusedMedicationsEndDate01_dte"
        Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusion_InfusedMedicationsEndDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsEndDate01_dte.TabIndex = 2
        Me.Infusion_InfusedMedicationsEndDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusion_InfusedMedicationsStartTime_lbl
        '
        Me.Infusion_InfusedMedicationsStartTime_lbl.Location = New System.Drawing.Point(160, 28)
        Me.Infusion_InfusedMedicationsStartTime_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime_lbl.Name = "Infusion_InfusedMedicationsStartTime_lbl"
        Me.Infusion_InfusedMedicationsStartTime_lbl.Size = New System.Drawing.Size(69, 18)
        Me.Infusion_InfusedMedicationsStartTime_lbl.TabIndex = 244
        Me.Infusion_InfusedMedicationsStartTime_lbl.Text = "Start Time"
        '
        'Infusion_InfusedMedicationsStartDate_lbl
        '
        Me.Infusion_InfusedMedicationsStartDate_lbl.Location = New System.Drawing.Point(63, 28)
        Me.Infusion_InfusedMedicationsStartDate_lbl.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate_lbl.Name = "Infusion_InfusedMedicationsStartDate_lbl"
        Me.Infusion_InfusedMedicationsStartDate_lbl.Size = New System.Drawing.Size(67, 18)
        Me.Infusion_InfusedMedicationsStartDate_lbl.TabIndex = 243
        Me.Infusion_InfusedMedicationsStartDate_lbl.Text = "Start Date"
        '
        'Infusion_InfusedMedicationsStartTime04_txt
        '
        Me.Infusion_InfusedMedicationsStartTime04_txt.Location = New System.Drawing.Point(163, 133)
        Me.Infusion_InfusedMedicationsStartTime04_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime04_txt.Name = "Infusion_InfusedMedicationsStartTime04_txt"
        Me.Infusion_InfusedMedicationsStartTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime04_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime04_txt.TabIndex = 23
        '
        'Infusion_InfusedMedicationsStartDate04_dte
        '
        Me.Infusion_InfusedMedicationsStartDate04_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate04_dte.Location = New System.Drawing.Point(47, 133)
        Me.Infusion_InfusedMedicationsStartDate04_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate04_dte.Name = "Infusion_InfusedMedicationsStartDate04_dte"
        Me.Infusion_InfusedMedicationsStartDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate04_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate04_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate04_dte.TabIndex = 22
        '
        'Infusion_InfusedMedicationsStartTime03_txt
        '
        Me.Infusion_InfusedMedicationsStartTime03_txt.Location = New System.Drawing.Point(163, 105)
        Me.Infusion_InfusedMedicationsStartTime03_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime03_txt.Name = "Infusion_InfusedMedicationsStartTime03_txt"
        Me.Infusion_InfusedMedicationsStartTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime03_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime03_txt.TabIndex = 16
        '
        'Infusion_InfusedMedicationsStartDate03_dte
        '
        Me.Infusion_InfusedMedicationsStartDate03_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate03_dte.Location = New System.Drawing.Point(47, 105)
        Me.Infusion_InfusedMedicationsStartDate03_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate03_dte.Name = "Infusion_InfusedMedicationsStartDate03_dte"
        Me.Infusion_InfusedMedicationsStartDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate03_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate03_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate03_dte.TabIndex = 15
        '
        'Infusion_InfusedMedicationsStartTime07_txt
        '
        Me.Infusion_InfusedMedicationsStartTime07_txt.Location = New System.Drawing.Point(163, 216)
        Me.Infusion_InfusedMedicationsStartTime07_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime07_txt.Name = "Infusion_InfusedMedicationsStartTime07_txt"
        Me.Infusion_InfusedMedicationsStartTime07_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime07_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime07_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime07_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime07_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime07_txt.TabIndex = 44
        '
        'Infusion_InfusedMedicationsStartTime06_txt
        '
        Me.Infusion_InfusedMedicationsStartTime06_txt.Location = New System.Drawing.Point(163, 188)
        Me.Infusion_InfusedMedicationsStartTime06_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime06_txt.Name = "Infusion_InfusedMedicationsStartTime06_txt"
        Me.Infusion_InfusedMedicationsStartTime06_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime06_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime06_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime06_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime06_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime06_txt.TabIndex = 37
        '
        'Infusion_InfusedMedicationsStartTime05_txt
        '
        Me.Infusion_InfusedMedicationsStartTime05_txt.Location = New System.Drawing.Point(163, 161)
        Me.Infusion_InfusedMedicationsStartTime05_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime05_txt.Name = "Infusion_InfusedMedicationsStartTime05_txt"
        Me.Infusion_InfusedMedicationsStartTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime05_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime05_txt.TabIndex = 30
        '
        'Infusion_InfusedMedicationsStartTime02_txt
        '
        Me.Infusion_InfusedMedicationsStartTime02_txt.Location = New System.Drawing.Point(163, 78)
        Me.Infusion_InfusedMedicationsStartTime02_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime02_txt.Name = "Infusion_InfusedMedicationsStartTime02_txt"
        Me.Infusion_InfusedMedicationsStartTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime02_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime02_txt.TabIndex = 9
        '
        'Infusion_InfusedMedicationsStartTime01_txt
        '
        Me.Infusion_InfusedMedicationsStartTime01_txt.Location = New System.Drawing.Point(163, 50)
        Me.Infusion_InfusedMedicationsStartTime01_txt.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartTime01_txt.Name = "Infusion_InfusedMedicationsStartTime01_txt"
        Me.Infusion_InfusedMedicationsStartTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Me.Infusion_InfusedMedicationsStartTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Me.Infusion_InfusedMedicationsStartTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Me.Infusion_InfusedMedicationsStartTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Me.Infusion_InfusedMedicationsStartTime01_txt.Size = New System.Drawing.Size(65, 24)
        Me.Infusion_InfusedMedicationsStartTime01_txt.TabIndex = 1
        '
        'Infusion_InfusedMedicationsStartDate07_dte
        '
        Me.Infusion_InfusedMedicationsStartDate07_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate07_dte.Location = New System.Drawing.Point(47, 216)
        Me.Infusion_InfusedMedicationsStartDate07_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate07_dte.Name = "Infusion_InfusedMedicationsStartDate07_dte"
        Me.Infusion_InfusedMedicationsStartDate07_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate07_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate07_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate07_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate07_dte.TabIndex = 43
        '
        'Infusion_InfusedMedicationsStartDate06_dte
        '
        Me.Infusion_InfusedMedicationsStartDate06_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate06_dte.Location = New System.Drawing.Point(47, 188)
        Me.Infusion_InfusedMedicationsStartDate06_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate06_dte.Name = "Infusion_InfusedMedicationsStartDate06_dte"
        Me.Infusion_InfusedMedicationsStartDate06_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate06_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate06_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate06_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate06_dte.TabIndex = 36
        '
        'Infusion_InfusedMedicationsStartDate05_dte
        '
        Me.Infusion_InfusedMedicationsStartDate05_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate05_dte.Location = New System.Drawing.Point(47, 161)
        Me.Infusion_InfusedMedicationsStartDate05_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate05_dte.Name = "Infusion_InfusedMedicationsStartDate05_dte"
        Me.Infusion_InfusedMedicationsStartDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate05_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate05_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate05_dte.TabIndex = 29
        '
        'Infusion_InfusedMedicationsStartDate02_dte
        '
        Me.Infusion_InfusedMedicationsStartDate02_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate02_dte.Location = New System.Drawing.Point(47, 78)
        Me.Infusion_InfusedMedicationsStartDate02_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate02_dte.Name = "Infusion_InfusedMedicationsStartDate02_dte"
        Me.Infusion_InfusedMedicationsStartDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate02_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate02_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate02_dte.TabIndex = 8
        '
        'Infusion_InfusedMedicationsStartDate01_dte
        '
        Me.Infusion_InfusedMedicationsStartDate01_dte.EditValue = Nothing
        Me.Infusion_InfusedMedicationsStartDate01_dte.Location = New System.Drawing.Point(47, 50)
        Me.Infusion_InfusedMedicationsStartDate01_dte.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsStartDate01_dte.Name = "Infusion_InfusedMedicationsStartDate01_dte"
        Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Me.Infusion_InfusedMedicationsStartDate01_dte.Size = New System.Drawing.Size(115, 24)
        Me.Infusion_InfusedMedicationsStartDate01_dte.TabIndex = 0
        Me.Infusion_InfusedMedicationsStartDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.[Error]
        '
        'Infusion_InfusedMedicationsResponse07_cbo
        '
        Me.Infusion_InfusedMedicationsResponse07_cbo.Location = New System.Drawing.Point(745, 217)
        Me.Infusion_InfusedMedicationsResponse07_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse07_cbo.Name = "Infusion_InfusedMedicationsResponse07_cbo"
        Me.Infusion_InfusedMedicationsResponse07_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse07_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse07_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse07_cbo.TabIndex = 49
        '
        'Infusion_InfusedMedicationsSite07_cbo
        '
        Me.Infusion_InfusedMedicationsSite07_cbo.Location = New System.Drawing.Point(492, 217)
        Me.Infusion_InfusedMedicationsSite07_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite07_cbo.Name = "Infusion_InfusedMedicationsSite07_cbo"
        Me.Infusion_InfusedMedicationsSite07_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite07_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_InfusedMedicationsSite07_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite07_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite07_cbo.TabIndex = 47
        '
        'Infusion_InfusedMedicationsResponse06_cbo
        '
        Me.Infusion_InfusedMedicationsResponse06_cbo.Location = New System.Drawing.Point(745, 190)
        Me.Infusion_InfusedMedicationsResponse06_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse06_cbo.Name = "Infusion_InfusedMedicationsResponse06_cbo"
        Me.Infusion_InfusedMedicationsResponse06_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse06_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse06_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse06_cbo.TabIndex = 42
        '
        'Infusion_InfusedMedicationsResponse05_cbo
        '
        Me.Infusion_InfusedMedicationsResponse05_cbo.Location = New System.Drawing.Point(745, 162)
        Me.Infusion_InfusedMedicationsResponse05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse05_cbo.Name = "Infusion_InfusedMedicationsResponse05_cbo"
        Me.Infusion_InfusedMedicationsResponse05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse05_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse05_cbo.TabIndex = 35
        '
        'Infusion_InfusedMedicationsResponse04_cbo
        '
        Me.Infusion_InfusedMedicationsResponse04_cbo.Location = New System.Drawing.Point(745, 134)
        Me.Infusion_InfusedMedicationsResponse04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse04_cbo.Name = "Infusion_InfusedMedicationsResponse04_cbo"
        Me.Infusion_InfusedMedicationsResponse04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse04_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse04_cbo.TabIndex = 28
        '
        'Infusion_InfusedMedicationsResponse03_cbo
        '
        Me.Infusion_InfusedMedicationsResponse03_cbo.Location = New System.Drawing.Point(745, 107)
        Me.Infusion_InfusedMedicationsResponse03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse03_cbo.Name = "Infusion_InfusedMedicationsResponse03_cbo"
        Me.Infusion_InfusedMedicationsResponse03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse03_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse03_cbo.TabIndex = 21
        '
        'Infusion_InfusedMedicationsResponse02_cbo
        '
        Me.Infusion_InfusedMedicationsResponse02_cbo.Location = New System.Drawing.Point(745, 79)
        Me.Infusion_InfusedMedicationsResponse02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse02_cbo.Name = "Infusion_InfusedMedicationsResponse02_cbo"
        Me.Infusion_InfusedMedicationsResponse02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse02_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse02_cbo.TabIndex = 14
        '
        'Infusion_InfusedMedicationsResponse01_cbo
        '
        Me.Infusion_InfusedMedicationsResponse01_cbo.Location = New System.Drawing.Point(745, 51)
        Me.Infusion_InfusedMedicationsResponse01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsResponse01_cbo.Name = "Infusion_InfusedMedicationsResponse01_cbo"
        Me.Infusion_InfusedMedicationsResponse01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsResponse01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsResponse01_cbo.Size = New System.Drawing.Size(140, 24)
        Me.Infusion_InfusedMedicationsResponse01_cbo.TabIndex = 7
        '
        'Infusion_InfusedMedicationsSite06_cbo
        '
        Me.Infusion_InfusedMedicationsSite06_cbo.Location = New System.Drawing.Point(492, 190)
        Me.Infusion_InfusedMedicationsSite06_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite06_cbo.Name = "Infusion_InfusedMedicationsSite06_cbo"
        Me.Infusion_InfusedMedicationsSite06_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite06_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_InfusedMedicationsSite06_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite06_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite06_cbo.TabIndex = 40
        '
        'Infusion_InfusedMedicationsSite05_cbo
        '
        Me.Infusion_InfusedMedicationsSite05_cbo.Location = New System.Drawing.Point(492, 162)
        Me.Infusion_InfusedMedicationsSite05_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite05_cbo.Name = "Infusion_InfusedMedicationsSite05_cbo"
        Me.Infusion_InfusedMedicationsSite05_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite05_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_InfusedMedicationsSite05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite05_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite05_cbo.TabIndex = 33
        '
        'Infusion_InfusedMedicationsSite04_cbo
        '
        Me.Infusion_InfusedMedicationsSite04_cbo.Location = New System.Drawing.Point(492, 134)
        Me.Infusion_InfusedMedicationsSite04_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite04_cbo.Name = "Infusion_InfusedMedicationsSite04_cbo"
        Me.Infusion_InfusedMedicationsSite04_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite04_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_InfusedMedicationsSite04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite04_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite04_cbo.TabIndex = 26
        '
        'Infusion_InfusedMedicationsSite03_cbo
        '
        Me.Infusion_InfusedMedicationsSite03_cbo.Location = New System.Drawing.Point(492, 107)
        Me.Infusion_InfusedMedicationsSite03_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite03_cbo.Name = "Infusion_InfusedMedicationsSite03_cbo"
        Me.Infusion_InfusedMedicationsSite03_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite03_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Me.Infusion_InfusedMedicationsSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite03_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite03_cbo.TabIndex = 19
        '
        'Infusion_InfusedMedicationsSite02_cbo
        '
        Me.Infusion_InfusedMedicationsSite02_cbo.Location = New System.Drawing.Point(492, 79)
        Me.Infusion_InfusedMedicationsSite02_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite02_cbo.Name = "Infusion_InfusedMedicationsSite02_cbo"
        Me.Infusion_InfusedMedicationsSite02_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite02_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite02_cbo.TabIndex = 12
        '
        'Infusion_InfusedMedicationsSite01_cbo
        '
        Me.Infusion_InfusedMedicationsSite01_cbo.Location = New System.Drawing.Point(492, 51)
        Me.Infusion_InfusedMedicationsSite01_cbo.Margin = New System.Windows.Forms.Padding(4)
        Me.Infusion_InfusedMedicationsSite01_cbo.Name = "Infusion_InfusedMedicationsSite01_cbo"
        Me.Infusion_InfusedMedicationsSite01_cbo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Infusion_InfusedMedicationsSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Infusion_InfusedMedicationsSite01_cbo.Size = New System.Drawing.Size(84, 24)
        Me.Infusion_InfusedMedicationsSite01_cbo.TabIndex = 4
        '
        'JJCTestForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(2470, 1069)
        Me.Controls.Add(Me.Infusion_AccessInfusaport_grp)
        Me.Controls.Add(Me.Infusions_Nurse_grp)
        Me.Controls.Add(Me.Infusions_QualityIndicators_grp)
        Me.Controls.Add(Me.Infusions_Thrombolytics_grp)
        Me.Controls.Add(Me.Infusions_BloodAdministration_grp)
        Me.Controls.Add(Me.Infusions_IVSites_grp)
        Me.Controls.Add(Me.Infusions_TitratedMedications_grp)
        Me.Controls.Add(Me.Infusions_Hydrations_grp)
        Me.Controls.Add(Me.Infusions_InfusedMedications_grp)
        Me.Name = "JJCTestForm"
        Me.Text = "JJCTestForm"
        CType(Me.Infusion_AccessInfusaport_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusion_AccessInfusaport_grp.ResumeLayout(False)
        CType(Me.Infusion_AccessInfusaport_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_Nurse_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_Nurse_grp.ResumeLayout(False)
        CType(Me.Infusions_Nurse_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_QualityIndicators_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_QualityIndicators_grp.ResumeLayout(False)
        Me.Infusions_QualityIndicators_grp.PerformLayout()
        CType(Me.Infusion_QI_TotalInfusCodedAsIVP_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_QI_TotalInfusNotCoded_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_QIOption02_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_QIOption01_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_SitesND_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_IncorrectNursingDocumentation_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_IncompleteTimes_chk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_Thrombolytics_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_Thrombolytics_grp.ResumeLayout(False)
        CType(Me.Infusion_Thrombolytics_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_BloodAdministration_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_BloodAdministration_grp.ResumeLayout(False)
        CType(Me.Infusion_BloodAdminstration_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_IVSites_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_IVSites_grp.ResumeLayout(False)
        Me.Infusions_IVSites_grp.PerformLayout()
        CType(Me.Infusion_IVSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_IVSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_IVSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedications_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_TitratedMedications_grp.ResumeLayout(False)
        Me.Infusions_TitratedMedications_grp.PerformLayout()
        CType(Me.Infusions_TitratedMedicationsMedication05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView26, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsMedication04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView25, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsMedication03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView24, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsMedication02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsMedication01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_TitratedMedicationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_Hydrations_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_Hydrations_grp.ResumeLayout(False)
        Me.Infusions_Hydrations_grp.PerformLayout()
        CType(Me.Infusion_HydrationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsRate05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsRate04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsRate03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsRate02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsRate01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_HydrationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusions_InfusedMedications_grp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Infusions_InfusedMedications_grp.ResumeLayout(False)
        Me.Infusions_InfusedMedications_grp.PerformLayout()
        CType(Me.Infusion_InfusedMedicationsMedication07_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication06_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsMedication01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime07_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime06_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate07_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate07_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate06_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate06_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsEndDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime04_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate04_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate04_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime03_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate03_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate03_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime07_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime06_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime05_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime02_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartTime01_txt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate07_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate07_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate06_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate06_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate05_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate05_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate02_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate02_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate01_dte.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsStartDate01_dte.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse07_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite07_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse06_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsResponse01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite06_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite05_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite04_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite03_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite02_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Infusion_InfusedMedicationsSite01_cbo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents Infusion_AccessInfusaport_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_AccessInfusaport_chk As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Infusions_Nurse_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusions_Nurse_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Protected WithEvents Infusions_QualityIndicators_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_QI_TotalInfusCodedAsIVP_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_QI_TotalInfusCodedAsIVP_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_QI_TotalInfusNotCoded_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_QI_TotalInfusNotCoded_lbl As DevExpress.XtraEditors.LabelControl
    Protected WithEvents Infusion_QIOption02_chk As DevExpress.XtraEditors.CheckEdit
    Protected WithEvents Infusion_QIOption01_chk As DevExpress.XtraEditors.CheckEdit
    Protected WithEvents Infusion_SitesND_chk As DevExpress.XtraEditors.CheckEdit
    Protected WithEvents Infusion_IncorrectNursingDocumentation_chk As DevExpress.XtraEditors.CheckEdit
    Protected WithEvents Infusion_IncompleteTimes_chk As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Infusions_Thrombolytics_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_Thrombolytics_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_BloodAdministration_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_BloodAdminstration_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_IVSites_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_IVSite03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_IVSite02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_IVSite01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_IVSite03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_IVSite02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_IVSite01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedications_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusions_TitratedMedicationsMedication05_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView26 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn131 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn132 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn133 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn134 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn135 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusions_TitratedMedicationsMedication04_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView25 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn126 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn127 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn128 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn129 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn130 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusions_TitratedMedicationsMedication03_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView24 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn121 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn122 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn123 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn124 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn125 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusions_TitratedMedicationsMedication02_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView23 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn116 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn117 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn118 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn119 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn120 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusions_TitratedMedicationsMedication01_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView22 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn111 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn112 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn113 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn114 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn115 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusions_TitratedMedications05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedications04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedications03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedications02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedications01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMedication_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsResponse_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsSite_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsMins_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsEndTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsEndDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsEndTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsEndDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsEndTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsEndDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsEndTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsEndTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsEndTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsEndDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsEndDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsEndDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsStartTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsStartDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusions_TitratedMedicationsStartTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsStartDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsStartTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsStartDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsStartTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsStartTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsStartTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusions_TitratedMedicationsStartDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsStartDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsStartDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusions_TitratedMedicationsResponse05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsResponse04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsResponse03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsResponse02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsResponse01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsSite05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsSite04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsSite03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsSite02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_TitratedMedicationsSite01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_Hydrations_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_Hydrations05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_Hydrations04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_Hydrations03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_Hydrations02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_Hydrations01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsRate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsResponse_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsSite_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsMins_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsEndTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsEndDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsEndTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsEndDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsEndTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsEndDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsEndTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsEndTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsEndTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsEndDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsEndDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsEndDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsStartTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsStartDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_HydrationsStartTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsStartDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsStartTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsStartDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsStartTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsStartTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsStartTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_HydrationsStartDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsStartDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsStartDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_HydrationsResponse05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsResponse04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsResponse03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsResponse02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsResponse01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusions_InfusedMedications_grp As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Infusion_InfusedMedicationsMedication07_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView19 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn96 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn97 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn98 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn99 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn100 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication06_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView20 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn101 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn102 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn103 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn104 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn105 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication05_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView21 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn106 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn107 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn108 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn109 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn110 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication04_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView17 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn86 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn87 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn88 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn89 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn90 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication03_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView18 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn91 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn92 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn93 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn94 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn95 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication02_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView16 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn81 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn82 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn83 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn84 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn85 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication01_cbo As DevExpress.XtraEditors.GridLookUpEdit
    Friend WithEvents GridView15 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn76 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn77 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn78 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn79 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn80 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_InfusedMedicationsMedication_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications07_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications06_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedications01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins07_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins06_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins05_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins04_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins03_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins02_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins01_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsResponse_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsSite_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsMins_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsEndTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsEndDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsEndTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime07_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime06_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate07_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate06_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsEndDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsStartDate_lbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Infusion_InfusedMedicationsStartTime04_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate04_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime03_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate03_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime07_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime06_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime05_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime02_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartTime01_txt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate07_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate06_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate05_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate02_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsStartDate01_dte As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse07_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite07_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse06_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsResponse01_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite06_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite05_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite04_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite03_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite02_cbo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Infusion_InfusedMedicationsSite01_cbo As DevExpress.XtraEditors.ComboBoxEdit
End Class

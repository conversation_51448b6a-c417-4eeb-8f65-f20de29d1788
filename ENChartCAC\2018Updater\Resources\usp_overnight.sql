----------------------------------------------------------------------------------------------
if NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = object_id(N'[dbo].[usp_overnight]') AND OBJECTPROPERTY(object_id, N'IsProcedure') = 1)
BEGIN
	-- If the Stored Procedure doesn't exist, first create a simple version so that it can be altered later in the script
	DECLARE @strSQL nvarchar(200);
	SELECT @strSQL='CREATE PROC [dbo].[usp_overnight] as SELECT 1';
	EXEC sp_executesql @strSQL;
END

/* IC 2018 Release - Check for an Intubation column and create it if it doesn't exist */
IF NOT EXISTS(SELECT 1 FROM sys.columns WHERE Name = N'Intubation' AND Object_ID = Object_ID(N'DBO.Overnight_Data'))
BEGIN
	ALTER TABLE dbo.Overnight_Data ADD Intubation VARCHAR(25) NULL
END

GO 
/*----------------------------------------------------------------------------------------------
	Object:  StoredProcedure [dbo].usp_overnight    
	Description: Overnight Processing.
	Usage: exec usp_overnight 
	SQL Audit Flagsusp_overnight: Insert=InsertYN, Update=UpdateYN, Delete=DeleteYN, Select=SelectYN
	Updates:
	Date -------Programmer----------Update
	06/20/2018  Dustin O'Neil   Added retrieval of Intubation values
	03/03/2015	Davis, Jason	Initial Creation

----------------------------------------------------------------------------------------------*/
ALTER PROC [dbo].[usp_overnight]
	@DaysToProcess Integer = 3
AS
DECLARE @ProcName varchar(100)
	,@cur_dt datetime = GETDATE();
SET @ProcName = OBJECT_NAME(@@PROCID);

--XML-SET ANSI_WARNINGS, ANSI_PADDING ON;***REMOVE IF NOT USED*****
----------------------------------------------------------------------------------------------
-- Body of Stored Procedure
SET NOCOUNT ON;
--If setting current datetime use this variable so the datetime is consistent for the life of the proc
--SET @cur_dt = @cur_dt;

-- For insert / update / delete on SQL 2005 and later versions, use try/catch for error handling
BEGIN TRY
	BEGIN TRANSACTION;
--		INSERT / UPDATE / DELETE STATEMENT
		-- =============================================================
		insert into Overnight_Data_Timer values( '-',current_timestamp) ;
		  -- =============================================================
		insert into Overnight_Data_Timer values( 'Start_RB',current_timestamp) ;
		  -- =============================================================;
		  -- =============================================================
		  -- overnight_NEW processStart
		  --	 PRIMARY KEY ("Chart");
		  --     SET OPTION timestamp_FORMAT = 'yyyy-mm-dd hh:mm:ss.sss';
		  --    alter table overnight_data modify chart not null;
		  --    alter table overnight_data add PRIMARY KEY ("Chart");
		  --    Trigger unload of Old Process data before new process is run
		  --
		delete from overnight_data where
			overnight_data.dochartinfo = any(select distinct DOChartInfo.OID from(
			  dbo.DOChartInfo as DOChartInfo join dbo.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) where
			  dochart.creationdate > @cur_dt - @DaysToProcess) ; --current_date-3);
		  -- =============================================================
		  --set option timestamp_FORMAT = 'yyyy-mm-dd hh:mm:ss.sss';
		  --
		delete from overnight_data where
			overnight_data.chart = any(select distinct DOChartInfo.chart from(
			  dbo.DOChartInfo as DOChartInfo join dbo.overnight_data as overnight_data on DOChartInfo.Chart = overnight_data.chart) where
			  DOChartInfo.GCRecord is not null);
		  -- =============================================================;
		delete from dochartredisplay_tmp;
		insert into dochartredisplay_tmp 
		([GCRecord],[OID],[Chart],[ItemName],[ItemValue],[OptimisticLockField])
		select DOChartredisplay.GCRecord,DOChartredisplay.OID,DOChartredisplay.Chart, DOChartredisplay.ItemName,DOChartredisplay.ItemValue,DOChartredisplay.OptimisticLockField
		from((
		  dbo.DOChartInfo as DOChartInfo join dbo.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
		  dbo.DOChartRedisplay as DOChartRedisplay on DOChartInfo.Chart = DOChartRedisplay.Chart) join
		  dbo.DOChartSummary as DOChartSummary on DOChartInfo.Chart = DOChartSummary.Chart where
		  DOChartInfo.GCRecord is null and dochart.creationdate > @cur_dt - @DaysToProcess;--current_date-3);
		  -- =============================================================
		  -- =============================================================
	
		-- =============================================================
		-- overnight processStart
		insert into Overnight_Data( VisitID,Chart,DOChartInfo,ChartVersion,DoChartCreated,CreationDate,Facility,ValidDates,Arrival_TriagePhysician,Arrival_TriageNurse,Arrival_ProtocolInitiation,Arrival_Treatment,Arrival_PhysicianMSE,Arrival_Decision,Arrival_Boarder,Arrival_Disposition,Arrival_CriticalCareStart,Arrival_CriticalCareEnd,TriagePhysician_TriageNurse,TriagePhysician_ProtocolInitiation,TriagePhysician_Treatment,TriagePhysician_PhysicianMSE,TriagePhysician_Decision,TriagePhysician_Boarder,TriagePhysician_Disposition,TriagePhysician_CriticalCareStart,TriagePhysician_CriticalCareEnd,TriageNurse_ProtocolInitiation,TriageNurse_Treatment,TriageNurse_PhysicianMSE,TriageNurse_Decision,TriageNurse_Boarder,TriageNurse_Disposition,TriageNurse_CriticalCareStart,TriageNurse_CriticalCareEnd,ProtocolInitiation_Treatment,ProtocolInitiation_PhysicianMSE,ProtocolInitiation_Decision,ProtocolInitiation_Boarder,ProtocolInitiation_Disposition,ProtocolInitiation_CriticalCareStart,ProtocolInitiation_CriticalCareEnd,Treatment_PhysicianMSE,Treatment_Decision,Treatment_Boarder,Treatment_Disposition,Treatment_CriticalCareStart,Treatment_CriticalCareEnd,PhysicianMSE_Decision,PhysicianMSE_Boarder,PhysicianMSE_Disposition,PhysicianMSE_CriticalCareStart,PhysicianMSE_CriticalCareEnd,Decision_Boarder,Decision_Disposition,Decision_CriticalCareStart,Decision_CriticalCareEnd,Boarder_Disposition,Boarder_CriticalCareStart,Boarder_CriticalCareEnd,Disposition_CriticalCareStart,Disposition_CriticalCareEnd,CriticalCareStart_CriticalCareEnd) 
		select distinct DOChartInfo.VisitID,DOChartInfo.chart,DOChartInfo.OID,DOChartInfo.ChartVersion,DOChart.creationdate,DOChartInfo.creationdate,DOChartInfo.Facility,
		  DoChartSummary.ValidDates,
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.TriagePhysicianDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.TriageNurseDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.ProtocolInitiationDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.TreatmentDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.PhysicianMSEDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.ArrivalDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.TriageNurseDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.ProtocolInitiationDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.TreatmentDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.PhysicianMSEDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.TriagePhysicianDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.ProtocolInitiationDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.TreatmentDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.PhysicianMSEDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.TriageNurseDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.TreatmentDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.PhysicianMSEDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.ProtocolInitiationDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.PhysicianMSEDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.TreatmentDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.PhysicianMSEDate,DoChartSummary.DecisionDate),
		  datediff(minute,DochartSummary.PhysicianMSEDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.PhysicianMSEDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.PhysicianMSEDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.PhysicianMSEDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.DecisionDate,DoChartSummary.BoarderDate),
		  datediff(minute,DochartSummary.DecisionDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.DecisionDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.DecisionDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.BoarderDate,DoChartSummary.DispositionDate),
		  datediff(minute,DochartSummary.BoarderDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.BoarderDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.DispositionDate,DoChartSummary.CriticalCareStartDate),
		  datediff(minute,DochartSummary.DispositionDate,DoChartSummary.CriticalCareEndDate),
		  datediff(minute,DochartSummary.CriticalCareStartDate,DoChartSummary.CriticalCareEndDate) from((
		  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
		  DBO.DOChartSummary as DOChartSummary on DOChartInfo.Chart = DOChartSummary.Chart) where
		  DOChartInfo.GCRecord is null and dochart.gcrecord is null and dochart.creationdate > @cur_dt - @DaysToProcess;
		  
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (sedation = 'Yes')
		  --
		  update Overnight_Data set
			sedation = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Procedures02_ProceduralSedation_cbo');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = Yes)
		  --
		  update Overnight_Data set
			safetyissues = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'Yes');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = no)
		  --
		  update Overnight_Data set
			safetyissues = 'No' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'No');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = ND)
		  --
		  update Overnight_Data set
			safetyissues = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'n/d');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (ERCode = 'Yes')
		  --
		  update Overnight_Data set
			ercode = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_Code_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Restraints = 'Yes')
		  --
		  update Overnight_Data set
			Restraints = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Psychosocial_Restraints_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (DCVitals = 'Yes')
		  --
		  update Overnight_Data set
			DCVitals = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Disposition_VitalSignsDischarge_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (DCVitals = 'ND')
		  --
		  update Overnight_Data set
			DCVitals = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Disposition_DischargeVitalSignsND_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Pediatric = 'Yes')
		  --
		  update Overnight_Data set
			Pediatric = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_Pediatric_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (EDBoarder = 'Yes')
		  --
		  update Overnight_Data set
			EDBoarder = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Disposition_BoarderStartDate%');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (TraumaTeam = 'Trauma Team List Item')
		  --
		  update Overnight_Data set
			TraumaTeam = LEFT(DOChartRedisplay_Tmp.itemvalue, 30) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_TraumaTeam_cbo') and DOChartRedisplay_Tmp.itemname = 'Triage_TraumaTeam_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (FoleyCath = 'complex')
		  --
		  update Overnight_Data set
			FoleyCath = 'Complex' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_FoleyCatheterComplex_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (FoleyCath = 'simple')
		  --
		  update Overnight_Data set
			FoleyCath = 'Simple' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_FoleyCatheterSimple_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (PsychEval = 'Yes')
		  --
		  update Overnight_Data set
			PsychEval = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Psychosocial_PsychiatricEvaluation_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (PainScale = 'Yes')
		  --
		  update Overnight_Data set
			PainScale = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_PainScale_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (PainScale = 'ND')
		  --
		  update Overnight_Data set
			PainScale = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_PainScaleND_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (LOC = 'Yes')
		  --
		  update Overnight_Data set
			LOC = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_LOC_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (LOC = 'ND')
		  --
		  update Overnight_Data set
			LOC = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_LOCND_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (TriageException = 'Exception List Item')
		  --
		  update Overnight_Data set
			TriageException = LEFT(DOChartRedisplay_Tmp.itemvalue, 50) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_Exception_cbo') and DOChartRedisplay_Tmp.itemname = 'Triage_Exception_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (FallsRisk = 'Yes')
		  --
		  update Overnight_Data set
			FallsRisk = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Psychosocial_RiskOfFalls_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (PsychCode = 'Yes')
		  --
		  update Overnight_Data set
			PsychCode = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Psychosocial_PsychiatricCodeCalled_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (ProcedureTO = 'Yes')
		  --
		  update Overnight_Data set
			ProcedureTO = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures02_ProcedureTimeOut_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (ProcedureTO = 'Yes')
		  --
		  update Overnight_Data set
			ProcedureTO = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures02_ProcedureTimeOut_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (CardiacMonitor = 'Yes')
		  --
		  update Overnight_Data set
			CardiacMonitor = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_CardiacMonitor_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (CPR = 'Yes')
		  --
		  update Overnight_Data set
			CPR = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_CPR_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (NIBP = 'Yes')
		  --
		  update Overnight_Data set
			NIBP = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Assessments_ContNIBP_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (ExtendedStay = 'Yes')
		  --
		  update Overnight_Data set
			ExtendedStay = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Disposition_ExtendedStay_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (TriageVS = 'Yes')
		  --
		  update Overnight_Data set
			TriageVS = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_VS_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (TriageVS = 'ND')
		  --
		  update Overnight_Data set
			TriageVS = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Triage_VitalSignsND_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Smoking = 'Less than 3 minutes')
		  --
		  update Overnight_Data set
			Smoking = 'Less than 3 minutes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Disposition_SmokingCessationLT3_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Smoking = 'Greater than or equal to 3 minutes')
		  --
		  update Overnight_Data set
			Smoking = 'Greater than or equal to 3 minutes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Disposition_SmokingCessationGT3_chk');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (MedicationResponseND = 'Yes')
		  --
		  update Overnight_Data set
			MedicationResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Medication%MedicationsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (ImmunizationResponseND = 'Yes')
		  --
		  update Overnight_Data set
			ImmunizationResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Medication%ImmunizationsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (InjectionResponseND = 'Yes')
		  --
		  update Overnight_Data set
			InjectionResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Medication%InjectionsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (MedTabResponseND = 'Yes')
		  --
		  update Overnight_Data set
			MedTabResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Medication%Response%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (TitratedResponseND = 'Yes')
		  --
		  update Overnight_Data set
			TitratedResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Infusion%TitratedMedicationsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (HydrationResponseND = 'Yes')
		  --
		  update Overnight_Data set
			HydrationResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Infusion%HydrationsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (InfusedResponseND = 'Yes')
		  --
		  update Overnight_Data set
			InfusedResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Infusion%InfusedMedicationsResponse%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (InfusionTabResponseND = 'Yes')
		  --
		  update Overnight_Data set
			InfusionTabResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Infusion%Response%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Med_InfusionTabsResponseND = 'Yes')
		  --
		  update Overnight_Data set
			Med_InfusionTabsResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Medication%Response%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Med_InfusionTabsResponseND = 'Yes')
		  --
		  update Overnight_Data set
			Med_InfusionTabsResponseND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Infusion%Response%' and DOChartRedisplay_Tmp.itemvalue = 'Not Documented');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Seclusion = 'Yes')
		  --
		  update Overnight_Data set
			Seclusion = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Psychosocial_SeclusionSitter_chk');
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Any CC ND = 'Yes')
		  --
		  update Overnight_Data set
			CriticalCAreND = 'Yes' where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  (DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Times_CriticalCareEndND_chk' or
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Times_CriticalCareStartND_chk' or
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Disposition_CCMinutesND_chk' or
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'EMLevel_CCMinutesND_chk'));
		  -- update matching visits linked to chart redisplay table (Any CC ND = 'Yes')
		  --
		  update Overnight_Data set
			CriticalCAreUTD = 'Yes' where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  (DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Disposition_CCMinutesUnableToDetermine_chk' or
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'EMLevel_CCMinutesUnableToDetermine_chk'));
		  -- =============================================================
		  --QI Triage
		  -- update matching visits linked to chart redisplay table (QualityIndicators_Return48_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Triage_Return48 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_Return48_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_Return24_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Triage_Return24 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_Return24_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = Yes)   QI_Triage_Safe_rdogrp
		  --
		  update Overnight_Data set
			QI_Triage_Safe_rdogrp = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'Yes');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = no)   QI_Triage_Safe_rdogrp
		  --
		  update Overnight_Data set
			QI_Triage_Safe_rdogrp = 'No' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'No');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (safety issues = ND)   QI_Triage_Safe_rdogrp
		  --
		  update Overnight_Data set
			QI_Triage_Safe_rdogrp = 'ND' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.ItemName = 'Triage_Safe_rdogrp' and
			  DOChartRedisplay_Tmp.ItemValue = 'n/d');
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_WorkersComp_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Triage_WorkersComp = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_WorkersComp_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  --  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  --  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  --  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  --  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ImmuneStatus_chk = 'Yes')
		  --  --
		  update Overnight_Data set
			QI_Triage_ImmuneStatus = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ImmuneStatus_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_Respiratory_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Triage_Respiratory = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_Respiratory_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  --QI Psychosocial
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_RiskOfFalls_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Psychosocial_RiskOfFalls = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_RiskOfFalls_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_RestraintDocumentationComplete_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Psychosocial_RestraintDocumentationComplete = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_RestraintDocumentationComplete_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_RestraintDocumentationCompleteND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Psychosocial_RestraintDocumentationCompleteND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_RestraintDocumentationCompleteND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_PsychosocialOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Psychosocial_PsychosocialOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_PsychosocialOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_PsychosocialOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Psychosocial_PsychosocialOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_PsychosocialOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Assessment
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_NoReassessments_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Assessment_NoReassessments = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_NoReassessments_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_VitalSignsND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Assessment_VitalSignsND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_VitalSignsND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_AssessmentOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Assessment_AssessmentOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_AssessmentOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_AssessmentOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Assessment_AssessmentOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_AssessmentOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI DISPOSITIONS
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DispositionAssessmentND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DispositionAssessmentND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DispositionAssessmentND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DispositionND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DispositionND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DispositionND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DischargePainAssessmentND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DischargePainAssessmentND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DischargePainAssessmentND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DischargeVitalSignsND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DischargeVitalSignsND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DischargeVitalSignsND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DispositionOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DispositionOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DispositionOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_DispositionOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Disposition_DispositionOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_DispositionOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI TESTS
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_TestND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Tests_TestND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_TestND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_TestsOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Tests_TestsOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_TestsOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_TestsOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Tests_TestsOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_TestsOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Medicaitons
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_RouteUnclear_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_RouteUnclear = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_RouteUnclear_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_TimesND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_TimesND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_TimesND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_UnableToDetermineInjections_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_UnableToDetermineInjections = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_UnableToDetermineInjections_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncompleteNursingDocumentation_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_IncompleteNursingDocumentation = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncompleteNursingDocumentation_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MedicationsOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_MedicationsOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MedicationsOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MedicationsOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Medications_MedicationsOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MedicationsOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Infusions
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncompleteTimes_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Infusions_IncompleteTimes = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncompleteTimes_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncorrectNursingDocumentation_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Infusions_IncorrectNursingDocumentation = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncorrectNursingDocumentation_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_SitesND_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Infusions_SitesND = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_SitesND_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_InfusionsOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Infusions_InfusionsOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_InfusionsOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_InfusionsOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Infusions_InfusionsOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_InfusionsOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  --QI Procedures
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProcedureTimeOut_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProcedureTimeOut = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProcedureTimeOut_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProceduresOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProceduresOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProceduresOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProceduresOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProceduresOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProceduresOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProceduresOption03_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProceduresOption03 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProceduresOption03_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProceduresOption04_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProceduresOption04 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProceduresOption04_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_ProceduresOption05_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Procedures_ProceduresOption05 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_ProceduresOption05_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Ortho
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncompleteOrthoNote_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_IncompleteOrthoNote = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncompleteOrthoNote_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_OrthoOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_OrthoOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_OrthoOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_OrthoOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_OrthoOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_OrthoOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_OrthoOption03_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_OrthoOption03 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_OrthoOption03_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_OrthoOption04_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_OrthoOption04 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_OrthoOption04_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_OrthoOption05_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Ortho_OrthoOption05 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_OrthoOption05_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Surgical
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncompletedLacerationNote_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Surgical_IncompletedLacerationNote = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncompletedLacerationNote_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_IncompletedFBIDNote_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Surgical_IncompletedFBIDNote = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_IncompletedFBIDNote_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_SurgicalOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Surgical_SurgicalOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_SurgicalOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_SurgicalOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Surgical_SurgicalOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_SurgicalOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_SurgicalOption03_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Surgical_SurgicalOption03 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_SurgicalOption03_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- QI Misc
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption01_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption01 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption01_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption02_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption02 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption02_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption03_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption03 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption03_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption04_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption04 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption04_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption05_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption05 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption05_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (QualityIndicators_MiscOption06_chk = 'Yes')
		  --
		  update Overnight_Data set
			QI_Misc_MiscOption06 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'QualityIndicators_MiscOption06_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- QI Audit
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Last Audited By = 'Miscellaneous_AC_LastAuditedBy_txt')
		  --
		  update Overnight_Data set
			Audit_LastAuditedBy = LEFT(DOChartRedisplay_Tmp.itemvalue, 128) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_LastAuditedBy_txt') and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_LastAuditedBy_txt';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Audit Reason 1 = 'Audit Reason 1 List Item')
		  --
		  update Overnight_Data set
			Audit_AuditReason1 = LEFT(DOChartRedisplay_Tmp.itemvalue, 128) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason01_cbo') and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason01_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Audit Reason 2 = 'Audit Reason 2 List Item')
		  --
		  update Overnight_Data set
			Audit_AuditReason2 = LEFT(DOChartRedisplay_Tmp.itemvalue, 128) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason02_cbo') and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason02_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Audit Reason 3 = 'Audit Reason 3 List Item')
		  --
		  update Overnight_Data set
			Audit_AuditReason3 = LEFT(DOChartRedisplay_Tmp.itemvalue, 128) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason03_cbo') and DOChartRedisplay_Tmp.itemname = 'Miscellaneous_AC_Reason03_cbo';
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (SexualAssault = 'Yes')
		  --
		  update Overnight_Data set
			SexualAssault = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Procedures01_SexualAssaultExam_chk');
		  -- =============================================================
		  -- =============================================================
		  -- =============================================================
		  -- =============================================================
		  -- Physician_Items  Overnight_NEW
		  -- =============================================================
		  -- =============================================================
		  update Overnight_Data set
			Physician_EM = DOCodingReportRecord.hcpcs from DoCodingReportRecord join Overnight_Data on DOCodingReportRecord.chart = Overnight_Data.chart where
			DOCodingReportRecord.HCPCS like '992__-__' and DOCodingReportRecord.IsPhysicianTypeCode = 1 and
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOCodingReportRecord as DOCodingReportRecord on DOChartInfo.Chart = DOCodingReportRecord.Chart) where
			  DOChartInfo.GCRecord is null);
		  -- =============================================================
		  -- =============================================================
		  --====  Physican calculated EM level HCPCS if one exists
		  -- =============================================================
		  update Overnight_Data set
			Physician_EM_Calc = LEFT(DOChartRedisplay_Tmp.itemvalue, 25) from DOChartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartredisplay_tmp as DOChartredisplay_tmp on DOChartInfo.Chart = DOChartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartredisplay_tmp.itemname = 'Physician_Summary_EMLevel_txt') and DOChartredisplay_tmp.itemname = 'Physician_Summary_EMLevel_txt';
		  -- =============================================================
		  -- =============================================================
		  --====  Physican Override EM level HCPCS if one exists
		  -- =============================================================
		  update Overnight_Data set
			Physician_EM_Override = LEFT(DOChartRedisplay_Tmp.itemvalue, 25) from DOChartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartredisplay_tmp as DOChartredisplay_tmp on DOChartInfo.Chart = DOChartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartredisplay_tmp.itemname = 'Physician_Summary_EMLevelOverride_cbo') and DOChartredisplay_tmp.itemname = 'Physician_Summary_EMLevelOverride_cbo';
		  -- =============================================================
		  -- =============================================================
		  --====  Physican Exception description if one exists
		  -- =============================================================
		  update Overnight_Data set
			Physician_Exception = LEFT(DOChartRedisplay_Tmp.itemvalue, 25) from DOChartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartredisplay_tmp as DOChartredisplay_tmp on DOChartInfo.Chart = DOChartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartredisplay_tmp.itemname = 'Physician_Summary_Exception_cbo') and DOChartredisplay_tmp.itemname = 'Physician_Summary_Exception_cbo';
		  -- =============================================================
		  --====  Physican Critical Care Minutes
		  -- =============================================================
		  update Overnight_Data set
			Physician_CC_Minutes = DOChartRedisplay_tmp.itemvalue from DOChartRedisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_tmp as DOChartRedisplay_tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_tmp.itemname = 'Physician_History_CCMinutes_txt') and DOChartRedisplay_tmp.itemname = 'Physician_History_CCMinutes_txt';
		  -- =============================================================
		  -- Physician_Summary_QIs
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_ChargesImpacted_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_ChargesImpacted = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_ChargesImpacted_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_ChartNotSigned_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_ChartNotSigned = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_ChartNotSigned_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteHPI_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteHPI = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteHPI_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteLaceration_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteLaceration = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteLaceration_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteMDM_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteMDM = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteMDM_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteOrtho_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteOrtho = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteOrtho_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteOtherSurgical_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteOtherSurgical = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteOtherSurgical_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompletePE_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompletePE = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompletePE_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompletePFSH_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompletePFSH = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompletePFSH_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_IncompleteROS_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_IncompleteROS = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_IncompleteROS_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_NoChiefComplaint_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_NoChiefComplaint = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_NoChiefComplaint_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_NoDiagnosisListed_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_NoDiagnosisListed = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_NoDiagnosisListed_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- Physician_Summary_QI_Option[1-6]
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option1_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option1 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option1_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option2_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option2 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option2_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option3_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option3 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option3_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option4_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option4 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option4_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option5_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option5 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option5_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table (Physician_Summary_QI_Option6_chk = 'Yes')
		  --
		  update Overnight_Data set
			Physician_Summary_QI_Option6 = 'Yes' where
			Overnight_data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname like 'Physician_Summary_QI_Option6_chk' and DOChartRedisplay_Tmp.itemvalue = 'True');
		  -- =============================================================  
		  -- ==== END OF PHYSICIAN QIs  ==================================
		  -- =============================================================
		  -- =============================================================
		  -- =============================================================
		  -- update visits with DOB
		  update Overnight_Data set
			DateOfBirth = LEFT(DOChartRedisplay_Tmp.itemvalue, 10) from DOChartRedisplay_Tmp join Overnight_Data on DOChartRedisplay_Tmp.chart = Overnight_Data.chart where
			Overnight_Data.chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Demographics_DOB_txt') and DOChartRedisplay_Tmp.itemname = 'Demographics_DOB_txt';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table Quantity for Infusion_QI_TotalInfusNotCoded_cbo
		  update Overnight_Data set
			overnight_data.QI_Infusion_QI_TotalInfusNotCoded = DOChartRedisplay_tmp.itemvalue from
			Overnight_Data,DOChartRedisplay_tmp where
			Overnight_Data.chart = DOChartRedisplay_tmp.Chart and
			DOChartRedisplay_tmp.ItemName = 'Infusion_QI_TotalInfusNotCoded_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table Quantity for Infusion_QI_TotalInfusCodedAsIVP_cbo
		  update Overnight_Data set
			overnight_data.QI_Infusion_QI_TotalInfusCodedAsIVP = DOChartRedisplay_tmp.itemvalue from
			Overnight_Data,DOChartRedisplay_tmp where
			Overnight_Data.chart = DOChartRedisplay_tmp.Chart and
			DOChartRedisplay_tmp.ItemName = 'Infusion_QI_TotalInfusCodedAsIVP_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update matching visits linked to chart dochartsummary convert Disposition Hour to Integer
		  update Overnight_Data set
			overnight_data.DispoHour = CAST(SUBSTRING(convert(varchar(8), DispositionDate, 108),0,3) As Int) FROM -- cast(SUBSTRING(Dochartsummary.dispositiondate,12,2) as integer) from
			Overnight_Data,Dochartsummary where
			Overnight_Data.chart = Dochartsummary.Chart and
			Dochartsummary.ValidDates like '________1______';
		  -- ============================================================= 
		  --
		  -- =============================================================
		  -- update matching visits linked to chart redisplay table Tests_Hemocult_chk = Yes
		  --
		  update Overnight_Data set
			Hemocult = 'Yes' where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Tests_Hemocult_chk');
		  -- =============================================================
		  -- =============================================================
		  --
		  -- update matching visits linked to chart redisplay table Procedures01_CentralLineGTE5_chk = Yes
		  --
		  update Overnight_Data set
			CentralLineGTE5 = 'Yes' where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_CentralLineGTE5_chk');
		  -- =============================================================
		  --Procedures01_CentralLineLT5_chk
		  -- update matching visits linked to chart redisplay table Procedures01_CentralLineGTE5_chk = Yes
		  --
		  update Overnight_Data set
			CentralLineLT5 = 'Yes' where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_Tmp.Chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'Procedures01_CentralLineLT5_chk');
		  -- =============================================================
		  -- =============================================================
		  --
		  -- update matching visits linked to chart redisplay item Procedures01_Intubation_cbo
		  --
		  update Overnight_Data set 
		  Intubation = DOChartRedisplay.itemvalue where 
		  Overnight_Data.Chart = 
		  any(select DOChartInfo.Chart from((
			DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			DBA.DOChartRedisplay as DOChartRedisplay on DOChartInfo.Chart = DOChartRedisplay.Chart) where
			DOChartInfo.GCRecord is null and DOChartRedisplay.itemname = 'Procedures01_Intubation_cbo') and DOChartRedisplay.itemname = 'Procedures01_Intubation_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Physician
		  update Overnight_Data set
			Observation_Physician = LEFT(DOChartRedisplay_Tmp.itemvalue, 50) from DOChartRedisplay_Tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursPhys_cbo') and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursPhys_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Disposition
		  update Overnight_Data set
			Observation_Disposition = LEFT(DOChartRedisplay_Tmp.itemvalue, 50) from DOChartRedisplay_Tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursDispo_cbo') and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursDispo_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Point of Entry
		  update Overnight_Data set
			Observation_POE = LEFT(DOChartRedisplay_Tmp.itemvalue, 15) from DOChartRedisplay_Tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'ObsTimesPoeSelect_rdogrp_cbo') and DOChartRedisplay_Tmp.itemname = 'ObsTimesPoeSelect_rdogrp';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Reportable Units
		  update Overnight_Data set
			Observation_ReportableUnits = LEFT(DOChartRedisplay_Tmp.itemvalue, 5) from DOChartRedisplay_Tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursRepUnits_txt') and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursRepUnits_txt';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Diagnosis Category
		  update Overnight_Data set
			Observation_DiagnosisCategory = LEFT(DOChartRedisplay_Tmp.itemvalue, 15) from DOChartRedisplay_Tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.DOChartRedisplay_Tmp as DOChartRedisplay_Tmp on DOChartInfo.Chart = DOChartRedisplay_tmp.chart) where
			  DOChartInfo.GCRecord is null and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursDiagCat_cbo') and DOChartRedisplay_Tmp.itemname = 'ObsTimesFinalBillHoursDiagCat_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- =============================================================
		  --            OBSERVATION
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Physician
		  update Overnight_Data set
			Observation_Physician = LEFT(DOChartRedisplay_Tmp.itemvalue, 50) from dochartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.dochartredisplay_tmp as dochartredisplay_tmp on DOChartInfo.Chart = dochartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursPhys_cbo') and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursPhys_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Disposition
		  update Overnight_Data set
			Observation_Disposition = LEFT(DOChartRedisplay_Tmp.itemvalue, 50) from dochartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.dochartredisplay_tmp as dochartredisplay_tmp on DOChartInfo.Chart = dochartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursDispo_cbo') and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursDispo_cbo';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Point of Entry
		  update Overnight_Data set
			Observation_POE = LEFT(DOChartRedisplay_Tmp.itemvalue, 15) from dochartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.dochartredisplay_tmp as dochartredisplay_tmp on DOChartInfo.Chart = dochartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and dochartredisplay_tmp.itemname = 'ObsTimesPoeSelect_rdogrp') and dochartredisplay_tmp.itemname = 'ObsTimesPoeSelect_rdogrp';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Reportable Units
		  update Overnight_Data set
			Observation_ReportableUnits = LEFT(DOChartRedisplay_Tmp.itemvalue, 5) from dochartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.dochartredisplay_tmp as dochartredisplay_tmp on DOChartInfo.Chart = dochartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursRepUnits_txt') and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursRepUnits_txt';
		  -- =============================================================
		  -- =============================================================
		  -- update visits with Observation Diagnosis Category
		  update Overnight_Data set
			Observation_DiagnosisCategory = LEFT(DOChartRedisplay_Tmp.itemvalue, 15) from dochartredisplay_tmp join Overnight_Data on dochartredisplay_tmp.chart = Overnight_Data.chart where
			Overnight_Data.Chart = 
			any(select DOChartInfo.Chart from((
			  DBO.DOChartInfo as DOChartInfo join DBO.DOChart as DOChart on DOChartInfo.Chart = DOChart.OID) join
			  DBO.dochartredisplay_tmp as dochartredisplay_tmp on DOChartInfo.Chart = dochartredisplay_tmp.Chart) where
			  DOChartInfo.GCRecord is null and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursDiagCat_cbo') and dochartredisplay_tmp.itemname = 'ObsTimesFinalBillHoursDiagCat_cbo';
		  -- =============================================================
		  -- =============================================================
		  insert into Overnight_Data_Timer values( 'Stop_RB',current_timestamp) ;
		  insert into Overnight_Data_Timer values( '-',current_timestamp) ;
		  -- =============================================================
		  --trigger event Overnight_Grid_New;
		  --waitfor delay '00:00:03';
		  --trigger event Overnight_OBS_New;
		  -- =============================================================
		  --waitfor delay '00:00:03';
		  --trigger event Overnight_HCPCS_Count
			COMMIT;
		END TRY
------------------------
----CATCH ERROR HANDLING
------------------------
BEGIN CATCH
	EXEC dbo.usp_GetErrorInfo;  -- raises the error that was generated and formats the error message to be more readable
	IF @@TRANCOUNT > 0 BEGIN ROLLBACK TRANSACTION END;
	BEGIN TRANSACTION
	  insert into Overnight_Data_Timer values( '-',current_timestamp) ;
	  insert into Overnight_Data_Timer values( 'ERR_RB',current_timestamp) 
	COMMIT;
	RETURN -1;	-- (if multiple tables, may want to return different negative values - i.e., -1 for table A fails, -2 if table B fails)
END CATCH

GO
----------------------------------------------------------------------------------------------
-- Add procedure description entry for data dictionary
if NOT EXISTS (SELECT 1 FROM sys.objects o WHERE o.name = 'usp_overnight')
	EXEC sp_addextendedproperty 
	@name = N'MS_Description',
	@value = 'OvernightProcessing',
	@level0type = N'Schema',
	@level0name = N'dbo',
	@level1type = N'Procedure',
	@level1name = N'usp_overnight';

GO
----------------------------------------------------------------------------------------------
GRANT EXECUTE ON dbo.usp_overnight TO public;
GO

﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configurations>Debug;Release</Configurations>
    <Platforms>AnyCPU;x86</Platforms>
    <TargetFramework>net6.0-windows</TargetFramework>
    <OutputType>Library</OutputType>
    <StartupObject>CDMReports.My.MyApplication</StartupObject>
    <RootNamespace>CDMReports</RootNamespace>
    <AssemblyName>CDMViewer</AssemblyName>
    <MyType>WindowsForms</MyType>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>true</UseWindowsForms>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
  </PropertyGroup>
  <!--<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DatabaseMigration|AnyCPU'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\DatabaseMigration\</OutputPath>
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DatabaseMigration|x86'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x86\DatabaseMigration\</OutputPath>
    <DocumentationFile>CDMViewer.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>-->
  <ItemGroup>
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="CDMEdit\CDMEditDialog.Designer.vb">
      <DependentUpon>CDMEditDialog.vb</DependentUpon>
    </Compile>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Timeout\Timeout\Timeout.vbproj" />
    <ProjectReference Include="..\EnchartDOLib\EnchartDOLib.vbproj" />
    <ProjectReference Include="..\MICCustomDataProviders\MICCustomConnectionProviders.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DevExpress.Win.Design" Version="22.1.6" />
    <PackageReference Include="DevExpress.Xpo">
      <Version>22.1.6</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.355802">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  <!--<PropertyGroup>
    <PostBuildEvent>copy "$(TargetPath)"  "C:\apps\ECCoder\$(TargetFileName)"</PostBuildEvent>
  </PropertyGroup>-->
</Project>
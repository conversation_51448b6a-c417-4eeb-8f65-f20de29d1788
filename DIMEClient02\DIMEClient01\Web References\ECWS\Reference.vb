﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.ComponentModel
Imports System.Diagnostics
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Xml.Serialization

'
'This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
'
Namespace ECWS
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "********"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Web.Services.WebServiceBindingAttribute(Name:="ServiceSoap", [Namespace]:="http://EN-Chart.com/")>  _
    Partial Public Class Service
        Inherits Microsoft.Web.Services2.WebServicesClientProtocol

        Private ENSetLevelerInformationOperationCompleted As System.Threading.SendOrPostCallback
        
        Private sendChartOperationCompleted As System.Threading.SendOrPostCallback
        
        Private useDefaultCredentialsSetExplicitly As Boolean

        '''<remarks/>
        Public Sub New()
            MyBase.New
            Me.Url = Global.DIMEClient01.My.MySettings.Default.DIMEClient01_localhost_Service
            If (Me.IsLocalFileSystemWebService(Me.Url) = True) Then
                Me.UseDefaultCredentials = True
                Me.useDefaultCredentialsSetExplicitly = False
            Else
                Me.useDefaultCredentialsSetExplicitly = True
            End If
        End Sub
        Public Sub New(ByVal passed_url As String)
            Me.New()
            Me.Url = passed_url
        End Sub

        Public Shadows Property Url() As String
            Get
                Return MyBase.Url
            End Get
            Set
                If (((Me.IsLocalFileSystemWebService(MyBase.Url) = true)  _
                            AndAlso (Me.useDefaultCredentialsSetExplicitly = false))  _
                            AndAlso (Me.IsLocalFileSystemWebService(value) = false)) Then
                    MyBase.UseDefaultCredentials = false
                End If
                MyBase.Url = value
            End Set
        End Property
        
        Public Shadows Property UseDefaultCredentials() As Boolean
            Get
                Return MyBase.UseDefaultCredentials
            End Get
            Set
                MyBase.UseDefaultCredentials = value
                Me.useDefaultCredentialsSetExplicitly = true
            End Set
        End Property
        
        '''<remarks/>
        Public Event ENSetLevelerInformationCompleted As ENSetLevelerInformationCompletedEventHandler
        
        '''<remarks/>
        Public Event sendChartCompleted As sendChartCompletedEventHandler
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://EN-Chart.com/ENSetLevelerInformation", RequestNamespace:="http://EN-Chart.com/", ResponseNamespace:="http://EN-Chart.com/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function ENSetLevelerInformation(ByVal hostName As String, ByVal portNum As String) As Boolean
            Dim results() As Object = Me.Invoke("ENSetLevelerInformation", New Object() {hostName, portNum})
            Return CType(results(0),Boolean)
        End Function
        
        '''<remarks/>
        Public Overloads Sub ENSetLevelerInformationAsync(ByVal hostName As String, ByVal portNum As String)
            Me.ENSetLevelerInformationAsync(hostName, portNum, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub ENSetLevelerInformationAsync(ByVal hostName As String, ByVal portNum As String, ByVal userState As Object)
            If (Me.ENSetLevelerInformationOperationCompleted Is Nothing) Then
                Me.ENSetLevelerInformationOperationCompleted = AddressOf Me.OnENSetLevelerInformationOperationCompleted
            End If
            Me.InvokeAsync("ENSetLevelerInformation", New Object() {hostName, portNum}, Me.ENSetLevelerInformationOperationCompleted, userState)
        End Sub
        
        Private Sub OnENSetLevelerInformationOperationCompleted(ByVal arg As Object)
            If (Not (Me.ENSetLevelerInformationCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent ENSetLevelerInformationCompleted(Me, New ENSetLevelerInformationCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://EN-Chart.com/sendChart", RequestNamespace:="http://EN-Chart.com/", ResponseNamespace:="http://EN-Chart.com/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function sendChart(ByVal patChartInfo As ENPatientChartingInfo, ByVal patDemoInfo As ENPatientDemographicInfo) As ENCodingResult
            Dim results() As Object = Me.Invoke("sendChart", New Object() {patChartInfo, patDemoInfo})
            Return CType(results(0),ENCodingResult)
        End Function
        
        '''<remarks/>
        Public Overloads Sub sendChartAsync(ByVal patChartInfo As ENPatientChartingInfo, ByVal patDemoInfo As ENPatientDemographicInfo)
            Me.sendChartAsync(patChartInfo, patDemoInfo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub sendChartAsync(ByVal patChartInfo As ENPatientChartingInfo, ByVal patDemoInfo As ENPatientDemographicInfo, ByVal userState As Object)
            If (Me.sendChartOperationCompleted Is Nothing) Then
                Me.sendChartOperationCompleted = AddressOf Me.OnsendChartOperationCompleted
            End If
            Me.InvokeAsync("sendChart", New Object() {patChartInfo, patDemoInfo}, Me.sendChartOperationCompleted, userState)
        End Sub
        
        Private Sub OnsendChartOperationCompleted(ByVal arg As Object)
            If (Not (Me.sendChartCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent sendChartCompleted(Me, New sendChartCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        Public Shadows Sub CancelAsync(ByVal userState As Object)
            MyBase.CancelAsync(userState)
        End Sub
        
        Private Function IsLocalFileSystemWebService(ByVal url As String) As Boolean
            If ((url Is Nothing)  _
                        OrElse (url Is String.Empty)) Then
                Return false
            End If
            Dim wsUri As System.Uri = New System.Uri(url)
            If ((wsUri.Port >= 1024)  _
                        AndAlso (String.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) = 0)) Then
                Return true
            End If
            Return false
        End Function
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0"),  _
     System.SerializableAttribute(),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Xml.Serialization.XmlTypeAttribute([Namespace]:="http://EN-Chart.com/")>  _
    Partial Public Class ENPatientChartingInfo
        
        Private facilityField As String
        
        Private treatmentAreaField As String
        
        Private accountNoField As String
        
        Private dateOfServiceField As String
        
        Private chartViewURLField As String
        
        '''<remarks/>
        Public Property facility() As String
            Get
                Return Me.facilityField
            End Get
            Set
                Me.facilityField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property treatmentArea() As String
            Get
                Return Me.treatmentAreaField
            End Get
            Set
                Me.treatmentAreaField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property accountNo() As String
            Get
                Return Me.accountNoField
            End Get
            Set
                Me.accountNoField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property dateOfService() As String
            Get
                Return Me.dateOfServiceField
            End Get
            Set
                Me.dateOfServiceField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property chartViewURL() As String
            Get
                Return Me.chartViewURLField
            End Get
            Set
                Me.chartViewURLField = value
            End Set
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0"),  _
     System.SerializableAttribute(),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Xml.Serialization.XmlTypeAttribute([Namespace]:="http://EN-Chart.com/")>  _
    Partial Public Class ENCodingResult
        
        Private resultCodeField As Integer
        
        Private resultTextField As String
        
        '''<remarks/>
        Public Property resultCode() As Integer
            Get
                Return Me.resultCodeField
            End Get
            Set
                Me.resultCodeField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property resultText() As String
            Get
                Return Me.resultTextField
            End Get
            Set
                Me.resultTextField = value
            End Set
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0"),  _
     System.SerializableAttribute(),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Xml.Serialization.XmlTypeAttribute([Namespace]:="http://EN-Chart.com/")>  _
    Partial Public Class ENPatientDemographicInfo
        
        Private firstNameField As String
        
        Private middleNameField As String
        
        Private lastNameField As String
        
        Private suffixNameField As String
        
        Private dOBField As String
        
        Private genderField As String
        
        Private mRNField As String
        
        Private ssField As String
        
        Private financialClassField As String
        
        Private addressLine1Field As String
        
        Private addressLine2Field As String
        
        Private cityField As String
        
        Private stateField As String
        
        Private zipCodeField As String
        
        '''<remarks/>
        Public Property firstName() As String
            Get
                Return Me.firstNameField
            End Get
            Set
                Me.firstNameField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property middleName() As String
            Get
                Return Me.middleNameField
            End Get
            Set
                Me.middleNameField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property lastName() As String
            Get
                Return Me.lastNameField
            End Get
            Set
                Me.lastNameField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property suffixName() As String
            Get
                Return Me.suffixNameField
            End Get
            Set
                Me.suffixNameField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property DOB() As String
            Get
                Return Me.dOBField
            End Get
            Set
                Me.dOBField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property gender() As String
            Get
                Return Me.genderField
            End Get
            Set
                Me.genderField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property MRN() As String
            Get
                Return Me.mRNField
            End Get
            Set
                Me.mRNField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property SS() As String
            Get
                Return Me.ssField
            End Get
            Set
                Me.ssField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property financialClass() As String
            Get
                Return Me.financialClassField
            End Get
            Set
                Me.financialClassField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property addressLine1() As String
            Get
                Return Me.addressLine1Field
            End Get
            Set
                Me.addressLine1Field = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property addressLine2() As String
            Get
                Return Me.addressLine2Field
            End Get
            Set
                Me.addressLine2Field = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property city() As String
            Get
                Return Me.cityField
            End Get
            Set
                Me.cityField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property state() As String
            Get
                Return Me.stateField
            End Get
            Set
                Me.stateField = value
            End Set
        End Property
        
        '''<remarks/>
        Public Property zipCode() As String
            Get
                Return Me.zipCodeField
            End Get
            Set
                Me.zipCodeField = value
            End Set
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "********")>  _
    Public Delegate Sub ENSetLevelerInformationCompletedEventHandler(ByVal sender As Object, ByVal e As ENSetLevelerInformationCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "********"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class ENSetLevelerInformationCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As Boolean
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),Boolean)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "********")>  _
    Public Delegate Sub sendChartCompletedEventHandler(ByVal sender As Object, ByVal e As sendChartCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "********"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class sendChartCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As ENCodingResult
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),ENCodingResult)
            End Get
        End Property
    End Class
End Namespace

﻿Imports System.Windows.Forms

Public Class RemoveFieldDialog

    Public Sub New(ByVal fields As List(Of String))

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        cboFieldMappingName.Properties.Items.AddRange(fields)
        cboFieldMappingName.SelectedIndex = 0
    End Sub

    Public ReadOnly Property FieldMappingName
        Get
            Return cboFieldMappingName.SelectedItem
        End Get
    End Property

    Private Sub OK_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OK_Button.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub
End Class

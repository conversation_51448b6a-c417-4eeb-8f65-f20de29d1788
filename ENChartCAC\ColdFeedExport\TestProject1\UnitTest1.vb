﻿Imports System
Imports System.Text
Imports System.Collections.Generic
Imports Microsoft.VisualStudio.TestTools.UnitTesting
Imports McKesson.HIC.ColdFeed
Imports McKesson.HIC.ColdFeed.ColdFeedHelper
Imports EnchartDOLib
Imports DevExpress
Imports DevExpress.Xpo
Imports DevExpress.Data
Imports System.Configuration
Imports DevExpress.Xpo.DB
Imports System.IO

'Imports McKesson.HIC.ColdFeed.



<TestClass()> Public Class UnitTest1

    Private testContextInstance As TestContext

    '''<summary>
    '''Gets or sets the test context which provides
    '''information about and functionality for the current test run.
    '''</summary>
    Public Property TestContext() As TestContext
        Get
            Return testContextInstance
        End Get
        Set(ByVal value As TestContext)
            testContextInstance = Value
        End Set
    End Property

#Region "Additional test attributes"
    '
    ' You can use the following additional attributes as you write your tests:
    '
    ' Use ClassInitialize to run code before running the first test in the class
    ' <ClassInitialize()> Public Shared Sub MyClassInitialize(ByVal testContext As TestContext)
    ' End Sub
    '
    ' Use ClassCleanup to run code after all tests in a class have run
    ' <ClassCleanup()> Public Shared Sub MyClassCleanup()
    ' End Sub
    '
    ' Use TestInitialize to run code before running each test
    ' <TestInitialize()> Public Sub MyTestInitialize()
    ' End Sub
    '
    ' Use TestCleanup to run code after each test has run
    ' <TestCleanup()> Public Sub MyTestCleanup()
    ' End Sub
    '
#End Region

    'JJC - Blah, I commented out this test because the "current" directory seems to change depending on 
    'the type of test being run... so when calling GetMappedFieldsDict, it won't fine needed configuration files
    'that live in the c:\apps\eccodeer dir.

    '<TestMethod()> Public Sub TestMGetFieldMappedValues()
    '    Dim i = 1 + 1
    '    ConnectToECDataBase()

    '    'Const STR_ColdFeedFieldExportMapsxml As String = "ColdFeedFieldExportMaps.xml"
    '    'If Not File.Exists(Path.Combine(My.Application.Info.DirectoryPath, STR_ColdFeedFieldExportMapsxml)) Then
    '    '    File.Copy(Path.Combine("c:\apps\eccoder", STR_ColdFeedFieldExportMapsxml), _
    '    '              Path.Combine(My.Application.Info.DirectoryPath, STR_ColdFeedFieldExportMapsxml))
    '    'End If

    '    Dim cfh As New ColdFeedHelper(New HPFColdFeedImageFileHandler, _
    '                                  New HPFColdFeedDetailsFileHandler, _
    '                                  New DefaulGetChartsToExport, _
    '                                  New DefaultLog, _
    '                                  New DefaultShowMessage, "..\", "..\")

    '    Dim chart As New DOChart()
    '    '= DOChart.GetChartByOid(1000)
    '    chart.ChartInfo = New DOChartInfo()
    '    'chart.ChartInfo.Facility = New DOFacility() With {.Oid = 7}
    '    chart.ChartInfo.Facility = GetFacility(7)
    '    chart.ChartInfo.VisitID = "ItWorked"

    '    'Dim dtoIn As New DTOGetFieldMappedValuesInput(chart)
    '    'dtoIn.MappingList.Add(New DTOGetFieldMappedValuesInput.Fields(chart.ChartInfo.Facility.Oid, "DOChartInfo", "VisitID", "Encounter"))
    '    'Dim dtoOut = cfh.GetFieldMappedValues(dtoIn)

    '    cfh.InitForFacility(chart.ChartInfo.Facility)

    '    Dim dict = cfh.GetMappedFieldsDict(New ChartObjWrapper(chart))
    '    ' cfh.o()
    '    Dim s As String = dict("Encounter")


    '    'End Using
    '    Assert.AreEqual("ItWorked", dict("Encounter"))
    'End Sub

    'Shared Sub ConnectToECDataBase()
    '    Dim fm As New System.Configuration.ExeConfigurationFileMap() With {.ExeConfigFilename = "c:\apps\eccoder\ENChartCAC.exe.config"}

    '    Dim config As Configuration = ConfigurationManager.OpenMappedExeConfiguration(fm, ConfigurationUserLevel.None)
    '    'Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
    '    Dim cs As String = config.ConnectionStrings.ConnectionStrings("ENChartCAC.My.MySettings.DefualtConnectionString").ConnectionString
    '    cs += ";uid=dba;pwd=sql"

    '    XpoDefault.DataLayer = XpoDefault.GetDataLayer(cs, AutoCreateOption.DatabaseAndSchema)
    'End Sub

End Class

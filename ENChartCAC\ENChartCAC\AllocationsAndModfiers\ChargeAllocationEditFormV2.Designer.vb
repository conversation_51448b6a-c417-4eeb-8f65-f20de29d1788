﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ChargeAllocationEditFormV2
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.ChargeBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.ChargeAllocationsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.ChargeMasterTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CodeTypeTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DescriptionTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ESPValueTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.HcpcsTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OriginalBillingdateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.TotalUnitsSpinEdit = New DevExpress.XtraEditors.SpinEdit()
        Me.TreatmentAreaTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ChargeAllocationsGridControl = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colHcpcs = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTreatmentArea = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUnits = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUserDefinedBillingdate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutConverter1 = New DevExpress.XtraLayout.Converter.LayoutConverter(Me.components)
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.ChargeBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChargeAllocationsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChargeMasterTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CodeTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DescriptionTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ESPValueTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.HcpcsTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OriginalBillingdateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OriginalBillingdateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TotalUnitsSpinEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TreatmentAreaTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChargeAllocationsGridControl, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        Me.SuspendLayout()
        '
        'ChargeBindingSource
        '
        Me.ChargeBindingSource.DataSource = GetType(AIC.SharedData.CAM2.Charge)
        '
        'ChargeAllocationsBindingSource
        '
        Me.ChargeAllocationsBindingSource.DataMember = "ChargeAllocations"
        Me.ChargeAllocationsBindingSource.DataSource = Me.ChargeBindingSource
        '
        'ChargeMasterTextEdit
        '
        Me.ChargeMasterTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "ChargeMaster", True))
        Me.ChargeMasterTextEdit.Location = New System.Drawing.Point(96, 2)
        Me.ChargeMasterTextEdit.Name = "ChargeMasterTextEdit"
        Me.ChargeMasterTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.ChargeMasterTextEdit.TabIndex = 2
        '
        'CodeTypeTextEdit
        '
        Me.CodeTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "CodeType", True))
        Me.CodeTypeTextEdit.Location = New System.Drawing.Point(96, 26)
        Me.CodeTypeTextEdit.Name = "CodeTypeTextEdit"
        Me.CodeTypeTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.CodeTypeTextEdit.TabIndex = 4
        '
        'DescriptionTextEdit
        '
        Me.DescriptionTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "Description", True))
        Me.DescriptionTextEdit.Location = New System.Drawing.Point(96, 50)
        Me.DescriptionTextEdit.Name = "DescriptionTextEdit"
        Me.DescriptionTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.DescriptionTextEdit.TabIndex = 6
        '
        'ESPValueTextEdit
        '
        Me.ESPValueTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "ESPValue", True))
        Me.ESPValueTextEdit.Location = New System.Drawing.Point(96, 74)
        Me.ESPValueTextEdit.Name = "ESPValueTextEdit"
        Me.ESPValueTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.ESPValueTextEdit.TabIndex = 8
        '
        'HcpcsTextEdit
        '
        Me.HcpcsTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "Hcpcs", True))
        Me.HcpcsTextEdit.Location = New System.Drawing.Point(96, 98)
        Me.HcpcsTextEdit.Name = "HcpcsTextEdit"
        Me.HcpcsTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.HcpcsTextEdit.TabIndex = 10
        '
        'OriginalBillingdateDateEdit
        '
        Me.OriginalBillingdateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "OriginalBillingdate", True))
        Me.OriginalBillingdateDateEdit.EditValue = Nothing
        Me.OriginalBillingdateDateEdit.Location = New System.Drawing.Point(96, 122)
        Me.OriginalBillingdateDateEdit.Name = "OriginalBillingdateDateEdit"
        Me.OriginalBillingdateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.OriginalBillingdateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.OriginalBillingdateDateEdit.Size = New System.Drawing.Size(223, 20)
        Me.OriginalBillingdateDateEdit.TabIndex = 12
        '
        'TotalUnitsSpinEdit
        '
        Me.TotalUnitsSpinEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "TotalUnits", True))
        Me.TotalUnitsSpinEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.TotalUnitsSpinEdit.Location = New System.Drawing.Point(96, 146)
        Me.TotalUnitsSpinEdit.Name = "TotalUnitsSpinEdit"
        Me.TotalUnitsSpinEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TotalUnitsSpinEdit.Size = New System.Drawing.Size(223, 20)
        Me.TotalUnitsSpinEdit.TabIndex = 14
        '
        'TreatmentAreaTextEdit
        '
        Me.TreatmentAreaTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.ChargeBindingSource, "TreatmentArea", True))
        Me.TreatmentAreaTextEdit.Location = New System.Drawing.Point(96, 170)
        Me.TreatmentAreaTextEdit.Name = "TreatmentAreaTextEdit"
        Me.TreatmentAreaTextEdit.Size = New System.Drawing.Size(223, 20)
        Me.TreatmentAreaTextEdit.TabIndex = 16
        '
        'ChargeAllocationsGridControl
        '
        Me.ChargeAllocationsGridControl.DataSource = Me.ChargeAllocationsBindingSource
        Me.ChargeAllocationsGridControl.Location = New System.Drawing.Point(333, 2)
        Me.ChargeAllocationsGridControl.MainView = Me.GridView1
        Me.ChargeAllocationsGridControl.Name = "ChargeAllocationsGridControl"
        Me.ChargeAllocationsGridControl.Size = New System.Drawing.Size(752, 227)
        Me.ChargeAllocationsGridControl.TabIndex = 16
        Me.ChargeAllocationsGridControl.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colHcpcs, Me.colTreatmentArea, Me.colUnits, Me.colUserDefinedBillingdate})
        Me.GridView1.GridControl = Me.ChargeAllocationsGridControl
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Top
        '
        'colHcpcs
        '
        Me.colHcpcs.FieldName = "Hcpcs"
        Me.colHcpcs.Name = "colHcpcs"
        '
        'colTreatmentArea
        '
        Me.colTreatmentArea.FieldName = "TreatmentArea"
        Me.colTreatmentArea.Name = "colTreatmentArea"
        Me.colTreatmentArea.Visible = True
        Me.colTreatmentArea.VisibleIndex = 1
        '
        'colUnits
        '
        Me.colUnits.FieldName = "Units"
        Me.colUnits.Name = "colUnits"
        Me.colUnits.Visible = True
        Me.colUnits.VisibleIndex = 2
        '
        'colUserDefinedBillingdate
        '
        Me.colUserDefinedBillingdate.FieldName = "UserDefinedBillingdate"
        Me.colUserDefinedBillingdate.Name = "colUserDefinedBillingdate"
        Me.colUserDefinedBillingdate.Visible = True
        Me.colUserDefinedBillingdate.VisibleIndex = 0
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Location = New System.Drawing.Point(2, 194)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(149, 35)
        Me.SimpleButton1.TabIndex = 17
        Me.SimpleButton1.Text = "SimpleButton1"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Location = New System.Drawing.Point(155, 194)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(164, 35)
        Me.SimpleButton2.TabIndex = 18
        Me.SimpleButton2.Text = "SimpleButton2"
        '
        'GroupControl1
        '
        Me.GroupControl1.CaptionLocation = DevExpress.Utils.Locations.Top
        Me.GroupControl1.Controls.Add(Me.GroupControl2)
        Me.GroupControl1.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.ShowCaption = False
        Me.GroupControl1.Size = New System.Drawing.Size(1097, 517)
        Me.GroupControl1.TabIndex = 20
        Me.GroupControl1.Text = "LayoutControlGroup1"
        '
        'GroupControl2
        '
        Me.GroupControl2.CaptionLocation = DevExpress.Utils.Locations.Top
        Me.GroupControl2.Controls.Add(Me.LabelControl1)
        Me.GroupControl2.Controls.Add(Me.ChargeMasterTextEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl2)
        Me.GroupControl2.Controls.Add(Me.CodeTypeTextEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl3)
        Me.GroupControl2.Controls.Add(Me.DescriptionTextEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl4)
        Me.GroupControl2.Controls.Add(Me.ESPValueTextEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl5)
        Me.GroupControl2.Controls.Add(Me.HcpcsTextEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl6)
        Me.GroupControl2.Controls.Add(Me.OriginalBillingdateDateEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl7)
        Me.GroupControl2.Controls.Add(Me.TotalUnitsSpinEdit)
        Me.GroupControl2.Controls.Add(Me.LabelControl8)
        Me.GroupControl2.Controls.Add(Me.TreatmentAreaTextEdit)
        Me.GroupControl2.Controls.Add(Me.ChargeAllocationsGridControl)
        Me.GroupControl2.Controls.Add(Me.SimpleButton1)
        Me.GroupControl2.Controls.Add(Me.SimpleButton2)
        Me.GroupControl2.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.ShowCaption = False
        Me.GroupControl2.Size = New System.Drawing.Size(1077, 497)
        Me.GroupControl2.TabIndex = 0
        Me.GroupControl2.Text = "Root"
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(2, 2)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(75, 13)
        Me.LabelControl1.TabIndex = 0
        Me.LabelControl1.Text = "Charge Master:"
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(2, 26)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(56, 13)
        Me.LabelControl2.TabIndex = 3
        Me.LabelControl2.Text = "Code Type:"
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(2, 50)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(57, 13)
        Me.LabelControl3.TabIndex = 5
        Me.LabelControl3.Text = "Description:"
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(2, 74)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(48, 13)
        Me.LabelControl4.TabIndex = 7
        Me.LabelControl4.Text = "ESPValue:"
        '
        'LabelControl5
        '
        Me.LabelControl5.Location = New System.Drawing.Point(2, 98)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(32, 13)
        Me.LabelControl5.TabIndex = 9
        Me.LabelControl5.Text = "Hcpcs:"
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(2, 122)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(91, 13)
        Me.LabelControl6.TabIndex = 11
        Me.LabelControl6.Text = "Original Billingdate:"
        '
        'LabelControl7
        '
        Me.LabelControl7.Location = New System.Drawing.Point(2, 146)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(55, 13)
        Me.LabelControl7.TabIndex = 13
        Me.LabelControl7.Text = "Total Units:"
        '
        'LabelControl8
        '
        Me.LabelControl8.Location = New System.Drawing.Point(2, 170)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(80, 13)
        Me.LabelControl8.TabIndex = 15
        Me.LabelControl8.Text = "Treatment Area:"
        '
        'ChargeAllocationEditFormV2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1097, 517)
        Me.Controls.Add(Me.GroupControl1)
        Me.Name = "ChargeAllocationEditFormV2"
        Me.Text = "ChargeAllocationEditFormV2"
        CType(Me.ChargeBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChargeAllocationsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChargeMasterTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CodeTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DescriptionTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ESPValueTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.HcpcsTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OriginalBillingdateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OriginalBillingdateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TotalUnitsSpinEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TreatmentAreaTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChargeAllocationsGridControl, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents ChargeBindingSource As BindingSource
    Friend WithEvents ChargeAllocationsBindingSource As BindingSource
    Friend WithEvents ChargeMasterTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CodeTypeTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DescriptionTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ESPValueTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents HcpcsTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents OriginalBillingdateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TotalUnitsSpinEdit As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents TreatmentAreaTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ChargeAllocationsGridControl As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colHcpcs As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUnits As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUserDefinedBillingdate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutConverter1 As DevExpress.XtraLayout.Converter.LayoutConverter
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
End Class

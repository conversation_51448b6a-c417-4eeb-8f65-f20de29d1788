﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>%24/VIC/Experimental/SourceCode/ENChartCAC/ECConfig</SccProjectName>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <SccAuxPath>http://tfs.paragon.mckesson.com:8080/tfs/collection1-eis</SccAuxPath>
    <SccLocalPath>.</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <OutputType>WinExe</OutputType>
    <StartupObject>ECConfig.My.MyApplication</StartupObject>
    <MyType>WindowsForms</MyType>
    <!--<SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>-->
    <!--<ApplicationManifest>My Project\app.manifest</ApplicationManifest>-->
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>False</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
       <Configurations>Debug;Release;JJCDebug</Configurations>
    <Platforms>AnyCPU;</Platforms>
    <ImportedNamespaces>EnchartDOLib=False,System.Windows.Forms=False,Microsoft.VisualBasic=True,System=True,System.Collections=True,System.Collections.Generic=True,System.Diagnostics=True,System.Linq=True,System.Xml.Linq=True,System.Threading.Tasks=True,ECConfig=True</ImportedNamespaces>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='JJCDebug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  
  <!--<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'UI and DOLib|x86' ">
    <OutputPath>bin\x86\UI and DOLib\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DatabaseMigration|AnyCPU'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\DatabaseMigration\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DatabaseMigration|x86'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x86\DatabaseMigration\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UI and DOLib|AnyCPU'">
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42024,42030,42032,42036,42099,42104,42108,42109</NoWarn>
  </PropertyGroup>-->
  <!--<ItemGroup>
    <Import Include="EnchartDOLib" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="AddNewFacilityForm.Designer.vb">
      <DependentUpon>AddNewFacilityForm.vb</DependentUpon>
    </Compile>
    <Compile Update="ComboBoxListEditorFormv2.Designer.vb">
      <DependentUpon>ComboBoxListEditorFormv2.vb</DependentUpon>
    </Compile>
    <Compile Update="CopyTreatmentAreaForm.Designer.vb">
      <DependentUpon>CopyTreatmentAreaForm.vb</DependentUpon>
    </Compile>
    <Compile Update="CreateNewConfigInstanceForAllFacilitiesForm.Designer.vb">
      <DependentUpon>CreateNewConfigInstanceForAllFacilitiesForm.vb</DependentUpon>
    </Compile>
    <Compile Update="Espcodes2018Form.Designer.vb">
      <DependentUpon>Espcodes2018Form.vb</DependentUpon>
    </Compile>
    <Compile Update="FacilitySettingsForm.Designer.vb">
      <DependentUpon>FacilitySettingsForm.vb</DependentUpon>
    </Compile>
    <Compile Update="FacilitySettingsMissingDefaultsForm.Designer.vb">
      <DependentUpon>FacilitySettingsMissingDefaultsForm.vb</DependentUpon>
    </Compile>
    <Compile Update="GlobalSettingsForm.Designer.vb">
      <DependentUpon>GlobalSettingsForm.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeedForm.Designer.vb">
      <DependentUpon>ImageFeedForm.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\AddFieldDialog.Designer.vb">
      <DependentUpon>AddFieldDialog.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\HylandOnBaseModule.Designer.vb">
      <DependentUpon>HylandOnBaseModule.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\HylandOnBaseModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\MeditechModule.Designer.vb">
      <DependentUpon>MeditechModule.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\MeditechModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\NoImageFeedModule.Designer.vb">
      <DependentUpon>NoImageFeedModule.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\NoImageFeedModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\OneContentFilenameModule.Designer.vb">
      <DependentUpon>OneContentFilenameModule.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\OneContentFilenameModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\OneContentModule.Designer.vb">
      <DependentUpon>OneContentModule.vb</DependentUpon>
    </Compile>
    <Compile Update="ImageFeed\OneContentModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\RemoveFieldDialog.Designer.vb">
      <DependentUpon>RemoveFieldDialog.vb</DependentUpon>
    </Compile>
    <Compile Update="LoadNcciEditsForm.Designer.vb">
      <DependentUpon>LoadNcciEditsForm.vb</DependentUpon>
    </Compile>
    <Compile Update="DoingSomethingMsgForm.Designer.vb">
      <DependentUpon>DoingSomethingMsgForm.vb</DependentUpon>
    </Compile>
    <Compile Update="FacilityConfigImportForm.Designer.vb">
      <DependentUpon>FacilityConfigImportForm.vb</DependentUpon>
    </Compile>
    <Compile Update="FacilityCopyForm.Designer.vb">
      <DependentUpon>FacilityCopyForm.vb</DependentUpon>
    </Compile>-->
  <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <!--<Compile Update="ControlOverridesForm.Designer.vb">
      <DependentUpon>ControlOverridesForm.vb</DependentUpon>
    </Compile>
    <Compile Update="TreatmentAreas\EditTreatmentAreaForm.Designer.vb">
      <DependentUpon>EditTreatmentAreaForm.vb</DependentUpon>
    </Compile>
    <Compile Update="TreatmentAreas\TreatmentAreasPage.Designer.vb">
      <DependentUpon>TreatmentAreasPage.vb</DependentUpon>
    </Compile>
    <Compile Update="TreatmentAreas\TreatmentAreasPage.vb" />
    <Compile Update="UpdateModifier25Form.Designer.vb">
      <DependentUpon>UpdateModifier25Form.vb</DependentUpon>
    </Compile>
    <Compile Update="Utils\LoadMedicationMastersForm.Designer.vb">
      <DependentUpon>LoadMedicationMastersForm.vb</DependentUpon>
    </Compile>
    <Compile Update="Utils\ParagonImmunizationMappingsForm.Designer.vb">
      <DependentUpon>ParagonImmunizationMappingsForm.vb</DependentUpon>
    </Compile>
    <Compile Update="Utils\ParagonMedFiltersForm.Designer.vb">
      <DependentUpon>ParagonMedFiltersForm.vb</DependentUpon>
    </Compile>
    <Compile Update="Utils\ParagonMedIdForm.Designer.vb">
      <DependentUpon>ParagonMedIdForm.vb</DependentUpon>
    </Compile>-->
  </ItemGroup>
  
  <ItemGroup>
    <EmbeddedResource Include="My Project\licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\DataSources\EnchartDOLib.DOMedicationMaster.datasource" />
    <None Include="My Project\DataSources\EnchartDOLib.DOParagonImmunization.datasource" />
    <None Include="My Project\DataSources\EnchartDOLib.DOParagonMedIdMapping.datasource" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Timeout\Timeout\Timeout.vbproj" />
    <ProjectReference Include="..\CdmReportsViewer2\CDMReportViewer2.vbproj" />
    <ProjectReference Include="..\ColdFeedExport\McKeesson.HIC.HPFColdFeed.dll\McKesson.HIC.HPFColdFeed.vbproj" />
    <ProjectReference Include="..\ColdFeedExport\McKesson.HIC.ChargeSummaryColdFeedHelper\McKesson.HIC.ChargeSummaryColdFeedHelper.vbproj" />
    <ProjectReference Include="..\ENChartCAC\ENChartCAC.vbproj" />
    <ProjectReference Include="..\EnchartDOLib\EnchartDOLib.vbproj" />
    <ProjectReference Include="..\MICCustomDataProviders\MICCustomConnectionProviders.csproj" />
    <ProjectReference Include="..\MICMiscUtilCSharp\MICMiscUtilCSharp.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\DefaultFacilitySettings.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DevExpress.Win.Design" Version="22.1.6" />
    <PackageReference Include="Mapster.Core">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.355802">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="ManageLicenseAndConfig.Designer.vb" />
    <Compile Remove="ManageLicenseAndConfig.vb" />
    <Compile Remove="ModWorkStation.Designer.vb" />
    <Compile Remove="ModWorkStation.vb" />
    <EmbeddedResource Remove="ManageLicenseAndConfig.resx" />
    <EmbeddedResource Remove="ModWorkStation.resx" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="EnchartDOLib" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <!--<PropertyGroup>
    <PostBuildEvent>copy "$(TargetDir)"$(TargetFileName).config "c:\apps\eccoder\$(TargetFileName).config"
copy "$(TargetPath)"  "$(SolutionDir)ENChartCAC\bin\Release\$(TargetFileName)"
copy "$(TargetPath)"  "C:\apps\ECCoder\$(TargetFileName)"
</PostBuildEvent>
  </PropertyGroup>-->
</Project>
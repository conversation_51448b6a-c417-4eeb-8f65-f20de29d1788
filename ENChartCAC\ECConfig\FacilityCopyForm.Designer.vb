<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FacilityCopyForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.cboFrom = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.cboTo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        Me.progressControl = New DevExpress.XtraEditors.ProgressBarControl()
        Me.ceImportCDM = New DevExpress.XtraEditors.CheckEdit()
        Me.ceImportReports = New DevExpress.XtraEditors.CheckEdit()
        Me.gvTreatmentArea = New System.Windows.Forms.DataGridView()
        Me.colTA = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colCopyTA = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.gvUsers = New System.Windows.Forms.DataGridView()
        Me.colOID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colUserName = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colCopy = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.btnClearAllUsers = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSellectAllusers = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.cboFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.progressControl.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceImportCDM.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceImportReports.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvTreatmentArea, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvUsers, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'cboFrom
        '
        Me.cboFrom.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboFrom.Location = New System.Drawing.Point(143, 414)
        Me.cboFrom.Name = "cboFrom"
        Me.cboFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboFrom.Properties.ImmediatePopup = True
        Me.cboFrom.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboFrom.Size = New System.Drawing.Size(286, 20)
        Me.cboFrom.TabIndex = 0
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(13, 417)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(60, 13)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "From Facility"
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(13, 457)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(48, 13)
        Me.LabelControl2.TabIndex = 3
        Me.LabelControl2.Text = "To Facility"
        '
        'cboTo
        '
        Me.cboTo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboTo.Location = New System.Drawing.Point(143, 454)
        Me.cboTo.Name = "cboTo"
        Me.cboTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboTo.Properties.ImmediatePopup = True
        Me.cboTo.Properties.Sorted = True
        Me.cboTo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboTo.Size = New System.Drawing.Size(286, 20)
        Me.cboTo.TabIndex = 2
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.Location = New System.Drawing.Point(907, 560)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 9
        Me.btnCancel.Text = "Cancel"
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.Location = New System.Drawing.Point(812, 560)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(75, 23)
        Me.btnOK.TabIndex = 8
        Me.btnOK.Text = "OK"
        '
        'progressControl
        '
        Me.progressControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.progressControl.Location = New System.Drawing.Point(13, 493)
        Me.progressControl.Name = "progressControl"
        Me.progressControl.Size = New System.Drawing.Size(970, 18)
        Me.progressControl.TabIndex = 10
        '
        'ceImportCDM
        '
        Me.ceImportCDM.Location = New System.Drawing.Point(13, 526)
        Me.ceImportCDM.Name = "ceImportCDM"
        Me.ceImportCDM.Properties.Caption = "Import CDM Table Records"
        Me.ceImportCDM.Size = New System.Drawing.Size(201, 19)
        Me.ceImportCDM.TabIndex = 11
        '
        'ceImportReports
        '
        Me.ceImportReports.EditValue = True
        Me.ceImportReports.Location = New System.Drawing.Point(13, 551)
        Me.ceImportReports.Name = "ceImportReports"
        Me.ceImportReports.Properties.Caption = "Import Admin Report Records"
        Me.ceImportReports.Size = New System.Drawing.Size(215, 19)
        Me.ceImportReports.TabIndex = 12
        '
        'gvTreatmentArea
        '
        Me.gvTreatmentArea.AllowUserToAddRows = False
        Me.gvTreatmentArea.AllowUserToDeleteRows = False
        Me.gvTreatmentArea.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvTreatmentArea.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.colTA, Me.colCopyTA})
        Me.gvTreatmentArea.Location = New System.Drawing.Point(15, 49)
        Me.gvTreatmentArea.Name = "gvTreatmentArea"
        Me.gvTreatmentArea.Size = New System.Drawing.Size(454, 318)
        Me.gvTreatmentArea.TabIndex = 13
        '
        'colTA
        '
        Me.colTA.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.colTA.HeaderText = "Treatment Area"
        Me.colTA.Name = "colTA"
        '
        'colCopyTA
        '
        Me.colCopyTA.HeaderText = "Process"
        Me.colCopyTA.Name = "colCopyTA"
        '
        'gvUsers
        '
        Me.gvUsers.AllowUserToAddRows = False
        Me.gvUsers.AllowUserToDeleteRows = False
        Me.gvUsers.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvUsers.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.colOID, Me.colUserName, Me.colCopy})
        Me.gvUsers.Location = New System.Drawing.Point(475, 49)
        Me.gvUsers.Name = "gvUsers"
        Me.gvUsers.Size = New System.Drawing.Size(483, 318)
        Me.gvUsers.TabIndex = 14
        '
        'colOID
        '
        Me.colOID.HeaderText = "OID"
        Me.colOID.Name = "colOID"
        '
        'colUserName
        '
        Me.colUserName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.colUserName.HeaderText = "UserName"
        Me.colUserName.Name = "colUserName"
        '
        'colCopy
        '
        Me.colCopy.HeaderText = "Copy"
        Me.colCopy.Name = "colCopy"
        Me.colCopy.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.colCopy.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        '
        'btnClearAllUsers
        '
        Me.btnClearAllUsers.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClearAllUsers.Location = New System.Drawing.Point(475, 373)
        Me.btnClearAllUsers.Name = "btnClearAllUsers"
        Me.btnClearAllUsers.Size = New System.Drawing.Size(146, 23)
        Me.btnClearAllUsers.TabIndex = 15
        Me.btnClearAllUsers.Text = "Clear All"
        '
        'btnSellectAllusers
        '
        Me.btnSellectAllusers.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSellectAllusers.Location = New System.Drawing.Point(812, 373)
        Me.btnSellectAllusers.Name = "btnSellectAllusers"
        Me.btnSellectAllusers.Size = New System.Drawing.Size(146, 23)
        Me.btnSellectAllusers.TabIndex = 16
        Me.btnSellectAllusers.Text = "Select All"
        '
        'FacilityCopyForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(995, 605)
        Me.ControlBox = False
        Me.Controls.Add(Me.btnSellectAllusers)
        Me.Controls.Add(Me.btnClearAllUsers)
        Me.Controls.Add(Me.gvUsers)
        Me.Controls.Add(Me.gvTreatmentArea)
        Me.Controls.Add(Me.ceImportReports)
        Me.Controls.Add(Me.ceImportCDM)
        Me.Controls.Add(Me.progressControl)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.cboTo)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.cboFrom)
        Me.MaximizeBox = False
        Me.Name = "FacilityCopyForm"
        Me.ShowIcon = False
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Copy Facility Configuration"
        CType(Me.cboFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.progressControl.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceImportCDM.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceImportReports.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvTreatmentArea, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvUsers, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents cboFrom As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboTo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents progressControl As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents ceImportCDM As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceImportReports As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents gvTreatmentArea As System.Windows.Forms.DataGridView
    Friend WithEvents gvUsers As System.Windows.Forms.DataGridView
    Friend WithEvents colOID As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents colUserName As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents colCopy As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents colTA As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents colCopyTA As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents btnClearAllUsers As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSellectAllusers As DevExpress.XtraEditors.SimpleButton
End Class

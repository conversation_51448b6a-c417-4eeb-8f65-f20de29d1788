<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://EN-Chart.com/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://EN-Chart.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://EN-Chart.com/">
      <s:element name="ENSetLevelerInformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="hostName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="portNum" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ENSetLevelerInformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ENSetLevelerInformationResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="sendChart">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="patChartInfo" type="tns:ENPatientChartingInfo" />
            <s:element minOccurs="0" maxOccurs="1" name="patDemoInfo" type="tns:ENPatientDemographicInfo" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ENPatientChartingInfo">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="facility" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="treatmentArea" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="accountNo" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="dateOfService" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="chartViewURL" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ENPatientDemographicInfo">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="middleName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="lastName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="suffixName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DOB" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="gender" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="MRN" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SS" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="financialClass" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="addressLine1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="addressLine2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="city" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="zipCode" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="sendChartResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sendChartResult" type="tns:ENCodingResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ENCodingResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="resultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="resultText" type="s:string" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="ENSetLevelerInformationSoapIn">
    <wsdl:part name="parameters" element="tns:ENSetLevelerInformation" />
  </wsdl:message>
  <wsdl:message name="ENSetLevelerInformationSoapOut">
    <wsdl:part name="parameters" element="tns:ENSetLevelerInformationResponse" />
  </wsdl:message>
  <wsdl:message name="sendChartSoapIn">
    <wsdl:part name="parameters" element="tns:sendChart" />
  </wsdl:message>
  <wsdl:message name="sendChartSoapOut">
    <wsdl:part name="parameters" element="tns:sendChartResponse" />
  </wsdl:message>
  <wsdl:portType name="ServiceSoap">
    <wsdl:operation name="ENSetLevelerInformation">
      <wsdl:input message="tns:ENSetLevelerInformationSoapIn" />
      <wsdl:output message="tns:ENSetLevelerInformationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="sendChart">
      <wsdl:input message="tns:sendChartSoapIn" />
      <wsdl:output message="tns:sendChartSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ServiceSoap" type="tns:ServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ENSetLevelerInformation">
      <soap:operation soapAction="http://EN-Chart.com/ENSetLevelerInformation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendChart">
      <soap:operation soapAction="http://EN-Chart.com/sendChart" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ServiceSoap12" type="tns:ServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ENSetLevelerInformation">
      <soap12:operation soapAction="http://EN-Chart.com/ENSetLevelerInformation" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendChart">
      <soap12:operation soapAction="http://EN-Chart.com/sendChart" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service">
    <wsdl:port name="ServiceSoap" binding="tns:ServiceSoap">
      <soap:address location="http://***********/WSTest3/ECMcKessonService.asmx" />
    </wsdl:port>
    <wsdl:port name="ServiceSoap12" binding="tns:ServiceSoap12">
      <soap12:address location="http://***********/WSTest3/ECMcKessonService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
﻿Imports System.Windows.Forms
Imports DevExpress.XtraEditors.Controls

Public Class FacilitySettingsMissingDefaultsForm

    Public Sub New(ByVal missingSettings As Dictionary(Of Integer, List(Of DOFacilitySettings)))

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        For Each facilitySettings As List(Of DOFacilitySettings) In missingSettings.Values
            clbMissingSettings.Items.AddRange(facilitySettings.ToArray)
        Next
    End Sub

    Private Sub SaveCheckedSettings()
        For Each ci As CheckedListBoxItem In clbMissingSettings.CheckedItems
            Dim setting As DOFacilitySettings = CType(ci.Value, DOFacilitySettings)
            setting.Save()
        Next
    End Sub

    Private Sub OK_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddSettings.Click
        SaveCheckedSettings()
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub clbMissingSettings_CustomItemDisplayText(sender As Object, e As DevExpress.XtraEditors.CustomItemDisplayTextEventArgs) Handles clbMissingSettings.CustomItemDisplayText
        Dim i As CheckedListBoxItem = CType(e.Item, CheckedListBoxItem)
        If i.Value IsNot Nothing Then
            Dim setting As DOFacilitySettings = CType(i.Value, DOFacilitySettings)
            Dim facility As DOFacility = DOFacility.GetFacilityByOid(setting.Facility)

            e.DisplayText = $"{facility.LongName} / {setting.TreatmentArea} - {setting.RuleSettingCategory} / {setting.RuleSetting}"
        End If
    End Sub

    Private Sub chkSelectAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkSelectAll.CheckedChanged
        If chkSelectAll.Checked Then
            clbMissingSettings.CheckAll()
        Else
            clbMissingSettings.UnCheckAll()
        End If
    End Sub
End Class

Imports System.IO
Imports EnchartDOLib

Public Class LocalReportViewer

    Public Overrides Sub Show_Report(facilityReport As DOFacilityReport, treatment_area As String, mindate As Date, maxdate As Date, datetype As String, pf As ReportStartup)
        ParentReportStartup = pf

        Dim szSQL As String = ""
        Dim szMinYear As String = ""
        Dim szMinMonth As String = ""
        Dim szMinDay As String = ""
        Dim szMaxYear As String = ""
        Dim szMaxMonth As String = ""
        Dim szMaxDay As String = ""
        Dim szDateField As String = "DOChartSummary.ArrivalDate"

        Select Case UCase(datetype)
            Case "DOS"
                'szDateField = "command.ArrivalDate"
                'Select Case UCase(facilityReport.ReportType)
                '    Case "OBS"
                '        szDateField = "command.ObsStartDate"
                '    Case Else
                '        szDateField = "command.ArrivalDate"
                'End Select
                If facilityReport.ReportType.IsObservationReport Then
                    szDateField = "command.ObsStartDate"
                Else
                    szDateField = "command.ArrivalDate"
                End If
            Case "DATEENTERED"
                'Select Case UCase(facilityReport.ReportType)
                '    Case "FACILITY"
                '        szDateField = "command.FacChartStatusChangedOn"
                '    Case "PHYSICIAN"
                '        szDateField = "command.PhysChartStatusChangedOn"
                '    Case "OBS"
                '        szDateField = "command.ObsChartStatusChangedOn"
                '    Case Else
                '        szDateField = "command.CreationDate"
                'End Select
                If facilityReport.ReportType.IsPhysicianReport Then
                    szDateField = "command.PhysChartStatusChangedOn"
                ElseIf facilityReport.ReportType.IsObservationReport Then
                    szDateField = "command.ObsChartStatusChangedOn"
                Else
                    szDateField = "command.CreationDate"
                End If
            Case "DATEEXPORTED"
                szDateField = "command.ExportDate"
        End Select
        szMinYear = Format(mindate, "yyyy")
        szMinMonth = Format(mindate, "MM")
        szMinDay = Format(mindate, "dd")
        szMaxYear = Format(maxdate, "yyyy")
        szMaxMonth = Format(maxdate, "MM")
        szMaxDay = Format(maxdate, "dd")

        If Not treatment_area.ToUpper = "ALL" Then
            'If UCase(facilityReport.ReportType) = "OBS" Then
            '    szSQL = "{command.ObsTreatmentArea} = """ & treatment_area & """ AND "
            'Else
            '    szSQL = "{command.TreatmentArea} = """ & treatment_area & """ AND "
            'End If
            If facilityReport.ReportType.IsObservationReport Then
                szSQL = "{command.ObsTreatmentArea} = """ & treatment_area & """ AND "
            Else
                szSQL = "{command.TreatmentArea} = """ & treatment_area & """ AND "
            End If
        End If

        szSQL += "{command.Facility} = " & facilityReport.ReportCategory.Facility.Oid & " AND "
        szSQL += "{" & szDateField & "} in "
        szSQL += "DateTime (" & szMinYear & ", " & szMinMonth & ", " & szMinDay & ", 00, 00, 00) "
        szSQL += "to "
        szSQL += "DateTime (" & szMaxYear & ", " & szMaxMonth & ", " & szMaxDay & ", 23, 59, 59) "

        RegisterButtonHandlers()
        SetupAuditLogger()

        SelectionCriteria = szSQL
        AuditLogger.Reports.AdminReportsLogRun(SelectionCriteria)

        Console.WriteLine(datetype)
        Console.WriteLine(szSQL)

        Try
            Dim reportfilename As String
#If DEBUG Then
            reportfilename = Path.Combine("c:\apps\eccoder\reports\", facilityReport.ReportFilename)
#Else
            reportfilename = Path.Combine(Application.StartupPath, "\reports\") & facilityReport.ReportFilename
#End If
            Dim fi As New FileInfo(reportfilename)

            LoadReport(reportfilename, szSQL)
        Catch ex As CrystalDecisions.CrystalReports.Engine.LoadSaveReportException
            MessageBox.Show("The report could not be found.", "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
            Me.Close()
        End Try
    End Sub

End Class
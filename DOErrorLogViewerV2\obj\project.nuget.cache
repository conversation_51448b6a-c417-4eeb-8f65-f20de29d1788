{"version": 2, "dgSpecHash": "ZYYbTAKKXCk=", "success": true, "projectFilePath": "C:\\AIC\\Main_Next\\SourceCode\\DOErrorLogViewerV2\\DOErrorLogViewerV2.vbproj", "expectedPackageFiles": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.charts\\23.1.5\\devexpress.charts.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.charts.core\\23.1.5\\devexpress.charts.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.codeparser\\23.1.5\\devexpress.codeparser.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.data\\23.1.5\\devexpress.data.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.data.desktop\\23.1.5\\devexpress.data.desktop.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.dataaccess\\23.1.5\\devexpress.dataaccess.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.dataaccess.ui\\23.1.5\\devexpress.dataaccess.ui.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.datavisualization.core\\23.1.5\\devexpress.datavisualization.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.diagram.core\\23.1.5\\devexpress.diagram.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.drawing\\23.1.5\\devexpress.drawing.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.gauges.core\\23.1.5\\devexpress.gauges.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.images\\23.1.5\\devexpress.images.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.map.core\\23.1.5\\devexpress.map.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.mvvm\\23.1.5\\devexpress.mvvm.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.office.core\\23.1.5\\devexpress.office.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.pdf.core\\23.1.5\\devexpress.pdf.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.pdf.drawing\\23.1.5\\devexpress.pdf.drawing.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.pivotgrid.core\\23.1.5\\devexpress.pivotgrid.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.printing.core\\23.1.5\\devexpress.printing.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.reporting.core\\23.1.5\\devexpress.reporting.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.richedit.core\\23.1.5\\devexpress.richedit.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.richedit.export\\23.1.5\\devexpress.richedit.export.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.scheduler.core\\23.1.5\\devexpress.scheduler.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.scheduler.coredesktop\\23.1.5\\devexpress.scheduler.coredesktop.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.sparkline.core\\23.1.5\\devexpress.sparkline.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.spellchecker.core\\23.1.5\\devexpress.spellchecker.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.spreadsheet.core\\23.1.5\\devexpress.spreadsheet.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.treemap\\23.1.5\\devexpress.treemap.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.treemap.core\\23.1.5\\devexpress.treemap.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.utils\\23.1.5\\devexpress.utils.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.utils.ui\\23.1.5\\devexpress.utils.ui.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win\\23.1.5\\devexpress.win.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.charts\\23.1.5\\devexpress.win.charts.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.design\\23.1.5\\devexpress.win.design.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.diagram\\23.1.5\\devexpress.win.diagram.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.dialogs\\23.1.5\\devexpress.win.dialogs.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.dialogs.core\\23.1.5\\devexpress.win.dialogs.core.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.gantt\\23.1.5\\devexpress.win.gantt.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.gauges\\23.1.5\\devexpress.win.gauges.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.grid\\23.1.5\\devexpress.win.grid.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.map\\23.1.5\\devexpress.win.map.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.navigation\\23.1.5\\devexpress.win.navigation.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.pdfviewer\\23.1.5\\devexpress.win.pdfviewer.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.pivotgrid\\23.1.5\\devexpress.win.pivotgrid.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.printing\\23.1.5\\devexpress.win.printing.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.reporting\\23.1.5\\devexpress.win.reporting.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.richedit\\23.1.5\\devexpress.win.richedit.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.scheduler\\23.1.5\\devexpress.win.scheduler.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.schedulerextensions\\23.1.5\\devexpress.win.schedulerextensions.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.schedulerreporting\\23.1.5\\devexpress.win.schedulerreporting.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.spellchecker\\23.1.5\\devexpress.win.spellchecker.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.spreadsheet\\23.1.5\\devexpress.win.spreadsheet.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.treelist\\23.1.5\\devexpress.win.treelist.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.treemap\\23.1.5\\devexpress.win.treemap.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.win.verticalgrid\\23.1.5\\devexpress.win.verticalgrid.23.1.5.nupkg.sha512", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages\\devexpress.xpo\\23.1.5\\devexpress.xpo.23.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster\\7.3.0\\mapster.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster.core\\1.2.0\\mapster.core.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\5.0.0\\microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\2.0.0\\microsoft.extensions.configuration.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\3.1.8\\microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\2.0.0\\microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\7.0.0\\microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\7.0.0\\microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\3.0.0\\microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\3.1.8\\microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\3.1.8\\microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\7.0.0\\microsoft.extensions.logging.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\7.0.0\\microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\5.0.10\\microsoft.extensions.objectpool.5.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\7.0.0\\microsoft.extensions.options.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\2.0.0\\microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\7.0.0\\microsoft.extensions.primitives.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualbasic\\10.4.0-preview.18571.3\\microsoft.visualbasic.10.4.0-preview.18571.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry.accesscontrol\\7.0.0\\microsoft.win32.registry.accesscontrol.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\7.0.0\\microsoft.win32.systemevents.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.compatibility\\7.0.3\\microsoft.windows.compatibility.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.3\\netstandard.library.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\7.0.0\\runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\7.0.0\\runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\7.0.0\\runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\7.0.0\\runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\7.0.0\\runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\7.0.0\\runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\2.10.0\\serilog.2.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\6.1.0\\serilog.aspnetcore.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\5.0.1\\serilog.extensions.hosting.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\3.1.0\\serilog.extensions.logging.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\1.1.0\\serilog.formatting.compact.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\3.3.0\\serilog.settings.configuration.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\4.0.1\\serilog.sinks.console.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\1.5.0\\system.collections.immutable.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\7.0.0\\system.componentmodel.composition.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition.registration\\7.0.0\\system.componentmodel.composition.registration.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\7.0.0\\system.configuration.configurationmanager.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.datasetextensions\\4.5.0\\system.data.datasetextensions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.odbc\\7.0.0\\system.data.odbc.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\7.0.0\\system.data.oledb.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\7.0.0\\system.diagnostics.eventlog.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\7.0.0\\system.diagnostics.performancecounter.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\7.0.1\\system.directoryservices.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.accountmanagement\\7.0.0\\system.directoryservices.accountmanagement.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\7.0.1\\system.directoryservices.protocols.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\7.0.0\\system.drawing.common.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.1\\system.formats.asn1.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\7.0.0\\system.io.packaging.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\7.0.0\\system.io.ports.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.2\\system.management.7.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.servicemodel\\4.10.2\\system.private.servicemodel.4.10.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.context\\7.0.0\\system.reflection.context.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.dispatchproxy\\4.7.1\\system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.7.0\\system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\7.0.0\\system.runtime.caching.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\7.0.2\\system.security.cryptography.pkcs.7.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\7.0.1\\system.security.cryptography.protecteddata.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\7.0.1\\system.security.cryptography.xml.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\7.0.0\\system.security.permissions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.duplex\\4.9.0\\system.servicemodel.duplex.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\4.10.2\\system.servicemodel.http.4.10.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\4.9.0\\system.servicemodel.nettcp.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\4.10.2\\system.servicemodel.primitives.4.10.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.security\\4.9.0\\system.servicemodel.security.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.syndication\\7.0.0\\system.servicemodel.syndication.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.serviceprocess.servicecontroller\\7.0.1\\system.serviceprocess.servicecontroller.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\7.0.0\\system.speech.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\7.0.0\\system.text.encoding.codepages.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\7.0.1\\system.threading.accesscontrol.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.web.services.description\\4.9.0\\system.web.services.description.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\7.0.0\\system.windows.extensions.7.0.0.nupkg.sha512"], "logs": []}
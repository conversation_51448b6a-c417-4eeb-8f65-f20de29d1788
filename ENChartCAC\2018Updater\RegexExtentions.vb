﻿Imports System.Runtime.CompilerServices
Imports System.Text.RegularExpressions

Module RegexExtensions
    <Extension()>
    Function ReplaceNamedGroup(ByVal input As String, ByVal regex As Regex, ByVal groupName As String, ByVal replacement As String) As String
        Return regex.Replace(input, Function(m) ReplaceNamedGroupHelper(input, groupName, replacement, m))
    End Function

    Private Function ReplaceNamedGroupHelper(ByVal input As String, ByVal groupName As String, ByVal replacement As String, ByVal m As Match) As String
        Dim capture As String = m.Value
        capture = capture.Remove(m.Groups(groupName).Index - m.Index, m.Groups(groupName).Length)
        capture = capture.Insert(m.Groups(groupName).Index - m.Index, replacement)
        Return capture
    End Function
End Module


﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup />
  <ItemGroup>
    <Compile Update="AddNewFacilityForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="ComboBoxListEditorFormv2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="ControlOverridesForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="CopyTreatmentAreaForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="CreateNewConfigInstanceForAllFacilitiesForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="DoingSomethingMsgForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Espcodes2018Form.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="FacilityConfigImportForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="FacilityCopyForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="FacilitySettingsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="FacilitySettingsMissingDefaultsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="GlobalSettingsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="ImageFeedForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="ImageFeed\AddFieldDialog.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="ImageFeed\HylandOnBaseModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\MeditechModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\NoImageFeedModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\OneContentFilenameModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\OneContentModule.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="ImageFeed\RemoveFieldDialog.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="LoadNcciEditsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="TreatmentAreas\EditTreatmentAreaForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="TreatmentAreas\TreatmentAreasPage.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="UpdateModifier25Form.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\LoadMedicationMastersForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\ParagonHydrationsListForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\ParagonImmunizationMappingsForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\ParagonMedFiltersForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\ParagonMedIdForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Utils\ParagonRouteListForm.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>
﻿Imports System.IO
Imports System.Xml
Imports System.Text

Imports EnchartDOLib

Imports McKesson.HIC.ColdFeed

Namespace ColdFeed

    'Note this dll is not reference anywhere... it's loaded dynamically at runtime
    Public Class HPFColdFeedDetailsFileHandler
        Implements IExportChargeSummaryDetailsFile

#Region "Fields"

        Protected OuputPath As String

        Private _cFHelper As ColdFeedHelper

#End Region 'Fields

#Region "Properties"

        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryDetailsFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property

#End Region 'Properties

#Region "Methods"
        Public Sub Initialize() Implements IExportChargeSummaryDetailsFile.Initialize

        End Sub
        Public Function CreateDetail(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryDetailsFile.CreateDetail
            If Not chart.ImageExportSucceeded Then
                Dim xmlFilename = Path.Combine(Path.GetFileNameWithoutExtension(chart.ImageFileName), "xml")
                If File.Exists(xmlFilename) Then
                    File.Delete(xmlFilename)
                End If
                Return False
            End If

            Dim map_field_values_list As List(Of MapFieldValuePair) = CFHelper.GetMappedFieldValuePairs(chart)
            Dim batch As New BatchCompiler
            batch.Batch.BLabel = CFHelper.OptionsDict("BatchLabel") '"JJC Test... where are gonna read this from ... hmmmm"

            Dim doc As New Document


            ' Added 2012/09/04 - DAO
            If CFHelper.OptionsDict("UseFullPathPData") Then
                Dim full_file_path As String = Path.Combine(CFHelper.OptionsDict("ImageOutputPath"), chart.ImageFileName)
                doc.Pages.Add(New Page(full_file_path))
            Else
                doc.Pages.Add(New Page(chart.ImageFileName))
            End If

            For Each map_pair As MapFieldValuePair In map_field_values_list
                Dim indx As New Index(map_pair.Field, map_pair.Value)
                doc.Indexes.Add(indx)
            Next
            batch.Batch.Documents.Add(doc)

            Dim outPath As String = CFHelper.DetailsFileExportPath
            'Dim filetype = ".xml"
            'Dim fileNameWithoutEx = Path.GetFileNameWithoutExtension(chart.ImageFileName)
            'Dim fileName = Path.Combine(fileNameWithoutEx, filetype)
            Dim fileName = String.Format("{0}_{1}v{2}.{3}", chart.Chart.ChartInfo.Facility.FacilityID,
                                        chart.Chart.VisitID, chart.Chart.Version, "xml")

            Dim fullPath = Path.Combine(My.Application.Info.DirectoryPath, outPath)

            If Not Directory.Exists(fullPath) Then
                Directory.CreateDirectory(fullPath)
            End If

            fullPath = Path.Combine(fullPath, fileName)

            Dim fs As New FileStream(fullPath, FileMode.Create)
            Dim x As New Xml.Serialization.XmlSerializer(GetType(BatchCompiler))
            'Dim ts As New XmlTextWriter(fs, System.Text.Encoding.UTF8)

            x.Serialize(fs, batch)
            fs.Flush()
            fs.Close()

            'ShowMessage.Show(String.Format("CreateDetail:DOChartInfo({0})", chart.Chart.ChartInfo.Oid))
            Return True
        End Function
        Public Sub CreateFooter() Implements IExportChargeSummaryDetailsFile.CreateFooter
        End Sub
        Public Sub CreateHeader() Implements IExportChargeSummaryDetailsFile.CreateHeader
        End Sub
        Public Sub WriteFileToDestination() Implements IExportChargeSummaryDetailsFile.WriteFileToDestination
        End Sub
        Public Sub WrapUp() Implements IExportChargeSummaryDetailsFile.WrapUp

        End Sub
#End Region 'Methods

    End Class

    Public Class HPFColdFeedDetailsFakeFileHandler
        Implements IExportChargeSummaryDetailsFile

#Region "Fields"

        Protected OuputPath As String

        Private _cFHelper As ColdFeedHelper

#End Region 'Fields

#Region "Properties"

        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryDetailsFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property

#End Region 'Properties
        Public Sub Initialize() Implements IExportChargeSummaryDetailsFile.Initialize

        End Sub
        Public Function CreateDetail(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryDetailsFile.CreateDetail
            Return True
        End Function
        Public Sub CreateFooter() Implements IExportChargeSummaryDetailsFile.CreateFooter

        End Sub
        Public Sub CreateHeader() Implements IExportChargeSummaryDetailsFile.CreateHeader

        End Sub
        Public Sub WriteFileToDestination() Implements IExportChargeSummaryDetailsFile.WriteFileToDestination

        End Sub
        Public Sub WrapUp() Implements IExportChargeSummaryDetailsFile.WrapUp

        End Sub
    End Class

    Public Class SingleDelimitedFileHandler
        Implements IExportChargeSummaryDetailsFile

#Region "Fields"

        Protected _output_path As String
        Private _cf_helper As ColdFeedHelper
        Private _delimiter As String = ","
        Private _field_definitions As List(Of DOColdFeedFieldMap)
        Private _is_initialized As Boolean = False

        Private _detail_rows As List(Of String)

#End Region 'Fields

#Region "Properties"
        Public Property CFHelper As ColdFeedHelper Implements IExportChargeSummaryDetailsFile.CFHelper
            Get
                Return _cf_helper
            End Get
            Set(value As ColdFeedHelper)
                _cf_helper = value
            End Set
        End Property
        Public Property OutputPath As String
            Get
                Return _output_path
            End Get
            Set(value As String)
                _output_path = value
            End Set
        End Property
        Public Property Delimiter As String
            Get
                Return _delimiter
            End Get
            Set(value As String)
                _delimiter = value
            End Set
        End Property
        'Public Property FieldDefinitions As List(Of DOColdFeedFieldMap)
        '    Get
        '        Return _field_definitions
        '    End Get
        '    Set(value As List(Of DOColdFeedFieldMap))
        '        _field_definitions = value
        '    End Set
        'End Property
        Public Property DetailRows As List(Of String)
            Get
                Return _detail_rows
            End Get
            Set(value As List(Of String))
                _detail_rows = value
            End Set
        End Property
#End Region

        Private Sub Initialize() Implements IExportChargeSummaryDetailsFile.Initialize
            Dim filename As String = String.Format("{0}_{1}.{2}", CFHelper.GetCustomSettingValue("IndexFilePrefix"), Now.ToString("yyyyMMdd-HHmm"), "txt")
            Dim config_output_path As String = CFHelper.DetailsFileExportPath

            Dim application_path = Path.Combine(My.Application.Info.DirectoryPath, config_output_path)

            OutputPath = Path.Combine(application_path, filename)
            DetailRows = New List(Of String)

            Delimiter = CFHelper.GetCustomSettingValue("FieldDelimiter")

            'FieldDefinitions = CFHelper.FieldMapDefList
        End Sub
        Public Function CreateDetail(chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryDetailsFile.CreateDetail
            Dim sb As New StringBuilder
            With sb
                Dim map_field_values_list As List(Of MapFieldValuePair) = CFHelper.GetMappedFieldValuePairs(chart)

                For Each map_pair In map_field_values_list
                    .Append(map_pair.Value)
                    If map_field_values_list.IndexOf(map_pair) < (map_field_values_list.Count - 1) Then
                        .Append(Delimiter)
                    End If
                Next
            End With

            DetailRows.Add(sb.ToString)
            Return True
        End Function
        Public Sub CreateFooter() Implements IExportChargeSummaryDetailsFile.CreateFooter

        End Sub
        Public Sub CreateHeader() Implements IExportChargeSummaryDetailsFile.CreateHeader

        End Sub
        Public Sub WriteFileToDestination() Implements IExportChargeSummaryDetailsFile.WriteFileToDestination

        End Sub
        Public Sub WrapUp() Implements IExportChargeSummaryDetailsFile.WrapUp

            If DetailRows.Count > 0 Then

                Dim sb As New StringBuilder
                With sb
                    For Each dr As String In DetailRows
                        If DetailRows.IndexOf(dr) = DetailRows.Count - 1 Then
                            .Append(dr)
                        Else
                            .AppendLine(dr)
                        End If
                    Next
                End With

                Using sw As New StreamWriter(OutputPath)
                    sw.Write(sb.ToString)
                End Using

            End If

        End Sub
    End Class

End Namespace


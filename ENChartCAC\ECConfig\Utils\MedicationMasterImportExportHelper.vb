﻿Option Infer On

Imports System.IO
Imports System.Linq
Imports System.Reflection
Imports System.Text
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports Newtonsoft.Json

Public Class MedicationMasterImportExportHelper

    Public Sub New()

    End Sub

    Public Sub New(defaultSession As Session)
        _dbSession = defaultSession
    End Sub

    Private _dbSession As Session
    Public Property dbSession() As Session
        Get
            If _dbSession Is Nothing Then
                _dbSession = XpoDefault.Session
            End If
            Return _dbSession
        End Get
        Set(ByVal value As Session)

            _dbSession = value
        End Set
    End Property

#Region "User Interaction"
    Public Function GetFileToLoadFromUser() As FileInfo
        Dim ofd As New OpenFileDialog
        ofd.Filter = "Medications (*.txt) | *.txt| (*.csv)|*.csv"
        ofd.FileName = "Medications.txt"

        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then
            Return Nothing 'Exit Function
        End If

        Dim ffile As New FileInfo(ofd.FileName)
        Return ffile
    End Function

    Public Function GetShawnasFileToLoad() As FileInfo
        Dim ofd As New OpenFileDialog
        ofd.DefaultExt = "csv"
        ofd.Filter = "Comma Seperated File (*.csv) |*.csv| Text File |*.txt"
        ofd.InitialDirectory = Directory.GetCurrentDirectory
        'ofd.FileName = "AIC Paragon Med List Mapped to MEDIDs_FINAL"

        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then
            Return Nothing 'Exit Function
        End If

        Dim ffile As New FileInfo(ofd.FileName)
        Return ffile
    End Function

    Public Function GetJsonFileToLoadFromUser() As FileInfo
        Dim ofd As New OpenFileDialog
        ofd.Filter = "Medications definition file (*.json)|*.json"
        ofd.FileName = "Medications.json"

        Dim result As DialogResult = ofd.ShowDialog()
        If result = DialogResult.Cancel Then
            Return Nothing 'Exit Function
        End If

        Dim ffile As New FileInfo(ofd.FileName)
        Return ffile
    End Function
#End Region
    Public Function GetAllAsSimpleMedRecEx(Optional onlyGetEnabled As Boolean = False) As IEnumerable(Of DTOMedicationMaster)
        Return DOMedicationMaster.GetAllAsSimpleMedRecEx(onlyGetEnabled)
    End Function

    Public Function GetAllAsDOParagonMedicationData() As IEnumerable(Of DOParagonMedIdMapping)
        Return DOParagonMedIdMapping.GetAllAsDOs '.GetAllAsSimpleMedRecEx()
    End Function

    Public Sub LoadFromJason(filetoLoad As FileInfo)
        Dim medicationMastersInfo = LoadMedInfoFromJson(filetoLoad)
        SaveMedicationMasterInfoToDb(medicationMastersInfo)
    End Sub

    Public Function LoadMedInfoFromJson(fileToLoad As FileInfo) As DTOMedicationMasterInfo
        Dim data As String = File.ReadAllText(fileToLoad.FullName)
        Dim medInfo = JsonConvert.DeserializeObject(Of DTOMedicationMasterInfo)(data)
        Return medInfo
    End Function

    Public Sub SaveMedicationMasterInfoToDb(medicationMastersInfo As DTOMedicationMasterInfo)
        Dim existingMeds = GetAllAsSimpleMedRecEx(False)
        Dim newMedsToAdd = New List(Of DTOMedicationMaster)
        Dim dupmeds = New List(Of String)

        For Each medFromFile In medicationMastersInfo.MedicationsList
            Dim alreadyExists = False
            'If medFromFile.MedName.Contains("Abatacept") Then
            '    Debug.WriteLine("break")
            'End If
            For Each existingMed In existingMeds
                If existingMed.UID = medFromFile.UID Then
                    alreadyExists = True
                    Exit For
                End If
                If existingMed.MedName = medFromFile.MedName Then
                    alreadyExists = True
                    Exit For
                End If
            Next
            If Not alreadyExists Then
                'create new one
                newMedsToAdd.Add(medFromFile)
            End If
            If alreadyExists Then
                dupmeds.Add(medFromFile.MedName)
            End If
        Next
        DOMedicationMaster.AddNewFromList(newMedsToAdd)

        Dim newMedIdMappingsToAdd = New List(Of DTOParagonMedIdMapping)
        Dim existingMedIdMappings = GetAllAsDOParagonMedicationData()
        Dim dupMedMapping As New List(Of String)

        If medicationMastersInfo.ParagonMedIdMapping IsNot Nothing Then

            For Each medIdMapping In medicationMastersInfo.ParagonMedIdMapping
                Dim alreadyExists = False
                For Each existingMedIdMap In existingMedIdMappings
                    If existingMedIdMap.MedId = medIdMapping.MedId Then
                        alreadyExists = True
                        Exit For
                    End If
                Next

                'also check for dup medId from stuff being added
                For Each newMedIdMap In newMedIdMappingsToAdd
                    If newMedIdMap.MedId = medIdMapping.MedId Then
                        alreadyExists = True
                        Exit For
                    End If
                Next

                If Not alreadyExists Then
                    'create new one
                    Dim medMaster = FindMedicationMaster(medIdMapping.MedicationMaster, newMedsToAdd, existingMeds)
                    If medMaster IsNot Nothing Then
                        newMedIdMappingsToAdd.Add(medIdMapping)
                    End If
                End If
                If alreadyExists Then

                    dupMedMapping.Add(medIdMapping.MedId.ToString)
                End If
            Next

            WriteMedIdMappingsToDb(newMedIdMappingsToAdd, XpoDefault.Session)
        End If
    End Sub

    Public Sub LoadMedsFromCsv(fileToLoad As FileInfo)
        Dim Lines() As String = File.ReadAllLines(fileToLoad.FullName)

        Dim Medication As DOMedicationMaster = Nothing
        Dim o As Integer = 1000
        Dim linecount As Integer = 0
        'Using session As New Session
        'XpoDefault.Session.BeginTransaction()

        For Each Line As String In Lines
            linecount += 1
            Debug.WriteLine(Line)
            If Line.Length = 0 Then Continue For 'ignore blank lines
            Dim fields() As String = Split(Line, ",")
            Dim lMedName As String = fields(0)

            If fields.Count >= 8 Then

            ElseIf fields.Count = 5 Then
                Medication = New DOMedicationMaster
                Medication.Enabled = True
                If fields(0) <> "" Then
                    Medication.MedName = lMedName
                End If

                If fields(DOMedication.FieldsEnum2019.CC) <> "" Then
                    Medication.CC = True
                End If

                If fields(DOMedication.FieldsEnum2019.DedicatedLine) <> "" Then
                    Medication.DedicatedLine = True
                End If

                If fields(DOMedication.FieldsEnum2019.Chemo) <> "" Then
                    Medication.Chemo = True
                End If

                If fields(DOMedication.FieldsEnum2019.Hormonal) <> "" Then
                    Medication.Hormonal = True
                End If
            Else
                MessageBox.Show($"The file appears to have the wrong number of fields.")
                Exit Sub
            End If


            Medication.Save()
        Next

        'XpoDefault.Session.CommitTransaction()
    End Sub


    Public Sub LoadMedsFromCsv2(fileToLoad As FileInfo)
        Dim Lines() As String = File.ReadAllLines(fileToLoad.FullName)
        Dim newMedsToAdd = New List(Of DTOMedicationMaster)
        'Dim Medication As DOMedicationMaster = Nothing
        Dim Medication As DTOMedicationMaster = Nothing

        Dim linecount As Integer = 0
        'Using session As New Session
        'XpoDefault.Session.BeginTransaction()

        For Each Line As String In Lines
            linecount += 1
            Debug.WriteLine(Line)
            If Line.Length = 0 Then Continue For 'ignore blank lines
            Dim fields() As String = Split(Line, ",")
            Dim lMedName As String = fields(0)

            If fields.Count >= 8 Then

            ElseIf fields.Count = 5 Then
                Medication = New DTOMedicationMaster
                If fields(0) <> "" Then
                    Medication.MedName = lMedName
                End If

                If fields(DOMedication.FieldsEnum2019.CC) <> "" Then
                    Medication.CC = True
                End If

                If fields(DOMedication.FieldsEnum2019.DedicatedLine) <> "" Then
                    Medication.DedicatedLine = True
                End If

                If fields(DOMedication.FieldsEnum2019.Chemo) <> "" Then
                    Medication.Chemo = True
                End If

                If fields(DOMedication.FieldsEnum2019.Hormonal) <> "" Then
                    Medication.Hormonal = True
                End If
            Else
                MessageBox.Show($"The file appears to have the wrong number of fields.")
                Exit Sub
            End If


            'Medication.Save()
            newMedsToAdd.Add(Medication)
        Next
        SaveMedicationMasterInfoToDb(New DTOMedicationMasterInfo(newMedsToAdd, Nothing))
        'XpoDefault.Session.CommitTransaction()
    End Sub

    Public Sub SaveMedicationsToDiskAsCsv()
        Cursor.Current = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = AppContext.BaseDirectory
            fsa.Filter = "Medications Config File|*.txt"
            fsa.DefaultExt = "*.txt"
            fsa.FileName = "Medications.txt"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)
            Dim sb As New StringBuilder
            For Each med In DOMedicationMaster.GetAllAsDTOs()
                sb.Length = 0
                sb.Append(med.MedName + ",")
                sb.Append(IIf(med.CC, "X,", ","))
                sb.Append(IIf(med.DedicatedLine, "X,", ","))
                sb.Append(IIf(med.Chemo, "X,", ",")) '11.27.2012
                sb.Append(IIf(med.Hormonal, "X", "")) '05.2014
                sw.WriteLine(sb.ToString)
            Next
            sw.Close()

            MessageBox.Show("Medications successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub

    Sub SaveMedicationsToDiskAsJson()
        Cursor.Current = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = AppContext.BaseDirectory
            fsa.Filter = "Medications Config File|*.json"
            fsa.DefaultExt = "json"
            fsa.FileName = "Medications"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim blah = DOParagonMedIdMapping.GetAllAsDTOs()
            Dim medMasterDTOs = DOMedicationMaster.GetAllAsDTOs()

            Dim dtos As New DTOMedicationMasterInfo(DOMedicationMaster.GetAllAsDTOs(), DOParagonMedIdMapping.GetAllAsDTOs())

            Dim fi As New FileInfo(fsa.FileName)

            'File.WriteAllText(fi.FullName, JsonConvert.SerializeObject(DOMedicationMaster.GetEnabledAsSimpleMedRec(), Formatting.Indented))
            File.WriteAllText(fi.FullName, JsonConvert.SerializeObject(dtos, Formatting.Indented))

            MessageBox.Show("Medications successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub

    Sub WriteMedIdMappingsToDb(newMedIdMappingsToAdd As List(Of DTOParagonMedIdMapping), session As Session)
        Dim medicationMasters = New List(Of DOMedicationMaster)(DOMedicationMaster.GetAllAsDOs)
        Dim newDosToWriteToDb As New List(Of DOParagonMedIdMapping)
        For Each newMedIdMapping In newMedIdMappingsToAdd
            Dim newDo = New DOParagonMedIdMapping With {
                .MedicationMaster = LookupMedicationMasterAsDo(newMedIdMapping.MedicationMaster, session),
                .MedId = newMedIdMapping.MedId}
            newDosToWriteToDb.Add(newDo)
        Next

        session.Save(newDosToWriteToDb)
        Debug.WriteLine("success")
    End Sub

    Function LookupMedicationMasterAsDo(medicationMasterID As Guid, session As Session) As DOMedicationMaster
        Return session.GetObjectByKey(Of DOMedicationMaster)(medicationMasterID)
    End Function

    Function FindMedicationMaster(medicationMasterId As Guid, newMedsList As List(Of DTOMedicationMaster), existingMedsList As List(Of DTOMedicationMaster)) As DTOMedicationMaster
        For Each med In newMedsList
            If med.UID = medicationMasterId Then
                Return med
            End If
        Next

        For Each med In existingMedsList
            If med.UID = medicationMasterId Then
                Return med
            End If
        Next

        Return Nothing
    End Function

    Sub Merge()
        Dim oldMedsList As New XPCollection(Of DOMedication)
        Dim medNamesAddedList As New List(Of String)
        Cursor.Current = Cursors.WaitCursor
        Dim sw As New Stopwatch
        sw.Start()

        Try
            For Each oldMed In oldMedsList
                If String.IsNullOrEmpty(oldMed.MedName) Then Continue For
                If Not medNamesAddedList.Contains(oldMed.MedName.Trim, StringComparer.OrdinalIgnoreCase) Then
                    medNamesAddedList.Add(oldMed.MedName.Trim)

                    Dim medMaster As New DOMedicationMaster() With {
                        .MedName = oldMed.MedName.Trim,
                        .CC = oldMed.CC,
                        .Chemo = oldMed.Chemo,
                        .DedicatedLine = oldMed.DedicatedLine,
                        .Enabled = True,
                        .Hormonal = oldMed.Hormonal,
                        .UID = Guid.NewGuid
                    }
                    medMaster.Save()
                End If
            Next
            sw.Stop()
            Cursor.Current = Cursors.Default
            MessageBox.Show($"{medNamesAddedList.Count} facility specific meds successfully merged into Global meds table (DOMedicationMaster) in {sw.Elapsed}", "Merge Successfull", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"{ex.Message}", "Merge failed")
        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub

    Sub DeleteAll()
        'Using lUnitOfWork As New UnitOfWork
        '    lUnitOfWork.ExecuteNonQuery($"delete from {"DOParagonMedIdMapping"}")
        '    lUnitOfWork.ExecuteNonQuery($"delete from {"DOMedicationMaster"}")
        'End Using

        'dbSession.ExecuteNonQuery($"delete from {"DOParagonMedIdMapping"}")
        'dbSession.ExecuteNonQuery($"delete from {"DOMedicationMaster"}")
        'dbSession.DropIdentityMap()

        'method 2
        ' Using uow As New UnitOfWork
        dbSession.Delete(DOParagonMedIdMapping.GetAllAsDOs)
        dbSession.Delete(DOMedicationMaster.GetAllAsDOs)

    End Sub

    Function DOMedicationHasData() As Boolean
        Return DOMedicationMaster.HasData()
    End Function

    Sub LoadDataFromShawnasFile(fileToLoad As FileInfo)
        SaveMedicationMasterInfoToDb(LoadShawnasFileToMedicationMasterInfo(fileToLoad.FullName))
    End Sub

    'C:\Users\<USER>\Downloads\AIC Paragon Med List Mapped to MEDIDs_FINAL.csv
    Public Function LoadShawnasFileToMedicationMasterInfo(fileName As String) As DTOMedicationMasterInfo
        Dim returnMedicationMasterInfo As New DTOMedicationMasterInfo
        Dim medicationsMasterList As New List(Of DTOMedicationMaster) 'List(Of DOMedicationMaster)
        Dim medMappingList As New List(Of DTOParagonMedIdMapping) 'List(Of DOParagonMedIdMapping)
        Dim fi As New FileInfo(fileName)

        Dim Lines() As String = File.ReadAllLines(fi.FullName)
        Dim linecount = 1
        Dim maxMedName As Integer = Nothing
        For Each line In Lines
            If linecount = 1 Then
                linecount += 1
                Continue For
            End If

            Dim cols = line.Split(",")
            'Dim mmed As New DOMedicationMaster With {
            '        .UID = Guid.NewGuid(),
            '            .Enabled = True}

            Dim mmed As New DTOMedicationMaster With {.UID = Guid.NewGuid()}

            Dim colCount = 0
            For Each col In cols
                Select Case colCount
                    Case 0
                        mmed.MedName = col

                        If mmed.MedName.Length > maxMedName Then
                            maxMedName = mmed.MedName.Length
                        End If
                    Case 1
                        If String.IsNullOrWhiteSpace(col) Then
                            mmed.CC = False
                        Else
                            mmed.CC = True
                        End If
                    Case 2
                        If String.IsNullOrWhiteSpace(col) Then
                            mmed.DedicatedLine = False
                        Else
                            mmed.DedicatedLine = True
                        End If
                    Case 3
                        If String.IsNullOrWhiteSpace(col) Then
                            mmed.Chemo = False
                        Else
                            mmed.Chemo = True
                        End If
                    Case 4
                        If String.IsNullOrWhiteSpace(col) Then
                            mmed.Hormonal = False
                        Else
                            mmed.Hormonal = True
                        End If
                    Case >= 5
                        If String.IsNullOrWhiteSpace(col) Then
                            Continue For
                        End If
                        'Dim medData As New DOParagonMedIdMapping With {
                        '        .MedicationMaster = mmed,
                        '        .MedId = col}

                        Dim medData As New DTOParagonMedIdMapping With {
                                .MedicationMaster = mmed.UID,
                                .MedId = col}


                        medMappingList.Add(medData)
                End Select
                colCount += 1
            Next

            medicationsMasterList.Add(mmed)
            linecount += 1
        Next

        'For Each medMast In medicationsMasterList
        '    medMast.Save()
        'Next

        Dim duplist As New List(Of UInt32)
        Dim medIdList As New List(Of UInt32)
        For Each medMapping In medMappingList
            If medIdList.Contains(medMapping.MedId) Then
                duplist.Add(medMapping.MedId)
                Continue For
            Else
                medIdList.Add(medMapping.MedId)
            End If
            Try
                'medMapping.Save()

            Catch ex As Exception
                duplist.Add(medMapping.MedId)
            End Try
        Next

        Dim sb As New StringBuilder
        For Each medid In duplist
            sb.Append($"{medid}{vbCrLf}")
        Next

        Dim dupsAsString = sb.ToString

        returnMedicationMasterInfo.MedicationsList = medicationsMasterList
        returnMedicationMasterInfo.ParagonMedIdMapping = medMappingList
        Return returnMedicationMasterInfo
    End Function

End Class
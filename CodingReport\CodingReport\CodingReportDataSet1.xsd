﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="CodingReportDataSet1" targetNamespace="http://tempuri.org/CodingReportDataSet1.xsd" xmlns:mstns="http://tempuri.org/CodingReportDataSet1.xsd" xmlns="http://tempuri.org/CodingReportDataSet1.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="5" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.Odbc" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString1" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString1 (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.ConnectionString1" Provider="System.Data.OleDb" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="AsaODBC" IsAppSettingsProperty="true" Modifier="Assembly" Name="AsaODBC (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.AsaODBC" Provider="System.Data.Odbc" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="AsaOLE" IsAppSettingsProperty="true" Modifier="Assembly" Name="AsaOLE (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.AsaOLE" Provider="System.Data.OleDb" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString2" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString2 (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.ConnectionString2" Provider="System.Data.OleDb" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ASAOLE" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ASAOLE (MySettings)" PropertyReference="ApplicationSettings.CodingReport.My.MySettings.GlobalReference.Default.ASAOLE" Provider="System.Data.OleDb" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DOCodingReportTextTableAdapter" GeneratorDataComponentClassName="DOCodingReportTextTableAdapter" Name="DOCodingReportText" UserDataComponentName="DOCodingReportTextTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ASAOLE (MySettings)" DbObjectName="DBA.DOCodingReportText" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO "DBA"."CodingReportText" ("LineText") VALUES (?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="LineText" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LineText" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        LineText
FROM            DBA.DOCodingReportText
WHERE        (ReportBand = ?) AND (ConfigInstance = ?)
ORDER BY LineNumber</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="Param1" ColumnName="ReportBand" DataSourceName="DBA.DOCodingReportText" DataTypeServer="DbType.AnsiStringFixedLength(30)" DbType="AnsiString" Direction="Input" ParameterName="ReportBand" Precision="0" ProviderType="Char" Scale="0" Size="30" SourceColumn="ReportBand" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Param2" ColumnName="ConfigInstance" DataSourceName="DBA.DOCodingReportText" DataTypeServer="DbType.Int32" DbType="Int32" Direction="Input" ParameterName="ConfigInstance" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ConfigInstance" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="LineText" DataSetColumn="LineText" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DOChartInfoTableAdapter" GeneratorDataComponentClassName="DOChartInfoTableAdapter" Name="DOChartInfo" UserDataComponentName="DOChartInfoTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ASAOLE (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT     a.VisitID, a.Facility, a.ChartType, a.UsesPoints, d.OID AS Chart, d.Version AS ChartVersion, b.TreatmentArea, b.EMLevel, c.LongName, d.TreatmentArea AS tmpTxArea, 
                      d.ConfigInstanceVersion, d.PatientFirstName, d.PatientMiddleName, d.PatientLastName, d.PatientSuffix, b.EMLevelHCPCS, b.EMLevelCDM, 
                      b.ChartStatus, e.UserName, b.PhysChartStatus, d.CreationDate, d.DateOfService, b.ObsChartStatus
FROM         DBA.DOChartInfo a, DBA.DOFacility c, DBA.DOChart d, DBA.DOChartSummary b, DBA.DOUser e
WHERE     a.Facility = c.OID AND a.OID = d.ChartInfo AND d.OID = b.Chart AND d."User" = e.OID AND (a.GCRecord IS NULL) AND (d.OID = ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Param1" ColumnName="OID" DataSourceName="DBA.DOChart" DataTypeServer="DbType.Int32" DbType="Int32" Direction="Input" ParameterName="OID" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="VisitID" DataSetColumn="VisitID" />
              <Mapping SourceColumn="TreatmentArea" DataSetColumn="TreatmentArea" />
              <Mapping SourceColumn="Facility" DataSetColumn="Facility" />
              <Mapping SourceColumn="Chart" DataSetColumn="Chart" />
              <Mapping SourceColumn="ChartVersion" DataSetColumn="ChartVersion" />
              <Mapping SourceColumn="LongName" DataSetColumn="LongName" />
              <Mapping SourceColumn="EMLevel" DataSetColumn="EMLevel" />
              <Mapping SourceColumn="tmpTxArea" DataSetColumn="tmpTxArea" />
              <Mapping SourceColumn="ConfigInstanceVersion" DataSetColumn="ConfigInstanceVersion" />
              <Mapping SourceColumn="PatientFirstName" DataSetColumn="PatientFirstName" />
              <Mapping SourceColumn="PatientMiddleName" DataSetColumn="PatientMiddleName" />
              <Mapping SourceColumn="PatientLastName" DataSetColumn="PatientLastName" />
              <Mapping SourceColumn="EMLevelHCPCS" DataSetColumn="EMLevelHCPCS" />
              <Mapping SourceColumn="EMLevelCDM" DataSetColumn="EMLevelCDM" />
              <Mapping SourceColumn="ChartStatus" DataSetColumn="ChartStatus" />
              <Mapping SourceColumn="PatientSuffix" DataSetColumn="PatientSuffix" />
              <Mapping SourceColumn="UserName" DataSetColumn="UserName" />
              <Mapping SourceColumn="PhysChartStatus" DataSetColumn="PhysChartStatus" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="ObsChartStatus" DataSetColumn="ObsChartStatus" />
              <Mapping SourceColumn="ChartType" DataSetColumn="ChartType" />
              <Mapping SourceColumn="UsesPoints" DataSetColumn="UsesPoints" />
              <Mapping SourceColumn="DateOfService" DataSetColumn="DateOfService1" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DOCodingReportRecordTableAdapter" GeneratorDataComponentClassName="DOCodingReportRecordTableAdapter" Name="DOCodingReportRecord" UserDataComponentName="DOCodingReportRecordTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ASAOLE (MySettings)" DbObjectName="DBA.DOCodingReportRecord" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM "DBA"."DOCodingReportRecord" WHERE (((? = 1 AND "GCRecord" IS NULL) OR ("GCRecord" = ?)) AND ("OID" = ?) AND ((? = 1 AND "Chart" IS NULL) OR ("Chart" = ?)) AND ((? = 1 AND "ReportDisplayOrder" IS NULL) OR ("ReportDisplayOrder" = ?)) AND ((? = 1 AND "CDM" IS NULL) OR ("CDM" = ?)) AND ((? = 1 AND "HCPCS" IS NULL) OR ("HCPCS" = ?)) AND ((? = 1 AND "ICD9" IS NULL) OR ("ICD9" = ?)) AND ((? = 1 AND "NewProperty" IS NULL) OR ("NewProperty" = ?)) AND ((? = 1 AND "Modifier" IS NULL) OR ("Modifier" = ?)) AND ((? = 1 AND "InsertDate" IS NULL) OR ("InsertDate" = ?)) AND ((? = 1 AND "OptimisticLockField" IS NULL) OR ("OptimisticLockField" = ?)) AND ((? = 1 AND "Detail" IS NULL) OR ("Detail" = ?)) AND ((? = 1 AND "Points" IS NULL) OR ("Points" = ?)) AND ((? = 1 AND "Quantity" IS NULL) OR ("Quantity" = ?)) AND ((? = 1 AND "IsPhysicianTypeCode" IS NULL) OR ("IsPhysicianTypeCode" = ?)) AND ((? = 1 AND "CodeType" IS NULL) OR ("CodeType" = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_OID" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_CDM" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_CDM" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HCPCS" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_HCPCS" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ICD9" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_ICD9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Modifier" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_Modifier" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_InsertDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_InsertDate" Precision="0" ProviderType="DBTimeStamp" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Detail" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_Detail" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_IsPhysicianTypeCode" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="Original_IsPhysicianTypeCode" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_CodeType" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_CodeType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO "DBA"."DOCodingReportRecord" ("GCRecord", "Chart", "ReportDisplayOrder", "CDM", "HCPCS", "ICD9", "NewProperty", "Modifier", "InsertDate", "OptimisticLockField", "Detail", "Points", "Quantity", "IsPhysicianTypeCode", "CodeType") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="CDM" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="HCPCS" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="ICD9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Modifier" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="InsertDate" Precision="0" ProviderType="DBTimeStamp" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Detail" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="IsPhysicianTypeCode" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="CodeType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT     GCRecord, OID, Chart, ReportDisplayOrder, CDM, HCPCS, ICD9, NewProperty, Modifier, InsertDate, OptimisticLockField, Detail, Points, Quantity, 
                      IsPhysicianTypeCode, CodeType
FROM         DBA.DOCodingReportRecord
WHERE     (Chart = ?)
ORDER BY ReportDisplayOrder</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="Param1" ColumnName="Chart" DataSourceName="DBA.DOCodingReportRecord" DataTypeServer="DbType.Int32" DbType="Int32" Direction="Input" ParameterName="Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE "DBA"."DOCodingReportRecord" SET "GCRecord" = ?, "Chart" = ?, "ReportDisplayOrder" = ?, "CDM" = ?, "HCPCS" = ?, "ICD9" = ?, "NewProperty" = ?, "Modifier" = ?, "InsertDate" = ?, "OptimisticLockField" = ?, "Detail" = ?, "Points" = ?, "Quantity" = ?, "IsPhysicianTypeCode" = ?, "CodeType" = ? WHERE (((? = 1 AND "GCRecord" IS NULL) OR ("GCRecord" = ?)) AND ("OID" = ?) AND ((? = 1 AND "Chart" IS NULL) OR ("Chart" = ?)) AND ((? = 1 AND "ReportDisplayOrder" IS NULL) OR ("ReportDisplayOrder" = ?)) AND ((? = 1 AND "CDM" IS NULL) OR ("CDM" = ?)) AND ((? = 1 AND "HCPCS" IS NULL) OR ("HCPCS" = ?)) AND ((? = 1 AND "ICD9" IS NULL) OR ("ICD9" = ?)) AND ((? = 1 AND "NewProperty" IS NULL) OR ("NewProperty" = ?)) AND ((? = 1 AND "Modifier" IS NULL) OR ("Modifier" = ?)) AND ((? = 1 AND "InsertDate" IS NULL) OR ("InsertDate" = ?)) AND ((? = 1 AND "OptimisticLockField" IS NULL) OR ("OptimisticLockField" = ?)) AND ((? = 1 AND "Detail" IS NULL) OR ("Detail" = ?)) AND ((? = 1 AND "Points" IS NULL) OR ("Points" = ?)) AND ((? = 1 AND "Quantity" IS NULL) OR ("Quantity" = ?)) AND ((? = 1 AND "IsPhysicianTypeCode" IS NULL) OR ("IsPhysicianTypeCode" = ?)) AND ((? = 1 AND "CodeType" IS NULL) OR ("CodeType" = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="CDM" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="HCPCS" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="ICD9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Modifier" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="InsertDate" Precision="0" ProviderType="DBTimeStamp" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Detail" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="IsPhysicianTypeCode" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="CodeType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_GCRecord" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="GCRecord" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_OID" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Chart" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Chart" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_ReportDisplayOrder" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportDisplayOrder" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_CDM" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_CDM" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CDM" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HCPCS" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_HCPCS" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="HCPCS" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ICD9" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_ICD9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ICD9" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_NewProperty" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="NewProperty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Modifier" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_Modifier" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Modifier" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_InsertDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_InsertDate" Precision="0" ProviderType="DBTimeStamp" Scale="0" Size="0" SourceColumn="InsertDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_OptimisticLockField" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="OptimisticLockField" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Detail" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_Detail" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Detail" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Points" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Points" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Quantity" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Quantity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_IsPhysicianTypeCode" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="Original_IsPhysicianTypeCode" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="IsPhysicianTypeCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_CodeType" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="Original_CodeType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CodeType" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GCRecord" DataSetColumn="GCRecord" />
              <Mapping SourceColumn="OID" DataSetColumn="OID" />
              <Mapping SourceColumn="Chart" DataSetColumn="Chart" />
              <Mapping SourceColumn="ReportDisplayOrder" DataSetColumn="ReportDisplayOrder" />
              <Mapping SourceColumn="CDM" DataSetColumn="CDM" />
              <Mapping SourceColumn="HCPCS" DataSetColumn="HCPCS" />
              <Mapping SourceColumn="ICD9" DataSetColumn="ICD9" />
              <Mapping SourceColumn="NewProperty" DataSetColumn="NewProperty" />
              <Mapping SourceColumn="Modifier" DataSetColumn="Modifier" />
              <Mapping SourceColumn="InsertDate" DataSetColumn="InsertDate" />
              <Mapping SourceColumn="OptimisticLockField" DataSetColumn="OptimisticLockField" />
              <Mapping SourceColumn="Detail" DataSetColumn="Detail" />
              <Mapping SourceColumn="Points" DataSetColumn="Points" />
              <Mapping SourceColumn="Quantity" DataSetColumn="Quantity" />
              <Mapping SourceColumn="IsPhysicianTypeCode" DataSetColumn="IsPhysicianTypeCode" />
              <Mapping SourceColumn="CodeType" DataSetColumn="CodeType" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources>
          <DbSource ConnectionRef="ASAOLE (MySettings)" DbObjectName="DBA.FacilitySettings" DbObjectType="Table" GenerateShortCommands="true" GeneratorSourceName="FacilityFontSize" MethodsParameterType="CLR" Modifier="Public" Name="FacilityFontSize" QueryType="Scalar" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="FacilityFontSize">
            <SelectCommand>
              <DbCommand CommandType="Text" ModifiedByUser="true">
                <CommandText>SELECT        RuleSettingValue
FROM            DBA.FacilitySettings
WHERE        (Facility = ?) AND (RuleSetting = 'CodingReportDetailFontSize')</CommandText>
                <Parameters>
                  <Parameter AllowDbNull="false" AutogeneratedName="Param1" ColumnName="Facility" DataSourceName="DBA.FacilitySettings" DataTypeServer="DbType.Int32" DbType="Int32" Direction="Input" ParameterName="Facility" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Facility" SourceColumnNullMapping="false" SourceVersion="Current" />
                </Parameters>
              </DbCommand>
            </SelectCommand>
          </DbSource>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="CodingReportDataSet1" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="CodingReportDataSet1" msprop:Generator_DataSetName="CodingReportDataSet1">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DOCodingReportText" msprop:Generator_UserTableName="DOCodingReportText" msprop:Generator_RowDeletedName="DOCodingReportTextRowDeleted" msprop:Generator_RowChangedName="DOCodingReportTextRowChanged" msprop:Generator_RowClassName="DOCodingReportTextRow" msprop:Generator_RowChangingName="DOCodingReportTextRowChanging" msprop:Generator_RowEvArgName="DOCodingReportTextRowChangeEvent" msprop:Generator_RowEvHandlerName="DOCodingReportTextRowChangeEventHandler" msprop:Generator_TableClassName="DOCodingReportTextDataTable" msprop:Generator_TableVarName="tableDOCodingReportText" msprop:Generator_RowDeletingName="DOCodingReportTextRowDeleting" msprop:Generator_TablePropName="DOCodingReportText">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="LineText" msprop:Generator_UserColumnName="LineText" msprop:Generator_ColumnPropNameInRow="LineText" msprop:Generator_ColumnVarNameInTable="columnLineText" msprop:Generator_ColumnPropNameInTable="LineTextColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DOCodingReportRecord" msprop:Generator_UserTableName="DOCodingReportRecord" msprop:Generator_RowDeletedName="DOCodingReportRecordRowDeleted" msprop:Generator_RowChangedName="DOCodingReportRecordRowChanged" msprop:Generator_RowClassName="DOCodingReportRecordRow" msprop:Generator_RowChangingName="DOCodingReportRecordRowChanging" msprop:Generator_RowEvArgName="DOCodingReportRecordRowChangeEvent" msprop:Generator_RowEvHandlerName="DOCodingReportRecordRowChangeEventHandler" msprop:Generator_TableClassName="DOCodingReportRecordDataTable" msprop:Generator_TableVarName="tableDOCodingReportRecord" msprop:Generator_RowDeletingName="DOCodingReportRecordRowDeleting" msprop:Generator_TablePropName="DOCodingReportRecord">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GCRecord" msprop:Generator_UserColumnName="GCRecord" msprop:Generator_ColumnPropNameInRow="GCRecord" msprop:Generator_ColumnVarNameInTable="columnGCRecord" msprop:Generator_ColumnPropNameInTable="GCRecordColumn" type="xs:int" minOccurs="0" />
              <xs:element name="OID" msdata:AutoIncrement="true" msprop:Generator_UserColumnName="OID" msprop:Generator_ColumnPropNameInRow="OID" msprop:Generator_ColumnVarNameInTable="columnOID" msprop:Generator_ColumnPropNameInTable="OIDColumn" type="xs:int" />
              <xs:element name="Chart" msprop:Generator_UserColumnName="Chart" msprop:Generator_ColumnPropNameInRow="Chart" msprop:Generator_ColumnVarNameInTable="columnChart" msprop:Generator_ColumnPropNameInTable="ChartColumn" type="xs:int" />
              <xs:element name="ReportDisplayOrder" msprop:Generator_UserColumnName="ReportDisplayOrder" msprop:Generator_ColumnPropNameInRow="ReportDisplayOrder" msprop:Generator_ColumnVarNameInTable="columnReportDisplayOrder" msprop:Generator_ColumnPropNameInTable="ReportDisplayOrderColumn" type="xs:int" minOccurs="0" />
              <xs:element name="CDM" msprop:Generator_UserColumnName="CDM" msprop:Generator_ColumnPropNameInRow="CDM" msprop:Generator_ColumnVarNameInTable="columnCDM" msprop:Generator_ColumnPropNameInTable="CDMColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="HCPCS" msprop:Generator_UserColumnName="HCPCS" msprop:Generator_ColumnPropNameInRow="HCPCS" msprop:Generator_ColumnVarNameInTable="columnHCPCS" msprop:Generator_ColumnPropNameInTable="HCPCSColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ICD9" msprop:Generator_UserColumnName="ICD9" msprop:Generator_ColumnPropNameInRow="ICD9" msprop:Generator_ColumnVarNameInTable="columnICD9" msprop:Generator_ColumnPropNameInTable="ICD9Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="NewProperty" msprop:Generator_UserColumnName="NewProperty" msprop:Generator_ColumnPropNameInRow="NewProperty" msprop:Generator_ColumnVarNameInTable="columnNewProperty" msprop:Generator_ColumnPropNameInTable="NewPropertyColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Modifier" msprop:Generator_UserColumnName="Modifier" msprop:Generator_ColumnPropNameInRow="Modifier" msprop:Generator_ColumnVarNameInTable="columnModifier" msprop:Generator_ColumnPropNameInTable="ModifierColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InsertDate" msprop:Generator_UserColumnName="InsertDate" msprop:Generator_ColumnPropNameInRow="InsertDate" msprop:Generator_ColumnVarNameInTable="columnInsertDate" msprop:Generator_ColumnPropNameInTable="InsertDateColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="OptimisticLockField" msprop:Generator_UserColumnName="OptimisticLockField" msprop:Generator_ColumnPropNameInRow="OptimisticLockField" msprop:Generator_ColumnVarNameInTable="columnOptimisticLockField" msprop:Generator_ColumnPropNameInTable="OptimisticLockFieldColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Detail" msprop:Generator_UserColumnName="Detail" msprop:Generator_ColumnPropNameInRow="Detail" msprop:Generator_ColumnVarNameInTable="columnDetail" msprop:Generator_ColumnPropNameInTable="DetailColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Points" msprop:Generator_UserColumnName="Points" msprop:Generator_ColumnPropNameInRow="Points" msprop:Generator_ColumnVarNameInTable="columnPoints" msprop:Generator_ColumnPropNameInTable="PointsColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Quantity" msprop:Generator_UserColumnName="Quantity" msprop:Generator_ColumnPropNameInRow="Quantity" msprop:Generator_ColumnVarNameInTable="columnQuantity" msprop:Generator_ColumnPropNameInTable="QuantityColumn" type="xs:int" minOccurs="0" />
              <xs:element name="IsPhysicianTypeCode" msprop:Generator_UserColumnName="IsPhysicianTypeCode" msprop:Generator_ColumnPropNameInRow="IsPhysicianTypeCode" msprop:Generator_ColumnVarNameInTable="columnIsPhysicianTypeCode" msprop:Generator_ColumnPropNameInTable="IsPhysicianTypeCodeColumn" type="xs:boolean" minOccurs="0" />
              <xs:element name="CodeType" msprop:Generator_UserColumnName="CodeType" msprop:Generator_ColumnVarNameInTable="columnCodeType" msprop:Generator_ColumnPropNameInRow="CodeType" msprop:Generator_ColumnPropNameInTable="CodeTypeColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DOChartInfo" msprop:Generator_UserTableName="DOChartInfo" msprop:Generator_RowDeletedName="DOChartInfoRowDeleted" msprop:Generator_RowChangedName="DOChartInfoRowChanged" msprop:Generator_RowClassName="DOChartInfoRow" msprop:Generator_RowChangingName="DOChartInfoRowChanging" msprop:Generator_RowEvArgName="DOChartInfoRowChangeEvent" msprop:Generator_RowEvHandlerName="DOChartInfoRowChangeEventHandler" msprop:Generator_TableClassName="DOChartInfoDataTable" msprop:Generator_TableVarName="tableDOChartInfo" msprop:Generator_RowDeletingName="DOChartInfoRowDeleting" msprop:Generator_TablePropName="DOChartInfo">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VisitID" msprop:Generator_UserColumnName="VisitID" msprop:Generator_ColumnPropNameInRow="VisitID" msprop:Generator_ColumnVarNameInTable="columnVisitID" msprop:Generator_ColumnPropNameInTable="VisitIDColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="45" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TreatmentArea" msprop:Generator_UserColumnName="TreatmentArea" msprop:Generator_ColumnPropNameInRow="TreatmentArea" msprop:Generator_ColumnVarNameInTable="columnTreatmentArea" msprop:Generator_ColumnPropNameInTable="TreatmentAreaColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Facility" msprop:Generator_UserColumnName="Facility" msprop:Generator_ColumnPropNameInRow="Facility" msprop:Generator_ColumnVarNameInTable="columnFacility" msprop:Generator_ColumnPropNameInTable="FacilityColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Chart" msprop:Generator_UserColumnName="Chart" msprop:Generator_ColumnPropNameInRow="Chart" msprop:Generator_ColumnVarNameInTable="columnChart" msprop:Generator_ColumnPropNameInTable="ChartColumn" type="xs:int" />
              <xs:element name="ChartVersion" msprop:Generator_UserColumnName="ChartVersion" msprop:Generator_ColumnPropNameInRow="ChartVersion" msprop:Generator_ColumnVarNameInTable="columnChartVersion" msprop:Generator_ColumnPropNameInTable="ChartVersionColumn" type="xs:int" minOccurs="0" />
              <xs:element name="LongName" msprop:Generator_UserColumnName="LongName" msprop:Generator_ColumnPropNameInRow="LongName" msprop:Generator_ColumnVarNameInTable="columnLongName" msprop:Generator_ColumnPropNameInTable="LongNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EMLevel" msprop:Generator_UserColumnName="EMLevel" msprop:Generator_ColumnPropNameInRow="EMLevel" msprop:Generator_ColumnVarNameInTable="columnEMLevel" msprop:Generator_ColumnPropNameInTable="EMLevelColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmpTxArea" msprop:Generator_UserColumnName="tmpTxArea" msprop:Generator_ColumnPropNameInRow="tmpTxArea" msprop:Generator_ColumnVarNameInTable="columntmpTxArea" msprop:Generator_ColumnPropNameInTable="tmpTxAreaColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ConfigInstanceVersion" msprop:Generator_UserColumnName="ConfigInstanceVersion" msprop:Generator_ColumnPropNameInRow="ConfigInstanceVersion" msprop:Generator_ColumnVarNameInTable="columnConfigInstanceVersion" msprop:Generator_ColumnPropNameInTable="ConfigInstanceVersionColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PatientFirstName" msprop:Generator_UserColumnName="PatientFirstName" msprop:Generator_ColumnVarNameInTable="columnPatientFirstName" msprop:Generator_ColumnPropNameInRow="PatientFirstName" msprop:Generator_ColumnPropNameInTable="PatientFirstNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PatientMiddleName" msprop:Generator_UserColumnName="PatientMiddleName" msprop:Generator_ColumnVarNameInTable="columnPatientMiddleName" msprop:Generator_ColumnPropNameInRow="PatientMiddleName" msprop:Generator_ColumnPropNameInTable="PatientMiddleNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PatientLastName" msprop:Generator_UserColumnName="PatientLastName" msprop:Generator_ColumnVarNameInTable="columnPatientLastName" msprop:Generator_ColumnPropNameInRow="PatientLastName" msprop:Generator_ColumnPropNameInTable="PatientLastNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EMLevelHCPCS" msprop:Generator_UserColumnName="EMLevelHCPCS" msprop:Generator_ColumnPropNameInRow="EMLevelHCPCS" msprop:Generator_ColumnVarNameInTable="columnEMLevelHCPCS" msprop:Generator_ColumnPropNameInTable="EMLevelHCPCSColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EMLevelCDM" msprop:Generator_UserColumnName="EMLevelCDM" msprop:Generator_ColumnPropNameInRow="EMLevelCDM" msprop:Generator_ColumnVarNameInTable="columnEMLevelCDM" msprop:Generator_ColumnPropNameInTable="EMLevelCDMColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChartStatus" msprop:Generator_UserColumnName="ChartStatus" msprop:Generator_ColumnVarNameInTable="columnChartStatus" msprop:Generator_ColumnPropNameInRow="ChartStatus" msprop:Generator_ColumnPropNameInTable="ChartStatusColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PatientSuffix" msprop:Generator_UserColumnName="PatientSuffix" msprop:Generator_ColumnPropNameInRow="PatientSuffix" msprop:Generator_ColumnVarNameInTable="columnPatientSuffix" msprop:Generator_ColumnPropNameInTable="PatientSuffixColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="UserName" msprop:Generator_UserColumnName="UserName" msprop:Generator_ColumnPropNameInRow="UserName" msprop:Generator_ColumnVarNameInTable="columnUserName" msprop:Generator_ColumnPropNameInTable="UserNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PhysChartStatus" msprop:Generator_UserColumnName="PhysChartStatus" msprop:Generator_ColumnPropNameInRow="PhysChartStatus" msprop:Generator_ColumnVarNameInTable="columnPhysChartStatus" msprop:Generator_ColumnPropNameInTable="PhysChartStatusColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_UserColumnName="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="DateOfService" msprop:Generator_UserColumnName="DateOfService" msprop:Generator_ColumnVarNameInTable="columnDateOfService" msprop:Generator_ColumnPropNameInRow="DateOfService" msprop:Generator_ColumnPropNameInTable="DateOfServiceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ObsChartStatus" msprop:Generator_UserColumnName="ObsChartStatus" msprop:Generator_ColumnPropNameInRow="ObsChartStatus" msprop:Generator_ColumnVarNameInTable="columnObsChartStatus" msprop:Generator_ColumnPropNameInTable="ObsChartStatusColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChartType" msprop:Generator_UserColumnName="ChartType" msprop:Generator_ColumnVarNameInTable="columnChartType" msprop:Generator_ColumnPropNameInRow="ChartType" msprop:Generator_ColumnPropNameInTable="ChartTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="UsesPoints" msprop:Generator_UserColumnName="UsesPoints" msprop:Generator_ColumnPropNameInRow="UsesPoints" msprop:Generator_ColumnVarNameInTable="columnUsesPoints" msprop:Generator_ColumnPropNameInTable="UsesPointsColumn" type="xs:boolean" minOccurs="0" />
              <xs:element name="DateOfService1" msdata:Caption="DateOfService" msprop:Generator_UserColumnName="DateOfService1" msprop:Generator_ColumnPropNameInRow="DateOfService1" msprop:Generator_ColumnVarNameInTable="columnDateOfService1" msprop:Generator_ColumnPropNameInTable="DateOfService1Column" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:DOCodingReportRecord" />
      <xs:field xpath="mstns:OID" />
      <xs:field xpath="mstns:Chart" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="DOChartInfo_DOCodingReportRecord" msdata:parent="DOChartInfo" msdata:child="DOCodingReportRecord" msdata:parentkey="Chart" msdata:childkey="Chart" msprop:Generator_UserRelationName="DOChartInfo_DOCodingReportRecord" msprop:Generator_RelationVarName="relationDOChartInfo_DOCodingReportRecord" msprop:Generator_UserChildTable="DOCodingReportRecord" msprop:Generator_UserParentTable="DOChartInfo" msprop:Generator_ParentPropName="DOChartInfoRow" msprop:Generator_ChildPropName="GetDOCodingReportRecordRows" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>
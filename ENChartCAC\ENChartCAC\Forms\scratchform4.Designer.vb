﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class scratchform4
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        GroupControl2 = New GroupControl()
        XtraScrollableControl9 = New XtraScrollableControl()
        Infusion_Hydrations05_lbl = New LabelControl()
        Infusion_Hydrations04_lbl = New LabelControl()
        Infusion_Hydrations03_lbl = New LabelControl()
        Infusion_Hydrations02_lbl = New LabelControl()
        Infusion_Hydrations01_lbl = New LabelControl()
        Infusion_HydrationsMins05_lbl = New LabelControl()
        Infusion_HydrationsMins04_lbl = New LabelControl()
        Infusion_HydrationsMins03_lbl = New LabelControl()
        Infusion_HydrationsMins02_lbl = New LabelControl()
        Infusion_HydrationsMins01_lbl = New LabelControl()
        Infusion_HydrationsEndTime04_txt = New TextEdit()
        Infusion_HydrationsEndDate04_dte = New DateEdit()
        Infusion_HydrationsEndTime03_txt = New TextEdit()
        Infusion_HydrationsEndDate03_dte = New DateEdit()
        Infusion_HydrationsEndTime05_txt = New TextEdit()
        Infusion_HydrationsEndTime02_txt = New TextEdit()
        Infusion_HydrationsEndTime01_txt = New TextEdit()
        Infusion_HydrationsEndDate05_dte = New DateEdit()
        Infusion_HydrationsEndDate02_dte = New DateEdit()
        Infusion_HydrationsEndDate01_dte = New DateEdit()
        Infusion_HydrationsStartTime04_txt = New TextEdit()
        Infusion_HydrationsStartDate04_dte = New DateEdit()
        Infusion_HydrationsStartTime03_txt = New TextEdit()
        Infusion_HydrationsStartDate03_dte = New DateEdit()
        Infusion_HydrationsStartTime05_txt = New TextEdit()
        Infusion_HydrationsStartTime02_txt = New TextEdit()
        Infusion_HydrationsStartDate05_dte = New DateEdit()
        Infusion_HydrationsStartDate02_dte = New DateEdit()
        Infusion_HydrationsStartDate01_dte = New DateEdit()
        Infusion_HydrationsRate05_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate04_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate03_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate02_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate01_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite05_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite04_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite03_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite02_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite01_cbo = New ComboBoxEdit()
        Infusion_Hydrations12_lbl = New LabelControl()
        Infusion_Hydrations11_lbl = New LabelControl()
        Infusion_HydrationsStartTime11_txt = New TextEdit()
        Infusion_HydrationsStartDate11_dte = New DateEdit()
        Infusion_HydrationsEndDate12_dte = New DateEdit()
        Infusion_HydrationsStartTime12_txt = New TextEdit()
        Infusion_HydrationsEndTime12_txt = New TextEdit()
        Infusion_HydrationsMins12_lbl = New LabelControl()
        Infusion_HydrationsSite11_cbo = New ComboBoxEdit()
        Infusion_HydrationsMins11_lbl = New LabelControl()
        Infusion_HydrationsSite12_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartDate12_dte = New DateEdit()
        Infusion_HydrationsEndDate11_dte = New DateEdit()
        Infusion_HydrationsEndTime11_txt = New TextEdit()
        Infusion_HydrationsRate12_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate11_cbo = New ComboBoxEdit()
        Infusion_Hydrations10_lbl = New LabelControl()
        Infusion_Hydrations09_lbl = New LabelControl()
        Infusion_HydrationsEndDate06_dte = New DateEdit()
        Infusion_HydrationsStartTime09_txt = New TextEdit()
        Infusion_Hydrations08_lbl = New LabelControl()
        Infusion_HydrationsEndDate07_dte = New DateEdit()
        Infusion_HydrationsStartDate09_dte = New DateEdit()
        Infusion_Hydrations07_lbl = New LabelControl()
        Infusion_HydrationsEndDate10_dte = New DateEdit()
        Infusion_HydrationsStartTime08_txt = New TextEdit()
        Infusion_Hydrations06_lbl = New LabelControl()
        Infusion_HydrationsEndTime06_txt = New TextEdit()
        Infusion_HydrationsSite06_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartDate08_dte = New DateEdit()
        Infusion_HydrationsEndTime07_txt = New TextEdit()
        Infusion_HydrationsSite07_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartTime010_txt = New TextEdit()
        Infusion_HydrationsEndTime10_txt = New TextEdit()
        Infusion_HydrationsSite08_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartTime07_txt = New TextEdit()
        Infusion_HydrationsMins10_lbl = New LabelControl()
        Infusion_HydrationsEndDate08_dte = New DateEdit()
        Infusion_HydrationsSite09_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartTime06_txt = New TextEdit()
        Infusion_HydrationsMins09_lbl = New LabelControl()
        Infusion_HydrationsEndTime08_txt = New TextEdit()
        Infusion_HydrationsSite10_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartDate10_dte = New DateEdit()
        Infusion_HydrationsMins08_lbl = New LabelControl()
        Infusion_HydrationsEndDate09_dte = New DateEdit()
        Infusion_HydrationsRate06_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartDate07_dte = New DateEdit()
        Infusion_HydrationsMins07_lbl = New LabelControl()
        Infusion_HydrationsEndTime09_txt = New TextEdit()
        Infusion_HydrationsRate07_cbo = New ComboBoxEdit()
        Infusion_HydrationsStartDate06_dte = New DateEdit()
        Infusion_HydrationsMins06_lbl = New LabelControl()
        Infusion_HydrationsRate08_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate10_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate09_cbo = New ComboBoxEdit()
        LabelControl75 = New LabelControl()
        LabelControl76 = New LabelControl()
        LabelControl81 = New LabelControl()
        LabelControl82 = New LabelControl()
        LabelControl92 = New LabelControl()
        LabelControl93 = New LabelControl()
        LabelControl94 = New LabelControl()
        Infusion_HydrationsStartTime01_txt = New TimeEdit()
        CType(GroupControl2, ComponentModel.ISupportInitialize).BeginInit()
        GroupControl2.SuspendLayout()
        XtraScrollableControl9.SuspendLayout()
        CType(Infusion_HydrationsEndTime04_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime03_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime05_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime02_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime01_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime04_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime03_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime05_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime02_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate05_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate04_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate03_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate02_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate01_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite05_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite04_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite03_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite02_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite01_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime11_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate12_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime12_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime12_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite11_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite12_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate11_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime11_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate12_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate11_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate06_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate06_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime09_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate07_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate10_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime08_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime06_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite06_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime07_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite07_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime010_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime10_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite08_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime07_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate08_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite09_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime06_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime08_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite10_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate09_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate06_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime09_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate07_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate06_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate06_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate08_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate10_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate09_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime01_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' GroupControl2
        ' 
        GroupControl2.Controls.Add(XtraScrollableControl9)
        GroupControl2.Location = New Point(26, 43)
        GroupControl2.Margin = New Padding(2, 3, 2, 3)
        GroupControl2.Name = "GroupControl2"
        GroupControl2.Size = New Size(754, 252)
        GroupControl2.TabIndex = 14
        GroupControl2.Text = "Hydrations"
        ' 
        ' XtraScrollableControl9
        ' 
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations05_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations04_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations03_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations02_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations01_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins05_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins04_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins03_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins02_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins01_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime04_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate04_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime03_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate03_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime05_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime02_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime01_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate05_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate02_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate01_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime04_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate04_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime03_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate03_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime05_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime02_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate05_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate02_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate01_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate05_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate04_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate03_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate02_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate01_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite05_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite04_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite03_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite02_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite01_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations12_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations11_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime11_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate11_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate12_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime12_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime12_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins12_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite11_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins11_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite12_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate12_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate11_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime11_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate12_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate11_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations10_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations09_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate06_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime09_txt)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations08_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate07_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate09_dte)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations07_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate10_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime08_txt)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations06_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime06_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite06_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate08_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime07_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite07_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime010_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime10_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite08_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime07_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins10_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate08_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite09_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime06_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins09_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime08_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite10_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate10_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins08_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate09_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate06_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate07_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins07_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime09_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate07_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate06_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins06_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate08_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate10_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate09_cbo)
        XtraScrollableControl9.Controls.Add(LabelControl75)
        XtraScrollableControl9.Controls.Add(LabelControl76)
        XtraScrollableControl9.Controls.Add(LabelControl81)
        XtraScrollableControl9.Controls.Add(LabelControl82)
        XtraScrollableControl9.Controls.Add(LabelControl92)
        XtraScrollableControl9.Controls.Add(LabelControl93)
        XtraScrollableControl9.Controls.Add(LabelControl94)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime01_txt)
        XtraScrollableControl9.Dock = DockStyle.Fill
        XtraScrollableControl9.Location = New Point(2, 22)
        XtraScrollableControl9.Margin = New Padding(4, 3, 4, 3)
        XtraScrollableControl9.Name = "XtraScrollableControl9"
        XtraScrollableControl9.Size = New Size(750, 228)
        XtraScrollableControl9.TabIndex = 0
        ' 
        ' Infusion_Hydrations05_lbl
        ' 
        Infusion_Hydrations05_lbl.Location = New Point(12, 101)
        Infusion_Hydrations05_lbl.Margin = New Padding(0)
        Infusion_Hydrations05_lbl.Name = "Infusion_Hydrations05_lbl"
        Infusion_Hydrations05_lbl.Size = New Size(12, 14)
        Infusion_Hydrations05_lbl.TabIndex = 409
        Infusion_Hydrations05_lbl.Text = "5)"
        ' 
        ' Infusion_Hydrations04_lbl
        ' 
        Infusion_Hydrations04_lbl.Location = New Point(12, 81)
        Infusion_Hydrations04_lbl.Margin = New Padding(0)
        Infusion_Hydrations04_lbl.Name = "Infusion_Hydrations04_lbl"
        Infusion_Hydrations04_lbl.Size = New Size(12, 14)
        Infusion_Hydrations04_lbl.TabIndex = 408
        Infusion_Hydrations04_lbl.Text = "4)"
        ' 
        ' Infusion_Hydrations03_lbl
        ' 
        Infusion_Hydrations03_lbl.Location = New Point(12, 61)
        Infusion_Hydrations03_lbl.Margin = New Padding(0)
        Infusion_Hydrations03_lbl.Name = "Infusion_Hydrations03_lbl"
        Infusion_Hydrations03_lbl.Size = New Size(12, 14)
        Infusion_Hydrations03_lbl.TabIndex = 407
        Infusion_Hydrations03_lbl.Text = "3)"
        ' 
        ' Infusion_Hydrations02_lbl
        ' 
        Infusion_Hydrations02_lbl.Location = New Point(12, 41)
        Infusion_Hydrations02_lbl.Margin = New Padding(0)
        Infusion_Hydrations02_lbl.Name = "Infusion_Hydrations02_lbl"
        Infusion_Hydrations02_lbl.Size = New Size(12, 14)
        Infusion_Hydrations02_lbl.TabIndex = 406
        Infusion_Hydrations02_lbl.Text = "2)"
        ' 
        ' Infusion_Hydrations01_lbl
        ' 
        Infusion_Hydrations01_lbl.Location = New Point(12, 22)
        Infusion_Hydrations01_lbl.Margin = New Padding(0)
        Infusion_Hydrations01_lbl.Name = "Infusion_Hydrations01_lbl"
        Infusion_Hydrations01_lbl.Size = New Size(12, 14)
        Infusion_Hydrations01_lbl.TabIndex = 405
        Infusion_Hydrations01_lbl.Text = "1)"
        ' 
        ' Infusion_HydrationsMins05_lbl
        ' 
        Infusion_HydrationsMins05_lbl.Location = New Point(376, 102)
        Infusion_HydrationsMins05_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins05_lbl.Name = "Infusion_HydrationsMins05_lbl"
        Infusion_HydrationsMins05_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins05_lbl.TabIndex = 395
        Infusion_HydrationsMins05_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins04_lbl
        ' 
        Infusion_HydrationsMins04_lbl.Location = New Point(376, 82)
        Infusion_HydrationsMins04_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins04_lbl.Name = "Infusion_HydrationsMins04_lbl"
        Infusion_HydrationsMins04_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins04_lbl.TabIndex = 388
        Infusion_HydrationsMins04_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins03_lbl
        ' 
        Infusion_HydrationsMins03_lbl.Location = New Point(376, 62)
        Infusion_HydrationsMins03_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins03_lbl.Name = "Infusion_HydrationsMins03_lbl"
        Infusion_HydrationsMins03_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins03_lbl.TabIndex = 381
        Infusion_HydrationsMins03_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins02_lbl
        ' 
        Infusion_HydrationsMins02_lbl.Location = New Point(376, 42)
        Infusion_HydrationsMins02_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins02_lbl.Name = "Infusion_HydrationsMins02_lbl"
        Infusion_HydrationsMins02_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins02_lbl.TabIndex = 374
        Infusion_HydrationsMins02_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins01_lbl
        ' 
        Infusion_HydrationsMins01_lbl.Location = New Point(376, 22)
        Infusion_HydrationsMins01_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins01_lbl.Name = "Infusion_HydrationsMins01_lbl"
        Infusion_HydrationsMins01_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins01_lbl.TabIndex = 367
        Infusion_HydrationsMins01_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndTime04_txt
        ' 
        Infusion_HydrationsEndTime04_txt.Location = New Point(299, 79)
        Infusion_HydrationsEndTime04_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime04_txt.Name = "Infusion_HydrationsEndTime04_txt"
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime04_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime04_txt.TabIndex = 387
        ' 
        ' Infusion_HydrationsEndDate04_dte
        ' 
        Infusion_HydrationsEndDate04_dte.EditValue = Nothing
        Infusion_HydrationsEndDate04_dte.Location = New Point(198, 79)
        Infusion_HydrationsEndDate04_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate04_dte.Name = "Infusion_HydrationsEndDate04_dte"
        Infusion_HydrationsEndDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate04_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate04_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate04_dte.TabIndex = 386
        ' 
        ' Infusion_HydrationsEndTime03_txt
        ' 
        Infusion_HydrationsEndTime03_txt.Location = New Point(299, 59)
        Infusion_HydrationsEndTime03_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime03_txt.Name = "Infusion_HydrationsEndTime03_txt"
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime03_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime03_txt.TabIndex = 380
        ' 
        ' Infusion_HydrationsEndDate03_dte
        ' 
        Infusion_HydrationsEndDate03_dte.EditValue = Nothing
        Infusion_HydrationsEndDate03_dte.Location = New Point(198, 59)
        Infusion_HydrationsEndDate03_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate03_dte.Name = "Infusion_HydrationsEndDate03_dte"
        Infusion_HydrationsEndDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate03_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate03_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate03_dte.TabIndex = 379
        ' 
        ' Infusion_HydrationsEndTime05_txt
        ' 
        Infusion_HydrationsEndTime05_txt.Location = New Point(299, 99)
        Infusion_HydrationsEndTime05_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime05_txt.Name = "Infusion_HydrationsEndTime05_txt"
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime05_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime05_txt.TabIndex = 394
        ' 
        ' Infusion_HydrationsEndTime02_txt
        ' 
        Infusion_HydrationsEndTime02_txt.Location = New Point(299, 39)
        Infusion_HydrationsEndTime02_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime02_txt.Name = "Infusion_HydrationsEndTime02_txt"
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime02_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime02_txt.TabIndex = 373
        ' 
        ' Infusion_HydrationsEndTime01_txt
        ' 
        Infusion_HydrationsEndTime01_txt.Location = New Point(299, 19)
        Infusion_HydrationsEndTime01_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime01_txt.Name = "Infusion_HydrationsEndTime01_txt"
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime01_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime01_txt.TabIndex = 366
        ' 
        ' Infusion_HydrationsEndDate05_dte
        ' 
        Infusion_HydrationsEndDate05_dte.EditValue = Nothing
        Infusion_HydrationsEndDate05_dte.Location = New Point(198, 99)
        Infusion_HydrationsEndDate05_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate05_dte.Name = "Infusion_HydrationsEndDate05_dte"
        Infusion_HydrationsEndDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate05_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate05_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate05_dte.TabIndex = 393
        ' 
        ' Infusion_HydrationsEndDate02_dte
        ' 
        Infusion_HydrationsEndDate02_dte.EditValue = Nothing
        Infusion_HydrationsEndDate02_dte.Location = New Point(198, 39)
        Infusion_HydrationsEndDate02_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate02_dte.Name = "Infusion_HydrationsEndDate02_dte"
        Infusion_HydrationsEndDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate02_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate02_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate02_dte.TabIndex = 372
        ' 
        ' Infusion_HydrationsEndDate01_dte
        ' 
        Infusion_HydrationsEndDate01_dte.EditValue = Nothing
        Infusion_HydrationsEndDate01_dte.Location = New Point(198, 19)
        Infusion_HydrationsEndDate01_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate01_dte.Name = "Infusion_HydrationsEndDate01_dte"
        Infusion_HydrationsEndDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate01_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsEndDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsEndDate01_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate01_dte.TabIndex = 365
        Infusion_HydrationsEndDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsStartTime04_txt
        ' 
        Infusion_HydrationsStartTime04_txt.Location = New Point(138, 79)
        Infusion_HydrationsStartTime04_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime04_txt.Name = "Infusion_HydrationsStartTime04_txt"
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime04_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime04_txt.TabIndex = 385
        ' 
        ' Infusion_HydrationsStartDate04_dte
        ' 
        Infusion_HydrationsStartDate04_dte.EditValue = Nothing
        Infusion_HydrationsStartDate04_dte.Location = New Point(37, 79)
        Infusion_HydrationsStartDate04_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate04_dte.Name = "Infusion_HydrationsStartDate04_dte"
        Infusion_HydrationsStartDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate04_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate04_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate04_dte.TabIndex = 384
        ' 
        ' Infusion_HydrationsStartTime03_txt
        ' 
        Infusion_HydrationsStartTime03_txt.Location = New Point(138, 59)
        Infusion_HydrationsStartTime03_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime03_txt.Name = "Infusion_HydrationsStartTime03_txt"
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime03_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime03_txt.TabIndex = 378
        ' 
        ' Infusion_HydrationsStartDate03_dte
        ' 
        Infusion_HydrationsStartDate03_dte.EditValue = Nothing
        Infusion_HydrationsStartDate03_dte.Location = New Point(37, 59)
        Infusion_HydrationsStartDate03_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate03_dte.Name = "Infusion_HydrationsStartDate03_dte"
        Infusion_HydrationsStartDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate03_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate03_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate03_dte.TabIndex = 377
        ' 
        ' Infusion_HydrationsStartTime05_txt
        ' 
        Infusion_HydrationsStartTime05_txt.Location = New Point(138, 99)
        Infusion_HydrationsStartTime05_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime05_txt.Name = "Infusion_HydrationsStartTime05_txt"
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime05_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime05_txt.TabIndex = 392
        ' 
        ' Infusion_HydrationsStartTime02_txt
        ' 
        Infusion_HydrationsStartTime02_txt.Location = New Point(138, 39)
        Infusion_HydrationsStartTime02_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime02_txt.Name = "Infusion_HydrationsStartTime02_txt"
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime02_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime02_txt.TabIndex = 371
        ' 
        ' Infusion_HydrationsStartDate05_dte
        ' 
        Infusion_HydrationsStartDate05_dte.EditValue = Nothing
        Infusion_HydrationsStartDate05_dte.Location = New Point(37, 99)
        Infusion_HydrationsStartDate05_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate05_dte.Name = "Infusion_HydrationsStartDate05_dte"
        Infusion_HydrationsStartDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate05_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate05_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate05_dte.TabIndex = 391
        ' 
        ' Infusion_HydrationsStartDate02_dte
        ' 
        Infusion_HydrationsStartDate02_dte.EditValue = Nothing
        Infusion_HydrationsStartDate02_dte.Location = New Point(37, 39)
        Infusion_HydrationsStartDate02_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate02_dte.Name = "Infusion_HydrationsStartDate02_dte"
        Infusion_HydrationsStartDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate02_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate02_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate02_dte.TabIndex = 370
        ' 
        ' Infusion_HydrationsStartDate01_dte
        ' 
        Infusion_HydrationsStartDate01_dte.EditValue = Nothing
        Infusion_HydrationsStartDate01_dte.Location = New Point(37, 19)
        Infusion_HydrationsStartDate01_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate01_dte.Name = "Infusion_HydrationsStartDate01_dte"
        Infusion_HydrationsStartDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate01_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsStartDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsStartDate01_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate01_dte.TabIndex = 363
        Infusion_HydrationsStartDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsRate05_cbo
        ' 
        Infusion_HydrationsRate05_cbo.Location = New Point(486, 99)
        Infusion_HydrationsRate05_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate05_cbo.Name = "Infusion_HydrationsRate05_cbo"
        Infusion_HydrationsRate05_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate05_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate05_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate05_cbo.TabIndex = 397
        ' 
        ' Infusion_HydrationsRate04_cbo
        ' 
        Infusion_HydrationsRate04_cbo.Location = New Point(486, 79)
        Infusion_HydrationsRate04_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate04_cbo.Name = "Infusion_HydrationsRate04_cbo"
        Infusion_HydrationsRate04_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate04_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate04_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate04_cbo.TabIndex = 390
        ' 
        ' Infusion_HydrationsRate03_cbo
        ' 
        Infusion_HydrationsRate03_cbo.Location = New Point(486, 59)
        Infusion_HydrationsRate03_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate03_cbo.Name = "Infusion_HydrationsRate03_cbo"
        Infusion_HydrationsRate03_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate03_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate03_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate03_cbo.TabIndex = 383
        ' 
        ' Infusion_HydrationsRate02_cbo
        ' 
        Infusion_HydrationsRate02_cbo.Location = New Point(486, 39)
        Infusion_HydrationsRate02_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate02_cbo.Name = "Infusion_HydrationsRate02_cbo"
        Infusion_HydrationsRate02_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate02_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate02_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate02_cbo.TabIndex = 376
        ' 
        ' Infusion_HydrationsRate01_cbo
        ' 
        Infusion_HydrationsRate01_cbo.Location = New Point(486, 19)
        Infusion_HydrationsRate01_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate01_cbo.Name = "Infusion_HydrationsRate01_cbo"
        Infusion_HydrationsRate01_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate01_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate01_cbo.TabIndex = 369
        ' 
        ' Infusion_HydrationsSite05_cbo
        ' 
        Infusion_HydrationsSite05_cbo.Location = New Point(415, 99)
        Infusion_HydrationsSite05_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite05_cbo.Name = "Infusion_HydrationsSite05_cbo"
        Infusion_HydrationsSite05_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite05_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite05_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite05_cbo.TabIndex = 396
        ' 
        ' Infusion_HydrationsSite04_cbo
        ' 
        Infusion_HydrationsSite04_cbo.Location = New Point(415, 79)
        Infusion_HydrationsSite04_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite04_cbo.Name = "Infusion_HydrationsSite04_cbo"
        Infusion_HydrationsSite04_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite04_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite04_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite04_cbo.TabIndex = 389
        ' 
        ' Infusion_HydrationsSite03_cbo
        ' 
        Infusion_HydrationsSite03_cbo.Location = New Point(415, 59)
        Infusion_HydrationsSite03_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite03_cbo.Name = "Infusion_HydrationsSite03_cbo"
        Infusion_HydrationsSite03_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite03_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite03_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite03_cbo.TabIndex = 382
        ' 
        ' Infusion_HydrationsSite02_cbo
        ' 
        Infusion_HydrationsSite02_cbo.Location = New Point(415, 39)
        Infusion_HydrationsSite02_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite02_cbo.Name = "Infusion_HydrationsSite02_cbo"
        Infusion_HydrationsSite02_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite02_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite02_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite02_cbo.TabIndex = 375
        ' 
        ' Infusion_HydrationsSite01_cbo
        ' 
        Infusion_HydrationsSite01_cbo.Location = New Point(415, 19)
        Infusion_HydrationsSite01_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite01_cbo.Name = "Infusion_HydrationsSite01_cbo"
        Infusion_HydrationsSite01_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite01_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite01_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite01_cbo.TabIndex = 368
        ' 
        ' Infusion_Hydrations12_lbl
        ' 
        Infusion_Hydrations12_lbl.Location = New Point(12, 239)
        Infusion_Hydrations12_lbl.Margin = New Padding(0)
        Infusion_Hydrations12_lbl.Name = "Infusion_Hydrations12_lbl"
        Infusion_Hydrations12_lbl.Size = New Size(19, 14)
        Infusion_Hydrations12_lbl.TabIndex = 362
        Infusion_Hydrations12_lbl.Text = "12)"
        ' 
        ' Infusion_Hydrations11_lbl
        ' 
        Infusion_Hydrations11_lbl.Location = New Point(12, 219)
        Infusion_Hydrations11_lbl.Margin = New Padding(0)
        Infusion_Hydrations11_lbl.Name = "Infusion_Hydrations11_lbl"
        Infusion_Hydrations11_lbl.Size = New Size(19, 14)
        Infusion_Hydrations11_lbl.TabIndex = 361
        Infusion_Hydrations11_lbl.Text = "11)"
        ' 
        ' Infusion_HydrationsStartTime11_txt
        ' 
        Infusion_HydrationsStartTime11_txt.Location = New Point(138, 219)
        Infusion_HydrationsStartTime11_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime11_txt.Name = "Infusion_HydrationsStartTime11_txt"
        Infusion_HydrationsStartTime11_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime11_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime11_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime11_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime11_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime11_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime11_txt.TabIndex = 348
        ' 
        ' Infusion_HydrationsStartDate11_dte
        ' 
        Infusion_HydrationsStartDate11_dte.EditValue = Nothing
        Infusion_HydrationsStartDate11_dte.Location = New Point(37, 219)
        Infusion_HydrationsStartDate11_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate11_dte.Name = "Infusion_HydrationsStartDate11_dte"
        Infusion_HydrationsStartDate11_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate11_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate11_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate11_dte.TabIndex = 347
        ' 
        ' Infusion_HydrationsEndDate12_dte
        ' 
        Infusion_HydrationsEndDate12_dte.EditValue = Nothing
        Infusion_HydrationsEndDate12_dte.Location = New Point(198, 239)
        Infusion_HydrationsEndDate12_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate12_dte.Name = "Infusion_HydrationsEndDate12_dte"
        Infusion_HydrationsEndDate12_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate12_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate12_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate12_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate12_dte.TabIndex = 356
        ' 
        ' Infusion_HydrationsStartTime12_txt
        ' 
        Infusion_HydrationsStartTime12_txt.Location = New Point(138, 239)
        Infusion_HydrationsStartTime12_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime12_txt.Name = "Infusion_HydrationsStartTime12_txt"
        Infusion_HydrationsStartTime12_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime12_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime12_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime12_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime12_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime12_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime12_txt.TabIndex = 355
        ' 
        ' Infusion_HydrationsEndTime12_txt
        ' 
        Infusion_HydrationsEndTime12_txt.Location = New Point(299, 239)
        Infusion_HydrationsEndTime12_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime12_txt.Name = "Infusion_HydrationsEndTime12_txt"
        Infusion_HydrationsEndTime12_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime12_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime12_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime12_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime12_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime12_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime12_txt.TabIndex = 357
        ' 
        ' Infusion_HydrationsMins12_lbl
        ' 
        Infusion_HydrationsMins12_lbl.Location = New Point(376, 242)
        Infusion_HydrationsMins12_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins12_lbl.Name = "Infusion_HydrationsMins12_lbl"
        Infusion_HydrationsMins12_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins12_lbl.TabIndex = 358
        Infusion_HydrationsMins12_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsSite11_cbo
        ' 
        Infusion_HydrationsSite11_cbo.Location = New Point(415, 219)
        Infusion_HydrationsSite11_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite11_cbo.Name = "Infusion_HydrationsSite11_cbo"
        Infusion_HydrationsSite11_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite11_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite11_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite11_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite11_cbo.TabIndex = 352
        ' 
        ' Infusion_HydrationsMins11_lbl
        ' 
        Infusion_HydrationsMins11_lbl.Location = New Point(376, 222)
        Infusion_HydrationsMins11_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins11_lbl.Name = "Infusion_HydrationsMins11_lbl"
        Infusion_HydrationsMins11_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins11_lbl.TabIndex = 351
        Infusion_HydrationsMins11_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsSite12_cbo
        ' 
        Infusion_HydrationsSite12_cbo.Location = New Point(415, 239)
        Infusion_HydrationsSite12_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite12_cbo.Name = "Infusion_HydrationsSite12_cbo"
        Infusion_HydrationsSite12_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite12_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite12_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite12_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite12_cbo.TabIndex = 359
        ' 
        ' Infusion_HydrationsStartDate12_dte
        ' 
        Infusion_HydrationsStartDate12_dte.EditValue = Nothing
        Infusion_HydrationsStartDate12_dte.Location = New Point(37, 239)
        Infusion_HydrationsStartDate12_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate12_dte.Name = "Infusion_HydrationsStartDate12_dte"
        Infusion_HydrationsStartDate12_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate12_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate12_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate12_dte.TabIndex = 354
        ' 
        ' Infusion_HydrationsEndDate11_dte
        ' 
        Infusion_HydrationsEndDate11_dte.EditValue = Nothing
        Infusion_HydrationsEndDate11_dte.Location = New Point(198, 219)
        Infusion_HydrationsEndDate11_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate11_dte.Name = "Infusion_HydrationsEndDate11_dte"
        Infusion_HydrationsEndDate11_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate11_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate11_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate11_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate11_dte.TabIndex = 349
        ' 
        ' Infusion_HydrationsEndTime11_txt
        ' 
        Infusion_HydrationsEndTime11_txt.Location = New Point(299, 219)
        Infusion_HydrationsEndTime11_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime11_txt.Name = "Infusion_HydrationsEndTime11_txt"
        Infusion_HydrationsEndTime11_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime11_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime11_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime11_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime11_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime11_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime11_txt.TabIndex = 350
        ' 
        ' Infusion_HydrationsRate12_cbo
        ' 
        Infusion_HydrationsRate12_cbo.Location = New Point(486, 239)
        Infusion_HydrationsRate12_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate12_cbo.Name = "Infusion_HydrationsRate12_cbo"
        Infusion_HydrationsRate12_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate12_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate12_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate12_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate12_cbo.TabIndex = 360
        ' 
        ' Infusion_HydrationsRate11_cbo
        ' 
        Infusion_HydrationsRate11_cbo.Location = New Point(486, 219)
        Infusion_HydrationsRate11_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate11_cbo.Name = "Infusion_HydrationsRate11_cbo"
        Infusion_HydrationsRate11_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate11_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate11_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate11_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate11_cbo.TabIndex = 353
        ' 
        ' Infusion_Hydrations10_lbl
        ' 
        Infusion_Hydrations10_lbl.Location = New Point(12, 198)
        Infusion_Hydrations10_lbl.Margin = New Padding(0)
        Infusion_Hydrations10_lbl.Name = "Infusion_Hydrations10_lbl"
        Infusion_Hydrations10_lbl.Size = New Size(19, 14)
        Infusion_Hydrations10_lbl.TabIndex = 346
        Infusion_Hydrations10_lbl.Text = "10)"
        ' 
        ' Infusion_Hydrations09_lbl
        ' 
        Infusion_Hydrations09_lbl.Location = New Point(12, 179)
        Infusion_Hydrations09_lbl.Margin = New Padding(0)
        Infusion_Hydrations09_lbl.Name = "Infusion_Hydrations09_lbl"
        Infusion_Hydrations09_lbl.Size = New Size(12, 14)
        Infusion_Hydrations09_lbl.TabIndex = 345
        Infusion_Hydrations09_lbl.Text = "9)"
        ' 
        ' Infusion_HydrationsEndDate06_dte
        ' 
        Infusion_HydrationsEndDate06_dte.EditValue = Nothing
        Infusion_HydrationsEndDate06_dte.Location = New Point(198, 118)
        Infusion_HydrationsEndDate06_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate06_dte.Name = "Infusion_HydrationsEndDate06_dte"
        Infusion_HydrationsEndDate06_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate06_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate06_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate06_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsEndDate06_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsEndDate06_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate06_dte.TabIndex = 309
        Infusion_HydrationsEndDate06_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsStartTime09_txt
        ' 
        Infusion_HydrationsStartTime09_txt.Location = New Point(138, 179)
        Infusion_HydrationsStartTime09_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime09_txt.Name = "Infusion_HydrationsStartTime09_txt"
        Infusion_HydrationsStartTime09_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime09_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime09_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime09_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime09_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime09_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime09_txt.TabIndex = 329
        ' 
        ' Infusion_Hydrations08_lbl
        ' 
        Infusion_Hydrations08_lbl.Location = New Point(12, 159)
        Infusion_Hydrations08_lbl.Margin = New Padding(0)
        Infusion_Hydrations08_lbl.Name = "Infusion_Hydrations08_lbl"
        Infusion_Hydrations08_lbl.Size = New Size(12, 14)
        Infusion_Hydrations08_lbl.TabIndex = 344
        Infusion_Hydrations08_lbl.Text = "8)"
        ' 
        ' Infusion_HydrationsEndDate07_dte
        ' 
        Infusion_HydrationsEndDate07_dte.EditValue = Nothing
        Infusion_HydrationsEndDate07_dte.Location = New Point(198, 139)
        Infusion_HydrationsEndDate07_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate07_dte.Name = "Infusion_HydrationsEndDate07_dte"
        Infusion_HydrationsEndDate07_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate07_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate07_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate07_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate07_dte.TabIndex = 316
        ' 
        ' Infusion_HydrationsStartDate09_dte
        ' 
        Infusion_HydrationsStartDate09_dte.EditValue = Nothing
        Infusion_HydrationsStartDate09_dte.Location = New Point(37, 179)
        Infusion_HydrationsStartDate09_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate09_dte.Name = "Infusion_HydrationsStartDate09_dte"
        Infusion_HydrationsStartDate09_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate09_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate09_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate09_dte.TabIndex = 328
        ' 
        ' Infusion_Hydrations07_lbl
        ' 
        Infusion_Hydrations07_lbl.Location = New Point(12, 140)
        Infusion_Hydrations07_lbl.Margin = New Padding(0)
        Infusion_Hydrations07_lbl.Name = "Infusion_Hydrations07_lbl"
        Infusion_Hydrations07_lbl.Size = New Size(12, 14)
        Infusion_Hydrations07_lbl.TabIndex = 343
        Infusion_Hydrations07_lbl.Text = "7)"
        ' 
        ' Infusion_HydrationsEndDate10_dte
        ' 
        Infusion_HydrationsEndDate10_dte.EditValue = Nothing
        Infusion_HydrationsEndDate10_dte.Location = New Point(198, 199)
        Infusion_HydrationsEndDate10_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate10_dte.Name = "Infusion_HydrationsEndDate10_dte"
        Infusion_HydrationsEndDate10_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate10_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate10_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate10_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate10_dte.TabIndex = 337
        ' 
        ' Infusion_HydrationsStartTime08_txt
        ' 
        Infusion_HydrationsStartTime08_txt.Location = New Point(138, 159)
        Infusion_HydrationsStartTime08_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime08_txt.Name = "Infusion_HydrationsStartTime08_txt"
        Infusion_HydrationsStartTime08_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime08_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime08_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime08_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime08_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime08_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime08_txt.TabIndex = 322
        ' 
        ' Infusion_Hydrations06_lbl
        ' 
        Infusion_Hydrations06_lbl.Location = New Point(12, 120)
        Infusion_Hydrations06_lbl.Margin = New Padding(0)
        Infusion_Hydrations06_lbl.Name = "Infusion_Hydrations06_lbl"
        Infusion_Hydrations06_lbl.Size = New Size(12, 14)
        Infusion_Hydrations06_lbl.TabIndex = 342
        Infusion_Hydrations06_lbl.Text = "6)"
        ' 
        ' Infusion_HydrationsEndTime06_txt
        ' 
        Infusion_HydrationsEndTime06_txt.Location = New Point(299, 118)
        Infusion_HydrationsEndTime06_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime06_txt.Name = "Infusion_HydrationsEndTime06_txt"
        Infusion_HydrationsEndTime06_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime06_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime06_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime06_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime06_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime06_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime06_txt.TabIndex = 310
        ' 
        ' Infusion_HydrationsSite06_cbo
        ' 
        Infusion_HydrationsSite06_cbo.Location = New Point(415, 118)
        Infusion_HydrationsSite06_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite06_cbo.Name = "Infusion_HydrationsSite06_cbo"
        Infusion_HydrationsSite06_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite06_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite06_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite06_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite06_cbo.TabIndex = 312
        ' 
        ' Infusion_HydrationsStartDate08_dte
        ' 
        Infusion_HydrationsStartDate08_dte.EditValue = Nothing
        Infusion_HydrationsStartDate08_dte.Location = New Point(37, 159)
        Infusion_HydrationsStartDate08_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate08_dte.Name = "Infusion_HydrationsStartDate08_dte"
        Infusion_HydrationsStartDate08_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate08_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate08_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate08_dte.TabIndex = 321
        ' 
        ' Infusion_HydrationsEndTime07_txt
        ' 
        Infusion_HydrationsEndTime07_txt.Location = New Point(299, 139)
        Infusion_HydrationsEndTime07_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime07_txt.Name = "Infusion_HydrationsEndTime07_txt"
        Infusion_HydrationsEndTime07_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime07_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime07_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime07_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime07_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime07_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime07_txt.TabIndex = 317
        ' 
        ' Infusion_HydrationsSite07_cbo
        ' 
        Infusion_HydrationsSite07_cbo.Location = New Point(415, 139)
        Infusion_HydrationsSite07_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite07_cbo.Name = "Infusion_HydrationsSite07_cbo"
        Infusion_HydrationsSite07_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite07_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite07_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite07_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite07_cbo.TabIndex = 319
        ' 
        ' Infusion_HydrationsStartTime010_txt
        ' 
        Infusion_HydrationsStartTime010_txt.Location = New Point(138, 199)
        Infusion_HydrationsStartTime010_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime010_txt.Name = "Infusion_HydrationsStartTime010_txt"
        Infusion_HydrationsStartTime010_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime010_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime010_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime010_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime010_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime010_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime010_txt.TabIndex = 336
        ' 
        ' Infusion_HydrationsEndTime10_txt
        ' 
        Infusion_HydrationsEndTime10_txt.Location = New Point(299, 199)
        Infusion_HydrationsEndTime10_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime10_txt.Name = "Infusion_HydrationsEndTime10_txt"
        Infusion_HydrationsEndTime10_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime10_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime10_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime10_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime10_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime10_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime10_txt.TabIndex = 338
        ' 
        ' Infusion_HydrationsSite08_cbo
        ' 
        Infusion_HydrationsSite08_cbo.Location = New Point(415, 159)
        Infusion_HydrationsSite08_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite08_cbo.Name = "Infusion_HydrationsSite08_cbo"
        Infusion_HydrationsSite08_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite08_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite08_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite08_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite08_cbo.TabIndex = 326
        ' 
        ' Infusion_HydrationsStartTime07_txt
        ' 
        Infusion_HydrationsStartTime07_txt.Location = New Point(138, 139)
        Infusion_HydrationsStartTime07_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime07_txt.Name = "Infusion_HydrationsStartTime07_txt"
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime07_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime07_txt.TabIndex = 315
        ' 
        ' Infusion_HydrationsMins10_lbl
        ' 
        Infusion_HydrationsMins10_lbl.Location = New Point(376, 202)
        Infusion_HydrationsMins10_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins10_lbl.Name = "Infusion_HydrationsMins10_lbl"
        Infusion_HydrationsMins10_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins10_lbl.TabIndex = 339
        Infusion_HydrationsMins10_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndDate08_dte
        ' 
        Infusion_HydrationsEndDate08_dte.EditValue = Nothing
        Infusion_HydrationsEndDate08_dte.Location = New Point(198, 159)
        Infusion_HydrationsEndDate08_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate08_dte.Name = "Infusion_HydrationsEndDate08_dte"
        Infusion_HydrationsEndDate08_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate08_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate08_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate08_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate08_dte.TabIndex = 323
        ' 
        ' Infusion_HydrationsSite09_cbo
        ' 
        Infusion_HydrationsSite09_cbo.Location = New Point(415, 179)
        Infusion_HydrationsSite09_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite09_cbo.Name = "Infusion_HydrationsSite09_cbo"
        Infusion_HydrationsSite09_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite09_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite09_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite09_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite09_cbo.TabIndex = 333
        ' 
        ' Infusion_HydrationsStartTime06_txt
        ' 
        Infusion_HydrationsStartTime06_txt.Location = New Point(138, 118)
        Infusion_HydrationsStartTime06_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime06_txt.Name = "Infusion_HydrationsStartTime06_txt"
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime06_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime06_txt.TabIndex = 308
        ' 
        ' Infusion_HydrationsMins09_lbl
        ' 
        Infusion_HydrationsMins09_lbl.Location = New Point(376, 182)
        Infusion_HydrationsMins09_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins09_lbl.Name = "Infusion_HydrationsMins09_lbl"
        Infusion_HydrationsMins09_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins09_lbl.TabIndex = 332
        Infusion_HydrationsMins09_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndTime08_txt
        ' 
        Infusion_HydrationsEndTime08_txt.Location = New Point(299, 159)
        Infusion_HydrationsEndTime08_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime08_txt.Name = "Infusion_HydrationsEndTime08_txt"
        Infusion_HydrationsEndTime08_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime08_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime08_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime08_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime08_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime08_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime08_txt.TabIndex = 324
        ' 
        ' Infusion_HydrationsSite10_cbo
        ' 
        Infusion_HydrationsSite10_cbo.Location = New Point(415, 199)
        Infusion_HydrationsSite10_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite10_cbo.Name = "Infusion_HydrationsSite10_cbo"
        Infusion_HydrationsSite10_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite10_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite10_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite10_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite10_cbo.TabIndex = 340
        ' 
        ' Infusion_HydrationsStartDate10_dte
        ' 
        Infusion_HydrationsStartDate10_dte.EditValue = Nothing
        Infusion_HydrationsStartDate10_dte.Location = New Point(37, 199)
        Infusion_HydrationsStartDate10_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate10_dte.Name = "Infusion_HydrationsStartDate10_dte"
        Infusion_HydrationsStartDate10_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate10_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate10_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate10_dte.TabIndex = 335
        ' 
        ' Infusion_HydrationsMins08_lbl
        ' 
        Infusion_HydrationsMins08_lbl.Location = New Point(376, 162)
        Infusion_HydrationsMins08_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins08_lbl.Name = "Infusion_HydrationsMins08_lbl"
        Infusion_HydrationsMins08_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins08_lbl.TabIndex = 325
        Infusion_HydrationsMins08_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndDate09_dte
        ' 
        Infusion_HydrationsEndDate09_dte.EditValue = Nothing
        Infusion_HydrationsEndDate09_dte.Location = New Point(198, 179)
        Infusion_HydrationsEndDate09_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate09_dte.Name = "Infusion_HydrationsEndDate09_dte"
        Infusion_HydrationsEndDate09_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate09_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate09_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate09_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate09_dte.TabIndex = 330
        ' 
        ' Infusion_HydrationsRate06_cbo
        ' 
        Infusion_HydrationsRate06_cbo.Location = New Point(486, 118)
        Infusion_HydrationsRate06_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate06_cbo.Name = "Infusion_HydrationsRate06_cbo"
        Infusion_HydrationsRate06_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate06_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate06_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate06_cbo.TabIndex = 313
        ' 
        ' Infusion_HydrationsStartDate07_dte
        ' 
        Infusion_HydrationsStartDate07_dte.EditValue = Nothing
        Infusion_HydrationsStartDate07_dte.Location = New Point(37, 139)
        Infusion_HydrationsStartDate07_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate07_dte.Name = "Infusion_HydrationsStartDate07_dte"
        Infusion_HydrationsStartDate07_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate07_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate07_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate07_dte.TabIndex = 314
        ' 
        ' Infusion_HydrationsMins07_lbl
        ' 
        Infusion_HydrationsMins07_lbl.Location = New Point(376, 142)
        Infusion_HydrationsMins07_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins07_lbl.Name = "Infusion_HydrationsMins07_lbl"
        Infusion_HydrationsMins07_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins07_lbl.TabIndex = 318
        Infusion_HydrationsMins07_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndTime09_txt
        ' 
        Infusion_HydrationsEndTime09_txt.Location = New Point(299, 179)
        Infusion_HydrationsEndTime09_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime09_txt.Name = "Infusion_HydrationsEndTime09_txt"
        Infusion_HydrationsEndTime09_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime09_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime09_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime09_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime09_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime09_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime09_txt.TabIndex = 331
        ' 
        ' Infusion_HydrationsRate07_cbo
        ' 
        Infusion_HydrationsRate07_cbo.Location = New Point(486, 139)
        Infusion_HydrationsRate07_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate07_cbo.Name = "Infusion_HydrationsRate07_cbo"
        Infusion_HydrationsRate07_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate07_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate07_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate07_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate07_cbo.TabIndex = 320
        ' 
        ' Infusion_HydrationsStartDate06_dte
        ' 
        Infusion_HydrationsStartDate06_dte.EditValue = Nothing
        Infusion_HydrationsStartDate06_dte.Location = New Point(37, 118)
        Infusion_HydrationsStartDate06_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate06_dte.Name = "Infusion_HydrationsStartDate06_dte"
        Infusion_HydrationsStartDate06_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate06_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate06_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate06_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsStartDate06_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsStartDate06_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate06_dte.TabIndex = 307
        Infusion_HydrationsStartDate06_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsMins06_lbl
        ' 
        Infusion_HydrationsMins06_lbl.Location = New Point(376, 122)
        Infusion_HydrationsMins06_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins06_lbl.Name = "Infusion_HydrationsMins06_lbl"
        Infusion_HydrationsMins06_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins06_lbl.TabIndex = 311
        Infusion_HydrationsMins06_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsRate08_cbo
        ' 
        Infusion_HydrationsRate08_cbo.Location = New Point(486, 159)
        Infusion_HydrationsRate08_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate08_cbo.Name = "Infusion_HydrationsRate08_cbo"
        Infusion_HydrationsRate08_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate08_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate08_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate08_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate08_cbo.TabIndex = 327
        ' 
        ' Infusion_HydrationsRate10_cbo
        ' 
        Infusion_HydrationsRate10_cbo.Location = New Point(486, 199)
        Infusion_HydrationsRate10_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate10_cbo.Name = "Infusion_HydrationsRate10_cbo"
        Infusion_HydrationsRate10_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate10_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate10_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate10_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate10_cbo.TabIndex = 341
        ' 
        ' Infusion_HydrationsRate09_cbo
        ' 
        Infusion_HydrationsRate09_cbo.Location = New Point(486, 179)
        Infusion_HydrationsRate09_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate09_cbo.Name = "Infusion_HydrationsRate09_cbo"
        Infusion_HydrationsRate09_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate09_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate09_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate09_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate09_cbo.TabIndex = 334
        ' 
        ' LabelControl75
        ' 
        LabelControl75.Location = New Point(37, 3)
        LabelControl75.Margin = New Padding(2, 3, 2, 3)
        LabelControl75.Name = "LabelControl75"
        LabelControl75.Size = New Size(57, 14)
        LabelControl75.TabIndex = 279
        LabelControl75.Text = "Start Date"
        ' 
        ' LabelControl76
        ' 
        LabelControl76.Location = New Point(136, 3)
        LabelControl76.Margin = New Padding(2, 3, 2, 3)
        LabelControl76.Name = "LabelControl76"
        LabelControl76.Size = New Size(58, 14)
        LabelControl76.TabIndex = 280
        LabelControl76.Text = "Start Time"
        ' 
        ' LabelControl81
        ' 
        LabelControl81.Location = New Point(518, 2)
        LabelControl81.Margin = New Padding(2, 3, 2, 3)
        LabelControl81.Name = "LabelControl81"
        LabelControl81.Size = New Size(25, 14)
        LabelControl81.TabIndex = 301
        LabelControl81.Text = "Rate"
        ' 
        ' LabelControl82
        ' 
        LabelControl82.Location = New Point(438, 3)
        LabelControl82.Margin = New Padding(2, 3, 2, 3)
        LabelControl82.Name = "LabelControl82"
        LabelControl82.Size = New Size(21, 14)
        LabelControl82.TabIndex = 299
        LabelControl82.Text = "Site"
        ' 
        ' LabelControl92
        ' 
        LabelControl92.Location = New Point(218, 3)
        LabelControl92.Margin = New Padding(2, 3, 2, 3)
        LabelControl92.Name = "LabelControl92"
        LabelControl92.Size = New Size(51, 14)
        LabelControl92.TabIndex = 291
        LabelControl92.Text = "End Date"
        ' 
        ' LabelControl93
        ' 
        LabelControl93.Location = New Point(380, 3)
        LabelControl93.Margin = New Padding(2, 3, 2, 3)
        LabelControl93.Name = "LabelControl93"
        LabelControl93.Size = New Size(23, 14)
        LabelControl93.TabIndex = 293
        LabelControl93.Text = "Mins"
        ' 
        ' LabelControl94
        ' 
        LabelControl94.Location = New Point(299, 3)
        LabelControl94.Margin = New Padding(2, 3, 2, 3)
        LabelControl94.Name = "LabelControl94"
        LabelControl94.Size = New Size(52, 14)
        LabelControl94.TabIndex = 292
        LabelControl94.Text = "End Time"
        ' 
        ' Infusion_HydrationsStartTime01_txt
        ' 
        Infusion_HydrationsStartTime01_txt.EditValue = Nothing
        Infusion_HydrationsStartTime01_txt.Location = New Point(138, 19)
        Infusion_HydrationsStartTime01_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime01_txt.Name = "Infusion_HydrationsStartTime01_txt"
        Infusion_HydrationsStartTime01_txt.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartTime01_txt.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("mask", "")
        Infusion_HydrationsStartTime01_txt.Properties.TimeEditStyle = Repository.TimeEditStyle.TouchUI
        Infusion_HydrationsStartTime01_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime01_txt.TabIndex = 364
        ' 
        ' scratchform4
        ' 
        AutoScaleDimensions = New SizeF(7F, 14F)
        AutoScaleMode = AutoScaleMode.Font
        ClientSize = New Size(1351, 664)
        Controls.Add(GroupControl2)
        Name = "scratchform4"
        Text = "scratchform4"
        CType(GroupControl2, ComponentModel.ISupportInitialize).EndInit()
        GroupControl2.ResumeLayout(False)
        XtraScrollableControl9.ResumeLayout(False)
        XtraScrollableControl9.PerformLayout()
        CType(Infusion_HydrationsEndTime04_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime03_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime05_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime02_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime01_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime04_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime03_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime05_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime02_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate05_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate04_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate03_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate02_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate01_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite05_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite04_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite03_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite02_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite01_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime11_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate12_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime12_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime12_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite11_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite12_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate11_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime11_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate12_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate11_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate06_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate06_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime09_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate07_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate10_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime08_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime06_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite06_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime07_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite07_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime010_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime10_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite08_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime07_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate08_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite09_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime06_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime08_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite10_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate09_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate06_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime09_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate07_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate06_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate06_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate08_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate10_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate09_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime01_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
    End Sub

    Friend WithEvents GroupControl2 As GroupControl
    Friend WithEvents XtraScrollableControl9 As XtraScrollableControl
    Friend WithEvents Infusion_Hydrations05_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations04_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations03_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations02_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations01_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins05_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins04_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins03_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins02_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins01_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndTime04_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate04_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime03_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate03_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime05_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime02_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime01_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate05_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate02_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate01_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime04_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate04_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime03_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate03_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime05_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartTime02_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate05_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate02_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate01_dte As DateEdit
    Friend WithEvents Infusion_HydrationsRate05_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate04_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate03_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate02_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate01_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite05_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite04_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite03_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite02_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite01_cbo As ComboBoxEdit
    Friend WithEvents Infusion_Hydrations12_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations11_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsStartTime11_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate11_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate12_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime12_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime12_txt As TextEdit
    Friend WithEvents Infusion_HydrationsMins12_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsSite11_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsMins11_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsSite12_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartDate12_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate11_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime11_txt As TextEdit
    Friend WithEvents Infusion_HydrationsRate12_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate11_cbo As ComboBoxEdit
    Friend WithEvents Infusion_Hydrations10_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations09_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndDate06_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime09_txt As TextEdit
    Friend WithEvents Infusion_Hydrations08_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndDate07_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate09_dte As DateEdit
    Friend WithEvents Infusion_Hydrations07_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndDate10_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime08_txt As TextEdit
    Friend WithEvents Infusion_Hydrations06_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndTime06_txt As TextEdit
    Friend WithEvents Infusion_HydrationsSite06_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartDate08_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime07_txt As TextEdit
    Friend WithEvents Infusion_HydrationsSite07_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartTime010_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime10_txt As TextEdit
    Friend WithEvents Infusion_HydrationsSite08_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartTime07_txt As TextEdit
    Friend WithEvents Infusion_HydrationsMins10_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndDate08_dte As DateEdit
    Friend WithEvents Infusion_HydrationsSite09_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartTime06_txt As TextEdit
    Friend WithEvents Infusion_HydrationsMins09_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndTime08_txt As TextEdit
    Friend WithEvents Infusion_HydrationsSite10_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartDate10_dte As DateEdit
    Friend WithEvents Infusion_HydrationsMins08_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndDate09_dte As DateEdit
    Friend WithEvents Infusion_HydrationsRate06_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartDate07_dte As DateEdit
    Friend WithEvents Infusion_HydrationsMins07_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndTime09_txt As TextEdit
    Friend WithEvents Infusion_HydrationsRate07_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsStartDate06_dte As DateEdit
    Friend WithEvents Infusion_HydrationsMins06_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsRate08_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate10_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate09_cbo As ComboBoxEdit
    Friend WithEvents LabelControl75 As LabelControl
    Friend WithEvents LabelControl76 As LabelControl
    Friend WithEvents LabelControl81 As LabelControl
    Friend WithEvents LabelControl82 As LabelControl
    Friend WithEvents LabelControl92 As LabelControl
    Friend WithEvents LabelControl93 As LabelControl
    Friend WithEvents LabelControl94 As LabelControl
    Friend WithEvents Infusion_HydrationsStartTime01_txt As TimeEdit
End Class

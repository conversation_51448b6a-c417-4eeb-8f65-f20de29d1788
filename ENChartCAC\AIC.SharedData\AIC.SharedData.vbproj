﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>%24/VIC/Main_Next/SourceCode/ENChartCAC/AIC.SharedData</SccProjectName>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <SccAuxPath>https://tfs.cloud.mdrxdev.com/tfs/collection1-eis</SccAuxPath>
    <SccLocalPath>.</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectGuid>{B00D9EB7-8558-4E81-9755-23187838CBA5}</ProjectGuid>
    <!--<MyType>Windows</MyType>-->
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
 
  <!--<Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy /f /y &quot;$(TargetPath)&quot; &quot;..\..\..\sharedlibs\hic\$(TargetFileName)&quot;" />
  </Target>-->
  <ItemGroup>
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="7.0.3" />
    <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
  </ItemGroup>

  <Import Project="..\..\build\CustomPostBuild.targets" />
</Project>
﻿Option Infer On
#Region "Header"

'End Class

#End Region 'Header

Imports DevExpress.XtraEditors
Imports DevExpress.XtraTab

Public Class ECRuleTab
    Inherits ECRuleTabBase

#Region "Fields"

    '    Private _validating As Boolean

#End Region 'Fields

#Region "Constructors"

    Public Sub New(ByVal tPage As XtraTabPage)
        MyBase.New(tPage)
        'TabPage = tPage
        'Name = tPage.Name
    End Sub

#End Region 'Constructors

#Region "Properties"

    Public Overrides Property IsValid() As Boolean
        Get
            'Return MyBase.IsValid
            Return _isValid
        End Get
        Set(ByVal value As Boolean)
            'MyBase.IsValid = value
            _isValid = value
            UpdateTabHeaderStatus()
        End Set
    End Property

#End Region 'Properties

#Region "Methods"

    Public Overrides Sub AddChild(ByRef child As ECRule)
        child.ParentRule = Me
        _children.Add(child)
        If TypeOf child Is ECRuleTabBase Then
            AddEventHandlers(child, child)
        Else
            AddEventHandlers(child, Me)
        End If
    End Sub
    Protected Sub AddEventHandlers(ByRef ruleObj As ECRule, ByRef pParentTab As ECRuleTabBase)
        If TypeOf ruleObj Is ECRuleControlWrapper Then

            If TypeOf DirectCast(ruleObj, ECRuleControlWrapper).InnerControl Is BaseEdit Then
                AddHandler DirectCast(DirectCast(ruleObj, ECRuleControlWrapper).InnerControl, BaseEdit).LostFocus, AddressOf Me.LostFocus
                AddHandler DirectCast(DirectCast(ruleObj, ECRuleControlWrapper).InnerControl, BaseEdit).EditValueChanged, AddressOf Me.EditValueChanged
                ruleObj.ContainingTab = pParentTab
            ElseIf TypeOf DirectCast(ruleObj, ECRuleControlWrapper).InnerControl Is ICStatusChanged Then
                AddHandler DirectCast(DirectCast(ruleObj, ECRuleControlWrapper).InnerControl, ICStatusChanged).ErrorStatusChanged, AddressOf Me.OnErrorStatusChanged
                'pParentTab.AddStandAloneChild(DirectCast(DirectCast(ruleObj, ECRuleControlWrapper).InnerControl, ICStatusChanged))
                ruleObj.ContainingTab = pParentTab
            End If
        End If

        For Each child As ECRule In ruleObj.Children
            Try
                If TypeOf child Is ECRuleTabBase Then
                    AddEventHandlers(child, child)
                Else
                    AddEventHandlers(child, pParentTab)
                End If
            Catch ex As Exception
                'OLD TODO: if we catch this error.. we should do something with it....
                'IF nothing more than log it.
            End Try
        Next

        For Each child As ECRule In ruleObj.DependentChildren
            Try
                AddEventHandlers(child, pParentTab)

            Catch ex As Exception
                'OLD TODO: if we catch this error.. we should do something with it....
                'IF nothing more than log it.
            End Try
        Next
    End Sub

    Public Overrides Sub EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If SuspendValidation = True Then
            Exit Sub 'jjc 07.14.19
        End If

        Validate()
    End Sub

    Public Sub OnErrorStatusChanged(sender As Object, e As ErrorStatusChangedEventArgs)
        If SuspendValidation = True Then
            Exit Sub 'jjc 07.14.19
        End If
        Validate()
    End Sub

    Public Overrides Function HasValue() As Boolean
        Return True
    End Function

    Public Sub LostFocus(ByVal sender As Object, ByVal e As System.EventArgs)
        'Validate()
    End Sub

    Public Overrides Function Validate() As Boolean

        Validate = False 'To eliminate compile waring
        'If ECGlobals.IgnoreAllEvents = True Then Return False
        If SuspendValidation = True Then Return False

        If TabPage.PageEnabled = False OrElse TabPage.PageVisible = False Then
            IsValid = True
            Return IsValid
        End If

        Try
            _validatingCount += 1
            If _validating Then
                Return True
            Else
                _validating = True
            End If

            HasData = 0
            Validate = True
            For Each child As ECRule In _children
                If child.Validate() = False Then
                    Validate = False
                End If
            Next

            For Each child As ECRule In _dependentChildren
                If child.Validate() = False Then
                    Validate = False
                End If
            Next
        Catch ex As Exception '03.26.12
            ECLog.WriteEntry(String.Format("(ECRuleTab.Validate) Error : {0}", ex.Message), TraceEventType.Error)
            Debug.Assert(False)
        Finally

            _validating = False
            IsValid = Validate

        End Try
        Return Validate
    End Function
#End Region 'Methods
End Class

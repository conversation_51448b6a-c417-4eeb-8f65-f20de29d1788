Imports EnchartDOLib
Imports DevExpress.XtraEditors
Imports DevExpress.Xpo
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.Data.Filtering

Public Class ReportRolesEditor

    Private Property CurrentFacilityReportList As New List(Of Object)
    Private Property PendingChanges As Boolean = False
    Private Property AddList As New List(Of Object)
    Private Property RemoveList As New List(Of Object)

#Disable Warning BC40041 ' Type is not CLS-compliant
    Public Sub New(ByVal facilities As List(Of DOFacility))
#Enable Warning BC40041 ' Type is not CLS-compliant

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        LoadFacilitiesList(facilities)

        If Not Session.DefaultSession.InTransaction Then
            Session.DefaultSession.BeginTransaction()
        End If


    End Sub

#Region "Build/Load Lists"
    Private Sub LoadFacilitiesList(ByVal facilityList As List(Of DOFacility))
        cboFacility.Properties.Items.Clear()

        For Each facility As DOFacility In facilityList
            cboFacility.Properties.Items.Add(facility)
        Next

        cboFacility.SelectedIndex = 0
    End Sub

    ''' <summary>
    ''' This builds a master list of reports for that particular facility and should be rebuilt each time the facility changes.
    ''' </summary>
    ''' <param name="facility"></param>
    ''' <returns></returns>
    Private Function BuildReportList(ByVal facility As DOFacility) As List(Of Object)
        Dim returnList As New List(Of Object)
        For Each category As DOFacilityReportCategory In DOFacilityReportCategory.GetAllCategoriesByFacility(facility) ' DAO TODO - Should this be sorted?
            returnList.Add(category)
            For Each report As DOFacilityReport In category.FacilityReports
                returnList.Add(report)
            Next
        Next
        Return returnList
    End Function

    Private Sub LoadReportList()
        listAllReports.Items.Clear()
        Dim userRole As DOUserRolesList = cboRoles.SelectedItem
        For Each item As Object In CurrentFacilityReportList
            listAllReports.Items.Add(item)
        Next
    End Sub

    Private Sub LoadRoleList()
        Dim facility As DOFacility = cboFacility.SelectedItem

        cboRoles.Properties.Items.Clear()
        For Each role As DOUserRolesList In facility.UserRolesList
            cboRoles.Properties.Items.Add(role)
        Next
        cboRoles.SelectedIndex = 0
    End Sub

    Private Sub LoadRoleReports()
        listRoleReports.Items.Clear()
        Dim userRole As DOUserRolesList = cboRoles.SelectedItem
        For Each roleCategory As DOFacilityReportCategoryRole In DOFacilityReportCategoryRole.GetByUserRole(userRole)
            listRoleReports.Items.Add(roleCategory.Category)

            For Each roleReport As DOFacilityReportRole In DOFacilityReportRole.GetByUserRoleAndCategory(userRole, roleCategory.Category)
                listRoleReports.Items.Add(roleReport.Report)
            Next
        Next
    End Sub
#End Region

#Region "Change List Methods"
    ''' <summary>
    ''' Load the changes based on the current UserRole. If the change exists it will be added/removed from the listbox based upon if it is enabled or disabled.
    ''' </summary>
    Private Sub LoadChanges()
        For Each categoryChange As Object In AddList
            If TypeOf (categoryChange) Is DOFacilityReportCategoryRole Then
                Dim item As DOFacilityReportCategoryRole = categoryChange
                Dim reportRoleIndex As Integer = ListLocation(item.Category)
                If item.Enabled Then
                    If reportRoleIndex = -1 AndAlso CType(cboRoles.SelectedItem, DOUserRolesList).Oid = item.UserRole.Oid Then listRoleReports.Items.Add(item.Category)
                Else
                    If reportRoleIndex > -1 AndAlso CType(cboRoles.SelectedItem, DOUserRolesList).Oid = item.UserRole.Oid Then listRoleReports.Items.RemoveAt(reportRoleIndex)
                End If
            End If
        Next

        For Each reportChange As Object In AddList
            If TypeOf (reportChange) Is DOFacilityReportRole Then
                Dim changeReportRole As DOFacilityReportRole = CType(reportChange, DOFacilityReportRole)
                Dim reportRoleIndex As Integer = ListLocation(changeReportRole.Report)

                Dim my_category_location As Integer = ListLocation(changeReportRole.Report.ReportCategory)
                Dim next_category_location As Integer = GetNextCategoryLocation(changeReportRole.Report.ReportCategory)

                If changeReportRole.Enabled Then
                    If reportRoleIndex = -1 AndAlso CType(cboRoles.SelectedItem, DOUserRolesList).Oid = changeReportRole.UserRole.Oid Then
                        If next_category_location <> -1 Then
                            listRoleReports.Items.Insert(next_category_location, changeReportRole.Report)
                        Else
                            listRoleReports.Items.Add(changeReportRole.Report)
                        End If
                    End If
                Else
                    If reportRoleIndex > -1 AndAlso CType(cboRoles.SelectedItem, DOUserRolesList).Oid = changeReportRole.UserRole.Oid Then listRoleReports.Items.RemoveAt(reportRoleIndex)
                End If
            End If
        Next
    End Sub

    ''' <summary>
    ''' Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
    ''' </summary>
    ''' <param name="item"></param>
    Public Sub AddReportRoleToAddList(ByVal item As DOFacilityReportCategoryRole)
        For Each change As Object In AddList
            If TypeOf (change) Is DOFacilityReportCategoryRole Then
                If CType(item, DOFacilityReportCategoryRole).Category.Oid = CType(change, DOFacilityReportCategoryRole).Category.Oid _
                    AndAlso CType(item, DOFacilityReportCategoryRole).UserRole.Oid = CType(change, DOFacilityReportCategoryRole).UserRole.Oid Then

                    CType(change, DOFacilityReportCategoryRole).Enabled = CType(item, DOFacilityReportCategoryRole).Enabled
                    Return
                End If
            End If
        Next
        AddList.Add(item)
    End Sub

    ''' <summary>
    ''' Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
    ''' </summary>
    ''' <param name="item"></param>
    Public Sub AddReportRoleToAddList(ByVal item As DOFacilityReportRole)
        For Each change As Object In AddList
            If TypeOf (change) Is DOFacilityReportRole Then
                If CType(item, DOFacilityReportRole).Report.Oid = CType(change, DOFacilityReportRole).Report.Oid _
                    AndAlso CType(item, DOFacilityReportRole).UserRole.Oid = CType(change, DOFacilityReportRole).UserRole.Oid Then

                    CType(change, DOFacilityReportRole).Enabled = CType(item, DOFacilityReportRole).Enabled
                    Return
                End If
            End If
        Next
        AddList.Add(item)
    End Sub


    ''' <summary>
    ''' Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
    ''' </summary>
    ''' <param name="item"></param>
    Public Sub AddReportRoleToRemoveList(ByVal item As DOFacilityReportCategoryRole)
        For Each change As Object In RemoveList
            If TypeOf (change) Is DOFacilityReportCategoryRole Then
                If CType(item, DOFacilityReportCategoryRole).Category.Oid = CType(change, DOFacilityReportCategoryRole).Category.Oid _
                    AndAlso CType(item, DOFacilityReportCategoryRole).UserRole.Oid = CType(change, DOFacilityReportCategoryRole).UserRole.Oid Then

                    CType(change, DOFacilityReportCategoryRole).Enabled = CType(item, DOFacilityReportCategoryRole).Enabled
                    Return
                End If
            End If
        Next
        RemoveList.Add(item)
    End Sub

    ''' <summary>
    ''' Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
    ''' </summary>
    ''' <param name="item"></param>
    Public Sub AddReportRoleToRemoveList(ByVal item As DOFacilityReportRole)
        For Each change As Object In RemoveList
            If TypeOf (change) Is DOFacilityReportRole Then
                If CType(item, DOFacilityReportRole).Report.Oid = CType(change, DOFacilityReportRole).Report.Oid _
                    AndAlso CType(item, DOFacilityReportRole).UserRole.Oid = CType(change, DOFacilityReportRole).UserRole.Oid Then

                    CType(change, DOFacilityReportRole).Enabled = CType(item, DOFacilityReportRole).Enabled
                    Return
                End If
            End If
        Next
        RemoveList.Add(item)
    End Sub
#End Region

#Region "Report Role Editing"
    Public Function AddReportRole(ByVal role_name As String) As Boolean
        Dim new_role As New DOUserRolesList()
        'new_role.Facility = active_facility
        new_role.Role = role_name

        CType(cboFacility.SelectedItem, DOFacility).UserRolesList.Add(new_role)

        new_role.Save()

        LoadRoleList()

        For Each role As UserRole In cboRoles.Properties.Items
            If role.RoleName = role_name Then
                cboRoles.SelectedItem = role
            End If
        Next
    End Function
#End Region

#Region "Save Method"
    Private Sub SaveAll()
        For Each change As XPObject In AddList
            change.Save()
        Next
        AddList.Clear()

        For Each change As XPObject In RemoveList
            change.Delete()
        Next
        RemoveList.Clear()
    End Sub
#End Region

#Region "ListBox Manipulation"
    Private Function ListContains(ByVal itemToCheck As Object) As Boolean
        If TypeOf itemToCheck Is DOFacilityReportCategory Then
            Dim categoryToCheck As DOFacilityReportCategory = CType(itemToCheck, DOFacilityReportCategory)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReportCategory Then
                    Dim listCategory As DOFacilityReportCategory = CType(listItem, DOFacilityReportCategory)

                    If categoryToCheck.Oid = listCategory.Oid Then Return True
                End If
            Next
        Else
            Dim reportToCheck As DOFacilityReport = CType(itemToCheck, DOFacilityReport)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReport Then
                    Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                    If reportToCheck.Oid = listReport.Oid Then Return True
                End If
            Next
        End If

        Return False
    End Function

    Private Sub ListRemove(ByVal itemToRemove As Object)
        If TypeOf itemToRemove Is DOFacilityReportCategory Then
            Dim categoryToRemove As DOFacilityReportCategory = CType(itemToRemove, DOFacilityReportCategory)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReportCategory Then
                    Dim listCategory As DOFacilityReportCategory = CType(listItem, DOFacilityReportCategory)

                    If categoryToRemove.Oid = listCategory.Oid Then
                        listRoleReports.Items.Remove(listCategory)
                        Exit For
                    End If
                End If
            Next

        Else
            Dim reportToRemove As DOFacilityReport = CType(itemToRemove, DOFacilityReport)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReport Then
                    Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                    If listReport.Oid = reportToRemove.Oid Then
                        listRoleReports.Items.Remove(listReport)
                        Exit For
                    End If
                End If
            Next

        End If
    End Sub

    Private Function ListLocation(ByVal itemToLocate As Object) As Integer
        If TypeOf itemToLocate Is DOFacilityReportCategory Then
            Dim categoryToLocate As DOFacilityReportCategory = CType(itemToLocate, DOFacilityReportCategory)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReportCategory Then
                    Dim listCategory As DOFacilityReportCategory = CType(listItem, DOFacilityReportCategory)

                    If listCategory.Oid = categoryToLocate.Oid Then
                        Return listRoleReports.Items.IndexOf(listCategory)
                    End If
                End If
            Next
        Else
            Dim reportToLocate As DOFacilityReport = CType(itemToLocate, DOFacilityReport)

            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReport Then
                    Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                    If listReport.Oid = reportToLocate.Oid Then
                        Return listRoleReports.Items.IndexOf(listReport)
                    End If
                End If
            Next
        End If

        Return -1
    End Function

    Private Function GetNextCategoryLocation(ByVal categoryToLocate As Object) As Integer
        Dim categoryLocation As Integer = ListLocation(categoryToLocate)

        For Each listItem As Object In listRoleReports.Items
            If (listRoleReports.Items.IndexOf(listItem) > categoryLocation) And (TypeOf listItem Is DOFacilityReportCategory) Then
                Return listRoleReports.Items.IndexOf(listItem)
            End If
        Next
        Return -1
    End Function
#End Region

#Region "Add/Remove Button Handlers"
    Private Sub btnAddOne_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddOne.Click
        Dim facility As DOFacility = cboFacility.SelectedItem

        For Each listItem As Object In listAllReports.SelectedItems
            If TypeOf listItem Is DOFacilityReportCategory Then
                Dim listCategory As DOFacilityReportCategory = CType(listItem, DOFacilityReportCategory)
                Dim categoryIndex As Integer

                If ListContains(listItem) Then
                    categoryIndex = ListLocation(listItem)
                Else
                    categoryIndex = listRoleReports.Items.Add(listItem)
                End If

                For Each categoryReport As DOFacilityReport In listCategory.FacilityReports
                    If ListContains(categoryReport) Then
                        ListRemove(categoryReport)
                    End If

                    categoryReport.ReportCategory = listCategory
                    listRoleReports.Items.Insert(categoryIndex + 1, categoryReport)

                    categoryIndex += 1

                    Dim newRoleReport = New DOFacilityReportRole With {
                            .Report = categoryReport,
                            .UserRole = CType(cboRoles.SelectedItem, DOUserRolesList),
                            .Enabled = True
                        }
                    AddReportRoleToAddList(newRoleReport)
                Next

                Dim newRoleCategory = New DOFacilityReportCategoryRole With {
                        .Category = listCategory,
                        .UserRole = CType(cboRoles.SelectedItem, DOUserRolesList),
                        .Enabled = True
                    }
                AddReportRoleToAddList(newRoleCategory)

            Else
                Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                If Not ListContains(listReport) Then
                    If Not ListContains(listReport.ReportCategory) Then
                        listRoleReports.Items.Add(listReport.ReportCategory)
                        listRoleReports.Items.Add(listReport)

                        Dim newRoleCategory = New DOFacilityReportCategoryRole With {
                                .Category = listReport.ReportCategory,
                                .UserRole = CType(cboRoles.SelectedItem, DOUserRolesList),
                                .Enabled = True
                            }
                        AddReportRoleToAddList(newRoleCategory)
                    Else
                        Dim my_category_location As Integer = ListLocation(listReport.ReportCategory)
                        Dim next_category_location As Integer = GetNextCategoryLocation(listReport.ReportCategory)

                        If next_category_location <> -1 Then
                            listRoleReports.Items.Insert(next_category_location, listReport)
                        Else
                            listRoleReports.Items.Add(listReport)
                        End If
                    End If

                    Dim newRoleReport = New DOFacilityReportRole With {
                            .Report = listReport,
                            .UserRole = CType(cboRoles.SelectedItem, DOUserRolesList),
                            .Enabled = True
                        }
                    AddReportRoleToAddList(newRoleReport)
                End If
            End If
        Next

        LoadReportList()
        LoadChanges()
        PendingChanges = True
        btnSave.Enabled = True
    End Sub

    Private Sub btnRemoveOne_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRemoveOne.Click
        Dim categoriesRemovedFrom As New List(Of DOFacilityReportCategory)
        Dim itemsToRemove As New List(Of Object)

        For Each itemToRemove As Object In listRoleReports.SelectedItems
            itemsToRemove.Add(itemToRemove)
        Next

        For Each itemToRemove As Object In itemsToRemove
            If listRoleReports.Items.IndexOf(itemToRemove) <> -1 Then ' It may have been removed already
                If TypeOf itemToRemove Is DOFacilityReportCategory Then
                    Dim categoryToRemove As DOFacilityReportCategory = CType(itemToRemove, DOFacilityReportCategory)
                    Dim itemsToRemoveList = New List(Of Object)

                    For Each listItem As Object In listRoleReports.Items
                        If TypeOf listItem Is DOFacilityReport Then
                            Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                            If listReport.ReportCategory.Oid = itemToRemove.OID Then
                                itemsToRemoveList.Add(listItem)
                            End If
                        End If
                    Next

                    For Each reportToRemove As Object In itemsToRemoveList

                        Dim reportToRemoveAsReport As DOFacilityReport = CType(reportToRemove, DOFacilityReport)
                        Dim currentRole As DOUserRolesList = CType(cboRoles.SelectedItem, DOUserRolesList)
                        Dim removeRoleReports As New XPCollection(Of DOFacilityReportRole)(CriteriaOperator.Parse($"Report = {reportToRemoveAsReport.Oid} AND UserRole = {currentRole.Oid}"))

                        If removeRoleReports.Count > 0 Then
                            AddReportRoleToRemoveList(removeRoleReports(0))
                            ListRemove(reportToRemove)
                        End If

                    Next

                    categoriesRemovedFrom.Add(categoryToRemove)

                Else

                    Dim reportToRemove As DOFacilityReport = CType(itemToRemove, DOFacilityReport)
                    If Not categoriesRemovedFrom.Contains(reportToRemove.ReportCategory) Then
                        categoriesRemovedFrom.Add(reportToRemove.ReportCategory)
                    End If

                    Dim currentRole As DOUserRolesList = CType(cboRoles.SelectedItem, DOUserRolesList)
                    Dim removeRoleReports As New XPCollection(Of DOFacilityReportRole)(CriteriaOperator.Parse($"Report = {reportToRemove.Oid} AND UserRole = {currentRole.Oid}"))

                    If removeRoleReports.Count > 0 Then
                        AddReportRoleToRemoveList(removeRoleReports(0))
                    End If

                End If
                listRoleReports.Items.Remove(itemToRemove)
            End If
        Next

        For Each categoryToRemove As DOFacilityReportCategory In categoriesRemovedFrom
            Dim deleteCategory As Boolean = True
            For Each listItem As Object In listRoleReports.Items
                If TypeOf listItem Is DOFacilityReport Then
                    Dim listReport As DOFacilityReport = CType(listItem, DOFacilityReport)

                    If listReport.ReportCategory.Oid = categoryToRemove.Oid Then
                        deleteCategory = False
                        Exit For
                    End If
                End If
            Next

            If deleteCategory Then

                Dim currentRole As DOUserRolesList = CType(cboRoles.SelectedItem, DOUserRolesList)
                Dim removeRoleCategories As New XPCollection(Of DOFacilityReportCategoryRole)(CriteriaOperator.Parse($"Category = {categoryToRemove.Oid} AND UserRole = {currentRole.Oid}"))

                If removeRoleCategories.Count > 0 Then
                    AddReportRoleToRemoveList(removeRoleCategories(0))
                    ListRemove(categoryToRemove)
                End If

            End If
        Next

        LoadReportList()
        PendingChanges = True
        btnSave.Enabled = True
    End Sub
#End Region

#Region "Save/Cancel/New Role Button Handlers & Form Closing Handler"
    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        Me.Cursor = Cursors.WaitCursor
        SaveAll()
        If Session.DefaultSession.InTransaction Then
            Session.DefaultSession.CommitTransaction()
        End If
        PendingChanges = False
        btnSave.Enabled = False
        Me.Cursor = Cursors.Default
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnNewRole_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNewRole.Click
        Dim roleEditor As New NewReportRole()
        If roleEditor.ShowDialog() = DialogResult.OK Then
            Dim facility As DOFacility = CType(cboFacility.SelectedItem, DOFacility)
            Dim newRole As New DOUserRolesList With {
                .Facility = facility,
                .Role = roleEditor.GetRoleName()
            }
            newRole.Save()
            facility.UserRolesList.Add(newRole)
            cboRoles.Properties.Items.Add(newRole)
            cboRoles.SelectedIndex = cboRoles.Properties.Items.Count - 1

            For Each role As DOUserRolesList In cboRoles.Properties.Items
                If role.Oid = newRole.Oid Then
                    cboRoles.SelectedItem = role
                End If
            Next
        End If
    End Sub

    Private Sub ReportRolesEditor_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If PendingChanges Then
            Dim dialog_result As DialogResult = MessageBox.Show("Some changes may not have been saved. Are you sure you want to quit?", "Closing Window", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation)

            If (dialog_result = System.Windows.Forms.DialogResult.No) Then
                e.Cancel = True
            Else
                If Session.DefaultSession.InTransaction Then
                    Session.DefaultSession.RollbackTransaction()
                End If

                Me.DialogResult = System.Windows.Forms.DialogResult.OK
                Me.Dispose()
            End If
        End If
    End Sub
#End Region

#Region "Combo Box Event Handlers"
    Private Sub cboFacility_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboFacility.SelectedIndexChanged
        CurrentFacilityReportList = BuildReportList(cboFacility.SelectedItem)
        LoadRoleList()
    End Sub

    Private Sub cboRoles_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboRoles.SelectedIndexChanged
        LoadRoleReports()
        LoadReportList()
        LoadChanges()
    End Sub
#End Region

#Region "Event Handlers for Displaying and Drawing"
    Private Sub listAllReports_DrawItem(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.ListBoxDrawItemEventArgs) Handles listAllReports.DrawItem, listRoleReports.DrawItem
        If TypeOf e.Item Is DOFacilityReportCategory Then
            Dim category As DOFacilityReportCategory = CType(e.Item, DOFacilityReportCategory)
            e.Appearance.DrawBackground(e.Cache, e.Bounds)
            Using boldFont As New Font(e.Appearance.Font, FontStyle.Bold)
                e.Graphics.DrawString(category.CategoryName & " (Category)", boldFont, e.Appearance.GetForeBrush(e.Cache), e.Bounds.Location)
            End Using
            e.Handled = True
        Else
            Dim report As DOFacilityReport = CType(e.Item, DOFacilityReport)
            e.Appearance.DrawBackground(e.Cache, e.Bounds)
            e.Graphics.DrawString(report.ReportDescription, e.Appearance.Font, e.Appearance.GetForeBrush(e.Cache), e.Bounds.Location)
            e.Handled = True
        End If
    End Sub

    Private Sub cboRoles_CustomDisplayText(ByVal sender As Object, ByVal e As CustomDisplayTextEventArgs) Handles cboRoles.CustomDisplayText
        Dim item As DOUserRolesList = e.Value
        If Not item Is Nothing Then
            e.DisplayText = item.Role
        End If
    End Sub

    Private Sub cboRoles_DrawItem(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.ListBoxDrawItemEventArgs) Handles cboRoles.DrawItem
        Dim item As DOUserRolesList = e.Item
        e.Appearance.DrawBackground(e.Cache, e.Bounds)
        e.Graphics.DrawString(item.Role, e.Appearance.Font, e.Appearance.GetForeBrush(e.Cache), e.Bounds.Location)
        e.Handled = True
    End Sub
#End Region
End Class
﻿Imports System.IO

Public Class PdfViewerForm
    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Public Sub LoadDoc(docPath As String)
        PdfViewer1.LoadDocument(docPath)
    End Sub

    Private streamData As Stream

    Public Sub LoadDoc(stream As Stream)
        streamData = stream
        PdfViewer1.DetachStreamAfterLoadComplete = True
        PdfViewer1.LoadDocument(streamData)
    End Sub

    Private Sub PdfExportFormDataBarItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles PdfExportFormDataBarItem1.ItemClick

    End Sub

    Private Sub PdfViewerForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub PdfViewerForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        streamData.Dispose()
    End Sub
End Class
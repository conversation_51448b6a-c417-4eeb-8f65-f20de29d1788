﻿Imports System.Linq
Imports DevExpress.Xpo

Public Class MigrateTAHelper

    Public Sub MigrateAllFacilities()
        Try
            For Each facility In DOFacility.GetAllFacilitesAsList()
                MigrateLegacyTreatmentAreas(facility)
            Next

            MessageBox.Show($"Treatment Area tables successfully created/updated.", "", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"An unexpected error occurred while migrating Treatment Area tables. Error Message {ex?.Message}", "MigrateAllFacilities error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    Private Sub MigrateLegacyTreatmentAreas(facility As DOFacility)
        Dim treatmentAreas = New XPCollection(Of DOTreatmentArea)().ToList().Where(Function(ta) ta.Facility.Oid = facility.Oid)

        Dim treatmentAreaItems = facility.ConfigInstanceVersion.GetComboBoxListByControlName("TreatmentArea_cbo")
        If treatmentAreaItems IsNot Nothing Then
            For Each ta In treatmentAreaItems.ListItems
                ' If the treatment area is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ta.ItemDisplayName) Is Nothing Then
                    CreateTreatmentAreaRecord(facility, ta.ItemDisplayName, "ED")
                End If
            Next
        End If

        Dim nursingStationItems = facility.ConfigInstanceVersion.GetComboBoxListByControlName("TreatmentAreaObsTab_cbo")
        If nursingStationItems IsNot Nothing Then
            For Each ns In nursingStationItems.ListItems
                ' If the nursingstation is currently not a record in the table create it
                If treatmentAreas.FirstOrDefault(Function(x) x.Name = ns.ItemDisplayName) Is Nothing Then
                    CreateTreatmentAreaRecord(facility, ns.ItemDisplayName, "OBS")
                End If
            Next
        End If
    End Sub

    Private Sub CreateTreatmentAreaRecord(facility As DOFacility, treatmentAreaName As String, treatmentAreaType As String)
        Dim newTreatmentArea As New DOTreatmentArea With {
                    .Facility = facility,
                    .Name = treatmentAreaName,
                    .ChargeMaster = treatmentAreaName,
        .TreatmentAreaType = treatmentAreaType,
                    .Enabled = True
            }
        newTreatmentArea.Save()
    End Sub


End Class

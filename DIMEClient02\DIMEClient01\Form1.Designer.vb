<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.Label1 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.txtXMLFile = New DevExpress.XtraEditors.TextEdit
        Me.lblXMLFileLabel = New DevExpress.XtraEditors.LabelControl
        Me.lblFirstName = New DevExpress.XtraEditors.LabelControl
        Me.txtFirstName = New DevExpress.XtraEditors.TextEdit
        Me.txtMiddleName = New DevExpress.XtraEditors.TextEdit
        Me.lblMiddleName = New DevExpress.XtraEditors.LabelControl
        Me.txtLastName = New DevExpress.XtraEditors.TextEdit
        Me.lblLastName = New DevExpress.XtraEditors.LabelControl
        Me.txtDOB = New DevExpress.XtraEditors.TextEdit
        Me.lblDOB = New DevExpress.XtraEditors.LabelControl
        Me.txtMRN = New DevExpress.XtraEditors.TextEdit
        Me.lblMRN = New DevExpress.XtraEditors.LabelControl
        Me.txtSSN = New DevExpress.XtraEditors.TextEdit
        Me.lblSSN = New DevExpress.XtraEditors.LabelControl
        Me.txtGender = New DevExpress.XtraEditors.TextEdit
        Me.lblGender = New DevExpress.XtraEditors.LabelControl
        Me.lblSuffix = New DevExpress.XtraEditors.LabelControl
        Me.txtSuffix = New DevExpress.XtraEditors.TextEdit
        Me.txtFinancialClass = New DevExpress.XtraEditors.TextEdit
        Me.lblFinancialClass = New DevExpress.XtraEditors.LabelControl
        Me.txtAddress1 = New DevExpress.XtraEditors.TextEdit
        Me.lblAddress1 = New DevExpress.XtraEditors.LabelControl
        Me.txtAddress2 = New DevExpress.XtraEditors.TextEdit
        Me.lblAddress2 = New DevExpress.XtraEditors.LabelControl
        Me.txtCity = New DevExpress.XtraEditors.TextEdit
        Me.lblCity = New DevExpress.XtraEditors.LabelControl
        Me.txtState = New DevExpress.XtraEditors.TextEdit
        Me.lblState = New DevExpress.XtraEditors.LabelControl
        Me.txtZip = New DevExpress.XtraEditors.TextEdit
        Me.lblZip = New DevExpress.XtraEditors.LabelControl
        Me.txtTreatmentArea = New DevExpress.XtraEditors.TextEdit
        Me.lblTreatmentArea = New DevExpress.XtraEditors.LabelControl
        Me.txtFacility = New DevExpress.XtraEditors.TextEdit
        Me.lblFacility = New DevExpress.XtraEditors.LabelControl
        Me.txtDOS = New DevExpress.XtraEditors.TextEdit
        Me.lblDOS = New DevExpress.XtraEditors.LabelControl
        Me.txtAccountNumber = New DevExpress.XtraEditors.TextEdit
        Me.lblAccountNumber = New DevExpress.XtraEditors.LabelControl
        Me.btnSend = New DevExpress.XtraEditors.SimpleButton
        Me.btnSendX = New DevExpress.XtraEditors.SimpleButton
        Me.txtSendX = New DevExpress.XtraEditors.TextEdit
        Me.btnContinuous = New DevExpress.XtraEditors.SimpleButton
        Me.lblSendX = New DevExpress.XtraEditors.LabelControl
        Me.txtContinuousMins = New DevExpress.XtraEditors.TextEdit
        Me.lblContinuousMins = New DevExpress.XtraEditors.LabelControl
        Me.tmrContinuous = New System.Windows.Forms.Timer(Me.components)
        Me.stpMain = New System.Windows.Forms.StatusStrip
        Me.stplblURL = New System.Windows.Forms.ToolStripStatusLabel
        Me.stplblLogFile = New System.Windows.Forms.ToolStripStatusLabel
        Me.btnClear = New DevExpress.XtraEditors.SimpleButton
        Me.btnSendFolder = New DevExpress.XtraEditors.SimpleButton
        CType(Me.txtXMLFile.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtFirstName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtMiddleName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtLastName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDOB.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtMRN.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtSSN.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtGender.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtSuffix.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtFinancialClass.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtAddress1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtAddress2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtState.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtTreatmentArea.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtFacility.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDOS.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtAccountNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtSendX.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtContinuousMins.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.stpMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Arial Black", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(7, 22)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(97, 23)
        Me.Label1.TabIndex = 4
        Me.Label1.Text = "Chart Info"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Arial Black", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(7, 176)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(113, 23)
        Me.Label2.TabIndex = 5
        Me.Label2.Text = "Patient Info"
        '
        'txtXMLFile
        '
        Me.txtXMLFile.EditValue = ""
        Me.txtXMLFile.Location = New System.Drawing.Point(12, 435)
        Me.txtXMLFile.Name = "txtXMLFile"
        Me.txtXMLFile.Size = New System.Drawing.Size(552, 20)
        Me.txtXMLFile.TabIndex = 22
        '
        'lblXMLFileLabel
        '
        Me.lblXMLFileLabel.Location = New System.Drawing.Point(12, 416)
        Me.lblXMLFileLabel.Name = "lblXMLFileLabel"
        Me.lblXMLFileLabel.Size = New System.Drawing.Size(42, 13)
        Me.lblXMLFileLabel.TabIndex = 23
        Me.lblXMLFileLabel.Text = "XML File:"
        '
        'lblFirstName
        '
        Me.lblFirstName.Location = New System.Drawing.Point(12, 217)
        Me.lblFirstName.Name = "lblFirstName"
        Me.lblFirstName.Size = New System.Drawing.Size(55, 13)
        Me.lblFirstName.TabIndex = 24
        Me.lblFirstName.Text = "First Name:"
        '
        'txtFirstName
        '
        Me.txtFirstName.Location = New System.Drawing.Point(82, 210)
        Me.txtFirstName.Name = "txtFirstName"
        Me.txtFirstName.Size = New System.Drawing.Size(156, 20)
        Me.txtFirstName.TabIndex = 25
        '
        'txtMiddleName
        '
        Me.txtMiddleName.Location = New System.Drawing.Point(82, 236)
        Me.txtMiddleName.Name = "txtMiddleName"
        Me.txtMiddleName.Size = New System.Drawing.Size(156, 20)
        Me.txtMiddleName.TabIndex = 27
        '
        'lblMiddleName
        '
        Me.lblMiddleName.Location = New System.Drawing.Point(12, 243)
        Me.lblMiddleName.Name = "lblMiddleName"
        Me.lblMiddleName.Size = New System.Drawing.Size(64, 13)
        Me.lblMiddleName.TabIndex = 26
        Me.lblMiddleName.Text = "Middle Name:"
        '
        'txtLastName
        '
        Me.txtLastName.Location = New System.Drawing.Point(82, 262)
        Me.txtLastName.Name = "txtLastName"
        Me.txtLastName.Size = New System.Drawing.Size(156, 20)
        Me.txtLastName.TabIndex = 28
        '
        'lblLastName
        '
        Me.lblLastName.Location = New System.Drawing.Point(12, 269)
        Me.lblLastName.Name = "lblLastName"
        Me.lblLastName.Size = New System.Drawing.Size(54, 13)
        Me.lblLastName.TabIndex = 28
        Me.lblLastName.Text = "Last Name:"
        '
        'txtDOB
        '
        Me.txtDOB.Location = New System.Drawing.Point(82, 314)
        Me.txtDOB.Name = "txtDOB"
        Me.txtDOB.Size = New System.Drawing.Size(156, 20)
        Me.txtDOB.TabIndex = 31
        Me.txtDOB.ToolTip = "yyyy-mm-dd hh:mm:ss.f"
        '
        'lblDOB
        '
        Me.lblDOB.Location = New System.Drawing.Point(12, 321)
        Me.lblDOB.Name = "lblDOB"
        Me.lblDOB.Size = New System.Drawing.Size(25, 13)
        Me.lblDOB.TabIndex = 30
        Me.lblDOB.Text = "DOB:"
        '
        'txtMRN
        '
        Me.txtMRN.Location = New System.Drawing.Point(82, 340)
        Me.txtMRN.Name = "txtMRN"
        Me.txtMRN.Size = New System.Drawing.Size(156, 20)
        Me.txtMRN.TabIndex = 33
        '
        'lblMRN
        '
        Me.lblMRN.Location = New System.Drawing.Point(12, 347)
        Me.lblMRN.Name = "lblMRN"
        Me.lblMRN.Size = New System.Drawing.Size(26, 13)
        Me.lblMRN.TabIndex = 32
        Me.lblMRN.Text = "MRN:"
        '
        'txtSSN
        '
        Me.txtSSN.Location = New System.Drawing.Point(82, 366)
        Me.txtSSN.Name = "txtSSN"
        Me.txtSSN.Size = New System.Drawing.Size(156, 20)
        Me.txtSSN.TabIndex = 35
        '
        'lblSSN
        '
        Me.lblSSN.Location = New System.Drawing.Point(12, 373)
        Me.lblSSN.Name = "lblSSN"
        Me.lblSSN.Size = New System.Drawing.Size(23, 13)
        Me.lblSSN.TabIndex = 34
        Me.lblSSN.Text = "SSN:"
        '
        'txtGender
        '
        Me.txtGender.Location = New System.Drawing.Point(340, 210)
        Me.txtGender.Name = "txtGender"
        Me.txtGender.Size = New System.Drawing.Size(156, 20)
        Me.txtGender.TabIndex = 37
        Me.txtGender.ToolTip = "M/F"
        '
        'lblGender
        '
        Me.lblGender.Location = New System.Drawing.Point(270, 217)
        Me.lblGender.Name = "lblGender"
        Me.lblGender.Size = New System.Drawing.Size(39, 13)
        Me.lblGender.TabIndex = 36
        Me.lblGender.Text = "Gender:"
        '
        'lblSuffix
        '
        Me.lblSuffix.Location = New System.Drawing.Point(12, 295)
        Me.lblSuffix.Name = "lblSuffix"
        Me.lblSuffix.Size = New System.Drawing.Size(32, 13)
        Me.lblSuffix.TabIndex = 28
        Me.lblSuffix.Text = "Suffix:"
        '
        'txtSuffix
        '
        Me.txtSuffix.Location = New System.Drawing.Point(82, 288)
        Me.txtSuffix.Name = "txtSuffix"
        Me.txtSuffix.Size = New System.Drawing.Size(156, 20)
        Me.txtSuffix.TabIndex = 29
        '
        'txtFinancialClass
        '
        Me.txtFinancialClass.Location = New System.Drawing.Point(340, 236)
        Me.txtFinancialClass.Name = "txtFinancialClass"
        Me.txtFinancialClass.Size = New System.Drawing.Size(156, 20)
        Me.txtFinancialClass.TabIndex = 39
        '
        'lblFinancialClass
        '
        Me.lblFinancialClass.Location = New System.Drawing.Point(270, 243)
        Me.lblFinancialClass.Name = "lblFinancialClass"
        Me.lblFinancialClass.Size = New System.Drawing.Size(50, 13)
        Me.lblFinancialClass.TabIndex = 38
        Me.lblFinancialClass.Text = "Fin. Class:"
        '
        'txtAddress1
        '
        Me.txtAddress1.Location = New System.Drawing.Point(340, 262)
        Me.txtAddress1.Name = "txtAddress1"
        Me.txtAddress1.Size = New System.Drawing.Size(156, 20)
        Me.txtAddress1.TabIndex = 41
        '
        'lblAddress1
        '
        Me.lblAddress1.Location = New System.Drawing.Point(270, 269)
        Me.lblAddress1.Name = "lblAddress1"
        Me.lblAddress1.Size = New System.Drawing.Size(52, 13)
        Me.lblAddress1.TabIndex = 40
        Me.lblAddress1.Text = "Address 1:"
        '
        'txtAddress2
        '
        Me.txtAddress2.Location = New System.Drawing.Point(340, 288)
        Me.txtAddress2.Name = "txtAddress2"
        Me.txtAddress2.Size = New System.Drawing.Size(156, 20)
        Me.txtAddress2.TabIndex = 43
        '
        'lblAddress2
        '
        Me.lblAddress2.Location = New System.Drawing.Point(270, 295)
        Me.lblAddress2.Name = "lblAddress2"
        Me.lblAddress2.Size = New System.Drawing.Size(52, 13)
        Me.lblAddress2.TabIndex = 42
        Me.lblAddress2.Text = "Address 2:"
        '
        'txtCity
        '
        Me.txtCity.Location = New System.Drawing.Point(340, 314)
        Me.txtCity.Name = "txtCity"
        Me.txtCity.Size = New System.Drawing.Size(156, 20)
        Me.txtCity.TabIndex = 45
        '
        'lblCity
        '
        Me.lblCity.Location = New System.Drawing.Point(270, 321)
        Me.lblCity.Name = "lblCity"
        Me.lblCity.Size = New System.Drawing.Size(23, 13)
        Me.lblCity.TabIndex = 44
        Me.lblCity.Text = "City:"
        '
        'txtState
        '
        Me.txtState.Location = New System.Drawing.Point(340, 340)
        Me.txtState.Name = "txtState"
        Me.txtState.Size = New System.Drawing.Size(156, 20)
        Me.txtState.TabIndex = 47
        '
        'lblState
        '
        Me.lblState.Location = New System.Drawing.Point(270, 347)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(30, 13)
        Me.lblState.TabIndex = 46
        Me.lblState.Text = "State:"
        '
        'txtZip
        '
        Me.txtZip.Location = New System.Drawing.Point(340, 366)
        Me.txtZip.Name = "txtZip"
        Me.txtZip.Size = New System.Drawing.Size(156, 20)
        Me.txtZip.TabIndex = 49
        '
        'lblZip
        '
        Me.lblZip.Location = New System.Drawing.Point(270, 373)
        Me.lblZip.Name = "lblZip"
        Me.lblZip.Size = New System.Drawing.Size(18, 13)
        Me.lblZip.TabIndex = 48
        Me.lblZip.Text = "Zip:"
        '
        'txtTreatmentArea
        '
        Me.txtTreatmentArea.Location = New System.Drawing.Point(82, 129)
        Me.txtTreatmentArea.Name = "txtTreatmentArea"
        Me.txtTreatmentArea.Size = New System.Drawing.Size(156, 20)
        Me.txtTreatmentArea.TabIndex = 56
        '
        'lblTreatmentArea
        '
        Me.lblTreatmentArea.Location = New System.Drawing.Point(12, 136)
        Me.lblTreatmentArea.Name = "lblTreatmentArea"
        Me.lblTreatmentArea.Size = New System.Drawing.Size(62, 13)
        Me.lblTreatmentArea.TabIndex = 54
        Me.lblTreatmentArea.Text = "Trtmnt Area:"
        '
        'txtFacility
        '
        Me.txtFacility.Location = New System.Drawing.Point(82, 103)
        Me.txtFacility.Name = "txtFacility"
        Me.txtFacility.Size = New System.Drawing.Size(156, 20)
        Me.txtFacility.TabIndex = 57
        '
        'lblFacility
        '
        Me.lblFacility.Location = New System.Drawing.Point(12, 110)
        Me.lblFacility.Name = "lblFacility"
        Me.lblFacility.Size = New System.Drawing.Size(37, 13)
        Me.lblFacility.TabIndex = 55
        Me.lblFacility.Text = "Facility:"
        '
        'txtDOS
        '
        Me.txtDOS.Location = New System.Drawing.Point(82, 77)
        Me.txtDOS.Name = "txtDOS"
        Me.txtDOS.Size = New System.Drawing.Size(156, 20)
        Me.txtDOS.TabIndex = 53
        '
        'lblDOS
        '
        Me.lblDOS.Location = New System.Drawing.Point(12, 84)
        Me.lblDOS.Name = "lblDOS"
        Me.lblDOS.Size = New System.Drawing.Size(25, 13)
        Me.lblDOS.TabIndex = 52
        Me.lblDOS.Text = "DOS:"
        '
        'txtAccountNumber
        '
        Me.txtAccountNumber.Location = New System.Drawing.Point(82, 51)
        Me.txtAccountNumber.Name = "txtAccountNumber"
        Me.txtAccountNumber.Size = New System.Drawing.Size(156, 20)
        Me.txtAccountNumber.TabIndex = 51
        '
        'lblAccountNumber
        '
        Me.lblAccountNumber.Location = New System.Drawing.Point(12, 58)
        Me.lblAccountNumber.Name = "lblAccountNumber"
        Me.lblAccountNumber.Size = New System.Drawing.Size(54, 13)
        Me.lblAccountNumber.TabIndex = 50
        Me.lblAccountNumber.Text = "Account #:"
        '
        'btnSend
        '
        Me.btnSend.Location = New System.Drawing.Point(489, 12)
        Me.btnSend.Name = "btnSend"
        Me.btnSend.Size = New System.Drawing.Size(75, 23)
        Me.btnSend.TabIndex = 58
        Me.btnSend.Text = "Send"
        '
        'btnSendX
        '
        Me.btnSendX.Location = New System.Drawing.Point(489, 42)
        Me.btnSendX.Name = "btnSendX"
        Me.btnSendX.Size = New System.Drawing.Size(75, 23)
        Me.btnSendX.TabIndex = 59
        Me.btnSendX.Text = "Send X"
        '
        'txtSendX
        '
        Me.txtSendX.EditValue = "50"
        Me.txtSendX.Location = New System.Drawing.Point(453, 44)
        Me.txtSendX.Name = "txtSendX"
        Me.txtSendX.Size = New System.Drawing.Size(30, 20)
        Me.txtSendX.TabIndex = 60
        '
        'btnContinuous
        '
        Me.btnContinuous.Location = New System.Drawing.Point(489, 72)
        Me.btnContinuous.Name = "btnContinuous"
        Me.btnContinuous.Size = New System.Drawing.Size(75, 23)
        Me.btnContinuous.TabIndex = 61
        Me.btnContinuous.Text = "Continuous"
        '
        'lblSendX
        '
        Me.lblSendX.Location = New System.Drawing.Point(400, 47)
        Me.lblSendX.Name = "lblSendX"
        Me.lblSendX.Size = New System.Drawing.Size(47, 13)
        Me.lblSendX.TabIndex = 62
        Me.lblSendX.Text = "# Charts:"
        '
        'txtContinuousMins
        '
        Me.txtContinuousMins.EditValue = "60"
        Me.txtContinuousMins.Location = New System.Drawing.Point(453, 74)
        Me.txtContinuousMins.Name = "txtContinuousMins"
        Me.txtContinuousMins.Size = New System.Drawing.Size(29, 20)
        Me.txtContinuousMins.TabIndex = 63
        '
        'lblContinuousMins
        '
        Me.lblContinuousMins.Location = New System.Drawing.Point(411, 77)
        Me.lblContinuousMins.Name = "lblContinuousMins"
        Me.lblContinuousMins.Size = New System.Drawing.Size(36, 13)
        Me.lblContinuousMins.TabIndex = 64
        Me.lblContinuousMins.Text = "# Mins:"
        '
        'tmrContinuous
        '
        '
        'stpMain
        '
        Me.stpMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.stplblURL, Me.stplblLogFile})
        Me.stpMain.Location = New System.Drawing.Point(0, 465)
        Me.stpMain.Name = "stpMain"
        Me.stpMain.Size = New System.Drawing.Size(576, 22)
        Me.stpMain.TabIndex = 65
        Me.stpMain.Text = "StatusStrip1"
        '
        'stplblURL
        '
        Me.stplblURL.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Right
        Me.stplblURL.BorderStyle = System.Windows.Forms.Border3DStyle.Etched
        Me.stplblURL.IsLink = True
        Me.stplblURL.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.stplblURL.LinkColor = System.Drawing.Color.Black
        Me.stplblURL.Name = "stplblURL"
        Me.stplblURL.Padding = New System.Windows.Forms.Padding(10, 0, 10, 0)
        Me.stplblURL.Size = New System.Drawing.Size(43, 17)
        Me.stplblURL.Text = "..."
        '
        'stplblLogFile
        '
        Me.stplblLogFile.IsLink = True
        Me.stplblLogFile.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.stplblLogFile.LinkColor = System.Drawing.Color.Black
        Me.stplblLogFile.Name = "stplblLogFile"
        Me.stplblLogFile.Padding = New System.Windows.Forms.Padding(10, 0, 10, 0)
        Me.stplblLogFile.Size = New System.Drawing.Size(69, 17)
        Me.stplblLogFile.Text = "View Log"
        '
        'btnClear
        '
        Me.btnClear.Location = New System.Drawing.Point(489, 130)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Size = New System.Drawing.Size(75, 23)
        Me.btnClear.TabIndex = 66
        Me.btnClear.Text = "Clear"
        '
        'btnSendFolder
        '
        Me.btnSendFolder.Location = New System.Drawing.Point(489, 101)
        Me.btnSendFolder.Name = "btnSendFolder"
        Me.btnSendFolder.Size = New System.Drawing.Size(75, 23)
        Me.btnSendFolder.TabIndex = 67
        Me.btnSendFolder.Text = "Send Folder"
        '
        'Form1
        '
        Me.AllowDrop = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(576, 487)
        Me.Controls.Add(Me.btnSendFolder)
        Me.Controls.Add(Me.btnClear)
        Me.Controls.Add(Me.stpMain)
        Me.Controls.Add(Me.lblContinuousMins)
        Me.Controls.Add(Me.txtContinuousMins)
        Me.Controls.Add(Me.lblSendX)
        Me.Controls.Add(Me.btnContinuous)
        Me.Controls.Add(Me.txtSendX)
        Me.Controls.Add(Me.btnSendX)
        Me.Controls.Add(Me.btnSend)
        Me.Controls.Add(Me.txtTreatmentArea)
        Me.Controls.Add(Me.lblTreatmentArea)
        Me.Controls.Add(Me.txtFacility)
        Me.Controls.Add(Me.lblFacility)
        Me.Controls.Add(Me.txtDOS)
        Me.Controls.Add(Me.lblDOS)
        Me.Controls.Add(Me.txtAccountNumber)
        Me.Controls.Add(Me.lblAccountNumber)
        Me.Controls.Add(Me.txtZip)
        Me.Controls.Add(Me.lblZip)
        Me.Controls.Add(Me.txtState)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.txtCity)
        Me.Controls.Add(Me.lblCity)
        Me.Controls.Add(Me.txtAddress2)
        Me.Controls.Add(Me.lblAddress2)
        Me.Controls.Add(Me.txtAddress1)
        Me.Controls.Add(Me.lblAddress1)
        Me.Controls.Add(Me.txtFinancialClass)
        Me.Controls.Add(Me.lblFinancialClass)
        Me.Controls.Add(Me.txtGender)
        Me.Controls.Add(Me.lblGender)
        Me.Controls.Add(Me.txtSSN)
        Me.Controls.Add(Me.lblSSN)
        Me.Controls.Add(Me.txtMRN)
        Me.Controls.Add(Me.lblMRN)
        Me.Controls.Add(Me.txtDOB)
        Me.Controls.Add(Me.lblDOB)
        Me.Controls.Add(Me.txtSuffix)
        Me.Controls.Add(Me.lblSuffix)
        Me.Controls.Add(Me.txtLastName)
        Me.Controls.Add(Me.lblLastName)
        Me.Controls.Add(Me.txtMiddleName)
        Me.Controls.Add(Me.lblMiddleName)
        Me.Controls.Add(Me.txtFirstName)
        Me.Controls.Add(Me.lblFirstName)
        Me.Controls.Add(Me.lblXMLFileLabel)
        Me.Controls.Add(Me.txtXMLFile)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Name = "Form1"
        Me.Text = "HEC DIME Client"
        CType(Me.txtXMLFile.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtFirstName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtMiddleName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtLastName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDOB.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtMRN.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtSSN.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtGender.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtSuffix.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtFinancialClass.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtAddress1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtAddress2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtState.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtTreatmentArea.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtFacility.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDOS.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtAccountNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtSendX.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtContinuousMins.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.stpMain.ResumeLayout(False)
        Me.stpMain.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents txtXMLFile As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblXMLFileLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblFirstName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtFirstName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtMiddleName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblMiddleName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtLastName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblLastName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtDOB As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblDOB As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtMRN As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblMRN As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtSSN As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblSSN As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtGender As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblGender As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblSuffix As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtSuffix As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtFinancialClass As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblFinancialClass As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtAddress1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblAddress1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtAddress2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblAddress2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtCity As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblCity As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtState As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblState As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblZip As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtTreatmentArea As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblTreatmentArea As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtFacility As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblFacility As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtDOS As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblDOS As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtAccountNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblAccountNumber As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnSend As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSendX As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtSendX As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnContinuous As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblSendX As DevExpress.XtraEditors.LabelControl
    Friend WithEvents txtContinuousMins As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblContinuousMins As DevExpress.XtraEditors.LabelControl
    Friend WithEvents tmrContinuous As System.Windows.Forms.Timer
    Friend WithEvents stpMain As System.Windows.Forms.StatusStrip
    Friend WithEvents stplblURL As System.Windows.Forms.ToolStripStatusLabel
    Friend WithEvents stplblLogFile As System.Windows.Forms.ToolStripStatusLabel
    Friend WithEvents btnClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSendFolder As DevExpress.XtraEditors.SimpleButton

End Class

@ECHO OFF 
@ECHO Please shutdown the Sybase 9 Db server if it's still running.
PAUSE

SET dbName=enchartplus

IF "%1" == "" GOTO INSTRUCTIONS
IF "%2" == "" GOTO INSTRUCTIONS
IF NOT "%3" == "" SET dbName=%3

SET passWord=%1
SET encryptKey=%2
SET oldDbName= %dbName%Backup


IF "%SQLANY17%" == "" GOTO INSTALLSAP17

IF EXIST %dbName%.db (
rename %dbname%.db %oldDbName%.db
dblog %oldDbname%.db -t %oldDbname%.log
)


IF EXIST %dbName%.log rename %dbname%.log %oldDbName%.log


:DOMIGRATION

"%SQLANY17%\Bin64\dbunload.EXE" -v -c "UID=dba;PWD=%passWord%;DBF=%oldDbName%.db" -an %dbName%.db -ap 4096 -ea AES256 -ek %encryptKey% -ii -up

@echo Done ...
GOTO END

:INSTALLSAP17
@ECHO The SQLANY17 environment variable was NOT detected. Please install SAP 17 first.
GOTO END

:INSTRUCTIONS

@ECHO ERROR - Missing Parameters
@ECHO Usage: sybase9to17 password key [database name]


GOTO END

:END
pause
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class formEditControlOverrides
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView
        Me.colEnabled1 = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colName = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colDisplayText = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colHide = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colWidthMod = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colXPosMod = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colYPosMod = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colIsRequiredOverride = New DevExpress.XtraGrid.Columns.GridColumn
        Me.gcControlOverride = New DevExpress.XtraGrid.GridControl
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView
        Me.colEnabled = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colGroupName = New DevExpress.XtraGrid.Columns.GridColumn
        Me.colComments = New DevExpress.XtraGrid.Columns.GridColumn
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView
        Me.btnDone = New DevExpress.XtraEditors.SimpleButton
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcControlOverride, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridView3
        '
        Me.GridView3.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEnabled1, Me.colName, Me.colDisplayText, Me.colHide, Me.colWidthMod, Me.colXPosMod, Me.colYPosMod, Me.colIsRequiredOverride})
        Me.GridView3.GridControl = Me.gcControlOverride
        Me.GridView3.Name = "GridView3"
        Me.GridView3.OptionsView.ShowAutoFilterRow = True
        '
        'colEnabled1
        '
        Me.colEnabled1.Caption = "Enabled"
        Me.colEnabled1.FieldName = "Enabled"
        Me.colEnabled1.Name = "colEnabled1"
        Me.colEnabled1.Visible = True
        Me.colEnabled1.VisibleIndex = 0
        Me.colEnabled1.Width = 91
        '
        'colName
        '
        Me.colName.Caption = "Name"
        Me.colName.FieldName = "Name"
        Me.colName.Name = "colName"
        Me.colName.Visible = True
        Me.colName.VisibleIndex = 1
        Me.colName.Width = 268
        '
        'colDisplayText
        '
        Me.colDisplayText.Caption = "DisplayText"
        Me.colDisplayText.FieldName = "DisplayText"
        Me.colDisplayText.Name = "colDisplayText"
        Me.colDisplayText.Visible = True
        Me.colDisplayText.VisibleIndex = 2
        Me.colDisplayText.Width = 241
        '
        'colHide
        '
        Me.colHide.Caption = "Hide"
        Me.colHide.FieldName = "Hide"
        Me.colHide.Name = "colHide"
        Me.colHide.Visible = True
        Me.colHide.VisibleIndex = 3
        Me.colHide.Width = 64
        '
        'colWidthMod
        '
        Me.colWidthMod.Caption = "WidthMod"
        Me.colWidthMod.FieldName = "WidthMod"
        Me.colWidthMod.Name = "colWidthMod"
        Me.colWidthMod.Visible = True
        Me.colWidthMod.VisibleIndex = 6
        Me.colWidthMod.Width = 571
        '
        'colXPosMod
        '
        Me.colXPosMod.Caption = "XPosMod"
        Me.colXPosMod.FieldName = "XPosMod"
        Me.colXPosMod.Name = "colXPosMod"
        Me.colXPosMod.Visible = True
        Me.colXPosMod.VisibleIndex = 4
        Me.colXPosMod.Width = 96
        '
        'colYPosMod
        '
        Me.colYPosMod.Caption = "YPosMod"
        Me.colYPosMod.FieldName = "YPosMod"
        Me.colYPosMod.Name = "colYPosMod"
        Me.colYPosMod.Visible = True
        Me.colYPosMod.VisibleIndex = 5
        Me.colYPosMod.Width = 92
        '
        'colIsRequiredOverride
        '
        Me.colIsRequiredOverride.Caption = "IsRequiredOverride"
        Me.colIsRequiredOverride.FieldName = "IsRequiredOverride"
        Me.colIsRequiredOverride.Name = "colIsRequiredOverride"
        Me.colIsRequiredOverride.Width = 184
        '
        'gcControlOverride
        '
        Me.gcControlOverride.DataSource = Me.XpCollection1
        Me.gcControlOverride.EmbeddedNavigator.Name = ""
        GridLevelNode1.LevelTemplate = Me.GridView3
        GridLevelNode1.RelationName = "Controlconfigs"
        Me.gcControlOverride.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1})
        Me.gcControlOverride.Location = New System.Drawing.Point(22, 42)
        Me.gcControlOverride.MainView = Me.GridView1
        Me.gcControlOverride.Name = "gcControlOverride"
        Me.gcControlOverride.ShowOnlyPredefinedDetails = True
        Me.gcControlOverride.Size = New System.Drawing.Size(941, 569)
        Me.gcControlOverride.TabIndex = 1
        Me.gcControlOverride.UseEmbeddedNavigator = True
        Me.gcControlOverride.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1, Me.GridView2, Me.GridView3})
        '
        'XpCollection1
        '
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOControlConfigGroup)
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEnabled, Me.colGroupName, Me.colComments})
        Me.GridView1.GridControl = Me.gcControlOverride
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        '
        'colEnabled
        '
        Me.colEnabled.Caption = "Enabled"
        Me.colEnabled.FieldName = "Enabled"
        Me.colEnabled.Name = "colEnabled"
        Me.colEnabled.Visible = True
        Me.colEnabled.VisibleIndex = 0
        Me.colEnabled.Width = 140
        '
        'colGroupName
        '
        Me.colGroupName.Caption = "GroupName"
        Me.colGroupName.FieldName = "GroupName"
        Me.colGroupName.Name = "colGroupName"
        Me.colGroupName.Visible = True
        Me.colGroupName.VisibleIndex = 1
        Me.colGroupName.Width = 371
        '
        'colComments
        '
        Me.colComments.Caption = "Comments"
        Me.colComments.FieldName = "Comments"
        Me.colComments.Name = "colComments"
        Me.colComments.Visible = True
        Me.colComments.VisibleIndex = 2
        Me.colComments.Width = 862
        '
        'GridView2
        '
        Me.GridView2.GridControl = Me.gcControlOverride
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsView.ShowAutoFilterRow = True
        '
        'btnDone
        '
        Me.btnDone.Location = New System.Drawing.Point(237, 627)
        Me.btnDone.Name = "btnDone"
        Me.btnDone.Size = New System.Drawing.Size(75, 23)
        Me.btnDone.TabIndex = 2
        Me.btnDone.Text = "Done"
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(557, 627)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 3
        Me.btnCancel.Text = "Cancel"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, CType((System.Drawing.FontStyle.Bold Or System.Drawing.FontStyle.Italic), System.Drawing.FontStyle))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Location = New System.Drawing.Point(22, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(850, 13)
        Me.LabelControl1.TabIndex = 4
        Me.LabelControl1.Text = "*Still under development! Please don't use unless you are sure of what you're doi" & _
            "ng! Please click the 'Cancel' button below to avoid potentiall data loss."
        '
        'formEditControlOverrides
        '
        Me.AcceptButton = Me.btnDone
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnCancel
        Me.ClientSize = New System.Drawing.Size(987, 668)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnDone)
        Me.Controls.Add(Me.gcControlOverride)
        Me.Name = "formEditControlOverrides"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Control Overrides"
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcControlOverride, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents gcControlOverride As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents colEnabled As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colGroupName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colComments As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridView3 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colEnabled1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsRequiredOverride As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDisplayText As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHide As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colWidthMod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colXPosMod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colYPosMod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnDone As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
End Class

﻿Public Class BOErrorLog

    Public Sub New()

    End Sub

    Public Sub New(doRec As EnchartDOLib.DOErrorLog)
        Application = doRec.Application
        HttpRequestId = doRec.HttpRequestId
        ChartInfoOid = doRec.ChartInfoOid
        ChartOid = doRec.Oid
        VisitId = doRec.VisitId
        SourceContext = doRec.SourceContext
        LogEventProperties = doRec.LogEventProperties
        MsgType = doRec.MsgType
        FacilityID = doRec.FacilityID
        DateTime = doRec.DateTime
        MachineName = doRec.MachineName
        WindowsUserName = doRec.WindowsUserName
        ECUserID = doRec.ECUserID
        ErrorCode = doRec.ErrorCode
        Message = doRec.Message
    End Sub

    Public Property Application As String
    Public Property HttpRequestId As String
    Public Property ChartInfoOid As Integer
    Public Property ChartOid As Integer
    Public Property VisitId As String
    Public Property SourceContext As String
    Property LogEventProperties As String

    Property MsgType As String
    Property FacilityID As Integer
    Property DateTime As DateTime
    Public Property MachineName() As String
    Public Property WindowsUserName() As String
    Property ECUserID As String
    Property ErrorCode As String
    Property Message As String
End Class

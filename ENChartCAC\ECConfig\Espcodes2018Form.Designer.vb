﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class Espcodes2018Form
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Espcodes2018Form))
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colFacility = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colESPVALUE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colLongName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHCPCS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colOid = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colActiveFrom = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFlag = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPoints = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReportDisplayOrder = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSpecial = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colOrcaTab = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colESP_Policy = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colMigrationError = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemDateEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.RepositoryItemDateEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit2.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.BackgroundImage = CType(resources.GetObject("GridControl1.BackgroundImage"), System.Drawing.Image)
        Me.GridControl1.DataSource = Me.XpCollection1
        Me.GridControl1.Location = New System.Drawing.Point(22, 12)
        Me.GridControl1.LookAndFeel.SkinName = "Coffee"
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemDateEdit1, Me.RepositoryItemTextEdit1, Me.RepositoryItemDateEdit2})
        Me.GridControl1.Size = New System.Drawing.Size(991, 627)
        Me.GridControl1.TabIndex = 1
        Me.GridControl1.UseEmbeddedNavigator = True
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'XpCollection1
        '
        Me.XpCollection1.DeleteObjectOnRemove = True
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOESPCode2018)
        '
        'GridView1
        '
        Me.GridView1.Appearance.EvenRow.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridView1.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colFacility, Me.colESPVALUE, Me.colLongName, Me.colHCPCS, Me.colOid, Me.colActiveFrom, Me.colFlag, Me.colPoints, Me.colReportDisplayOrder, Me.colSpecial, Me.colOrcaTab, Me.colESP_Policy, Me.colMigrationError})
        Me.GridView1.CustomizationFormBounds = New System.Drawing.Rectangle(2999, -171, 208, 191)
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupCount = 1
        Me.GridView1.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "", Nothing, "")})
        Me.GridView1.HorzScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Always
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.AllowIncrementalSearch = True
        Me.GridView1.OptionsBehavior.EditingMode = DevExpress.XtraGrid.Views.Grid.GridEditingMode.EditForm
        Me.GridView1.OptionsClipboard.AllowCopy = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsEditForm.ShowOnF2Key = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsFind.AlwaysVisible = True
        Me.GridView1.OptionsFind.FindDelay = 500
        Me.GridView1.OptionsFind.FindMode = DevExpress.XtraEditors.FindMode.Always
        Me.GridView1.OptionsView.AllowHtmlDrawHeaders = True
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ColumnHeaderAutoHeight = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Top
        Me.GridView1.OptionsView.RowAutoHeight = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.OptionsView.ShowGroupedColumns = True
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colFacility, DevExpress.Data.ColumnSortOrder.Ascending), New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colESPVALUE, DevExpress.Data.ColumnSortOrder.Ascending)})
        Me.GridView1.VertScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Always
        Me.GridView1.ViewCaption = "test"
        '
        'colFacility
        '
        Me.colFacility.FieldName = "Facility"
        Me.colFacility.Name = "colFacility"
        Me.colFacility.OptionsEditForm.Visible = DevExpress.Utils.DefaultBoolean.[True]
        '
        'colESPVALUE
        '
        Me.colESPVALUE.FieldName = "ESPVALUE"
        Me.colESPVALUE.Name = "colESPVALUE"
        Me.colESPVALUE.OptionsEditForm.ColumnSpan = 3
        Me.colESPVALUE.OptionsEditForm.StartNewRow = True
        Me.colESPVALUE.OptionsEditForm.UseEditorColRowSpan = False
        Me.colESPVALUE.Visible = True
        Me.colESPVALUE.VisibleIndex = 0
        Me.colESPVALUE.Width = 180
        '
        'colLongName
        '
        Me.colLongName.FieldName = "LongName"
        Me.colLongName.Name = "colLongName"
        Me.colLongName.OptionsEditForm.ColumnSpan = 3
        Me.colLongName.OptionsEditForm.StartNewRow = True
        Me.colLongName.OptionsEditForm.UseEditorColRowSpan = False
        Me.colLongName.Visible = True
        Me.colLongName.VisibleIndex = 5
        Me.colLongName.Width = 562
        '
        'colHCPCS
        '
        Me.colHCPCS.FieldName = "HCPCS"
        Me.colHCPCS.Name = "colHCPCS"
        Me.colHCPCS.OptionsEditForm.StartNewRow = True
        Me.colHCPCS.OptionsEditForm.UseEditorColRowSpan = False
        Me.colHCPCS.Visible = True
        Me.colHCPCS.VisibleIndex = 1
        Me.colHCPCS.Width = 82
        '
        'colOid
        '
        Me.colOid.FieldName = "Oid"
        Me.colOid.Name = "colOid"
        '
        'colActiveFrom
        '
        Me.colActiveFrom.FieldName = "ActiveFrom"
        Me.colActiveFrom.Name = "colActiveFrom"
        Me.colActiveFrom.OptionsEditForm.Visible = DevExpress.Utils.DefaultBoolean.[True]
        Me.colActiveFrom.Visible = True
        Me.colActiveFrom.VisibleIndex = 4
        '
        'colFlag
        '
        Me.colFlag.FieldName = "Flag"
        Me.colFlag.Name = "colFlag"
        Me.colFlag.Visible = True
        Me.colFlag.VisibleIndex = 6
        '
        'colPoints
        '
        Me.colPoints.FieldName = "Points"
        Me.colPoints.Name = "colPoints"
        Me.colPoints.Visible = True
        Me.colPoints.VisibleIndex = 2
        Me.colPoints.Width = 58
        '
        'colReportDisplayOrder
        '
        Me.colReportDisplayOrder.FieldName = "ReportDisplayOrder"
        Me.colReportDisplayOrder.Name = "colReportDisplayOrder"
        '
        'colSpecial
        '
        Me.colSpecial.FieldName = "Special"
        Me.colSpecial.Name = "colSpecial"
        Me.colSpecial.Visible = True
        Me.colSpecial.VisibleIndex = 3
        '
        'colOrcaTab
        '
        Me.colOrcaTab.FieldName = "OrcaTab"
        Me.colOrcaTab.Name = "colOrcaTab"
        Me.colOrcaTab.OptionsEditForm.StartNewRow = True
        Me.colOrcaTab.Width = 63
        '
        'colESP_Policy
        '
        Me.colESP_Policy.FieldName = "ESP_Policy"
        Me.colESP_Policy.Name = "colESP_Policy"
        Me.colESP_Policy.OptionsEditForm.Visible = DevExpress.Utils.DefaultBoolean.[True]
        Me.colESP_Policy.Visible = True
        Me.colESP_Policy.VisibleIndex = 7
        '
        'colMigrationError
        '
        Me.colMigrationError.Caption = "QA"
        Me.colMigrationError.FieldName = "MigrationError"
        Me.colMigrationError.Name = "colMigrationError"
        Me.colMigrationError.ToolTip = "Checked if there was conflicing data in the source espcodes table."
        '
        'RepositoryItemDateEdit1
        '
        Me.RepositoryItemDateEdit1.AutoHeight = False
        Me.RepositoryItemDateEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.RepositoryItemDateEdit1.Name = "RepositoryItemDateEdit1"
        Me.RepositoryItemDateEdit1.NullDate = New Date(CType(0, Long))
        Me.RepositoryItemDateEdit1.NullText = "N/A"
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        Me.RepositoryItemTextEdit1.NullText = "N/A"
        '
        'RepositoryItemDateEdit2
        '
        Me.RepositoryItemDateEdit2.AutoHeight = False
        Me.RepositoryItemDateEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemDateEdit2.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.RepositoryItemDateEdit2.Name = "RepositoryItemDateEdit2"
        Me.RepositoryItemDateEdit2.NullDate = "1/1/0001 12:00:00 AM"
        Me.RepositoryItemDateEdit2.NullText = "N/A"
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.Location = New System.Drawing.Point(425, 659)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(187, 23)
        Me.btnOK.TabIndex = 6
        Me.btnOK.Text = "OK"
        '
        'Espcodes2018Form
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink
        Me.ClientSize = New System.Drawing.Size(1036, 694)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.GridControl1)
        Me.LookAndFeel.SkinName = "Coffee"
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(841, 501)
        Me.Name = "Espcodes2018Form"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Espcodes2018Form"
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit2.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents RepositoryItemDateEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents RepositoryItemDateEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents colOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colActiveFrom As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colESPVALUE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colESP_Policy As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFacility As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFlag As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHCPCS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colLongName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colOrcaTab As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPoints As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReportDisplayOrder As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSpecial As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMigrationError As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
End Class

﻿Public Class GetParagonApiDataForChartResult
    Property ReturnStatusCode As Integer
    Property ReturnStatusDescription As String
    Public Function IsSuccess() As Boolean
        If ReturnStatusCode = 200 Then Return True

        Return False
    End Function

    Property PapiResults As String()
End Class

Public Class GetParagonApiDataForCharDTO
    Property VisitID As String
    Property FacilityID As Integer
End Class
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormNewCategory
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnAddCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.txtCategoryName = New DevExpress.XtraEditors.TextEdit()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.lblCategoryName = New DevExpress.XtraEditors.LabelControl()
        CType(Me.txtCategoryName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btnAddCategory
        '
        Me.btnAddCategory.Enabled = False
        Me.btnAddCategory.Location = New System.Drawing.Point(113, 69)
        Me.btnAddCategory.Name = "btnAddCategory"
        Me.btnAddCategory.Size = New System.Drawing.Size(86, 23)
        Me.btnAddCategory.TabIndex = 0
        Me.btnAddCategory.Text = "Add Category"
        '
        'txtCategoryName
        '
        Me.txtCategoryName.Location = New System.Drawing.Point(12, 43)
        Me.txtCategoryName.Name = "txtCategoryName"
        Me.txtCategoryName.Size = New System.Drawing.Size(268, 20)
        Me.txtCategoryName.TabIndex = 1
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(205, 69)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 23)
        Me.btnCancel.TabIndex = 2
        Me.btnCancel.Text = "Cancel"
        '
        'lblCategoryName
        '
        Me.lblCategoryName.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblCategoryName.Appearance.Options.UseFont = True
        Me.lblCategoryName.Location = New System.Drawing.Point(13, 23)
        Me.lblCategoryName.Name = "lblCategoryName"
        Me.lblCategoryName.Size = New System.Drawing.Size(145, 13)
        Me.lblCategoryName.TabIndex = 3
        Me.lblCategoryName.Text = "Enter the Category Name:"
        '
        'FormNewCategory
        '
        Me.AcceptButton = Me.btnAddCategory
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnCancel
        Me.ClientSize = New System.Drawing.Size(292, 110)
        Me.ControlBox = False
        Me.Controls.Add(Me.lblCategoryName)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.txtCategoryName)
        Me.Controls.Add(Me.btnAddCategory)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FormNewCategory"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Add a Category"
        CType(Me.txtCategoryName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnAddCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtCategoryName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblCategoryName As DevExpress.XtraEditors.LabelControl
End Class

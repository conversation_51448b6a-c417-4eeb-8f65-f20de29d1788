﻿
Imports System.ComponentModel
Imports System.ComponentModel.DataAnnotations
Imports System.Text.RegularExpressions

Public Class EmailOptionsDTO
    Implements IDataErrorInfo, IValidatableObject

    <Display(Name:="SMTP Server")>
    Public Property SmtpServer As String

    <Display(Name:="SMTP Port")>
    Public Property SmtpPort As Integer?

    Public Property Username As String

    Public Property Password As String

    <Display(Name:="Recipient Email(s)")>
    Public Property RecipientEmail As String

    <Display(Name:="Sender Email")>
    Public Property SenderEmail As String

    Public Property UseSsl As Boolean?

    Public Property AuthenticationType As SmtpAuthenticationType?

    Public Sub New()
        ' Initialize with default values
        SmtpPort = 25
        UseSsl = False
        ' AuthenticationType is left as Nothing by default
    End Sub

    Public ReadOnly Property [Error] As String Implements IDataErrorInfo.Error
        Get
            Dim validationContext As New ValidationContext(Me)
            Dim validationResults As New List(Of ValidationResult)()
            If Not Validator.TryValidateObject(Me, validationContext, validationResults, True) Then
                Return String.Join(Environment.NewLine, validationResults.Select(Function(vr) vr.ErrorMessage))
            End If
            Return Nothing
        End Get
    End Property

    Default Public ReadOnly Property Item(columnName As String) As String Implements IDataErrorInfo.Item
        Get
            Select Case columnName
                Case NameOf(SmtpServer)
                    If String.IsNullOrWhiteSpace(SmtpServer) Then
                        Return "SMTP Server is required"
                    ElseIf Not IsValidHostname(SmtpServer) Then
                        Return "Invalid SMTP Server hostname"
                    End If
                Case NameOf(SmtpPort)
                    If Not SmtpPort.HasValue Then
                        Return "SMTP Port is required"
                    ElseIf SmtpPort < 1 OrElse SmtpPort > 65535 Then
                        Return "Port must be between 1 and 65535"
                    End If
                Case NameOf(RecipientEmail)
                    If String.IsNullOrWhiteSpace(RecipientEmail) Then
                        Return "At least one recipient email is required"
                    ElseIf Not IsValidEmailList(RecipientEmail) Then
                        Return "Invalid recipient email format"
                    End If
                Case NameOf(SenderEmail)
                    If String.IsNullOrWhiteSpace(SenderEmail) Then
                        Return "Sender Email is required"
                    ElseIf Not IsValidEmail(SenderEmail) Then
                        Return "Invalid sender email format"
                    End If
                Case NameOf(UseSsl)
                    If Not UseSsl.HasValue Then
                        Return "Use SSL must be set"
                    End If
                Case NameOf(AuthenticationType)
                    If Not AuthenticationType.HasValue Then
                        Return "Authentication Type must be selected"
                    End If
                Case NameOf(Username)
                    If AuthenticationType = SmtpAuthenticationType.NetworkCredentials AndAlso String.IsNullOrEmpty(Username) Then
                        Return "Username is required when using Basic authentication"
                    End If
                Case NameOf(Password)
                    If AuthenticationType = SmtpAuthenticationType.NetworkCredentials AndAlso String.IsNullOrWhiteSpace(Password) Then
                        Return "Password is required when authentication is enabled"
                    End If
            End Select
            Return String.Empty
        End Get
    End Property

    Public Function Validate(validationContext As ValidationContext) As IEnumerable(Of ValidationResult) Implements IValidatableObject.Validate
        Dim results As New List(Of ValidationResult)()

        If AuthenticationType.HasValue AndAlso AuthenticationType.Value = SmtpAuthenticationType.NetworkCredentials Then
            If String.IsNullOrWhiteSpace(Username) Then
                results.Add(New ValidationResult("Username is required when authentication is enabled", New String() {NameOf(Username)}))
            End If
            If String.IsNullOrWhiteSpace(Password) Then
                results.Add(New ValidationResult("Password is required when authentication is enabled", New String() {NameOf(Password)}))
            End If
        End If

        ' Additional complex validation rules can be added here

        Return results
    End Function

    Private Function IsValidHostname(hostname As String) As Boolean
        ' Basic hostname validation
        Return Regex.IsMatch(hostname, "^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$")
    End Function

    Private Function IsValidEmail(email As String) As Boolean
        ' Basic email validation
        Return Regex.IsMatch(email, "^[^@\s]+@[^@\s]+\.[^@\s]+$")
    End Function

    Private Function IsValidEmailList(emails As String) As Boolean
        ' Validate list of emails separated by commas
        Dim emailList = emails.Split(","c)
        Return emailList.All(Function(email) IsValidEmail(email.Trim()))
    End Function

    Public Function ToSecureString() As String
        ' Return a string representation of the object with sensitive data masked
        Return $"SMTP: {SmtpServer}:{SmtpPort}, Sender: {SenderEmail}, SSL: {UseSsl}, Auth: {If(AuthenticationType.HasValue, AuthenticationType.ToString(), "Not Set")}"
    End Function
End Class


Public Class EmailOptionsDTO_Old
    Implements IDataErrorInfo

    <Required(ErrorMessage:="SMTP Server is required")>
    <Display(Name:="SMTP Server")>
    Public Property SmtpServer As String

    <Required(ErrorMessage:="SMTP Port is required")>
    <Range(1, 65535, ErrorMessage:="Port must be between 1 and 65535")>
    <Display(Name:="SMTP Port")>
    Public Property SmtpPort As Integer

    ' <Required(ErrorMessage:="Username is required when using Basic authentication")>
    Public Property Username As String

    ' Password is managed externally, so we remove validation for it
    Public Property Password As String

    <Required(ErrorMessage:="At least one recipient email is required")>
    <Display(Name:="Recipient Email(s)")>
    Public Property RecipientEmail As String

    <Required(ErrorMessage:="Sender Email is required")>
    <EmailAddress(ErrorMessage:="Invalid sender email address format")>
    <Display(Name:="Sender Email")>
    Public Property SenderEmail As String

    <Required(ErrorMessage:="Use Ssl must be set")>
    Public Property UseSsl As Boolean

    <Required>
    Public Property AuthenticationType As SmtpAuthenticationType

    Public ReadOnly Property [Error] As String Implements IDataErrorInfo.Error
        Get
            Return Nothing
        End Get
    End Property

    Default Public ReadOnly Property Item(columnName As String) As String Implements IDataErrorInfo.Item
        Get
            Dim validationResults As New List(Of ValidationResult)()
            Dim validationContext = New ValidationContext(Me, Nothing, Nothing) With {.MemberName = columnName}

            If Not Validator.TryValidateProperty(GetType(EmailOptionsDTO).GetProperty(columnName).GetValue(Me), validationContext, validationResults) Then
                Return String.Join(Environment.NewLine, validationResults.Select(Function(r) r.ErrorMessage))
            End If

            ' Additional custom validations
            Select Case columnName
                Case NameOf(RecipientEmail)
                    Dim invalidEmails = ValidateMultipleEmails(RecipientEmail)
                    If invalidEmails.Any() Then
                        Return $"The following email addresses are invalid: {String.Join(", ", invalidEmails)}"
                    End If
                Case NameOf(SmtpPort)
                    If UseSsl AndAlso SmtpPort = 25 Then
                        Return "Port 25 is typically not used with SSL. Consider using port 465 or 587 for SSL/TLS."
                    End If
                Case NameOf(Username)
                    If AuthenticationType = SmtpAuthenticationType.NetworkCredentials AndAlso String.IsNullOrEmpty(Username) Then
                        Return "Username is required when using Basic authentication"
                    End If
            End Select

            Return Nothing
        End Get
    End Property

    Private Function ValidateMultipleEmails(emails As String) As List(Of String)
        If String.IsNullOrWhiteSpace(emails) Then
            Return New List(Of String) From {"At least one email address is required"}
        End If

        Dim invalidEmails As New List(Of String)
        For Each email In emails.Split(","c)
            Dim trimmedEmail = email.Trim()
            If Not IsValidEmail(trimmedEmail) Then
                invalidEmails.Add(trimmedEmail)
            End If
        Next

        Return invalidEmails
    End Function

    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr = New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function
End Class

Public Enum SmtpAuthenticationType
    AppPoolCredentials
    NetworkCredentials
    Anonymous
End Enum

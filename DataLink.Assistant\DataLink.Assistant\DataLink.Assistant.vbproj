﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>DataLink.Assistant</RootNamespace>
    <!--<Configurations>Release;Debug</Configurations>-->
    <Platforms>AnyCPU</Platforms>
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputType>Library</OutputType>
    <!--<MyType>Windows</MyType>-->
    <!--<RuntimeIdentifier>win</RuntimeIdentifier>-->
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <RunAnalyzersDuringBuild>False</RunAnalyzersDuringBuild>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>DataLink.Assistant.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <!--<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>DataLink.Assistant.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>-->
  <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Update="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <!--<PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.355802">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>-->
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\ENChartCAC\EnchartDOLib\EnchartDOLib.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\DataLink.Paragon.Data\DataLink.Paragon.Data.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\DataLink.Paragon\DataLink.Paragon.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\DataLink\DataLink\DataLink.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\McKessonIntelligentCoding.Common.Contracts\McKessonIntelligentCoding.Common.Contracts.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\McKessonIntelligentCoding.Common.Logging\McKessonIntelligentCoding.Common.Logging.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\McKessonIntelligentCoding.Data.Repository\McKessonIntelligentCoding.Data.Repository.vbproj" />
    <ProjectReference Include="..\..\McKessonIntelligentCoding\VIC.ChartService\VIC.ChartService.vbproj" />
  </ItemGroup>
  <Import Project="..\..\build\CustomPostBuild.targets" />
</Project>
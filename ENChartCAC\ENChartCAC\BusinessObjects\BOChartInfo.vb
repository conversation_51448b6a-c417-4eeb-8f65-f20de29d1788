﻿Imports DevExpress.Xpo
Imports EnchartDOLib

'Public Class BOMiscDictionary
'    Public Property Name As String
'    Public dic As Dictionary(Of String, Object)
'End Class


Public Class BOChartInfo
    Inherits BusinessObject

    Public Property MiscDic As Dictionary(Of String, Object)


    Public Property Locked As Boolean
    Public Property LockStatus As String
    Public Property LockUser As Integer 'BOUser
    Public Property LockDate As Date
    Public Property MachineName As String
    Public Property Facility As Integer  'BOFacility
    Public Property VisitID As String
    Public Property ChartVersion As Integer
    Public Property Chart As BOChart
    Public Property IsSeries As Boolean
    Public Property CreationDate As Date
    Public Property ExportStatus As String
    Public Property ExportedChart As BOChart
    Public Property AdtOutOfSync As Boolean
    Public Property ExportStatusChanged As Date
    Public Property PhysExportStatusChanged As Date
    Public Property PhysExportStatus As String
    Public Property PhysExportChart As BOChart
    Public Property CFExportFacilityChartVersion As BOChart
    Public Property CFExportFacilityExportedDate As Date
    Public Property CFExportFacilityStatus As String
    Public Property CFExportInclude As Boolean
    Public Property CFExportPhysicianChartVersion As BOChart
    Public Property CFExportPhysicianExportedDate As Date
    Public Property CFExportPhysicianStatus As String
    Public Property CFExportUserRequestPending As Boolean
    Public Property CFExportObservationChartVersion As BOChart
    Public Property CFExportObservationExportedDate As Date
    Public Property CFExportObservationStatus As String
    Public Property ObsExportChart As BOChart
    Public Property ObsExportStatus As String
    Public Property ObsExportStatusChanged As Date
    Public Property UserHasChangedEdData As Boolean
    Public Property UserHasChangedObsData As Boolean
    Public Property UserHasChangedPhysData As Boolean
    Public Property ChartType As String
    Public Property UsesPoints As Boolean

    Public Property IdChanged() As Boolean

    Public Shared Function UpdateDOFromBO(doCI As DOChartInfo, boCI As BOChartInfo) As DOChartInfo
        If boCI Is Nothing Then Return Nothing

        doCI.Locked = boCI.Locked
        doCI.LockStatus = boCI.LockStatus
        doCI.LockUser = XpoDefault.Session.GetObjectByKey(Of DOUser)(boCI.LockUser)
        doCI.LockDate = boCI.LockDate
        doCI.MachineName = boCI.MachineName
        doCI.Facility = XpoDefault.Session.GetObjectByKey(Of DOFacility)(boCI.Facility)
        doCI.VisitID = boCI.VisitID
        'doCI.ChartVersion = boCI.ChartVersion
        'If boCI.Chart IsNot Nothing Then
        '    doCI.Chart = boCI.Chart

        '    ' There is code that autoincrements the chartversion when the chart is being set and is not 
        '    ' loaded from the database. So we need to reset it to the passed in version
        '    doCI.Chart.Version = boCI.Chart.Version
        '    doCI.ChartVersion = boCI.Chart.Version
        'End If

        doCI.IsSeries = boCI.IsSeries
        doCI.CreationDate = boCI.CreationDate
        doCI.AdtOutOfSync = boCI.AdtOutOfSync
        doCI.CFExportFacilityChartVersion = boCI.CFExportFacilityChartVersion
        doCI.CFExportFacilityExportedDate = boCI.CFExportFacilityExportedDate
        doCI.CFExportFacilityStatus = boCI.CFExportFacilityStatus
        doCI.CFExportInclude = boCI.CFExportInclude
        doCI.CFExportPhysicianChartVersion = boCI.CFExportPhysicianChartVersion
        doCI.CFExportPhysicianExportedDate = boCI.CFExportPhysicianExportedDate
        doCI.CFExportPhysicianStatus = boCI.CFExportPhysicianStatus
        doCI.CFExportUserRequestPending = boCI.CFExportUserRequestPending
        doCI.CFExportObservationChartVersion = boCI.CFExportObservationChartVersion
        doCI.CFExportObservationExportedDate = boCI.CFExportObservationExportedDate
        doCI.CFExportObservationStatus = boCI.CFExportObservationStatus

        'There is code in the ExportStatus properties that sets other stuff so we only want to set it if the value
        'that is being return is not null and different from what is currently there
        If Not String.IsNullOrEmpty(boCI.ExportStatus) AndAlso boCI.ExportStatus <> doCI.ExportStatus Then
            doCI.ExportStatus = boCI.ExportStatus
        End If

        If Not String.IsNullOrEmpty(boCI.PhysExportStatus) AndAlso boCI.PhysExportStatus <> doCI.PhysExportStatus Then
            doCI.PhysExportStatus = boCI.PhysExportStatus
        End If

        If Not String.IsNullOrEmpty(boCI.ObsExportStatus) AndAlso boCI.ObsExportStatus <> doCI.ObsExportStatus Then
            doCI.ObsExportStatus = boCI.ObsExportStatus
        End If

        doCI.UserHasChangedEdData = boCI.UserHasChangedEdData
        doCI.UserHasChangedObsData = boCI.UserHasChangedObsData
        doCI.UserHasChangedPhysData = boCI.UserHasChangedPhysData
        doCI.ChartType = boCI.ChartType
        doCI.UsesPoints = boCI.UsesPoints
        Return doCI
    End Function

    Public Shared Widening Operator CType(ByVal boCI As BOChartInfo) As DOChartInfo
        If boCI Is Nothing Then Return Nothing
        Dim doCI As DOChartInfo
        If boCI.Oid > NEW_OBJ_OID Then
            doCI = XpoDefault.Session.GetObjectByKey(Of DOChartInfo)(boCI.Oid)
        Else
            doCI = New DOChartInfo()
        End If
        doCI.Locked = boCI.Locked
        doCI.LockStatus = boCI.LockStatus
        doCI.LockUser = XpoDefault.Session.GetObjectByKey(Of DOUser)(boCI.LockUser)
        doCI.LockDate = boCI.LockDate
        doCI.MachineName = boCI.MachineName
        doCI.Facility = XpoDefault.Session.GetObjectByKey(Of DOFacility)(boCI.Facility)
        doCI.VisitID = boCI.VisitID
        doCI.ChartVersion = boCI.ChartVersion
        If boCI.Chart IsNot Nothing Then
            doCI.Chart = boCI.Chart

            ' There is code that autoincrements the chartversion when the chart is being set and is not 
            ' loaded from the database. So we need to reset it to the passed in version
            doCI.Chart.Version = boCI.Chart.Version
            doCI.ChartVersion = boCI.Chart.Version
        End If

        doCI.IsSeries = boCI.IsSeries
        doCI.CreationDate = boCI.CreationDate
        doCI.AdtOutOfSync = boCI.AdtOutOfSync
        doCI.CFExportFacilityChartVersion = boCI.CFExportFacilityChartVersion
        doCI.CFExportFacilityExportedDate = boCI.CFExportFacilityExportedDate
        doCI.CFExportFacilityStatus = boCI.CFExportFacilityStatus
        doCI.CFExportInclude = boCI.CFExportInclude
        doCI.CFExportPhysicianChartVersion = boCI.CFExportPhysicianChartVersion
        doCI.CFExportPhysicianExportedDate = boCI.CFExportPhysicianExportedDate
        doCI.CFExportPhysicianStatus = boCI.CFExportPhysicianStatus
        doCI.CFExportUserRequestPending = boCI.CFExportUserRequestPending
        doCI.CFExportObservationChartVersion = boCI.CFExportObservationChartVersion
        doCI.CFExportObservationExportedDate = boCI.CFExportObservationExportedDate
        doCI.CFExportObservationStatus = boCI.CFExportObservationStatus

        doCI.ExportedChart = boCI.ExportedChart
        doCI.ExportStatusChanged = boCI.ExportStatusChanged

        doCI.PhysExportStatusChanged = boCI.PhysExportStatusChanged
        doCI.PhysExportChart = boCI.PhysExportChart

        doCI.ObsExportChart = boCI.ObsExportChart
        doCI.ObsExportStatusChanged = boCI.ObsExportStatusChanged

        'There is code in the ExportStatus properties that sets other stuff so we only want to set it if the value
        'that is being return is not null and different from what is currently there
        If Not String.IsNullOrEmpty(boCI.ExportStatus) AndAlso boCI.ExportStatus <> doCI.ExportStatus Then
            doCI.ExportStatus = boCI.ExportStatus
        End If

        If Not String.IsNullOrEmpty(boCI.PhysExportStatus) AndAlso boCI.PhysExportStatus <> doCI.PhysExportStatus Then
            doCI.PhysExportStatus = boCI.PhysExportStatus
        End If

        If Not String.IsNullOrEmpty(boCI.ObsExportStatus) AndAlso boCI.ObsExportStatus <> doCI.ObsExportStatus Then
            doCI.ObsExportStatus = boCI.ObsExportStatus
        End If

        doCI.UserHasChangedEdData = boCI.UserHasChangedEdData
        doCI.UserHasChangedObsData = boCI.UserHasChangedObsData
        doCI.UserHasChangedPhysData = boCI.UserHasChangedPhysData

        doCI.ChartType = boCI.ChartType
        doCI.UsesPoints = boCI.UsesPoints
        Return doCI
    End Operator

    Public Shared Narrowing Operator CType(ByVal doCI As DOChartInfo) As BOChartInfo
        If doCI Is Nothing Then Return Nothing
        Dim boCI As New BOChartInfo

        boCI.Oid = doCI.Oid
        boCI.Locked = doCI.Locked
        boCI.LockStatus = doCI.LockStatus
        boCI.LockUser = If(doCI.LockUser IsNot Nothing, doCI.LockUser.Oid, NEW_OBJ_OID)
        boCI.LockDate = doCI.LockDate
        boCI.MachineName = doCI.MachineName
        boCI.Facility = If(doCI.Facility IsNot Nothing, doCI.Facility.Oid, NEW_OBJ_OID)
        boCI.VisitID = doCI.VisitID
        boCI.ChartVersion = doCI.ChartVersion
        boCI.Chart = doCI.Chart

        boCI.IsSeries = doCI.IsSeries
        boCI.CreationDate = doCI.CreationDate
        boCI.ExportStatus = doCI.ExportStatus
        boCI.ExportedChart = doCI.ExportedChart
        boCI.AdtOutOfSync = doCI.AdtOutOfSync
        boCI.ExportStatusChanged = doCI.ExportStatusChanged
        boCI.PhysExportStatusChanged = doCI.PhysExportStatusChanged
        boCI.PhysExportStatus = doCI.PhysExportStatus
        boCI.PhysExportChart = doCI.PhysExportChart
        boCI.CFExportFacilityChartVersion = doCI.CFExportFacilityChartVersion
        boCI.CFExportFacilityExportedDate = doCI.CFExportFacilityExportedDate
        boCI.CFExportFacilityStatus = doCI.CFExportFacilityStatus
        boCI.CFExportInclude = doCI.CFExportInclude
        boCI.CFExportPhysicianChartVersion = doCI.CFExportPhysicianChartVersion
        boCI.CFExportPhysicianExportedDate = doCI.CFExportPhysicianExportedDate
        boCI.CFExportPhysicianStatus = doCI.CFExportPhysicianStatus
        boCI.CFExportUserRequestPending = doCI.CFExportUserRequestPending
        boCI.CFExportObservationChartVersion = doCI.CFExportObservationChartVersion
        boCI.CFExportObservationExportedDate = doCI.CFExportObservationExportedDate
        boCI.CFExportObservationStatus = doCI.CFExportObservationStatus
        boCI.ObsExportChart = doCI.ObsExportChart
        boCI.ObsExportStatus = doCI.ObsExportStatus
        boCI.ObsExportStatusChanged = doCI.ObsExportStatusChanged
        boCI.UserHasChangedEdData = doCI.UserHasChangedEdData
        boCI.UserHasChangedObsData = doCI.UserHasChangedObsData
        boCI.UserHasChangedPhysData = doCI.UserHasChangedPhysData
        boCI.ChartType = doCI.ChartType
        boCI.UsesPoints = doCI.UsesPoints
        Return boCI
    End Operator
End Class

﻿Imports System.Text
Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraGrid.Views.Grid
Imports EnchartDOLib
Imports System.IO
Imports System.Xml.Serialization
Imports System.Reflection
Imports System.Linq

'Imports System.Web.Script.Serialization

Public Class Form1

    Private Const CUSTOM_RANGE_STR = "Custom Range"
    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        EnchartDOLib.Utils.ConnectToECDataBase()
        'ConnectToECDataBaseOrEss()

        InitDayLookUpDict()

        quickDate.Properties.Items.Clear()
        quickDate.Properties.Items.AddRange(DayLookUpDict.Keys)
        quickDate.Properties.Items.Add(CUSTOM_RANGE_STR)
        quickDate.EditValue = quickDate.Properties.Items(0)

        '       Dim users = DOUser.GetAllUsersAsList()
        'userIdCbo.Properties.Items.Add("")
        'For Each user In users
        '    userIdCbo.Properties.Items.Add(user.UserID)
        'Next

        GridView1.OptionsView.RowAutoHeight = True
        'GridView1.OptionsFind.AlwaysVisible = True


        quickDate.EditValue = "24 Hours"
        GetDataWithFilter()
        'GridView1.BestFitColumns()

        Dim filePath = Path.Combine(GetExecutingDirectoryName(), ExportFileName)
        teLoadFromFile.Text = filePath

    End Sub

    Private Sub DoRefresh()
        XpCollection2.Reload()
        GridControl1.RefreshDataSource()
    End Sub

    Private DayLookUpDict As New Dictionary(Of String, Short)

    Private Sub InitDayLookUpDict()
        DayLookUpDict("24 Hours") = -1
        DayLookUpDict("2 Days") = -2
        DayLookUpDict("3 Days") = -3
        DayLookUpDict("4 Days") = -4
        DayLookUpDict("5 Days") = -5
        DayLookUpDict("6 Days") = -6
        DayLookUpDict("1 Week") = -7
        DayLookUpDict("2 Week") = -14
        DayLookUpDict("3 Week") = -21
        DayLookUpDict("1 Month") = -1
        DayLookUpDict("6 Month") = -6
        DayLookUpDict("1 Year") = -1
        DayLookUpDict("2 Years") = -2
        'DayLookUpDict("3 Years") = -3
        'DayLookUpDict("4 Years") = -4
        'DayLookUpDict("5 Years") = -5
        'DayLookUpDict("10 Years") = -10
    End Sub

    Private Function GetFilterWithParamQuery() As CriteriaOperator
        Dim CritOps As New List(Of CriteriaOperator)
        Dim critOpMain As CriteriaOperator
        Dim selectQuery As New StringBuilder

        selectQuery.Append(String.Format("(GCRecord is Null) "))

        'Dim useSimpleTransactionDate As Boolean = True
        'If Not String.IsNullOrEmpty(actionCbo.EditValue) Then
        '    selectQuery.Append(String.Format("and ActionDescription = '{0}'", actionCbo.EditValue))
        'End If

        'If Not String.IsNullOrEmpty(encounterTextEdit.EditValue) Then
        '    selectQuery.Append(String.Format("and VisitId = '{0}'", encounterTextEdit.EditValue))
        'End If

        'If Not String.IsNullOrEmpty(userIdCbo.EditValue) Then
        '    selectQuery.Append(String.Format("and UserID = '{0}'", userIdCbo.EditValue))
        'End If

        critOpMain = CriteriaOperator.Parse(selectQuery.ToString())

        If fromDate.EditValue IsNot Nothing Then
            Dim op As CriteriaOperator = New BinaryOperator("DateTime", fromDate.EditValue, BinaryOperatorType.GreaterOrEqual)
            critOpMain = CriteriaOperator.And(critOpMain, op)
        End If

        If toDate.EditValue IsNot Nothing Then
            Dim op As CriteriaOperator = New BinaryOperator("DateTime", toDate.EditValue, BinaryOperatorType.LessOrEqual)
            critOpMain = CriteriaOperator.And(critOpMain, op)
        End If

        If chkVerbose.Checked = False Then
            Dim opVerbose As CriteriaOperator = New BinaryOperator("MsgType", "Verbose", BinaryOperatorType.NotEqual)
            critOpMain = CriteriaOperator.And(critOpMain, opVerbose)

            Dim opTrace As CriteriaOperator = New BinaryOperator("MsgType", "Trace", BinaryOperatorType.NotEqual)
            critOpMain = CriteriaOperator.And(critOpMain, opTrace)

            Dim opDebug As CriteriaOperator = New BinaryOperator("MsgType", "Debug", BinaryOperatorType.NotEqual)
            critOpMain = CriteriaOperator.And(critOpMain, opDebug)
        End If

        If chkInformation.Checked = False Then
            Dim opInfo As CriteriaOperator = New BinaryOperator("MsgType", "Information", BinaryOperatorType.NotEqual)
            critOpMain = CriteriaOperator.And(critOpMain, opInfo)
        End If


        Return critOpMain
    End Function

    Private Function BuildFromDate(ByVal choice As String) As Nullable(Of DateTime)
        BuildFromDate = Nothing
        If String.IsNullOrEmpty(choice) Then Return BuildFromDate

        If choice.ToUpper.Contains("DAY") OrElse choice.ToUpper.Contains("WEEK") OrElse choice.ToUpper.Contains("HOURS") Then
            'Debug.Assert(Me.DayLookUpDict.ContainsKey(choice))
            Dim ldateTimeModifier As Integer = Me.DayLookUpDict(choice)
            BuildFromDate = DateAdd(DateInterval.Day, ldateTimeModifier, Now)
        ElseIf choice.ToUpper.Contains("MONTH") Then
            Debug.Assert(Me.DayLookUpDict.ContainsKey(choice))
            Dim ldateTimeModifier As Integer = Me.DayLookUpDict(choice)
            BuildFromDate = DateAdd(DateInterval.Month, ldateTimeModifier, Now)
        Else
            'Debug.Assert(Me.DayLookUpDict.ContainsKey(choice))
            Dim ldateTimeModifier As Integer = Me.DayLookUpDict(choice)
            BuildFromDate = DateAdd(DateInterval.Year, ldateTimeModifier, Now)
        End If

        Return BuildFromDate
    End Function

    Private Sub GetDataButton_Click(sender As Object, e As EventArgs) Handles btnGetDataButton.Click
        GetDataWithFilter()
    End Sub

    Private errorLogRecsDO As XPCollection(Of DOErrorLog)
    Private errorLogRecsBO As List(Of BOErrorLog)
    Private Sub GetDataWithFilter()
        If twLoadFromFile.IsOn = False Then
            errorLogRecsDO = New XPCollection(Of DOErrorLog)(GetFilterWithParamQuery())

            Dim sortCollection As SortingCollection = New SortingCollection()
            sortCollection.Add(New SortProperty("DateTime", SortingDirection.Descending))
            errorLogRecsDO.Sorting = sortCollection
            errorLogRecsBO = GetSerializableLogRecs(errorLogRecsDO)
        Else
            errorLogRecsBO = LoadFromFile()
        End If


        GridControl1.DataSource = errorLogRecsBO
    End Sub

    Private Sub QuickDate_SelectedIndexChanged(sender As Object, e As EventArgs) Handles quickDate.SelectedIndexChanged
        If quickDate.EditValue = CUSTOM_RANGE_STR Then
            fromDate.Enabled = True
            toDate.Enabled = True
            Return
        Else
            fromDate.Enabled = False
            toDate.Enabled = False
            toDate.EditValue = Nothing
        End If

        Dim fDateTime As DateTime? = BuildFromDate(quickDate.EditValue)
        fromDate.EditValue = fDateTime

        GetDataWithFilter()

    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        ' Create a PrintingSystem component.
        Dim ps As New PrintingSystem()
        ' Create a link that will print a control.
        Dim link As New PrintableComponentLink(ps)
        ' Specify the control to be printed.
        link.Component = Me.GridControl1
        ' Subscribe to the CreateReportHeaderArea event used to generate the report header.
        ' AddHandler link.CreateReportHeaderArea, AddressOf PrintableComponentLink1_CreateReportHeaderArea
        ' Generate the report.
        link.Landscape = True
        link.CreateDocument()
        ' Show the report.
        link.ShowPreview()
    End Sub

    Private Sub chkInformation_CheckedChanged(sender As Object, e As EventArgs) Handles chkInformation.CheckedChanged
        '    GetDataWithFilter()
    End Sub

    Private Sub chkVerbose_CheckedChanged(sender As Object, e As EventArgs) Handles chkVerbose.CheckedChanged
        '  GetDataWithFilter()
    End Sub

    Private Sub btnReset_Click(sender As Object, e As EventArgs) Handles btnAllowEdit.Click
        '    MessageBox.Show("Oh no you didn't!")
        GridView1.OptionsBehavior.Editable = Not GridView1.OptionsBehavior.Editable
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Application.Exit()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        GridView1.BestFitColumns()
    End Sub

    'Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
    '    GridView1.ColumnsCustomization()
    'End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs) Handles GridView1.RowStyle
        Dim View As GridView = sender
        If (e.RowHandle >= 0) Then
            Dim message As String = View.GetRowCellDisplayText(e.RowHandle, View.Columns("Message"))
            Dim msgType As String = View.GetRowCellDisplayText(e.RowHandle, View.Columns("MsgType"))

            If message.StartsWith("----- Begin Processing Chart -----") Then
                e.Appearance.BackColor = Color.LightBlue

                e.Appearance.Font = New Font(e.Appearance.Font, FontStyle.Bold)

            ElseIf msgType = "Error" Then

                e.Appearance.ForeColor = Color.Red

            ElseIf msgType = "Information" Then
                'e.Appearance.BackColor = Color.LightSalmon
                'e.Appearance.BackColor2 = Color.MistyRose
                e.Appearance.ForeColor = Color.Green
            ElseIf msgType = "Critical" Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.BackColor2 = Color.White
                'e.Appearance.ForeColor = Color.Red
            End If


        End If

    End Sub

    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        Dim View As GridView = sender
        If e.Column.FieldName = "MsgType" Then
            Dim msgType As String = View.GetRowCellDisplayText(e.RowHandle, View.Columns("MsgType"))
            If msgType = "Error" Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.BackColor2 = Color.White
                e.Appearance.ForeColor = Color.Black
            ElseIf msgType = "Critical" Then
                ' e.Appearance.BackColor = Color.Red
            ElseIf msgType = "Information" Then
                e.Appearance.BackColor = Color.Green
                e.Appearance.BackColor2 = Color.White
                e.Appearance.ForeColor = Color.Black
            End If
        End If

    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        ExportToFileAndClipboard(useFilteredSelction:=True)
    End Sub

    Private Sub btnQuickExport_Click(sender As Object, e As EventArgs) Handles btnQuickExport.Click
        ExportToFileAndClipboard(useFilteredSelction:=False)
    End Sub

    Private Const ExportFileName = "DOErrorlog.xml"
    Private Sub ExportToFileAndClipboard(useFilteredSelction As Boolean)


        Dim logRecsToExport As List(Of BOErrorLog) = Nothing
        If useFilteredSelction Then
            logRecsToExport = GetSerializableLogRecs(errorLogRecsDO)
        Else
            Dim logRecsXPList = From errorLog In New XPQuery(Of DOErrorLog)(XpoDefault.Session)
                                Where errorLog.DateTime < Now.AddMonths(-1)
                                Select errorLog
            logRecsToExport = New List(Of BOErrorLog)
            For Each recItem In logRecsXPList
                logRecsToExport.Add(New BOErrorLog(recItem))
            Next

            logRecsToExport = GetSerializableLogRecs(logRecsXPList)
        End If


        Dim filePath = Path.Combine(GetExecutingDirectoryName(), ExportFileName)
        WriteToXmlFile(filePath, logRecsToExport)

        Dim f() As String = {filePath}

        Dim d As New DataObject(DataFormats.FileDrop, f)
        Clipboard.SetDataObject(d, True)

        MessageBox.Show($"Errorlog successly exported to: {Environment.NewLine}{Environment.NewLine}{filePath}{Environment.NewLine}{Environment.NewLine}(File also placed on clipboard) ", "Export Successful", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function GetSerializableLogRecs(listOfDOs As IEnumerable(Of DOErrorLog)) As List(Of BOErrorLog)
        Dim returnList As New List(Of BOErrorLog)
        For Each recItem In listOfDOs
            returnList.Add(New BOErrorLog(recItem))
        Next
        Return returnList
    End Function

    Public Shared Sub WriteToXmlFile(Of T As New)(ByVal filePath As String, ByVal objectToWrite As T, ByVal Optional append As Boolean = False)
        Dim writer As TextWriter = Nothing

        Try
            Dim serializer = New XmlSerializer(GetType(T))
            writer = New StreamWriter(filePath, append)
            serializer.Serialize(writer, objectToWrite)
        Finally
            If writer IsNot Nothing Then writer.Close()
        End Try
    End Sub

    Public Shared Function ReadFromXmlFile(Of T As New)(ByVal filePath As String) As T
        Dim reader As TextReader = Nothing

        Try
            Dim serializer = New XmlSerializer(GetType(T))
            reader = New StreamReader(filePath)
            Return CType(serializer.Deserialize(reader), T)
        Finally
            If reader IsNot Nothing Then reader.Close()
        End Try
    End Function

    Public Shared Function GetExecutingDirectoryName() As String
        Dim location = Assembly.GetEntryAssembly().Location
        Return New FileInfo(location).Directory.FullName
    End Function


    'Public Shared Function GetExecutingDirectoryName() As String
    '    Dim location = New Uri(Assembly.GetEntryAssembly().GetName().CodeBase)
    '    Return New FileInfo(location.AbsolutePath).Directory.FullName
    'End Function

    Private Sub btnLoadFromFile_Click(sender As Object, e As EventArgs) Handles btnLoadFromFile.Click
        errorLogRecsBO = LoadFromFile()
        GridControl1.DataSource = errorLogRecsBO

    End Sub

    Public Logs As List(Of BOErrorLog)
    Dim tLog As List(Of BOErrorLog)
    Private Function LoadFromFile() As List(Of BOErrorLog)
        Dim filePath = Path.Combine(GetExecutingDirectoryName(), ExportFileName)

        Logs = ReadFromXmlFile(Of List(Of BOErrorLog))(filePath)

        Dim sorted = From x In Logs Select x Order By x.DateTime
        'tLog = New List(Of BOErrorLog)
        tLog = sorted.ToList()

        ''Detective work
        'Dim addRec As Boolean = False
        'Dim lastStartUpTime As Date = #01/01/2001#
        'Dim lastCriticalError As Nullable(Of Date) '= #01/01/2001#

        'For Each rec In sorted.ToList
        '    Dim timeSinceServerStart As TimeSpan = rec.DateTime - lastStartUpTime
        '    Dim timeSinceLastCriticalError As TimeSpan '= TimeSpan.FromSeconds(60) '= rec.DateTime - lastCriticalError

        '    If lastCriticalError.HasValue Then
        '        timeSinceLastCriticalError = rec.DateTime - lastCriticalError.Value
        '    Else
        '        timeSinceLastCriticalError = TimeSpan.FromSeconds(60)
        '    End If

        '    If rec.Message.ToLower.Contains("server startup setup completed") Then
        '        addRec = True
        '        lastStartUpTime = rec.DateTime
        '    End If

        '    If timeSinceServerStart.TotalSeconds < 10 Then
        '        'If rec.WindowsUserName?.ToLower.Contains("smitht5") Then addRec = True
        '        ' If rec.WindowsUserName?.ToLower.Contains("rachumr") Then addRec = True
        '        'If rec.WindowsUserName?.ToLower.Contains("marcota") Then addRec = True
        '        If rec.WindowsUserName?.ToLower.Contains("swiftl") Then addRec = True
        '        'swiftl


        '        Dim words = {"error", "critical, warning"}
        '        If words.Any(Function(w) rec.MsgType.ToLower.Contains(w)) Then addRec = True
        '        If rec.MsgType.ToLower.Contains("error") Or rec.MsgType.ToLower.Contains("warning") Then addRec = True

        '        If rec.MsgType.ToLower.Contains("critical") Then
        '            lastCriticalError = rec.DateTime
        '            addRec = True
        '        End If
        '    End If

        'If timeSinceLastCriticalError.TotalSeconds < 20 Then
        '        addRec = True
        '    Else
        '        addRec = addRec
        '    End If

        '    If rec.WindowsUserName?.ToLower.Contains("shaikk") Then addRec = False
        '    If addRec Then
        '        tLog.Add(rec)
        '    End If
        '    addRec = False
        'Next


        Return tLog
    End Function

    Private Sub twLoadFromFile_Toggled(sender As Object, e As EventArgs) Handles twLoadFromFile.Toggled
        If twLoadFromFile.IsOn Then
            teLoadFromFile.Enabled = True
            btnLoadFromFile.Enabled = True
            LabelControl2.ForeColor = Color.Green
        Else
            teLoadFromFile.Enabled = False
            btnLoadFromFile.Enabled = False
            LabelControl2.ResetForeColor()
        End If
        'GetDataWithFilter()
    End Sub

    Private Sub btnToggleWindowSize_Click(sender As Object, e As EventArgs) Handles btnToggleWindowSize.Click
        Me.Width = 2000
        Me.Height = 1000
        CenterToScreen()
    End Sub
End Class

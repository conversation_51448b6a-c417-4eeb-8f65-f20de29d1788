Imports System
Imports refit
Imports System.IO

Module Program
    Sub Main(args As String())

        Dim wh As New WorkstationLogHelper()
        wh.UploadLogToServer()

        Console.WriteLine("Hello World!")
    End Sub

    Public Class WorkstationLogHelper
        Public Class WorkStationDTO
            Public FileName As String
            Public Lines As String()
        End Class

        Interface IAdminController
            <[Get]("/shutdown")>
            Function Shutdown() As System.Threading.Tasks.Task(Of String)
            <Post("/UploadWorkstationLog")>
            Function UploadWorkstationLog(ByVal dTO As WorkStationDTO) As Task(Of Boolean)
        End Interface

        Public Sub UploadLogToServer()
            Try
                Dim di As IO.DirectoryInfo = New IO.DirectoryInfo(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AIC\Logs"))
                Dim fileDic As Dictionary(Of String, String()) = New Dictionary(Of String, String())()

                For Each fi In di.GetFiles()
                    Dim lines = File.ReadAllLines(fi.FullName)
                    If lines IsNot Nothing Then fileDic(fi.FullName) = lines
                    If UploadToEss(fi.FullName, lines) Then fi.Delete()
                Next

            Catch ex As Exception
            End Try
        End Sub

        Public Sub shutdown()
            Dim suffix = "api/Admin"
            Dim url = $"https://localhost:44399/{suffix}"
            Dim ac = Refit.RestService.[For](Of IAdminController)(url)
            Dim blah = ac.Shutdown().Result
        End Sub

        Private Function UploadToEss(ByVal logFileName As String, ByVal lines As String()) As Boolean
            Dim suffix = "api/Admin"
            Dim url = $"https://localhost:44399/{suffix}"
            Dim ac = Refit.RestService.[For](Of IAdminController)(url)
            Dim wsd As WorkStationDTO = New WorkStationDTO() With {
            .FileName = logFileName,
            .Lines = lines
        }
            Dim result = ac.UploadWorkstationLog(wsd).Result
            Return True
        End Function
    End Class

End Module

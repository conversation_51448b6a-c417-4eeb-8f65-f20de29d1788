﻿Public Class DBSchema
    Property TableCount As Integer

    Public Class FieldInfo
        Public Sub New()
            'needed for xml serialization ...
        End Sub
        Public Sub New(name As String, type As String, length As Integer)
            FieldName = name
            FieldType = type
            FieldLength = length
        End Sub
        Property FieldName As String
        Property FieldType As String
        Property FieldTypeName As String
        'Property FieldLength As Integer
        Private _FieldLength As Integer
        Public Property FieldLength() As Integer
            Get
                'If FieldTypeName = "nvarchar" Then
                '    Return _FieldLength / 2
                'End If
                Return _FieldLength
            End Get
            Set(ByVal value As Integer)
                _FieldLength = value
            End Set
        End Property

        Property Nullable As Boolean

        Public Function GetDBAdjustedFieldLength() As Integer
            If FieldTypeName = "nvarchar" Then
                Return _FieldLength / 2
            End If
            Return _FieldLength
        End Function
    End Class

    Public Class TableInfo
        Property TableName As String
        Property FieldCount As Integer
        Property fields As New List(Of FieldInfo)


    End Class

    Public Property TableList As New List(Of TableInfo)

End Class

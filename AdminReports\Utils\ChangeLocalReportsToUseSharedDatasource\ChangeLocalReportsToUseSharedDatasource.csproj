﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{95FAD441-3750-4667-AB71-234AF94C6779}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>ChangeLocalReportsToUseSharedDatasource</RootNamespace>
    <AssemblyName>ChangeLocalReportsToUseSharedDatasource</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>2</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>3ABF161FA5328A2E87193FEF95843403E07C1F4C</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>ConsoleApp3_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Web References\ReportService2010WebRef\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="ConsoleApp3_TemporaryKey.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Web References\ReportService2010WebRef\ActiveState1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\CacheRefreshPlan1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\CacheRefreshPlanState1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\CatalogItem1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\DataRetrievalPlan1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\DataSetDefinition1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\DataSource1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\DataSourceDefinition1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\DataSourcePrompt1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Event1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ExpirationDefinition1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Extension1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ExtensionParameter1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ExtensionSettings1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ItemHistorySnapshot1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ItemParameter1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ItemReferenceData1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Job1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ModelCatalogItem1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ModelDrillthroughReport1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ModelItem1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ParameterValue1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ParameterValueOrFieldReference1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Policy1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Property1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\ReportService2010WebRef\reportservice2010.wsdl" />
    <None Include="Web References\ReportService2010WebRef\Role1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Schedule1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\ScheduleDefinitionOrReference1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Subscription1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Task1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ReportService2010WebRef\Warning1.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://localhost/reportserver/reportservice2010.asmx">
      <UrlBehavior>Static</UrlBehavior>
      <RelPath>Web References\ReportService2010WebRef\</RelPath>
      <UpdateFromURL>http://localhost/reportserver/reportservice2010.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>ConsoleApp3_ReportService2010WebRef_ReportingService2010</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.61">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.61 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.1</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
﻿Imports EnchartDOLib
Imports System.IO
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB

Module ExportSchema

    Sub Main()
        Try
            Utils.ConnectToECDataBase()

            Dim connString = XpoDefault.DataLayer?.Connection?.ConnectionString
            If connString.ToUpper.Contains("INITIAL") Then
                'in case the latest version of dolib is being used ...
                Console.WriteLine("Error: Please change the connection string in the ENChartCAC.exe.config file to point to a Sybase server!")
                Console.WriteLine("Press any key to continue ...")
                Console.ReadKey()
                Return
            End If

            Dim dbInfo As New DBSchema
            Dim tableCount As Integer = 0
            For Each tableName In GetSybaseTableNamesAsList()
                Dim ti As New DBSchema.TableInfo
                ti.TableName = tableName
                Dim fieldcount As Integer = 0
                For Each field In GetFieldListFromSybase(tableName)
                    ti.fields.Add(field)
                    fieldcount += 1
                Next
                ti.FieldCount = fieldcount
                dbInfo.TableList.Add(ti)
                tableCount += 1
            Next
            dbInfo.TableCount = tableCount
            SaveToDisk(dbInfo)
        Catch ex As Exception
            Console.WriteLine("Error - {0}", ex.Message)
            Console.WriteLine("Press any key to continue ...")
            Console.ReadKey()
            Return
        End Try

        Console.WriteLine("{0} File successfully created", GetTableName())
        Console.WriteLine("Press any key to continue ...")
        Console.ReadKey()
    End Sub

    Sub SaveToDisk(dbInfo As DBSchema)
        Dim fs As New FileStream(GetTableName, FileMode.Create)
        Dim x As New Xml.Serialization.XmlSerializer(GetType(DBSchema))

        x.Serialize(fs, dbInfo)
        fs.Flush()
        fs.Close()
    End Sub

    Function GetTableName() As String
        Return "DbSchemaForSybase.xml"
    End Function

    Public Function GetSybaseTableNamesAsList() As List(Of String)
        Dim tableNamesList As New List(Of String)

        Using cmd As IDbCommand = XpoDefault.DataLayer.Connection.CreateCommand()
            '  cmd.CommandText = String.Format("exec sp_tables '%', 'dba', '%', ""'TABLE'""")
            cmd.CommandText = "select table_name from systable where creator = 1 order by table_name"
            Using reader = cmd.ExecuteReader
                While reader.Read

                    Dim tableNameField As String = reader("table_name")
                    tableNamesList.Add(tableNameField)
                End While
            End Using
        End Using

        Return tableNamesList
    End Function

    Public Function GetFieldListFromSybase(tableName As String) As List(Of DBSchema.FieldInfo)

        Dim fieldList As New List(Of DBSchema.FieldInfo)

        Using cmd As IDbCommand = XpoDefault.DataLayer.Connection.CreateCommand()
            'cmd.CommandText = "exec sp_columns " & tableName
            ' cmd.CommandText = String.Format("select sc.* from syscolumn where table_name = {0} and creator = 1 order by table_name", tableName)
            cmd.CommandText = String.Format("select sc.* from syscolumn sc INNER JOIN systable st ON sc.table_id = st.table_id WHERE st.table_name = '{0}'", tableName)
            Using reader = cmd.ExecuteReader
                While reader.Read
                    Dim fi As DBSchema.FieldInfo
                    fi = GetSybaseFieldInfo(tableName, reader("column_id"))
                    fi.FieldName = reader("column_name")
                    'fi.FieldType = reader("data_type")
                    'fi.FieldTypeName = reader("type_name")
                    'fi.FieldLength = reader("length")
                    'fi.Nullable = reader("nullable")
                    fieldList.Add(fi)
                End While
            End Using
        End Using

        Return fieldList
    End Function

    Public Function GetSybaseFieldInfo(tableName As String, columnID As String) As DBSchema.FieldInfo
        Using cmd As IDbCommand = XpoDefault.DataLayer.Connection.CreateCommand()
            cmd.CommandText = "exec sp_columns " & tableName
            'cmd.CommandText = String.Format("select sc.* from syscolumn where table_name = {0} and field_name = {1} and creator = 1 order by table_name", tableName, fieldName)
            Using reader = cmd.ExecuteReader
                While reader.Read
                    If reader("colid") = columnID Then
                        Dim fi As New DBSchema.FieldInfo
                        'fi.FieldName = reader("column_name")
                        fi.FieldType = reader("data_type")
                        fi.FieldTypeName = reader("type_name")
                        fi.FieldLength = reader("length")
                        fi.Nullable = reader("nullable")
                        Return fi
                    End If
                    'fieldList.Add(fi)
                End While
            End Using
        End Using

        Return Nothing
    End Function



End Module

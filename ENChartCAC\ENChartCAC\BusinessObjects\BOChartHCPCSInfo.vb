﻿Imports DevExpress.Xpo
Imports EnchartDOLib

<DebuggerDisplay("({HCPCS}:{HcpcsCpt} CodeType:{CodeType}, BillingDate:{BillingDate}, BillDate:{BillDate}, NU1:{NursingUnit1}, NU2:{NursingUnit2})")> Public Class BOChartHCPCSInfo
    Inherits BusinessObject
    Public Property Chart As Integer
    Public Property BillingDate As Nullable(Of Date)
    Public Property BillDate As Nullable(Of Date)
    Public Property HCPCS As String
    Public Property HcpcsCpt As String
    Public Property Modifier1 As String
    Public Property Modifier2 As String
    Public Property Modifier3 As String
    Public Property Modifier4 As String
    Public Property MaxUnits As Integer
    Public Property TotalUnits As Integer
    Public Property Units1 As Integer
    Public Property NursingUnit1 As String
    Public Property Units2 As Integer
    Public Property NursingUnit2 As String
    Public Property Units3 As Integer
    Public Property NursingUnit3 As String
    Public Property Units4 As Integer
    Public Property NursingUnit4 As String
    Public Property Units5 As Integer
    Public Property NursingUnit5 As String
    Public Property CodeType As String

    Public Shared Widening Operator CType(ByVal boCHI As BOChartHCPCSInfo) As DOChartHCPCSInfo
        If boCHI Is Nothing Then Return Nothing
        Dim doCHI As DOChartHCPCSInfo
        If boCHI.Oid > NEW_OBJ_OID Then
            doCHI = XpoDefault.Session.GetObjectByKey(Of DOChartHCPCSInfo)(boCHI.Oid)
        Else
            doCHI = New DOChartHCPCSInfo() ' With {.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCHI.Chart)}
        End If

        'doCHI.Chart = XpoDefault.Session.GetObjectByKey(Of DOChart)(boCHI.Chart)
        doCHI.BillingDate = boCHI.BillingDate
        doCHI.BillDate = boCHI.BillDate
        doCHI.HCPCS = boCHI.HCPCS
        doCHI.HcpcsCpt = boCHI.HcpcsCpt
        doCHI.Modifier1 = boCHI.Modifier1
        doCHI.Modifier2 = boCHI.Modifier2
        doCHI.Modifier3 = boCHI.Modifier3
        doCHI.Modifier4 = boCHI.Modifier4
        doCHI.MaxUnits = boCHI.MaxUnits
        doCHI.TotalUnits = boCHI.TotalUnits
        doCHI.Units1 = boCHI.Units1
        doCHI.NursingUnit1 = boCHI.NursingUnit1
        doCHI.Units2 = boCHI.Units2
        doCHI.NursingUnit2 = boCHI.NursingUnit2
        doCHI.Units3 = boCHI.Units3
        doCHI.NursingUnit3 = boCHI.NursingUnit3
        doCHI.Units4 = boCHI.Units4
        doCHI.NursingUnit4 = boCHI.NursingUnit4
        doCHI.Units5 = boCHI.Units5
        doCHI.NursingUnit5 = boCHI.NursingUnit5
        doCHI.CodeType = boCHI.CodeType
        Return doCHI
    End Operator


    'Public Shared Narrowing Operator CType(ByVal doCHI As DOChartHCPCSInfo) As BOChartHCPCSInfo
    '    If doCHI Is Nothing Then Return Nothing
    '    Dim boCHI As New BOChartHCPCSInfo

    '    boCHI.Chart = If(doCHI IsNot Nothing, doCHI.Chart.Oid, NEW_OBJ_OID)
    '    boCHI.BillingDate = doCHI.BillingDate
    '    boCHI.BillDate = doCHI.BillDate
    '    boCHI.HCPCS = doCHI.HCPCS
    '    boCHI.HcpcsCpt = doCHI.HcpcsCpt
    '    boCHI.Modifier1 = doCHI.Modifier1
    '    boCHI.Modifier2 = doCHI.Modifier2
    '    boCHI.Modifier3 = doCHI.Modifier3
    '    boCHI.Modifier4 = doCHI.Modifier4
    '    boCHI.MaxUnits = doCHI.MaxUnits
    '    boCHI.TotalUnits = doCHI.TotalUnits
    '    boCHI.Units1 = doCHI.Units1
    '    boCHI.NursingUnit1 = doCHI.NursingUnit1
    '    boCHI.Units2 = doCHI.Units2
    '    boCHI.NursingUnit2 = doCHI.NursingUnit2
    '    boCHI.Units3 = doCHI.Units3
    '    boCHI.NursingUnit3 = doCHI.NursingUnit3
    '    boCHI.Units4 = doCHI.Units4
    '    boCHI.NursingUnit4 = doCHI.NursingUnit4
    '    boCHI.Units5 = doCHI.Units5
    '    boCHI.NursingUnit5 = doCHI.NursingUnit5
    '    boCHI.CodeType = doCHI.CodeType
    '    Return boCHI
    'End Operator
End Class


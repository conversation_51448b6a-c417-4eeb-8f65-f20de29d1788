Imports System
Imports System.IO
Imports System.Threading

'ummm
Module LauncherUpdaterProgram
    Sub Main()
        Dim bNotDone As Boolean = True
        Console.WriteLine("Waiting To Update MIC ECLauncher ...")
        Do
            Dim localProcesses As Process() = Process.GetProcessesByName("eclauncher")
            If localProcesses.Count <= 0 Then
                bNotDone = False
            End If
            Console.WriteLine(".")
            Thread.Sleep(250)
        Loop While bNotDone

        If VerifyNewLauncherIsPresent() = False Then
            DisplayMissingFileMsg()
            Exit Sub
        End If
        DeleteOldLauncher()
        ReNameNewLauncher()
        RestartLauncher()
    End Sub

    Private Function VerifyNewLauncherIsPresent() As Boolean
        Dim targetPath As String = "ECLauncherTemp.exe"
        Try
            Dim targetFile As New FileInfo(targetPath)
            If targetFile.Exists Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Sub DisplayMissingFileMsg()
        'MessageBox.Show("ECLauncherTemp.exe file not found. Verify that the new ECLauncher.exe you are trying to deploy has been renamed to ECLauncherTemp.exe and exists in the updates folder.", "Launcher Updater Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Sub

    Private Sub RestartLauncher()
        Process.Start("ECLauncher.exe")
    End Sub

    Private Sub DeleteOldLauncher()
        Dim targetPath As String = "ECLauncher.exe"
        Try
            Dim oldFile As New FileInfo(targetPath)
            If oldFile.Exists Then
                oldFile.Delete()
            End If

        Catch ex As Exception
            Console.WriteLine(String.Format("ERROR : {0}", ex.Message))
            Thread.Sleep(2000)
            Environment.Exit(0)
        End Try
    End Sub

    Private Sub ReNameNewLauncher()
        Dim targetPath As String = "ECLauncherTemp.exe"
        Try
            Dim newFile As New FileInfo(targetPath)
            If newFile.Exists Then
                FileSystem.Rename(targetPath, "ECLauncher.exe")
            Else
                Environment.Exit(0)
            End If
        Catch ex As Exception
            Environment.Exit(0)
        End Try

    End Sub
End Module


﻿Imports System.Collections.Specialized
Imports System.Reflection
Imports System.IO
Imports System.Text.RegularExpressions

Imports DevExpress
Imports DevExpress.Xpo

Imports EnchartDOLib

Imports McKesson.HIC.ColdFeed.ChartObjWrapper.ReasonEnum
Imports System.Runtime.Serialization

Namespace ColdFeed

    ''' <summary>
    ''' This Class hold references to all the concrete implementations of the interfaces...
    ''' </summary>
    ''' <remarks></remarks>
    Public Class ColdFeedHelper

#Region "Fields"

        'Public OriginalDetailsFileExportPath As String
        'Public OriginalImageFileExportPath As String

        'interfaces
        Protected iExportDetailsFile As IExportChargeSummaryDetailsFile
        Protected iExportImageFile As IExportChargeSummaryImageFile
        Public iGetChartsToExport As IGetChartsToExport
        Protected ilog As ICustomLog
        Protected iShowMessage As IShowMessage

        Private _bLabel As String

        'other needed data
        Private _DetailsFileExportPath As String
        Private _facility As DOFacility
        Private _FieldMapDefList As List(Of DOColdFeedFieldMap)
        Private _CustomSettingsDictionary As Dictionary(Of String, String)
        Private _ImageExportType As String
        Private _ImageFileExportPath As String

#End Region 'Fields

#Region "Constructors"

        Public Sub New(ByVal pExportImage As IExportChargeSummaryImageFile,
            ByVal pExportDetails As IExportChargeSummaryDetailsFile,
            ByVal pGetCharts As IGetChartsToExport,
            ByVal pLog As ICustomLog, ByVal pShowMessage As IShowMessage,
            ByVal pImageFileExportPath As String, ByVal pDetailsFileExportPath As String)

            Me.iShowMessage = pShowMessage
            Me.ilog = pLog

            Me.iExportImageFile = pExportImage
            Me.iExportImageFile.CFHelper = Me
            Me.iExportImageFile.CFHelper = Me
            'Me.iExportImageFile.ShowMessage = pShowMessage

            Me.iExportDetailsFile = pExportDetails
            Me.iExportDetailsFile.CFHelper = Me
            'Me.iExportDetailsFile.Log = pLog
            'Me.iExportDetailsFile.ShowMessage = pShowMessage

            Me.iGetChartsToExport = pGetCharts
            Me.iGetChartsToExport.CFHelper = Me
            Me.ImageFileExportPath = pImageFileExportPath
            Me.DetailsFileExportPath = pDetailsFileExportPath
        End Sub

#End Region 'Constructors

#Region "Properties"

        Private _OptionsDict As Dictionary(Of String, String)
        ''' <summary>
        ''' This is the actual dictionary of settings + overrides
        ''' (from the DOColdfeedOptions table) this is actually
        ''' passed to the plugins via the ChartObjWrapper container.
        ''' </summary>
        ''' <value></value>
        ''' <returns></returns>
        ''' <remarks></remarks>
        Public Property OptionsDict() As Dictionary(Of String, String)
            Get
                If _OptionsDict Is Nothing Then
                    _OptionsDict = New Dictionary(Of String, String)
                End If
                Return _OptionsDict
            End Get
            Set(ByVal value As Dictionary(Of String, String))
                _OptionsDict = value
            End Set
        End Property


        Private _DefaultOptionsDict As Dictionary(Of String, String)
        ''' <summary>
        ''' A dictionary of the settings in the app.config file.
        ''' These are the values that will be used unless a facility specific
        ''' record is defined for a particular setting in the DOColdFeedOptions table.
        ''' </summary>
        ''' <value></value>
        ''' <returns></returns>
        ''' <remarks></remarks>
        Public Property DefaultOptionsDict() As Dictionary(Of String, String)
            Get
                Return _DefaultOptionsDict
            End Get
            Set(ByVal value As Dictionary(Of String, String))
                _DefaultOptionsDict = value
            End Set
        End Property


        Public Property BatchLabel() As String
            Get
                Return _bLabel
            End Get
            Set(ByVal value As String)
                _bLabel = value
            End Set
        End Property

        Public Property DetailsFileExportPath() As String
            Get
                Return _DetailsFileExportPath
            End Get
            Set(ByVal value As String)
                _DetailsFileExportPath = value
                ' OriginalDetailsFileExportPath = value
            End Set
        End Property

        'Public Property Facility() As DOFacility
        '    Get
        '        Return _facility
        '    End Get
        '    Set(ByVal value As DOFacility)
        '        If _facility Is value Then
        '            Return
        '        End If
        '        _facility = value
        '    End Set
        'End Property

        Public Property FieldMapDefList() As List(Of DOColdFeedFieldMap)
            Get
                Return _FieldMapDefList
            End Get
            Set(ByVal value As List(Of DOColdFeedFieldMap))
                _FieldMapDefList = value
            End Set
        End Property

        Private Property CustomSettingsDictionary As Dictionary(Of String, String)
            Get
                Return _CustomSettingsDictionary
            End Get
            Set(value As Dictionary(Of String, String))
                _CustomSettingsDictionary = value
            End Set
        End Property

        Public Property ImageExportType() As String
            Get
                Return _ImageExportType
            End Get
            Set(ByVal value As String)
                _ImageExportType = value
            End Set
        End Property

        Public Property ImageFileExportPath() As String
            Get
                Return _ImageFileExportPath
            End Get
            Set(ByVal value As String)
                _ImageFileExportPath = value
                'OriginalImageFileExportPath = value 'i know i know...
            End Set
        End Property

#End Region 'Properties

#Region "Methods"

        ''' <summary>
        ''' Update appropriate ColdFeed export fields in the DOChartInfo and DOChart tables...
        ''' </summary>
        ''' <param name="chart"></param>
        ''' <remarks></remarks>
        Public Shared Sub UpdateColdFeedExportStatusAndUnLock(ByVal chart As ChartObjWrapper)

            Dim chartInfo As DOChartInfo = chart.Chart.ChartInfo
            Dim pThisChart = chart.Chart

            If Not chart.ImageExportSucceeded Then
                chartInfo.Unlock()
                Return
            End If

            If chart.bSetFacToExported Then
                chartInfo.CFExportFacilityStatus = STR_Exported
                chartInfo.CFExportFacilityChartVersion = chart.Chart
                chartInfo.CFExportFacilityExportedDate = Now
            End If

            If chart.bSetPhysToExported Then
                chartInfo.CFExportPhysicianStatus = STR_Exported
                chartInfo.CFExportPhysicianChartVersion = chart.Chart
                chartInfo.CFExportPhysicianExportedDate = Now
            End If

            If chart.bSetObsToExported Then
                chartInfo.CFExportObservationStatus = STR_Exported
                chartInfo.CFExportObservationChartVersion = chart.Chart
                chartInfo.CFExportObservationExportedDate = Now
            End If

            chartInfo.CFExportInclude = False
            chartInfo.CFExportUserRequestPending = False

            chartInfo.Unlock()
        End Sub

        Public Function ExportOneAtTime(ByVal facility As DOFacility, ByVal chartWrapperDict As ChartWrapperDictionary) As Boolean
            If Not InitForFacility(facility) Then
                Return False
            End If

            iExportDetailsFile.Initialize()

            For Each chartWrapper As ChartObjWrapper In chartWrapperDict.Values
                Try
                    Dim ExportChartImageSucceeded As Boolean
                    Dim ExportChartDetailSucceeded As Boolean

                    ExportChartImageSucceeded = ExportChartImage(chartWrapper)
                    ExportChartDetailSucceeded = ExportChartDetail(chartWrapper)

                    Dim resultString As String = IIf(ExportChartImageSucceeded And ExportChartDetailSucceeded, "Succeeded", "Failed").ToString

                    AuditLogger.ImageFeed.LogImageFeedExport(chartWrapper.Chart, resultString, facility.LongName)

                    ShowMessageAndLog(String.Format("Export ({0}) for  VisitID({1}) = DOChartInfoOID({2}) DOChartOID({3}) Chart Version ({4})",
                                                    resultString, chartWrapper.Chart.VisitID, chartWrapper.Chart.ChartInfo.Oid, chartWrapper.Chart.Oid, chartWrapper.Chart.Version))

                Catch ex As Exception
                    ShowMessageAndLog(ex.Message)
                    Dim chart As DOChart = chartWrapper.Chart
                    If chart IsNot Nothing AndAlso chart.ChartInfo.Locked Then
                        Try
                            chart.ChartInfo.Unlock() 'remember... this will save new fields
                        Catch ex2 As Exception
                            'ilog.LogError(ex2.Message)
                        End Try

                    End If
                    'We should could trying right? ...
                    'Return False
                End Try
            Next

            iExportDetailsFile.WrapUp()

            Return True
        End Function

        Public Function GetMappedFieldValuePairs(ByVal chartWrapper As ChartObjWrapper) As List(Of MapFieldValuePair)
            Dim return_list As New List(Of MapFieldValuePair)
            Dim chart As DOChart = chartWrapper.Chart

            Try
                For Each mappingDef As DOColdFeedFieldMap In FieldMapDefList
                    Dim value As Object
                    Dim t As Type
                    Dim fi As PropertyInfo

                    Select Case mappingDef.TableName
                        Case "DOChartInfo"
                            t = chart.ChartInfo.GetType
                            fi = t.GetProperty(mappingDef.FieldName)
                            If fi IsNot Nothing Then

                                value = fi.GetValue(chart.ChartInfo, Nothing)

                                If Not String.IsNullOrEmpty(mappingDef.RegexFilter) Then
                                    Dim regex As New Regex(mappingDef.RegexFilter)
                                    Dim match As Match = regex.Match(value.ToString)

                                    If match.Success = True Then
                                        value = match.Value
                                    End If
                                End If

                                If value Is Nothing Then value = ""
                                return_list.Add(New MapFieldValuePair(mappingDef.OutPutFieldName, value.ToString))
                            Else
                                ShowMessageAndLog(String.Format("DOColdFeedFieldMap error, {0} is not a member of {1}",
                                                          mappingDef.FieldName, mappingDef.TableName))
                            End If

                        Case "DOChart"
                            t = chart.GetType
                            fi = t.GetProperty(mappingDef.FieldName)
                            If fi IsNot Nothing Then
                                value = fi.GetValue(chart, Nothing)

                                If Not String.IsNullOrEmpty(mappingDef.RegexFilter) Then
                                    Dim regex As New Regex(mappingDef.RegexFilter)
                                    Dim match As Match = regex.Match(value.ToString)

                                    If match.Success = True Then
                                        value = match.Value
                                    End If
                                End If

                                If value Is Nothing Then value = ""

                                If fi.PropertyType Is GetType(Date) Then
                                    Dim dateFormat As String = "MM/dd/yy HH:mm"
                                    'Dim dateFormat As String = "yyyyMMdd hh:mm"
                                    If OptionsDict.ContainsKey("DateFormat") Then
                                        dateFormat = OptionsDict("DateFormat")
                                    End If
                                    return_list.Add(New MapFieldValuePair(mappingDef.OutPutFieldName, Date.Parse(value.ToString).ToString(dateFormat)))
                                Else
                                    return_list.Add(New MapFieldValuePair(mappingDef.OutPutFieldName, value.ToString))
                                End If
                            Else
                                ShowMessageAndLog(String.Format("DOColdFeedFieldMap error, {0} is not a member of {1}",
                                                          mappingDef.FieldName, mappingDef.TableName))
                            End If
                        Case "CUSTOM HARDCODE", "HARDCODE"
                            return_list.Add(New MapFieldValuePair(mappingDef.FieldName, mappingDef.OutPutFieldName))
                        Case "IMAGEFILENAME"
                            return_list.Add(New MapFieldValuePair(mappingDef.FieldName, chartWrapper.ImageFileName))
                        Case Else

                    End Select
                Next
            Catch ex As Exception
                ShowMessageAndLog(ex.Message)
            End Try

            Return return_list
        End Function

        Public Function InitForFacility(ByVal facility As DOFacility) As Boolean
            InitColdFeedMap(facility)

            Return True
        End Function

        Public Sub ShowMessageAndLog(ByVal pmsg As String)
            iShowMessage.ShowMsg(pmsg)
            ilog.LogMessage(pmsg)
        End Sub

        Private Function ExportChartDetail(ByVal chart As ChartObjWrapper) As Boolean
            Try
                iExportDetailsFile.CreateHeader()
                iExportDetailsFile.CreateDetail(chart)
                iExportDetailsFile.CreateFooter()
                iExportDetailsFile.WriteFileToDestination()

                UpdateColdFeedExportStatusAndUnLock(chart)
                Return True
            Catch ex As Exception
                ShowMessageAndLog(ex.ToString)
                Return False
            End Try
        End Function

        Private Function ExportChartImage(ByVal chart As ChartObjWrapper) As Boolean
            Return iExportImageFile.CreateImageFile(chart)
        End Function

        Public Sub InitColdFeedMap(ByVal Facility As DOFacility)
            Dim tdict As New Dictionary(Of String, DOColdFeedFieldMap)

            Dim cff_maps As ColdFeedFieldMaps = LoadXmlFile()

            Dim ColdFeedMappingsDict As Dictionary(Of Integer, List(Of DTOColdFeedFieldMap)) = Me.GetFieldMapsDictionary(cff_maps)

            If ColdFeedMappingsDict Is Nothing OrElse Not ColdFeedMappingsDict.ContainsKey(Facility.Oid) Then
                ShowMessageAndLog(String.Format("WARNING -There are no Field mappings defined for facility {0}", Facility.Oid))
            Else

                For Each fmx In ColdFeedMappingsDict(Facility.Oid)
                    Dim nfm As New DOColdFeedFieldMap()
                    nfm.OutPutFieldName = fmx.OutPutFieldName
                    nfm.TableName = fmx.TableName
                    nfm.FieldName = fmx.FieldName
                    nfm.RegexFilter = fmx.RegexFilter
                    tdict(fmx.OutPutFieldName) = nfm
                Next
            End If

            'Now load the same data from the database if it exists, and override what's in config file...
            Dim xpCol As XPCollection(Of DOColdFeedFieldMap) = Facility.ColdFeedFieldMaps

            For Each fm In xpCol
                tdict(fm.OutPutFieldName) = fm
            Next
            Dim newList As New List(Of DOColdFeedFieldMap)
            For Each fm In tdict.Values
                newList.Add(fm)
            Next

            Me.FieldMapDefList = newList

            ' ----------------------------------------------------------

            Dim custom_settings_dictionary As Dictionary(Of Integer, List(Of CustomSetting)) = Me.GetCustomSettingsDictionary(cff_maps)
            Dim facility_cs_dictionary As New Dictionary(Of String, String)

            If custom_settings_dictionary.ContainsKey(Facility.Oid) Then
                For Each cs As CustomSetting In custom_settings_dictionary(Facility.Oid)
                    facility_cs_dictionary(cs.key) = cs.value
                Next
            End If

            Me.CustomSettingsDictionary = facility_cs_dictionary

        End Sub


        ''' <summary>
        ''' 
        ''' </summary>
        ''' <returns></returns>
        ''' <remarks>This function should be in a different dll ...</remarks>
        Private Function LoadXmlFile() As ColdFeedFieldMaps
            'Private Function GetFieldMapDictFromXmlFile() As Dictionary(Of Integer, List(Of DTOColdFeedFieldMap))

            Dim return_val As ColdFeedFieldMaps = Nothing
            Const STR_ColdFeedFieldMapxml As String = "ColdFeedFieldExportMaps.xml"

            Try
                'If Not File.Exists(Path.Combine(My.Application.Info.DirectoryPath, STR_ColdFeedFieldMapxml)) Then
                If Not File.Exists(STR_ColdFeedFieldMapxml) Then
                    ShowMessageAndLog(String.Format("{0} optional config file not found. Skipping...", STR_ColdFeedFieldMapxml))
                Else
                    Using fs As New FileStream(STR_ColdFeedFieldMapxml, FileMode.Open)
                        Dim x As New Xml.Serialization.XmlSerializer(GetType(ColdFeedFieldMaps))
                        return_val = TryCast(x.Deserialize(fs), ColdFeedFieldMaps)
                        'Dim fms As ColdFeedFieldMaps = TryCast(x.Deserialize(fs), ColdFeedFieldMaps)
                    End Using
                End If

            Catch ex As Exception
                ShowMessageAndLog(String.Format("Error trying to load {0}: {1}", STR_ColdFeedFieldMapxml, ex.Message))
            End Try

            Return return_val
        End Function

        Private Function GetFieldMapsDictionary(ByVal cff_maps As ColdFeedFieldMaps) As Dictionary(Of Integer, List(Of DTOColdFeedFieldMap))
            Dim return_dict As New Dictionary(Of Integer, List(Of DTOColdFeedFieldMap))

            For Each fm As ColdFeedFieldMap In cff_maps
                return_dict(fm.FacilityOID) = fm.FieldDefList
            Next

            Return return_dict
        End Function

        Private Function GetCustomSettingsDictionary(ByVal cff_maps As ColdFeedFieldMaps) As Dictionary(Of Integer, List(Of CustomSetting))
            Dim return_dict As New Dictionary(Of Integer, List(Of CustomSetting))

            For Each fm As ColdFeedFieldMap In cff_maps
                return_dict(fm.FacilityOID) = fm.CustomSettings
            Next

            Return return_dict
        End Function

        Public Function GetCustomSettingValue(ByVal key As String) As String
            If CustomSettingsDictionary.ContainsKey(key) Then
                Return CustomSettingsDictionary(key)
            Else
                Return ""
            End If
        End Function

#End Region 'Methods

    End Class

    Public Class CustomSetting
        Public key As String
        Public value As String
    End Class

    Public Class MapFieldValuePair
        Public Property Field As String
        Public Property Value As String

        Public Sub New(ByVal field As String, ByVal value As String)
            Me.Field = field
            Me.Value = value
        End Sub
    End Class

    Public Class ColdFeedFieldMap
        Public Sub New()

        End Sub
        Public FacilityOID As Integer

        Public CustomSettings As New List(Of CustomSetting)

        Public FieldDefList As New List(Of DTOColdFeedFieldMap)
    End Class

    Public Class ColdFeedFieldMaps
        Inherits List(Of ColdFeedFieldMap)

        Public Sub New()

        End Sub
    End Class

End Namespace


Imports System.Collections.Specialized
Imports System.Reflection
Imports System.IO
Imports DevExpress
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports McKesson.HIC.ColdFeed.ChartObjWrapper.ReasonEnum
Imports Microsoft.VisualBasic.Logging
Imports Microsoft.Extensions

Namespace ColdFeed
    'Example of Simple iLog implementation
    Public Class DefaultLog
        Implements ICustomLog
        Private _logger As Microsoft.VisualBasic.Logging.Log

        Sub New()
            'Dim logPath = IO.Path.Combine(My.Application.Info.DirectoryPath, "CFELogs")
            'My.Application.Log.DefaultFileLogWriter.CustomLocation = logPath
            'If Not IO.Directory.Exists(logPath) Then
            '    IO.Directory.CreateDirectory(logPath)
            'End If

            'My.Application.Log.DefaultFileLogWriter.Location = Logging.LogFileLocation.Custom
            'My.Application.Log.DefaultFileLogWriter.CustomLocation = logPath
            'My.Application.Log.DefaultFileLogWriter.BaseFileName = "CFE_" & Strings.Format(Now, "MM-dd-yy_hh.mm")
            'My.Application.Log.DefaultFileLogWriter.AutoFlush = True
            'My.Application.Log.DefaultFileLogWriter.Append = True

        End Sub

        Sub New(logger As Microsoft.VisualBasic.Logging.Log)
            _logger = logger
        End Sub


        'Private _log As Logging.Log 'Lazy-initialized and cached log object.
        '''' <summary>
        '''' Provides access to logging capability.
        '''' </summary>
        '''' <value>Returns a Microsoft.VisualBasic.Windows.Log object used for logging to OS log, debug window
        '''' and a delimited text file or xml log.</value>
        'Public ReadOnly Property Log() As Logging.Log
        '    Get
        '        If _log Is Nothing Then
        '            _log = New Logging.Log
        '        End If
        '        Return _log
        '    End Get
        'End Property

#Region "Methods"

        Public Sub LogMessage(ByVal pMsg As String) Implements ICustomLog.LogMessage
            _logger.WriteEntry(String.Format("{0}: {1}", Now, pMsg))
            'My.Application.Log.WriteEntry(String.Format("{0}: {1}", Now, pMsg))
            'Log.WriteEntry(String.Format("{0}: {1}", Now, pMsg))
        End Sub

#End Region 'Methods

    End Class
End Namespace

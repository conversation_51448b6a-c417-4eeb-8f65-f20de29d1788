﻿Imports System.Windows.Forms

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class CreateNewConfigInstanceForAllFacilitiesForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LabelControl31 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl32 = New DevExpress.XtraEditors.LabelControl()
        Me.deActivationDate = New DevExpress.XtraEditors.DateEdit()
        Me.teComments = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl30 = New DevExpress.XtraEditors.LabelControl()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.CloseButton = New System.Windows.Forms.Button()
        Me.LoadCiInfoButton = New System.Windows.Forms.Button()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.cbeFormClassaName = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ProgressBar = New DevExpress.XtraEditors.ProgressBarControl()
        Me.ProgressBar1 = New System.Windows.Forms.ProgressBar()
        Me.ProgressMsgLabel = New System.Windows.Forms.Label()
        Me.FaciltiyDescriptionLabel = New System.Windows.Forms.Label()
        CType(Me.deActivationDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deActivationDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teComments.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.cbeFormClassaName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBar.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelControl31
        '
        Me.LabelControl31.Location = New System.Drawing.Point(309, 58)
        Me.LabelControl31.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LabelControl31.Name = "LabelControl31"
        Me.LabelControl31.Size = New System.Drawing.Size(82, 13)
        Me.LabelControl31.TabIndex = 50
        Me.LabelControl31.Text = "Form Class Name"
        '
        'LabelControl32
        '
        Me.LabelControl32.Location = New System.Drawing.Point(12, 57)
        Me.LabelControl32.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LabelControl32.Name = "LabelControl32"
        Me.LabelControl32.Size = New System.Drawing.Size(78, 13)
        Me.LabelControl32.TabIndex = 49
        Me.LabelControl32.Text = "Activation Date:"
        '
        'deActivationDate
        '
        Me.deActivationDate.EditValue = Nothing
        Me.deActivationDate.Location = New System.Drawing.Point(110, 53)
        Me.deActivationDate.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.deActivationDate.Name = "deActivationDate"
        Me.deActivationDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.deActivationDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deActivationDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.deActivationDate.Properties.DisplayFormat.FormatString = "g"
        Me.deActivationDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.deActivationDate.Properties.EditFormat.FormatString = "g"
        Me.deActivationDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.deActivationDate.Properties.Mask.EditMask = "g"
        Me.deActivationDate.Size = New System.Drawing.Size(173, 20)
        Me.deActivationDate.TabIndex = 47
        '
        'teComments
        '
        Me.teComments.Location = New System.Drawing.Point(86, 97)
        Me.teComments.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.teComments.Name = "teComments"
        Me.teComments.Size = New System.Drawing.Size(926, 20)
        Me.teComments.TabIndex = 52
        '
        'LabelControl30
        '
        Me.LabelControl30.Location = New System.Drawing.Point(12, 100)
        Me.LabelControl30.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LabelControl30.Name = "LabelControl30"
        Me.LabelControl30.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl30.TabIndex = 51
        Me.LabelControl30.Text = "Comment"
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(830, 228)
        Me.Button1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(88, 27)
        Me.Button1.TabIndex = 53
        Me.Button1.Text = "Get Busy!"
        Me.Button1.UseVisualStyleBackColor = True
        '
        'CloseButton
        '
        Me.CloseButton.Location = New System.Drawing.Point(948, 228)
        Me.CloseButton.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CloseButton.Name = "CloseButton"
        Me.CloseButton.Size = New System.Drawing.Size(88, 27)
        Me.CloseButton.TabIndex = 54
        Me.CloseButton.Text = "Close"
        Me.CloseButton.UseVisualStyleBackColor = True
        '
        'LoadCiInfoButton
        '
        Me.LoadCiInfoButton.Location = New System.Drawing.Point(925, 52)
        Me.LoadCiInfoButton.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LoadCiInfoButton.Name = "LoadCiInfoButton"
        Me.LoadCiInfoButton.Size = New System.Drawing.Size(88, 27)
        Me.LoadCiInfoButton.TabIndex = 55
        Me.LoadCiInfoButton.Text = "Load "
        Me.LoadCiInfoButton.UseVisualStyleBackColor = True
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.deActivationDate)
        Me.Panel1.Controls.Add(Me.LoadCiInfoButton)
        Me.Panel1.Controls.Add(Me.cbeFormClassaName)
        Me.Panel1.Controls.Add(Me.LabelControl32)
        Me.Panel1.Controls.Add(Me.LabelControl31)
        Me.Panel1.Controls.Add(Me.teComments)
        Me.Panel1.Controls.Add(Me.LabelControl30)
        Me.Panel1.Location = New System.Drawing.Point(15, 3)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1024, 147)
        Me.Panel1.TabIndex = 56
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(705, 7)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(308, 45)
        Me.Label1.TabIndex = 56
        Me.Label1.Text = "* If the ""ImportConfig.dat"" file is in the same directory as " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the exe is run fro" &
    "m it should be loaded automatically." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'cbeFormClassaName
        '
        Me.cbeFormClassaName.Location = New System.Drawing.Point(428, 53)
        Me.cbeFormClassaName.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cbeFormClassaName.Name = "cbeFormClassaName"
        Me.cbeFormClassaName.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeFormClassaName.Properties.Items.AddRange(New Object() {"ChartFormV18", "ChartFormV17", "ChartFormV16", "ChartFormV15", "ChartFormV14", "ChartFormV13", "ChartFormV12", "ChartFormV11", "ChartFormV10", "ChartFormV9", "ChartForm"})
        Me.cbeFormClassaName.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeFormClassaName.Size = New System.Drawing.Size(176, 20)
        Me.cbeFormClassaName.TabIndex = 48
        '
        'ProgressBar
        '
        Me.ProgressBar.Location = New System.Drawing.Point(12, 262)
        Me.ProgressBar.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(1026, 21)
        Me.ProgressBar.TabIndex = 57
        Me.ProgressBar.Visible = False
        '
        'ProgressBar1
        '
        Me.ProgressBar1.Location = New System.Drawing.Point(10, 195)
        Me.ProgressBar1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New System.Drawing.Size(1026, 27)
        Me.ProgressBar1.TabIndex = 58
        '
        'ProgressMsgLabel
        '
        Me.ProgressMsgLabel.AutoSize = True
        Me.ProgressMsgLabel.Location = New System.Drawing.Point(12, 230)
        Me.ProgressMsgLabel.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.ProgressMsgLabel.Name = "ProgressMsgLabel"
        Me.ProgressMsgLabel.Size = New System.Drawing.Size(41, 15)
        Me.ProgressMsgLabel.TabIndex = 59
        Me.ProgressMsgLabel.Text = "Label2"
        '
        'FaciltiyDescriptionLabel
        '
        Me.FaciltiyDescriptionLabel.AutoSize = True
        Me.FaciltiyDescriptionLabel.Location = New System.Drawing.Point(14, 166)
        Me.FaciltiyDescriptionLabel.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.FaciltiyDescriptionLabel.Name = "FaciltiyDescriptionLabel"
        Me.FaciltiyDescriptionLabel.Size = New System.Drawing.Size(41, 15)
        Me.FaciltiyDescriptionLabel.TabIndex = 60
        Me.FaciltiyDescriptionLabel.Text = "Label2"
        '
        'CreateNewConfigInstanceForAllFacilitiesForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 15.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1055, 294)
        Me.ControlBox = False
        Me.Controls.Add(Me.FaciltiyDescriptionLabel)
        Me.Controls.Add(Me.ProgressMsgLabel)
        Me.Controls.Add(Me.ProgressBar1)
        Me.Controls.Add(Me.ProgressBar)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.CloseButton)
        Me.Controls.Add(Me.Button1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "CreateNewConfigInstanceForAllFacilitiesForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Create New Pending CI For All Facilities"
        CType(Me.deActivationDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deActivationDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teComments.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.cbeFormClassaName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBar.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents LabelControl31 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl32 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents deActivationDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents teComments As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl30 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Button1 As Button
    Friend WithEvents CloseButton As Button
    Friend WithEvents LoadCiInfoButton As Button
    Friend WithEvents Panel1 As Panel
    Friend WithEvents ProgressBar As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents Label1 As Label
    Friend WithEvents cbeFormClassaName As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ProgressBar1 As ProgressBar
    Friend WithEvents ProgressMsgLabel As Label
    Friend WithEvents FaciltiyDescriptionLabel As Label
End Class

﻿Imports System.Collections.Specialized
Imports System.Data.Sql
Imports System.Data.SqlClient
Imports iAnywhere.Data.AsaClient
Imports System.Data.Odbc

Imports DevExpress.Xpo
Imports System.IO
Imports System.IO.FileStream

Imports System.Reflection

Public Class frmViewConfigInstances

    Public Sub frmViewConfigInstances()
    End Sub

#Region "StartUp"

    Public FacilityLookUPList As New List(Of FacLookUp)
    Public licDataTable As New DataTable()
    Public wrkStationDataTable As New DataTable()
    Private blNumLic As Boolean = False




    Public Class FacLookUp
        Public facilityPtr As DOFacility

        Private _facPtr As DOFacility
        Public Property FacPtr() As DOFacility
            Get
                Return _facPtr
            End Get
            Set(ByVal value As DOFacility)
                _facPtr = value
            End Set
        End Property


        Private _EID As String
        Public Property EID() As String
            Get
                Return _EID
            End Get
            Set(ByVal value As String)
                _EID = value
            End Set
        End Property

        Private _Company As String
        Public Property Company() As String
            Get
                Return _Company
            End Get
            Set(ByVal value As String)
                _Company = value
            End Set
        End Property

        Private _CompanyClient As String
        Public Property CompanyClient() As String
            Get
                Return _CompanyClient
            End Get
            Set(ByVal value As String)
                _CompanyClient = value
            End Set
        End Property

        Private _Faciltiy As String
        Public Property Facility() As String
            Get
                Return _Faciltiy
            End Get
            Set(ByVal value As String)
                _Faciltiy = value
            End Set
        End Property


        Public Sub New(ByVal pfac As DOFacility)
            FacPtr = pfac
            EID = pfac.CompanyClient.Company.EnchartID.LongName
            Company = pfac.CompanyClient.Company.LongName
            CompanyClient = pfac.CompanyClient.LongName
            Facility = pfac.LongName
        End Sub
    End Class

    Private Sub frmViewConfigInstances_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CheckSQL("Exists", "DODBLicValidation")
        CheckDOWLVExists("Exists", "DOWLV")
        HandleConfigs(frmType)
        blNumLic = False
    End Sub


#End Region

    Private _frmType As Integer = 0
    Public Property frmType()
        Get
            Return _frmType
        End Get
        Set(ByVal value)
            _frmType = value
        End Set
    End Property


    Private hiddenPages As New List(Of TabPage)()
    Public licList As List(Of String())


    Private Sub HandleConfigs(ByVal iType As Integer)
        Select Case iType
            Case 1
                LoadConfigInstances(iType)
                'LoadConfigInstances(3)
                dataGridConfigs.EditMode() = DataGridViewEditMode.EditProgrammatically
                tbCtrlConfigInstances.Visible = True
                tbCtrlConfigInstances.Dock() = DockStyle.Fill
                tbCtrlLicenses.Visible = False
                tbCtrlWrkStations.Visible = False
                tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(0)
                MenuStrip1.Visible = False
                MenuStrip2.Visible = True
                MenuStrip2.Dock() = DockStyle.Fill
                MenuStrip3.Visible = False
            Case 2
                LoadConfigInstances(iType)
                LoadConfigInstances(3)
                tbCtrlLicenses.Dock() = DockStyle.Fill
                tbCtrlConfigInstances.Visible = False
                tbCtrlWrkStations.Visible = False
                tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(0)
                MenuStrip1.Visible = True
                MenuStrip1.Dock() = DockStyle.Fill
                MenuStrip2.Visible = False
                MenuStrip3.Visible = False
            Case 3
                LoadConfigInstances(iType)
                LoadConfigInstances(3)
                tbCtrlLicenses.Dock() = DockStyle.Fill
                tbCtrlConfigInstances.Visible = False
                tbCtrlWrkStations.Visible = False
                tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(0)
                MenuStrip1.Visible = False
                MenuStrip2.Visible = False
                MenuStrip3.Visible = True
                MenuStrip3.Dock() = DockStyle.Fill
            Case 4
                LoadConfigInstances(iType)
                tbCtrlLicenses.Visible = False
                tbCtrlConfigInstances.Visible = False
                tbCtrlWrkStations.Visible = True
                tbCtrlWrkStations.Dock() = DockStyle.Fill
                'tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(0)
                MenuStrip1.Visible = False
                MenuStrip2.Visible = False
                MenuStrip3.Visible = True
                MenuStrip3.Dock() = DockStyle.Fill



        End Select

    End Sub


    Public Sub LoadConfigInstances(ByVal iType As Integer)

        Dim strCmd As String

        Select Case iType
            Case 1

                Try
                    strCmd = "Select * from vwCurrentPending"
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        BuildStoredProc()
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        Dim reader As IDataReader = cmd.ExecuteReader()
                        licDataTable.Load(reader)
                        dataGridConfigs.DataSource = licDataTable
                        DropStoredProc()
                        cmd.Dispose()
                    End Using

                    SizeScreen(iType)

                Catch ex As Exception
                    strCmd = String.Empty
                End Try

            Case 2
                Try

                    strCmd = "Select MaxLic, Enabled from DODBLicValidation"
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        Dim reader As IDataReader = cmd.ExecuteReader()
                        licDataTable.Load(reader)
                        dataGridLicenses.DataSource = licDataTable
                        cmd.Dispose()
                    End Using

                    SizeScreen(1)

                Catch ex As Exception
                    strCmd = String.Empty
                End Try

            Case 3
                Try
                    strCmd = "Select [GCRECORD] as Enabled, [OID] as ID,[WorkstationName],[Comment] from DOWLV" ',[OptimisticLockField] from DOWLV
                    Dim ds As New DataSet()
                    ds.Clear()
                    dataGridWrkStations2.DataSource = ds
                    wrkStationDataTable = New DataTable()
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        Dim reader As IDataReader = cmd.ExecuteReader()
                        wrkStationDataTable.Clear()
                        wrkStationDataTable.Load(reader)
                        dataGridWrkStations2.DataSource = wrkStationDataTable
                        tbCtrlLicenses.SelectedTab = tbCtrlLicenses.TabPages(1)
                        cmd.Dispose()
                    End Using

                    Dim dcs As DataGridViewColumn = dataGridWrkStations2.Columns(0)
                    'dcs.DefaultCellStyle() = TypeOf(Boolean'typeof(Boolean)
                    dcs.Width = 50
                    Dim dcs2 As DataGridViewColumn = dataGridWrkStations2.Columns(1)
                    dcs2.Width = 50
                    Dim dcs3 As DataGridViewColumn = dataGridWrkStations2.Columns(2)
                    dcs3.Width = 150
                    Dim dcs4 As DataGridViewColumn = dataGridWrkStations2.Columns(3)
                    dcs4.Width = 200

                    SizeScreen(3)

                Catch ex As Exception
                    strCmd = String.Empty
                End Try
            Case 4
                Try

                    strCmd = "Select MaxLic, Enabled from DODBLicValidation"
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        Dim reader As IDataReader = cmd.ExecuteReader()
                        licDataTable.Load(reader)
                        For Each r As DataRow In licDataTable.Rows
                            NumericUpDown1.Value = CInt(r(0).ToString())
                            CheckBox1.Checked = CInt(r(1).ToString())
                        Next
                        blNumLic = False
                        cmd.Dispose()
                    End Using


                    strCmd = "Select [GCRECORD] as Enabled, [OID] as ID,[WorkstationName],[Comment] from DOWLV" ',[OptimisticLockField] from DOWLV

                    Dim ds As New DataSet()
                    ds.Clear()
                    dataGridWrkStations.DataSource = ds
                    wrkStationDataTable = New DataTable()
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        Dim reader As IDataReader = cmd.ExecuteReader()
                        wrkStationDataTable.Clear()
                        wrkStationDataTable.Columns().Add("Enabled", GetType(String))
                        wrkStationDataTable.Load(reader)

                        wrkStationDataTable.Columns().Add("Enabled2", GetType(Boolean))
                        For Each dr As DataRow In wrkStationDataTable.Rows
                            If dr("Enabled").ToString() <> "" Then
                                dr("Enabled2") = False
                            Else
                                dr("Enabled2") = True
                            End If

                        Next
                        wrkStationDataTable.Columns.Remove("Enabled")
                        wrkStationDataTable.Columns("Enabled2").SetOrdinal(0)
                        dataGridWrkStations.DataSource = wrkStationDataTable
                        dataGridWrkStations.Columns(0).HeaderText = "Enabled"
                        dataGridWrkStations.Columns(1).Visible = False
                        tbCtrlLicenses.SelectedTab = tbCtrlLicenses.TabPages(1)
                        cmd.Dispose()
                    End Using

                    Dim c As DataGridViewCheckBoxColumn = dataGridWrkStations.Columns(0)

                    c.Width = 50

                    Dim dcs2 As DataGridViewColumn = dataGridWrkStations.Columns(1)
                    dcs2.Width = 50
                    Dim dcs3 As DataGridViewColumn = dataGridWrkStations.Columns(2)
                    dcs3.Width = 150
                    Dim dcs4 As DataGridViewColumn = dataGridWrkStations.Columns(3)
                    dcs4.Width = 200

                    SizeScreen(4)

                Catch ex As Exception
                    strCmd = String.Empty
                End Try
        End Select



    End Sub

    Private Sub SizeScreen(Optional ByVal i As Integer = 0, Optional ByVal iType As Integer = 0)

        If iType > 0 Then

            Select Case iType

                Case 1
                    Me.Text = "View Config Instances"
                    dataGridLicenses.EditMode = DataGridViewEditMode.EditProgrammatically
                    Dim sz As New System.Drawing.Size(980, 352)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2
                    Me.TabPage1.Text = Me.Text
                Case 2
                    Me.Text = "View/Edit License Details"
                    Dim sz As New System.Drawing.Size(506, 252)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2
                    Me.TabPage1.Text = Me.Text

            End Select

        Else

            Select Case i

                Case 1

                    Me.Text = "View/Edit Config Instances"
                    Dim sz As New System.Drawing.Size(980, 352)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2

                    Me.TabPage1.Text = Me.Text

                Case 2


                    Me.Text = "View/Edit WorkStation Details"
                    Dim sz As New System.Drawing.Size(640, 430)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2

                    Me.TabPage1.Text = "View/Edit License Details"
                    Me.TabPage2.Text = Me.Text

                Case 3


                    Me.Text = "View WorkStation Details"
                    Dim sz As New System.Drawing.Size(640, 430)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2

                    Me.TabPage1.Text = "View License Details"
                    Me.TabPage2.Text = Me.Text

                Case 4


                    Me.Text = "View/Edit License Details"
                    Dim sz As New System.Drawing.Size(540, 630)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2

                    Me.TabPage1.Text = Me.Text

                Case 5


                    Me.Text = "View WorkStation Details"
                    Dim sz As New System.Drawing.Size(540, 430)
                    Me.Size = sz
                    Me.Left = (Screen.PrimaryScreen.WorkingArea.Width - Me.Width) / 2
                    Me.Top = (Screen.PrimaryScreen.WorkingArea.Height - Me.Height) / 2

                    Me.TabPage1.Text = "View License Details"
                    Me.TabPage2.Text = Me.Text

            End Select

        End If

    End Sub

    Public Function CheckTable(ByVal strCmd() As String) As Boolean
        Try

            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = String.Format(strCmd(0))
                cmd.Connection = XpoDefault.Session.Connection
                Dim reader As IDataReader = cmd.ExecuteReader()
                ''Dim tableName As String
                ' ''Dim 
                ''While reader.Read()

                ''    If Not reader.GetValue(0) Is DBNull.Value Then _
                ''        tableName = reader.GetValue(0)
                ''    'If Not reader.GetValue(1) Is DBNull.Value Then _
                ''    'firstname = reader.GetValue(1)

                ''End While
                ''reader.Close()
                'Dim tableName As String = reader.GetValue(0).ToString()
                If Not reader.GetValue(0) Is DBNull.Value Then
                    'table.Load(reader)
                    If CheckRecs(strCmd(1)) Then
                        Return True
                    Else
                        If DoCmd(strCmd(3)) Then
                            Return True
                        Else
                            Return False
                        End If
                    End If
                Else
                    If DoCmd(strCmd(2)) Then
                        If DoCmd(strCmd(3)) Then
                            Return True
                        Else
                            Return False
                        End If
                    Else
                        Return False
                    End If
                End If

            End Using

        Catch ex As Exception
            Return False
        End Try

        Return False

    End Function

    Public Function CheckRecs(ByVal strCmd As String) As Boolean
        Try
            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = String.Format(strCmd)
                cmd.Connection = XpoDefault.Session.Connection
                Dim reader As IDataReader = cmd.ExecuteReader()

                If reader.FieldCount > 0 Then
                    Return True
                End If

            End Using

        Catch ex As Exception
            Return False
        End Try

        Return False

    End Function

    Public Function DoCmd(ByVal strCmd As String) As Boolean
        Try
            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = String.Format(strCmd)
                cmd.Connection = XpoDefault.Session.Connection
                Dim reader As IDataReader = cmd.ExecuteReader()

                If reader.FieldCount > 0 Then
                    Return True
                End If

            End Using

        Catch ex As Exception
            Return False
        End Try

        Return False

    End Function

    Public Sub DropStoredProc()
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

            Try
                Select Case frmType
                    Case 1
                        cmd.CommandText = String.Format("DROP VIEW vwCurrentPending") '"Select * from vwCurrentPending")
                    Case 2
                        cmd.CommandText = String.Format("DROP VIEW vwCurrentPending") '"Select * from vwCurrentPending")
                End Select

                cmd.Connection = XpoDefault.Session.Connection
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                If ex.Message.ToLower().Contains("not found") Then
                    'MessageBox.Show("hey")
                End If
            End Try

        End Using
    End Sub

    Public Sub BuildStoredProc()
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

            Try
                'GetSQL()
                cmd.CommandText = String.Format(GetSQL()) '"Select * from vwCurrentPending")
                cmd.Connection = XpoDefault.Session.Connection
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                If ex.Message.ToLower().Contains("not found") Then
                    'MessageBox.Show("hey")
                End If
            End Try

        End Using
    End Sub

    Public Function GetSQL()

        Dim str As String = String.Empty

        Try
            Select Case frmType
                Case 1
                    str = "CREATE VIEW vwCurrentPending /* view_column_name, ... */" + vbCrLf
                    str = str + "as select X.Facility as FacilityNumber,X.OID as ConfigNumber, " + vbCrLf
                    str = str + "    --, B.'ConfigInstanceVersion', B.'PendingConfigInstanceVersion'" + vbCrLf
                    str = str + "    case when(X.OID = X.PendingConfigInstanceVersion and not X.ConfigInstanceVersion = X.PendingConfigInstanceVersion) then 'PENDING'" + vbCrLf
                    str = str + "    when(X.ConfigInstanceVersion = X.OID) then 'CURRENT' else 'ERROR'" + vbCrLf
                    str = str + "    end as ConfigType,X.LongName,X.FacilityID," + vbCrLf
                    'str = str + "    end as ConfigType,X.LongName,X.FacilityID,X.Enabled," + vbCrLf
                    str = str + "    X.Enabled," + vbCrLf
                    str = str + "    X.ActivatedDate,X.MajorChangeDate," + vbCrLf
                    str = str + "    --X.MajorChange, " + vbCrLf
                    str = str + "    --X.OptimisticLockField," + vbCrLf
                    str = str + "    X.ConfigInstanceVersion," + vbCrLf
                    str = str + "    X.PendingConfigInstanceVersion from" + vbCrLf
                    str = str + "    --, B.'ConfigInstanceVersion', B.'PendingConfigInstanceVersion'" + vbCrLf
                    str = str + "    (select A.OID," + vbCrLf
                    str = str + "      B.LongName," + vbCrLf
                    str = str + "      B.FacilityID," + vbCrLf
                    str = str + "      A.Enabled," + vbCrLf
                    str = str + "      A.Facility," + vbCrLf
                    str = str + "      A.ActivatedDate,A.MajorChangeDate,A.MajorChange," + vbCrLf
                    str = str + "      A.OptimisticLockField," + vbCrLf
                    str = str + "      B.ConfigInstanceVersion,B.PendingConfigInstanceVersion from" + vbCrLf
                    str = str + "      DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "      DBA.DOFacility as B on" + vbCrLf
                    str = str + "      A.Facility = B.OID join(" + vbCrLf
                    str = str + "      (select max(A.OID) as OID,A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and(b.ConfigInstanceVersion <> b.PendingConfigInstanceVersion) and not(a.activateddate is null)" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) union" + vbCrLf
                    str = str + "      (select max(A.OID),A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and b.ConfigInstanceVersion = b.PendingConfigInstanceVersion" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) union" + vbCrLf
                    str = str + "      (select max(A.OID),A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and(a.activateddate is null)" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) order by 2 asc) as C( OID,Facility) on" + vbCrLf
                    str = str + "      A.OID = C.OID where" + vbCrLf
                    str = str + "      A.Enabled = 1) as X( OID,LongName,FacilityID,Enabled," + vbCrLf
                    str = str + "      Facility,ActivatedDate,MajorChangeDate,MajorChange," + vbCrLf
                    str = str + "      OptimisticLockField,ConfigInstanceVersion,PendingConfigInstanceVersion) "
                Case 2
                    str = "CREATE VIEW vwCurrentPending /* view_column_name, ... */" + vbCrLf
                    str = str + "as select X.Facility as FacilityNumber,X.OID as ConfigNumber, " + vbCrLf
                    str = str + "    --, B.'ConfigInstanceVersion', B.'PendingConfigInstanceVersion'" + vbCrLf
                    str = str + "    case when(X.OID = X.PendingConfigInstanceVersion and not X.ConfigInstanceVersion = X.PendingConfigInstanceVersion) then 'PENDING'" + vbCrLf
                    str = str + "    when(X.ConfigInstanceVersion = X.OID) then 'CURRENT' else 'ERROR'" + vbCrLf
                    str = str + "    end as ConfigType,X.LongName,X.FacilityID," + vbCrLf
                    'str = str + "    end as ConfigType,X.LongName,X.FacilityID,X.Enabled," + vbCrLf
                    str = str + "    X.Enabled," + vbCrLf
                    str = str + "    X.ActivatedDate,X.MajorChangeDate," + vbCrLf
                    str = str + "    --X.MajorChange, " + vbCrLf
                    str = str + "    --X.OptimisticLockField," + vbCrLf
                    str = str + "    X.ConfigInstanceVersion," + vbCrLf
                    str = str + "    X.PendingConfigInstanceVersion from" + vbCrLf
                    str = str + "    --, B.'ConfigInstanceVersion', B.'PendingConfigInstanceVersion'" + vbCrLf
                    str = str + "    (select A.OID," + vbCrLf
                    str = str + "      B.LongName," + vbCrLf
                    str = str + "      B.FacilityID," + vbCrLf
                    str = str + "      A.Enabled," + vbCrLf
                    str = str + "      A.Facility," + vbCrLf
                    str = str + "      A.ActivatedDate,A.MajorChangeDate,A.MajorChange," + vbCrLf
                    str = str + "      A.OptimisticLockField," + vbCrLf
                    str = str + "      B.ConfigInstanceVersion,B.PendingConfigInstanceVersion from" + vbCrLf
                    str = str + "      DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "      DBA.DOFacility as B on" + vbCrLf
                    str = str + "      A.Facility = B.OID join(" + vbCrLf
                    str = str + "      (select max(A.OID) as OID,A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and(b.ConfigInstanceVersion <> b.PendingConfigInstanceVersion) and not(a.activateddate is null)" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) union" + vbCrLf
                    str = str + "      (select max(A.OID),A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and b.ConfigInstanceVersion = b.PendingConfigInstanceVersion" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) union" + vbCrLf
                    str = str + "      (select max(A.OID),A.Facility from" + vbCrLf
                    str = str + "        DBA.DOConfigInstance as A join" + vbCrLf
                    str = str + "        DBA.DOFacility as B on" + vbCrLf
                    str = str + "        A.Facility = B.OID where" + vbCrLf
                    str = str + "        A.Enabled = 1 and(a.activateddate is null)" + vbCrLf
                    str = str + "        group by a.facility order by" + vbCrLf
                    str = str + "        a.facility asc) order by 2 asc) as C( OID,Facility) on" + vbCrLf
                    str = str + "      A.OID = C.OID where" + vbCrLf
                    str = str + "      A.Enabled = 1) as X( OID,LongName,FacilityID,Enabled," + vbCrLf
                    str = str + "      Facility,ActivatedDate,MajorChangeDate,MajorChange," + vbCrLf
                    str = str + "      OptimisticLockField,ConfigInstanceVersion,PendingConfigInstanceVersion) "
                Case Else
                    Return String.Empty
            End Select

        Catch ex As Exception
            Return String.Empty
        End Try

        Return str

    End Function

    Public Sub GetAllConfigInstances()
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

            cmd.CommandText = String.Format("Select * from vwCurrentPending")
            cmd.Connection = XpoDefault.Session.Connection

            Dim table As New DataTable()

            Using reader As IDataReader = cmd.ExecuteReader()
                table.Load(reader)
                dataGridLicenses.DataSource = table
            End Using

        End Using
    End Sub

    Public Function GetAllConfigInstances2() As ArrayList
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

            cmd.CommandText = String.Format("Select * from vwCurrentPending")
            cmd.Connection = XpoDefault.Session.Connection


            Using reader As IDataReader = cmd.ExecuteReader()
                Dim arl1 As New ArrayList
                Do While reader.Read

                    Dim sc As New StringCollection
                    Dim arlHolder As New ArrayList

                    For i As Integer = 0 To reader.FieldCount - 1

                        Dim nvp As New NameValPair
                        If Not reader(i).Equals(DBNull.Value) Then
                            'sc.Add(reader(i))
                            nvp.Name = reader.GetName(i) ' reader.Item(i)
                            nvp.Val = reader(i)
                        Else
                            'sc.Add(String.Empty)
                            nvp.Name = reader.GetName(i) ' reader.Item(i)
                            nvp.Val = String.Empty 'reader(i)
                        End If
                        arlHolder.Add(nvp)
                    Next

                    arl1.Add(arlHolder)
                Loop

                Return arl1
            End Using
        End Using
    End Function

    Private Sub frmViewConfigInstances_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        If blNumLic Then
            If MessageBox.Show("Are you sure you want to Change the Number of Licenses ?", "", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                UpdateRecord("MaxLic", NumericUpDown1.Value)
            End If
        End If


        Me.Dispose()
    End Sub

    Private Sub frmViewConfigInstances_Disposed(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Disposed
        KillDS(3)
    End Sub

    Private Sub KillDS(ByVal i As Integer)

        Try
            Dim ds As New DataSet()
            ds.Clear()
            Select Case i
                Case 1
                    licDataTable = New DataTable()
                    dataGridLicenses.DataSource = ds
                Case 2
                    wrkStationDataTable = New DataTable()
                    dataGridWrkStations2.DataSource = ds
                Case 3
                    wrkStationDataTable = New DataTable()
                    licDataTable = New DataTable()
                    dataGridLicenses.DataSource = ds
                    dataGridWrkStations2.DataSource = ds
            End Select
        Catch ex As Exception

        End Try
    End Sub

    Private Sub dataGridLicenses_CellEndEdit(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles dataGridLicenses.CellEndEdit

        Select Case frmType
            Case 1
            Case 2
                Try
                    Dim str As String
                    str = dataGridLicenses.CurrentCell.Value 'dataGridLicenses.Rows(e.RowIndex).Cells(e.ColumnIndex).Value.ToString()
                    UpdateLicenses(dataGridLicenses.Columns(e.ColumnIndex).Name, str)
                    dataGridLicenses.UpdateCellValue(e.ColumnIndex, e.RowIndex)
                Catch ex As Exception

                End Try
        End Select

    End Sub

    Private Sub dataGridLicenses_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dataGridLicenses.MouseLeave

        Select Case frmType
            Case 1
            Case 2
                Try
                    Dim str As String
                    dataGridLicenses.EndEdit()
                    str = dataGridLicenses.CurrentCell.Value
                    UpdateLicenses(dataGridLicenses.Columns(dataGridLicenses.CurrentCell.ColumnIndex).Name, str)
                Catch ex As Exception

                End Try
        End Select

    End Sub

    Public Sub UpdateLicenses(ByVal col As String, ByVal val As String)

        Dim strCmd As String
        Select Case frmType
            Case 1
            Case 2
                Try
                    strCmd = "Update DODBLicValidation"
                    strCmd = strCmd + " Set " + col + " = " + val
                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.CommandText = String.Format(strCmd)
                        cmd.Connection = XpoDefault.Session.Connection
                        cmd.ExecuteNonQuery()
                    End Using

                Catch ex As Exception
                    strCmd = String.Empty
                End Try
        End Select

    End Sub

    Private Sub ImportLicenseFileMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ImportLicenseFileMenuItem.Click
        KillDS(1)
        frmType = 2
        OpenFile("DODBLicValidation.dat")
        'DoPages(EnabledPages.License)
        DropRecreateTable() 'wrkStationLst)
        LoadConfigInstances(2)
        tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(0)
    End Sub

    Private Sub RegisterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RegisterToolStripMenuItem.Click
        'KillDS(0)
    End Sub

    Private Sub ImportWorkStationFileMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ImportWorkStationFileMenuItem.Click
        KillDS(2)
        frmType = 3
        OpenFile("DOWLV.dat")
        'DoPages(EnabledPages.WorkStations)
        'DropRecreateTable(ListToDataTable(wrkStationLst))
        DropRecreateTable() 'wrkStationLst)
        LoadConfigInstances(3)
        DoPages(EnabledPages.WorkStations)
        tbCtrlLicenses.SelectedTab() = tbCtrlLicenses.TabPages(1)
    End Sub

    Private Sub OpenFile(ByVal str As String)

        OpenFileDialog1.Title = "Please Select a File"
        OpenFileDialog1.InitialDirectory = Application.ExecutablePath '"C:"
        'OpenFileDialog1.DefaultExt = "txt"
        OpenFileDialog1.FileName = str '"ImportConfig.dat"
        'OpenFileDialog1.Filter = "Text Files(*.txt)|*.*|Data Files(*.dat)|*.dat" '(*.doc)|(*.dat)"
        OpenFileDialog1.Filter = "Data Files(*.dat)|*.dat"
        OpenFileDialog1.ShowDialog()
    End Sub

    Private Sub OpenFileDialog1_FileOk(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles OpenFileDialog1.FileOk

        Dim strm As System.IO.Stream
        strm = OpenFileDialog1.OpenFile()

        LoadConfigList(OpenFileDialog1.FileName.ToString())

    End Sub

    Private Sub LoadConfigList(ByVal str As String) ', ByVal itype As Integer)
        Dim DelimiterChar As Char = ","c
        Dim Reader As New StreamReader(str)
        ' Declare DataColumn and DataRow variables. 
        'Dim column As DataColumn
        'Dim row As DataRow
        'Dim view As DataView


        Try

            Select Case frmType

                Case 2, 3
                    licList = New List(Of String())
                    While Not Reader.EndOfStream

                        Dim Line As String = Reader.ReadLine()
                        Dim Fields() As String = Line.Split(DelimiterChar)
                        licList.Add(Fields)
                    End While

                    Reader.Close()

                    ''Case 3

                    ''    licList = New List(Of String())
                    ''    While Not Reader.EndOfStream

                    ''        Dim Line As String = Reader.ReadLine()
                    ''        Dim Fields() As String = Line.Split(DelimiterChar)
                    ''        licList.Add(Fields)
                    ''    End While

                    ''    Reader.Close()
                Case 4

                    While Not Reader.EndOfStream
                        Dim Line As String = Reader.ReadLine()
                        Dim Fields() As String = Line.Split(DelimiterChar)

                        Select Case Fields(0)
                            Case "FormClassName"
                                'cbeFormClassaName.Text = Fields(1)
                            Case "ActivationDate"
                                Dim dt As New DateTimePicker
                                dt.Text = Fields(1)
                                'deActivationDate.DateTime = dt.Value
                                'deMajorChange.DateTime = dt.Value
                            Case "Comment"
                                'teComments.Text = Fields(1)
                        End Select

                    End While

                    Reader.Close()

            End Select






        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try

    End Sub


    Public Shared Function ListToDataTable(Of T)(ByVal list As List(Of T)) As DataTable
        Dim dt As New DataTable()

        For Each info As PropertyInfo In GetType(T).GetProperties()
            dt.Columns.Add(New DataColumn(info.Name, info.PropertyType))
        Next
        For Each tt As T In list
            Dim row As DataRow = dt.NewRow()
            For Each info As PropertyInfo In GetType(T).GetProperties()
                row(info.Name) = info.GetValue(tt, Nothing)
            Next
            dt.Rows.Add(row)
        Next
        Return dt
    End Function

    Private Sub DropRecreateTable()

        'I hate that we have to do this this way, but due to Sybase 9, DevExpress, and Microsoft's 
        'inability to work efficiently together, we had to cludge the SQL statements.
        'If we use SQL server, this would be greatly more efficient
        Try

            Select Case frmType

                Case 2


                    Dim blTrue As Boolean = False

                    Dim strStart As String = "INSERT INTO DODBLicValidation ([MaxLic],[Enabled])"
                    Dim strElse As String = " VALUES"
                    Dim strParaStrt As String = "("
                    Dim strParaEnd As String = ")"
                    Dim strCom As String = ","
                    Dim strParaSpc As String = " "
                    Dim strSemi As String = ";"


                    Try

                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                            cmd.CommandText = String.Format("SELECT 1 FROM sysobjects WHERE name = 'DODBLicValidation' AND type = 'U'")
                            cmd.Connection = XpoDefault.Session.Connection
                            Using reader As IDataReader = cmd.ExecuteReader()
                                Do While reader.Read
                                    blTrue = True
                                Loop
                            End Using

                            If Not blTrue Then

                                cmd.CommandText = String.Format("CREATE TABLE DODBLicValidation ([GCRecord] integer NULL," _
                                                    + " [OID] integer NOT NULL DEFAULT autoincrement, " _
                                                    + " [MaxLic] integer NULL," _
                                                    + " [Enabled] integer NULL," _
                                                    + " [OptimisticLockField] integer NULL," _
                                                    + " PRIMARY KEY ( OID ));")
                                cmd.ExecuteNonQuery()
                            End If

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try


                    If blTrue Then

                        Try
                            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                                cmd.CommandText = String.Format("DELETE DODBLicValidation")
                                cmd.Connection = XpoDefault.Session.Connection
                                cmd.ExecuteReader()
                                cmd.CommandText = String.Format("sa_reset_identity DODBLicValidation,dba,0")
                                cmd.ExecuteReader()
                            End Using

                        Catch ex As Exception
                            If ex.Message.ToLower().Contains("not found") Then
                                'MessageBox.Show("hey")
                            End If
                        End Try

                    End If

                    Try
                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

                            For Each lic As String() In licList
                                Dim strSQL As String = strStart
                                strSQL = strSQL + strElse + strParaSpc + strParaStrt + lic(0) + strCom + lic(1) + strParaEnd + strSemi '+ strCom + strParaSpc

                                cmd.CommandText = String.Format(strSQL)
                                cmd.ExecuteReader()
                            Next

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try

                Case 3

                    Dim blTrue As Boolean = False

                    Dim strStart As String = "INSERT INTO DOWLV ([WorkstationName],[Comment])"
                    Dim strElse As String = " VALUES"
                    Dim strParaStrt As String = "("
                    Dim strParaEnd As String = ")"
                    Dim strCom As String = ","
                    Dim strParaSpc As String = " "
                    Dim strSemi As String = ";"

                    Try

                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                            cmd.CommandText = String.Format("SELECT 1 FROM sysobjects WHERE name = 'DOWLV' AND type = 'U'")
                            cmd.Connection = XpoDefault.Session.Connection
                            Using reader As IDataReader = cmd.ExecuteReader()
                                Do While reader.Read
                                    blTrue = True
                                Loop
                            End Using

                            If Not blTrue Then

                                cmd.CommandText = String.Format("CREATE TABLE DOWLV ([GCRecord] integer NULL," _
                                                    + " [OID] integer NOT NULL DEFAULT autoincrement, " _
                                                    + " [WorkstationName] varchar(256) NULL," _
                                                    + " [Comment] varchar(156) NULL," _
                                                    + " [OptimisticLockField] integer NULL," _
                                                    + " PRIMARY KEY ( OID ));")
                                cmd.ExecuteNonQuery()
                            End If

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try



                    If blTrue Then

                        Try
                            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                                cmd.CommandText = String.Format("DELETE DOWLV")
                                cmd.Connection = XpoDefault.Session.Connection
                                cmd.ExecuteReader()
                                cmd.CommandText = String.Format("sa_reset_identity dowlv,dba,0")
                                cmd.ExecuteReader()
                            End Using

                        Catch ex As Exception
                            If ex.Message.ToLower().Contains("not found") Then
                                'MessageBox.Show("hey")
                            End If
                        End Try

                    End If

                    Try
                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

                            For Each lic As String() In licList
                                Dim strSQL As String = strStart
                                strSQL = strSQL + strElse + strParaSpc + strParaStrt + lic(0) + strCom + lic(1) + strParaEnd + strSemi
                                cmd.CommandText = String.Format(strSQL)
                                cmd.ExecuteReader()
                            Next

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try

                Case 4
                    Dim adapter As New AsaDataAdapter()
                    adapter.MissingMappingAction = MissingMappingAction.Passthrough
                    adapter.MissingSchemaAction = MissingSchemaAction.Add

                    adapter.InsertCommand = New AsaCommand("INSERT INTO DOWLV( WorkstationName, comment) " + _
                       " VALUES( @wrk_name, @comment)", XpoDefault.Session.Connection)
                    adapter.InsertCommand.UpdatedRowSource = UpdateRowSource.None
                    Dim parm As New AsaParameter("@wrk_name", AsaDbType.VarChar, 256)
                    parm.SourceColumn = "WorkstationName"
                    parm.SourceVersion = DataRowVersion.Current
                    adapter.InsertCommand.Parameters.Add(parm)
                    parm = New AsaParameter("@comment", AsaDbType.VarChar, 156)
                    parm.SourceColumn = "comment"
                    parm.SourceVersion = DataRowVersion.Current
                    adapter.InsertCommand.Parameters.Add(parm)
                    'adapter.Update(sourceTable)
            End Select


        Catch ex As Exception
            'strCmd = String.Empty
        End Try


    End Sub

    Private Sub DropRecreateTable(ByVal sourceTable As DataTable)


        Try

            Select Case frmType
                Case 3

                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand

                        Try
                            Select Case frmType
                                Case 1
                                    cmd.CommandText = String.Format("DROP VIEW vwCurrentPending") '"Select * from vwCurrentPending")
                                Case 2
                                    cmd.CommandText = String.Format("DROP VIEW vwCurrentPending") '"Select * from vwCurrentPending")
                            End Select

                            cmd.Connection = XpoDefault.Session.Connection
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            If ex.Message.ToLower().Contains("not found") Then
                                'MessageBox.Show("hey")
                            End If
                        End Try

                    End Using

                Case 4
                    Dim adapter As New AsaDataAdapter()
                    adapter.MissingMappingAction = MissingMappingAction.Passthrough
                    adapter.MissingSchemaAction = MissingSchemaAction.Add

                    adapter.InsertCommand = New AsaCommand("INSERT INTO DOWLV( WorkstationName, comment) " + _
                       " VALUES( @wrk_name, @comment)", XpoDefault.Session.Connection)
                    adapter.InsertCommand.UpdatedRowSource = UpdateRowSource.None
                    Dim parm As New AsaParameter("@wrk_name", AsaDbType.VarChar, 256)
                    parm.SourceColumn = "WorkstationName"
                    parm.SourceVersion = DataRowVersion.Current
                    adapter.InsertCommand.Parameters.Add(parm)
                    parm = New AsaParameter("@comment", AsaDbType.VarChar, 156)
                    parm.SourceColumn = "comment"
                    parm.SourceVersion = DataRowVersion.Current
                    adapter.InsertCommand.Parameters.Add(parm)
                    adapter.Update(sourceTable)
            End Select


        Catch ex As Exception
            'strCmd = String.Empty
        End Try


    End Sub

    Private Sub tbCtrlLicenses_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tbCtrlLicenses.SelectedIndexChanged

        Select Case Me.Text
            Case "View/Edit WorkStation Details"
                'SizeScreen(5)
            Case "View WorkStation Details"
                SizeScreen(5)
            Case Else
                SizeScreen(tbCtrlLicenses.SelectedIndex + 1)
        End Select


    End Sub

    Private Sub DoPages(ByVal ep As EnabledPages)

        Select Case ep

            Case EnabledPages.Config
                'EnablePage(TabControl1, TabControl1.TabPages(0), True)
                If hiddenPages.Count() = 0 Then
                    EnablePage(tbCtrlLicenses, tbCtrlLicenses.TabPages(1), False)
                End If
            Case EnabledPages.License
                If hiddenPages.Count() > 0 Then
                    EnablePage(tbCtrlLicenses, hiddenPages(0), True)
                End If
            Case EnabledPages.WorkStations
                If hiddenPages.Count() > 0 Then
                    EnablePage(tbCtrlLicenses, hiddenPages(0), True)
                End If
                'EnablePage(TabControl1, TabControl1.TabPages(0), True)
            Case EnabledPages.Extra

        End Select

    End Sub

    Private Sub EnablePage(ByVal tctl As TabControl, ByVal page As TabPage, ByVal enable As Boolean)
        If enable Then
            tctl.TabPages.Add(page)
            hiddenPages.Remove(page)
        Else
            tctl.TabPages.Remove(page)
            hiddenPages.Add(page)
        End If
    End Sub

    Protected Overrides Sub OnFormClosed(ByVal e As FormClosedEventArgs)
        For Each page As TabPage In hiddenPages
            page.Dispose()
        Next
        MyBase.OnFormClosed(e)
    End Sub

    Private Sub CheckBox1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.Click
        If CheckBox1.Checked Then
            If MessageBox.Show("Are you sure you want to Enable the Licenses?", "", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                UpdateRecord("Enabled", 1)
            End If
        Else
            If MessageBox.Show("Are you sure you want to Disable the Licenses?", "", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                UpdateRecord("Enabled", 0)
            End If
        End If
        HandleConfigs(4)
    End Sub

    Public Sub UpdateRecord(ByVal col As String, ByVal val As Integer, Optional ByVal Table As String = "DODBLicValidation")

        Dim strCmd As String = String.Empty
        Try

            Select Case col

                Case "MaxLic"

                    strCmd = "Update " + Table
                    strCmd = strCmd + " Set " + col + " = '" + Val.ToString() + "'"
                    blNumLic = False

                Case "Update"

                    strCmd = "Update " + Table
                    strCmd = strCmd + " Set Enabled = '" + CStr(CInt(Int(CheckBox1.Checked))) + "'"
                    strCmd = strCmd + ", MaxLic = '" + NumericUpDown1.Value.ToString() + "'"
                    blNumLic = False

                Case "Enabled"

                    strCmd = "Update " + Table
                    strCmd = strCmd + " Set " + col + " = '" + Val.ToString() + "'"

                    If blNumLic And col <> "MaxLic" Then
                        strCmd = strCmd + ", MaxLic = '" + NumericUpDown1.Value.ToString() + "'"
                        blNumLic = False
                    Else
                        blNumLic = False
                    End If

                Case Else

            End Select

            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = String.Format(strCmd)
                cmd.Connection = XpoDefault.Session.Connection
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            strCmd = String.Empty
        End Try

    End Sub

    Private Sub NumericUpDown1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles NumericUpDown1.ValueChanged
        blNumLic = True
    End Sub

    Private Sub frmViewConfigInstances_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Click

    End Sub

    Private Sub btnAddWrkStation_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddWrkStation.Click
        ModWorkStation.ShowDialog(Me)
        ModWorkStation.Dispose()
        HandleConfigs(4)
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub dataGridWrkStations_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles dataGridWrkStations.CellDoubleClick
        btnAddWrkStation_Click(Me, Nothing)
    End Sub

    Private Sub dataGridWrkStations_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles dataGridWrkStations.CellClick
        ModWorkStation.txtComment.Text = dataGridWrkStations.CurrentRow.Cells(3).Value
        ModWorkStation.txtWorkstationName.Text = dataGridWrkStations.CurrentRow.Cells(2).Value
        ModWorkStation.OID = dataGridWrkStations.CurrentRow.Cells(1).Value

        If dataGridWrkStations.CurrentRow.Cells(0).Value Is DBNull.Value Then
            ModWorkStation.chkEnabled.Checked = False
        Else
            ModWorkStation.chkEnabled.Checked = CBool(dataGridWrkStations.CurrentRow.Cells(0).Value)
        End If

    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If CheckSQL("Exists", "DODBLicValidation") Then
            UpdateRecord("Update", NumericUpDown1.Value)
        Else
            CheckSQL("Insert", "DODBLicValidation")
        End If

        HandleConfigs(4)
    End Sub


    Private Function CheckDOWLVExists(ByVal sType As String, ByVal Table As String) As Boolean


        Dim strCmd As String = String.Empty

        Try

            Select Case sType
                Case "Exists"

                    Dim blTrue As Boolean = False

                    Try

                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                            cmd.CommandText = String.Format("SELECT 1 FROM sysobjects WHERE name = '" + Table + "' AND type = 'U'")
                            cmd.Connection = XpoDefault.Session.Connection
                            Using reader As IDataReader = cmd.ExecuteReader()
                                Do While reader.Read
                                    blTrue = True
                                Loop
                            End Using

                            If Not blTrue Then

                                cmd.CommandText = String.Format("CREATE TABLE DOWLV ([GCRecord] integer NULL," _
                                                    + " [OID] integer NOT NULL DEFAULT autoincrement, " _
                                                    + " [WorkstationName] varchar(256) NULL," _
                                                    + " [Comment] varchar(156) NULL," _
                                                    + " [OptimisticLockField] integer NULL," _
                                                    + " PRIMARY KEY ( OID ));")
                                cmd.ExecuteNonQuery()

                                Return False

                            Else
                                blTrue = False
                                strCmd = "SELECT count(*) FROM " + Table

                                cmd.CommandText = String.Format(strCmd)
                                cmd.Connection = XpoDefault.Session.Connection

                                Using reader As IDataReader = cmd.ExecuteReader()
                                    Do While reader.Read
                                        Return True
                                    Loop
                                End Using

                            End If

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try


            End Select

        Catch ex As Exception
            Return False
        End Try

        Return True

    End Function

    Private Function CheckSQL(ByVal sType As String, ByVal Table As String) As Boolean ', Optional ByVal Table As String = "DODBLicValidation") As Boolean


        Dim strCmd As String = String.Empty


        Try

            Select Case sType
                Case "Insert"

                    strCmd = "INSERT INTO " + Table + "([MaxLic],[Enabled])VALUES("
                    strCmd = strCmd + "'" + NumericUpDown1.Value.ToString() + "',"
                    strCmd = strCmd + "'" + CStr(CInt(Int(CheckBox1.Checked))) + "')"

                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.Connection = XpoDefault.Session.Connection
                        cmd.CommandText = String.Format(strCmd)
                        cmd.ExecuteReader()
                        Return True
                    End Using
                Case "Delete"
                Case "Update"

                    strCmd = "Update " + Table
                    strCmd = strCmd + " Set Enabled = '" + CStr(CInt(Int(CheckBox1.Checked))) + "'"
                    strCmd = strCmd + ", MaxLic = '" + NumericUpDown1.Value.ToString() + "'"


                Case "reset_identity"
                    ''strCmd = String.Format("sa_reset_identity DOWLV,dba,0")
                Case "Exists"

                    Dim blTrue As Boolean = False

                    Try

                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                            cmd.CommandText = String.Format("SELECT 1 FROM sysobjects WHERE name = 'DODBLicValidation' AND type = 'U'")
                            cmd.Connection = XpoDefault.Session.Connection
                            Using reader As IDataReader = cmd.ExecuteReader()
                                Do While reader.Read
                                    blTrue = True
                                Loop
                            End Using

                            If Not blTrue Then

                                cmd.CommandText = String.Format("CREATE TABLE DODBLicValidation ([GCRecord] integer NULL," _
                                                    + " [OID] integer NOT NULL DEFAULT autoincrement, " _
                                                    + " [MaxLic] integer NULL," _
                                                    + " [Enabled] integer NULL," _
                                                    + " [OptimisticLockField] integer NULL," _
                                                    + " PRIMARY KEY ( OID ));")
                                cmd.ExecuteNonQuery()

                                Return False

                            Else
                                blTrue = False
                                strCmd = "SELECT count(*) FROM " + Table

                                cmd.CommandText = String.Format(strCmd)
                                cmd.Connection = XpoDefault.Session.Connection

                                Using reader As IDataReader = cmd.ExecuteReader()
                                    Do While reader.Read
                                        If reader.GetValue(0) > 0 Then
                                            Return True
                                        Else
                                            Return False
                                        End If
                                    Loop
                                End Using

                            End If

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try


            End Select

        Catch ex As Exception
            Return False
        End Try

        Return True

    End Function


    Private Sub WorkStationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WorkStationToolStripMenuItem.Click
        ModWorkStation.ShowDialog(Me)
        ModWorkStation.Dispose()
        HandleConfigs(4)
    End Sub

    Private Sub AddWorkStationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles AddWorkStationToolStripMenuItem.Click
        ModWorkStation.modType = ModWorkStation.FormType.Add
        ModWorkStation.ShowDialog(Me)
        ModWorkStation.Dispose()
        HandleConfigs(4)
    End Sub

    Private Sub EditWorkStationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EditWorkStationToolStripMenuItem.Click
        ModWorkStation.modType = ModWorkStation.FormType.Edit
        ModWorkStation.txtComment.Text = dataGridWrkStations.CurrentRow.Cells(3).Value
        ModWorkStation.txtWorkstationName.Text = dataGridWrkStations.CurrentRow.Cells(2).Value
        ModWorkStation.OID = dataGridWrkStations.CurrentRow.Cells(1).Value

        If dataGridWrkStations.CurrentRow.Cells(0).Value Is DBNull.Value Then
            ModWorkStation.chkEnabled.Checked = False
        Else
            ModWorkStation.chkEnabled.Checked = CBool(dataGridWrkStations.CurrentRow.Cells(0).Value)
        End If

        ModWorkStation.ShowDialog(Me)
        ModWorkStation.Dispose()
        HandleConfigs(4)
    End Sub

    Private Sub DeleteWorkStationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DeleteWorkStationToolStripMenuItem.Click
        ModWorkStation.modType = ModWorkStation.FormType.Delete
        ModWorkStation.txtComment.Text = dataGridWrkStations.CurrentRow.Cells(3).Value
        ModWorkStation.txtWorkstationName.Text = dataGridWrkStations.CurrentRow.Cells(2).Value
        ModWorkStation.OID = dataGridWrkStations.CurrentRow.Cells(1).Value

        If dataGridWrkStations.CurrentRow.Cells(0).Value Is DBNull.Value Then
            ModWorkStation.chkEnabled.Checked = False
        Else
            ModWorkStation.chkEnabled.Checked = CBool(dataGridWrkStations.CurrentRow.Cells(0).Value)
        End If

        ModWorkStation.ShowDialog(Me)
        ModWorkStation.Dispose()
        HandleConfigs(4)
    End Sub

    Private cellX As Integer = 0
    Private cellY As Integer = 0
    ''Private prevCellX As Integer = 0
    ''Private prevCellY As Integer = 0

    Private Sub dataGridWrkStations_CellMouseMove(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles dataGridWrkStations.CellMouseMove
        Dim grvScreenLocation As Point = dataGridWrkStations.PointToScreen(dataGridWrkStations.Location)
        Dim tempX As Integer = DataGridView.MousePosition.X - grvScreenLocation.X + dataGridWrkStations.Left
        Dim tempY As Integer = DataGridView.MousePosition.Y - grvScreenLocation.Y + dataGridWrkStations.Top
        Dim objHitTestInfo As DataGridView.HitTestInfo = dataGridWrkStations.HitTest(tempX, tempY)

        cellX = objHitTestInfo.RowIndex
        cellY = objHitTestInfo.ColumnIndex
        'txtInfo.Text = "( X = " & cellX & ", Y = " & cellY & ")"
        ''If (prevCellX <> -1 And prevCellY <> -1) Then
        ''    Me.dataGridWrkStations.Rows(prevCellX).Cells(prevCellY).Selected = False
        ''    'Me.dataGridWrkStations.SelectionMode = DataGridViewSelectionMode.CellSelect
        ''End If
        Me.dataGridWrkStations.ClearSelection()
        If (cellX <> -1 And cellY <> -1) Then
            Me.dataGridWrkStations.CurrentCell = Me.dataGridWrkStations.Rows(cellX).Cells(cellY)
            Me.dataGridWrkStations.Rows(cellX).Cells(cellY).Selected = True
            Me.dataGridWrkStations.Rows(cellX).Selected = True
            Me.dataGridWrkStations.SelectionMode = DataGridViewSelectionMode.CellSelect
        End If
        ''prevCellX = cellX
        ''prevCellY = cellY
    End Sub

    Private Sub ToolStripMenuItem11_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem11.Click
        ImportWorkStationFileMenuItem_Click(Me, Nothing)
        HandleConfigs(4)
    End Sub


End Class

Public Class NameValPair
    Private _name As String = String.Empty
    Public Property Name() As String
        Get
            Return _name
        End Get
        Set(ByVal value As String)
            _name = value
        End Set
    End Property

    Private _val As String = String.Empty
    Public Property Val() As String
        Get
            Return _val
        End Get
        Set(ByVal value As String)
            _val = value
        End Set
    End Property

End Class

Public Class clsLicense
    Private _MaxLic As String = String.Empty
    Public Property MaxLic() As String
        Get
            Return _MaxLic
        End Get
        Set(ByVal value As String)
            _MaxLic = value
        End Set
    End Property

    Private _Enabled As String = String.Empty
    Public Property Enabled() As String
        Get
            Return _Enabled
        End Get
        Set(ByVal Enabledue As String)
            _Enabled = Enabledue
        End Set
    End Property

End Class

''' <summary>
''' Levels of importance.
''' </summary>
Public Enum EnabledPages
    Config = 0
    License = 1
    WorkStations = 2
    Extra = 3
End Enum
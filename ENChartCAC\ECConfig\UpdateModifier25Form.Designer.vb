﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class UpdateModifier25Form
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Status2Label = New System.Windows.Forms.Label()
        Me.StatusLabel = New System.Windows.Forms.Label()
        Me.CloseButton = New System.Windows.Forms.Button()
        Me.CancelBtn = New System.Windows.Forms.Button()
        Me.<PERSON>ad<PERSON>utton = New System.Windows.Forms.Button()
        Me.ChooseFileButton = New System.Windows.Forms.Button()
        Me.ChooseFileTextBox = New System.Windows.Forms.TextBox()
        Me.ProgressBar1 = New System.Windows.Forms.ProgressBar()
        Me.tb1 = New System.Windows.Forms.RichTextBox()
        Me.cbSimulate = New System.Windows.Forms.CheckBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.SuspendLayout()
        '
        'Status2Label
        '
        Me.Status2Label.AutoSize = True
        Me.Status2Label.Location = New System.Drawing.Point(23, 80)
        Me.Status2Label.Name = "Status2Label"
        Me.Status2Label.Size = New System.Drawing.Size(37, 13)
        Me.Status2Label.TabIndex = 28
        Me.Status2Label.Text = "Status"
        '
        'StatusLabel
        '
        Me.StatusLabel.AutoSize = True
        Me.StatusLabel.Location = New System.Drawing.Point(345, 80)
        Me.StatusLabel.Name = "StatusLabel"
        Me.StatusLabel.Size = New System.Drawing.Size(21, 13)
        Me.StatusLabel.TabIndex = 27
        Me.StatusLabel.Text = "0%"
        '
        'CloseButton
        '
        Me.CloseButton.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.CloseButton.Location = New System.Drawing.Point(670, 100)
        Me.CloseButton.Name = "CloseButton"
        Me.CloseButton.Size = New System.Drawing.Size(75, 23)
        Me.CloseButton.TabIndex = 26
        Me.CloseButton.Text = "Close"
        Me.CloseButton.UseVisualStyleBackColor = True
        '
        'CancelBtn
        '
        Me.CancelBtn.Enabled = False
        Me.CancelBtn.Location = New System.Drawing.Point(585, 100)
        Me.CancelBtn.Name = "CancelBtn"
        Me.CancelBtn.Size = New System.Drawing.Size(75, 23)
        Me.CancelBtn.TabIndex = 25
        Me.CancelBtn.Text = "Cancel"
        Me.CancelBtn.UseVisualStyleBackColor = True
        '
        'LoadButton
        '
        Me.LoadButton.Enabled = False
        Me.LoadButton.Location = New System.Drawing.Point(500, 100)
        Me.LoadButton.Name = "LoadButton"
        Me.LoadButton.Size = New System.Drawing.Size(75, 23)
        Me.LoadButton.TabIndex = 24
        Me.LoadButton.Text = "Start"
        Me.LoadButton.UseVisualStyleBackColor = True
        '
        'ChooseFileButton
        '
        Me.ChooseFileButton.Location = New System.Drawing.Point(672, 18)
        Me.ChooseFileButton.Name = "ChooseFileButton"
        Me.ChooseFileButton.Size = New System.Drawing.Size(75, 23)
        Me.ChooseFileButton.TabIndex = 23
        Me.ChooseFileButton.Text = "Choose File"
        Me.ChooseFileButton.UseVisualStyleBackColor = True
        '
        'ChooseFileTextBox
        '
        Me.ChooseFileTextBox.Location = New System.Drawing.Point(26, 21)
        Me.ChooseFileTextBox.Name = "ChooseFileTextBox"
        Me.ChooseFileTextBox.Size = New System.Drawing.Size(633, 20)
        Me.ChooseFileTextBox.TabIndex = 22
        '
        'ProgressBar1
        '
        Me.ProgressBar1.Location = New System.Drawing.Point(26, 54)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New System.Drawing.Size(719, 23)
        Me.ProgressBar1.TabIndex = 21
        '
        'tb1
        '
        Me.tb1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tb1.Location = New System.Drawing.Point(26, 168)
        Me.tb1.Name = "tb1"
        Me.tb1.Size = New System.Drawing.Size(721, 407)
        Me.tb1.TabIndex = 30
        Me.tb1.Text = ""
        Me.tb1.WordWrap = False
        '
        'cbSimulate
        '
        Me.cbSimulate.AutoSize = True
        Me.cbSimulate.Location = New System.Drawing.Point(26, 105)
        Me.cbSimulate.Name = "cbSimulate"
        Me.cbSimulate.Size = New System.Drawing.Size(70, 17)
        Me.cbSimulate.TabIndex = 31
        Me.cbSimulate.Text = "*Simulate"
        Me.cbSimulate.UseVisualStyleBackColor = True
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(26, 129)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(184, 13)
        Me.Label1.TabIndex = 32
        Me.Label1.Text = "*Simulate will not modify the database"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(26, 142)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(680, 13)
        Me.Label2.TabIndex = 33
        Me.Label2.Text = "*Simulate will show which records will be updated. The mod25 value displayed duri" &
    "ng simulation is the current value, that needs to be changed."
        '
        'UpdateModifier25Form
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(769, 587)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.cbSimulate)
        Me.Controls.Add(Me.tb1)
        Me.Controls.Add(Me.Status2Label)
        Me.Controls.Add(Me.StatusLabel)
        Me.Controls.Add(Me.CloseButton)
        Me.Controls.Add(Me.CancelBtn)
        Me.Controls.Add(Me.LoadButton)
        Me.Controls.Add(Me.ChooseFileButton)
        Me.Controls.Add(Me.ChooseFileTextBox)
        Me.Controls.Add(Me.ProgressBar1)
        Me.Name = "UpdateModifier25Form"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Update Mod25 for ESPCodes"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Status2Label As Label
    Friend WithEvents StatusLabel As Label
    Friend WithEvents CloseButton As Button
    Friend WithEvents CancelBtn As Button
    Friend WithEvents LoadButton As Button
    Friend WithEvents ChooseFileButton As Button
    Friend WithEvents ChooseFileTextBox As TextBox
    Friend WithEvents ProgressBar1 As ProgressBar
    Friend WithEvents tb1 As RichTextBox
    Friend WithEvents cbSimulate As CheckBox
    Friend WithEvents Label1 As Label
    Friend WithEvents Label2 As Label
End Class

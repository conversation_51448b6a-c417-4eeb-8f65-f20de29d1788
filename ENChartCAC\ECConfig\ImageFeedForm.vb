﻿Imports DevExpress.Xpo
Imports System.Configuration
Imports System.Xml

Public Class ImageFeedForm
    Private Sub ImageFeedForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        LoadModuleList()
        SelectCurrentModule()
    End Sub

    Private Sub LoadModuleList()
        cboModule.Properties.Items.Clear()
        cboModule.Properties.Items.Add(New NoImageFeedModule)
        cboModule.Properties.Items.Add(New OneContentModule)
        cboModule.Properties.Items.Add(New HylandOnBaseModule)
        cboModule.Properties.Items.Add(New MeditechModule)
        cboModule.Properties.Items.Add(New OneContentFilenameModule)
    End Sub

    ''' <summary>
    ''' This sub will determine whether or not the Image Feed is currently enabled. If not, the NoImageFeedModule option will be selected. If so, 
    ''' this sub will check the current Image Feed configuration to determine which option to select.
    ''' </summary>
    Private Sub SelectCurrentModule()
        Dim isIFEnabledForAllFacilities As Boolean = True
        ' First iterate through all facilities to see if the Image Feed is disabled for any. This tool assumes all or nothing when enabling/disabling
        ' the Image Feed as this has been the setup for all AIC Image Feeds.


        Dim facilities As New XPCollection(Of DOFacility)
        For Each fac As DOFacility In facilities

            Dim configGroup As DOConfigGroup = fac.ConfigInstanceVersion.GetConfigSettingGroup("Application")
            If Not configGroup("EnableColdFeed").Enabled Then
                isIFEnabledForAllFacilities = False
            End If
        Next
        ' If it is enabled for all facilities, determine which facility module is being used by the combination of the document handler and index
        ' handler. The combination of these values is unique for each module.

        If isIFEnabledForAllFacilities Then
            Dim xdoc As New XmlDocument()
            xdoc.XmlResolver = Nothing
            xdoc.Load(".\ColdFeedExport.exe.config")

            Dim moduleMatched As Boolean = False
            Dim nodeImageFeedMode As XmlNode

            Try
                nodeImageFeedMode = xdoc.SelectSingleNode("/configuration/applicationSettings/ColdFeedExport.My.MySettings/setting[@name =""ImageFeedMode""]/value")

                For Each m As IImageFeedModule In cboModule.Properties.Items
                    If (m.ImageFeedMode.ToUpper = nodeImageFeedMode.InnerText.ToUpper) Then
                        cboModule.SelectedItem = m
                        moduleMatched = True
                    End If
                Next
            Catch

            End Try

            If Not moduleMatched Then cboModule.SelectedIndex = 0
        Else
            cboModule.SelectedIndex = 0
        End If
    End Sub

    Private Sub ToggleImageFeedEnabled(ByVal enable As Boolean)
        Dim facilities As New XPCollection(Of DOFacility)
        For Each fac As DOFacility In facilities
            Dim configGroup As DOConfigGroup = fac.ConfigInstanceVersion.GetConfigSettingGroup("Application")
            configGroup("EnableColdFeed").Enabled = enable
            configGroup("EnableColdFeed").Save()
        Next
    End Sub

    Private Function SaveConfiguration() As Boolean
        Dim selectedModule As IImageFeedModule = cboModule.SelectedItem

        Dim isConfigSaved As Boolean = selectedModule.SaveConfiguration()
        Dim isMappingsSaved As Boolean = selectedModule.SaveMappings()

        Return isConfigSaved And isMappingsSaved
    End Function

    Private Sub cboModule_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboModule.SelectedIndexChanged
        Dim selectedModule As IImageFeedModule = cboModule.SelectedItem

        fpnlModule.Controls.Clear()
        fpnlModule.Controls.Add(selectedModule)
        selectedModule.LoadConfiguration()
        selectedModule.LoadMappings()

        ToggleImageFeedEnabled(Not selectedModule.ModuleName = "No Image Feed")
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        If SaveConfiguration() Then
            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub
End Class
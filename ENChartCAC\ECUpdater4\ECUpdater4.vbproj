﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputType>WinExe</OutputType>
    <StartupObject>ECUpdater.My.MyApplication</StartupObject>
    <RootNamespace>ECUpdater</RootNamespace>
    <MyType>WindowsForms</MyType>
    
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>

    <!--<Configurations>Debug;Release;</Configurations>
    <Platforms>AnyCPU;</Platforms>-->
    
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>false</UseWPF>

    <!--<SelfContained>false</SelfContained>-->

    <!--<PublishSingleFile>true</PublishSingleFile>
    --><!--<PublishTrimmed>true</PublishTrimmed>--><!--
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    --><!--<EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>--><!--
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <SuppressTrimAnalysisWarnings>true</SuppressTrimAnalysisWarnings>
    --><!--<NoWarn>$(NoWarn);CA2007</NoWarn>-->
  
  </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
   
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='JJCDebug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
 
  </PropertyGroup>
   <ItemGroup>
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
   <ItemGroup>
     <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
   </ItemGroup>
  <ItemGroup>
    <!--<Compile Update="MaintenanceModeMsgForm.Designer.vb">
      <DependentUpon>MaintenanceModeMsgForm.vb</DependentUpon>
    </Compile>-->
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <!--<ItemGroup Condition=" '$(Configuration)' != 'Release' ">
    <PackageReference Include="DevExpress.Win.Design" Version="23.1.5" />
  </ItemGroup>-->
  <!--<ItemGroup >
    <PackageReference Include="DevExpress.Win.Design" Version="23.1.5" />
  </ItemGroup>-->
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EnchartDOLib\EnchartDOLib.vbproj" />
  </ItemGroup>
  <!--<ItemGroup>
    <Reference Include="DevExpress.Drawing.v23.1">
      
    </Reference>
    <Reference Include="DevExpress.Printing.v23.1.Core">
      
    </Reference>
    <Reference Include="DevExpress.Data.v23.1">
      
    </Reference>
    <Reference Include="DevExpress.Utils.v23.1">
      
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v23.1">
      
    </Reference>
  </ItemGroup>-->
  <!--<PropertyGroup>
    <PostBuildEvent>copy "$(TargetPath)"  "$(SolutionDir)ENChartCAC\bin\Release\$(TargetFileName)"
copy "$(TargetPath)"  "C:\apps\ECCoder\$(TargetFileName)"</PostBuildEvent>
  </PropertyGroup>-->

  <Import Project="..\..\build\CustomPostBuild.targets" />
</Project>
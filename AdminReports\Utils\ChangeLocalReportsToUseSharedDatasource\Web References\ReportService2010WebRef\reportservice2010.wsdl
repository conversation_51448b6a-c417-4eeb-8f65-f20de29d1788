<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">The Reporting Services Web Service enables you to manage a report server and its contents including server settings, security, reports, subscriptions, and data sources.</wsdl:documentation>
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer">
      <s:element name="CreateCatalogItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="Overwrite" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfProperty">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Property" nillable="true" type="tns:Property" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Property">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Value" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateCatalogItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemInfo" type="tns:CatalogItem" />
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="CatalogItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Path" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="VirtualPath" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TypeName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Size" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Hidden" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="CreationDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="ModifiedDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CreatedBy" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ModifiedBy" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ItemMetadata" type="tns:ArrayOfProperty" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfWarning">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Warning" nillable="true" type="tns:Warning" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Warning">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Code" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Severity" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ObjectName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ObjectType" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Message" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="TrustedUserHeader" type="tns:TrustedUserHeader" />
      <s:complexType name="TrustedUserHeader">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="UserToken" type="s:base64Binary" />
        </s:sequence>
        <s:anyAttribute />
      </s:complexType>
      <s:element name="ServerInfoHeader" type="tns:ServerInfoHeader" />
      <s:complexType name="ServerInfoHeader">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ReportServerVersionNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReportServerEdition" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReportServerVersion" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReportServerDateTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReportServerTimeZoneInfo" type="tns:TimeZoneInformation" />
        </s:sequence>
        <s:anyAttribute />
      </s:complexType>
      <s:complexType name="TimeZoneInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Bias" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="StandardBias" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="StandardDate" type="tns:SYSTEMTIME" />
          <s:element minOccurs="1" maxOccurs="1" name="DaylightBias" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="DaylightDate" type="tns:SYSTEMTIME" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="SYSTEMTIME">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="year" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="month" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="dayOfWeek" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="day" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="hour" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="minute" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="second" type="s:short" />
          <s:element minOccurs="1" maxOccurs="1" name="milliseconds" type="s:short" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetItemDefinition">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemDefinitionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemDefinition">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemDefinitionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="s:base64Binary" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemType">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemTypeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Type" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteItemResponse">
        <s:complexType />
      </s:element>
      <s:element name="MoveItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Target" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MoveItemResponse">
        <s:complexType />
      </s:element>
      <s:element name="InheritParentSecurity">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="InheritParentSecurityResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListItemHistory">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListItemHistoryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemHistory" type="tns:ArrayOfItemHistorySnapshot" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfItemHistorySnapshot">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ItemHistorySnapshot" nillable="true" type="tns:ItemHistorySnapshot" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ItemHistorySnapshot">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="HistoryID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CreationDate" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="Size" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListChildren">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="Recursive" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListChildrenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CatalogItems" type="tns:ArrayOfCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCatalogItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CatalogItem" nillable="true" type="tns:CatalogItem" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListDependentItems">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListDependentItemsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CatalogItems" type="tns:ArrayOfCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FindItems">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Folder" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="BooleanOperator" type="tns:BooleanOperatorEnum" />
            <s:element minOccurs="0" maxOccurs="1" name="SearchOptions" type="tns:ArrayOfProperty" />
            <s:element minOccurs="0" maxOccurs="1" name="SearchConditions" type="tns:ArrayOfSearchCondition" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="BooleanOperatorEnum">
        <s:restriction base="s:string">
          <s:enumeration value="And" />
          <s:enumeration value="Or" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfSearchCondition">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="SearchCondition" nillable="true" type="tns:SearchCondition" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="SearchCondition">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Condition" type="tns:ConditionEnum" />
          <s:element minOccurs="0" maxOccurs="1" name="Values" type="tns:ArrayOfString" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ConditionEnum">
        <s:restriction base="s:string">
          <s:enumeration value="Contains" />
          <s:enumeration value="Equals" />
          <s:enumeration value="In" />
          <s:enumeration value="Between" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Value" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="FindItemsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Items" type="tns:ArrayOfCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListParents">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListParentsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListParentsResult" type="tns:ArrayOfCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateFolder">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Folder" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateFolderResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemInfo" type="tns:CatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetPropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetPropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Values" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ItemNamespaceHeader" type="tns:ItemNamespaceHeader" />
      <s:complexType name="ItemNamespaceHeader">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ItemNamespace" type="tns:ItemNamespaceEnum" />
        </s:sequence>
        <s:anyAttribute />
      </s:complexType>
      <s:simpleType name="ItemNamespaceEnum">
        <s:restriction base="s:string">
          <s:enumeration value="PathBased" />
          <s:enumeration value="GUIDBased" />
        </s:restriction>
      </s:simpleType>
      <s:element name="SetItemReferences">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ItemReferences" type="tns:ArrayOfItemReference" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfItemReference">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ItemReference" nillable="true" type="tns:ItemReference" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ItemReference">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetItemReferencesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemReferences">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ReferenceItemType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemReferencesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemReferences" type="tns:ArrayOfItemReferenceData" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfItemReferenceData">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ItemReferenceData" nillable="true" type="tns:ItemReferenceData" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ItemReferenceData">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReferenceType" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListItemTypes">
        <s:complexType />
      </s:element>
      <s:element name="ListItemTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListItemTypesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString1">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetSubscriptionProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ExtensionSettings">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ParameterValues" type="tns:ArrayOfChoice1" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfChoice1">
        <s:choice minOccurs="0" maxOccurs="unbounded">
          <s:element minOccurs="1" maxOccurs="1" name="ParameterFieldReference" nillable="true" type="tns:ParameterFieldReference" />
          <s:element minOccurs="1" maxOccurs="1" name="ParameterValue" nillable="true" type="tns:ParameterValue" />
        </s:choice>
      </s:complexType>
      <s:complexType name="ParameterFieldReference">
        <s:complexContent mixed="false">
          <s:extension base="tns:ParameterValueOrFieldReference">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ParameterName" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="FieldAlias" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ParameterValueOrFieldReference" />
      <s:complexType name="ParameterValue">
        <s:complexContent mixed="false">
          <s:extension base="tns:ParameterValueOrFieldReference">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="Value" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="Label" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfParameterValue">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ParameterValue" nillable="true" type="tns:ParameterValue" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetSubscriptionPropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetSubscriptionProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSubscriptionPropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Owner" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Active" type="tns:ActiveState" />
            <s:element minOccurs="0" maxOccurs="1" name="Status" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ActiveState">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DeliveryExtensionRemoved" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="SharedDataSourceRemoved" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="MissingParameterValue" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="InvalidParameterValue" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="UnknownReportParameter" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="DisabledByUser" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetDataDrivenSubscriptionProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataDrivenSubscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="DataRetrievalPlan" type="tns:DataRetrievalPlan" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValueOrFieldReference" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="DataRetrievalPlan">
        <s:sequence>
          <s:choice minOccurs="1" maxOccurs="1">
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceDefinition" type="tns:DataSourceDefinition" />
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceReference" type="tns:DataSourceReference" />
            <s:element minOccurs="0" maxOccurs="1" name="InvalidDataSourceReference" type="tns:InvalidDataSourceReference" />
          </s:choice>
          <s:element minOccurs="0" maxOccurs="1" name="DataSet" type="tns:DataSetDefinition" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="DataSourceDefinition">
        <s:complexContent mixed="false">
          <s:extension base="tns:DataSourceDefinitionOrReference">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="ConnectString" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="UseOriginalConnectString" type="s:boolean" />
              <s:element minOccurs="1" maxOccurs="1" name="OriginalConnectStringExpressionBased" type="s:boolean" />
              <s:element minOccurs="1" maxOccurs="1" name="CredentialRetrieval" type="tns:CredentialRetrievalEnum" />
              <s:element minOccurs="1" maxOccurs="1" name="WindowsCredentials" type="s:boolean" />
              <s:element minOccurs="0" maxOccurs="1" name="ImpersonateUser" type="s:boolean" />
              <s:element minOccurs="0" maxOccurs="1" name="Prompt" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="Enabled" type="s:boolean" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="DataSourceDefinitionOrReference" />
      <s:complexType name="InvalidDataSourceReference">
        <s:complexContent mixed="false">
          <s:extension base="tns:DataSourceDefinitionOrReference" />
        </s:complexContent>
      </s:complexType>
      <s:complexType name="DataSourceReference">
        <s:complexContent mixed="false">
          <s:extension base="tns:DataSourceDefinitionOrReference">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:simpleType name="CredentialRetrievalEnum">
        <s:restriction base="s:string">
          <s:enumeration value="Prompt" />
          <s:enumeration value="Store" />
          <s:enumeration value="Integrated" />
          <s:enumeration value="None" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DataSetDefinition">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Fields" type="tns:ArrayOfField" />
          <s:element minOccurs="0" maxOccurs="1" name="Query" type="tns:QueryDefinition" />
          <s:element minOccurs="0" maxOccurs="1" name="CaseSensitivity" type="tns:SensitivityEnum" />
          <s:element minOccurs="0" maxOccurs="1" name="Collation" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AccentSensitivity" type="tns:SensitivityEnum" />
          <s:element minOccurs="0" maxOccurs="1" name="KanatypeSensitivity" type="tns:SensitivityEnum" />
          <s:element minOccurs="0" maxOccurs="1" name="WidthSensitivity" type="tns:SensitivityEnum" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfField">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Field" nillable="true" type="tns:Field" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Field">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Alias" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="QueryDefinition">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CommandType" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CommandText" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Timeout" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="SensitivityEnum">
        <s:restriction base="s:string">
          <s:enumeration value="True" />
          <s:enumeration value="False" />
          <s:enumeration value="Auto" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfParameterValueOrFieldReference">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ParameterValueOrFieldReference" nillable="true" type="tns:ParameterValueOrFieldReference" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetDataDrivenSubscriptionPropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetDataDrivenSubscriptionProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataDrivenSubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDataDrivenSubscriptionPropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Owner" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="DataRetrievalPlan" type="tns:DataRetrievalPlan" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Active" type="tns:ActiveState" />
            <s:element minOccurs="0" maxOccurs="1" name="Status" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValueOrFieldReference" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DisableSubscription">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DisableSubscriptionResponse">
        <s:complexType />
      </s:element>
      <s:element name="EnableSubscription">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnableSubscriptionResponse">
        <s:complexType />
      </s:element>
      <s:element name="DeleteSubscription">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteSubscriptionResponse">
        <s:complexType />
      </s:element>
      <s:element name="CreateSubscription">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateSubscriptionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDataDrivenSubscription">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionSettings" type="tns:ExtensionSettings" />
            <s:element minOccurs="0" maxOccurs="1" name="DataRetrievalPlan" type="tns:DataRetrievalPlan" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValueOrFieldReference" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDataDrivenSubscriptionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetExtensionSettings">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetExtensionSettingsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionParameters" type="tns:ArrayOfExtensionParameter" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfExtensionParameter">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ExtensionParameter" nillable="true" type="tns:ExtensionParameter" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ExtensionParameter">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DisplayName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Required" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="ReadOnly" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Value" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Error" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Encrypted" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="IsPassword" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="ValidValues" type="tns:ArrayOfValidValue" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfValidValue">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Value" nillable="true" type="tns:ValidValue" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ValidValue">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Label" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Value" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="ValidateExtensionSettings">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ParameterValues" type="tns:ArrayOfParameterValueOrFieldReference" />
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ValidateExtensionSettingsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ParameterErrors" type="tns:ArrayOfExtensionParameter" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSubscriptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPathOrSiteURL" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSubscriptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionItems" type="tns:ArrayOfSubscription" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfSubscription">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Subscription" nillable="true" type="tns:Subscription" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Subscription">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Owner" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Path" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="VirtualPath" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Report" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DeliverySettings" type="tns:ExtensionSettings" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Status" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Active" type="tns:ActiveState" />
          <s:element minOccurs="0" maxOccurs="1" name="LastExecuted" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="ModifiedBy" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ModifiedDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="IsDataDriven" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListMySubscriptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPathOrSiteURL" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListMySubscriptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionItems" type="tns:ArrayOfSubscription" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSubscriptionsUsingDataSource">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSubscriptionsUsingDataSourceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionItems" type="tns:ArrayOfSubscription" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangeSubscriptionOwner">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SubscriptionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="NewOwner" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangeSubscriptionOwnerResponse">
        <s:complexType />
      </s:element>
      <s:element name="CreateDataSource">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="Overwrite" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="tns:DataSourceDefinition" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDataSourceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemInfo" type="tns:CatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PrepareQuery">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="tns:DataSource" />
            <s:element minOccurs="0" maxOccurs="1" name="DataSet" type="tns:DataSetDefinition" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="DataSource">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:choice minOccurs="1" maxOccurs="1">
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceDefinition" type="tns:DataSourceDefinition" />
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceReference" type="tns:DataSourceReference" />
            <s:element minOccurs="0" maxOccurs="1" name="InvalidDataSourceReference" type="tns:InvalidDataSourceReference" />
          </s:choice>
        </s:sequence>
      </s:complexType>
      <s:element name="PrepareQueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSettings" type="tns:DataSetDefinition" />
            <s:element minOccurs="1" maxOccurs="1" name="Changed" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="ParameterNames" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnableDataSource">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnableDataSourceResponse">
        <s:complexType />
      </s:element>
      <s:element name="DisableDataSource">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DisableDataSourceResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetDataSourceContents">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="tns:DataSourceDefinition" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetDataSourceContentsResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetDataSourceContents">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDataSourceContentsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="tns:DataSourceDefinition" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListDatabaseCredentialRetrievalOptions">
        <s:complexType />
      </s:element>
      <s:element name="ListDatabaseCredentialRetrievalOptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListDatabaseCredentialRetrievalOptionsResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemDataSources">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DataSources" type="tns:ArrayOfDataSource" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfDataSource">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DataSource" nillable="true" type="tns:DataSource" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetItemDataSourcesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemDataSources">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemDataSourcesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSources" type="tns:ArrayOfDataSource" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestConnectForDataSourceDefinition">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceDefinition" type="tns:DataSourceDefinition" />
            <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestConnectForDataSourceDefinitionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="TestConnectForDataSourceDefinitionResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="ConnectError" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestConnectForItemDataSource">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DataSourceName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestConnectForItemDataSourceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="TestConnectForItemDataSourceResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="ConnectError" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateRole">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TaskIDs" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateRoleResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetRoleProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TaskIDs" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetRolePropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetRoleProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetRolePropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TaskIDs" type="tns:ArrayOfString1" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRole">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRoleResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListRoles">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SecurityScope" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListRolesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Roles" type="tns:ArrayOfRole" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfRole">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Role" nillable="true" type="tns:Role" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Role">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListTasks">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SecurityScope" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListTasksResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Tasks" type="tns:ArrayOfTask" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfTask">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Task" nillable="true" type="tns:Task" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Task">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TaskID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfPolicy">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Policy" nillable="true" type="tns:Policy" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Policy">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="GroupUserName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Roles" type="tns:ArrayOfRole" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetPoliciesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetPoliciesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
            <s:element minOccurs="1" maxOccurs="1" name="InheritParent" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemDataSourcePrompts">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemDataSourcePromptsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSourcePrompts" type="tns:ArrayOfDataSourcePrompt" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfDataSourcePrompt">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DataSourcePrompt" nillable="true" type="tns:DataSourcePrompt" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="DataSourcePrompt">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DataSourceID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Prompt" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GenerateModel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DataSource" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GenerateModelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemInfo" type="tns:CatalogItem" />
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetModelItemPermissions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetModelItemPermissionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Permissions" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetModelItemPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetModelItemPoliciesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetModelItemPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetModelItemPoliciesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
            <s:element minOccurs="1" maxOccurs="1" name="InheritParent" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetUserModel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Perspective" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetUserModelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="s:base64Binary" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="InheritModelItemParentSecurity">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="InheritModelItemParentSecurityResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetModelDrillthroughReports">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Reports" type="tns:ArrayOfModelDrillthroughReport" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfModelDrillthroughReport">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ModelDrillthroughReport" nillable="true" type="tns:ModelDrillthroughReport" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ModelDrillthroughReport">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Path" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Type" type="tns:DrillthroughType" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="DrillthroughType">
        <s:restriction base="s:string">
          <s:enumeration value="Detail" />
          <s:enumeration value="List" />
        </s:restriction>
      </s:simpleType>
      <s:element name="SetModelDrillthroughReportsResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListModelDrillthroughReports">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListModelDrillthroughReportsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Reports" type="tns:ArrayOfModelDrillthroughReport" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListModelItemChildren">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModelItemID" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="Recursive" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListModelItemChildrenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ModelItems" type="tns:ArrayOfModelItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfModelItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ModelItem" nillable="true" type="tns:ModelItem" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ModelItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ModelItemTypeName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ModelItems" type="tns:ArrayOfModelItem" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListModelItemTypes">
        <s:complexType />
      </s:element>
      <s:element name="ListModelItemTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListModelItemTypesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListModelPerspectives">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListModelPerspectivesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ModelCatalogItems" type="tns:ArrayOfModelCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfModelCatalogItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ModelCatalogItem" nillable="true" type="tns:ModelCatalogItem" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ModelCatalogItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Perspectives" type="tns:ArrayOfModelPerspective" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfModelPerspective">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ModelPerspective" nillable="true" type="tns:ModelPerspective" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ModelPerspective">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="RegenerateModel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RegenerateModelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RemoveAllModelItemPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RemoveAllModelItemPoliciesResponse">
        <s:complexType />
      </s:element>
      <s:element name="CreateSchedule">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ScheduleDefinition">
        <s:complexContent mixed="false">
          <s:extension base="tns:ScheduleDefinitionOrReference">
            <s:sequence>
              <s:choice minOccurs="1" maxOccurs="1">
                <s:element minOccurs="0" maxOccurs="1" name="MinuteRecurrence" type="tns:MinuteRecurrence" />
                <s:element minOccurs="0" maxOccurs="1" name="DailyRecurrence" type="tns:DailyRecurrence" />
                <s:element minOccurs="0" maxOccurs="1" name="WeeklyRecurrence" type="tns:WeeklyRecurrence" />
                <s:element minOccurs="0" maxOccurs="1" name="MonthlyRecurrence" type="tns:MonthlyRecurrence" />
                <s:element minOccurs="0" maxOccurs="1" name="MonthlyDOWRecurrence" type="tns:MonthlyDOWRecurrence" />
              </s:choice>
              <s:element minOccurs="1" maxOccurs="1" name="StartDateTime" type="s:dateTime" />
              <s:element minOccurs="0" maxOccurs="1" name="EndDate" type="s:dateTime" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScheduleDefinitionOrReference" />
      <s:complexType name="MinuteRecurrence">
        <s:complexContent mixed="false">
          <s:extension base="tns:RecurrencePattern">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="MinutesInterval" type="s:int" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="RecurrencePattern" />
      <s:complexType name="MonthlyDOWRecurrence">
        <s:complexContent mixed="false">
          <s:extension base="tns:RecurrencePattern">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="WhichWeek" type="tns:WeekNumberEnum" />
              <s:element minOccurs="0" maxOccurs="1" name="DaysOfWeek" type="tns:DaysOfWeekSelector" />
              <s:element minOccurs="0" maxOccurs="1" name="MonthsOfYear" type="tns:MonthsOfYearSelector" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:simpleType name="WeekNumberEnum">
        <s:restriction base="s:string">
          <s:enumeration value="FirstWeek" />
          <s:enumeration value="SecondWeek" />
          <s:enumeration value="ThirdWeek" />
          <s:enumeration value="FourthWeek" />
          <s:enumeration value="LastWeek" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DaysOfWeekSelector">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Sunday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Monday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Tuesday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Wednesday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Thursday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Friday" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="Saturday" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="MonthsOfYearSelector">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="January" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="February" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="March" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="April" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="May" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="June" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="July" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="August" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="September" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="October" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="November" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="December" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="MonthlyRecurrence">
        <s:complexContent mixed="false">
          <s:extension base="tns:RecurrencePattern">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Days" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="MonthsOfYear" type="tns:MonthsOfYearSelector" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="WeeklyRecurrence">
        <s:complexContent mixed="false">
          <s:extension base="tns:RecurrencePattern">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="WeeksInterval" type="s:int" />
              <s:element minOccurs="0" maxOccurs="1" name="DaysOfWeek" type="tns:DaysOfWeekSelector" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="DailyRecurrence">
        <s:complexContent mixed="false">
          <s:extension base="tns:RecurrencePattern">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="DaysInterval" type="s:int" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="CreateScheduleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteSchedule">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteScheduleResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListSchedules">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSchedulesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Schedules" type="tns:ArrayOfSchedule" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfSchedule">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Schedule" nillable="true" type="tns:Schedule" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Schedule">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Definition" type="tns:ScheduleDefinition" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Creator" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NextRunTime" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="LastRunTime" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="ReferencesPresent" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="ScheduleStateName" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetScheduleProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSchedulePropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Schedule" type="tns:Schedule" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListScheduleStates">
        <s:complexType />
      </s:element>
      <s:element name="ListScheduleStatesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListScheduleStatesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PauseSchedule">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PauseScheduleResponse">
        <s:complexType />
      </s:element>
      <s:element name="ResumeSchedule">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResumeScheduleResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetScheduleProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSchedulePropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListScheduledItems">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListScheduledItemsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Items" type="tns:ArrayOfCatalogItem" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemParameters">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfItemParameter" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfItemParameter">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ItemParameter" nillable="true" type="tns:ItemParameter" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ItemParameter">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ParameterTypeName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Nullable" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="AllowBlank" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="MultiValue" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="QueryParameter" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Prompt" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PromptUser" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Dependencies" type="tns:ArrayOfString2" />
          <s:element minOccurs="0" maxOccurs="1" name="ValidValuesQueryBased" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="ValidValues" type="tns:ArrayOfValidValue1" />
          <s:element minOccurs="0" maxOccurs="1" name="DefaultValuesQueryBased" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="DefaultValues" type="tns:ArrayOfString" />
          <s:element minOccurs="0" maxOccurs="1" name="ParameterStateName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ErrorMessage" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfString2">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Dependency" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfValidValue1">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ValidValue" nillable="true" type="tns:ValidValue" />
        </s:sequence>
      </s:complexType>
      <s:element name="SetItemParametersResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemParameters">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="HistoryID" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="ForRendering" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="Values" type="tns:ArrayOfParameterValue" />
            <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:ArrayOfDataSourceCredentials" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfDataSourceCredentials">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DataSourceCredentials" nillable="true" type="tns:DataSourceCredentials" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="DataSourceCredentials">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DataSourceName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetItemParametersResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfItemParameter" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListParameterTypes">
        <s:complexType />
      </s:element>
      <s:element name="ListParameterTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListParameterTypesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListParameterStates">
        <s:complexType />
      </s:element>
      <s:element name="ListParameterStatesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListParameterStatesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateReportEditSession">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Report" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Definition" type="s:base64Binary" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateReportEditSessionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EditSessionID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateLinkedItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parent" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Link" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateLinkedItemResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetItemLink">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Link" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemLinkResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemLink">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemLinkResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Link" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListExecutionSettings">
        <s:complexType />
      </s:element>
      <s:element name="ListExecutionSettingsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListExecutionSettingsResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetExecutionOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ExecutionSetting" type="s:string" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleReference" type="tns:ScheduleReference" />
              <s:element minOccurs="0" maxOccurs="1" name="NoSchedule" type="tns:NoSchedule" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ScheduleReference">
        <s:complexContent mixed="false">
          <s:extension base="tns:ScheduleDefinitionOrReference">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleID" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="Definition" type="tns:ScheduleDefinition" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="NoSchedule">
        <s:complexContent mixed="false">
          <s:extension base="tns:ScheduleDefinitionOrReference" />
        </s:complexContent>
      </s:complexType>
      <s:element name="SetExecutionOptionsResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetExecutionOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetExecutionOptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ExecutionSetting" type="s:string" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleReference" type="tns:ScheduleReference" />
              <s:element minOccurs="0" maxOccurs="1" name="NoSchedule" type="tns:NoSchedule" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateItemExecutionSnapshot">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateItemExecutionSnapshotResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetCacheOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="CacheItem" type="s:boolean" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="TimeExpiration" type="tns:TimeExpiration" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleExpiration" type="tns:ScheduleExpiration" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="TimeExpiration">
        <s:complexContent mixed="false">
          <s:extension base="tns:ExpirationDefinition">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Minutes" type="s:int" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ExpirationDefinition" />
      <s:complexType name="ScheduleExpiration">
        <s:complexContent mixed="false">
          <s:extension base="tns:ExpirationDefinition">
            <s:sequence>
              <s:choice minOccurs="1" maxOccurs="1">
                <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
                <s:element minOccurs="0" maxOccurs="1" name="ScheduleReference" type="tns:ScheduleReference" />
              </s:choice>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SetCacheOptionsResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetCacheOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCacheOptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CacheItem" type="s:boolean" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="TimeExpiration" type="tns:TimeExpiration" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleExpiration" type="tns:ScheduleExpiration" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FlushCache">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FlushCacheResponse">
        <s:complexType />
      </s:element>
      <s:element name="CreateItemHistorySnapshot">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateItemHistorySnapshotResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HistoryID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Warnings" type="tns:ArrayOfWarning" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteItemHistorySnapshot">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="HistoryID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteItemHistorySnapshotResponse">
        <s:complexType />
      </s:element>
      <s:element name="SetItemHistoryLimit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="UseSystem" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="HistoryLimit" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemHistoryLimitResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemHistoryLimit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemHistoryLimitResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="HistoryLimit" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="IsSystem" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="SystemLimit" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemHistoryOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="EnableManualSnapshotCreation" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="KeepExecutionSnapshots" type="s:boolean" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleReference" type="tns:ScheduleReference" />
              <s:element minOccurs="0" maxOccurs="1" name="NoSchedule" type="tns:NoSchedule" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetItemHistoryOptionsResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetItemHistoryOptions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetItemHistoryOptionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="EnableManualSnapshotCreation" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="KeepExecutionSnapshots" type="s:boolean" />
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleDefinition" type="tns:ScheduleDefinition" />
              <s:element minOccurs="0" maxOccurs="1" name="ScheduleReference" type="tns:ScheduleReference" />
              <s:element minOccurs="0" maxOccurs="1" name="NoSchedule" type="tns:NoSchedule" />
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetReportServerConfigInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ScaleOut" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetReportServerConfigInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ServerConfigInfo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IsSSLRequired">
        <s:complexType />
      </s:element>
      <s:element name="IsSSLRequiredResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IsSSLRequiredResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSystemProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSystemPropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetSystemProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSystemPropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Values" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetUserSettings">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetUserSettingsResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetUserSettings">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Properties" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetUserSettingsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Values" type="tns:ArrayOfProperty" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSystemPolicies">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSystemPoliciesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetSystemPolicies">
        <s:complexType />
      </s:element>
      <s:element name="GetSystemPoliciesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Policies" type="tns:ArrayOfPolicy" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListExtensions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ExtensionType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListExtensionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Extensions" type="tns:ArrayOfExtension" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfExtension">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Extension" nillable="true" type="tns:Extension" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Extension">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ExtensionTypeName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LocalizedName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Visible" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="IsModelGenerationSupported" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListExtensionTypes">
        <s:complexType />
      </s:element>
      <s:element name="ListExtensionTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListExtensionTypesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListEvents">
        <s:complexType />
      </s:element>
      <s:element name="ListEventsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Events" type="tns:ArrayOfEvent" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfEvent">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Event" nillable="true" type="tns:Event" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Event">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Type" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="FireEvent">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SiteUrl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FireEventResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListJobs">
        <s:complexType />
      </s:element>
      <s:element name="ListJobsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Jobs" type="tns:ArrayOfJob" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfJob">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Job" nillable="true" type="tns:Job" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Job">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="JobID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Path" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Machine" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="User" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StartDateTime" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="JobActionName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="JobTypeName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="JobStatusName" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="ListJobTypes">
        <s:complexType />
      </s:element>
      <s:element name="ListJobTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListJobTypesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListJobActions">
        <s:complexType />
      </s:element>
      <s:element name="ListJobActionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListJobActionsResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListJobStates">
        <s:complexType />
      </s:element>
      <s:element name="ListJobStatesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListJobStatesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelJob">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="JobID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelJobResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CancelJobResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateCacheRefreshPlan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateCacheRefreshPlanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlanID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetCacheRefreshPlanProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlanID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetCacheRefreshPlanPropertiesResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetCacheRefreshPlanProperties">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlanID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCacheRefreshPlanPropertiesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="LastRunStatus" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="State" type="tns:CacheRefreshPlanState" />
            <s:element minOccurs="0" maxOccurs="1" name="EventType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MatchData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Parameters" type="tns:ArrayOfParameterValue" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="CacheRefreshPlanState">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="MissingParameterValue" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="InvalidParameterValue" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="UnknownItemParameter" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="CachingNotEnabledOnItem" type="s:boolean" />
        </s:sequence>
      </s:complexType>
      <s:element name="DeleteCacheRefreshPlan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlanID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCacheRefreshPlanResponse">
        <s:complexType />
      </s:element>
      <s:element name="ListCacheRefreshPlans">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListCacheRefreshPlansResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlans" type="tns:ArrayOfCacheRefreshPlan" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCacheRefreshPlan">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CacheRefreshPlan" nillable="true" type="tns:CacheRefreshPlan" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="CacheRefreshPlan">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CacheRefreshPlanID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="State" type="tns:CacheRefreshPlanState" />
          <s:element minOccurs="1" maxOccurs="1" name="LastExecuted" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="ModifiedDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="ModifiedBy" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LastRunStatus" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="LogonUser">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="authority" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogonUserResponse">
        <s:complexType />
      </s:element>
      <s:element name="Logoff">
        <s:complexType />
      </s:element>
      <s:element name="LogoffResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetPermissions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ItemPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetPermissionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Permissions" type="tns:ArrayOfString3" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString3">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Operation" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetSystemPermissions">
        <s:complexType />
      </s:element>
      <s:element name="GetSystemPermissionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Permissions" type="tns:ArrayOfString3" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListSecurityScopes">
        <s:complexType />
      </s:element>
      <s:element name="ListSecurityScopesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ListSecurityScopesResult" type="tns:ArrayOfString1" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CreateCatalogItemSoapIn">
    <wsdl:part name="parameters" element="tns:CreateCatalogItem" />
  </wsdl:message>
  <wsdl:message name="CreateCatalogItemSoapOut">
    <wsdl:part name="parameters" element="tns:CreateCatalogItemResponse" />
  </wsdl:message>
  <wsdl:message name="CreateCatalogItemServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateCatalogItemTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemDefinitionSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemDefinition" />
  </wsdl:message>
  <wsdl:message name="SetItemDefinitionSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemDefinitionResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemDefinitionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemDefinitionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDefinitionSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemDefinition" />
  </wsdl:message>
  <wsdl:message name="GetItemDefinitionSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemDefinitionResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemDefinitionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDefinitionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemTypeSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemType" />
  </wsdl:message>
  <wsdl:message name="GetItemTypeSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemTypeResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemTypeServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemTypeTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteItemSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteItem" />
  </wsdl:message>
  <wsdl:message name="DeleteItemSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteItemResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteItemServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteItemTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="MoveItemSoapIn">
    <wsdl:part name="parameters" element="tns:MoveItem" />
  </wsdl:message>
  <wsdl:message name="MoveItemSoapOut">
    <wsdl:part name="parameters" element="tns:MoveItemResponse" />
  </wsdl:message>
  <wsdl:message name="MoveItemServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="MoveItemTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="InheritParentSecuritySoapIn">
    <wsdl:part name="parameters" element="tns:InheritParentSecurity" />
  </wsdl:message>
  <wsdl:message name="InheritParentSecuritySoapOut">
    <wsdl:part name="parameters" element="tns:InheritParentSecurityResponse" />
  </wsdl:message>
  <wsdl:message name="InheritParentSecurityServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="InheritParentSecurityTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListItemHistorySoapIn">
    <wsdl:part name="parameters" element="tns:ListItemHistory" />
  </wsdl:message>
  <wsdl:message name="ListItemHistorySoapOut">
    <wsdl:part name="parameters" element="tns:ListItemHistoryResponse" />
  </wsdl:message>
  <wsdl:message name="ListItemHistoryServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListItemHistoryTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListChildrenSoapIn">
    <wsdl:part name="parameters" element="tns:ListChildren" />
  </wsdl:message>
  <wsdl:message name="ListChildrenSoapOut">
    <wsdl:part name="parameters" element="tns:ListChildrenResponse" />
  </wsdl:message>
  <wsdl:message name="ListChildrenServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListChildrenTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListDependentItemsSoapIn">
    <wsdl:part name="parameters" element="tns:ListDependentItems" />
  </wsdl:message>
  <wsdl:message name="ListDependentItemsSoapOut">
    <wsdl:part name="parameters" element="tns:ListDependentItemsResponse" />
  </wsdl:message>
  <wsdl:message name="ListDependentItemsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListDependentItemsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="FindItemsSoapIn">
    <wsdl:part name="parameters" element="tns:FindItems" />
  </wsdl:message>
  <wsdl:message name="FindItemsSoapOut">
    <wsdl:part name="parameters" element="tns:FindItemsResponse" />
  </wsdl:message>
  <wsdl:message name="FindItemsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="FindItemsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListParentsSoapIn">
    <wsdl:part name="parameters" element="tns:ListParents" />
  </wsdl:message>
  <wsdl:message name="ListParentsSoapOut">
    <wsdl:part name="parameters" element="tns:ListParentsResponse" />
  </wsdl:message>
  <wsdl:message name="ListParentsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListParentsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateFolderSoapIn">
    <wsdl:part name="parameters" element="tns:CreateFolder" />
  </wsdl:message>
  <wsdl:message name="CreateFolderSoapOut">
    <wsdl:part name="parameters" element="tns:CreateFolderResponse" />
  </wsdl:message>
  <wsdl:message name="CreateFolderServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateFolderTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetProperties" />
  </wsdl:message>
  <wsdl:message name="SetPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetProperties" />
  </wsdl:message>
  <wsdl:message name="GetPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetPropertiesItemNamespaceHeader">
    <wsdl:part name="ItemNamespaceHeader" element="tns:ItemNamespaceHeader" />
  </wsdl:message>
  <wsdl:message name="GetPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemReferencesSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemReferences" />
  </wsdl:message>
  <wsdl:message name="SetItemReferencesSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemReferencesResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemReferencesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemReferencesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemReferencesSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemReferences" />
  </wsdl:message>
  <wsdl:message name="GetItemReferencesSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemReferencesResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemReferencesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemReferencesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListItemTypesSoapIn">
    <wsdl:part name="parameters" element="tns:ListItemTypes" />
  </wsdl:message>
  <wsdl:message name="ListItemTypesSoapOut">
    <wsdl:part name="parameters" element="tns:ListItemTypesResponse" />
  </wsdl:message>
  <wsdl:message name="ListItemTypesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListItemTypesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetSubscriptionPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetSubscriptionProperties" />
  </wsdl:message>
  <wsdl:message name="SetSubscriptionPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetSubscriptionPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetSubscriptionPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetSubscriptionPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetSubscriptionPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSubscriptionProperties" />
  </wsdl:message>
  <wsdl:message name="GetSubscriptionPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSubscriptionPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetSubscriptionPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetSubscriptionPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetDataDrivenSubscriptionPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetDataDrivenSubscriptionProperties" />
  </wsdl:message>
  <wsdl:message name="SetDataDrivenSubscriptionPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetDataDrivenSubscriptionPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetDataDrivenSubscriptionPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetDataDrivenSubscriptionPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetDataDrivenSubscriptionPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetDataDrivenSubscriptionProperties" />
  </wsdl:message>
  <wsdl:message name="GetDataDrivenSubscriptionPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetDataDrivenSubscriptionPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetDataDrivenSubscriptionPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetDataDrivenSubscriptionPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DisableSubscriptionSoapIn">
    <wsdl:part name="parameters" element="tns:DisableSubscription" />
  </wsdl:message>
  <wsdl:message name="DisableSubscriptionSoapOut">
    <wsdl:part name="parameters" element="tns:DisableSubscriptionResponse" />
  </wsdl:message>
  <wsdl:message name="DisableSubscriptionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DisableSubscriptionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="EnableSubscriptionSoapIn">
    <wsdl:part name="parameters" element="tns:EnableSubscription" />
  </wsdl:message>
  <wsdl:message name="EnableSubscriptionSoapOut">
    <wsdl:part name="parameters" element="tns:EnableSubscriptionResponse" />
  </wsdl:message>
  <wsdl:message name="EnableSubscriptionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="EnableSubscriptionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteSubscriptionSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteSubscription" />
  </wsdl:message>
  <wsdl:message name="DeleteSubscriptionSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteSubscriptionResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteSubscriptionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteSubscriptionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateSubscriptionSoapIn">
    <wsdl:part name="parameters" element="tns:CreateSubscription" />
  </wsdl:message>
  <wsdl:message name="CreateSubscriptionSoapOut">
    <wsdl:part name="parameters" element="tns:CreateSubscriptionResponse" />
  </wsdl:message>
  <wsdl:message name="CreateSubscriptionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateSubscriptionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateDataDrivenSubscriptionSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDataDrivenSubscription" />
  </wsdl:message>
  <wsdl:message name="CreateDataDrivenSubscriptionSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDataDrivenSubscriptionResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDataDrivenSubscriptionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateDataDrivenSubscriptionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetExtensionSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:GetExtensionSettings" />
  </wsdl:message>
  <wsdl:message name="GetExtensionSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:GetExtensionSettingsResponse" />
  </wsdl:message>
  <wsdl:message name="GetExtensionSettingsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetExtensionSettingsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ValidateExtensionSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:ValidateExtensionSettings" />
  </wsdl:message>
  <wsdl:message name="ValidateExtensionSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:ValidateExtensionSettingsResponse" />
  </wsdl:message>
  <wsdl:message name="ValidateExtensionSettingsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ValidateExtensionSettingsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsSoapIn">
    <wsdl:part name="parameters" element="tns:ListSubscriptions" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsSoapOut">
    <wsdl:part name="parameters" element="tns:ListSubscriptionsResponse" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListMySubscriptionsSoapIn">
    <wsdl:part name="parameters" element="tns:ListMySubscriptions" />
  </wsdl:message>
  <wsdl:message name="ListMySubscriptionsSoapOut">
    <wsdl:part name="parameters" element="tns:ListMySubscriptionsResponse" />
  </wsdl:message>
  <wsdl:message name="ListMySubscriptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListMySubscriptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsUsingDataSourceSoapIn">
    <wsdl:part name="parameters" element="tns:ListSubscriptionsUsingDataSource" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsUsingDataSourceSoapOut">
    <wsdl:part name="parameters" element="tns:ListSubscriptionsUsingDataSourceResponse" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsUsingDataSourceServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListSubscriptionsUsingDataSourceTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ChangeSubscriptionOwnerSoapIn">
    <wsdl:part name="parameters" element="tns:ChangeSubscriptionOwner" />
  </wsdl:message>
  <wsdl:message name="ChangeSubscriptionOwnerSoapOut">
    <wsdl:part name="parameters" element="tns:ChangeSubscriptionOwnerResponse" />
  </wsdl:message>
  <wsdl:message name="ChangeSubscriptionOwnerServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ChangeSubscriptionOwnerTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateDataSourceSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDataSource" />
  </wsdl:message>
  <wsdl:message name="CreateDataSourceSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDataSourceResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDataSourceServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateDataSourceTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="PrepareQuerySoapIn">
    <wsdl:part name="parameters" element="tns:PrepareQuery" />
  </wsdl:message>
  <wsdl:message name="PrepareQuerySoapOut">
    <wsdl:part name="parameters" element="tns:PrepareQueryResponse" />
  </wsdl:message>
  <wsdl:message name="PrepareQueryServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="PrepareQueryTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="EnableDataSourceSoapIn">
    <wsdl:part name="parameters" element="tns:EnableDataSource" />
  </wsdl:message>
  <wsdl:message name="EnableDataSourceSoapOut">
    <wsdl:part name="parameters" element="tns:EnableDataSourceResponse" />
  </wsdl:message>
  <wsdl:message name="EnableDataSourceServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="EnableDataSourceTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DisableDataSourceSoapIn">
    <wsdl:part name="parameters" element="tns:DisableDataSource" />
  </wsdl:message>
  <wsdl:message name="DisableDataSourceSoapOut">
    <wsdl:part name="parameters" element="tns:DisableDataSourceResponse" />
  </wsdl:message>
  <wsdl:message name="DisableDataSourceServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DisableDataSourceTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetDataSourceContentsSoapIn">
    <wsdl:part name="parameters" element="tns:SetDataSourceContents" />
  </wsdl:message>
  <wsdl:message name="SetDataSourceContentsSoapOut">
    <wsdl:part name="parameters" element="tns:SetDataSourceContentsResponse" />
  </wsdl:message>
  <wsdl:message name="SetDataSourceContentsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetDataSourceContentsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetDataSourceContentsSoapIn">
    <wsdl:part name="parameters" element="tns:GetDataSourceContents" />
  </wsdl:message>
  <wsdl:message name="GetDataSourceContentsSoapOut">
    <wsdl:part name="parameters" element="tns:GetDataSourceContentsResponse" />
  </wsdl:message>
  <wsdl:message name="GetDataSourceContentsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetDataSourceContentsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListDatabaseCredentialRetrievalOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:ListDatabaseCredentialRetrievalOptions" />
  </wsdl:message>
  <wsdl:message name="ListDatabaseCredentialRetrievalOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:ListDatabaseCredentialRetrievalOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="ListDatabaseCredentialRetrievalOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListDatabaseCredentialRetrievalOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemDataSourcesSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemDataSources" />
  </wsdl:message>
  <wsdl:message name="SetItemDataSourcesSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemDataSourcesResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemDataSourcesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemDataSourcesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcesSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemDataSources" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcesSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemDataSourcesResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="TestConnectForDataSourceDefinitionSoapIn">
    <wsdl:part name="parameters" element="tns:TestConnectForDataSourceDefinition" />
  </wsdl:message>
  <wsdl:message name="TestConnectForDataSourceDefinitionSoapOut">
    <wsdl:part name="parameters" element="tns:TestConnectForDataSourceDefinitionResponse" />
  </wsdl:message>
  <wsdl:message name="TestConnectForDataSourceDefinitionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="TestConnectForDataSourceDefinitionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="TestConnectForItemDataSourceSoapIn">
    <wsdl:part name="parameters" element="tns:TestConnectForItemDataSource" />
  </wsdl:message>
  <wsdl:message name="TestConnectForItemDataSourceSoapOut">
    <wsdl:part name="parameters" element="tns:TestConnectForItemDataSourceResponse" />
  </wsdl:message>
  <wsdl:message name="TestConnectForItemDataSourceServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="TestConnectForItemDataSourceTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateRoleSoapIn">
    <wsdl:part name="parameters" element="tns:CreateRole" />
  </wsdl:message>
  <wsdl:message name="CreateRoleSoapOut">
    <wsdl:part name="parameters" element="tns:CreateRoleResponse" />
  </wsdl:message>
  <wsdl:message name="CreateRoleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateRoleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetRolePropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetRoleProperties" />
  </wsdl:message>
  <wsdl:message name="SetRolePropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetRolePropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetRolePropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetRolePropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetRolePropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetRoleProperties" />
  </wsdl:message>
  <wsdl:message name="GetRolePropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetRolePropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetRolePropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetRolePropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteRoleSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteRole" />
  </wsdl:message>
  <wsdl:message name="DeleteRoleSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteRoleResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteRoleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteRoleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListRolesSoapIn">
    <wsdl:part name="parameters" element="tns:ListRoles" />
  </wsdl:message>
  <wsdl:message name="ListRolesSoapOut">
    <wsdl:part name="parameters" element="tns:ListRolesResponse" />
  </wsdl:message>
  <wsdl:message name="ListRolesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListRolesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListTasksSoapIn">
    <wsdl:part name="parameters" element="tns:ListTasks" />
  </wsdl:message>
  <wsdl:message name="ListTasksSoapOut">
    <wsdl:part name="parameters" element="tns:ListTasksResponse" />
  </wsdl:message>
  <wsdl:message name="ListTasksServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListTasksTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:SetPolicies" />
  </wsdl:message>
  <wsdl:message name="SetPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:SetPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="SetPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:GetPolicies" />
  </wsdl:message>
  <wsdl:message name="GetPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:GetPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="GetPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcePromptsSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemDataSourcePrompts" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcePromptsSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemDataSourcePromptsResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcePromptsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemDataSourcePromptsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GenerateModelSoapIn">
    <wsdl:part name="parameters" element="tns:GenerateModel" />
  </wsdl:message>
  <wsdl:message name="GenerateModelSoapOut">
    <wsdl:part name="parameters" element="tns:GenerateModelResponse" />
  </wsdl:message>
  <wsdl:message name="GenerateModelServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GenerateModelTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPermissionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetModelItemPermissions" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPermissionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetModelItemPermissionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPermissionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPermissionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetModelItemPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:SetModelItemPolicies" />
  </wsdl:message>
  <wsdl:message name="SetModelItemPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:SetModelItemPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="SetModelItemPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetModelItemPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:GetModelItemPolicies" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:GetModelItemPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetModelItemPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetUserModelSoapIn">
    <wsdl:part name="parameters" element="tns:GetUserModel" />
  </wsdl:message>
  <wsdl:message name="GetUserModelSoapOut">
    <wsdl:part name="parameters" element="tns:GetUserModelResponse" />
  </wsdl:message>
  <wsdl:message name="GetUserModelServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetUserModelTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="InheritModelItemParentSecuritySoapIn">
    <wsdl:part name="parameters" element="tns:InheritModelItemParentSecurity" />
  </wsdl:message>
  <wsdl:message name="InheritModelItemParentSecuritySoapOut">
    <wsdl:part name="parameters" element="tns:InheritModelItemParentSecurityResponse" />
  </wsdl:message>
  <wsdl:message name="InheritModelItemParentSecurityServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="InheritModelItemParentSecurityTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetModelDrillthroughReportsSoapIn">
    <wsdl:part name="parameters" element="tns:SetModelDrillthroughReports" />
  </wsdl:message>
  <wsdl:message name="SetModelDrillthroughReportsSoapOut">
    <wsdl:part name="parameters" element="tns:SetModelDrillthroughReportsResponse" />
  </wsdl:message>
  <wsdl:message name="SetModelDrillthroughReportsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetModelDrillthroughReportsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelDrillthroughReportsSoapIn">
    <wsdl:part name="parameters" element="tns:ListModelDrillthroughReports" />
  </wsdl:message>
  <wsdl:message name="ListModelDrillthroughReportsSoapOut">
    <wsdl:part name="parameters" element="tns:ListModelDrillthroughReportsResponse" />
  </wsdl:message>
  <wsdl:message name="ListModelDrillthroughReportsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelDrillthroughReportsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelItemChildrenSoapIn">
    <wsdl:part name="parameters" element="tns:ListModelItemChildren" />
  </wsdl:message>
  <wsdl:message name="ListModelItemChildrenSoapOut">
    <wsdl:part name="parameters" element="tns:ListModelItemChildrenResponse" />
  </wsdl:message>
  <wsdl:message name="ListModelItemChildrenServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelItemChildrenTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelItemTypesSoapIn">
    <wsdl:part name="parameters" element="tns:ListModelItemTypes" />
  </wsdl:message>
  <wsdl:message name="ListModelItemTypesSoapOut">
    <wsdl:part name="parameters" element="tns:ListModelItemTypesResponse" />
  </wsdl:message>
  <wsdl:message name="ListModelItemTypesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelItemTypesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelPerspectivesSoapIn">
    <wsdl:part name="parameters" element="tns:ListModelPerspectives" />
  </wsdl:message>
  <wsdl:message name="ListModelPerspectivesSoapOut">
    <wsdl:part name="parameters" element="tns:ListModelPerspectivesResponse" />
  </wsdl:message>
  <wsdl:message name="ListModelPerspectivesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListModelPerspectivesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="RegenerateModelSoapIn">
    <wsdl:part name="parameters" element="tns:RegenerateModel" />
  </wsdl:message>
  <wsdl:message name="RegenerateModelSoapOut">
    <wsdl:part name="parameters" element="tns:RegenerateModelResponse" />
  </wsdl:message>
  <wsdl:message name="RegenerateModelServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="RegenerateModelTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="RemoveAllModelItemPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:RemoveAllModelItemPolicies" />
  </wsdl:message>
  <wsdl:message name="RemoveAllModelItemPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:RemoveAllModelItemPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="RemoveAllModelItemPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="RemoveAllModelItemPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateScheduleSoapIn">
    <wsdl:part name="parameters" element="tns:CreateSchedule" />
  </wsdl:message>
  <wsdl:message name="CreateScheduleSoapOut">
    <wsdl:part name="parameters" element="tns:CreateScheduleResponse" />
  </wsdl:message>
  <wsdl:message name="CreateScheduleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateScheduleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteScheduleSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteSchedule" />
  </wsdl:message>
  <wsdl:message name="DeleteScheduleSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteScheduleResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteScheduleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteScheduleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListSchedulesSoapIn">
    <wsdl:part name="parameters" element="tns:ListSchedules" />
  </wsdl:message>
  <wsdl:message name="ListSchedulesSoapOut">
    <wsdl:part name="parameters" element="tns:ListSchedulesResponse" />
  </wsdl:message>
  <wsdl:message name="ListSchedulesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListSchedulesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetSchedulePropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetScheduleProperties" />
  </wsdl:message>
  <wsdl:message name="GetSchedulePropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSchedulePropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetSchedulePropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetSchedulePropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListScheduleStatesSoapIn">
    <wsdl:part name="parameters" element="tns:ListScheduleStates" />
  </wsdl:message>
  <wsdl:message name="ListScheduleStatesSoapOut">
    <wsdl:part name="parameters" element="tns:ListScheduleStatesResponse" />
  </wsdl:message>
  <wsdl:message name="ListScheduleStatesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListScheduleStatesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="PauseScheduleSoapIn">
    <wsdl:part name="parameters" element="tns:PauseSchedule" />
  </wsdl:message>
  <wsdl:message name="PauseScheduleSoapOut">
    <wsdl:part name="parameters" element="tns:PauseScheduleResponse" />
  </wsdl:message>
  <wsdl:message name="PauseScheduleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="PauseScheduleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ResumeScheduleSoapIn">
    <wsdl:part name="parameters" element="tns:ResumeSchedule" />
  </wsdl:message>
  <wsdl:message name="ResumeScheduleSoapOut">
    <wsdl:part name="parameters" element="tns:ResumeScheduleResponse" />
  </wsdl:message>
  <wsdl:message name="ResumeScheduleServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ResumeScheduleTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetSchedulePropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetScheduleProperties" />
  </wsdl:message>
  <wsdl:message name="SetSchedulePropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetSchedulePropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetSchedulePropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetSchedulePropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListScheduledItemsSoapIn">
    <wsdl:part name="parameters" element="tns:ListScheduledItems" />
  </wsdl:message>
  <wsdl:message name="ListScheduledItemsSoapOut">
    <wsdl:part name="parameters" element="tns:ListScheduledItemsResponse" />
  </wsdl:message>
  <wsdl:message name="ListScheduledItemsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListScheduledItemsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemParametersSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemParameters" />
  </wsdl:message>
  <wsdl:message name="SetItemParametersSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemParametersResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemParametersServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemParametersTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemParametersSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemParameters" />
  </wsdl:message>
  <wsdl:message name="GetItemParametersSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemParametersResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemParametersServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemParametersTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListParameterTypesSoapIn">
    <wsdl:part name="parameters" element="tns:ListParameterTypes" />
  </wsdl:message>
  <wsdl:message name="ListParameterTypesSoapOut">
    <wsdl:part name="parameters" element="tns:ListParameterTypesResponse" />
  </wsdl:message>
  <wsdl:message name="ListParameterTypesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListParameterTypesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListParameterStatesSoapIn">
    <wsdl:part name="parameters" element="tns:ListParameterStates" />
  </wsdl:message>
  <wsdl:message name="ListParameterStatesSoapOut">
    <wsdl:part name="parameters" element="tns:ListParameterStatesResponse" />
  </wsdl:message>
  <wsdl:message name="ListParameterStatesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListParameterStatesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateReportEditSessionSoapIn">
    <wsdl:part name="parameters" element="tns:CreateReportEditSession" />
  </wsdl:message>
  <wsdl:message name="CreateReportEditSessionSoapOut">
    <wsdl:part name="parameters" element="tns:CreateReportEditSessionResponse" />
  </wsdl:message>
  <wsdl:message name="CreateReportEditSessionServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateReportEditSessionTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateLinkedItemSoapIn">
    <wsdl:part name="parameters" element="tns:CreateLinkedItem" />
  </wsdl:message>
  <wsdl:message name="CreateLinkedItemSoapOut">
    <wsdl:part name="parameters" element="tns:CreateLinkedItemResponse" />
  </wsdl:message>
  <wsdl:message name="CreateLinkedItemServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateLinkedItemTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemLinkSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemLink" />
  </wsdl:message>
  <wsdl:message name="SetItemLinkSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemLinkResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemLinkServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemLinkTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemLinkSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemLink" />
  </wsdl:message>
  <wsdl:message name="GetItemLinkSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemLinkResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemLinkServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemLinkTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListExecutionSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:ListExecutionSettings" />
  </wsdl:message>
  <wsdl:message name="ListExecutionSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:ListExecutionSettingsResponse" />
  </wsdl:message>
  <wsdl:message name="ListExecutionSettingsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListExecutionSettingsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetExecutionOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:SetExecutionOptions" />
  </wsdl:message>
  <wsdl:message name="SetExecutionOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:SetExecutionOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="SetExecutionOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetExecutionOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetExecutionOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetExecutionOptions" />
  </wsdl:message>
  <wsdl:message name="GetExecutionOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetExecutionOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetExecutionOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetExecutionOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="UpdateItemExecutionSnapshotSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateItemExecutionSnapshot" />
  </wsdl:message>
  <wsdl:message name="UpdateItemExecutionSnapshotSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateItemExecutionSnapshotResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateItemExecutionSnapshotServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="UpdateItemExecutionSnapshotTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetCacheOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:SetCacheOptions" />
  </wsdl:message>
  <wsdl:message name="SetCacheOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:SetCacheOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="SetCacheOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetCacheOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetCacheOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetCacheOptions" />
  </wsdl:message>
  <wsdl:message name="GetCacheOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetCacheOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetCacheOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetCacheOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="FlushCacheSoapIn">
    <wsdl:part name="parameters" element="tns:FlushCache" />
  </wsdl:message>
  <wsdl:message name="FlushCacheSoapOut">
    <wsdl:part name="parameters" element="tns:FlushCacheResponse" />
  </wsdl:message>
  <wsdl:message name="FlushCacheServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="FlushCacheTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateItemHistorySnapshotSoapIn">
    <wsdl:part name="parameters" element="tns:CreateItemHistorySnapshot" />
  </wsdl:message>
  <wsdl:message name="CreateItemHistorySnapshotSoapOut">
    <wsdl:part name="parameters" element="tns:CreateItemHistorySnapshotResponse" />
  </wsdl:message>
  <wsdl:message name="CreateItemHistorySnapshotServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateItemHistorySnapshotTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteItemHistorySnapshotSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteItemHistorySnapshot" />
  </wsdl:message>
  <wsdl:message name="DeleteItemHistorySnapshotSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteItemHistorySnapshotResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteItemHistorySnapshotServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteItemHistorySnapshotTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryLimitSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemHistoryLimit" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryLimitSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemHistoryLimitResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryLimitServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryLimitTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryLimitSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemHistoryLimit" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryLimitSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemHistoryLimitResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryLimitServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryLimitTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:SetItemHistoryOptions" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:SetItemHistoryOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetItemHistoryOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryOptionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetItemHistoryOptions" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryOptionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetItemHistoryOptionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryOptionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetItemHistoryOptionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetReportServerConfigInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetReportServerConfigInfo" />
  </wsdl:message>
  <wsdl:message name="GetReportServerConfigInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetReportServerConfigInfoResponse" />
  </wsdl:message>
  <wsdl:message name="GetReportServerConfigInfoServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetReportServerConfigInfoTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="IsSSLRequiredSoapIn">
    <wsdl:part name="parameters" element="tns:IsSSLRequired" />
  </wsdl:message>
  <wsdl:message name="IsSSLRequiredSoapOut">
    <wsdl:part name="parameters" element="tns:IsSSLRequiredResponse" />
  </wsdl:message>
  <wsdl:message name="IsSSLRequiredServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="IsSSLRequiredTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetSystemPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetSystemProperties" />
  </wsdl:message>
  <wsdl:message name="SetSystemPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetSystemPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetSystemPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetSystemPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSystemProperties" />
  </wsdl:message>
  <wsdl:message name="GetSystemPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSystemPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetSystemPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetUserSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:SetUserSettings" />
  </wsdl:message>
  <wsdl:message name="SetUserSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:SetUserSettingsResponse" />
  </wsdl:message>
  <wsdl:message name="SetUserSettingsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetUserSettingsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetUserSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:GetUserSettings" />
  </wsdl:message>
  <wsdl:message name="GetUserSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:GetUserSettingsResponse" />
  </wsdl:message>
  <wsdl:message name="GetUserSettingsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetUserSettingsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetSystemPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:SetSystemPolicies" />
  </wsdl:message>
  <wsdl:message name="SetSystemPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:SetSystemPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="SetSystemPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetSystemPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPoliciesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSystemPolicies" />
  </wsdl:message>
  <wsdl:message name="GetSystemPoliciesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSystemPoliciesResponse" />
  </wsdl:message>
  <wsdl:message name="GetSystemPoliciesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPoliciesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListExtensionsSoapIn">
    <wsdl:part name="parameters" element="tns:ListExtensions" />
  </wsdl:message>
  <wsdl:message name="ListExtensionsSoapOut">
    <wsdl:part name="parameters" element="tns:ListExtensionsResponse" />
  </wsdl:message>
  <wsdl:message name="ListExtensionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListExtensionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListExtensionTypesSoapIn">
    <wsdl:part name="parameters" element="tns:ListExtensionTypes" />
  </wsdl:message>
  <wsdl:message name="ListExtensionTypesSoapOut">
    <wsdl:part name="parameters" element="tns:ListExtensionTypesResponse" />
  </wsdl:message>
  <wsdl:message name="ListExtensionTypesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListExtensionTypesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListEventsSoapIn">
    <wsdl:part name="parameters" element="tns:ListEvents" />
  </wsdl:message>
  <wsdl:message name="ListEventsSoapOut">
    <wsdl:part name="parameters" element="tns:ListEventsResponse" />
  </wsdl:message>
  <wsdl:message name="ListEventsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListEventsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="FireEventSoapIn">
    <wsdl:part name="parameters" element="tns:FireEvent" />
  </wsdl:message>
  <wsdl:message name="FireEventSoapOut">
    <wsdl:part name="parameters" element="tns:FireEventResponse" />
  </wsdl:message>
  <wsdl:message name="FireEventServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="FireEventTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobsSoapIn">
    <wsdl:part name="parameters" element="tns:ListJobs" />
  </wsdl:message>
  <wsdl:message name="ListJobsSoapOut">
    <wsdl:part name="parameters" element="tns:ListJobsResponse" />
  </wsdl:message>
  <wsdl:message name="ListJobsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobTypesSoapIn">
    <wsdl:part name="parameters" element="tns:ListJobTypes" />
  </wsdl:message>
  <wsdl:message name="ListJobTypesSoapOut">
    <wsdl:part name="parameters" element="tns:ListJobTypesResponse" />
  </wsdl:message>
  <wsdl:message name="ListJobTypesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobTypesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobActionsSoapIn">
    <wsdl:part name="parameters" element="tns:ListJobActions" />
  </wsdl:message>
  <wsdl:message name="ListJobActionsSoapOut">
    <wsdl:part name="parameters" element="tns:ListJobActionsResponse" />
  </wsdl:message>
  <wsdl:message name="ListJobActionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobActionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobStatesSoapIn">
    <wsdl:part name="parameters" element="tns:ListJobStates" />
  </wsdl:message>
  <wsdl:message name="ListJobStatesSoapOut">
    <wsdl:part name="parameters" element="tns:ListJobStatesResponse" />
  </wsdl:message>
  <wsdl:message name="ListJobStatesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListJobStatesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CancelJobSoapIn">
    <wsdl:part name="parameters" element="tns:CancelJob" />
  </wsdl:message>
  <wsdl:message name="CancelJobSoapOut">
    <wsdl:part name="parameters" element="tns:CancelJobResponse" />
  </wsdl:message>
  <wsdl:message name="CancelJobServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CancelJobTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="CreateCacheRefreshPlanSoapIn">
    <wsdl:part name="parameters" element="tns:CreateCacheRefreshPlan" />
  </wsdl:message>
  <wsdl:message name="CreateCacheRefreshPlanSoapOut">
    <wsdl:part name="parameters" element="tns:CreateCacheRefreshPlanResponse" />
  </wsdl:message>
  <wsdl:message name="CreateCacheRefreshPlanServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="CreateCacheRefreshPlanTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="SetCacheRefreshPlanPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:SetCacheRefreshPlanProperties" />
  </wsdl:message>
  <wsdl:message name="SetCacheRefreshPlanPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:SetCacheRefreshPlanPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="SetCacheRefreshPlanPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="SetCacheRefreshPlanPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetCacheRefreshPlanPropertiesSoapIn">
    <wsdl:part name="parameters" element="tns:GetCacheRefreshPlanProperties" />
  </wsdl:message>
  <wsdl:message name="GetCacheRefreshPlanPropertiesSoapOut">
    <wsdl:part name="parameters" element="tns:GetCacheRefreshPlanPropertiesResponse" />
  </wsdl:message>
  <wsdl:message name="GetCacheRefreshPlanPropertiesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetCacheRefreshPlanPropertiesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteCacheRefreshPlanSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCacheRefreshPlan" />
  </wsdl:message>
  <wsdl:message name="DeleteCacheRefreshPlanSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCacheRefreshPlanResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteCacheRefreshPlanServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="DeleteCacheRefreshPlanTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListCacheRefreshPlansSoapIn">
    <wsdl:part name="parameters" element="tns:ListCacheRefreshPlans" />
  </wsdl:message>
  <wsdl:message name="ListCacheRefreshPlansSoapOut">
    <wsdl:part name="parameters" element="tns:ListCacheRefreshPlansResponse" />
  </wsdl:message>
  <wsdl:message name="ListCacheRefreshPlansServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListCacheRefreshPlansTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="LogonUserSoapIn">
    <wsdl:part name="parameters" element="tns:LogonUser" />
  </wsdl:message>
  <wsdl:message name="LogonUserSoapOut">
    <wsdl:part name="parameters" element="tns:LogonUserResponse" />
  </wsdl:message>
  <wsdl:message name="LogonUserServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="LogonUserTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="LogoffSoapIn">
    <wsdl:part name="parameters" element="tns:Logoff" />
  </wsdl:message>
  <wsdl:message name="LogoffSoapOut">
    <wsdl:part name="parameters" element="tns:LogoffResponse" />
  </wsdl:message>
  <wsdl:message name="LogoffServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="LogoffTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetPermissionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetPermissions" />
  </wsdl:message>
  <wsdl:message name="GetPermissionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetPermissionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetPermissionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetPermissionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPermissionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetSystemPermissions" />
  </wsdl:message>
  <wsdl:message name="GetSystemPermissionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetSystemPermissionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetSystemPermissionsServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="GetSystemPermissionsTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:message name="ListSecurityScopesSoapIn">
    <wsdl:part name="parameters" element="tns:ListSecurityScopes" />
  </wsdl:message>
  <wsdl:message name="ListSecurityScopesSoapOut">
    <wsdl:part name="parameters" element="tns:ListSecurityScopesResponse" />
  </wsdl:message>
  <wsdl:message name="ListSecurityScopesServerInfoHeader">
    <wsdl:part name="ServerInfoHeader" element="tns:ServerInfoHeader" />
  </wsdl:message>
  <wsdl:message name="ListSecurityScopesTrustedUserHeader">
    <wsdl:part name="TrustedUserHeader" element="tns:TrustedUserHeader" />
  </wsdl:message>
  <wsdl:portType name="ReportingService2010Soap">
    <wsdl:operation name="CreateCatalogItem">
      <wsdl:input message="tns:CreateCatalogItemSoapIn" />
      <wsdl:output message="tns:CreateCatalogItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemDefinition">
      <wsdl:input message="tns:SetItemDefinitionSoapIn" />
      <wsdl:output message="tns:SetItemDefinitionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemDefinition">
      <wsdl:input message="tns:GetItemDefinitionSoapIn" />
      <wsdl:output message="tns:GetItemDefinitionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemType">
      <wsdl:input message="tns:GetItemTypeSoapIn" />
      <wsdl:output message="tns:GetItemTypeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteItem">
      <wsdl:input message="tns:DeleteItemSoapIn" />
      <wsdl:output message="tns:DeleteItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="MoveItem">
      <wsdl:input message="tns:MoveItemSoapIn" />
      <wsdl:output message="tns:MoveItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="InheritParentSecurity">
      <wsdl:input message="tns:InheritParentSecuritySoapIn" />
      <wsdl:output message="tns:InheritParentSecuritySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListItemHistory">
      <wsdl:input message="tns:ListItemHistorySoapIn" />
      <wsdl:output message="tns:ListItemHistorySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListChildren">
      <wsdl:input message="tns:ListChildrenSoapIn" />
      <wsdl:output message="tns:ListChildrenSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListDependentItems">
      <wsdl:input message="tns:ListDependentItemsSoapIn" />
      <wsdl:output message="tns:ListDependentItemsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FindItems">
      <wsdl:input message="tns:FindItemsSoapIn" />
      <wsdl:output message="tns:FindItemsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListParents">
      <wsdl:input message="tns:ListParentsSoapIn" />
      <wsdl:output message="tns:ListParentsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateFolder">
      <wsdl:input message="tns:CreateFolderSoapIn" />
      <wsdl:output message="tns:CreateFolderSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetProperties">
      <wsdl:input message="tns:SetPropertiesSoapIn" />
      <wsdl:output message="tns:SetPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetProperties">
      <wsdl:input message="tns:GetPropertiesSoapIn" />
      <wsdl:output message="tns:GetPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemReferences">
      <wsdl:input message="tns:SetItemReferencesSoapIn" />
      <wsdl:output message="tns:SetItemReferencesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemReferences">
      <wsdl:input message="tns:GetItemReferencesSoapIn" />
      <wsdl:output message="tns:GetItemReferencesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListItemTypes">
      <wsdl:input message="tns:ListItemTypesSoapIn" />
      <wsdl:output message="tns:ListItemTypesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetSubscriptionProperties">
      <wsdl:input message="tns:SetSubscriptionPropertiesSoapIn" />
      <wsdl:output message="tns:SetSubscriptionPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSubscriptionProperties">
      <wsdl:input message="tns:GetSubscriptionPropertiesSoapIn" />
      <wsdl:output message="tns:GetSubscriptionPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetDataDrivenSubscriptionProperties">
      <wsdl:input message="tns:SetDataDrivenSubscriptionPropertiesSoapIn" />
      <wsdl:output message="tns:SetDataDrivenSubscriptionPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDataDrivenSubscriptionProperties">
      <wsdl:input message="tns:GetDataDrivenSubscriptionPropertiesSoapIn" />
      <wsdl:output message="tns:GetDataDrivenSubscriptionPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DisableSubscription">
      <wsdl:input message="tns:DisableSubscriptionSoapIn" />
      <wsdl:output message="tns:DisableSubscriptionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EnableSubscription">
      <wsdl:input message="tns:EnableSubscriptionSoapIn" />
      <wsdl:output message="tns:EnableSubscriptionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteSubscription">
      <wsdl:input message="tns:DeleteSubscriptionSoapIn" />
      <wsdl:output message="tns:DeleteSubscriptionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateSubscription">
      <wsdl:input message="tns:CreateSubscriptionSoapIn" />
      <wsdl:output message="tns:CreateSubscriptionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDataDrivenSubscription">
      <wsdl:input message="tns:CreateDataDrivenSubscriptionSoapIn" />
      <wsdl:output message="tns:CreateDataDrivenSubscriptionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetExtensionSettings">
      <wsdl:input message="tns:GetExtensionSettingsSoapIn" />
      <wsdl:output message="tns:GetExtensionSettingsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ValidateExtensionSettings">
      <wsdl:input message="tns:ValidateExtensionSettingsSoapIn" />
      <wsdl:output message="tns:ValidateExtensionSettingsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptions">
      <wsdl:input message="tns:ListSubscriptionsSoapIn" />
      <wsdl:output message="tns:ListSubscriptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListMySubscriptions">
      <wsdl:input message="tns:ListMySubscriptionsSoapIn" />
      <wsdl:output message="tns:ListMySubscriptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptionsUsingDataSource">
      <wsdl:input message="tns:ListSubscriptionsUsingDataSourceSoapIn" />
      <wsdl:output message="tns:ListSubscriptionsUsingDataSourceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ChangeSubscriptionOwner">
      <wsdl:input message="tns:ChangeSubscriptionOwnerSoapIn" />
      <wsdl:output message="tns:ChangeSubscriptionOwnerSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDataSource">
      <wsdl:input message="tns:CreateDataSourceSoapIn" />
      <wsdl:output message="tns:CreateDataSourceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PrepareQuery">
      <wsdl:input message="tns:PrepareQuerySoapIn" />
      <wsdl:output message="tns:PrepareQuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EnableDataSource">
      <wsdl:input message="tns:EnableDataSourceSoapIn" />
      <wsdl:output message="tns:EnableDataSourceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DisableDataSource">
      <wsdl:input message="tns:DisableDataSourceSoapIn" />
      <wsdl:output message="tns:DisableDataSourceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetDataSourceContents">
      <wsdl:input message="tns:SetDataSourceContentsSoapIn" />
      <wsdl:output message="tns:SetDataSourceContentsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDataSourceContents">
      <wsdl:input message="tns:GetDataSourceContentsSoapIn" />
      <wsdl:output message="tns:GetDataSourceContentsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListDatabaseCredentialRetrievalOptions">
      <wsdl:input message="tns:ListDatabaseCredentialRetrievalOptionsSoapIn" />
      <wsdl:output message="tns:ListDatabaseCredentialRetrievalOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemDataSources">
      <wsdl:input message="tns:SetItemDataSourcesSoapIn" />
      <wsdl:output message="tns:SetItemDataSourcesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSources">
      <wsdl:input message="tns:GetItemDataSourcesSoapIn" />
      <wsdl:output message="tns:GetItemDataSourcesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TestConnectForDataSourceDefinition">
      <wsdl:input message="tns:TestConnectForDataSourceDefinitionSoapIn" />
      <wsdl:output message="tns:TestConnectForDataSourceDefinitionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TestConnectForItemDataSource">
      <wsdl:input message="tns:TestConnectForItemDataSourceSoapIn" />
      <wsdl:output message="tns:TestConnectForItemDataSourceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateRole">
      <wsdl:input message="tns:CreateRoleSoapIn" />
      <wsdl:output message="tns:CreateRoleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetRoleProperties">
      <wsdl:input message="tns:SetRolePropertiesSoapIn" />
      <wsdl:output message="tns:SetRolePropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetRoleProperties">
      <wsdl:input message="tns:GetRolePropertiesSoapIn" />
      <wsdl:output message="tns:GetRolePropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteRole">
      <wsdl:input message="tns:DeleteRoleSoapIn" />
      <wsdl:output message="tns:DeleteRoleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListRoles">
      <wsdl:input message="tns:ListRolesSoapIn" />
      <wsdl:output message="tns:ListRolesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListTasks">
      <wsdl:input message="tns:ListTasksSoapIn" />
      <wsdl:output message="tns:ListTasksSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetPolicies">
      <wsdl:input message="tns:SetPoliciesSoapIn" />
      <wsdl:output message="tns:SetPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetPolicies">
      <wsdl:input message="tns:GetPoliciesSoapIn" />
      <wsdl:output message="tns:GetPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSourcePrompts">
      <wsdl:input message="tns:GetItemDataSourcePromptsSoapIn" />
      <wsdl:output message="tns:GetItemDataSourcePromptsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GenerateModel">
      <wsdl:input message="tns:GenerateModelSoapIn" />
      <wsdl:output message="tns:GenerateModelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPermissions">
      <wsdl:input message="tns:GetModelItemPermissionsSoapIn" />
      <wsdl:output message="tns:GetModelItemPermissionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetModelItemPolicies">
      <wsdl:input message="tns:SetModelItemPoliciesSoapIn" />
      <wsdl:output message="tns:SetModelItemPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPolicies">
      <wsdl:input message="tns:GetModelItemPoliciesSoapIn" />
      <wsdl:output message="tns:GetModelItemPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetUserModel">
      <wsdl:input message="tns:GetUserModelSoapIn" />
      <wsdl:output message="tns:GetUserModelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="InheritModelItemParentSecurity">
      <wsdl:input message="tns:InheritModelItemParentSecuritySoapIn" />
      <wsdl:output message="tns:InheritModelItemParentSecuritySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetModelDrillthroughReports">
      <wsdl:input message="tns:SetModelDrillthroughReportsSoapIn" />
      <wsdl:output message="tns:SetModelDrillthroughReportsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListModelDrillthroughReports">
      <wsdl:input message="tns:ListModelDrillthroughReportsSoapIn" />
      <wsdl:output message="tns:ListModelDrillthroughReportsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListModelItemChildren">
      <wsdl:input message="tns:ListModelItemChildrenSoapIn" />
      <wsdl:output message="tns:ListModelItemChildrenSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListModelItemTypes">
      <wsdl:input message="tns:ListModelItemTypesSoapIn" />
      <wsdl:output message="tns:ListModelItemTypesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListModelPerspectives">
      <wsdl:input message="tns:ListModelPerspectivesSoapIn" />
      <wsdl:output message="tns:ListModelPerspectivesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="RegenerateModel">
      <wsdl:input message="tns:RegenerateModelSoapIn" />
      <wsdl:output message="tns:RegenerateModelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="RemoveAllModelItemPolicies">
      <wsdl:input message="tns:RemoveAllModelItemPoliciesSoapIn" />
      <wsdl:output message="tns:RemoveAllModelItemPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateSchedule">
      <wsdl:input message="tns:CreateScheduleSoapIn" />
      <wsdl:output message="tns:CreateScheduleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteSchedule">
      <wsdl:input message="tns:DeleteScheduleSoapIn" />
      <wsdl:output message="tns:DeleteScheduleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListSchedules">
      <wsdl:input message="tns:ListSchedulesSoapIn" />
      <wsdl:output message="tns:ListSchedulesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetScheduleProperties">
      <wsdl:input message="tns:GetSchedulePropertiesSoapIn" />
      <wsdl:output message="tns:GetSchedulePropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListScheduleStates">
      <wsdl:input message="tns:ListScheduleStatesSoapIn" />
      <wsdl:output message="tns:ListScheduleStatesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PauseSchedule">
      <wsdl:input message="tns:PauseScheduleSoapIn" />
      <wsdl:output message="tns:PauseScheduleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ResumeSchedule">
      <wsdl:input message="tns:ResumeScheduleSoapIn" />
      <wsdl:output message="tns:ResumeScheduleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetScheduleProperties">
      <wsdl:input message="tns:SetSchedulePropertiesSoapIn" />
      <wsdl:output message="tns:SetSchedulePropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListScheduledItems">
      <wsdl:input message="tns:ListScheduledItemsSoapIn" />
      <wsdl:output message="tns:ListScheduledItemsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemParameters">
      <wsdl:input message="tns:SetItemParametersSoapIn" />
      <wsdl:output message="tns:SetItemParametersSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemParameters">
      <wsdl:input message="tns:GetItemParametersSoapIn" />
      <wsdl:output message="tns:GetItemParametersSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListParameterTypes">
      <wsdl:input message="tns:ListParameterTypesSoapIn" />
      <wsdl:output message="tns:ListParameterTypesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListParameterStates">
      <wsdl:input message="tns:ListParameterStatesSoapIn" />
      <wsdl:output message="tns:ListParameterStatesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateReportEditSession">
      <wsdl:input message="tns:CreateReportEditSessionSoapIn" />
      <wsdl:output message="tns:CreateReportEditSessionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateLinkedItem">
      <wsdl:input message="tns:CreateLinkedItemSoapIn" />
      <wsdl:output message="tns:CreateLinkedItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemLink">
      <wsdl:input message="tns:SetItemLinkSoapIn" />
      <wsdl:output message="tns:SetItemLinkSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemLink">
      <wsdl:input message="tns:GetItemLinkSoapIn" />
      <wsdl:output message="tns:GetItemLinkSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListExecutionSettings">
      <wsdl:input message="tns:ListExecutionSettingsSoapIn" />
      <wsdl:output message="tns:ListExecutionSettingsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetExecutionOptions">
      <wsdl:input message="tns:SetExecutionOptionsSoapIn" />
      <wsdl:output message="tns:SetExecutionOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetExecutionOptions">
      <wsdl:input message="tns:GetExecutionOptionsSoapIn" />
      <wsdl:output message="tns:GetExecutionOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateItemExecutionSnapshot">
      <wsdl:input message="tns:UpdateItemExecutionSnapshotSoapIn" />
      <wsdl:output message="tns:UpdateItemExecutionSnapshotSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetCacheOptions">
      <wsdl:input message="tns:SetCacheOptionsSoapIn" />
      <wsdl:output message="tns:SetCacheOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetCacheOptions">
      <wsdl:input message="tns:GetCacheOptionsSoapIn" />
      <wsdl:output message="tns:GetCacheOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FlushCache">
      <wsdl:input message="tns:FlushCacheSoapIn" />
      <wsdl:output message="tns:FlushCacheSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateItemHistorySnapshot">
      <wsdl:input message="tns:CreateItemHistorySnapshotSoapIn" />
      <wsdl:output message="tns:CreateItemHistorySnapshotSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteItemHistorySnapshot">
      <wsdl:input message="tns:DeleteItemHistorySnapshotSoapIn" />
      <wsdl:output message="tns:DeleteItemHistorySnapshotSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryLimit">
      <wsdl:input message="tns:SetItemHistoryLimitSoapIn" />
      <wsdl:output message="tns:SetItemHistoryLimitSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryLimit">
      <wsdl:input message="tns:GetItemHistoryLimitSoapIn" />
      <wsdl:output message="tns:GetItemHistoryLimitSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryOptions">
      <wsdl:input message="tns:SetItemHistoryOptionsSoapIn" />
      <wsdl:output message="tns:SetItemHistoryOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryOptions">
      <wsdl:input message="tns:GetItemHistoryOptionsSoapIn" />
      <wsdl:output message="tns:GetItemHistoryOptionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetReportServerConfigInfo">
      <wsdl:input message="tns:GetReportServerConfigInfoSoapIn" />
      <wsdl:output message="tns:GetReportServerConfigInfoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IsSSLRequired">
      <wsdl:input message="tns:IsSSLRequiredSoapIn" />
      <wsdl:output message="tns:IsSSLRequiredSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetSystemProperties">
      <wsdl:input message="tns:SetSystemPropertiesSoapIn" />
      <wsdl:output message="tns:SetSystemPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSystemProperties">
      <wsdl:input message="tns:GetSystemPropertiesSoapIn" />
      <wsdl:output message="tns:GetSystemPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetUserSettings">
      <wsdl:input message="tns:SetUserSettingsSoapIn" />
      <wsdl:output message="tns:SetUserSettingsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetUserSettings">
      <wsdl:input message="tns:GetUserSettingsSoapIn" />
      <wsdl:output message="tns:GetUserSettingsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetSystemPolicies">
      <wsdl:input message="tns:SetSystemPoliciesSoapIn" />
      <wsdl:output message="tns:SetSystemPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSystemPolicies">
      <wsdl:input message="tns:GetSystemPoliciesSoapIn" />
      <wsdl:output message="tns:GetSystemPoliciesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListExtensions">
      <wsdl:input message="tns:ListExtensionsSoapIn" />
      <wsdl:output message="tns:ListExtensionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListExtensionTypes">
      <wsdl:input message="tns:ListExtensionTypesSoapIn" />
      <wsdl:output message="tns:ListExtensionTypesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListEvents">
      <wsdl:input message="tns:ListEventsSoapIn" />
      <wsdl:output message="tns:ListEventsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FireEvent">
      <wsdl:input message="tns:FireEventSoapIn" />
      <wsdl:output message="tns:FireEventSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListJobs">
      <wsdl:input message="tns:ListJobsSoapIn" />
      <wsdl:output message="tns:ListJobsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListJobTypes">
      <wsdl:input message="tns:ListJobTypesSoapIn" />
      <wsdl:output message="tns:ListJobTypesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListJobActions">
      <wsdl:input message="tns:ListJobActionsSoapIn" />
      <wsdl:output message="tns:ListJobActionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListJobStates">
      <wsdl:input message="tns:ListJobStatesSoapIn" />
      <wsdl:output message="tns:ListJobStatesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CancelJob">
      <wsdl:input message="tns:CancelJobSoapIn" />
      <wsdl:output message="tns:CancelJobSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateCacheRefreshPlan">
      <wsdl:input message="tns:CreateCacheRefreshPlanSoapIn" />
      <wsdl:output message="tns:CreateCacheRefreshPlanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetCacheRefreshPlanProperties">
      <wsdl:input message="tns:SetCacheRefreshPlanPropertiesSoapIn" />
      <wsdl:output message="tns:SetCacheRefreshPlanPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetCacheRefreshPlanProperties">
      <wsdl:input message="tns:GetCacheRefreshPlanPropertiesSoapIn" />
      <wsdl:output message="tns:GetCacheRefreshPlanPropertiesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCacheRefreshPlan">
      <wsdl:input message="tns:DeleteCacheRefreshPlanSoapIn" />
      <wsdl:output message="tns:DeleteCacheRefreshPlanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListCacheRefreshPlans">
      <wsdl:input message="tns:ListCacheRefreshPlansSoapIn" />
      <wsdl:output message="tns:ListCacheRefreshPlansSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="LogonUser">
      <wsdl:input message="tns:LogonUserSoapIn" />
      <wsdl:output message="tns:LogonUserSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <wsdl:input message="tns:LogoffSoapIn" />
      <wsdl:output message="tns:LogoffSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetPermissions">
      <wsdl:input message="tns:GetPermissionsSoapIn" />
      <wsdl:output message="tns:GetPermissionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSystemPermissions">
      <wsdl:input message="tns:GetSystemPermissionsSoapIn" />
      <wsdl:output message="tns:GetSystemPermissionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ListSecurityScopes">
      <wsdl:input message="tns:ListSecurityScopesSoapIn" />
      <wsdl:output message="tns:ListSecurityScopesSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ReportingService2010Soap" type="tns:ReportingService2010Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CreateCatalogItem">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateCatalogItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateCatalogItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateCatalogItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemDefinition">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemDefinition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDefinition">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDefinition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemType">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemType" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemTypeTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemTypeServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteItem">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MoveItem">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/MoveItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:MoveItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:MoveItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InheritParentSecurity">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/InheritParentSecurity" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:InheritParentSecurityTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:InheritParentSecurityServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListItemHistory">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListItemHistory" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListItemHistoryTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListItemHistoryServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListChildren">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListChildren" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListChildrenTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListChildrenServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListDependentItems">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListDependentItems" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListDependentItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListDependentItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FindItems">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FindItems" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:FindItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:FindItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParents">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListParentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListParentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateFolder">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateFolder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateFolderTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateFolderServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetPropertiesItemNamespaceHeader" part="ItemNamespaceHeader" use="literal" />
        <soap:header message="tns:GetPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemReferences">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemReferences" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemReferencesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemReferencesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemReferences">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemReferences" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemReferencesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemReferencesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListItemTypes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListItemTypes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListItemTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListItemTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSubscriptionProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSubscriptionProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetDataDrivenSubscriptionProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetDataDrivenSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetDataDrivenSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetDataDrivenSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDataDrivenSubscriptionProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetDataDrivenSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetDataDrivenSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetDataDrivenSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DisableSubscription">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DisableSubscription" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DisableSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DisableSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnableSubscription">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/EnableSubscription" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:EnableSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:EnableSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSubscription">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteSubscription" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateSubscription">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateSubscription" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDataDrivenSubscription">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateDataDrivenSubscription" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateDataDrivenSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateDataDrivenSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetExtensionSettings">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetExtensionSettings" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetExtensionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetExtensionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateExtensionSettings">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ValidateExtensionSettings" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ValidateExtensionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ValidateExtensionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSubscriptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListSubscriptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListSubscriptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListMySubscriptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListMySubscriptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListMySubscriptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListMySubscriptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptionsUsingDataSource">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSubscriptionsUsingDataSource" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListSubscriptionsUsingDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListSubscriptionsUsingDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeSubscriptionOwner">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ChangeSubscriptionOwner" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ChangeSubscriptionOwnerTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ChangeSubscriptionOwnerServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDataSource">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateDataSource" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PrepareQuery">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/PrepareQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:PrepareQueryTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:PrepareQueryServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnableDataSource">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/EnableDataSource" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:EnableDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:EnableDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DisableDataSource">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DisableDataSource" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DisableDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DisableDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetDataSourceContents">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetDataSourceContents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetDataSourceContentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetDataSourceContentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDataSourceContents">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetDataSourceContents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetDataSourceContentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetDataSourceContentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListDatabaseCredentialRetrievalOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListDatabaseCredentialRetrievalOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListDatabaseCredentialRetrievalOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListDatabaseCredentialRetrievalOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemDataSources">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemDataSources" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemDataSourcesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemDataSourcesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSources">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDataSources" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDataSourcesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDataSourcesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestConnectForDataSourceDefinition">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/TestConnectForDataSourceDefinition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TestConnectForDataSourceDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:TestConnectForDataSourceDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestConnectForItemDataSource">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/TestConnectForItemDataSource" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TestConnectForItemDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:TestConnectForItemDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateRole">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateRole" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateRoleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateRoleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetRoleProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetRoleProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetRolePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetRolePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetRoleProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetRolePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetRolePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRole">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteRole" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteRoleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteRoleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListRoles">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListRoles" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListRolesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListRolesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListTasks">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListTasks" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListTasksTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListTasksServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSourcePrompts">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDataSourcePrompts" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDataSourcePromptsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemDataSourcePromptsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GenerateModel">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GenerateModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GenerateModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GenerateModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPermissions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetModelItemPermissions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetModelItemPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetModelItemPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetModelItemPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetModelItemPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetModelItemPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserModel">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetUserModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetUserModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetUserModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InheritModelItemParentSecurity">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/InheritModelItemParentSecurity" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:InheritModelItemParentSecurityTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:InheritModelItemParentSecurityServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetModelDrillthroughReports">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetModelDrillthroughReports" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetModelDrillthroughReportsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetModelDrillthroughReportsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelDrillthroughReports">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelDrillthroughReports" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelDrillthroughReportsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelDrillthroughReportsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelItemChildren">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelItemChildren" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelItemChildrenTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelItemChildrenServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelItemTypes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelItemTypes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelItemTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelItemTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelPerspectives">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelPerspectives" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelPerspectivesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListModelPerspectivesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegenerateModel">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/RegenerateModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:RegenerateModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:RegenerateModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveAllModelItemPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/RemoveAllModelItemPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:RemoveAllModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:RemoveAllModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateSchedule">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateSchedule" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSchedule">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteSchedule" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSchedules">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSchedules" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListSchedulesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListSchedulesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetScheduleProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetScheduleProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetSchedulePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetSchedulePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListScheduleStates">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListScheduleStates" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListScheduleStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListScheduleStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PauseSchedule">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/PauseSchedule" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:PauseScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:PauseScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResumeSchedule">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ResumeSchedule" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ResumeScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ResumeScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetScheduleProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetScheduleProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetSchedulePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetSchedulePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListScheduledItems">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListScheduledItems" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListScheduledItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListScheduledItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemParameters">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemParameters" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemParametersTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemParametersServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemParameters">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemParameters" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemParametersTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemParametersServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParameterTypes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParameterTypes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListParameterTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListParameterTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParameterStates">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParameterStates" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListParameterStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListParameterStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateReportEditSession">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateReportEditSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateReportEditSessionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateReportEditSessionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateLinkedItem">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateLinkedItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateLinkedItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateLinkedItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemLink">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemLink" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemLinkTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemLinkServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemLink">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemLink" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemLinkTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemLinkServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExecutionSettings">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExecutionSettings" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListExecutionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListExecutionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetExecutionOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetExecutionOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetExecutionOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetExecutionOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetExecutionOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetExecutionOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetExecutionOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetExecutionOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateItemExecutionSnapshot">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/UpdateItemExecutionSnapshot" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:UpdateItemExecutionSnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:UpdateItemExecutionSnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCacheOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetCacheOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetCacheOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetCacheOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCacheOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetCacheOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetCacheOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetCacheOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlushCache">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FlushCache" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:FlushCacheTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:FlushCacheServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateItemHistorySnapshot">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateItemHistorySnapshot" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateItemHistorySnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateItemHistorySnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteItemHistorySnapshot">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteItemHistorySnapshot" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteItemHistorySnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteItemHistorySnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryLimit">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemHistoryLimit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemHistoryLimitTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemHistoryLimitServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryLimit">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemHistoryLimit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemHistoryLimitTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemHistoryLimitServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemHistoryOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemHistoryOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetItemHistoryOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryOptions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemHistoryOptions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemHistoryOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetItemHistoryOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetReportServerConfigInfo">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetReportServerConfigInfo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetReportServerConfigInfoTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetReportServerConfigInfoServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsSSLRequired">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/IsSSLRequired" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:IsSSLRequiredTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:IsSSLRequiredServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSystemProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSystemProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetSystemPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetSystemPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetUserSettings">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetUserSettings" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetUserSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetUserSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserSettings">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetUserSettings" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetUserSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetUserSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSystemPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSystemPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetSystemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetSystemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemPolicies">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemPolicies" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExtensions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExtensions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListExtensionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListExtensionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExtensionTypes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExtensionTypes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListExtensionTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListExtensionTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListEvents">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListEvents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListEventsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListEventsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FireEvent">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FireEvent" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:FireEventTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:FireEventServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobs">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobTypes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobTypes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobActions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobActions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobActionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobActionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobStates">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobStates" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListJobStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelJob">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CancelJob" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CancelJobTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CancelJobServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCacheRefreshPlan">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateCacheRefreshPlan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:CreateCacheRefreshPlanTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:CreateCacheRefreshPlanServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCacheRefreshPlanProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetCacheRefreshPlanProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SetCacheRefreshPlanPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:SetCacheRefreshPlanPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCacheRefreshPlanProperties">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetCacheRefreshPlanProperties" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetCacheRefreshPlanPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetCacheRefreshPlanPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCacheRefreshPlan">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteCacheRefreshPlan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteCacheRefreshPlanTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:DeleteCacheRefreshPlanServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListCacheRefreshPlans">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListCacheRefreshPlans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListCacheRefreshPlansTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListCacheRefreshPlansServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogonUser">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/LogonUser" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:LogonUserTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:LogonUserServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/Logoff" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:LogoffTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:LogoffServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPermissions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetPermissions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemPermissions">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemPermissions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:GetSystemPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSecurityScopes">
      <soap:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSecurityScopes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:ListSecurityScopesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
        <soap:header message="tns:ListSecurityScopesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ReportingService2010Soap12" type="tns:ReportingService2010Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CreateCatalogItem">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateCatalogItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateCatalogItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateCatalogItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemDefinition">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemDefinition" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDefinition">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDefinition" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemType">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemType" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemTypeTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemTypeServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteItem">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MoveItem">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/MoveItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:MoveItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:MoveItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InheritParentSecurity">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/InheritParentSecurity" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:InheritParentSecurityTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:InheritParentSecurityServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListItemHistory">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListItemHistory" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListItemHistoryTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListItemHistoryServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListChildren">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListChildren" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListChildrenTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListChildrenServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListDependentItems">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListDependentItems" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListDependentItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListDependentItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FindItems">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FindItems" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:FindItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:FindItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParents">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParents" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateFolder">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateFolder" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateFolderTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateFolderServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPropertiesItemNamespaceHeader" part="ItemNamespaceHeader" use="literal" />
        <soap12:header message="tns:GetPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemReferences">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemReferences" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemReferencesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemReferencesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemReferences">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemReferences" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemReferencesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemReferencesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListItemTypes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListItemTypes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListItemTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListItemTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSubscriptionProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSubscriptionProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetDataDrivenSubscriptionProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetDataDrivenSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetDataDrivenSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetDataDrivenSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDataDrivenSubscriptionProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetDataDrivenSubscriptionProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetDataDrivenSubscriptionPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetDataDrivenSubscriptionPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DisableSubscription">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DisableSubscription" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DisableSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DisableSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnableSubscription">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/EnableSubscription" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:EnableSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:EnableSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSubscription">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteSubscription" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateSubscription">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateSubscription" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDataDrivenSubscription">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateDataDrivenSubscription" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateDataDrivenSubscriptionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateDataDrivenSubscriptionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetExtensionSettings">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetExtensionSettings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetExtensionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetExtensionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateExtensionSettings">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ValidateExtensionSettings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ValidateExtensionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ValidateExtensionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSubscriptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSubscriptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSubscriptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListMySubscriptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListMySubscriptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListMySubscriptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListMySubscriptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSubscriptionsUsingDataSource">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSubscriptionsUsingDataSource" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSubscriptionsUsingDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSubscriptionsUsingDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeSubscriptionOwner">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ChangeSubscriptionOwner" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ChangeSubscriptionOwnerTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ChangeSubscriptionOwnerServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDataSource">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateDataSource" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PrepareQuery">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/PrepareQuery" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:PrepareQueryTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:PrepareQueryServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnableDataSource">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/EnableDataSource" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:EnableDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:EnableDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DisableDataSource">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DisableDataSource" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DisableDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DisableDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetDataSourceContents">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetDataSourceContents" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetDataSourceContentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetDataSourceContentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDataSourceContents">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetDataSourceContents" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetDataSourceContentsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetDataSourceContentsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListDatabaseCredentialRetrievalOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListDatabaseCredentialRetrievalOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListDatabaseCredentialRetrievalOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListDatabaseCredentialRetrievalOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemDataSources">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemDataSources" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemDataSourcesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemDataSourcesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSources">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDataSources" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDataSourcesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDataSourcesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestConnectForDataSourceDefinition">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/TestConnectForDataSourceDefinition" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TestConnectForDataSourceDefinitionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:TestConnectForDataSourceDefinitionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestConnectForItemDataSource">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/TestConnectForItemDataSource" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TestConnectForItemDataSourceTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:TestConnectForItemDataSourceServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateRole">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateRole" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateRoleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateRoleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetRoleProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetRoleProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetRolePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetRolePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetRoleProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetRolePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetRolePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRole">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteRole" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteRoleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteRoleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListRoles">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListRoles" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListRolesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListRolesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListTasks">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListTasks" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListTasksTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListTasksServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemDataSourcePrompts">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemDataSourcePrompts" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDataSourcePromptsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemDataSourcePromptsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GenerateModel">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GenerateModel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GenerateModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GenerateModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPermissions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetModelItemPermissions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetModelItemPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetModelItemPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetModelItemPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetModelItemPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModelItemPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetModelItemPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserModel">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetUserModel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetUserModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetUserModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InheritModelItemParentSecurity">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/InheritModelItemParentSecurity" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:InheritModelItemParentSecurityTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:InheritModelItemParentSecurityServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetModelDrillthroughReports">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetModelDrillthroughReports" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetModelDrillthroughReportsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetModelDrillthroughReportsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelDrillthroughReports">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelDrillthroughReports" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelDrillthroughReportsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelDrillthroughReportsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelItemChildren">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelItemChildren" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelItemChildrenTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelItemChildrenServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelItemTypes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelItemTypes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelItemTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelItemTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListModelPerspectives">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListModelPerspectives" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelPerspectivesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListModelPerspectivesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegenerateModel">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/RegenerateModel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:RegenerateModelTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:RegenerateModelServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveAllModelItemPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/RemoveAllModelItemPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:RemoveAllModelItemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:RemoveAllModelItemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateSchedule">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateSchedule" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSchedule">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteSchedule" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSchedules">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSchedules" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSchedulesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSchedulesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetScheduleProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetScheduleProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSchedulePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSchedulePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListScheduleStates">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListScheduleStates" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListScheduleStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListScheduleStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PauseSchedule">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/PauseSchedule" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:PauseScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:PauseScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResumeSchedule">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ResumeSchedule" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ResumeScheduleTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ResumeScheduleServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetScheduleProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetScheduleProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSchedulePropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSchedulePropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListScheduledItems">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListScheduledItems" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListScheduledItemsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListScheduledItemsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemParameters">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemParameters" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemParametersTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemParametersServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemParameters">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemParameters" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemParametersTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemParametersServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParameterTypes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParameterTypes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParameterTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParameterTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListParameterStates">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListParameterStates" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParameterStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListParameterStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateReportEditSession">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateReportEditSession" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateReportEditSessionTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateReportEditSessionServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateLinkedItem">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateLinkedItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateLinkedItemTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateLinkedItemServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemLink">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemLink" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemLinkTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemLinkServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemLink">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemLink" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemLinkTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemLinkServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExecutionSettings">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExecutionSettings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExecutionSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExecutionSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetExecutionOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetExecutionOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetExecutionOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetExecutionOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetExecutionOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetExecutionOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetExecutionOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetExecutionOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateItemExecutionSnapshot">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/UpdateItemExecutionSnapshot" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:UpdateItemExecutionSnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:UpdateItemExecutionSnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCacheOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetCacheOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetCacheOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetCacheOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCacheOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetCacheOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetCacheOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetCacheOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlushCache">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FlushCache" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:FlushCacheTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:FlushCacheServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateItemHistorySnapshot">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateItemHistorySnapshot" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateItemHistorySnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateItemHistorySnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteItemHistorySnapshot">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteItemHistorySnapshot" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteItemHistorySnapshotTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteItemHistorySnapshotServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryLimit">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemHistoryLimit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemHistoryLimitTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemHistoryLimitServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryLimit">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemHistoryLimit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemHistoryLimitTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemHistoryLimitServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetItemHistoryOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetItemHistoryOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemHistoryOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetItemHistoryOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetItemHistoryOptions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetItemHistoryOptions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemHistoryOptionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetItemHistoryOptionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetReportServerConfigInfo">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetReportServerConfigInfo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetReportServerConfigInfoTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetReportServerConfigInfoServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsSSLRequired">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/IsSSLRequired" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:IsSSLRequiredTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:IsSSLRequiredServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSystemProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSystemProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSystemPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSystemPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetUserSettings">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetUserSettings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetUserSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetUserSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetUserSettings">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetUserSettings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetUserSettingsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetUserSettingsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSystemPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetSystemPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSystemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetSystemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemPolicies">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemPolicies" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPoliciesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPoliciesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExtensions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExtensions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExtensionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExtensionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListExtensionTypes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListExtensionTypes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExtensionTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListExtensionTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListEvents">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListEvents" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListEventsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListEventsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FireEvent">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/FireEvent" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:FireEventTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:FireEventServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobs">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobTypes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobTypes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobTypesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobTypesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobActions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobActions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobActionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobActionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListJobStates">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListJobStates" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobStatesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListJobStatesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelJob">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CancelJob" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CancelJobTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CancelJobServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCacheRefreshPlan">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/CreateCacheRefreshPlan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateCacheRefreshPlanTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:CreateCacheRefreshPlanServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCacheRefreshPlanProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/SetCacheRefreshPlanProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetCacheRefreshPlanPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:SetCacheRefreshPlanPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCacheRefreshPlanProperties">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetCacheRefreshPlanProperties" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetCacheRefreshPlanPropertiesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetCacheRefreshPlanPropertiesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCacheRefreshPlan">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/DeleteCacheRefreshPlan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteCacheRefreshPlanTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:DeleteCacheRefreshPlanServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListCacheRefreshPlans">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListCacheRefreshPlans" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListCacheRefreshPlansTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListCacheRefreshPlansServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogonUser">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/LogonUser" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:LogonUserTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:LogonUserServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/Logoff" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:LogoffTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:LogoffServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPermissions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetPermissions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemPermissions">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/GetSystemPermissions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPermissionsTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:GetSystemPermissionsServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListSecurityScopes">
      <soap12:operation soapAction="http://schemas.microsoft.com/sqlserver/reporting/2010/03/01/ReportServer/ListSecurityScopes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSecurityScopesTrustedUserHeader" part="TrustedUserHeader" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
        <soap12:header message="tns:ListSecurityScopesServerInfoHeader" part="ServerInfoHeader" use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ReportingService2010">
    <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">The Reporting Services Web Service enables you to manage a report server and its contents including server settings, security, reports, subscriptions, and data sources.</wsdl:documentation>
    <wsdl:port name="ReportingService2010Soap" binding="tns:ReportingService2010Soap">
      <soap:address location="http://localhost:80/ReportServer/ReportService2010.asmx" />
    </wsdl:port>
    <wsdl:port name="ReportingService2010Soap12" binding="tns:ReportingService2010Soap12">
      <soap12:address location="http://localhost:80/ReportServer/ReportService2010.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
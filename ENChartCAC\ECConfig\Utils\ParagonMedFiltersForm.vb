﻿Public Class ParagonMedFiltersForm

    Public Sub New()
        GetType(EnchartDOLib.DOParagonMedAdminFilter).ToString()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub
    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        UnitOfWork1.CommitChanges()
        Close()
    End Sub

    Private Sub DOParagonMedAdminFilterBindingSource1_CurrentChanged(sender As Object, e As EventArgs) Handles DOParagonMedAdminFilterBindingSource1.CurrentChanged

    End Sub

    Private Sub ParagonMedFiltersForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class
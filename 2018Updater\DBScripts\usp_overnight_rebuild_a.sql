﻿if NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = object_id(N'[dbo].[usp_overnight_rebuild]') AND OBJECTPROPERTY(object_id, N'IsProcedure') = 1)
BEGIN
	-- Replication will not allow a drop procedure.  Therefore, we have to create the procedure if it does not exist and then alter it for
	-- the script to be re-runnable. We have to do it dynamically because create proc must be in it's own batch. 

	DECLARE @strSQL nvarchar(200);
	SELECT @strSQL='CREATE PROC [dbo].[usp_overnight_rebuild] as SELECT 1';
	EXEC sp_executesql @strSQL;
END
﻿Option Infer On
#Region "Header"

'End Class

#End Region 'Header

Imports DevExpress.XtraEditors
Imports DevExpress.XtraTab

Enum ECRuleTabStatus As Integer
    Blank = -1
    HasError = 3
    HasData = 2
End Enum

<DebuggerDisplay("TabPage={TabPage.Name}")>
Public Class ECRuleTabBase
    Inherits ECRule

    Public Shared TabList As New List(Of String)

#Region "Fields"

    Protected _hasData As Integer

    Private _isDirty As Boolean
    Private _tabPage As XtraTabPage
    Private _tabVisited As Boolean

    Protected _validating As Boolean 'jjc 03.2814
    Protected _validatingCount As Short = 0 'jjc 03.2814

#End Region 'Fields

#Region "Constructors"

    Public Sub New(ByVal tPage As XtraTabPage)
        TabPage = tPage
        RuleName = tPage.Name

    End Sub

#End Region 'Constructors

#Region "Properties"

    Public Property HasData() As Integer
        Get
            Return _hasData
        End Get
        Set(ByVal value As Integer)
            _hasData = value
        End Set
    End Property

    Public Property IsDirty() As Boolean
        Get
            Return _isDirty
        End Get
        Set(ByVal Value As Boolean)
            _isDirty = Value
        End Set
    End Property

    Public Overrides Property IsValid() As Boolean
        Get
            'Return MyBase.IsValid
            Return _isValid
        End Get
        Set(ByVal value As Boolean)
            'MyBase.IsValid = value
            _isValid = value
            UpdateTabHeaderStatus()
        End Set
    End Property

    Public Property TabPage() As XtraTabPage
        Get
            Return _tabPage
        End Get
        Set(ByVal Value As XtraTabPage)
            _tabPage = Value
        End Set
    End Property

    Public Property TabVisited() As Boolean
        Get
            Return _tabVisited
        End Get
        Set(ByVal Value As Boolean)
            _tabVisited = Value
        End Set
    End Property

#End Region 'Properties

#Region "Methods"
    'Protected ChildUserControls = New List(Of ICStatusChanged)
    'Public Sub AddStandAloneChild(child As ICStatusChanged)
    '    AddHandler child.ErrorStatusChanged, AddressOf OnErrorStatusChanged
    '    ChildUserControls.Add(child)
    'End Sub

    'Public Sub OnErrorStatusChanged(sender As Object, e As ErrorStatusChangedEventArgs)
    '    'IsValid = e.IsError
    '    Validate()
    'End Sub

    Public Overrides Sub AddChild(ByRef child As BaseEdit)
        AddChild(New ECRuleControlWrapper(child))

    End Sub

    Public Overrides Sub AddChild(ByRef child As ECRule)
        child.ParentRule = Me
        _children.Add(child)
    End Sub

    Public Overridable Overloads Sub AddChild(ByRef GrpBox As DevExpress.XtraEditors.GroupControl)
        'OLD TODO label controls can't be cast to Baseedit, may need to change this to "Control" instead... not sure...
        'really shouldnt' be trying to add a label anywho....
        Try
            For Each child As Control In GrpBox.Controls
                If TypeOf child Is CheckEdit Then
                    If child.Tag Is Nothing Then
                        AddChild(New ECRuleControlWrapper(child))
                    End If
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    'I openly admit, i should be drawn and quartered for this....
    Public Function HasErrorsIgnoreRule(ByVal prule As ECRule) As Boolean
        For Each rule As ECRule In Me.Children
            If prule.Equals(rule) Then
                Continue For
            End If

            If TypeOf rule Is ECRuleTabBase Then
                For Each innerrule As ECRule In rule.Children
                    If prule.Equals(innerrule) Then
                        Continue For
                    End If
                    If Not innerrule.IsValid Then
                        Return True
                    End If
                    'tabBase.HasErrorsIgnoreRule(prule)
                Next
                'Continue For
            Else
                If Not rule.IsValid Then
                    Return True
                End If
            End If
        Next

        Return False
    End Function

    Public Overrides Function HasValue() As Boolean
        Return True
    End Function

    Public Overrides Function HasValueCount() As Integer
        Dim count = MyBase.HasValueCount()
        If count > 0 Then Return count
        'For Each child As ICStatusChanged In ChildUserControls
        '    If child.HasData Then
        '        count += 1
        '    End If
        'Next

        Return HasValueCount
    End Function

    Public Overridable Sub UpdateTabHeaderStatus()

        If TabPage.PageEnabled = False Then
            Me.TabPage.ImageIndex = ECRuleTabStatus.Blank
            Return
        End If

        If IsValid = False Then
            Me.TabPage.ImageIndex = ECRuleTabStatus.HasError
        ElseIf HasData > 0 Then
            Me.TabPage.ImageIndex = ECRuleTabStatus.HasData
        Else
            Me.TabPage.ImageIndex = ECRuleTabStatus.Blank
        End If

    End Sub

    Public Overrides Function Validate() As Boolean
        Validate = False 'To eliminate compile waring
        If Me.RuleName = "TTObsTimesSubTab" Then
            'Debugger.Break()
            Debug.WriteLine("ECRuleTabBase.Validate()")
        End If

        If TabPage.PageEnabled = False OrElse TabPage.PageVisible = False Then
            'If TabPage.Name = "TriageTabPage" Then
            '    Debug.WriteLine("break")
            'End If
            IsValid = True
            Return IsValid
        End If

        If SuspendValidation = True Then Return True

        Try
            _validatingCount += 1
            'JJC 10.14.08: Commented out -----------start--------------------
            'If a rule updates another control during validation and we then just return from
            'here, instead of validating there is a chance that some validation will be missed.
            'The downside is ther is going to be excessive redundant validation...
            '
            'However, i think this logic would be ok, if the dependantchildren properly was correclty
            'set to validate any other controls that depend on any control being changed... right?

            'If this logic is later added back in, don't to look at (rewrite) the logic below
            'in the "try/final" clause so that _validate isn't cleared if we are aborting validation
            'because we are already validating....
            '.... ---> two years later... i have no idea what the above paragraph says... it appears an alien wrote it...

            If _validating Then
                Return True
            Else
                _validating = True
            End If

            HasData = 0
            Validate = True
            For Each child As ECRule In _children
                If child.Validate() = False Then
                    Validate = False
                End If
            Next

            For Each child As ECRule In _dependentChildren
                If child.Validate() = False Then
                    Validate = False
                End If
            Next

            'For Each child As ICStatusChanged In ChildUserControls
            '    If child.Validate() = False Then
            '        Validate = False
            '    Else
            '        If child.HasData Then
            '            HasData += 1
            '        End If
            '    End If
            'Next

        Finally
            _validating = False
            IsValid = Validate

            If HasData Then
                If ParentRule IsNot Nothing Then
                    If TypeOf ParentRule Is ECRuleTab Then
                        DirectCast(ParentRule, ECRuleTab).HasData += 1

                    ElseIf TypeOf ParentRule Is ECRuleTabbase Then
                        DirectCast(ParentRule, ECRuleTabBase).HasData += 1

                    ElseIf TypeOf ParentRule Is ECRuleTabV2 Then
                        DirectCast(ParentRule, ECRuleTabV2).HasData += 1
                    End If
                End If
            End If


        End Try
        Return Validate
    End Function

#End Region 'Methods

End Class

Option Infer On
Imports DevExpress.XtraEditors

Public Class ECRuleInjectionRow2014
    Inherits ECRule

    Protected _dateTimeRule As ObsTab.ECRuleConfigurableDateTimeV2
    Protected _medicationControlRule As ECRuleControlWrapper
    Protected _RouteAndResponseRule As ECRuleInjectionRouteAndResponse2014Rule

    Private _controls As ECRuleInjectionRow2014Controls

    Public Sub New(ByVal controls As ECRuleInjectionRow2014Controls, Optional ByVal ivpushTimeRequired As Boolean = True)
        _controls = controls

        _RouteAndResponseRule = New ECRuleInjectionRouteAndResponse2014Rule(controls.Route, controls.Response, controls.ResponseRequired)

        _dateTimeRule = New ObsTab.ECRuleConfigurableDateTimeV2(controls.GetWorkingDay, controls.GetValidDates, controls.DateControl, controls.Time, True, True, False)

        _medicationControlRule = New ECRuleControlWrapper(controls.Medication)

        AddChild(_dateTimeRule)
        AddChild(_medicationControlRule)
        AddChild(_RouteAndResponseRule)

        AddHandler _controls.Route.EditValueChanged, AddressOf RouteChangedEventHandler
        AddHandler _controls.Medication.EditValueChanged, AddressOf MedicationChangedEventHandler
    End Sub

    Public Overrides Function HasValue() As Boolean
        If HasValueCount() > 0 Then
            Return True
        Else
            Return False
        End If
    End Function

    Private Sub MedicationChangedEventHandler(ByVal sender As Object, ByVal e As EventArgs)
        If _medicationControlRule.HasValue Then
            If _dateTimeRule.IsDateBlank Then
                _dateTimeRule.AutoPopulateDate()
            End If
        End If
    End Sub
    ''' <summary>
    ''' Require a time if Route is an IV PUSH, and populate date to default
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    ''' <remarks></remarks>
    Private Sub RouteChangedEventHandler(ByVal sender As Object, ByVal e As EventArgs)

        If IsIVPush() Then
            _dateTimeRule.TimeRequired = True
        Else
            _dateTimeRule.TimeRequired = False
        End If

        If _controls.Route.EditValue = Nothing OrElse _controls.Route.EditValue = "" Then
            Exit Sub
        End If

        If _RouteAndResponseRule.HasValue Then
            If _controls.DateControl.EditValue Is Nothing Then
                _dateTimeRule.AutoPopulateDate()
            End If

            'Try 'once  a date control actually has a date, the below logic will throw an exception everytime... that's not good!
            '    If _controls.DateControl.EditValue = "" Then
            '        _dateTimeRule.AutoPopulateDate()
            '    End If
            'Catch ex As Exception
            '     Debug.Assert(False) 'jonathan... shame on you...
            'End Try
        End If
    End Sub

    Public Function IsIVPush() As Boolean
        Return _RouteAndResponseRule.IsIVPush
    End Function

    Public Overrides Function Validate() As Boolean
        _RouteAndResponseRule.Validate()
        _dateTimeRule.Validate()

        If _RouteAndResponseRule.IsValid = False Then
            IsValid = False
            Return False
        End If

        If _dateTimeRule.IsValid = False Then
            IsValid = False
            Return IsValid
        End If

        If (_dateTimeRule.HasValue Or _medicationControlRule.HasValue) And Not _RouteAndResponseRule.HasValue Then
            IsValid = False
            Return False
        End If

        If _RouteAndResponseRule.HasValue And Not _dateTimeRule.HasValue Then
            IsValid = False
            Return False
        End If

        If HasValue() And _medicationControlRule.HasValue = False Then
            IsValid = False
            Return False
        End If

        IsValid = True
        Return IsValid

    End Function

#Region "Classes"

    ''' <summary>
    ''' writen for 2014 Modification to ObsInjectionRows
    ''' </summary>
    ''' <remarks></remarks>
    Public Class ECRuleInjectionRow2014Controls

        Public LineLabel As LabelControl
        Public DateControl As DateEdit
        Public Time As TextEdit
        Public Medication As BaseEdit

        Public Route As ComboBoxEdit 'Called Injection in some places
        Public Response As ComboBoxEdit 'Only used on Facility Medciations Tab
        Public ResponseRequired As Boolean

        Public GetValidDates As ObsTab.IGetValidDates
        Public GetWorkingDay As SimpleWorkingDay
        'Public ArrivalDate As ECRuleArrivalDateTimeND
        'Public DispostionDate As ECRuleDateTimeND

    End Class

    ''' <summary>
    ''' This rule basically requires a Response if one is required... and also checks for a Route
    ''' </summary>
    ''' <remarks> Note, there is no Response for Med Injections for Obs, but there is one for Fac Medications so we've added one
    ''' to make future refactoring easier.
    ''' </remarks>
    Public Class ECRuleInjectionRouteAndResponse2014Rule
        Inherits ECRule

#Region "Fields"

        Protected RouteControlWrapper As ECRuleControlWrapper
        Protected ResponseObj As ECRule = Nothing
        Protected ResponseRequired As Boolean

#End Region 'Fields

#Region "Constructors"

        Public Sub New(ByRef pRoute As ComboBoxEdit, Optional ByRef pResponse As ComboBoxEdit = Nothing, Optional ByVal ResponseRequired As Boolean = False)
            RouteControlWrapper = New ECRuleControlWrapper(pRoute)

            If pResponse IsNot Nothing Then
                ResponseObj = New ECRuleControlWrapper(pResponse)
            End If

            Me.ResponseRequired = ResponseRequired
            AddChild(RouteControlWrapper)
            If ResponseObj IsNot Nothing Then
                AddChild(ResponseObj)
            End If

        End Sub

        Private Sub New(ByRef pRouteRule As ECRuleControlWrapper, ByRef pResponseObj As ECRule, ByVal ResponseRequired As Boolean)
            Me.RouteControlWrapper = pRouteRule
            Me.ResponseObj = pResponseObj

            Me.ResponseRequired = ResponseRequired
            AddChild(RouteControlWrapper)
            AddChild(ResponseObj)
        End Sub

#End Region 'Constructors

#Region "Methods"

        Public Overrides Function HasValue() As Boolean
            If HasValueCount() > 0 Then
                Return True
            Else
                Return False
            End If
        End Function

        Public Function IsIVPush() As Boolean
            Dim IvPush As Boolean = False

            Dim InfusionCbo As ComboBoxEdit = DirectCast(RouteControlWrapper.InnerControl, ComboBoxEdit)
            Debug.Assert(InfusionCbo IsNot Nothing)

            If InfusionCbo.EditValue = "IV Push" Then
                IvPush = True
            End If

            Return IvPush
        End Function

        Public Overrides Function Validate() As Boolean
            If ResponseObj IsNot Nothing Then
                If Me.RouteControlWrapper.HasValue Then
                    Me.ResponseObj.Required = Me.ResponseRequired
                Else
                    Me.ResponseObj.Required = False
                End If

                ResponseObj.Validate()

                If Me.ResponseObj.IsValid = False Then
                    IsValid = False
                    Return IsValid
                End If

                If Me.ResponseObj.HasValue And Me.RouteControlWrapper.HasValue = False Then
                    IsValid = False
                    Return IsValid
                End If
            End If

            IsValid = True
            Return IsValid
        End Function

#End Region 'Methods

    End Class
#End Region

End Class
﻿{
  "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json",
  "version": "2.1.0",
  "runs": [
    {
      "tool": {
        "driver": {
          "name": "Back up project",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.Backup.BackupStep",
              "fullDescription": {
                "text": "Project backed up to C:\\aic\\Experimental\\SourceCode\\ENChartCAC\\ECConfig.backup"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.Backup.BackupStep",
          "message": {
            "text": "Complete: Project backed up to C:\\aic\\Experimental\\SourceCode\\ENChartCAC\\ECConfig.backup"
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig.backup"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Convert project file to SDK style",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.ProjectFormat.TryConvertProjectConverterStep",
              "fullDescription": {
                "text": "Project file converted successfully! The project may require additional changes to build successfully against the new .NET target."
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.ProjectFormat.TryConvertProjectConverterStep",
          "message": {
            "text": "Complete: Project file converted successfully! The project may require additional changes to build successfully against the new .NET target."
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Remove reference 'System.configuration'",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.Reference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
              "fullDescription": {
                "text": "Remove reference 'System.configuration'"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.Reference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
          "message": {
            "text": "Complete: Remove reference 'System.configuration'"
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Add package 'System.Configuration.ConfigurationManager'",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.NuGetReference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
              "fullDescription": {
                "text": "Add package 'System.Configuration.ConfigurationManager'"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.NuGetReference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
          "message": {
            "text": "Complete: Add package 'System.Configuration.ConfigurationManager'"
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Add package 'Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers'",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.NuGetReference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
              "fullDescription": {
                "text": "Add package 'Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers'"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.Packages.PackageUpdaterStep+PackageManipulationStep`1[[Microsoft.DotNet.UpgradeAssistant.NuGetReference, Microsoft.DotNet.UpgradeAssistant.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35]]",
          "message": {
            "text": "Complete: Add package 'Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers'"
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Update TFM",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "Microsoft.DotNet.UpgradeAssistant.Steps.ProjectFormat.SetTFMStep",
              "fullDescription": {
                "text": "Updated TFM to net7.0-windows"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "Microsoft.DotNet.UpgradeAssistant.Steps.ProjectFormat.SetTFMStep",
          "message": {
            "text": "Complete: Updated TFM to net7.0-windows"
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    },
    {
      "tool": {
        "driver": {
          "name": "Default Font API Alert",
          "semanticVersion": "",
          "informationUri": "https://github.com/dotnet/upgrade-assistant#usage",
          "rules": [
            {
              "id": "UA209",
              "name": "Microsoft.DotNet.UpgradeAssistant.Extensions.Windows.WinformsDefaultFontUpdater",
              "fullDescription": {
                "text": "Default Font API Alert"
              },
              "helpUri": "about:blank"
            }
          ]
        }
      },
      "results": [
        {
          "ruleId": "UA209",
          "message": {
            "text": "Success: Default font in Windows Forms has been changed from Microsoft Sans Serif to Seg Segoe UI, in order to change the default font use the API - Application.SetDefaultFont(Font font). For more details see here - https://devblogs.microsoft.com/dotnet/whats-new-in-windows-forms-in-net-6-0-preview-5/#application-wide-default-font."
          },
          "locations": [
            {
              "physicalLocation": {
                "artifactLocation": {
                  "uri": "file:///C:/aic/Experimental/SourceCode/ENChartCAC/ECConfig/ecconfig.vbproj"
                },
                "region": {}
              }
            }
          ]
        }
      ],
      "columnKind": "utf16CodeUnits"
    }
  ]
}
Imports System.Collections.Specialized
Imports System.Reflection
Imports System.IO
Imports DevExpress
Imports DevExpress.Xpo
Imports EnchartDOLib
Imports McKesson.HIC.ColdFeed.ChartObjWrapper.ReasonEnum

Namespace ColdFeed
    'Example of Simple IShowMessage implementation
    Public Class DefaultShowMessage
        Implements IShowMessage

        Private ShowMessageCallBack As ShowMessageDelegate

        Public Sub ShowMsg(ByVal pMsg As String) Implements IShowMessage.ShowMsg
            'Console.WriteLine(pMsg)
            If ShowMessageCallBack Is Nothing Then
                Return '... this does not get set in testing modul which causes an error... perhaps look into this later
            End If
            ShowMessageCallBack(String.Format("{0}: {1}", Now, pMsg))
        End Sub


        Public Sub SetShowMessageCallback(ByVal cb As ShowMessageDelegate) Implements IShowMessage.SetShowMessageCallback
            ShowMessageCallBack = cb
        End Sub
    End Class
End Namespace

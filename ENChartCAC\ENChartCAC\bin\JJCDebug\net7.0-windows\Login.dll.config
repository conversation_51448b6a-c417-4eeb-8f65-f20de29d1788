﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/iMaginary</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value>Tahoma;9</value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>PerMonitorV2</value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <system.diagnostics>
    <sources>
      <!-- This section defines the logging configuration for My.Application.Log -->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog" />
          <!-- Uncomment the below section to write to the Application Event Log -->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information" />
    </switches>
    <sharedListeners>
      <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
      <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
      <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
    </sharedListeners>
  </system.diagnostics>
</configuration>
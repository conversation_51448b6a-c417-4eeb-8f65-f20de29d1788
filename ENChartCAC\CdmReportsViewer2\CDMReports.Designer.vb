<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CDMReports
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.ESPRowBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCDMCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCDMLongName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPhysicianCDMCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCDMHCPCS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colQuantity = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.btnPrint = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.btnExpand = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCollapse = New DevExpress.XtraEditors.SimpleButton()
        Me.bntSaveLayout = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.cboTreatmentArea = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.beiFacLookUp = New DevExpress.XtraEditors.LookUpEdit()
        Me.FacLookUpBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.cboLayout = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teLayoutName = New DevExpress.XtraEditors.TextEdit()
        Me.btnRefresh = New DevExpress.XtraEditors.SimpleButton()
        Me.btnClose = New DevExpress.XtraEditors.SimpleButton()
        Me.chkHideQuantity = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ESPRowBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboTreatmentArea.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beiFacLookUp.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FacLookUpBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboLayout.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teLayoutName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkHideQuantity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.DataSource = Me.ESPRowBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(13, 86)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemTextEdit1})
        Me.GridControl1.Size = New System.Drawing.Size(962, 545)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCDMCode, Me.colCDMLongName, Me.colPhysicianCDMCode, Me.colCDMHCPCS, Me.colQuantity})
        Me.GridView1.CustomizationFormBounds = New System.Drawing.Rectangle(972, 616, 208, 191)
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "", Nothing, "")})
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsDetail.EnableMasterViewMode = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        '
        'colCDMCode
        '
        Me.colCDMCode.Caption = "CDM"
        Me.colCDMCode.FieldName = "CDM"
        Me.colCDMCode.Name = "colCDMCode"
        Me.colCDMCode.Visible = True
        Me.colCDMCode.VisibleIndex = 2
        '
        'colCDMLongName
        '
        Me.colCDMLongName.Caption = "Long Name"
        Me.colCDMLongName.FieldName = "LongName"
        Me.colCDMLongName.Name = "colCDMLongName"
        Me.colCDMLongName.Visible = True
        Me.colCDMLongName.VisibleIndex = 1
        Me.colCDMLongName.Width = 537
        '
        'colPhysicianCDMCode
        '
        Me.colPhysicianCDMCode.Caption = "Physician CDM"
        Me.colPhysicianCDMCode.FieldName = "PhysicianCDM"
        Me.colPhysicianCDMCode.Name = "colPhysicianCDMCode"
        Me.colPhysicianCDMCode.Visible = True
        Me.colPhysicianCDMCode.VisibleIndex = 3
        Me.colPhysicianCDMCode.Width = 130
        '
        'colCDMHCPCS
        '
        Me.colCDMHCPCS.Caption = "HCPCS"
        Me.colCDMHCPCS.FieldName = "HCPCS"
        Me.colCDMHCPCS.Name = "colCDMHCPCS"
        Me.colCDMHCPCS.Visible = True
        Me.colCDMHCPCS.VisibleIndex = 0
        Me.colCDMHCPCS.Width = 120
        '
        'colQuantity
        '
        Me.colQuantity.AppearanceCell.Options.UseTextOptions = True
        Me.colQuantity.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.colQuantity.Caption = "Qty."
        Me.colQuantity.FieldName = "Quantity"
        Me.colQuantity.Name = "colQuantity"
        Me.colQuantity.Visible = True
        Me.colQuantity.VisibleIndex = 4
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        Me.RepositoryItemTextEdit1.NullText = "NULL"
        '
        'btnPrint
        '
        Me.btnPrint.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnPrint.Location = New System.Drawing.Point(389, 662)
        Me.btnPrint.Name = "btnPrint"
        Me.btnPrint.Size = New System.Drawing.Size(221, 23)
        Me.btnPrint.TabIndex = 19
        Me.btnPrint.Text = "Print List"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Location = New System.Drawing.Point(12, 19)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(44, 16)
        Me.LabelControl1.TabIndex = 21
        Me.LabelControl1.Text = "Facility"
        '
        'btnExpand
        '
        Me.btnExpand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExpand.Location = New System.Drawing.Point(625, 662)
        Me.btnExpand.Name = "btnExpand"
        Me.btnExpand.Size = New System.Drawing.Size(83, 23)
        Me.btnExpand.TabIndex = 22
        Me.btnExpand.Text = "Expand All"
        Me.btnExpand.Visible = False
        '
        'btnCollapse
        '
        Me.btnCollapse.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCollapse.Location = New System.Drawing.Point(743, 662)
        Me.btnCollapse.Name = "btnCollapse"
        Me.btnCollapse.Size = New System.Drawing.Size(83, 23)
        Me.btnCollapse.TabIndex = 23
        Me.btnCollapse.Text = "Collapse All"
        Me.btnCollapse.Visible = False
        '
        'bntSaveLayout
        '
        Me.bntSaveLayout.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bntSaveLayout.Location = New System.Drawing.Point(549, 12)
        Me.bntSaveLayout.Name = "bntSaveLayout"
        Me.bntSaveLayout.Size = New System.Drawing.Size(130, 23)
        Me.bntSaveLayout.TabIndex = 24
        Me.bntSaveLayout.Text = "Save  Layout As "
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl6.Appearance.Options.UseFont = True
        Me.LabelControl6.Location = New System.Drawing.Point(12, 49)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(104, 16)
        Me.LabelControl6.TabIndex = 48
        Me.LabelControl6.Text = "Treatment Area"
        '
        'cboTreatmentArea
        '
        Me.cboTreatmentArea.Location = New System.Drawing.Point(131, 48)
        Me.cboTreatmentArea.Name = "cboTreatmentArea"
        Me.cboTreatmentArea.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboTreatmentArea.Properties.Sorted = True
        Me.cboTreatmentArea.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboTreatmentArea.Size = New System.Drawing.Size(202, 20)
        Me.cboTreatmentArea.TabIndex = 47
        '
        'beiFacLookUp
        '
        Me.beiFacLookUp.Location = New System.Drawing.Point(62, 15)
        Me.beiFacLookUp.Name = "beiFacLookUp"
        Me.beiFacLookUp.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.beiFacLookUp.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Facility", "Facility", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("FacilityID", "FacilityID", 100, DevExpress.Utils.FormatType.None, "", False, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("FacPtr", "FacPtr", 50, DevExpress.Utils.FormatType.None, "", False, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("EID", "EID", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Company", "Company", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CompanyClient", "CompanyClient", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.beiFacLookUp.Properties.DataSource = Me.FacLookUpBindingSource
        Me.beiFacLookUp.Properties.DisplayMember = "Facility"
        Me.beiFacLookUp.Properties.PopupWidth = 800
        Me.beiFacLookUp.Size = New System.Drawing.Size(271, 20)
        Me.beiFacLookUp.TabIndex = 49
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl2.Appearance.Options.UseFont = True
        Me.LabelControl2.Location = New System.Drawing.Point(599, 41)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(80, 16)
        Me.LabelControl2.TabIndex = 51
        Me.LabelControl2.Text = "Load Layout"
        '
        'cboLayout
        '
        Me.cboLayout.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cboLayout.Location = New System.Drawing.Point(701, 48)
        Me.cboLayout.Name = "cboLayout"
        Me.cboLayout.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboLayout.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboLayout.Size = New System.Drawing.Size(274, 20)
        Me.cboLayout.TabIndex = 50
        '
        'teLayoutName
        '
        Me.teLayoutName.Location = New System.Drawing.Point(701, 16)
        Me.teLayoutName.Name = "teLayoutName"
        Me.teLayoutName.Size = New System.Drawing.Size(274, 20)
        Me.teLayoutName.TabIndex = 61
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnRefresh.Location = New System.Drawing.Point(13, 662)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Size = New System.Drawing.Size(83, 23)
        Me.btnRefresh.TabIndex = 62
        Me.btnRefresh.Text = "Refresh"
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnClose.Location = New System.Drawing.Point(892, 662)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Size = New System.Drawing.Size(83, 23)
        Me.btnClose.TabIndex = 63
        Me.btnClose.Text = "Close"
        '
        'chkHideQuantity
        '
        Me.chkHideQuantity.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkHideQuantity.EditValue = True
        Me.chkHideQuantity.Location = New System.Drawing.Point(13, 637)
        Me.chkHideQuantity.Name = "chkHideQuantity"
        Me.chkHideQuantity.Properties.Caption = "Hide HCPCS with Quantities"
        Me.chkHideQuantity.Size = New System.Drawing.Size(154, 19)
        Me.chkHideQuantity.TabIndex = 64
        '
        'CDMReports
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnClose
        Me.ClientSize = New System.Drawing.Size(999, 697)
        Me.Controls.Add(Me.chkHideQuantity)
        Me.Controls.Add(Me.btnClose)
        Me.Controls.Add(Me.btnRefresh)
        Me.Controls.Add(Me.teLayoutName)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.cboLayout)
        Me.Controls.Add(Me.beiFacLookUp)
        Me.Controls.Add(Me.LabelControl6)
        Me.Controls.Add(Me.cboTreatmentArea)
        Me.Controls.Add(Me.bntSaveLayout)
        Me.Controls.Add(Me.btnCollapse)
        Me.Controls.Add(Me.btnExpand)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.btnPrint)
        Me.Controls.Add(Me.GridControl1)
        Me.Name = "CDMReports"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "CDM Editor"
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ESPRowBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboTreatmentArea.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beiFacLookUp.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FacLookUpBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboLayout.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teLayoutName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkHideQuantity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout

End Sub
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colCDMCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDMLongName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnPrint As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents colPhysicianCDMCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDMHCPCS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnExpand As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCollapse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntSaveLayout As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboTreatmentArea As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents beiFacLookUp As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents FacLookUpBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboLayout As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ESPRowBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents colQuantity As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents teLayoutName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnRefresh As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnClose As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents chkHideQuantity As DevExpress.XtraEditors.CheckEdit
End Class

﻿Imports System.ComponentModel
Imports System.Runtime.CompilerServices
'Imports System.Runtime.Remoting.Channels

Namespace CAM2
    Public Class UserAllocationsCollection
        Public Charges As List(Of Charge)

        Public Sub New()
            Charges = New List(Of Charge)
        End Sub

        Public Function HasData() As Boolean
            Try
                For Each charge In Charges
                    If charge.ChargeAllocations.Count > 0 Then Return True
                    If charge.Modifiers.Count > 0 Then Return True
                Next
                Return False
            Catch ex As Exception
                Debug.Assert(False, "HasData Exception")
                Return False
            End Try

            Return False
        End Function

    End Class

    ''' <summary>
    ''' A single charge from ESS that can be allocated and assigned modifiers.
    ''' </summary>
    <DebuggerDisplay("HCPCS:{Hcpcs}, Units:{TotalUnits}, TA:{TreatmentArea} Date:{OriginalBillingdate}")>
    Public Class Charge
        Implements INotifyPropertyChanged

        Private _TotalUnits As Integer
        Public Event PropertyChanged As PropertyChangedEventHandler Implements INotifyPropertyChanged.PropertyChanged
        Public Property Version As String = "001"
        'Public Property ChartOid As Integer
        Public Property Hcpcs As String
        Public Property ChargeMaster As String
        Public Property TotalUnits As Integer
            Get
                If _TotalUnits <= 0 Then
                    _TotalUnits = 1
                End If
                Return _TotalUnits
            End Get
            Set
                _TotalUnits = Value
            End Set
        End Property

        Public Property OriginalBillingdate As Date
        Public Property TreatmentArea As String
        Public Property CodeType As String 'FACILITY' or 'OBS'
        Public Property ESPValue As String
        Public Property Description As String
        Public Property ChargeAllocations As New List(Of ChargeAllocation)
        Public Property Modifiers As New List(Of ChargeModifier)
        'Public Property Modifiers As New List(Of String)

        Public Sub New(hcpcs As String)
            Me.Hcpcs = hcpcs
        End Sub
    End Class

    <DebuggerDisplay("Units{Units}, TA:{TreatmentArea} Date:{UserDefinedBillingdate}")>
    Public Class ChargeAllocation
        Implements INotifyPropertyChanged

        Private _Units As Integer
        Private _UserDefinedBillingdate As Date
        Private _TreatmentArea As String
        Private _TreatmentAreaType As String
        Private _CDM As String

        Public Property Physician As String

        Public Property Units As Integer
            Get
                Return _Units
            End Get
            Set
                If _Units <> Value Then
                    OnPropertyChanged()
                End If
                _Units = Value
            End Set
        End Property

        Public Property UserDefinedBillingdate As Date
            Get
                Return _UserDefinedBillingdate
            End Get
            Set
                _UserDefinedBillingdate = Value
            End Set
        End Property

        Public Property TreatmentArea As String
            Get
                Return _TreatmentArea
            End Get
            Set
                If _TreatmentArea <> Value Then
                    OnPropertyChanged()
                End If
                _TreatmentArea = Value
            End Set
        End Property

        Public Property TreatmentAreaType As String 'FACILITY' or 'OBS'
            Get
                Return _TreatmentAreaType
            End Get
            Set
                If _TreatmentAreaType <> Value Then
                    OnPropertyChanged()
                End If
                _TreatmentAreaType = Value
            End Set
        End Property

        Public Property CDM As String
            Get
                Return _CDM
            End Get
            Set
                If _CDM <> Value Then
                    OnPropertyChanged()
                End If
                _CDM = Value
            End Set
        End Property

        Public Event PropertyChanged As PropertyChangedEventHandler Implements INotifyPropertyChanged.PropertyChanged

        Public Sub OnPropertyChanged(<CallerMemberName> Optional propertyName As String = "")

        End Sub

    End Class

    Public Class ChargeModifier
        Public Sub New(modifier As String)
            Me.Modifier = modifier
        End Sub

        Public Property Modifier() As String
    End Class

End Namespace
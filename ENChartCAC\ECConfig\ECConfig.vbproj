﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>%24/VIC/Main_Next/SourceCode/ENChartCAC/ECConfig</SccProjectName>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <SccAuxPath>https://tfs.cloud.mdrxdev.com/tfs/collection1-eis</SccAuxPath>
    <SccLocalPath>.</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net7.0-windows</TargetFramework>
    <OutputType>WinExe</OutputType>
    <StartupObject>ECConfig.My.MyApplication</StartupObject>
    <MyType>WindowsForms</MyType>
    <!--<GenerateAssemblyInfo>false</GenerateAssemblyInfo>-->
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>False</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <!--<ValidateExecutableReferencesMatchSelfContained>false</ValidateExecutableReferencesMatchSelfContained>-->
    <Configurations>Debug;Release;JJCDebug</Configurations>
    <Platforms>AnyCPU;</Platforms>
    <ImportedNamespaces>EnchartDOLib=False,System.Windows.Forms=False,Microsoft.VisualBasic=True,System=True,System.Collections=True,System.Collections.Generic=True,System.Diagnostics=True,System.Linq=True,System.Xml.Linq=True,System.Threading.Tasks=True,ECConfig=True</ImportedNamespaces>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='JJCDebug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <Import Include="EnchartDOLib" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>

  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\McKessonIntelligentCoding\DataLink.Paragon.Data\DataLink.Paragon.Data.vbproj" />
    <ProjectReference Include="..\..\Timeout\Timeout\Timeout.vbproj" />
    <ProjectReference Include="..\CdmReportsViewer2\CDMReportViewer2.vbproj" />
    <ProjectReference Include="..\ColdFeedExport\McKeesson.HIC.HPFColdFeed.dll\McKesson.HIC.HPFColdFeed.vbproj" />
    <ProjectReference Include="..\ColdFeedExport\McKesson.HIC.ChargeSummaryColdFeedHelper\McKesson.HIC.ChargeSummaryColdFeedHelper.vbproj" />
    <ProjectReference Include="..\ENChartCAC\ENChartCAC.vbproj" />
    <ProjectReference Include="..\EnchartDOLib\EnchartDOLib.vbproj" />
    <ProjectReference Include="..\MICCustomDataProviders\MICCustomConnectionProviders.csproj" />
    <ProjectReference Include="..\MICMiscUtilCSharp\MICMiscUtilCSharp.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\DefaultFacilitySettings.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DevExpress.Win.Design" Version="23.1.5" />
    <PackageReference Include="Mapster.Core">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
    <!--<PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.355802">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>-->
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="ManageLicenseAndConfig.Designer.vb" />
    <Compile Remove="ManageLicenseAndConfig.vb" />
    <Compile Remove="ModWorkStation.Designer.vb" />
    <Compile Remove="ModWorkStation.vb" />
    <EmbeddedResource Remove="ManageLicenseAndConfig.resx" />
    <EmbeddedResource Remove="ModWorkStation.resx" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="EnchartDOLib" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <Import Project="..\..\build\CustomPostBuild.targets" />
</Project>
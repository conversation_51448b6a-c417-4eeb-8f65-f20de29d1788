<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ScratchPadForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        GroupControl2 = New GroupControl()
        XtraScrollableControl9 = New XtraScrollableControl()
        Infusion_Hydrations05_lbl = New LabelControl()
        Infusion_Hydrations04_lbl = New LabelControl()
        Infusion_Hydrations03_lbl = New LabelControl()
        Infusion_Hydrations02_lbl = New LabelControl()
        Infusion_Hydrations01_lbl = New LabelControl()
        Infusion_HydrationsMins05_lbl = New LabelControl()
        Infusion_HydrationsMins04_lbl = New LabelControl()
        Infusion_HydrationsMins03_lbl = New LabelControl()
        Infusion_HydrationsMins02_lbl = New LabelControl()
        Infusion_HydrationsMins01_lbl = New LabelControl()
        Infusion_HydrationsEndTime04_txt = New TextEdit()
        Infusion_HydrationsEndDate04_dte = New DateEdit()
        Infusion_HydrationsEndTime03_txt = New TextEdit()
        Infusion_HydrationsEndDate03_dte = New DateEdit()
        Infusion_HydrationsEndTime05_txt = New TextEdit()
        Infusion_HydrationsEndTime02_txt = New TextEdit()
        Infusion_HydrationsEndTime01_txt = New TextEdit()
        Infusion_HydrationsEndDate05_dte = New DateEdit()
        Infusion_HydrationsEndDate02_dte = New DateEdit()
        Infusion_HydrationsEndDate01_dte = New DateEdit()
        Infusion_HydrationsStartTime04_txt = New TextEdit()
        Infusion_HydrationsStartDate04_dte = New DateEdit()
        Infusion_HydrationsStartTime03_txt = New TextEdit()
        Infusion_HydrationsStartDate03_dte = New DateEdit()
        Infusion_HydrationsStartTime05_txt = New TextEdit()
        Infusion_HydrationsStartTime02_txt = New TextEdit()
        Infusion_HydrationsStartTime01_txt = New TextEdit()
        Infusion_HydrationsStartDate05_dte = New DateEdit()
        Infusion_HydrationsStartDate02_dte = New DateEdit()
        Infusion_HydrationsStartDate01_dte = New DateEdit()
        Infusion_HydrationsRate05_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate04_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate03_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate02_cbo = New ComboBoxEdit()
        Infusion_HydrationsRate01_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite05_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite04_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite03_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite02_cbo = New ComboBoxEdit()
        Infusion_HydrationsSite01_cbo = New ComboBoxEdit()
        LabelControl60 = New LabelControl()
        LabelControl61 = New LabelControl()
        TextEdit6 = New TextEdit()
        Infusion_HydrationsStartDate11_dte = New DateEdit()
        DateEdit2 = New DateEdit()
        TextEdit9 = New TextEdit()
        TextEdit13 = New TextEdit()
        LabelControl62 = New LabelControl()
        ComboBoxEdit1 = New ComboBoxEdit()
        LabelControl63 = New LabelControl()
        ComboBoxEdit2 = New ComboBoxEdit()
        Infusion_HydrationsStartDate12_dte = New DateEdit()
        DateEdit4 = New DateEdit()
        TextEdit14 = New TextEdit()
        ComboBoxEdit14 = New ComboBoxEdit()
        ComboBoxEdit15 = New ComboBoxEdit()
        LabelControl64 = New LabelControl()
        Infusion_Hydrations09_lbl = New LabelControl()
        DateEdit5 = New DateEdit()
        TextEdit15 = New TextEdit()
        Infusion_Hydrations08_lbl = New LabelControl()
        DateEdit6 = New DateEdit()
        Infusion_HydrationsStartDate09_dte = New DateEdit()
        Infusion_Hydrations07_lbl = New LabelControl()
        DateEdit8 = New DateEdit()
        TextEdit16 = New TextEdit()
        Infusion_Hydrations06_lbl = New LabelControl()
        TextEdit17 = New TextEdit()
        ComboBoxEdit16 = New ComboBoxEdit()
        Infusion_HydrationsStartDate08_dte = New DateEdit()
        TextEdit18 = New TextEdit()
        ComboBoxEdit17 = New ComboBoxEdit()
        TextEdit19 = New TextEdit()
        TextEdit20 = New TextEdit()
        ComboBoxEdit18 = New ComboBoxEdit()
        Infusion_HydrationsStartTime07_txt = New TextEdit()
        LabelControl69 = New LabelControl()
        DateEdit10 = New DateEdit()
        ComboBoxEdit19 = New ComboBoxEdit()
        Infusion_HydrationsStartTime06_txt = New TextEdit()
        LabelControl70 = New LabelControl()
        TextEdit23 = New TextEdit()
        ComboBoxEdit20 = New ComboBoxEdit()
        Infusion_HydrationsStartDate10_dte = New DateEdit()
        LabelControl71 = New LabelControl()
        DateEdit12 = New DateEdit()
        ComboBoxEdit21 = New ComboBoxEdit()
        Infusion_HydrationsStartDate07_dte = New DateEdit()
        LabelControl72 = New LabelControl()
        TextEdit24 = New TextEdit()
        ComboBoxEdit22 = New ComboBoxEdit()
        DateEdit14 = New DateEdit()
        LabelControl73 = New LabelControl()
        ComboBoxEdit23 = New ComboBoxEdit()
        ComboBoxEdit24 = New ComboBoxEdit()
        ComboBoxEdit25 = New ComboBoxEdit()
        LabelControl75 = New LabelControl()
        LabelControl76 = New LabelControl()
        LabelControl81 = New LabelControl()
        LabelControl82 = New LabelControl()
        LabelControl92 = New LabelControl()
        LabelControl93 = New LabelControl()
        LabelControl94 = New LabelControl()
        GridControl1 = New DevExpress.XtraGrid.GridControl()
        GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        colStartDate = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemDateEdit1 = New Repository.RepositoryItemDateEdit()
        colEndDate = New DevExpress.XtraGrid.Columns.GridColumn()
        GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemComboBox2 = New Repository.RepositoryItemComboBox()
        GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemComboBox1 = New Repository.RepositoryItemComboBox()
        GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemPopupContainerEdit1 = New Repository.RepositoryItemPopupContainerEdit()
        CType(GroupControl2, ComponentModel.ISupportInitialize).BeginInit()
        GroupControl2.SuspendLayout()
        XtraScrollableControl9.SuspendLayout()
        CType(Infusion_HydrationsEndTime04_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime03_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime05_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime02_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndTime01_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime04_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime03_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime05_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime02_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime01_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate05_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate04_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate03_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate02_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsRate01_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite05_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite04_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite03_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite02_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsSite01_cbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit6.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit2.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit2.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit9.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit13.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit1.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit2.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit4.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit4.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit14.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit14.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit15.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit5.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit5.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit15.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit6.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit6.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit8.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit8.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit16.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit17.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit16.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit18.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit17.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit19.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit20.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit18.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime07_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit10.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit10.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit19.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartTime06_txt.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit23.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit20.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit12.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit12.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit21.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit24.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit22.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit14.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DateEdit14.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit23.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit24.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ComboBoxEdit25.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridView1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemDateEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemDateEdit1.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemComboBox2, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemComboBox1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemPopupContainerEdit1, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' GroupControl2
        ' 
        GroupControl2.Controls.Add(XtraScrollableControl9)
        GroupControl2.Location = New Point(28, 12)
        GroupControl2.Margin = New Padding(2, 3, 2, 3)
        GroupControl2.Name = "GroupControl2"
        GroupControl2.Size = New Size(1211, 302)
        GroupControl2.TabIndex = 12
        GroupControl2.Text = "Hydrations"
        ' 
        ' XtraScrollableControl9
        ' 
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations05_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations04_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations03_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations02_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations01_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins05_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins04_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins03_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins02_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsMins01_lbl)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime04_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate04_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime03_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate03_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime05_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime02_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndTime01_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate05_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate02_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsEndDate01_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime04_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate04_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime03_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate03_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime05_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime02_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime01_txt)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate05_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate02_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate01_dte)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate05_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate04_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate03_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate02_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsRate01_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite05_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite04_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite03_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite02_cbo)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsSite01_cbo)
        XtraScrollableControl9.Controls.Add(LabelControl60)
        XtraScrollableControl9.Controls.Add(LabelControl61)
        XtraScrollableControl9.Controls.Add(TextEdit6)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate11_dte)
        XtraScrollableControl9.Controls.Add(DateEdit2)
        XtraScrollableControl9.Controls.Add(TextEdit9)
        XtraScrollableControl9.Controls.Add(TextEdit13)
        XtraScrollableControl9.Controls.Add(LabelControl62)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit1)
        XtraScrollableControl9.Controls.Add(LabelControl63)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit2)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate12_dte)
        XtraScrollableControl9.Controls.Add(DateEdit4)
        XtraScrollableControl9.Controls.Add(TextEdit14)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit14)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit15)
        XtraScrollableControl9.Controls.Add(LabelControl64)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations09_lbl)
        XtraScrollableControl9.Controls.Add(DateEdit5)
        XtraScrollableControl9.Controls.Add(TextEdit15)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations08_lbl)
        XtraScrollableControl9.Controls.Add(DateEdit6)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate09_dte)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations07_lbl)
        XtraScrollableControl9.Controls.Add(DateEdit8)
        XtraScrollableControl9.Controls.Add(TextEdit16)
        XtraScrollableControl9.Controls.Add(Infusion_Hydrations06_lbl)
        XtraScrollableControl9.Controls.Add(TextEdit17)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit16)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate08_dte)
        XtraScrollableControl9.Controls.Add(TextEdit18)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit17)
        XtraScrollableControl9.Controls.Add(TextEdit19)
        XtraScrollableControl9.Controls.Add(TextEdit20)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit18)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime07_txt)
        XtraScrollableControl9.Controls.Add(LabelControl69)
        XtraScrollableControl9.Controls.Add(DateEdit10)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit19)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartTime06_txt)
        XtraScrollableControl9.Controls.Add(LabelControl70)
        XtraScrollableControl9.Controls.Add(TextEdit23)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit20)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate10_dte)
        XtraScrollableControl9.Controls.Add(LabelControl71)
        XtraScrollableControl9.Controls.Add(DateEdit12)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit21)
        XtraScrollableControl9.Controls.Add(Infusion_HydrationsStartDate07_dte)
        XtraScrollableControl9.Controls.Add(LabelControl72)
        XtraScrollableControl9.Controls.Add(TextEdit24)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit22)
        XtraScrollableControl9.Controls.Add(DateEdit14)
        XtraScrollableControl9.Controls.Add(LabelControl73)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit23)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit24)
        XtraScrollableControl9.Controls.Add(ComboBoxEdit25)
        XtraScrollableControl9.Controls.Add(LabelControl75)
        XtraScrollableControl9.Controls.Add(LabelControl76)
        XtraScrollableControl9.Controls.Add(LabelControl81)
        XtraScrollableControl9.Controls.Add(LabelControl82)
        XtraScrollableControl9.Controls.Add(LabelControl92)
        XtraScrollableControl9.Controls.Add(LabelControl93)
        XtraScrollableControl9.Controls.Add(LabelControl94)
        XtraScrollableControl9.Dock = DockStyle.Fill
        XtraScrollableControl9.Location = New Point(2, 22)
        XtraScrollableControl9.Margin = New Padding(4, 3, 4, 3)
        XtraScrollableControl9.Name = "XtraScrollableControl9"
        XtraScrollableControl9.Size = New Size(1207, 278)
        XtraScrollableControl9.TabIndex = 0
        ' 
        ' Infusion_Hydrations05_lbl
        ' 
        Infusion_Hydrations05_lbl.Location = New Point(12, 101)
        Infusion_Hydrations05_lbl.Margin = New Padding(0)
        Infusion_Hydrations05_lbl.Name = "Infusion_Hydrations05_lbl"
        Infusion_Hydrations05_lbl.Size = New Size(12, 14)
        Infusion_Hydrations05_lbl.TabIndex = 409
        Infusion_Hydrations05_lbl.Text = "5)"
        ' 
        ' Infusion_Hydrations04_lbl
        ' 
        Infusion_Hydrations04_lbl.Location = New Point(12, 81)
        Infusion_Hydrations04_lbl.Margin = New Padding(0)
        Infusion_Hydrations04_lbl.Name = "Infusion_Hydrations04_lbl"
        Infusion_Hydrations04_lbl.Size = New Size(12, 14)
        Infusion_Hydrations04_lbl.TabIndex = 408
        Infusion_Hydrations04_lbl.Text = "4)"
        ' 
        ' Infusion_Hydrations03_lbl
        ' 
        Infusion_Hydrations03_lbl.Location = New Point(12, 61)
        Infusion_Hydrations03_lbl.Margin = New Padding(0)
        Infusion_Hydrations03_lbl.Name = "Infusion_Hydrations03_lbl"
        Infusion_Hydrations03_lbl.Size = New Size(12, 14)
        Infusion_Hydrations03_lbl.TabIndex = 407
        Infusion_Hydrations03_lbl.Text = "3)"
        ' 
        ' Infusion_Hydrations02_lbl
        ' 
        Infusion_Hydrations02_lbl.Location = New Point(12, 41)
        Infusion_Hydrations02_lbl.Margin = New Padding(0)
        Infusion_Hydrations02_lbl.Name = "Infusion_Hydrations02_lbl"
        Infusion_Hydrations02_lbl.Size = New Size(12, 14)
        Infusion_Hydrations02_lbl.TabIndex = 406
        Infusion_Hydrations02_lbl.Text = "2)"
        ' 
        ' Infusion_Hydrations01_lbl
        ' 
        Infusion_Hydrations01_lbl.Location = New Point(12, 22)
        Infusion_Hydrations01_lbl.Margin = New Padding(0)
        Infusion_Hydrations01_lbl.Name = "Infusion_Hydrations01_lbl"
        Infusion_Hydrations01_lbl.Size = New Size(12, 14)
        Infusion_Hydrations01_lbl.TabIndex = 405
        Infusion_Hydrations01_lbl.Text = "1)"
        ' 
        ' Infusion_HydrationsMins05_lbl
        ' 
        Infusion_HydrationsMins05_lbl.Location = New Point(376, 102)
        Infusion_HydrationsMins05_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins05_lbl.Name = "Infusion_HydrationsMins05_lbl"
        Infusion_HydrationsMins05_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins05_lbl.TabIndex = 395
        Infusion_HydrationsMins05_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins04_lbl
        ' 
        Infusion_HydrationsMins04_lbl.Location = New Point(376, 81)
        Infusion_HydrationsMins04_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins04_lbl.Name = "Infusion_HydrationsMins04_lbl"
        Infusion_HydrationsMins04_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins04_lbl.TabIndex = 388
        Infusion_HydrationsMins04_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins03_lbl
        ' 
        Infusion_HydrationsMins03_lbl.Location = New Point(376, 62)
        Infusion_HydrationsMins03_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins03_lbl.Name = "Infusion_HydrationsMins03_lbl"
        Infusion_HydrationsMins03_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins03_lbl.TabIndex = 381
        Infusion_HydrationsMins03_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins02_lbl
        ' 
        Infusion_HydrationsMins02_lbl.Location = New Point(376, 41)
        Infusion_HydrationsMins02_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins02_lbl.Name = "Infusion_HydrationsMins02_lbl"
        Infusion_HydrationsMins02_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins02_lbl.TabIndex = 374
        Infusion_HydrationsMins02_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsMins01_lbl
        ' 
        Infusion_HydrationsMins01_lbl.Location = New Point(376, 22)
        Infusion_HydrationsMins01_lbl.Margin = New Padding(0)
        Infusion_HydrationsMins01_lbl.Name = "Infusion_HydrationsMins01_lbl"
        Infusion_HydrationsMins01_lbl.Size = New Size(28, 14)
        Infusion_HydrationsMins01_lbl.TabIndex = 367
        Infusion_HydrationsMins01_lbl.Text = "9999"
        ' 
        ' Infusion_HydrationsEndTime04_txt
        ' 
        Infusion_HydrationsEndTime04_txt.Location = New Point(299, 79)
        Infusion_HydrationsEndTime04_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime04_txt.Name = "Infusion_HydrationsEndTime04_txt"
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime04_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime04_txt.TabIndex = 387
        ' 
        ' Infusion_HydrationsEndDate04_dte
        ' 
        Infusion_HydrationsEndDate04_dte.EditValue = Nothing
        Infusion_HydrationsEndDate04_dte.Location = New Point(198, 79)
        Infusion_HydrationsEndDate04_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate04_dte.Name = "Infusion_HydrationsEndDate04_dte"
        Infusion_HydrationsEndDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate04_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate04_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate04_dte.TabIndex = 386
        ' 
        ' Infusion_HydrationsEndTime03_txt
        ' 
        Infusion_HydrationsEndTime03_txt.Location = New Point(299, 59)
        Infusion_HydrationsEndTime03_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime03_txt.Name = "Infusion_HydrationsEndTime03_txt"
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime03_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime03_txt.TabIndex = 380
        ' 
        ' Infusion_HydrationsEndDate03_dte
        ' 
        Infusion_HydrationsEndDate03_dte.EditValue = Nothing
        Infusion_HydrationsEndDate03_dte.Location = New Point(198, 59)
        Infusion_HydrationsEndDate03_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate03_dte.Name = "Infusion_HydrationsEndDate03_dte"
        Infusion_HydrationsEndDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate03_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate03_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate03_dte.TabIndex = 379
        ' 
        ' Infusion_HydrationsEndTime05_txt
        ' 
        Infusion_HydrationsEndTime05_txt.Location = New Point(299, 99)
        Infusion_HydrationsEndTime05_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime05_txt.Name = "Infusion_HydrationsEndTime05_txt"
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime05_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime05_txt.TabIndex = 394
        ' 
        ' Infusion_HydrationsEndTime02_txt
        ' 
        Infusion_HydrationsEndTime02_txt.Location = New Point(299, 39)
        Infusion_HydrationsEndTime02_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime02_txt.Name = "Infusion_HydrationsEndTime02_txt"
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime02_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime02_txt.TabIndex = 373
        ' 
        ' Infusion_HydrationsEndTime01_txt
        ' 
        Infusion_HydrationsEndTime01_txt.Location = New Point(299, 19)
        Infusion_HydrationsEndTime01_txt.Margin = New Padding(0)
        Infusion_HydrationsEndTime01_txt.Name = "Infusion_HydrationsEndTime01_txt"
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsEndTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsEndTime01_txt.Size = New Size(68, 20)
        Infusion_HydrationsEndTime01_txt.TabIndex = 366
        ' 
        ' Infusion_HydrationsEndDate05_dte
        ' 
        Infusion_HydrationsEndDate05_dte.EditValue = Nothing
        Infusion_HydrationsEndDate05_dte.Location = New Point(198, 99)
        Infusion_HydrationsEndDate05_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate05_dte.Name = "Infusion_HydrationsEndDate05_dte"
        Infusion_HydrationsEndDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate05_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate05_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate05_dte.TabIndex = 393
        ' 
        ' Infusion_HydrationsEndDate02_dte
        ' 
        Infusion_HydrationsEndDate02_dte.EditValue = Nothing
        Infusion_HydrationsEndDate02_dte.Location = New Point(198, 39)
        Infusion_HydrationsEndDate02_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate02_dte.Name = "Infusion_HydrationsEndDate02_dte"
        Infusion_HydrationsEndDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate02_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate02_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate02_dte.TabIndex = 372
        ' 
        ' Infusion_HydrationsEndDate01_dte
        ' 
        Infusion_HydrationsEndDate01_dte.EditValue = Nothing
        Infusion_HydrationsEndDate01_dte.Location = New Point(198, 19)
        Infusion_HydrationsEndDate01_dte.Margin = New Padding(0)
        Infusion_HydrationsEndDate01_dte.Name = "Infusion_HydrationsEndDate01_dte"
        Infusion_HydrationsEndDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsEndDate01_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsEndDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsEndDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsEndDate01_dte.Size = New Size(100, 20)
        Infusion_HydrationsEndDate01_dte.TabIndex = 365
        Infusion_HydrationsEndDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsStartTime04_txt
        ' 
        Infusion_HydrationsStartTime04_txt.Location = New Point(138, 79)
        Infusion_HydrationsStartTime04_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime04_txt.Name = "Infusion_HydrationsStartTime04_txt"
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime04_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime04_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime04_txt.TabIndex = 385
        ' 
        ' Infusion_HydrationsStartDate04_dte
        ' 
        Infusion_HydrationsStartDate04_dte.EditValue = Nothing
        Infusion_HydrationsStartDate04_dte.Location = New Point(37, 79)
        Infusion_HydrationsStartDate04_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate04_dte.Name = "Infusion_HydrationsStartDate04_dte"
        Infusion_HydrationsStartDate04_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate04_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate04_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate04_dte.TabIndex = 384
        ' 
        ' Infusion_HydrationsStartTime03_txt
        ' 
        Infusion_HydrationsStartTime03_txt.Location = New Point(138, 59)
        Infusion_HydrationsStartTime03_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime03_txt.Name = "Infusion_HydrationsStartTime03_txt"
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime03_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime03_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime03_txt.TabIndex = 378
        ' 
        ' Infusion_HydrationsStartDate03_dte
        ' 
        Infusion_HydrationsStartDate03_dte.EditValue = Nothing
        Infusion_HydrationsStartDate03_dte.Location = New Point(37, 59)
        Infusion_HydrationsStartDate03_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate03_dte.Name = "Infusion_HydrationsStartDate03_dte"
        Infusion_HydrationsStartDate03_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate03_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate03_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate03_dte.TabIndex = 377
        ' 
        ' Infusion_HydrationsStartTime05_txt
        ' 
        Infusion_HydrationsStartTime05_txt.Location = New Point(138, 99)
        Infusion_HydrationsStartTime05_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime05_txt.Name = "Infusion_HydrationsStartTime05_txt"
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime05_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime05_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime05_txt.TabIndex = 392
        ' 
        ' Infusion_HydrationsStartTime02_txt
        ' 
        Infusion_HydrationsStartTime02_txt.Location = New Point(138, 39)
        Infusion_HydrationsStartTime02_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime02_txt.Name = "Infusion_HydrationsStartTime02_txt"
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime02_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime02_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime02_txt.TabIndex = 371
        ' 
        ' Infusion_HydrationsStartTime01_txt
        ' 
        Infusion_HydrationsStartTime01_txt.Location = New Point(138, 19)
        Infusion_HydrationsStartTime01_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime01_txt.Name = "Infusion_HydrationsStartTime01_txt"
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime01_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime01_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime01_txt.TabIndex = 364
        ' 
        ' Infusion_HydrationsStartDate05_dte
        ' 
        Infusion_HydrationsStartDate05_dte.EditValue = Nothing
        Infusion_HydrationsStartDate05_dte.Location = New Point(37, 99)
        Infusion_HydrationsStartDate05_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate05_dte.Name = "Infusion_HydrationsStartDate05_dte"
        Infusion_HydrationsStartDate05_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate05_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate05_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate05_dte.TabIndex = 391
        ' 
        ' Infusion_HydrationsStartDate02_dte
        ' 
        Infusion_HydrationsStartDate02_dte.EditValue = Nothing
        Infusion_HydrationsStartDate02_dte.Location = New Point(37, 39)
        Infusion_HydrationsStartDate02_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate02_dte.Name = "Infusion_HydrationsStartDate02_dte"
        Infusion_HydrationsStartDate02_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate02_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate02_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate02_dte.TabIndex = 370
        ' 
        ' Infusion_HydrationsStartDate01_dte
        ' 
        Infusion_HydrationsStartDate01_dte.EditValue = Nothing
        Infusion_HydrationsStartDate01_dte.Location = New Point(37, 19)
        Infusion_HydrationsStartDate01_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate01_dte.Name = "Infusion_HydrationsStartDate01_dte"
        Infusion_HydrationsStartDate01_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate01_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate01_dte.Properties.Mask.UseMaskAsDisplayFormat = True
        Infusion_HydrationsStartDate01_dte.Properties.MaskSettings.Set("showPlaceholders", False)
        Infusion_HydrationsStartDate01_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate01_dte.TabIndex = 363
        Infusion_HydrationsStartDate01_dte.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' Infusion_HydrationsRate05_cbo
        ' 
        Infusion_HydrationsRate05_cbo.Location = New Point(486, 99)
        Infusion_HydrationsRate05_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate05_cbo.Name = "Infusion_HydrationsRate05_cbo"
        Infusion_HydrationsRate05_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate05_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate05_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate05_cbo.TabIndex = 397
        ' 
        ' Infusion_HydrationsRate04_cbo
        ' 
        Infusion_HydrationsRate04_cbo.Location = New Point(486, 79)
        Infusion_HydrationsRate04_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate04_cbo.Name = "Infusion_HydrationsRate04_cbo"
        Infusion_HydrationsRate04_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate04_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate04_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate04_cbo.TabIndex = 390
        ' 
        ' Infusion_HydrationsRate03_cbo
        ' 
        Infusion_HydrationsRate03_cbo.Location = New Point(486, 59)
        Infusion_HydrationsRate03_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate03_cbo.Name = "Infusion_HydrationsRate03_cbo"
        Infusion_HydrationsRate03_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate03_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate03_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate03_cbo.TabIndex = 383
        ' 
        ' Infusion_HydrationsRate02_cbo
        ' 
        Infusion_HydrationsRate02_cbo.Location = New Point(486, 39)
        Infusion_HydrationsRate02_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate02_cbo.Name = "Infusion_HydrationsRate02_cbo"
        Infusion_HydrationsRate02_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate02_cbo.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        Infusion_HydrationsRate02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate02_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate02_cbo.TabIndex = 376
        ' 
        ' Infusion_HydrationsRate01_cbo
        ' 
        Infusion_HydrationsRate01_cbo.Location = New Point(486, 19)
        Infusion_HydrationsRate01_cbo.Margin = New Padding(0)
        Infusion_HydrationsRate01_cbo.Name = "Infusion_HydrationsRate01_cbo"
        Infusion_HydrationsRate01_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsRate01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsRate01_cbo.Size = New Size(72, 20)
        Infusion_HydrationsRate01_cbo.TabIndex = 369
        ' 
        ' Infusion_HydrationsSite05_cbo
        ' 
        Infusion_HydrationsSite05_cbo.Location = New Point(415, 99)
        Infusion_HydrationsSite05_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite05_cbo.Name = "Infusion_HydrationsSite05_cbo"
        Infusion_HydrationsSite05_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite05_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite05_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite05_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite05_cbo.TabIndex = 396
        ' 
        ' Infusion_HydrationsSite04_cbo
        ' 
        Infusion_HydrationsSite04_cbo.Location = New Point(415, 79)
        Infusion_HydrationsSite04_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite04_cbo.Name = "Infusion_HydrationsSite04_cbo"
        Infusion_HydrationsSite04_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite04_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite04_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite04_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite04_cbo.TabIndex = 389
        ' 
        ' Infusion_HydrationsSite03_cbo
        ' 
        Infusion_HydrationsSite03_cbo.Location = New Point(415, 59)
        Infusion_HydrationsSite03_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite03_cbo.Name = "Infusion_HydrationsSite03_cbo"
        Infusion_HydrationsSite03_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite03_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite03_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite03_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite03_cbo.TabIndex = 382
        ' 
        ' Infusion_HydrationsSite02_cbo
        ' 
        Infusion_HydrationsSite02_cbo.Location = New Point(415, 39)
        Infusion_HydrationsSite02_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite02_cbo.Name = "Infusion_HydrationsSite02_cbo"
        Infusion_HydrationsSite02_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite02_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite02_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite02_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite02_cbo.TabIndex = 375
        ' 
        ' Infusion_HydrationsSite01_cbo
        ' 
        Infusion_HydrationsSite01_cbo.Location = New Point(415, 19)
        Infusion_HydrationsSite01_cbo.Margin = New Padding(0)
        Infusion_HydrationsSite01_cbo.Name = "Infusion_HydrationsSite01_cbo"
        Infusion_HydrationsSite01_cbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsSite01_cbo.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        Infusion_HydrationsSite01_cbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Infusion_HydrationsSite01_cbo.Size = New Size(72, 20)
        Infusion_HydrationsSite01_cbo.TabIndex = 368
        ' 
        ' LabelControl60
        ' 
        LabelControl60.Location = New Point(12, 240)
        LabelControl60.Margin = New Padding(0)
        LabelControl60.Name = "LabelControl60"
        LabelControl60.Size = New Size(19, 14)
        LabelControl60.TabIndex = 362
        LabelControl60.Text = "12)"
        ' 
        ' LabelControl61
        ' 
        LabelControl61.Location = New Point(12, 220)
        LabelControl61.Margin = New Padding(0)
        LabelControl61.Name = "LabelControl61"
        LabelControl61.Size = New Size(19, 14)
        LabelControl61.TabIndex = 361
        LabelControl61.Text = "11)"
        ' 
        ' TextEdit6
        ' 
        TextEdit6.Location = New Point(138, 219)
        TextEdit6.Margin = New Padding(0)
        TextEdit6.Name = "TextEdit6"
        TextEdit6.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit6.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit6.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit6.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit6.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit6.Size = New Size(57, 20)
        TextEdit6.TabIndex = 348
        ' 
        ' Infusion_HydrationsStartDate11_dte
        ' 
        Infusion_HydrationsStartDate11_dte.EditValue = Nothing
        Infusion_HydrationsStartDate11_dte.Location = New Point(37, 219)
        Infusion_HydrationsStartDate11_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate11_dte.Name = "Infusion_HydrationsStartDate11_dte"
        Infusion_HydrationsStartDate11_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate11_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate11_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate11_dte.TabIndex = 347
        ' 
        ' DateEdit2
        ' 
        DateEdit2.EditValue = Nothing
        DateEdit2.Location = New Point(198, 239)
        DateEdit2.Margin = New Padding(0)
        DateEdit2.Name = "DateEdit2"
        DateEdit2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit2.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit2.Size = New Size(100, 20)
        DateEdit2.TabIndex = 356
        ' 
        ' TextEdit9
        ' 
        TextEdit9.Location = New Point(138, 239)
        TextEdit9.Margin = New Padding(0)
        TextEdit9.Name = "TextEdit9"
        TextEdit9.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit9.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit9.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit9.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit9.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit9.Size = New Size(57, 20)
        TextEdit9.TabIndex = 355
        ' 
        ' TextEdit13
        ' 
        TextEdit13.Location = New Point(299, 239)
        TextEdit13.Margin = New Padding(0)
        TextEdit13.Name = "TextEdit13"
        TextEdit13.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit13.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit13.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit13.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit13.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit13.Size = New Size(68, 20)
        TextEdit13.TabIndex = 357
        ' 
        ' LabelControl62
        ' 
        LabelControl62.Location = New Point(376, 241)
        LabelControl62.Margin = New Padding(0)
        LabelControl62.Name = "LabelControl62"
        LabelControl62.Size = New Size(28, 14)
        LabelControl62.TabIndex = 358
        LabelControl62.Text = "9999"
        ' 
        ' ComboBoxEdit1
        ' 
        ComboBoxEdit1.Location = New Point(415, 219)
        ComboBoxEdit1.Margin = New Padding(0)
        ComboBoxEdit1.Name = "ComboBoxEdit1"
        ComboBoxEdit1.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit1.Size = New Size(72, 20)
        ComboBoxEdit1.TabIndex = 352
        ' 
        ' LabelControl63
        ' 
        LabelControl63.Location = New Point(376, 220)
        LabelControl63.Margin = New Padding(0)
        LabelControl63.Name = "LabelControl63"
        LabelControl63.Size = New Size(28, 14)
        LabelControl63.TabIndex = 351
        LabelControl63.Text = "9999"
        ' 
        ' ComboBoxEdit2
        ' 
        ComboBoxEdit2.Location = New Point(415, 239)
        ComboBoxEdit2.Margin = New Padding(0)
        ComboBoxEdit2.Name = "ComboBoxEdit2"
        ComboBoxEdit2.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit2.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit2.Size = New Size(72, 20)
        ComboBoxEdit2.TabIndex = 359
        ' 
        ' Infusion_HydrationsStartDate12_dte
        ' 
        Infusion_HydrationsStartDate12_dte.EditValue = Nothing
        Infusion_HydrationsStartDate12_dte.Location = New Point(37, 239)
        Infusion_HydrationsStartDate12_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate12_dte.Name = "Infusion_HydrationsStartDate12_dte"
        Infusion_HydrationsStartDate12_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate12_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate12_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate12_dte.TabIndex = 354
        ' 
        ' DateEdit4
        ' 
        DateEdit4.EditValue = Nothing
        DateEdit4.Location = New Point(198, 219)
        DateEdit4.Margin = New Padding(0)
        DateEdit4.Name = "DateEdit4"
        DateEdit4.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit4.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit4.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit4.Size = New Size(100, 20)
        DateEdit4.TabIndex = 349
        ' 
        ' TextEdit14
        ' 
        TextEdit14.Location = New Point(299, 219)
        TextEdit14.Margin = New Padding(0)
        TextEdit14.Name = "TextEdit14"
        TextEdit14.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit14.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit14.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit14.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit14.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit14.Size = New Size(68, 20)
        TextEdit14.TabIndex = 350
        ' 
        ' ComboBoxEdit14
        ' 
        ComboBoxEdit14.Location = New Point(486, 239)
        ComboBoxEdit14.Margin = New Padding(0)
        ComboBoxEdit14.Name = "ComboBoxEdit14"
        ComboBoxEdit14.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit14.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit14.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit14.Size = New Size(72, 20)
        ComboBoxEdit14.TabIndex = 360
        ' 
        ' ComboBoxEdit15
        ' 
        ComboBoxEdit15.Location = New Point(486, 219)
        ComboBoxEdit15.Margin = New Padding(0)
        ComboBoxEdit15.Name = "ComboBoxEdit15"
        ComboBoxEdit15.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit15.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit15.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit15.Size = New Size(72, 20)
        ComboBoxEdit15.TabIndex = 353
        ' 
        ' LabelControl64
        ' 
        LabelControl64.Location = New Point(12, 199)
        LabelControl64.Margin = New Padding(0)
        LabelControl64.Name = "LabelControl64"
        LabelControl64.Size = New Size(19, 14)
        LabelControl64.TabIndex = 346
        LabelControl64.Text = "10)"
        ' 
        ' Infusion_Hydrations09_lbl
        ' 
        Infusion_Hydrations09_lbl.Location = New Point(12, 180)
        Infusion_Hydrations09_lbl.Margin = New Padding(0)
        Infusion_Hydrations09_lbl.Name = "Infusion_Hydrations09_lbl"
        Infusion_Hydrations09_lbl.Size = New Size(12, 14)
        Infusion_Hydrations09_lbl.TabIndex = 345
        Infusion_Hydrations09_lbl.Text = "9)"
        ' 
        ' DateEdit5
        ' 
        DateEdit5.EditValue = Nothing
        DateEdit5.Location = New Point(198, 118)
        DateEdit5.Margin = New Padding(0)
        DateEdit5.Name = "DateEdit5"
        DateEdit5.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit5.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit5.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit5.Properties.Mask.UseMaskAsDisplayFormat = True
        DateEdit5.Properties.MaskSettings.Set("showPlaceholders", False)
        DateEdit5.Size = New Size(100, 20)
        DateEdit5.TabIndex = 309
        DateEdit5.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' TextEdit15
        ' 
        TextEdit15.Location = New Point(138, 179)
        TextEdit15.Margin = New Padding(0)
        TextEdit15.Name = "TextEdit15"
        TextEdit15.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit15.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit15.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit15.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit15.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit15.Size = New Size(57, 20)
        TextEdit15.TabIndex = 329
        ' 
        ' Infusion_Hydrations08_lbl
        ' 
        Infusion_Hydrations08_lbl.Location = New Point(12, 160)
        Infusion_Hydrations08_lbl.Margin = New Padding(0)
        Infusion_Hydrations08_lbl.Name = "Infusion_Hydrations08_lbl"
        Infusion_Hydrations08_lbl.Size = New Size(12, 14)
        Infusion_Hydrations08_lbl.TabIndex = 344
        Infusion_Hydrations08_lbl.Text = "8)"
        ' 
        ' DateEdit6
        ' 
        DateEdit6.EditValue = Nothing
        DateEdit6.Location = New Point(198, 139)
        DateEdit6.Margin = New Padding(0)
        DateEdit6.Name = "DateEdit6"
        DateEdit6.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit6.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit6.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit6.Size = New Size(100, 20)
        DateEdit6.TabIndex = 316
        ' 
        ' Infusion_HydrationsStartDate09_dte
        ' 
        Infusion_HydrationsStartDate09_dte.EditValue = Nothing
        Infusion_HydrationsStartDate09_dte.Location = New Point(37, 179)
        Infusion_HydrationsStartDate09_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate09_dte.Name = "Infusion_HydrationsStartDate09_dte"
        Infusion_HydrationsStartDate09_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate09_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate09_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate09_dte.TabIndex = 328
        ' 
        ' Infusion_Hydrations07_lbl
        ' 
        Infusion_Hydrations07_lbl.Location = New Point(12, 140)
        Infusion_Hydrations07_lbl.Margin = New Padding(0)
        Infusion_Hydrations07_lbl.Name = "Infusion_Hydrations07_lbl"
        Infusion_Hydrations07_lbl.Size = New Size(12, 14)
        Infusion_Hydrations07_lbl.TabIndex = 343
        Infusion_Hydrations07_lbl.Text = "7)"
        ' 
        ' DateEdit8
        ' 
        DateEdit8.EditValue = Nothing
        DateEdit8.Location = New Point(198, 199)
        DateEdit8.Margin = New Padding(0)
        DateEdit8.Name = "DateEdit8"
        DateEdit8.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit8.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit8.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit8.Size = New Size(100, 20)
        DateEdit8.TabIndex = 337
        ' 
        ' TextEdit16
        ' 
        TextEdit16.Location = New Point(138, 159)
        TextEdit16.Margin = New Padding(0)
        TextEdit16.Name = "TextEdit16"
        TextEdit16.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit16.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit16.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit16.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit16.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit16.Size = New Size(57, 20)
        TextEdit16.TabIndex = 322
        ' 
        ' Infusion_Hydrations06_lbl
        ' 
        Infusion_Hydrations06_lbl.Location = New Point(12, 120)
        Infusion_Hydrations06_lbl.Margin = New Padding(0)
        Infusion_Hydrations06_lbl.Name = "Infusion_Hydrations06_lbl"
        Infusion_Hydrations06_lbl.Size = New Size(12, 14)
        Infusion_Hydrations06_lbl.TabIndex = 342
        Infusion_Hydrations06_lbl.Text = "6)"
        ' 
        ' TextEdit17
        ' 
        TextEdit17.Location = New Point(299, 118)
        TextEdit17.Margin = New Padding(0)
        TextEdit17.Name = "TextEdit17"
        TextEdit17.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit17.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit17.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit17.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit17.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit17.Size = New Size(68, 20)
        TextEdit17.TabIndex = 310
        ' 
        ' ComboBoxEdit16
        ' 
        ComboBoxEdit16.Location = New Point(415, 118)
        ComboBoxEdit16.Margin = New Padding(0)
        ComboBoxEdit16.Name = "ComboBoxEdit16"
        ComboBoxEdit16.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit16.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit16.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit16.Size = New Size(72, 20)
        ComboBoxEdit16.TabIndex = 312
        ' 
        ' Infusion_HydrationsStartDate08_dte
        ' 
        Infusion_HydrationsStartDate08_dte.EditValue = Nothing
        Infusion_HydrationsStartDate08_dte.Location = New Point(37, 159)
        Infusion_HydrationsStartDate08_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate08_dte.Name = "Infusion_HydrationsStartDate08_dte"
        Infusion_HydrationsStartDate08_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate08_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate08_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate08_dte.TabIndex = 321
        ' 
        ' TextEdit18
        ' 
        TextEdit18.Location = New Point(299, 139)
        TextEdit18.Margin = New Padding(0)
        TextEdit18.Name = "TextEdit18"
        TextEdit18.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit18.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit18.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit18.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit18.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit18.Size = New Size(68, 20)
        TextEdit18.TabIndex = 317
        ' 
        ' ComboBoxEdit17
        ' 
        ComboBoxEdit17.Location = New Point(415, 139)
        ComboBoxEdit17.Margin = New Padding(0)
        ComboBoxEdit17.Name = "ComboBoxEdit17"
        ComboBoxEdit17.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit17.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit17.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit17.Size = New Size(72, 20)
        ComboBoxEdit17.TabIndex = 319
        ' 
        ' TextEdit19
        ' 
        TextEdit19.Location = New Point(138, 199)
        TextEdit19.Margin = New Padding(0)
        TextEdit19.Name = "TextEdit19"
        TextEdit19.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit19.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit19.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit19.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit19.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit19.Size = New Size(57, 20)
        TextEdit19.TabIndex = 336
        ' 
        ' TextEdit20
        ' 
        TextEdit20.Location = New Point(299, 199)
        TextEdit20.Margin = New Padding(0)
        TextEdit20.Name = "TextEdit20"
        TextEdit20.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit20.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit20.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit20.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit20.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit20.Size = New Size(68, 20)
        TextEdit20.TabIndex = 338
        ' 
        ' ComboBoxEdit18
        ' 
        ComboBoxEdit18.Location = New Point(415, 159)
        ComboBoxEdit18.Margin = New Padding(0)
        ComboBoxEdit18.Name = "ComboBoxEdit18"
        ComboBoxEdit18.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit18.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit18.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit18.Size = New Size(72, 20)
        ComboBoxEdit18.TabIndex = 326
        ' 
        ' Infusion_HydrationsStartTime07_txt
        ' 
        Infusion_HydrationsStartTime07_txt.Location = New Point(138, 139)
        Infusion_HydrationsStartTime07_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime07_txt.Name = "Infusion_HydrationsStartTime07_txt"
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime07_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime07_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime07_txt.TabIndex = 315
        ' 
        ' LabelControl69
        ' 
        LabelControl69.Location = New Point(376, 201)
        LabelControl69.Margin = New Padding(0)
        LabelControl69.Name = "LabelControl69"
        LabelControl69.Size = New Size(28, 14)
        LabelControl69.TabIndex = 339
        LabelControl69.Text = "9999"
        ' 
        ' DateEdit10
        ' 
        DateEdit10.EditValue = Nothing
        DateEdit10.Location = New Point(198, 159)
        DateEdit10.Margin = New Padding(0)
        DateEdit10.Name = "DateEdit10"
        DateEdit10.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit10.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit10.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit10.Size = New Size(100, 20)
        DateEdit10.TabIndex = 323
        ' 
        ' ComboBoxEdit19
        ' 
        ComboBoxEdit19.Location = New Point(415, 179)
        ComboBoxEdit19.Margin = New Padding(0)
        ComboBoxEdit19.Name = "ComboBoxEdit19"
        ComboBoxEdit19.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit19.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit19.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit19.Size = New Size(72, 20)
        ComboBoxEdit19.TabIndex = 333
        ' 
        ' Infusion_HydrationsStartTime06_txt
        ' 
        Infusion_HydrationsStartTime06_txt.Location = New Point(138, 118)
        Infusion_HydrationsStartTime06_txt.Margin = New Padding(0)
        Infusion_HydrationsStartTime06_txt.Name = "Infusion_HydrationsStartTime06_txt"
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("allowBlankInput", True)
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("isAutoComplete", True)
        Infusion_HydrationsStartTime06_txt.Properties.MaskSettings.Set("isOptimistic", True)
        Infusion_HydrationsStartTime06_txt.Size = New Size(57, 20)
        Infusion_HydrationsStartTime06_txt.TabIndex = 308
        ' 
        ' LabelControl70
        ' 
        LabelControl70.Location = New Point(376, 180)
        LabelControl70.Margin = New Padding(0)
        LabelControl70.Name = "LabelControl70"
        LabelControl70.Size = New Size(28, 14)
        LabelControl70.TabIndex = 332
        LabelControl70.Text = "9999"
        ' 
        ' TextEdit23
        ' 
        TextEdit23.Location = New Point(299, 159)
        TextEdit23.Margin = New Padding(0)
        TextEdit23.Name = "TextEdit23"
        TextEdit23.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit23.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit23.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit23.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit23.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit23.Size = New Size(68, 20)
        TextEdit23.TabIndex = 324
        ' 
        ' ComboBoxEdit20
        ' 
        ComboBoxEdit20.Location = New Point(415, 199)
        ComboBoxEdit20.Margin = New Padding(0)
        ComboBoxEdit20.Name = "ComboBoxEdit20"
        ComboBoxEdit20.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit20.Properties.Items.AddRange(New Object() {"Site 1", "Site 2", "Site 3"})
        ComboBoxEdit20.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit20.Size = New Size(72, 20)
        ComboBoxEdit20.TabIndex = 340
        ' 
        ' Infusion_HydrationsStartDate10_dte
        ' 
        Infusion_HydrationsStartDate10_dte.EditValue = Nothing
        Infusion_HydrationsStartDate10_dte.Location = New Point(37, 199)
        Infusion_HydrationsStartDate10_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate10_dte.Name = "Infusion_HydrationsStartDate10_dte"
        Infusion_HydrationsStartDate10_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate10_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate10_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate10_dte.TabIndex = 335
        ' 
        ' LabelControl71
        ' 
        LabelControl71.Location = New Point(376, 162)
        LabelControl71.Margin = New Padding(0)
        LabelControl71.Name = "LabelControl71"
        LabelControl71.Size = New Size(28, 14)
        LabelControl71.TabIndex = 325
        LabelControl71.Text = "9999"
        ' 
        ' DateEdit12
        ' 
        DateEdit12.EditValue = Nothing
        DateEdit12.Location = New Point(198, 179)
        DateEdit12.Margin = New Padding(0)
        DateEdit12.Name = "DateEdit12"
        DateEdit12.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit12.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit12.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit12.Size = New Size(100, 20)
        DateEdit12.TabIndex = 330
        ' 
        ' ComboBoxEdit21
        ' 
        ComboBoxEdit21.Location = New Point(486, 118)
        ComboBoxEdit21.Margin = New Padding(0)
        ComboBoxEdit21.Name = "ComboBoxEdit21"
        ComboBoxEdit21.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit21.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit21.Size = New Size(72, 20)
        ComboBoxEdit21.TabIndex = 313
        ' 
        ' Infusion_HydrationsStartDate07_dte
        ' 
        Infusion_HydrationsStartDate07_dte.EditValue = Nothing
        Infusion_HydrationsStartDate07_dte.Location = New Point(37, 139)
        Infusion_HydrationsStartDate07_dte.Margin = New Padding(0)
        Infusion_HydrationsStartDate07_dte.Name = "Infusion_HydrationsStartDate07_dte"
        Infusion_HydrationsStartDate07_dte.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        Infusion_HydrationsStartDate07_dte.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        Infusion_HydrationsStartDate07_dte.Size = New Size(100, 20)
        Infusion_HydrationsStartDate07_dte.TabIndex = 314
        ' 
        ' LabelControl72
        ' 
        LabelControl72.Location = New Point(376, 140)
        LabelControl72.Margin = New Padding(0)
        LabelControl72.Name = "LabelControl72"
        LabelControl72.Size = New Size(28, 14)
        LabelControl72.TabIndex = 318
        LabelControl72.Text = "9999"
        ' 
        ' TextEdit24
        ' 
        TextEdit24.Location = New Point(299, 179)
        TextEdit24.Margin = New Padding(0)
        TextEdit24.Name = "TextEdit24"
        TextEdit24.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.RegExpMaskManager))
        TextEdit24.Properties.MaskSettings.Set("allowBlankInput", True)
        TextEdit24.Properties.MaskSettings.Set("mask", "([01]?[0-9]|2[0-3]):[0-5]\d")
        TextEdit24.Properties.MaskSettings.Set("isAutoComplete", True)
        TextEdit24.Properties.MaskSettings.Set("isOptimistic", True)
        TextEdit24.Size = New Size(68, 20)
        TextEdit24.TabIndex = 331
        ' 
        ' ComboBoxEdit22
        ' 
        ComboBoxEdit22.Location = New Point(486, 139)
        ComboBoxEdit22.Margin = New Padding(0)
        ComboBoxEdit22.Name = "ComboBoxEdit22"
        ComboBoxEdit22.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit22.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit22.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit22.Size = New Size(72, 20)
        ComboBoxEdit22.TabIndex = 320
        ' 
        ' DateEdit14
        ' 
        DateEdit14.EditValue = Nothing
        DateEdit14.Location = New Point(37, 118)
        DateEdit14.Margin = New Padding(0)
        DateEdit14.Name = "DateEdit14"
        DateEdit14.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DateEdit14.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        DateEdit14.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DateEdit14.Properties.Mask.UseMaskAsDisplayFormat = True
        DateEdit14.Properties.MaskSettings.Set("showPlaceholders", False)
        DateEdit14.Size = New Size(100, 20)
        DateEdit14.TabIndex = 307
        DateEdit14.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' LabelControl73
        ' 
        LabelControl73.Location = New Point(376, 121)
        LabelControl73.Margin = New Padding(0)
        LabelControl73.Name = "LabelControl73"
        LabelControl73.Size = New Size(28, 14)
        LabelControl73.TabIndex = 311
        LabelControl73.Text = "9999"
        ' 
        ' ComboBoxEdit23
        ' 
        ComboBoxEdit23.Location = New Point(486, 159)
        ComboBoxEdit23.Margin = New Padding(0)
        ComboBoxEdit23.Name = "ComboBoxEdit23"
        ComboBoxEdit23.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit23.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit23.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit23.Size = New Size(72, 20)
        ComboBoxEdit23.TabIndex = 327
        ' 
        ' ComboBoxEdit24
        ' 
        ComboBoxEdit24.Location = New Point(486, 199)
        ComboBoxEdit24.Margin = New Padding(0)
        ComboBoxEdit24.Name = "ComboBoxEdit24"
        ComboBoxEdit24.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit24.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit24.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit24.Size = New Size(72, 20)
        ComboBoxEdit24.TabIndex = 341
        ' 
        ' ComboBoxEdit25
        ' 
        ComboBoxEdit25.Location = New Point(486, 179)
        ComboBoxEdit25.Margin = New Padding(0)
        ComboBoxEdit25.Name = "ComboBoxEdit25"
        ComboBoxEdit25.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ComboBoxEdit25.Properties.Items.AddRange(New Object() {"KVO", "Bolus", "Hourly"})
        ComboBoxEdit25.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ComboBoxEdit25.Size = New Size(72, 20)
        ComboBoxEdit25.TabIndex = 334
        ' 
        ' LabelControl75
        ' 
        LabelControl75.Location = New Point(37, 3)
        LabelControl75.Margin = New Padding(2, 3, 2, 3)
        LabelControl75.Name = "LabelControl75"
        LabelControl75.Size = New Size(57, 14)
        LabelControl75.TabIndex = 279
        LabelControl75.Text = "Start Date"
        ' 
        ' LabelControl76
        ' 
        LabelControl76.Location = New Point(136, 3)
        LabelControl76.Margin = New Padding(2, 3, 2, 3)
        LabelControl76.Name = "LabelControl76"
        LabelControl76.Size = New Size(58, 14)
        LabelControl76.TabIndex = 280
        LabelControl76.Text = "Start Time"
        ' 
        ' LabelControl81
        ' 
        LabelControl81.Location = New Point(518, 2)
        LabelControl81.Margin = New Padding(2, 3, 2, 3)
        LabelControl81.Name = "LabelControl81"
        LabelControl81.Size = New Size(25, 14)
        LabelControl81.TabIndex = 301
        LabelControl81.Text = "Rate"
        ' 
        ' LabelControl82
        ' 
        LabelControl82.Location = New Point(438, 3)
        LabelControl82.Margin = New Padding(2, 3, 2, 3)
        LabelControl82.Name = "LabelControl82"
        LabelControl82.Size = New Size(21, 14)
        LabelControl82.TabIndex = 299
        LabelControl82.Text = "Site"
        ' 
        ' LabelControl92
        ' 
        LabelControl92.Location = New Point(218, 3)
        LabelControl92.Margin = New Padding(2, 3, 2, 3)
        LabelControl92.Name = "LabelControl92"
        LabelControl92.Size = New Size(51, 14)
        LabelControl92.TabIndex = 291
        LabelControl92.Text = "End Date"
        ' 
        ' LabelControl93
        ' 
        LabelControl93.Location = New Point(380, 3)
        LabelControl93.Margin = New Padding(2, 3, 2, 3)
        LabelControl93.Name = "LabelControl93"
        LabelControl93.Size = New Size(23, 14)
        LabelControl93.TabIndex = 293
        LabelControl93.Text = "Mins"
        ' 
        ' LabelControl94
        ' 
        LabelControl94.Location = New Point(299, 3)
        LabelControl94.Margin = New Padding(2, 3, 2, 3)
        LabelControl94.Name = "LabelControl94"
        LabelControl94.Size = New Size(52, 14)
        LabelControl94.TabIndex = 292
        LabelControl94.Text = "End Time"
        ' 
        ' GridControl1
        ' 
        GridControl1.EmbeddedNavigator.Margin = New Padding(4, 3, 4, 3)
        GridControl1.Location = New Point(516, 549)
        GridControl1.MainView = GridView1
        GridControl1.Margin = New Padding(4, 3, 4, 3)
        GridControl1.Name = "GridControl1"
        GridControl1.RepositoryItems.AddRange(New Repository.RepositoryItem() {RepositoryItemDateEdit1, RepositoryItemPopupContainerEdit1, RepositoryItemComboBox1, RepositoryItemComboBox2})
        GridControl1.Size = New Size(716, 313)
        GridControl1.TabIndex = 13
        GridControl1.UseEmbeddedNavigator = True
        GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {GridView1})
        ' 
        ' GridView1
        ' 
        GridView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colStartDate, colEndDate, GridColumn1, GridColumn2, GridColumn3})
        GridView1.GridControl = GridControl1
        GridView1.Name = "GridView1"
        GridView1.NewItemRowText = "Add New Hydrations Here"
        GridView1.OptionsMenu.DialogFormBorderEffect = FormBorderEffect.Glow
        GridView1.OptionsView.EnableAppearanceEvenRow = True
        GridView1.OptionsView.EnableAppearanceOddRow = True
        GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Top
        GridView1.OptionsView.ShowErrorPanel = DevExpress.Utils.DefaultBoolean.True
        ' 
        ' colStartDate
        ' 
        colStartDate.ColumnEdit = RepositoryItemDateEdit1
        colStartDate.FieldName = "StartDate"
        colStartDate.Name = "colStartDate"
        colStartDate.Visible = True
        colStartDate.VisibleIndex = 0
        colStartDate.Width = 261
        ' 
        ' RepositoryItemDateEdit1
        ' 
        RepositoryItemDateEdit1.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        RepositoryItemDateEdit1.AutoHeight = False
        RepositoryItemDateEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemDateEdit1.CalendarTimeEditing = DevExpress.Utils.DefaultBoolean.True
        RepositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemDateEdit1.EditFormat.FormatString = "g"
        RepositoryItemDateEdit1.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        RepositoryItemDateEdit1.EditValueChangedDelay = 1
        RepositoryItemDateEdit1.MaskSettings.Set("mask", "g")
        RepositoryItemDateEdit1.MaskSettings.Set("useAdvancingCaret", True)
        RepositoryItemDateEdit1.MaskSettings.Set("spinWithCarry", True)
        RepositoryItemDateEdit1.Name = "RepositoryItemDateEdit1"
        RepositoryItemDateEdit1.NullValuePrompt = "Please Enter a Date"
        RepositoryItemDateEdit1.UseMaskAsDisplayFormat = True
        ' 
        ' colEndDate
        ' 
        colEndDate.ColumnEdit = RepositoryItemDateEdit1
        colEndDate.FieldName = "EndDate"
        colEndDate.Name = "colEndDate"
        colEndDate.Visible = True
        colEndDate.VisibleIndex = 1
        colEndDate.Width = 261
        ' 
        ' GridColumn1
        ' 
        GridColumn1.Caption = "Site"
        GridColumn1.ColumnEdit = RepositoryItemComboBox2
        GridColumn1.FieldName = "Site"
        GridColumn1.Name = "GridColumn1"
        GridColumn1.Visible = True
        GridColumn1.VisibleIndex = 3
        GridColumn1.Width = 337
        ' 
        ' RepositoryItemComboBox2
        ' 
        RepositoryItemComboBox2.AutoHeight = False
        RepositoryItemComboBox2.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemComboBox2.Items.AddRange(New Object() {"", "Site 1", "Site 2", "Site 3"})
        RepositoryItemComboBox2.Name = "RepositoryItemComboBox2"
        ' 
        ' GridColumn2
        ' 
        GridColumn2.Caption = "Rate"
        GridColumn2.ColumnEdit = RepositoryItemComboBox1
        GridColumn2.FieldName = "Rate"
        GridColumn2.Name = "GridColumn2"
        GridColumn2.Visible = True
        GridColumn2.VisibleIndex = 4
        GridColumn2.Width = 339
        ' 
        ' RepositoryItemComboBox1
        ' 
        RepositoryItemComboBox1.AutoHeight = False
        RepositoryItemComboBox1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemComboBox1.Items.AddRange(New Object() {"", "KVO", "Bolus", "Hourly"})
        RepositoryItemComboBox1.Name = "RepositoryItemComboBox1"
        ' 
        ' GridColumn3
        ' 
        GridColumn3.Caption = "Duration"
        GridColumn3.CustomizationCaption = "Duration"
        GridColumn3.FieldName = "Duration"
        GridColumn3.Name = "GridColumn3"
        GridColumn3.OptionsColumn.AllowEdit = False
        GridColumn3.OptionsColumn.AllowFocus = False
        GridColumn3.OptionsColumn.ReadOnly = True
        GridColumn3.Visible = True
        GridColumn3.VisibleIndex = 2
        GridColumn3.Width = 108
        ' 
        ' RepositoryItemPopupContainerEdit1
        ' 
        RepositoryItemPopupContainerEdit1.AutoHeight = False
        RepositoryItemPopupContainerEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemPopupContainerEdit1.Name = "RepositoryItemPopupContainerEdit1"
        ' 
        ' ScratchPadForm
        ' 
        Appearance.BackColor = SystemColors.Control
        Appearance.Options.UseBackColor = True
        Appearance.Options.UseFont = True
        AutoScaleDimensions = New SizeF(7F, 14F)
        AutoScaleMode = AutoScaleMode.Font
        ClientSize = New Size(1247, 874)
        Controls.Add(GridControl1)
        Controls.Add(GroupControl2)
        Font = New Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point)
        Margin = New Padding(4, 3, 4, 3)
        Name = "ScratchPadForm"
        Text = "ScratchPadForm"
        CType(GroupControl2, ComponentModel.ISupportInitialize).EndInit()
        GroupControl2.ResumeLayout(False)
        XtraScrollableControl9.ResumeLayout(False)
        XtraScrollableControl9.PerformLayout()
        CType(Infusion_HydrationsEndTime04_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate04_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime03_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate03_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime05_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime02_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndTime01_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate05_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate02_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsEndDate01_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime04_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate04_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime03_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate03_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime05_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime02_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime01_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate05_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate02_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate01_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate05_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate04_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate03_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate02_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsRate01_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite05_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite04_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite03_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite02_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsSite01_cbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit6.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate11_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit2.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit2.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit9.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit13.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit1.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit2.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate12_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit4.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit4.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit14.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit14.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit15.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit5.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit5.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit15.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit6.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit6.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate09_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit8.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit8.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit16.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit17.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit16.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate08_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit18.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit17.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit19.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit20.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit18.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime07_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit10.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit10.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit19.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartTime06_txt.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit23.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit20.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate10_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit12.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit12.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit21.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(Infusion_HydrationsStartDate07_dte.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit24.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit22.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit14.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DateEdit14.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit23.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit24.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ComboBoxEdit25.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).EndInit()
        CType(GridView1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemDateEdit1.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemDateEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemComboBox2, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemComboBox1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemPopupContainerEdit1, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
    End Sub
    Friend WithEvents LabelControl46 As LabelControl
    Friend WithEvents ComboBoxEdit13 As ComboBoxEdit
    Friend WithEvents CheckEdit2 As CheckEdit
    Friend WithEvents CheckEdit3 As CheckEdit
    Friend WithEvents CheckEdit4 As CheckEdit
    Friend WithEvents CheckEdit5 As CheckEdit
    Friend WithEvents CheckEdit6 As CheckEdit
    Friend WithEvents CheckEdit7 As CheckEdit
    Friend WithEvents CheckEdit8 As CheckEdit
    Friend WithEvents CheckEdit9 As CheckEdit
    Friend WithEvents CheckEdit10 As CheckEdit
    Friend WithEvents CheckEdit11 As CheckEdit
    Friend WithEvents LabelControl29 As LabelControl
    Friend WithEvents LabelControl35 As LabelControl
    Friend WithEvents LabelControl36 As LabelControl
    Friend WithEvents LabelControl37 As LabelControl
    Friend WithEvents ComboBoxEdit3 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit4 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit5 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit6 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit7 As ComboBoxEdit
    Friend WithEvents LabelControl38 As LabelControl
    Friend WithEvents ComboBoxEdit8 As ComboBoxEdit
    Friend WithEvents LabelControl40 As LabelControl
    Friend WithEvents ComboBoxEdit9 As ComboBoxEdit
    Friend WithEvents LabelControl41 As LabelControl
    Friend WithEvents ComboBoxEdit10 As ComboBoxEdit
    Friend WithEvents LabelControl42 As LabelControl
    Friend WithEvents LabelControl43 As LabelControl
    Friend WithEvents ComboBoxEdit11 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit12 As ComboBoxEdit
    Friend WithEvents CheckEdit1 As CheckEdit
    Friend WithEvents LabelControl70 As LabelControl
    Friend WithEvents LabelControl71 As LabelControl
    Friend WithEvents Infusion_HydrationsStartTime07_txt As TextEdit
    Friend WithEvents DateEdit12 As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime06_txt As TextEdit
    Friend WithEvents TextEdit23 As TextEdit
    Friend WithEvents LabelControl72 As LabelControl
    Friend WithEvents ComboBoxEdit1 As ComboBoxEdit
    Friend WithEvents LabelControl73 As LabelControl
    Friend WithEvents ComboBoxEdit14 As ComboBoxEdit
    Friend WithEvents DateEdit14 As DateEdit
    Friend WithEvents TextEdit24 As TextEdit
    Friend WithEvents ComboBoxEdit15 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit16 As ComboBoxEdit
    Friend WithEvents LabelControl60 As LabelControl
    Friend WithEvents LabelControl61 As LabelControl
    Friend WithEvents Infusion_HydrationsStartDate11_dte As DateEdit
    Friend WithEvents TextEdit6 As TextEdit
    Friend WithEvents LabelControl62 As LabelControl
    Friend WithEvents DateEdit2 As DateEdit
    Friend WithEvents LabelControl63 As LabelControl
    Friend WithEvents DateEdit4 As DateEdit
    Friend WithEvents TextEdit9 As TextEdit
    Friend WithEvents LabelControl64 As LabelControl
    Friend WithEvents TextEdit13 As TextEdit
    Friend WithEvents ComboBoxEdit2 As ComboBoxEdit
    Friend WithEvents TextEdit14 As TextEdit
    Friend WithEvents ComboBoxEdit17 As ComboBoxEdit
    Friend WithEvents TextEdit15 As TextEdit
    Friend WithEvents TextEdit16 As TextEdit
    Friend WithEvents ComboBoxEdit18 As ComboBoxEdit
    Friend WithEvents TextEdit17 As TextEdit
    Friend WithEvents Infusion_Hydrations09_lbl As LabelControl
    Friend WithEvents DateEdit6 As DateEdit
    Friend WithEvents ComboBoxEdit19 As ComboBoxEdit
    Friend WithEvents TextEdit18 As TextEdit
    Friend WithEvents Infusion_Hydrations08_lbl As LabelControl
    Friend WithEvents TextEdit19 As TextEdit
    Friend WithEvents ComboBoxEdit20 As ComboBoxEdit
    Friend WithEvents Infusion_Hydrations07_lbl As LabelControl
    Friend WithEvents DateEdit8 As DateEdit
    Friend WithEvents ComboBoxEdit21 As ComboBoxEdit
    Friend WithEvents Infusion_Hydrations06_lbl As LabelControl
    Friend WithEvents TextEdit20 As TextEdit
    Friend WithEvents ComboBoxEdit22 As ComboBoxEdit
    Friend WithEvents LabelControl69 As LabelControl
    Friend WithEvents ComboBoxEdit23 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit24 As ComboBoxEdit
    Friend WithEvents ComboBoxEdit25 As ComboBoxEdit
    Friend WithEvents GroupControl2 As GroupControl
    Friend WithEvents XtraScrollableControl9 As XtraScrollableControl
    Friend WithEvents Infusion_HydrationsStartDate12_dte As DateEdit
    Friend WithEvents DateEdit5 As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate09_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate08_dte As DateEdit
    Friend WithEvents DateEdit10 As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate10_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate07_dte As DateEdit
    Friend WithEvents LabelControl75 As LabelControl
    Friend WithEvents LabelControl76 As LabelControl
    Friend WithEvents LabelControl81 As LabelControl
    Friend WithEvents LabelControl82 As LabelControl
    Friend WithEvents LabelControl92 As LabelControl
    Friend WithEvents LabelControl93 As LabelControl
    Friend WithEvents LabelControl94 As LabelControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents DateTimePicker1 As DateTimePicker
    Friend WithEvents colStartDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEndDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemDateEdit1 As Repository.RepositoryItemDateEdit
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemPopupContainerEdit1 As Repository.RepositoryItemPopupContainerEdit
    Friend WithEvents RepositoryItemComboBox2 As Repository.RepositoryItemComboBox
    Friend WithEvents RepositoryItemComboBox1 As Repository.RepositoryItemComboBox
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Infusion_Hydrations05_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations04_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations03_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations02_lbl As LabelControl
    Friend WithEvents Infusion_Hydrations01_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins05_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins04_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins03_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins02_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsMins01_lbl As LabelControl
    Friend WithEvents Infusion_HydrationsEndTime04_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate04_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime03_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate03_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndTime05_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime02_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndTime01_txt As TextEdit
    Friend WithEvents Infusion_HydrationsEndDate05_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate02_dte As DateEdit
    Friend WithEvents Infusion_HydrationsEndDate01_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime04_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate04_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime03_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate03_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartTime05_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartTime02_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartTime01_txt As TextEdit
    Friend WithEvents Infusion_HydrationsStartDate05_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate02_dte As DateEdit
    Friend WithEvents Infusion_HydrationsStartDate01_dte As DateEdit
    Friend WithEvents Infusion_HydrationsRate05_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate04_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate03_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate02_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsRate01_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite05_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite04_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite03_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite02_cbo As ComboBoxEdit
    Friend WithEvents Infusion_HydrationsSite01_cbo As ComboBoxEdit
End Class

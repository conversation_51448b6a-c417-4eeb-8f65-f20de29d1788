﻿Imports System.IO
Imports System.Xml

Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Public Class FacilitySettingsHelper
    Public Shared Function LoadExistingSettings() As XPCollection(Of DOFacilitySettings)
        Dim loadedSettings As XPCollection(Of DOFacilitySettings)

        loadedSettings = New XPCollection(Of DOFacilitySettings) With {
            .DeleteObjectOnRemove = True
        }


        Return loadedSettings
    End Function

    Public Shared Function LoadBaselineSettings() As DTOFacilitySettingsList
        Return LoadSettingsFromResource(My.Resources.BaselineFacilitySettings)
    End Function

    Public Shared Function LoadNewSettings() As DTOFacilitySettingsList
        Return LoadSettingsFromResource(My.Resources.NewFacilitySettings)
    End Function

    Private Shared Function LoadSettingsFromResource(ByVal resource As String) As DTOFacilitySettingsList
        Dim loadedSettings As DTOFacilitySettingsList

        Using sr As New StringReader(resource)
            Dim xmlSettings As New XmlReaderSettings
            xmlSettings.DtdProcessing = DtdProcessing.Prohibit

            Using reader = XmlReader.Create(sr, xmlSettings)
                Dim x As New Xml.Serialization.XmlSerializer(GetType(DTOFacilitySettingsList))
                loadedSettings = x.Deserialize(reader)
            End Using
        End Using

        Return loadedSettings
    End Function

    Public Shared Function DetermineMissingBaselineSettings(ByVal facilityDict As Dictionary(Of Integer, DOFacility)) As Dictionary(Of Integer, List(Of DOFacilitySettings))

        Dim bfs As DTOFacilitySettingsList = LoadBaselineSettings()
        Dim returnDict As New Dictionary(Of Integer, List(Of DOFacilitySettings))

        For Each facilityOID As Integer In facilityDict.Keys

            For Each defset As DTOFacilitySetting In bfs.Settings

                If defset.IsTASpecific Then

                    For Each ta As String In GetTreatmentAreasAsListByFacility(facilityDict(facilityOID))

                        ' Check to see if there's an existing setting in the current facility/treatment area that has the same setting name and description:
                        ' If so, ignore it and continue
                        ' If Not, create a new DOFacilitySettings object to be returned
                        Dim existingSettingsByFacility As New XPCollection(Of DOFacilitySettings) _
                        (CriteriaOperator.Parse(String.Format("Facility = {0} and TreatmentArea = '{1}' and RuleSetting = '{2}' and RuleSettingCategory = '{3}'", facilityOID, ta, defset.Name, defset.Category)))

                        If existingSettingsByFacility.Count = 0 Then
                            Dim newSetting As DOFacilitySettings = BuildNewSetting(facilityOID, facilityDict(facilityOID).FacilityID, defset.Name, defset.Category, defset.Description, defset.DefaultValue, ta)

                            If Not returnDict.ContainsKey(facilityOID) Then
                                returnDict.Add(facilityOID, New List(Of DOFacilitySettings))
                            End If

                            returnDict(facilityOID).Add(newSetting)
                        End If

                    Next

                Else

                    ' Check to see if there's an existing setting in the current facility that has the same setting name and description:
                    ' If so, ignore it and continue
                    ' If Not, create a new DOFacilitySettings object to be returned
                    Dim existingSettingsByFacility As New XPCollection(Of DOFacilitySettings) _
                    (CriteriaOperator.Parse(String.Format("Facility = {0} and RuleSetting = '{1}' and RuleSettingCategory = '{2}'", facilityOID, defset.Name, defset.Category)))

                    If existingSettingsByFacility.Count = 0 Then
                        Dim newSetting As DOFacilitySettings = BuildNewSetting(facilityOID, facilityDict(facilityOID).FacilityID, defset.Name, defset.Category, defset.Description, defset.DefaultValue, "")

                        If Not returnDict.ContainsKey(facilityOID) Then
                            returnDict.Add(facilityOID, New List(Of DOFacilitySettings))
                        End If

                        returnDict(facilityOID).Add(newSetting)
                    End If
                End If

            Next

        Next

        If returnDict.Count > 0 Then
            Return returnDict
        Else
            Return Nothing
        End If
    End Function

    Public Shared Function DetermineMissingTreatmentAreaBaselineSettings(ByVal facility As DOFacility, treatmentArea As String) As List(Of DOFacilitySettings)
        Dim treatmentAreaSettings As New List(Of DOFacilitySettings)
        Dim bfs As DTOFacilitySettingsList = LoadBaselineSettings()

        For Each defset As DTOFacilitySetting In bfs.Settings
            If defset.IsTASpecific Then

                ' Check to see if there's an existing setting in the current facility/treatment area that has the same setting name and description:
                ' If so, ignore it and continue
                ' If Not, create a new DOFacilitySettings object to be returned
                Dim existingSettingsByFacility As New XPCollection(Of DOFacilitySettings) _
                    (CriteriaOperator.Parse(String.Format("Facility = {0} and TreatmentArea = '{1}' and RuleSetting = '{2}' and RuleSettingCategory = '{3}'", facility.Oid, treatmentArea, defset.Name, defset.Category)))

                If existingSettingsByFacility.Count = 0 Then
                    Dim newSetting As DOFacilitySettings = BuildNewSetting(facility.Oid, facility.FacilityID, defset.Name, defset.Category, defset.Description, defset.DefaultValue, treatmentArea)
                    treatmentAreaSettings.Add(newSetting)
                End If
            End If
        Next
        Return treatmentAreaSettings
    End Function

    Public Shared Function BuildNewSetting(ByVal facilityOID As Integer, ByVal facilityName As String, ByVal settingName As String, ByVal settingCategory As String,
                                     ByVal settingDescription As String, ByVal settingValue As String, ByVal ta As String) As DOFacilitySettings
        Dim newSetting As New DOFacilitySettings

        With newSetting
            .Facility = facilityOID
            .FacilityID = facilityName
            .RuleSetting = settingName
            .RuleSettingCategory = settingCategory
            .RuleSettingDescription = settingDescription
            .RuleSettingValue = settingValue
            .TreatmentArea = ta
        End With

        Return newSetting
    End Function

    Public Shared Sub InsertNewSettings()

        Dim nfs As DTOFacilitySettingsList = LoadNewSettings()

        For Each facility As DOFacility In New XPCollection(Of DOFacility)

            For Each defset As DTOFacilitySetting In nfs.Settings

                If defset.IsTASpecific Then

                    For Each ta As String In GetTreatmentAreasAsListByFacility(facility)

                        ' Check to see if there's an existing setting in the current facility/treatment area that has the same setting name and category:
                        ' If so, ignore it and continue
                        ' If Not, create a new DOFacilitySettings object to be returned
                        Dim existingSettingsByFacility As New XPCollection(Of DOFacilitySettings) _
                        (CriteriaOperator.Parse(String.Format("Facility = {0} and TreatmentArea = '{1}' and RuleSetting = '{2}' and RuleSettingCategory = '{3}'", facility.Oid, ta, defset.Name, defset.Category)))

                        If existingSettingsByFacility.Count = 0 Then
                            Dim newSetting As DOFacilitySettings = BuildNewSetting(facility.Oid, facility.FacilityID, defset.Name, defset.Category, defset.Description, defset.DefaultValue, ta)
                            newSetting.Save()
                            AuditLogger.FacilitySettings.LogSettingSaved(newSetting.Facility, newSetting.RuleSetting, newSetting.RuleSettingValue)
                        End If

                    Next

                Else

                    ' Check to see if there's an existing setting in the current facility that has the same setting name and category:
                    ' If so, ignore it and continue
                    ' If Not, create a new DOFacilitySettings object to be returned
                    Dim existingSettingsByFacility As New XPCollection(Of DOFacilitySettings) _
                    (CriteriaOperator.Parse(String.Format("Facility = {0} and RuleSetting = '{1}' and RuleSettingCategory = '{2}'", facility.Oid, defset.Name, defset.Category)))

                    If existingSettingsByFacility.Count = 0 Then
                        Dim newSetting As DOFacilitySettings = BuildNewSetting(facility.Oid, facility.FacilityID, defset.Name, defset.Category, defset.Description, defset.DefaultValue, "")
                        newSetting.Save()
                        AuditLogger.FacilitySettings.LogSettingSaved(newSetting.Facility, newSetting.RuleSetting, newSetting.RuleSettingValue)
                    End If
                End If

            Next

        Next
    End Sub

End Class

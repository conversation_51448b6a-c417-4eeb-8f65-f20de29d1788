﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On



'''<summary>
'''Represents a strongly typed in-memory cache of data.
'''</summary>
<Global.System.Serializable(),  _
 Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
 Global.System.ComponentModel.ToolboxItem(true),  _
 Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema"),  _
 Global.System.Xml.Serialization.XmlRootAttribute("CodingReportDataSet1"),  _
 Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")>  _
Partial Public Class CodingReportDataSet1
    Inherits Global.System.Data.DataSet
    
    Private tableDOCodingReportText As DOCodingReportTextDataTable
    
    Private tableDOChartInfo As DOChartInfoDataTable
    
    Private tableDOCodingReportRecord As DOCodingReportRecordDataTable
    
    Private relationDOChartInfo_DOCodingReportRecord As Global.System.Data.DataRelation
    
    Private _schemaSerializationMode As Global.System.Data.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Sub New()
        MyBase.New
        Me.BeginInit
        Me.InitClass
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler MyBase.Relations.CollectionChanged, schemaChangedHandler
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
        MyBase.New(info, context, false)
        If (Me.IsBinarySerialized(info, context) = true) Then
            Me.InitVars(false)
            Dim schemaChangedHandler1 As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
            AddHandler Me.Tables.CollectionChanged, schemaChangedHandler1
            AddHandler Me.Relations.CollectionChanged, schemaChangedHandler1
            Return
        End If
        Dim strSchema As String = CType(info.GetValue("XmlSchema", GetType(String)),String)
        If (Me.DetermineSchemaSerializationMode(info, context) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
            If (Not (ds.Tables("DOCodingReportText")) Is Nothing) Then
                MyBase.Tables.Add(New DOCodingReportTextDataTable(ds.Tables("DOCodingReportText")))
            End If
            If (Not (ds.Tables("DOChartInfo")) Is Nothing) Then
                MyBase.Tables.Add(New DOChartInfoDataTable(ds.Tables("DOChartInfo")))
            End If
            If (Not (ds.Tables("DOCodingReportRecord")) Is Nothing) Then
                MyBase.Tables.Add(New DOCodingReportRecordDataTable(ds.Tables("DOCodingReportRecord")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
        End If
        Me.GetSerializationData(info, context)
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler Me.Relations.CollectionChanged, schemaChangedHandler
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property DOCodingReportText() As DOCodingReportTextDataTable
        Get
            Return Me.tableDOCodingReportText
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property DOChartInfo() As DOChartInfoDataTable
        Get
            Return Me.tableDOChartInfo
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property DOCodingReportRecord() As DOCodingReportRecordDataTable
        Get
            Return Me.tableDOCodingReportRecord
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.BrowsableAttribute(true),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Visible)>  _
    Public Overrides Property SchemaSerializationMode() As Global.System.Data.SchemaSerializationMode
        Get
            Return Me._schemaSerializationMode
        End Get
        Set
            Me._schemaSerializationMode = value
        End Set
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Tables() As Global.System.Data.DataTableCollection
        Get
            Return MyBase.Tables
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Relations() As Global.System.Data.DataRelationCollection
        Get
            Return MyBase.Relations
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Overrides Sub InitializeDerivedDataSet()
        Me.BeginInit
        Me.InitClass
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Overrides Function Clone() As Global.System.Data.DataSet
        Dim cln As CodingReportDataSet1 = CType(MyBase.Clone,CodingReportDataSet1)
        cln.InitVars
        cln.SchemaSerializationMode = Me.SchemaSerializationMode
        Return cln
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Overrides Function ShouldSerializeTables() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Overrides Function ShouldSerializeRelations() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Overrides Sub ReadXmlSerializable(ByVal reader As Global.System.Xml.XmlReader)
        If (Me.DetermineSchemaSerializationMode(reader) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Me.Reset
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXml(reader)
            If (Not (ds.Tables("DOCodingReportText")) Is Nothing) Then
                MyBase.Tables.Add(New DOCodingReportTextDataTable(ds.Tables("DOCodingReportText")))
            End If
            If (Not (ds.Tables("DOChartInfo")) Is Nothing) Then
                MyBase.Tables.Add(New DOChartInfoDataTable(ds.Tables("DOChartInfo")))
            End If
            If (Not (ds.Tables("DOCodingReportRecord")) Is Nothing) Then
                MyBase.Tables.Add(New DOCodingReportRecordDataTable(ds.Tables("DOCodingReportRecord")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXml(reader)
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Protected Overrides Function GetSchemaSerializable() As Global.System.Xml.Schema.XmlSchema
        Dim stream As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
        Me.WriteXmlSchema(New Global.System.Xml.XmlTextWriter(stream, Nothing))
        stream.Position = 0
        Return Global.System.Xml.Schema.XmlSchema.Read(New Global.System.Xml.XmlTextReader(stream), Nothing)
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Friend Overloads Sub InitVars()
        Me.InitVars(true)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Friend Overloads Sub InitVars(ByVal initTable As Boolean)
        Me.tableDOCodingReportText = CType(MyBase.Tables("DOCodingReportText"),DOCodingReportTextDataTable)
        If (initTable = true) Then
            If (Not (Me.tableDOCodingReportText) Is Nothing) Then
                Me.tableDOCodingReportText.InitVars
            End If
        End If
        Me.tableDOChartInfo = CType(MyBase.Tables("DOChartInfo"),DOChartInfoDataTable)
        If (initTable = true) Then
            If (Not (Me.tableDOChartInfo) Is Nothing) Then
                Me.tableDOChartInfo.InitVars
            End If
        End If
        Me.tableDOCodingReportRecord = CType(MyBase.Tables("DOCodingReportRecord"),DOCodingReportRecordDataTable)
        If (initTable = true) Then
            If (Not (Me.tableDOCodingReportRecord) Is Nothing) Then
                Me.tableDOCodingReportRecord.InitVars
            End If
        End If
        Me.relationDOChartInfo_DOCodingReportRecord = Me.Relations("DOChartInfo_DOCodingReportRecord")
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Private Sub InitClass()
        Me.DataSetName = "CodingReportDataSet1"
        Me.Prefix = ""
        Me.Namespace = "http://tempuri.org/CodingReportDataSet1.xsd"
        Me.EnforceConstraints = true
        Me.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
        Me.tableDOCodingReportText = New DOCodingReportTextDataTable()
        MyBase.Tables.Add(Me.tableDOCodingReportText)
        Me.tableDOChartInfo = New DOChartInfoDataTable()
        MyBase.Tables.Add(Me.tableDOChartInfo)
        Me.tableDOCodingReportRecord = New DOCodingReportRecordDataTable()
        MyBase.Tables.Add(Me.tableDOCodingReportRecord)
        Me.relationDOChartInfo_DOCodingReportRecord = New Global.System.Data.DataRelation("DOChartInfo_DOCodingReportRecord", New Global.System.Data.DataColumn() {Me.tableDOChartInfo.ChartColumn}, New Global.System.Data.DataColumn() {Me.tableDOCodingReportRecord.ChartColumn}, false)
        Me.Relations.Add(Me.relationDOChartInfo_DOCodingReportRecord)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Private Function ShouldSerializeDOCodingReportText() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Private Function ShouldSerializeDOChartInfo() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Private Function ShouldSerializeDOCodingReportRecord() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Private Sub SchemaChanged(ByVal sender As Object, ByVal e As Global.System.ComponentModel.CollectionChangeEventArgs)
        If (e.Action = Global.System.ComponentModel.CollectionChangeAction.Remove) Then
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Shared Function GetTypedDataSetSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
        Dim ds As CodingReportDataSet1 = New CodingReportDataSet1()
        Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
        Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
        Dim any As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
        any.Namespace = ds.Namespace
        sequence.Items.Add(any)
        type.Particle = sequence
        Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
        If xs.Contains(dsSchema.TargetNamespace) Then
            Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Try 
                Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                dsSchema.Write(s1)
                Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                Do While schemas.MoveNext
                    schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                    s2.SetLength(0)
                    schema.Write(s2)
                    If (s1.Length = s2.Length) Then
                        s1.Position = 0
                        s2.Position = 0
                        
                        Do While ((s1.Position <> s1.Length)  _
                                    AndAlso (s1.ReadByte = s2.ReadByte))
                            
                            
                        Loop
                        If (s1.Position = s1.Length) Then
                            Return type
                        End If
                    End If
                    
                Loop
            Finally
                If (Not (s1) Is Nothing) Then
                    s1.Close
                End If
                If (Not (s2) Is Nothing) Then
                    s2.Close
                End If
            End Try
        End If
        xs.Add(dsSchema)
        Return type
    End Function
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Delegate Sub DOCodingReportTextRowChangeEventHandler(ByVal sender As Object, ByVal e As DOCodingReportTextRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Delegate Sub DOChartInfoRowChangeEventHandler(ByVal sender As Object, ByVal e As DOChartInfoRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Delegate Sub DOCodingReportRecordRowChangeEventHandler(ByVal sender As Object, ByVal e As DOCodingReportRecordRowChangeEvent)
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class DOCodingReportTextDataTable
        Inherits Global.System.Data.TypedTableBase(Of DOCodingReportTextRow)
        
        Private columnLineText As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "DOCodingReportText"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property LineTextColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLineText
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As DOCodingReportTextRow
            Get
                Return CType(Me.Rows(index),DOCodingReportTextRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportTextRowChanging As DOCodingReportTextRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportTextRowChanged As DOCodingReportTextRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportTextRowDeleting As DOCodingReportTextRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportTextRowDeleted As DOCodingReportTextRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Sub AddDOCodingReportTextRow(ByVal row As DOCodingReportTextRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Function AddDOCodingReportTextRow(ByVal LineText As String) As DOCodingReportTextRow
            Dim rowDOCodingReportTextRow As DOCodingReportTextRow = CType(Me.NewRow,DOCodingReportTextRow)
            Dim columnValuesArray() As Object = New Object() {LineText}
            rowDOCodingReportTextRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowDOCodingReportTextRow)
            Return rowDOCodingReportTextRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As DOCodingReportTextDataTable = CType(MyBase.Clone,DOCodingReportTextDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New DOCodingReportTextDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub InitVars()
            Me.columnLineText = MyBase.Columns("LineText")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitClass()
            Me.columnLineText = New Global.System.Data.DataColumn("LineText", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLineText)
            Me.columnLineText.MaxLength = 150
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function NewDOCodingReportTextRow() As DOCodingReportTextRow
            Return CType(Me.NewRow,DOCodingReportTextRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New DOCodingReportTextRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(DOCodingReportTextRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.DOCodingReportTextRowChangedEvent) Is Nothing) Then
                RaiseEvent DOCodingReportTextRowChanged(Me, New DOCodingReportTextRowChangeEvent(CType(e.Row,DOCodingReportTextRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.DOCodingReportTextRowChangingEvent) Is Nothing) Then
                RaiseEvent DOCodingReportTextRowChanging(Me, New DOCodingReportTextRowChangeEvent(CType(e.Row,DOCodingReportTextRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.DOCodingReportTextRowDeletedEvent) Is Nothing) Then
                RaiseEvent DOCodingReportTextRowDeleted(Me, New DOCodingReportTextRowChangeEvent(CType(e.Row,DOCodingReportTextRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.DOCodingReportTextRowDeletingEvent) Is Nothing) Then
                RaiseEvent DOCodingReportTextRowDeleting(Me, New DOCodingReportTextRowChangeEvent(CType(e.Row,DOCodingReportTextRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub RemoveDOCodingReportTextRow(ByVal row As DOCodingReportTextRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As CodingReportDataSet1 = New CodingReportDataSet1()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "DOCodingReportTextDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class DOChartInfoDataTable
        Inherits Global.System.Data.TypedTableBase(Of DOChartInfoRow)
        
        Private columnVisitID As Global.System.Data.DataColumn
        
        Private columnTreatmentArea As Global.System.Data.DataColumn
        
        Private columnFacility As Global.System.Data.DataColumn
        
        Private columnChart As Global.System.Data.DataColumn
        
        Private columnChartVersion As Global.System.Data.DataColumn
        
        Private columnLongName As Global.System.Data.DataColumn
        
        Private columnEMLevel As Global.System.Data.DataColumn
        
        Private columntmpTxArea As Global.System.Data.DataColumn
        
        Private columnConfigInstanceVersion As Global.System.Data.DataColumn
        
        Private columnPatientFirstName As Global.System.Data.DataColumn
        
        Private columnPatientMiddleName As Global.System.Data.DataColumn
        
        Private columnPatientLastName As Global.System.Data.DataColumn
        
        Private columnEMLevelHCPCS As Global.System.Data.DataColumn
        
        Private columnEMLevelCDM As Global.System.Data.DataColumn
        
        Private columnChartStatus As Global.System.Data.DataColumn
        
        Private columnPatientSuffix As Global.System.Data.DataColumn
        
        Private columnUserName As Global.System.Data.DataColumn
        
        Private columnPhysChartStatus As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnDateOfService As Global.System.Data.DataColumn
        
        Private columnObsChartStatus As Global.System.Data.DataColumn
        
        Private columnChartType As Global.System.Data.DataColumn
        
        Private columnUsesPoints As Global.System.Data.DataColumn
        
        Private columnDateOfService1 As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "DOChartInfo"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property VisitIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnVisitID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property TreatmentAreaColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnTreatmentArea
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property FacilityColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFacility
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ChartColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChart
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ChartVersionColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChartVersion
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property LongNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLongName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property EMLevelColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnEMLevel
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property tmpTxAreaColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columntmpTxArea
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ConfigInstanceVersionColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnConfigInstanceVersion
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PatientFirstNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPatientFirstName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PatientMiddleNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPatientMiddleName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PatientLastNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPatientLastName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property EMLevelHCPCSColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnEMLevelHCPCS
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property EMLevelCDMColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnEMLevelCDM
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ChartStatusColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChartStatus
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PatientSuffixColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPatientSuffix
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property UserNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnUserName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PhysChartStatusColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPhysChartStatus
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property DateOfServiceColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDateOfService
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ObsChartStatusColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnObsChartStatus
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ChartTypeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChartType
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property UsesPointsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnUsesPoints
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property DateOfService1Column() As Global.System.Data.DataColumn
            Get
                Return Me.columnDateOfService1
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As DOChartInfoRow
            Get
                Return CType(Me.Rows(index),DOChartInfoRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOChartInfoRowChanging As DOChartInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOChartInfoRowChanged As DOChartInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOChartInfoRowDeleting As DOChartInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOChartInfoRowDeleted As DOChartInfoRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Sub AddDOChartInfoRow(ByVal row As DOChartInfoRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Function AddDOChartInfoRow( _
                    ByVal VisitID As String,  _
                    ByVal TreatmentArea As String,  _
                    ByVal Facility As Integer,  _
                    ByVal Chart As Integer,  _
                    ByVal ChartVersion As Integer,  _
                    ByVal LongName As String,  _
                    ByVal EMLevel As String,  _
                    ByVal tmpTxArea As String,  _
                    ByVal ConfigInstanceVersion As Integer,  _
                    ByVal PatientFirstName As String,  _
                    ByVal PatientMiddleName As String,  _
                    ByVal PatientLastName As String,  _
                    ByVal EMLevelHCPCS As String,  _
                    ByVal EMLevelCDM As String,  _
                    ByVal ChartStatus As String,  _
                    ByVal PatientSuffix As String,  _
                    ByVal UserName As String,  _
                    ByVal PhysChartStatus As String,  _
                    ByVal CreationDate As Date,  _
                    ByVal DateOfService As String,  _
                    ByVal ObsChartStatus As String,  _
                    ByVal ChartType As Integer,  _
                    ByVal UsesPoints As Boolean,  _
                    ByVal DateOfService1 As Date) As DOChartInfoRow
            Dim rowDOChartInfoRow As DOChartInfoRow = CType(Me.NewRow,DOChartInfoRow)
            Dim columnValuesArray() As Object = New Object() {VisitID, TreatmentArea, Facility, Chart, ChartVersion, LongName, EMLevel, tmpTxArea, ConfigInstanceVersion, PatientFirstName, PatientMiddleName, PatientLastName, EMLevelHCPCS, EMLevelCDM, ChartStatus, PatientSuffix, UserName, PhysChartStatus, CreationDate, DateOfService, ObsChartStatus, ChartType, UsesPoints, DateOfService1}
            rowDOChartInfoRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowDOChartInfoRow)
            Return rowDOChartInfoRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As DOChartInfoDataTable = CType(MyBase.Clone,DOChartInfoDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New DOChartInfoDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub InitVars()
            Me.columnVisitID = MyBase.Columns("VisitID")
            Me.columnTreatmentArea = MyBase.Columns("TreatmentArea")
            Me.columnFacility = MyBase.Columns("Facility")
            Me.columnChart = MyBase.Columns("Chart")
            Me.columnChartVersion = MyBase.Columns("ChartVersion")
            Me.columnLongName = MyBase.Columns("LongName")
            Me.columnEMLevel = MyBase.Columns("EMLevel")
            Me.columntmpTxArea = MyBase.Columns("tmpTxArea")
            Me.columnConfigInstanceVersion = MyBase.Columns("ConfigInstanceVersion")
            Me.columnPatientFirstName = MyBase.Columns("PatientFirstName")
            Me.columnPatientMiddleName = MyBase.Columns("PatientMiddleName")
            Me.columnPatientLastName = MyBase.Columns("PatientLastName")
            Me.columnEMLevelHCPCS = MyBase.Columns("EMLevelHCPCS")
            Me.columnEMLevelCDM = MyBase.Columns("EMLevelCDM")
            Me.columnChartStatus = MyBase.Columns("ChartStatus")
            Me.columnPatientSuffix = MyBase.Columns("PatientSuffix")
            Me.columnUserName = MyBase.Columns("UserName")
            Me.columnPhysChartStatus = MyBase.Columns("PhysChartStatus")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnDateOfService = MyBase.Columns("DateOfService")
            Me.columnObsChartStatus = MyBase.Columns("ObsChartStatus")
            Me.columnChartType = MyBase.Columns("ChartType")
            Me.columnUsesPoints = MyBase.Columns("UsesPoints")
            Me.columnDateOfService1 = MyBase.Columns("DateOfService1")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitClass()
            Me.columnVisitID = New Global.System.Data.DataColumn("VisitID", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnVisitID)
            Me.columnTreatmentArea = New Global.System.Data.DataColumn("TreatmentArea", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnTreatmentArea)
            Me.columnFacility = New Global.System.Data.DataColumn("Facility", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFacility)
            Me.columnChart = New Global.System.Data.DataColumn("Chart", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChart)
            Me.columnChartVersion = New Global.System.Data.DataColumn("ChartVersion", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChartVersion)
            Me.columnLongName = New Global.System.Data.DataColumn("LongName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLongName)
            Me.columnEMLevel = New Global.System.Data.DataColumn("EMLevel", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnEMLevel)
            Me.columntmpTxArea = New Global.System.Data.DataColumn("tmpTxArea", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columntmpTxArea)
            Me.columnConfigInstanceVersion = New Global.System.Data.DataColumn("ConfigInstanceVersion", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnConfigInstanceVersion)
            Me.columnPatientFirstName = New Global.System.Data.DataColumn("PatientFirstName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPatientFirstName)
            Me.columnPatientMiddleName = New Global.System.Data.DataColumn("PatientMiddleName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPatientMiddleName)
            Me.columnPatientLastName = New Global.System.Data.DataColumn("PatientLastName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPatientLastName)
            Me.columnEMLevelHCPCS = New Global.System.Data.DataColumn("EMLevelHCPCS", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnEMLevelHCPCS)
            Me.columnEMLevelCDM = New Global.System.Data.DataColumn("EMLevelCDM", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnEMLevelCDM)
            Me.columnChartStatus = New Global.System.Data.DataColumn("ChartStatus", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChartStatus)
            Me.columnPatientSuffix = New Global.System.Data.DataColumn("PatientSuffix", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPatientSuffix)
            Me.columnUserName = New Global.System.Data.DataColumn("UserName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnUserName)
            Me.columnPhysChartStatus = New Global.System.Data.DataColumn("PhysChartStatus", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPhysChartStatus)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnDateOfService = New Global.System.Data.DataColumn("DateOfService", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDateOfService)
            Me.columnObsChartStatus = New Global.System.Data.DataColumn("ObsChartStatus", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnObsChartStatus)
            Me.columnChartType = New Global.System.Data.DataColumn("ChartType", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChartType)
            Me.columnUsesPoints = New Global.System.Data.DataColumn("UsesPoints", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnUsesPoints)
            Me.columnDateOfService1 = New Global.System.Data.DataColumn("DateOfService1", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDateOfService1)
            Me.columnVisitID.MaxLength = 45
            Me.columnTreatmentArea.MaxLength = 50
            Me.columnChart.AllowDBNull = false
            Me.columnLongName.MaxLength = 60
            Me.columnEMLevel.MaxLength = 50
            Me.columntmpTxArea.MaxLength = 50
            Me.columnPatientFirstName.MaxLength = 20
            Me.columnPatientMiddleName.MaxLength = 20
            Me.columnPatientLastName.MaxLength = 20
            Me.columnEMLevelHCPCS.MaxLength = 25
            Me.columnEMLevelCDM.MaxLength = 25
            Me.columnChartStatus.MaxLength = 50
            Me.columnPatientSuffix.MaxLength = 20
            Me.columnUserName.MaxLength = 30
            Me.columnPhysChartStatus.MaxLength = 50
            Me.columnObsChartStatus.MaxLength = 50
            Me.columnDateOfService1.Caption = "DateOfService"
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function NewDOChartInfoRow() As DOChartInfoRow
            Return CType(Me.NewRow,DOChartInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New DOChartInfoRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(DOChartInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.DOChartInfoRowChangedEvent) Is Nothing) Then
                RaiseEvent DOChartInfoRowChanged(Me, New DOChartInfoRowChangeEvent(CType(e.Row,DOChartInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.DOChartInfoRowChangingEvent) Is Nothing) Then
                RaiseEvent DOChartInfoRowChanging(Me, New DOChartInfoRowChangeEvent(CType(e.Row,DOChartInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.DOChartInfoRowDeletedEvent) Is Nothing) Then
                RaiseEvent DOChartInfoRowDeleted(Me, New DOChartInfoRowChangeEvent(CType(e.Row,DOChartInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.DOChartInfoRowDeletingEvent) Is Nothing) Then
                RaiseEvent DOChartInfoRowDeleting(Me, New DOChartInfoRowChangeEvent(CType(e.Row,DOChartInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub RemoveDOChartInfoRow(ByVal row As DOChartInfoRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As CodingReportDataSet1 = New CodingReportDataSet1()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "DOChartInfoDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class DOCodingReportRecordDataTable
        Inherits Global.System.Data.TypedTableBase(Of DOCodingReportRecordRow)
        
        Private columnGCRecord As Global.System.Data.DataColumn
        
        Private columnOID As Global.System.Data.DataColumn
        
        Private columnChart As Global.System.Data.DataColumn
        
        Private columnReportDisplayOrder As Global.System.Data.DataColumn
        
        Private columnCDM As Global.System.Data.DataColumn
        
        Private columnHCPCS As Global.System.Data.DataColumn
        
        Private columnICD9 As Global.System.Data.DataColumn
        
        Private columnNewProperty As Global.System.Data.DataColumn
        
        Private columnModifier As Global.System.Data.DataColumn
        
        Private columnInsertDate As Global.System.Data.DataColumn
        
        Private columnOptimisticLockField As Global.System.Data.DataColumn
        
        Private columnDetail As Global.System.Data.DataColumn
        
        Private columnPoints As Global.System.Data.DataColumn
        
        Private columnQuantity As Global.System.Data.DataColumn
        
        Private columnIsPhysicianTypeCode As Global.System.Data.DataColumn
        
        Private columnCodeType As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "DOCodingReportRecord"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property GCRecordColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnGCRecord
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property OIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnOID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ChartColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChart
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ReportDisplayOrderColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnReportDisplayOrder
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property CDMColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCDM
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property HCPCSColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnHCPCS
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ICD9Column() As Global.System.Data.DataColumn
            Get
                Return Me.columnICD9
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property NewPropertyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnNewProperty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property ModifierColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnModifier
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property InsertDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInsertDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property OptimisticLockFieldColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnOptimisticLockField
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property DetailColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDetail
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property PointsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPoints
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property QuantityColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnQuantity
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property IsPhysicianTypeCodeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnIsPhysicianTypeCode
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property CodeTypeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCodeType
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As DOCodingReportRecordRow
            Get
                Return CType(Me.Rows(index),DOCodingReportRecordRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportRecordRowChanging As DOCodingReportRecordRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportRecordRowChanged As DOCodingReportRecordRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportRecordRowDeleting As DOCodingReportRecordRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Event DOCodingReportRecordRowDeleted As DOCodingReportRecordRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Sub AddDOCodingReportRecordRow(ByVal row As DOCodingReportRecordRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overloads Function AddDOCodingReportRecordRow(ByVal GCRecord As Integer, ByVal parentDOChartInfoRowByDOChartInfo_DOCodingReportRecord As DOChartInfoRow, ByVal ReportDisplayOrder As Integer, ByVal CDM As String, ByVal HCPCS As String, ByVal ICD9 As String, ByVal NewProperty As Integer, ByVal Modifier As String, ByVal InsertDate As Date, ByVal OptimisticLockField As Integer, ByVal Detail As String, ByVal Points As Integer, ByVal Quantity As Integer, ByVal IsPhysicianTypeCode As Boolean, ByVal CodeType As String) As DOCodingReportRecordRow
            Dim rowDOCodingReportRecordRow As DOCodingReportRecordRow = CType(Me.NewRow,DOCodingReportRecordRow)
            Dim columnValuesArray() As Object = New Object() {GCRecord, Nothing, Nothing, ReportDisplayOrder, CDM, HCPCS, ICD9, NewProperty, Modifier, InsertDate, OptimisticLockField, Detail, Points, Quantity, IsPhysicianTypeCode, CodeType}
            If (Not (parentDOChartInfoRowByDOChartInfo_DOCodingReportRecord) Is Nothing) Then
                columnValuesArray(2) = parentDOChartInfoRowByDOChartInfo_DOCodingReportRecord(3)
            End If
            rowDOCodingReportRecordRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowDOCodingReportRecordRow)
            Return rowDOCodingReportRecordRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function FindByOIDChart(ByVal OID As Integer, ByVal Chart As Integer) As DOCodingReportRecordRow
            Return CType(Me.Rows.Find(New Object() {OID, Chart}),DOCodingReportRecordRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As DOCodingReportRecordDataTable = CType(MyBase.Clone,DOCodingReportRecordDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New DOCodingReportRecordDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub InitVars()
            Me.columnGCRecord = MyBase.Columns("GCRecord")
            Me.columnOID = MyBase.Columns("OID")
            Me.columnChart = MyBase.Columns("Chart")
            Me.columnReportDisplayOrder = MyBase.Columns("ReportDisplayOrder")
            Me.columnCDM = MyBase.Columns("CDM")
            Me.columnHCPCS = MyBase.Columns("HCPCS")
            Me.columnICD9 = MyBase.Columns("ICD9")
            Me.columnNewProperty = MyBase.Columns("NewProperty")
            Me.columnModifier = MyBase.Columns("Modifier")
            Me.columnInsertDate = MyBase.Columns("InsertDate")
            Me.columnOptimisticLockField = MyBase.Columns("OptimisticLockField")
            Me.columnDetail = MyBase.Columns("Detail")
            Me.columnPoints = MyBase.Columns("Points")
            Me.columnQuantity = MyBase.Columns("Quantity")
            Me.columnIsPhysicianTypeCode = MyBase.Columns("IsPhysicianTypeCode")
            Me.columnCodeType = MyBase.Columns("CodeType")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitClass()
            Me.columnGCRecord = New Global.System.Data.DataColumn("GCRecord", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnGCRecord)
            Me.columnOID = New Global.System.Data.DataColumn("OID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnOID)
            Me.columnChart = New Global.System.Data.DataColumn("Chart", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChart)
            Me.columnReportDisplayOrder = New Global.System.Data.DataColumn("ReportDisplayOrder", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnReportDisplayOrder)
            Me.columnCDM = New Global.System.Data.DataColumn("CDM", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCDM)
            Me.columnHCPCS = New Global.System.Data.DataColumn("HCPCS", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnHCPCS)
            Me.columnICD9 = New Global.System.Data.DataColumn("ICD9", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnICD9)
            Me.columnNewProperty = New Global.System.Data.DataColumn("NewProperty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnNewProperty)
            Me.columnModifier = New Global.System.Data.DataColumn("Modifier", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnModifier)
            Me.columnInsertDate = New Global.System.Data.DataColumn("InsertDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInsertDate)
            Me.columnOptimisticLockField = New Global.System.Data.DataColumn("OptimisticLockField", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnOptimisticLockField)
            Me.columnDetail = New Global.System.Data.DataColumn("Detail", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDetail)
            Me.columnPoints = New Global.System.Data.DataColumn("Points", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPoints)
            Me.columnQuantity = New Global.System.Data.DataColumn("Quantity", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnQuantity)
            Me.columnIsPhysicianTypeCode = New Global.System.Data.DataColumn("IsPhysicianTypeCode", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnIsPhysicianTypeCode)
            Me.columnCodeType = New Global.System.Data.DataColumn("CodeType", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCodeType)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnOID, Me.columnChart}, true))
            Me.columnOID.AutoIncrement = true
            Me.columnOID.AllowDBNull = false
            Me.columnChart.AllowDBNull = false
            Me.columnCDM.MaxLength = 250
            Me.columnHCPCS.MaxLength = 25
            Me.columnICD9.MaxLength = 25
            Me.columnModifier.MaxLength = 25
            Me.columnDetail.MaxLength = 200
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function NewDOCodingReportRecordRow() As DOCodingReportRecordRow
            Return CType(Me.NewRow,DOCodingReportRecordRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New DOCodingReportRecordRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(DOCodingReportRecordRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.DOCodingReportRecordRowChangedEvent) Is Nothing) Then
                RaiseEvent DOCodingReportRecordRowChanged(Me, New DOCodingReportRecordRowChangeEvent(CType(e.Row,DOCodingReportRecordRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.DOCodingReportRecordRowChangingEvent) Is Nothing) Then
                RaiseEvent DOCodingReportRecordRowChanging(Me, New DOCodingReportRecordRowChangeEvent(CType(e.Row,DOCodingReportRecordRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.DOCodingReportRecordRowDeletedEvent) Is Nothing) Then
                RaiseEvent DOCodingReportRecordRowDeleted(Me, New DOCodingReportRecordRowChangeEvent(CType(e.Row,DOCodingReportRecordRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.DOCodingReportRecordRowDeletingEvent) Is Nothing) Then
                RaiseEvent DOCodingReportRecordRowDeleting(Me, New DOCodingReportRecordRowChangeEvent(CType(e.Row,DOCodingReportRecordRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub RemoveDOCodingReportRecordRow(ByVal row As DOCodingReportRecordRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As CodingReportDataSet1 = New CodingReportDataSet1()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "DOCodingReportRecordDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class DOCodingReportTextRow
        Inherits Global.System.Data.DataRow
        
        Private tableDOCodingReportText As DOCodingReportTextDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableDOCodingReportText = CType(Me.Table,DOCodingReportTextDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property LineText() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportText.LineTextColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LineText' in table 'DOCodingReportText' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportText.LineTextColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsLineTextNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportText.LineTextColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetLineTextNull()
            Me(Me.tableDOCodingReportText.LineTextColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class DOChartInfoRow
        Inherits Global.System.Data.DataRow
        
        Private tableDOChartInfo As DOChartInfoDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableDOChartInfo = CType(Me.Table,DOChartInfoDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property VisitID() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.VisitIDColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'VisitID' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.VisitIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property TreatmentArea() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.TreatmentAreaColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'TreatmentArea' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.TreatmentAreaColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Facility() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.FacilityColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Facility' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.FacilityColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Chart() As Integer
            Get
                Return CType(Me(Me.tableDOChartInfo.ChartColumn),Integer)
            End Get
            Set
                Me(Me.tableDOChartInfo.ChartColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ChartVersion() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.ChartVersionColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ChartVersion' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.ChartVersionColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property LongName() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.LongNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LongName' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.LongNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property EMLevel() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.EMLevelColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'EMLevel' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.EMLevelColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property tmpTxArea() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.tmpTxAreaColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'tmpTxArea' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.tmpTxAreaColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ConfigInstanceVersion() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.ConfigInstanceVersionColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ConfigInstanceVersion' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.ConfigInstanceVersionColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property PatientFirstName() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.PatientFirstNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PatientFirstName' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.PatientFirstNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property PatientMiddleName() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.PatientMiddleNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PatientMiddleName' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.PatientMiddleNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property PatientLastName() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.PatientLastNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PatientLastName' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.PatientLastNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property EMLevelHCPCS() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.EMLevelHCPCSColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'EMLevelHCPCS' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.EMLevelHCPCSColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property EMLevelCDM() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.EMLevelCDMColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'EMLevelCDM' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.EMLevelCDMColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ChartStatus() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.ChartStatusColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ChartStatus' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.ChartStatusColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property PatientSuffix() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.PatientSuffixColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PatientSuffix' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.PatientSuffixColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property UserName() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.UserNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'UserName' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.UserNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property PhysChartStatus() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.PhysChartStatusColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PhysChartStatus' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.PhysChartStatusColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property CreationDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.CreationDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CreationDate' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property DateOfService() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.DateOfServiceColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'DateOfService' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.DateOfServiceColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ObsChartStatus() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.ObsChartStatusColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ObsChartStatus' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.ObsChartStatusColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ChartType() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.ChartTypeColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ChartType' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.ChartTypeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property UsesPoints() As Boolean
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.UsesPointsColumn),Boolean)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'UsesPoints' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.UsesPointsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property DateOfService1() As Date
            Get
                Try 
                    Return CType(Me(Me.tableDOChartInfo.DateOfService1Column),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'DateOfService1' in table 'DOChartInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOChartInfo.DateOfService1Column) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsVisitIDNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.VisitIDColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetVisitIDNull()
            Me(Me.tableDOChartInfo.VisitIDColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsTreatmentAreaNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.TreatmentAreaColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetTreatmentAreaNull()
            Me(Me.tableDOChartInfo.TreatmentAreaColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsFacilityNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.FacilityColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetFacilityNull()
            Me(Me.tableDOChartInfo.FacilityColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsChartVersionNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.ChartVersionColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetChartVersionNull()
            Me(Me.tableDOChartInfo.ChartVersionColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsLongNameNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.LongNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetLongNameNull()
            Me(Me.tableDOChartInfo.LongNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsEMLevelNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.EMLevelColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetEMLevelNull()
            Me(Me.tableDOChartInfo.EMLevelColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IstmpTxAreaNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.tmpTxAreaColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SettmpTxAreaNull()
            Me(Me.tableDOChartInfo.tmpTxAreaColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsConfigInstanceVersionNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.ConfigInstanceVersionColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetConfigInstanceVersionNull()
            Me(Me.tableDOChartInfo.ConfigInstanceVersionColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPatientFirstNameNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.PatientFirstNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPatientFirstNameNull()
            Me(Me.tableDOChartInfo.PatientFirstNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPatientMiddleNameNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.PatientMiddleNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPatientMiddleNameNull()
            Me(Me.tableDOChartInfo.PatientMiddleNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPatientLastNameNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.PatientLastNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPatientLastNameNull()
            Me(Me.tableDOChartInfo.PatientLastNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsEMLevelHCPCSNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.EMLevelHCPCSColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetEMLevelHCPCSNull()
            Me(Me.tableDOChartInfo.EMLevelHCPCSColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsEMLevelCDMNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.EMLevelCDMColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetEMLevelCDMNull()
            Me(Me.tableDOChartInfo.EMLevelCDMColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsChartStatusNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.ChartStatusColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetChartStatusNull()
            Me(Me.tableDOChartInfo.ChartStatusColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPatientSuffixNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.PatientSuffixColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPatientSuffixNull()
            Me(Me.tableDOChartInfo.PatientSuffixColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsUserNameNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.UserNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetUserNameNull()
            Me(Me.tableDOChartInfo.UserNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPhysChartStatusNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.PhysChartStatusColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPhysChartStatusNull()
            Me(Me.tableDOChartInfo.PhysChartStatusColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsCreationDateNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.CreationDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetCreationDateNull()
            Me(Me.tableDOChartInfo.CreationDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsDateOfServiceNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.DateOfServiceColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetDateOfServiceNull()
            Me(Me.tableDOChartInfo.DateOfServiceColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsObsChartStatusNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.ObsChartStatusColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetObsChartStatusNull()
            Me(Me.tableDOChartInfo.ObsChartStatusColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsChartTypeNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.ChartTypeColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetChartTypeNull()
            Me(Me.tableDOChartInfo.ChartTypeColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsUsesPointsNull() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.UsesPointsColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetUsesPointsNull()
            Me(Me.tableDOChartInfo.UsesPointsColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsDateOfService1Null() As Boolean
            Return Me.IsNull(Me.tableDOChartInfo.DateOfService1Column)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetDateOfService1Null()
            Me(Me.tableDOChartInfo.DateOfService1Column) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function GetDOCodingReportRecordRows() As DOCodingReportRecordRow()
            If (Me.Table.ChildRelations("DOChartInfo_DOCodingReportRecord") Is Nothing) Then
                Return New DOCodingReportRecordRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("DOChartInfo_DOCodingReportRecord")),DOCodingReportRecordRow())
            End If
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class DOCodingReportRecordRow
        Inherits Global.System.Data.DataRow
        
        Private tableDOCodingReportRecord As DOCodingReportRecordDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableDOCodingReportRecord = CType(Me.Table,DOCodingReportRecordDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property GCRecord() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.GCRecordColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'GCRecord' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.GCRecordColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property OID() As Integer
            Get
                Return CType(Me(Me.tableDOCodingReportRecord.OIDColumn),Integer)
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.OIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Chart() As Integer
            Get
                Return CType(Me(Me.tableDOCodingReportRecord.ChartColumn),Integer)
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.ChartColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ReportDisplayOrder() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.ReportDisplayOrderColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ReportDisplayOrder' in table 'DOCodingReportRecord' is DBNu"& _ 
                            "ll.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.ReportDisplayOrderColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property CDM() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.CDMColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CDM' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.CDMColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property HCPCS() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.HCPCSColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'HCPCS' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.HCPCSColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ICD9() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.ICD9Column),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ICD9' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.ICD9Column) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property NewProperty() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.NewPropertyColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'NewProperty' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.NewPropertyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Modifier() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.ModifierColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Modifier' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.ModifierColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property InsertDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.InsertDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'InsertDate' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.InsertDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property OptimisticLockField() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.OptimisticLockFieldColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'OptimisticLockField' in table 'DOCodingReportRecord' is DBN"& _ 
                            "ull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.OptimisticLockFieldColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Detail() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.DetailColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Detail' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.DetailColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Points() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.PointsColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Points' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.PointsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property Quantity() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.QuantityColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Quantity' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.QuantityColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property IsPhysicianTypeCode() As Boolean
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.IsPhysicianTypeCodeColumn),Boolean)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'IsPhysicianTypeCode' in table 'DOCodingReportRecord' is DBN"& _ 
                            "ull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.IsPhysicianTypeCodeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property CodeType() As String
            Get
                Try 
                    Return CType(Me(Me.tableDOCodingReportRecord.CodeTypeColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CodeType' in table 'DOCodingReportRecord' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableDOCodingReportRecord.CodeTypeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property DOChartInfoRow() As DOChartInfoRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("DOChartInfo_DOCodingReportRecord")),DOChartInfoRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("DOChartInfo_DOCodingReportRecord"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsGCRecordNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.GCRecordColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetGCRecordNull()
            Me(Me.tableDOCodingReportRecord.GCRecordColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsReportDisplayOrderNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.ReportDisplayOrderColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetReportDisplayOrderNull()
            Me(Me.tableDOCodingReportRecord.ReportDisplayOrderColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsCDMNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.CDMColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetCDMNull()
            Me(Me.tableDOCodingReportRecord.CDMColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsHCPCSNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.HCPCSColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetHCPCSNull()
            Me(Me.tableDOCodingReportRecord.HCPCSColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsICD9Null() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.ICD9Column)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetICD9Null()
            Me(Me.tableDOCodingReportRecord.ICD9Column) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsNewPropertyNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.NewPropertyColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetNewPropertyNull()
            Me(Me.tableDOCodingReportRecord.NewPropertyColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsModifierNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.ModifierColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetModifierNull()
            Me(Me.tableDOCodingReportRecord.ModifierColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsInsertDateNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.InsertDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetInsertDateNull()
            Me(Me.tableDOCodingReportRecord.InsertDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsOptimisticLockFieldNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.OptimisticLockFieldColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetOptimisticLockFieldNull()
            Me(Me.tableDOCodingReportRecord.OptimisticLockFieldColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsDetailNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.DetailColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetDetailNull()
            Me(Me.tableDOCodingReportRecord.DetailColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsPointsNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.PointsColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetPointsNull()
            Me(Me.tableDOCodingReportRecord.PointsColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsQuantityNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.QuantityColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetQuantityNull()
            Me(Me.tableDOCodingReportRecord.QuantityColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsIsPhysicianTypeCodeNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.IsPhysicianTypeCodeColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetIsPhysicianTypeCodeNull()
            Me(Me.tableDOCodingReportRecord.IsPhysicianTypeCodeColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Function IsCodeTypeNull() As Boolean
            Return Me.IsNull(Me.tableDOCodingReportRecord.CodeTypeColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub SetCodeTypeNull()
            Me(Me.tableDOCodingReportRecord.CodeTypeColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Class DOCodingReportTextRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As DOCodingReportTextRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New(ByVal row As DOCodingReportTextRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Row() As DOCodingReportTextRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Class DOChartInfoRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As DOChartInfoRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New(ByVal row As DOChartInfoRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Row() As DOChartInfoRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
    Public Class DOCodingReportRecordRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As DOCodingReportRecordRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New(ByVal row As DOCodingReportRecordRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Row() As DOCodingReportRecordRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
End Class

Namespace CodingReportDataSet1TableAdapters
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class DOCodingReportTextTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.OleDb.OleDbDataAdapter
        
        Private _connection As Global.System.Data.OleDb.OleDbConnection
        
        Private _commandCollection() As Global.System.Data.OleDb.OleDbCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.OleDb.OleDbDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Property Connection() As Global.System.Data.OleDb.OleDbConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.OleDb.OleDbCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.OleDb.OleDbCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.OleDb.OleDbDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "DOCodingReportText"
            tableMapping.ColumnMappings.Add("LineText", "LineText")
            Me._adapter.TableMappings.Add(tableMapping)
            Me._adapter.InsertCommand = New Global.System.Data.OleDb.OleDbCommand()
            Me._adapter.InsertCommand.Connection = Me.Connection
            Me._adapter.InsertCommand.CommandText = "INSERT INTO ""DBA"".""CodingReportText"" (""LineText"") VALUES (?)"
            Me._adapter.InsertCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("LineText", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "LineText", Global.System.Data.DataRowVersion.Current, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.OleDb.OleDbConnection()
            Me._connection.ConnectionString = Global.CodingReport.My.MySettings.Default.ASAOLE
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.OleDb.OleDbCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.OleDb.OleDbCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        LineText"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            DBA.DOCodingReportText"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Rep"& _ 
                "ortBand = ?) AND (ConfigInstance = ?)"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"ORDER BY LineNumber"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ReportBand", Global.System.Data.OleDb.OleDbType.[Char], 30, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportBand", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ConfigInstance", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ConfigInstance", Global.System.Data.DataRowVersion.Current, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As CodingReportDataSet1.DOCodingReportTextDataTable, ByVal ReportBand As String, ByVal ConfigInstance As Global.System.Nullable(Of Integer)) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            If (ReportBand Is Nothing) Then
                Me.Adapter.SelectCommand.Parameters(0).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.SelectCommand.Parameters(0).Value = CType(ReportBand,String)
            End If
            If (ConfigInstance.HasValue = true) Then
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(ConfigInstance.Value,Integer)
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = Global.System.DBNull.Value
            End If
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ReportBand As String, ByVal ConfigInstance As Global.System.Nullable(Of Integer)) As CodingReportDataSet1.DOCodingReportTextDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            If (ReportBand Is Nothing) Then
                Me.Adapter.SelectCommand.Parameters(0).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.SelectCommand.Parameters(0).Value = CType(ReportBand,String)
            End If
            If (ConfigInstance.HasValue = true) Then
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(ConfigInstance.Value,Integer)
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = Global.System.DBNull.Value
            End If
            Dim dataTable As CodingReportDataSet1.DOCodingReportTextDataTable = New CodingReportDataSet1.DOCodingReportTextDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataTable As CodingReportDataSet1.DOCodingReportTextDataTable) As Integer
            Return Me.Adapter.Update(dataTable)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataSet As CodingReportDataSet1) As Integer
            Return Me.Adapter.Update(dataSet, "DOCodingReportText")
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRow As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(New Global.System.Data.DataRow() {dataRow})
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRows() As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(dataRows)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Insert, true)>  _
        Public Overloads Overridable Function Insert(ByVal LineText As String) As Integer
            If (LineText Is Nothing) Then
                Throw New Global.System.ArgumentNullException("LineText")
            Else
                Me.Adapter.InsertCommand.Parameters(0).Value = CType(LineText,String)
            End If
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.InsertCommand.Connection.State
            If ((Me.Adapter.InsertCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.InsertCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.InsertCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.InsertCommand.Connection.Close
                End If
            End Try
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class DOChartInfoTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.OleDb.OleDbDataAdapter
        
        Private _connection As Global.System.Data.OleDb.OleDbConnection
        
        Private _commandCollection() As Global.System.Data.OleDb.OleDbCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.OleDb.OleDbDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Property Connection() As Global.System.Data.OleDb.OleDbConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.OleDb.OleDbCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.OleDb.OleDbCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.OleDb.OleDbDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "DOChartInfo"
            tableMapping.ColumnMappings.Add("VisitID", "VisitID")
            tableMapping.ColumnMappings.Add("TreatmentArea", "TreatmentArea")
            tableMapping.ColumnMappings.Add("Facility", "Facility")
            tableMapping.ColumnMappings.Add("Chart", "Chart")
            tableMapping.ColumnMappings.Add("ChartVersion", "ChartVersion")
            tableMapping.ColumnMappings.Add("LongName", "LongName")
            tableMapping.ColumnMappings.Add("EMLevel", "EMLevel")
            tableMapping.ColumnMappings.Add("tmpTxArea", "tmpTxArea")
            tableMapping.ColumnMappings.Add("ConfigInstanceVersion", "ConfigInstanceVersion")
            tableMapping.ColumnMappings.Add("PatientFirstName", "PatientFirstName")
            tableMapping.ColumnMappings.Add("PatientMiddleName", "PatientMiddleName")
            tableMapping.ColumnMappings.Add("PatientLastName", "PatientLastName")
            tableMapping.ColumnMappings.Add("EMLevelHCPCS", "EMLevelHCPCS")
            tableMapping.ColumnMappings.Add("EMLevelCDM", "EMLevelCDM")
            tableMapping.ColumnMappings.Add("ChartStatus", "ChartStatus")
            tableMapping.ColumnMappings.Add("PatientSuffix", "PatientSuffix")
            tableMapping.ColumnMappings.Add("UserName", "UserName")
            tableMapping.ColumnMappings.Add("PhysChartStatus", "PhysChartStatus")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("ObsChartStatus", "ObsChartStatus")
            tableMapping.ColumnMappings.Add("ChartType", "ChartType")
            tableMapping.ColumnMappings.Add("UsesPoints", "UsesPoints")
            tableMapping.ColumnMappings.Add("DateOfService", "DateOfService1")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.OleDb.OleDbConnection()
            Me._connection.ConnectionString = Global.CodingReport.My.MySettings.Default.ASAOLE
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.OleDb.OleDbCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.OleDb.OleDbCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT     a.VisitID, a.Facility, a.ChartType, a.UsesPoints, d.OID AS Chart, d.Ve"& _ 
                "rsion AS ChartVersion, b.TreatmentArea, b.EMLevel, c.LongName, d.TreatmentArea A"& _ 
                "S tmpTxArea, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                      d.ConfigInstanceVersion, d.PatientFirstName"& _ 
                ", d.PatientMiddleName, d.PatientLastName, d.PatientSuffix, b.EMLevelHCPCS, b.EML"& _ 
                "evelCDM, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                      b.ChartStatus, e.UserName, b.PhysChartStatus, d"& _ 
                ".CreationDate, d.DateOfService, b.ObsChartStatus"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM         DBA.DOChartInfo a"& _ 
                ", DBA.DOFacility c, DBA.DOChart d, DBA.DOChartSummary b, DBA.DOUser e"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE    "& _ 
                " a.Facility = c.OID AND a.OID = d.ChartInfo AND d.OID = b.Chart AND d.""User"" = e"& _ 
                ".OID AND (a.GCRecord IS NULL) AND (d.OID = ?)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("OID", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Current, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As CodingReportDataSet1.DOChartInfoDataTable, ByVal OID As Integer) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(OID,Integer)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal OID As Integer) As CodingReportDataSet1.DOChartInfoDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(OID,Integer)
            Dim dataTable As CodingReportDataSet1.DOChartInfoDataTable = New CodingReportDataSet1.DOChartInfoDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class DOCodingReportRecordTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.OleDb.OleDbDataAdapter
        
        Private _connection As Global.System.Data.OleDb.OleDbConnection
        
        Private _commandCollection() As Global.System.Data.OleDb.OleDbCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.OleDb.OleDbDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Friend Property Connection() As Global.System.Data.OleDb.OleDbConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.OleDb.OleDbCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.OleDb.OleDbCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.OleDb.OleDbDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "DOCodingReportRecord"
            tableMapping.ColumnMappings.Add("GCRecord", "GCRecord")
            tableMapping.ColumnMappings.Add("OID", "OID")
            tableMapping.ColumnMappings.Add("Chart", "Chart")
            tableMapping.ColumnMappings.Add("ReportDisplayOrder", "ReportDisplayOrder")
            tableMapping.ColumnMappings.Add("CDM", "CDM")
            tableMapping.ColumnMappings.Add("HCPCS", "HCPCS")
            tableMapping.ColumnMappings.Add("ICD9", "ICD9")
            tableMapping.ColumnMappings.Add("NewProperty", "NewProperty")
            tableMapping.ColumnMappings.Add("Modifier", "Modifier")
            tableMapping.ColumnMappings.Add("InsertDate", "InsertDate")
            tableMapping.ColumnMappings.Add("OptimisticLockField", "OptimisticLockField")
            tableMapping.ColumnMappings.Add("Detail", "Detail")
            tableMapping.ColumnMappings.Add("Points", "Points")
            tableMapping.ColumnMappings.Add("Quantity", "Quantity")
            tableMapping.ColumnMappings.Add("IsPhysicianTypeCode", "IsPhysicianTypeCode")
            tableMapping.ColumnMappings.Add("CodeType", "CodeType")
            Me._adapter.TableMappings.Add(tableMapping)
            Me._adapter.DeleteCommand = New Global.System.Data.OleDb.OleDbCommand()
            Me._adapter.DeleteCommand.Connection = Me.Connection
            Me._adapter.DeleteCommand.CommandText = "DELETE FROM ""DBA"".""DOCodingReportRecord"" WHERE (((? = 1 AND ""GCRecord"" IS NULL) O"& _ 
                "R (""GCRecord"" = ?)) AND (""OID"" = ?) AND ((? = 1 AND ""Chart"" IS NULL) OR (""Chart"""& _ 
                " = ?)) AND ((? = 1 AND ""ReportDisplayOrder"" IS NULL) OR (""ReportDisplayOrder"" = "& _ 
                "?)) AND ((? = 1 AND ""CDM"" IS NULL) OR (""CDM"" = ?)) AND ((? = 1 AND ""HCPCS"" IS NU"& _ 
                "LL) OR (""HCPCS"" = ?)) AND ((? = 1 AND ""ICD9"" IS NULL) OR (""ICD9"" = ?)) AND ((? ="& _ 
                " 1 AND ""NewProperty"" IS NULL) OR (""NewProperty"" = ?)) AND ((? = 1 AND ""Modifier"""& _ 
                " IS NULL) OR (""Modifier"" = ?)) AND ((? = 1 AND ""InsertDate"" IS NULL) OR (""Insert"& _ 
                "Date"" = ?)) AND ((? = 1 AND ""OptimisticLockField"" IS NULL) OR (""OptimisticLockFi"& _ 
                "eld"" = ?)) AND ((? = 1 AND ""Detail"" IS NULL) OR (""Detail"" = ?)) AND ((? = 1 AND "& _ 
                """Points"" IS NULL) OR (""Points"" = ?)) AND ((? = 1 AND ""Quantity"" IS NULL) OR (""Qu"& _ 
                "antity"" = ?)) AND ((? = 1 AND ""IsPhysicianTypeCode"" IS NULL) OR (""IsPhysicianTyp"& _ 
                "eCode"" = ?)) AND ((? = 1 AND ""CodeType"" IS NULL) OR (""CodeType"" = ?)))"
            Me._adapter.DeleteCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_OID", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OID", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_CDM", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_CDM", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_HCPCS", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_HCPCS", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_ICD9", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_ICD9", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Modifier", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Modifier", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_InsertDate", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_InsertDate", Global.System.Data.OleDb.OleDbType.DBTimeStamp, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Detail", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Detail", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Boolean], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_CodeType", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_CodeType", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.InsertCommand = New Global.System.Data.OleDb.OleDbCommand()
            Me._adapter.InsertCommand.Connection = Me.Connection
            Me._adapter.InsertCommand.CommandText = "INSERT INTO ""DBA"".""DOCodingReportRecord"" (""GCRecord"", ""Chart"", ""ReportDisplayOrde"& _ 
                "r"", ""CDM"", ""HCPCS"", ""ICD9"", ""NewProperty"", ""Modifier"", ""InsertDate"", ""Optimistic"& _ 
                "LockField"", ""Detail"", ""Points"", ""Quantity"", ""IsPhysicianTypeCode"", ""CodeType"") V"& _ 
                "ALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            Me._adapter.InsertCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("CDM", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("HCPCS", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ICD9", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Modifier", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("InsertDate", Global.System.Data.OleDb.OleDbType.DBTimeStamp, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Detail", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Boolean], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("CodeType", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand = New Global.System.Data.OleDb.OleDbCommand()
            Me._adapter.UpdateCommand.Connection = Me.Connection
            Me._adapter.UpdateCommand.CommandText = "UPDATE ""DBA"".""DOCodingReportRecord"" SET ""GCRecord"" = ?, ""Chart"" = ?, ""ReportDispl"& _ 
                "ayOrder"" = ?, ""CDM"" = ?, ""HCPCS"" = ?, ""ICD9"" = ?, ""NewProperty"" = ?, ""Modifier"" "& _ 
                "= ?, ""InsertDate"" = ?, ""OptimisticLockField"" = ?, ""Detail"" = ?, ""Points"" = ?, ""Q"& _ 
                "uantity"" = ?, ""IsPhysicianTypeCode"" = ?, ""CodeType"" = ? WHERE (((? = 1 AND ""GCRe"& _ 
                "cord"" IS NULL) OR (""GCRecord"" = ?)) AND (""OID"" = ?) AND ((? = 1 AND ""Chart"" IS N"& _ 
                "ULL) OR (""Chart"" = ?)) AND ((? = 1 AND ""ReportDisplayOrder"" IS NULL) OR (""Report"& _ 
                "DisplayOrder"" = ?)) AND ((? = 1 AND ""CDM"" IS NULL) OR (""CDM"" = ?)) AND ((? = 1 A"& _ 
                "ND ""HCPCS"" IS NULL) OR (""HCPCS"" = ?)) AND ((? = 1 AND ""ICD9"" IS NULL) OR (""ICD9"""& _ 
                " = ?)) AND ((? = 1 AND ""NewProperty"" IS NULL) OR (""NewProperty"" = ?)) AND ((? = "& _ 
                "1 AND ""Modifier"" IS NULL) OR (""Modifier"" = ?)) AND ((? = 1 AND ""InsertDate"" IS N"& _ 
                "ULL) OR (""InsertDate"" = ?)) AND ((? = 1 AND ""OptimisticLockField"" IS NULL) OR ("""& _ 
                "OptimisticLockField"" = ?)) AND ((? = 1 AND ""Detail"" IS NULL) OR (""Detail"" = ?)) "& _ 
                "AND ((? = 1 AND ""Points"" IS NULL) OR (""Points"" = ?)) AND ((? = 1 AND ""Quantity"" "& _ 
                "IS NULL) OR (""Quantity"" = ?)) AND ((? = 1 AND ""IsPhysicianTypeCode"" IS NULL) OR "& _ 
                "(""IsPhysicianTypeCode"" = ?)) AND ((? = 1 AND ""CodeType"" IS NULL) OR (""CodeType"" "& _ 
                "= ?)))"
            Me._adapter.UpdateCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("CDM", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("HCPCS", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("ICD9", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Modifier", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("InsertDate", Global.System.Data.OleDb.OleDbType.DBTimeStamp, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Detail", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Boolean], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("CodeType", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Current, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_GCRecord", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "GCRecord", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_OID", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OID", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_ReportDisplayOrder", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ReportDisplayOrder", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_CDM", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_CDM", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CDM", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_HCPCS", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_HCPCS", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "HCPCS", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_ICD9", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_ICD9", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "ICD9", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_NewProperty", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "NewProperty", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Modifier", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Modifier", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Modifier", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_InsertDate", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_InsertDate", Global.System.Data.OleDb.OleDbType.DBTimeStamp, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "InsertDate", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_OptimisticLockField", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "OptimisticLockField", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Detail", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Detail", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Detail", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Points", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Points", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_Quantity", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Quantity", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_IsPhysicianTypeCode", Global.System.Data.OleDb.OleDbType.[Boolean], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "IsPhysicianTypeCode", Global.System.Data.DataRowVersion.Original, false, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("IsNull_CodeType", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Original, true, Nothing))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Original_CodeType", Global.System.Data.OleDb.OleDbType.VarChar, 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "CodeType", Global.System.Data.DataRowVersion.Original, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.OleDb.OleDbConnection()
            Me._connection.ConnectionString = Global.CodingReport.My.MySettings.Default.ASAOLE
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.OleDb.OleDbCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.OleDb.OleDbCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT     GCRecord, OID, Chart, ReportDisplayOrder, CDM, HCPCS, ICD9, NewPropert"& _ 
                "y, Modifier, InsertDate, OptimisticLockField, Detail, Points, Quantity, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"      "& _ 
                "                IsPhysicianTypeCode, CodeType"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM         DBA.DOCodingReportRe"& _ 
                "cord"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE     (Chart = ?)"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"ORDER BY ReportDisplayOrder"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Chart", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Chart", Global.System.Data.DataRowVersion.Current, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As CodingReportDataSet1.DOCodingReportRecordDataTable, ByVal Chart As Global.System.Nullable(Of Integer)) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            If (Chart.HasValue = true) Then
                Me.Adapter.SelectCommand.Parameters(0).Value = CType(Chart.Value,Integer)
            Else
                Me.Adapter.SelectCommand.Parameters(0).Value = Global.System.DBNull.Value
            End If
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal Chart As Global.System.Nullable(Of Integer)) As CodingReportDataSet1.DOCodingReportRecordDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            If (Chart.HasValue = true) Then
                Me.Adapter.SelectCommand.Parameters(0).Value = CType(Chart.Value,Integer)
            Else
                Me.Adapter.SelectCommand.Parameters(0).Value = Global.System.DBNull.Value
            End If
            Dim dataTable As CodingReportDataSet1.DOCodingReportRecordDataTable = New CodingReportDataSet1.DOCodingReportRecordDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataTable As CodingReportDataSet1.DOCodingReportRecordDataTable) As Integer
            Return Me.Adapter.Update(dataTable)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataSet As CodingReportDataSet1) As Integer
            Return Me.Adapter.Update(dataSet, "DOCodingReportRecord")
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRow As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(New Global.System.Data.DataRow() {dataRow})
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRows() As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(dataRows)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Delete, true)>  _
        Public Overloads Overridable Function Delete( _
                    ByVal Original_GCRecord As Global.System.Nullable(Of Integer),  _
                    ByVal Original_OID As Integer,  _
                    ByVal Original_Chart As Global.System.Nullable(Of Integer),  _
                    ByVal Original_ReportDisplayOrder As Global.System.Nullable(Of Integer),  _
                    ByVal Original_CDM As String,  _
                    ByVal Original_HCPCS As String,  _
                    ByVal Original_ICD9 As String,  _
                    ByVal Original_NewProperty As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Modifier As String,  _
                    ByVal Original_InsertDate As Global.System.Nullable(Of Date),  _
                    ByVal Original_OptimisticLockField As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Detail As String,  _
                    ByVal Original_Points As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Quantity As Global.System.Nullable(Of Integer),  _
                    ByVal Original_IsPhysicianTypeCode As Global.System.Nullable(Of Boolean),  _
                    ByVal Original_CodeType As String) As Integer
            If (Original_GCRecord.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(0).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(1).Value = CType(Original_GCRecord.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(0).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(1).Value = Global.System.DBNull.Value
            End If
            Me.Adapter.DeleteCommand.Parameters(2).Value = CType(Original_OID,Integer)
            If (Original_Chart.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(3).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(4).Value = CType(Original_Chart.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(3).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(4).Value = Global.System.DBNull.Value
            End If
            If (Original_ReportDisplayOrder.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(5).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(6).Value = CType(Original_ReportDisplayOrder.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(5).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(6).Value = Global.System.DBNull.Value
            End If
            If (Original_CDM Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(7).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(8).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(7).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(8).Value = CType(Original_CDM,String)
            End If
            If (Original_HCPCS Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(9).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(10).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(9).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(10).Value = CType(Original_HCPCS,String)
            End If
            If (Original_ICD9 Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(11).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(12).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(11).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(12).Value = CType(Original_ICD9,String)
            End If
            If (Original_NewProperty.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(13).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(14).Value = CType(Original_NewProperty.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(13).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(14).Value = Global.System.DBNull.Value
            End If
            If (Original_Modifier Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(15).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(16).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(15).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(16).Value = CType(Original_Modifier,String)
            End If
            If (Original_InsertDate.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(17).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(18).Value = CType(Original_InsertDate.Value,Date)
            Else
                Me.Adapter.DeleteCommand.Parameters(17).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(18).Value = Global.System.DBNull.Value
            End If
            If (Original_OptimisticLockField.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(19).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(20).Value = CType(Original_OptimisticLockField.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(19).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(20).Value = Global.System.DBNull.Value
            End If
            If (Original_Detail Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(21).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(22).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(21).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(22).Value = CType(Original_Detail,String)
            End If
            If (Original_Points.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(23).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(24).Value = CType(Original_Points.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(23).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(24).Value = Global.System.DBNull.Value
            End If
            If (Original_Quantity.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(25).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(26).Value = CType(Original_Quantity.Value,Integer)
            Else
                Me.Adapter.DeleteCommand.Parameters(25).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(26).Value = Global.System.DBNull.Value
            End If
            If (Original_IsPhysicianTypeCode.HasValue = true) Then
                Me.Adapter.DeleteCommand.Parameters(27).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(28).Value = CType(Original_IsPhysicianTypeCode.Value,Boolean)
            Else
                Me.Adapter.DeleteCommand.Parameters(27).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(28).Value = Global.System.DBNull.Value
            End If
            If (Original_CodeType Is Nothing) Then
                Me.Adapter.DeleteCommand.Parameters(29).Value = CType(1,Object)
                Me.Adapter.DeleteCommand.Parameters(30).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.DeleteCommand.Parameters(29).Value = CType(0,Object)
                Me.Adapter.DeleteCommand.Parameters(30).Value = CType(Original_CodeType,String)
            End If
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.DeleteCommand.Connection.State
            If ((Me.Adapter.DeleteCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.DeleteCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.DeleteCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.DeleteCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Insert, true)>  _
        Public Overloads Overridable Function Insert(ByVal GCRecord As Global.System.Nullable(Of Integer), ByVal Chart As Global.System.Nullable(Of Integer), ByVal ReportDisplayOrder As Global.System.Nullable(Of Integer), ByVal CDM As String, ByVal HCPCS As String, ByVal ICD9 As String, ByVal NewProperty As Global.System.Nullable(Of Integer), ByVal Modifier As String, ByVal InsertDate As Global.System.Nullable(Of Date), ByVal OptimisticLockField As Global.System.Nullable(Of Integer), ByVal Detail As String, ByVal Points As Global.System.Nullable(Of Integer), ByVal Quantity As Global.System.Nullable(Of Integer), ByVal IsPhysicianTypeCode As Global.System.Nullable(Of Boolean), ByVal CodeType As String) As Integer
            If (GCRecord.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(0).Value = CType(GCRecord.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(0).Value = Global.System.DBNull.Value
            End If
            If (Chart.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(1).Value = CType(Chart.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(1).Value = Global.System.DBNull.Value
            End If
            If (ReportDisplayOrder.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(2).Value = CType(ReportDisplayOrder.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(2).Value = Global.System.DBNull.Value
            End If
            If (CDM Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(3).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(3).Value = CType(CDM,String)
            End If
            If (HCPCS Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(4).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(4).Value = CType(HCPCS,String)
            End If
            If (ICD9 Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(5).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(5).Value = CType(ICD9,String)
            End If
            If (NewProperty.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(6).Value = CType(NewProperty.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(6).Value = Global.System.DBNull.Value
            End If
            If (Modifier Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(7).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(7).Value = CType(Modifier,String)
            End If
            If (InsertDate.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(8).Value = CType(InsertDate.Value,Date)
            Else
                Me.Adapter.InsertCommand.Parameters(8).Value = Global.System.DBNull.Value
            End If
            If (OptimisticLockField.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(9).Value = CType(OptimisticLockField.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(9).Value = Global.System.DBNull.Value
            End If
            If (Detail Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(10).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(10).Value = CType(Detail,String)
            End If
            If (Points.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(11).Value = CType(Points.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(11).Value = Global.System.DBNull.Value
            End If
            If (Quantity.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(12).Value = CType(Quantity.Value,Integer)
            Else
                Me.Adapter.InsertCommand.Parameters(12).Value = Global.System.DBNull.Value
            End If
            If (IsPhysicianTypeCode.HasValue = true) Then
                Me.Adapter.InsertCommand.Parameters(13).Value = CType(IsPhysicianTypeCode.Value,Boolean)
            Else
                Me.Adapter.InsertCommand.Parameters(13).Value = Global.System.DBNull.Value
            End If
            If (CodeType Is Nothing) Then
                Me.Adapter.InsertCommand.Parameters(14).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.InsertCommand.Parameters(14).Value = CType(CodeType,String)
            End If
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.InsertCommand.Connection.State
            If ((Me.Adapter.InsertCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.InsertCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.InsertCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.InsertCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Update, true)>  _
        Public Overloads Overridable Function Update( _
                    ByVal GCRecord As Global.System.Nullable(Of Integer),  _
                    ByVal Chart As Global.System.Nullable(Of Integer),  _
                    ByVal ReportDisplayOrder As Global.System.Nullable(Of Integer),  _
                    ByVal CDM As String,  _
                    ByVal HCPCS As String,  _
                    ByVal ICD9 As String,  _
                    ByVal NewProperty As Global.System.Nullable(Of Integer),  _
                    ByVal Modifier As String,  _
                    ByVal InsertDate As Global.System.Nullable(Of Date),  _
                    ByVal OptimisticLockField As Global.System.Nullable(Of Integer),  _
                    ByVal Detail As String,  _
                    ByVal Points As Global.System.Nullable(Of Integer),  _
                    ByVal Quantity As Global.System.Nullable(Of Integer),  _
                    ByVal IsPhysicianTypeCode As Global.System.Nullable(Of Boolean),  _
                    ByVal CodeType As String,  _
                    ByVal Original_GCRecord As Global.System.Nullable(Of Integer),  _
                    ByVal Original_OID As Integer,  _
                    ByVal Original_Chart As Global.System.Nullable(Of Integer),  _
                    ByVal Original_ReportDisplayOrder As Global.System.Nullable(Of Integer),  _
                    ByVal Original_CDM As String,  _
                    ByVal Original_HCPCS As String,  _
                    ByVal Original_ICD9 As String,  _
                    ByVal Original_NewProperty As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Modifier As String,  _
                    ByVal Original_InsertDate As Global.System.Nullable(Of Date),  _
                    ByVal Original_OptimisticLockField As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Detail As String,  _
                    ByVal Original_Points As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Quantity As Global.System.Nullable(Of Integer),  _
                    ByVal Original_IsPhysicianTypeCode As Global.System.Nullable(Of Boolean),  _
                    ByVal Original_CodeType As String) As Integer
            If (GCRecord.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(0).Value = CType(GCRecord.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(0).Value = Global.System.DBNull.Value
            End If
            If (Chart.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(1).Value = CType(Chart.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(1).Value = Global.System.DBNull.Value
            End If
            If (ReportDisplayOrder.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(2).Value = CType(ReportDisplayOrder.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(2).Value = Global.System.DBNull.Value
            End If
            If (CDM Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(3).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(3).Value = CType(CDM,String)
            End If
            If (HCPCS Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(4).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(4).Value = CType(HCPCS,String)
            End If
            If (ICD9 Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(5).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(5).Value = CType(ICD9,String)
            End If
            If (NewProperty.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(6).Value = CType(NewProperty.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(6).Value = Global.System.DBNull.Value
            End If
            If (Modifier Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(7).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(7).Value = CType(Modifier,String)
            End If
            If (InsertDate.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(8).Value = CType(InsertDate.Value,Date)
            Else
                Me.Adapter.UpdateCommand.Parameters(8).Value = Global.System.DBNull.Value
            End If
            If (OptimisticLockField.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(9).Value = CType(OptimisticLockField.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(9).Value = Global.System.DBNull.Value
            End If
            If (Detail Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(10).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(10).Value = CType(Detail,String)
            End If
            If (Points.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(11).Value = CType(Points.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(11).Value = Global.System.DBNull.Value
            End If
            If (Quantity.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(12).Value = CType(Quantity.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(12).Value = Global.System.DBNull.Value
            End If
            If (IsPhysicianTypeCode.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(13).Value = CType(IsPhysicianTypeCode.Value,Boolean)
            Else
                Me.Adapter.UpdateCommand.Parameters(13).Value = Global.System.DBNull.Value
            End If
            If (CodeType Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(14).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(14).Value = CType(CodeType,String)
            End If
            If (Original_GCRecord.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(15).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(16).Value = CType(Original_GCRecord.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(15).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(16).Value = Global.System.DBNull.Value
            End If
            Me.Adapter.UpdateCommand.Parameters(17).Value = CType(Original_OID,Integer)
            If (Original_Chart.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(18).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(19).Value = CType(Original_Chart.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(18).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(19).Value = Global.System.DBNull.Value
            End If
            If (Original_ReportDisplayOrder.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(20).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(21).Value = CType(Original_ReportDisplayOrder.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(20).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(21).Value = Global.System.DBNull.Value
            End If
            If (Original_CDM Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(22).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(23).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(22).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(23).Value = CType(Original_CDM,String)
            End If
            If (Original_HCPCS Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(24).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(25).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(24).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(25).Value = CType(Original_HCPCS,String)
            End If
            If (Original_ICD9 Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(26).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(27).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(26).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(27).Value = CType(Original_ICD9,String)
            End If
            If (Original_NewProperty.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(28).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(29).Value = CType(Original_NewProperty.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(28).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(29).Value = Global.System.DBNull.Value
            End If
            If (Original_Modifier Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(30).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(31).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(30).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(31).Value = CType(Original_Modifier,String)
            End If
            If (Original_InsertDate.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(32).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(33).Value = CType(Original_InsertDate.Value,Date)
            Else
                Me.Adapter.UpdateCommand.Parameters(32).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(33).Value = Global.System.DBNull.Value
            End If
            If (Original_OptimisticLockField.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(34).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(35).Value = CType(Original_OptimisticLockField.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(34).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(35).Value = Global.System.DBNull.Value
            End If
            If (Original_Detail Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(36).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(37).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(36).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(37).Value = CType(Original_Detail,String)
            End If
            If (Original_Points.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(38).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(39).Value = CType(Original_Points.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(38).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(39).Value = Global.System.DBNull.Value
            End If
            If (Original_Quantity.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(40).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(41).Value = CType(Original_Quantity.Value,Integer)
            Else
                Me.Adapter.UpdateCommand.Parameters(40).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(41).Value = Global.System.DBNull.Value
            End If
            If (Original_IsPhysicianTypeCode.HasValue = true) Then
                Me.Adapter.UpdateCommand.Parameters(42).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(43).Value = CType(Original_IsPhysicianTypeCode.Value,Boolean)
            Else
                Me.Adapter.UpdateCommand.Parameters(42).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(43).Value = Global.System.DBNull.Value
            End If
            If (Original_CodeType Is Nothing) Then
                Me.Adapter.UpdateCommand.Parameters(44).Value = CType(1,Object)
                Me.Adapter.UpdateCommand.Parameters(45).Value = Global.System.DBNull.Value
            Else
                Me.Adapter.UpdateCommand.Parameters(44).Value = CType(0,Object)
                Me.Adapter.UpdateCommand.Parameters(45).Value = CType(Original_CodeType,String)
            End If
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.UpdateCommand.Connection.State
            If ((Me.Adapter.UpdateCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.UpdateCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.UpdateCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.UpdateCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Update, true)>  _
        Public Overloads Overridable Function Update( _
                    ByVal GCRecord As Global.System.Nullable(Of Integer),  _
                    ByVal ReportDisplayOrder As Global.System.Nullable(Of Integer),  _
                    ByVal CDM As String,  _
                    ByVal HCPCS As String,  _
                    ByVal ICD9 As String,  _
                    ByVal NewProperty As Global.System.Nullable(Of Integer),  _
                    ByVal Modifier As String,  _
                    ByVal InsertDate As Global.System.Nullable(Of Date),  _
                    ByVal OptimisticLockField As Global.System.Nullable(Of Integer),  _
                    ByVal Detail As String,  _
                    ByVal Points As Global.System.Nullable(Of Integer),  _
                    ByVal Quantity As Global.System.Nullable(Of Integer),  _
                    ByVal IsPhysicianTypeCode As Global.System.Nullable(Of Boolean),  _
                    ByVal CodeType As String,  _
                    ByVal Original_GCRecord As Global.System.Nullable(Of Integer),  _
                    ByVal Original_OID As Integer,  _
                    ByVal Original_Chart As Global.System.Nullable(Of Integer),  _
                    ByVal Original_ReportDisplayOrder As Global.System.Nullable(Of Integer),  _
                    ByVal Original_CDM As String,  _
                    ByVal Original_HCPCS As String,  _
                    ByVal Original_ICD9 As String,  _
                    ByVal Original_NewProperty As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Modifier As String,  _
                    ByVal Original_InsertDate As Global.System.Nullable(Of Date),  _
                    ByVal Original_OptimisticLockField As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Detail As String,  _
                    ByVal Original_Points As Global.System.Nullable(Of Integer),  _
                    ByVal Original_Quantity As Global.System.Nullable(Of Integer),  _
                    ByVal Original_IsPhysicianTypeCode As Global.System.Nullable(Of Boolean),  _
                    ByVal Original_CodeType As String) As Integer
            Return Me.Update(GCRecord, Original_Chart, ReportDisplayOrder, CDM, HCPCS, ICD9, NewProperty, Modifier, InsertDate, OptimisticLockField, Detail, Points, Quantity, IsPhysicianTypeCode, CodeType, Original_GCRecord, Original_OID, Original_Chart, Original_ReportDisplayOrder, Original_CDM, Original_HCPCS, Original_ICD9, Original_NewProperty, Original_Modifier, Original_InsertDate, Original_OptimisticLockField, Original_Detail, Original_Points, Original_Quantity, Original_IsPhysicianTypeCode, Original_CodeType)
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class QueriesTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private _commandCollection() As Global.System.Data.IDbCommand
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.IDbCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.IDbCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.OleDb.OleDbCommand()
            CType(Me._commandCollection(0),Global.System.Data.OleDb.OleDbCommand).Connection = New Global.System.Data.OleDb.OleDbConnection(Global.CodingReport.My.MySettings.Default.ASAOLE)
            CType(Me._commandCollection(0),Global.System.Data.OleDb.OleDbCommand).CommandText = "SELECT        RuleSettingValue"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            DBA.FacilitySettings"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE      "& _ 
                "  (Facility = ?) AND (RuleSetting = 'CodingReportDetailFontSize')"
            CType(Me._commandCollection(0),Global.System.Data.OleDb.OleDbCommand).CommandType = Global.System.Data.CommandType.Text
            CType(Me._commandCollection(0),Global.System.Data.OleDb.OleDbCommand).Parameters.Add(New Global.System.Data.OleDb.OleDbParameter("Facility", Global.System.Data.OleDb.OleDbType.[Integer], 0, Global.System.Data.ParameterDirection.Input, CType(0,Byte), CType(0,Byte), "Facility", Global.System.Data.DataRowVersion.Current, false, Nothing))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function FacilityFontSize(ByVal Facility As Integer) As Object
            Dim command As Global.System.Data.OleDb.OleDbCommand = CType(Me.CommandCollection(0),Global.System.Data.OleDb.OleDbCommand)
            command.Parameters(0).Value = CType(Facility,Integer)
            Dim previousConnectionState As Global.System.Data.ConnectionState = command.Connection.State
            If ((command.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                command.Connection.Open
            End If
            Dim returnValue As Object
            Try 
                returnValue = command.ExecuteScalar
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    command.Connection.Close
                End If
            End Try
            If ((returnValue Is Nothing)  _
                        OrElse (returnValue.GetType Is GetType(Global.System.DBNull))) Then
                Return Nothing
            Else
                Return CType(returnValue,Object)
            End If
        End Function
    End Class
End Namespace

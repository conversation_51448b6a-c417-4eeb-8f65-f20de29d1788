﻿Imports System
Imports McKesson.HIC.ColdFeed
Imports EnchartDOLib
Imports System.Threading
Imports System.IO

Public Class MainForm

    Private contextMenu1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents menuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents menuItem2 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents notifyIcon1 As System.Windows.Forms.NotifyIcon

    '---------------------------------------
    Dim iExportImageFile As IExportChargeSummaryImageFile
    Dim iExportDetailsFile As IExportChargeSummaryDetailsFile
    Dim iGetChartsToExport As IGetChartsToExport '= New DefaultGetChartsToExport
    Dim ilog As ICustomLog
    Dim iShowMessage As IShowMessage

    Const CHARTS_PER_BATCH = 10

    Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.


        Me.components = New System.ComponentModel.Container
        Me.contextMenu1 = New System.Windows.Forms.ContextMenuStrip
        Me.menuItem1 = New System.Windows.Forms.ToolStripMenuItem
        Me.menuItem2 = New System.Windows.Forms.ToolStripMenuItem

        ' Initialize contextMenu1
        Me.contextMenu1.Items.AddRange(New System.Windows.Forms.ToolStripMenuItem() _
                            {Me.menuItem1, Me.menuItem2})

        ' Initialize menuItem1
        Me.menuItem1.MergeIndex = 1 '.Index = 1
        Me.menuItem1.Text = "E&xit"


        ' Initialize menuItem1
        Me.menuItem2.MergeIndex = 0 'Index = 0
        Me.menuItem2.Text = "Open"

        ' Create the NotifyIcon.
        Me.notifyIcon1 = New System.Windows.Forms.NotifyIcon(Me.components)

        ' The Icon property sets the icon that will appear
        ' in the systray for this application.
        notifyIcon1.Icon = My.Resources.snowman


        ' The ContextMenu property sets the menu that will
        ' appear when the systray icon is right clicked.
        notifyIcon1.ContextMenuStrip = contextMenu1 'ContextMenu = Me.contextMenu1

        ' The Text property sets the text that will be displayed,
        ' in a tooltip, when the mouse hovers over the systray icon.
        notifyIcon1.Text = "AIC Cold Feed Export Running ..."
        notifyIcon1.Visible = True

    End Sub

    Private _logger As Logging.Log
    Sub InitDefaultLog()
        Dim logPath = Path.Combine(My.Application.Info.DirectoryPath, "CFELogs")
        My.Application.Log.DefaultFileLogWriter.CustomLocation = logPath
        If Not Directory.Exists(logPath) Then
            Directory.CreateDirectory(logPath)
        End If

        My.Application.Log.DefaultFileLogWriter.Location = Logging.LogFileLocation.Custom
        My.Application.Log.DefaultFileLogWriter.CustomLocation = logPath
        My.Application.Log.DefaultFileLogWriter.BaseFileName = "CFE_" & Strings.Format(Now, "MM-dd-yy_hh.mm")
        My.Application.Log.DefaultFileLogWriter.AutoFlush = True
        My.Application.Log.DefaultFileLogWriter.Append = True
        _logger = My.Application.Log
        ilog = New McKesson.HIC.ColdFeed.DefaultLog(_logger)
    End Sub

    Sub LogMessage(ByVal msg As String)

        If ilog IsNot Nothing Then
            ilog.LogMessage(msg)
        Else
            msg = String.Format("{0}: {1}", Now, msg)
            My.Application.Log.WriteEntry(msg)
        End If
    End Sub


    Private Sub notifyIcon1_DoubleClick(ByVal Sender As Object, ByVal e As EventArgs) Handles notifyIcon1.DoubleClick
        ' Show the form when the user double clicks on the notify icon.

        ' Set the WindowState to normal if the form is minimized.
        If (Me.WindowState = FormWindowState.Minimized) Then
            Me.WindowState = FormWindowState.Normal
            Me.Visible = True
        End If


        ' Activate the form.
        Me.Activate()
    End Sub

    Private Sub menuItem1_Click(ByVal Sender As Object, ByVal e As EventArgs) Handles menuItem1.Click
        ' Close the form, which closes the application.
        TimeToExit = True
        Me.Close()
    End Sub

    Private Sub menuItem12_Click(ByVal Sender As Object, ByVal e As EventArgs) Handles menuItem2.Click
        ' Close the form, which closes the application.

        Me.WindowState = FormWindowState.Normal
        Me.Visible = True
    End Sub

    Protected TimeToExit As Boolean = False
    Private Sub MainForm_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If TimeToExit Then
            Exit Sub
        End If

        Try
            If (Not Me.WindowState = FormWindowState.Minimized) AndAlso Me.Visible Then

                
                e.Cancel = True
                Me.Visible = False
                Me.WindowState = FormWindowState.Minimized
                Return
                'End If
            End If

            notifyIcon1.Visible = False
            notifyIcon1.Dispose()

        Catch
            'just look innocent
            'Application.Exit()
        End Try

    End Sub

    ' Dim WithEvents TestWorker As New System.ComponentModel.BackgroundWorker
    Dim doSomethingThread As Thread
    Private Sub Form1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
       
        InitDefaultLog()
        Timer1.Interval = 1000
        Timer1.Start()

    End Sub

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <remarks>
    ''' This DoStartup runs on the main thread...
    ''' </remarks>
    Sub DoStartup()
        'This is ugly.... must need some sleep!
        Try
            ShowMessageAndLogNonThreaded("*****************************************************************************")
            ShowMessageAndLogNonThreaded(String.Format("ColdFeedExport.exe Ver. {0} Starting up ...", FileVersionInfo.GetVersionInfo(System.Reflection.Assembly.GetExecutingAssembly().Location).FileVersion))
            ShowMessageAndLogNonThreaded("Connecting to db...")
            ConnectToECDataBase() 'Init DevExpres datalayer from enchartcac.exe.config file connectionString
            ShowMessageAndLogNonThreaded("Connected...")

        Catch ex As Exception
            Dim msg = String.Format("Error occured while trying to connect to database: {0}", ex.Message)
            ShowMessageAndLogNonThreaded(msg)
            ShowMessageAndLogNonThreaded("Shutting down...")
            'My.Application.Log.WriteEntry(msg)

            Dim sw As New Stopwatch
            sw.Start()
            Me.WindowState = FormWindowState.Normal
            Me.Visible = True
            Me.Refresh()
            While sw.ElapsedMilliseconds < 10000
                Application.DoEvents()
            End While
           
        End Try

        doSomethingThread = New Thread(AddressOf dosomething)
        doSomethingThread.Start()

        'Task.Factory.StartNew(Sub() dosomething())

        WaitForWorkerThreadToEndTimer.Enabled = True
        WaitForWorkerThreadToEndTimer.Interval = 1
        WaitForWorkerThreadToEndTimer.Start()

    End Sub

    Private Sub ForceExit()
        TimeToExit = True
        Application.Exit()
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        Timer1.Stop()
        DoStartup()
    End Sub

    Public Delegate Sub ShowMessageDelegate(ByVal msg As String)

    Sub ShowMessage(ByVal msg As String)

        Dim params() As Object = {msg}
        BeginInvoke(New ShowMessageDelegate(AddressOf AddMsg), params)

    End Sub


    Sub ShowMessageAndLogNonThreaded(ByVal msg As String)

        msg = String.Format("{0}: {1}", Now, msg)
        AddMsg(msg)
        'My.Application.Log.WriteEntry(msg)
        LogMessage(msg)
    End Sub

    Private Sub AddMsg(ByVal msg As String)
        Me.ListBoxControl1.Items.Add(msg)
        Me.ListBoxControl1.Refresh()
        Application.DoEvents()
    End Sub


    Private Sub DoDelayedShutDown()
        'DoDelayedShutDown
        ShowMessage("Shutting Down...")
        DelayedShutDownTimer.Enabled = True
        DelayedShutDownTimer.Interval = 10000
        DelayedShutDownTimer.Start()

    End Sub
    Sub dosomething()
        LogMessage("Processing Cold Feed Export...")
        Try
            Dim facilityList As List(Of DOFacility) = GetFacilityList(My.Settings.FacilityID)
            Dim defaultOptionsDict = LoadDefaultOptionsFromConfigFile()

            InitAuditLogger()

            If facilityList.Count > 0 Then

                For Each facility In facilityList
                    ShowMessage(String.Format("Preparing to process Facility({0}) {1}", facility.Oid, facility.FacilityID))
                    LogMessage(String.Format("Preparing to process Facility({0}) {1} *******************************************",
                                             facility.Oid, facility.FacilityID))

                    Dim options = BuildFacilitySpecificOptions(defaultOptionsDict, facility.GetColdFeedOptionsAsDict)

                    Dim ColdFeedHelperEngine As ColdFeedHelper = InitColdFeedHelperEngine(options)

                    ColdFeedHelperEngine.OptionsDict = options

                    Dim TriggerMethod As String = options("TriggerMethod") 'My.Settings.TriggerMethod
                    Dim chartWrapperDict As ChartWrapperDictionary = iGetChartsToExport.GetCharts(facility, TriggerMethod)
                    Try
                        LogMessage(String.Format("Output Configuration path= {0}", options("ImageOutputPath")))
                        ColdFeedHelperEngine.ExportOneAtTime(facility, chartWrapperDict)
                    Catch ex As Exception
                        LogMessage(String.Format("An unexpected error occurred: {0}", ex.Message))
                    End Try
                Next
            Else

                ilog = New McKesson.HIC.ColdFeed.DefaultLog(_logger)
            End If

            LogMessage(String.Format("ColdFeedExport.exe Ver. {0} ran successfully, Shutting down ...", FileVersionInfo.GetVersionInfo(System.Reflection.Assembly.GetExecutingAssembly().Location).FileVersion))
            LogMessage("*****************************************************************************")

        Catch ex As Exception
            Try
                LogMessage(ex.Message)
            Catch ex2 As Exception
                'blah
            End Try
            Application.Exit()
        End Try
    End Sub


    Private Function InitColdFeedHelperEngine(ByVal options As System.Collections.Generic.Dictionary(Of String, String)) As ColdFeedHelper
        Select Case options("ImageFeedMode").ToUpper
            Case "ONECONTENT"
                iExportImageFile = New McKesson.HIC.ColdFeed.HPFColdFeedImageFileHandler
                iExportDetailsFile = New McKesson.HIC.ColdFeed.HPFColdFeedDetailsFileHandler
            Case "HYLAND"
                iExportImageFile = New McKesson.HIC.ColdFeed.HPFColdFeedImageFileHandler
                iExportDetailsFile = New McKesson.HIC.ColdFeed.SingleDelimitedFileHandler
            Case "MEDITECH"
                iExportImageFile = New McKesson.HIC.ColdFeed.MeditechColdFeedImageFileHandler
                iExportDetailsFile = New McKesson.HIC.ColdFeed.HPFColdFeedDetailsFakeFileHandler
            Case "CUSTOMFILENAME"
                iExportImageFile = New McKesson.HIC.ColdFeed.CustomFilenameImageFileHandler
                iExportDetailsFile = New McKesson.HIC.ColdFeed.HPFColdFeedDetailsFakeFileHandler
            Case Else
                Throw New Exception("Invalid ImageFeedMode Setting, valid values are: OneContent, Hyland, Meditech, CustomFilename")
        End Select

        iShowMessage = New McKesson.HIC.ColdFeed.DefaultShowMessage
        iShowMessage.SetShowMessageCallback(AddressOf ShowMessage)

        ilog = New McKesson.HIC.ColdFeed.DefaultLog(_logger)

        iGetChartsToExport = New McKesson.HIC.ColdFeed.DefaulGetChartsToExport

        'Perhaps we shoudl also pass and Options Object that can customize behaver of the CFEngine
        'These options could be stored in config file, or db to allow for facility specific configurations...

        Dim ColdFeedHelperEngine As New ColdFeedHelper(iExportImageFile,
                                                                 iExportDetailsFile,
                                                                 iGetChartsToExport,
                                                                 ilog,
                                                                 iShowMessage,
                                                                 options("ImageOutputPath"),
                                                                 options("DetailsOutputPath"))

        Return ColdFeedHelperEngine
    End Function
    Private Function GetFacilityList(ByVal facilitiesOption As String) As List(Of DOFacility)
        Dim facilityList As List(Of DOFacility)
        Dim passedInFacilityID As Integer = -1
        Dim runSingleFacilityID As Integer

        Integer.TryParse(My.Settings.FacilityID, runSingleFacilityID)
        If My.Application.CommandLineArgs.Count > 0 Then
            passedInFacilityID = My.Application.CommandLineArgs(0)
            runSingleFacilityID = passedInFacilityID
        End If

        If facilitiesOption.ToUpper = "ALL" AndAlso passedInFacilityID < 0 Then
            facilityList = DOFacility.GetAllFacilitesAsList()
        Else
            facilityList = New List(Of DOFacility)
            facilityList.Add(GetFacility(runSingleFacilityID))
        End If

        If facilitiesOption = "All" AndAlso passedInFacilityID > 0 Then
            LogMessage(String.Format("Command Line Facility Override detected. Passed in FacilityOID({0}) will be used instead of 'All'", passedInFacilityID))

        End If

        Dim coldFeedEnabledFacilities As New List(Of DOFacility)
        For Each facility In facilityList
            If facility.IsConfigSettingEnabled("EnableColdFeed") Then
                coldFeedEnabledFacilities.Add(facility)
            Else
                ShowMessage(String.Format("Facility({0}) {1} does not have the 'EnableColdFeed'  option ( in DOConfigSetting) enabled. Skipping...", facility.Oid, facility.FacilityID))
            End If
        Next

        Return coldFeedEnabledFacilities
    End Function

    Private Shared Function LoadDefaultOptionsFromConfigFile() As Dictionary(Of String, String)
        Dim optionsDictionary As New Dictionary(Of String, String)

        optionsDictionary("BatchLabel") = My.Settings.BatchLabel
        optionsDictionary("TriggerMethod") = My.Settings.TriggerMethod
        optionsDictionary("ImageOutputPath") = My.Settings.ImageOutputPath
        optionsDictionary("DetailsOutputPath") = My.Settings.DetailsOutputPath
        optionsDictionary("ImageFileType") = My.Settings.ImageFileType
        optionsDictionary("StartDate") = My.Settings.StartDate
        optionsDictionary("MultiExport") = My.Settings.MultiExport
        optionsDictionary("DateFormat") = My.Settings.DateFormat
        optionsDictionary("UseFullPathPData") = My.Settings.UseFullPathPData ' Added 2012/09/04
        optionsDictionary("ImageFeedMode") = My.Settings.ImageFeedMode

        Return optionsDictionary
    End Function

    Private Shared Function BuildFacilitySpecificOptions(ByVal defaultDict As Dictionary(Of String, String), _
                                                  ByVal facDic As Dictionary(Of String, String)) _
                                                  As Dictionary(Of String, String)

        Dim MergedDictionary As New Dictionary(Of String, String)
        For Each settingName In defaultDict.Keys
            MergedDictionary(settingName) = defaultDict(settingName)
        Next

        For Each settingName In facDic.Keys
            MergedDictionary(settingName) = facDic(settingName)
        Next

        Return MergedDictionary
    End Function

    Private Sub Timer2_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DelayedShutDownTimer.Tick
        DelayedShutDownTimer.Stop()
        Me.ForceExit()
    End Sub

    Private Sub Timer3_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WaitForWorkerThreadToEndTimer.Tick
        If Me.doSomethingThread IsNot Nothing Then
            If Not Me.doSomethingThread.IsAlive Then
                WaitForWorkerThreadToEndTimer.Stop()
                Me.DoDelayedShutDown()
            End If
        End If
    End Sub

    Protected Friend Sub InitAuditLogger()

        AuditLogger.Init(New AltBasicAuditLogHelper("Image Feed"), New DefaultAuditLogWriter)

    End Sub
End Class

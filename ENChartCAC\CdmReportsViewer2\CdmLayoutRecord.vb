Imports System
Imports DevExpress.Xpo


Public Class CdmLayoutRecord
    Inherits XPObject

    'overriding this will cause the dropdown to display the Name when it contains an object of this type
    Public Overrides Function ToString() As String
        Return Name
    End Function

    Public Sub New()
        MyBase.New()
        ' This constructor is used when an object is loaded from a persistent storage.
        ' Do not place any code here.			
    End Sub

    Public Sub New(ByVal session As Session)
        MyBase.New(session)
        ' This constructor is used when an object is loaded from a persistent storage.
        ' Do not place any code here.			
    End Sub

    Public Overrides Sub AfterConstruction()
        MyBase.AfterConstruction()
        ' Use this constructor when you want to create a new object.
        ' Place here your initialization code.
    End Sub


    Private _name As String
    <Size(40)> _
    Public Property Name() As String
        Get
            Return _name
        End Get
        Set(ByVal value As String)
            _name = value
        End Set
    End Property


    Private _Layout As String
    <Size(5000)> _
    Public Property Laybout() As String
        Get
            Return _Layout
        End Get
        Set(ByVal value As String)
            _Layout = value
        End Set
    End Property


End Class

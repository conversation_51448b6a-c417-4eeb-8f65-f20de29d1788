Imports ENChartCAC
Imports EnchartDOLib

Public Class AddNewFacilityForm
    Protected CompanyClient As DOCompanyClient
    Private Sub AddNewFacilityForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.CompanyClient = ECGlobals.CurrentFacility.CompanyClient
        Me.teCompanyClient.EditValue = CompanyClient.LongName
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click


        Dim newFacility As New DOFacility
        newFacility.CompanyClient = CompanyClient

        newFacility.FacilityID = teFacilityID.EditValue
        newFacility.LongName = teLongName.EditValue
        newFacility.TechContact = teTechContact.EditValue
        newFacility.EDContact = teEDContact.EditValue
        newFacility.Save()

        CompanyClient.Facilities.Add(newFacility)
        Me.Close()
    End Sub

    Private Sub teCompanyClient_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles teCompanyClient.EditValueChanged
        If Not String.IsNullOrEmpty(Me.teFacilityID.EditValue) AndAlso Not String.IsNullOrEmpty(Me.teLongName.EditValue) Then
            btnOK.Enabled = True
        Else
            btnOK.Enabled = False
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub teFacilityID_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles teFacilityID.EditValueChanged
        If Not String.IsNullOrEmpty(Me.teFacilityID.EditValue) AndAlso Not String.IsNullOrEmpty(Me.teLongName.EditValue) Then
            btnOK.Enabled = True
        Else
            btnOK.Enabled = False
        End If
    End Sub

    Private Sub teLongName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles teLongName.EditValueChanged
        If Not String.IsNullOrEmpty(Me.teFacilityID.EditValue) AndAlso Not String.IsNullOrEmpty(Me.teLongName.EditValue) Then
            btnOK.Enabled = True
        Else
            btnOK.Enabled = False
        End If
    End Sub
End Class
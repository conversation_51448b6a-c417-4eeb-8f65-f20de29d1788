﻿Imports System.IO
Imports CodingReport

Imports System.Drawing

Namespace ColdFeed

    'Note this dll is not reference anywhere... it's loaded dynamically at runtime
    Public Class MeditechColdFeedImageFileHandler
        Implements IExportChargeSummaryImageFile


        Public Function CreateImageFile(ByVal chart As ChartObjWrapper) As Boolean Implements IExportChargeSummaryImageFile.CreateImageFile
            Dim outPath As String = CFHelper.OptionsDict("ImageOutputPath")
            Dim file_type As String = "TIFF"

            Dim temp_folder As String = CFHelper.GetCustomSettingValue("TemporaryFilePath")
            Dim temp_filename As String = String.Format("{0}{1}{2}.{3}", _
                                         chart.Chart.VisitID, _
                                         GetMeditechCreateDate, _
                                         GetMeditechCreateTime, _
                                         file_type)

            Dim temp_full_path = Path.Combine(My.Application.Info.DirectoryPath, temp_folder)
            temp_full_path = Path.Combine(temp_full_path, temp_filename)

            ' Make sure the temporary path folder exists

            If Not Directory.Exists(temp_folder) Then
                Directory.CreateDirectory(temp_folder)
            End If

            ' Temporarily export the document locally so that we can get its page count

            Dim report As New XtraCodingReport
            Dim sw As New Stopwatch
            sw.Start()

            report.Area51CodingReport(chart.Chart.Oid.ToString, temp_full_path, file_type)

            sw.Stop()

            Debug.WriteLine(sw.ElapsedMilliseconds)

            ' DAO - 2015/02/27
            ' Meditech has a very specific filename requirement as follows
            ' {0} Application ID            Required    30 characters   Pad right with spaces
            ' {1} Meditech Account Number   Required    12 characters   Pad right with spaces
            ' {2} Meditech Unit Number      Optional    10 characters   Pad right with spaces
            ' {3} Form ID                   Required    15 characters   Pad right with spaces
            ' {4} Delete Flag               Optional    1 character     Pad right with spaces
            ' {5} Number of Pages           Optional    5 characters    Pad left with zeroes
            ' {6} Date of Birth             Optional    8 characters    Pad right with spaces
            ' {7} Sex                       Optional    1 character     Pad right with spaces
            ' {8} Create Date               Optional    8 characters    Pad right with spaces
            ' {9} Create Time               Optional    4 characters    Pad right with spaces

            Dim medrec As String
            If String.IsNullOrEmpty(chart.Chart.MRN) Then
                medrec = ""
            Else
                medrec = chart.Chart.MRN
            End If

            Dim application_id As String ' The Application ID in the Meditech filename is a uniquely identifiable id
            If CFHelper.GetCustomSettingValue("AppendVersionNumber").ToUpper = "TRUE" Then
                application_id = String.Format("{0}v{1}", chart.Chart.VisitID, chart.Chart.Version)
            Else
                application_id = chart.Chart.VisitID
            End If

            Dim fileName = String.Format("{0}{1}{2}{3}{4}{5}{6}{7}{8}{9}.{10}",
                                         application_id.PadRight(30, " "),
                                         chart.Chart.VisitID.PadRight(12, " "),
                                         medrec.PadRight(10, " "),
                                         CFHelper.GetCustomSettingValue("MeditechFormID").PadRight(15, " "),
                                         CFHelper.GetCustomSettingValue("MeditechDeleteFlag").PadRight(1, " "),
                                         GetMeditechPageCount(temp_full_path),
                                         GetMeditechDOBString(chart),
                                         GetMeditechSex(chart),
                                         GetMeditechCreateDate,
                                         GetMeditechCreateTime,
                                         file_type)

            Dim fullPath = Path.Combine(My.Application.Info.DirectoryPath, outPath)

            If Not Directory.Exists(fullPath) Then
                    Directory.CreateDirectory(fullPath)
                End If

                fullPath = Path.Combine(fullPath, fileName)

                File.Move(temp_full_path, fullPath)

                chart.ImageFileName = Path.GetFileName(fullPath)

            If File.Exists(fullPath) Then
                chart.ImageExportSucceeded = True
            Else
                chart.ImageExportSucceeded = False
            End If

            Return chart.ImageExportSucceeded
        End Function

        Private Function GetMeditechDOBString(ByVal chart As ChartObjWrapper) As String
            Dim dob_redisplay_value As String = chart.Chart.GetRedisplayValue("Demographics_DOB_txt")
            Dim dob_meditech_string As String = "        "

            If dob_redisplay_value IsNot Nothing Then
                Try
                    Dim dob_date As Date = CDate(dob_redisplay_value)
                    dob_meditech_string = dob_date.ToString("yyyyMMdd")
                Catch ex As Exception
                End Try
            End If

            Return dob_meditech_string.PadRight(8, " ")
        End Function

        Private Function GetMeditechSex(ByVal chart As ChartObjWrapper) As String
            Return " "
        End Function

        Private Function GetMeditechCreateDate() As String
            Dim return_string As String = "        "

            Try
                return_string = Now.ToString("yyyyMMdd")
            Catch ex As Exception
            End Try

            Return return_string.PadRight(8, " ")
        End Function

        Private Function GetMeditechCreateTime() As String
            Dim return_string As String = "    "

            Try
                return_string = Now.ToString("hhmm")
            Catch ex As Exception
            End Try

            Return return_string.PadRight(4, " ")
        End Function

        Private Function GetMeditechPageCount(ByVal temp_file As String) As String
            Dim return_string As String = "00001"

            Try
                Dim tiff_image As Bitmap = New Bitmap(temp_file)
                return_string = tiff_image.GetFrameCount(System.Drawing.Imaging.FrameDimension.Page).ToString
                tiff_image.Dispose()
            Catch ex As Exception
            End Try

            Return return_string.PadLeft(5, "0")
        End Function

        Public Sub Init() Implements IExportChargeSummaryImageFile.Init

        End Sub

        Private _cFHelper As ColdFeedHelper
        Public Property CFHelper() As ColdFeedHelper Implements IExportChargeSummaryImageFile.CFHelper
            Get
                Return _cFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _cFHelper = value
            End Set
        End Property
    End Class
End Namespace
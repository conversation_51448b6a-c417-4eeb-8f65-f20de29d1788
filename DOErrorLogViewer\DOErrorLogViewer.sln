﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "DOErrorLogViewer", "DOErrorLogViewer\DOErrorLogViewer.vbproj", "{9FF902AC-1522-41AB-B896-9252365DC339}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9FF902AC-1522-41AB-B896-9252365DC339}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9FF902AC-1522-41AB-B896-9252365DC339}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9FF902AC-1522-41AB-B896-9252365DC339}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9FF902AC-1522-41AB-B896-9252365DC339}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 2
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfs.mckesson.com:8080/tfs/collection1
		SccLocalPath0 = .
		SccProjectUniqueName1 = DOErrorLogViewer\\DOErrorLogViewer.vbproj
		SccProjectName1 = DOErrorLogViewer
		SccLocalPath1 = DOErrorLogViewer
	EndGlobalSection
EndGlobal

﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("_2018Updater.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to ALTER EVENT Overnight
        '''ALTER HANDLER
        '''BEGIN
        '''  -- Drops and recreates table, populates with all data
        '''  --
        '''  -- =============================================================
        '''  insert into &quot;Overnight_Data_Timer&quot; values( &apos;-&apos;,current timestamp ) ;
        '''  insert into &quot;Overnight_Data_Timer&quot; values( &apos;Start_ALL&apos;,current timestamp ) ;
        '''  -- =============================================================
        '''  -- =============================================================
        '''  -- overnight processStart
        '''  --
        '''  drop table  [rest of string was truncated]&quot;;.
        '''</summary>
        Friend ReadOnly Property Overnight() As String
            Get
                Return ResourceManager.GetString("Overnight", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to IF NOT EXISTS(SELECT 1 FROM SYS.SYSCOLUMNS WHERE tname = &apos;Overnight_Data&apos; AND cname = &apos;Intubation&apos;) THEN
        '''
        '''  ALTER TABLE &quot;DBA&quot;.&quot;Overnight_Data&quot; ADD &quot;Intubation&quot; VARCHAR(25) NULL DEFAULT &apos;NA&apos;;  
        '''
        '''END IF;
        '''
        '''ALTER EVENT Overnight_New
        '''ALTER HANDLER
        '''BEGIN
        '''  -- ************************
        '''  -- Overnight_NEW Event
        '''  -- Last Modified 11/23/2014
        '''  -- ************************
        '''  -- Uncomment Triggers for Grid and Obs as needed.
        '''  -- Populates/Refreshes with the last 3 days of data entered
        '''  --
        '''  -- ======== [rest of string was truncated]&quot;;.
        '''</summary>
        Friend ReadOnly Property Overnight_New() As String
            Get
                Return ResourceManager.GetString("Overnight_New", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to ----------------------------------------------------------------------------------------------
        '''if NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = object_id(N&apos;[dbo].[usp_overnight]&apos;) AND OBJECTPROPERTY(object_id, N&apos;IsProcedure&apos;) = 1)
        '''BEGIN
        '''	-- If the Stored Procedure doesn&apos;t exist, first create a simple version so that it can be altered later in the script
        '''	DECLARE @strSQL nvarchar(200);
        '''	SELECT @strSQL=&apos;CREATE PROC [dbo].[usp_overnight] as SELECT 1&apos;;
        '''	EXEC sp_executesql @strSQL;
        '''END
        '''
        '''/* IC  [rest of string was truncated]&quot;;.
        '''</summary>
        Friend ReadOnly Property usp_overnight() As String
            Get
                Return ResourceManager.GetString("usp_overnight", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to if NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = object_id(N&apos;[dbo].[usp_overnight_rebuild]&apos;) AND OBJECTPROPERTY(object_id, N&apos;IsProcedure&apos;) = 1)
        '''BEGIN
        '''	-- Replication will not allow a drop procedure.  Therefore, we have to create the procedure if it does not exist and then alter it for
        '''	-- the script to be re-runnable. We have to do it dynamically because create proc must be in it&apos;s own batch. 
        '''
        '''	DECLARE @strSQL nvarchar(200);
        '''	SELECT @strSQL=&apos;CREATE PROC [dbo].[usp_overnight_rebuild] as SELE [rest of string was truncated]&quot;;.
        '''</summary>
        Friend ReadOnly Property usp_overnight_rebuild() As String
            Get
                Return ResourceManager.GetString("usp_overnight_rebuild", resourceCulture)
            End Get
        End Property
    End Module
End Namespace

﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:E<PERSON><PERSON>ReportsInstaller"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:SAK"
"SccLocalPath" = "8:SAK"
"SccAuxPath" = "8:SAK"
"SccProvider" = "8:SAK"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_0836ED9095051604663D28FEF920ACCC"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0836ED9095051604663D28FEF920ACCC"
        "OwnerKey" = "8:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_16937E5C22C44C6B93754430ED014F0B"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28233CD653A72ABB682277388999A478"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28233CD653A72ABB682277388999A478"
        "OwnerKey" = "8:_61603F656C338B535249C0A7BFA94F71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28233CD653A72ABB682277388999A478"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28233CD653A72ABB682277388999A478"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28233CD653A72ABB682277388999A478"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "OwnerKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFFF05F071DFD3E8240C52405622970"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BFFF05F071DFD3E8240C52405622970"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2EC53C15F41D7C58A733C6DE487DF323"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2EC53C15F41D7C58A733C6DE487DF323"
        "OwnerKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2EC53C15F41D7C58A733C6DE487DF323"
        "OwnerKey" = "8:_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2EC53C15F41D7C58A733C6DE487DF323"
        "OwnerKey" = "8:_CCED8303BAD73F81CA780FCF9BB6A75A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_414C8E9C167B50665977F4E1CF3DEFEC"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "OwnerKey" = "8:_61603F656C338B535249C0A7BFA94F71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_414C8E9C167B50665977F4E1CF3DEFEC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_0836ED9095051604663D28FEF920ACCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_C9DF3B20906D41A78ACE8C305F9264D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "OwnerKey" = "8:_97B17C791C427EC4929918052D674BCA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_61603F656C338B535249C0A7BFA94F71"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74D43A6C18DAEAFEE2971D2C4B04A83B"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74D43A6C18DAEAFEE2971D2C4B04A83B"
        "OwnerKey" = "8:_28233CD653A72ABB682277388999A478"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_75B18C52972B83F969AD35C88587A392"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_75B18C52972B83F969AD35C88587A392"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_75B18C52972B83F969AD35C88587A392"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_76CA34617862C826A9C2DE31804A9F43"
        "OwnerKey" = "8:_CCED8303BAD73F81CA780FCF9BB6A75A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_28233CD653A72ABB682277388999A478"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B86FC489C8E8BF17AC682A275C50E7D"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B86FC489C8E8BF17AC682A275C50E7D"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B86FC489C8E8BF17AC682A275C50E7D"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B86FC489C8E8BF17AC682A275C50E7D"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8FFC067BE324759EB55A5B279E6889EC"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8FFC067BE324759EB55A5B279E6889EC"
        "OwnerKey" = "8:_61603F656C338B535249C0A7BFA94F71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_28233CD653A72ABB682277388999A478"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97B17C791C427EC4929918052D674BCA"
        "OwnerKey" = "8:_414C8E9C167B50665977F4E1CF3DEFEC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97B17C791C427EC4929918052D674BCA"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7B25FB4D37F4C96BA11E79DFDF9A985"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C9DF3B20906D41A78ACE8C305F9264D9"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C9DF3B20906D41A78ACE8C305F9264D9"
        "OwnerKey" = "8:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C9DF3B20906D41A78ACE8C305F9264D9"
        "OwnerKey" = "8:_0836ED9095051604663D28FEF920ACCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CCED8303BAD73F81CA780FCF9BB6A75A"
        "OwnerKey" = "8:_8FFC067BE324759EB55A5B279E6889EC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CCED8303BAD73F81CA780FCF9BB6A75A"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D011CB64E26C4587B340053FFECD0B93"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DAACF9B761F6858D3C9955987223A9F0"
        "OwnerKey" = "8:_414C8E9C167B50665977F4E1CF3DEFEC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DAACF9B761F6858D3C9955987223A9F0"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E43EA453C0E84A47B8256A3D268A1E33"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F5EBFC041B0B40909D73EF56BC63481F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_61603F656C338B535249C0A7BFA94F71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9BF2D6A70E966AA89300120E68C35C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2BFFF05F071DFD3E8240C52405622970"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8E2895CDDF7585B559E0BECE322FECE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_28233CD653A72ABB682277388999A478"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8B86FC489C8E8BF17AC682A275C50E7D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_75B18C52972B83F969AD35C88587A392"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_968214603A108D0EC420BF8F95094B89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A95762C00B4469596E5ED52FF77A5C2F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2BFDE1BC6283FBA3140860EB7B5E391F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8A3CCA16C9D24E76052A0E50340C6AB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_77290E325439CACFC6127A624A1D71B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_932EE804647F4DDA02539545539BA9D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_74D43A6C18DAEAFEE2971D2C4B04A83B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8FFC067BE324759EB55A5B279E6889EC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CCED8303BAD73F81CA780FCF9BB6A75A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2EC53C15F41D7C58A733C6DE487DF323"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0836ED9095051604663D28FEF920ACCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C9DF3B20906D41A78ACE8C305F9264D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_414C8E9C167B50665977F4E1CF3DEFEC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97B17C791C427EC4929918052D674BCA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DAACF9B761F6858D3C9955987223A9F0"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\ENChartReportsInstaller.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                }
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\ENChartReportsInstaller.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:BusinessObjects.CrystalReports.NET.2.0"
                    {
                    "Name" = "8:Crystal Reports for .NET Framework 2.0"
                    "ProductCode" = "8:BusinessObjects.CrystalReports.NET.2.0"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Net.Framework.2.0"
                    {
                    "Name" = "8:.NET Framework 2.0"
                    "ProductCode" = "8:Microsoft.Net.Framework.2.0"
                    }
                }
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_B8220E10A5F54E8C9F1C4F8F8D5ED853"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:2.0.50727"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=9832"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0836ED9095051604663D28FEF920ACCC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraEditors.v7.2, Version=*******, Culture=neutral, PublicKeyToken=9b171c9fd64da1d1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0836ED9095051604663D28FEF920ACCC"
                    {
                    "Name" = "8:DevExpress.XtraEditors.v7.2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraEditors.v7.2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_16937E5C22C44C6B93754430ED014F0B"
            {
            "SourcePath" = "8:..\\ENChartReports\\search4doc.ico"
            "TargetName" = "8:search4doc.ico"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_234F05A9E7A924BFD99B7EEBDB0CA8EE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraTreeList.v7.2, Version=*******, Culture=neutral, PublicKeyToken=9b171c9fd64da1d1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_234F05A9E7A924BFD99B7EEBDB0CA8EE"
                    {
                    "Name" = "8:DevExpress.XtraTreeList.v7.2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraTreeList.v7.2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_28233CD653A72ABB682277388999A478"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.Shared, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_28233CD653A72ABB682277388999A478"
                    {
                    "Name" = "8:CrystalDecisions.Shared.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.Shared.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2BFDE1BC6283FBA3140860EB7B5E391F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CubeDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2BFDE1BC6283FBA3140860EB7B5E391F"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CubeDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.CubeDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2BFFF05F071DFD3E8240C52405622970"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.Prompting, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2BFFF05F071DFD3E8240C52405622970"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.Prompting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.Prompting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2EC53C15F41D7C58A733C6DE487DF323"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2EC53C15F41D7C58A733C6DE487DF323"
                    {
                    "Name" = "8:stdole.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:stdole.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_414C8E9C167B50665977F4E1CF3DEFEC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:EnchartDOLib, Version=1.3.2.7, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_414C8E9C167B50665977F4E1CF3DEFEC"
                    {
                    "Name" = "8:EnchartDOLib.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:EnchartDOLib.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4FFB5C77DCA5CE9C71AB8FD8F135321A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportSource, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4FFB5C77DCA5CE9C71AB8FD8F135321A"
                    {
                    "Name" = "8:CrystalDecisions.ReportSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_53E5E22ED213CBF09A3EC97F0E2BEE8F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Data.v7.2, Version=*******, Culture=neutral, PublicKeyToken=9b171c9fd64da1d1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_53E5E22ED213CBF09A3EC97F0E2BEE8F"
                    {
                    "Name" = "8:DevExpress.Data.v7.2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Data.v7.2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_61603F656C338B535249C0A7BFA94F71"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.Windows.Forms, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_61603F656C338B535249C0A7BFA94F71"
                    {
                    "Name" = "8:CrystalDecisions.Windows.Forms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.Windows.Forms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommonControls, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7328D7FDBFB4FE87A165DAC3E6E7C9B1"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommonControls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.CommonControls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_74D43A6C18DAEAFEE2971D2C4B04A83B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_74D43A6C18DAEAFEE2971D2C4B04A83B"
                    {
                    "Name" = "8:log4net.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:log4net.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_75B18C52972B83F969AD35C88587A392"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ObjectFactory, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_75B18C52972B83F969AD35C88587A392"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ObjectFactory.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.ObjectFactory.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_76CA34617862C826A9C2DE31804A9F43"
            {
            "SourcePath" = "8:Flash11c.ocx"
            "TargetName" = "8:Flash11c.ocx"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_77290E325439CACFC6127A624A1D71B6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommLayer, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_77290E325439CACFC6127A624A1D71B6"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommLayer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.CommLayer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8A3CCA16C9D24E76052A0E50340C6AB2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.DataDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8A3CCA16C9D24E76052A0E50340C6AB2"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.DataDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.DataDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8B86FC489C8E8BF17AC682A275C50E7D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.XmlSerialize, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8B86FC489C8E8BF17AC682A275C50E7D"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.XmlSerialize.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.XmlSerialize.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8E2895CDDF7585B559E0BECE322FECE0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.DataSetConversion, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8E2895CDDF7585B559E0BECE322FECE0"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.DataSetConversion.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.DataSetConversion.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8FFC067BE324759EB55A5B279E6889EC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_8FFC067BE324759EB55A5B279E6889EC"
                    {
                    "Name" = "8:FlashControlV71.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:FlashControlV71.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_932EE804647F4DDA02539545539BA9D2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommonObjectModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_932EE804647F4DDA02539545539BA9D2"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommonObjectModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.CommonObjectModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_968214603A108D0EC420BF8F95094B89"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.Controllers, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_968214603A108D0EC420BF8F95094B89"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.Controllers.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.Controllers.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97B17C791C427EC4929918052D674BCA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Xpo.v7.2, Version=*******, Culture=neutral, PublicKeyToken=9b171c9fd64da1d1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_97B17C791C427EC4929918052D674BCA"
                    {
                    "Name" = "8:DevExpress.Xpo.v7.2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Xpo.v7.2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9BF2D6A70E966AA89300120E68C35C1B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.CrystalReports.Engine, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9BF2D6A70E966AA89300120E68C35C1B"
                    {
                    "Name" = "8:CrystalDecisions.CrystalReports.Engine.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.CrystalReports.Engine.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A95762C00B4469596E5ED52FF77A5C2F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ReportDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A95762C00B4469596E5ED52FF77A5C2F"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ReportDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.ReportDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B2CFDD8D223B2649E3B51BDBBB0E38E3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ClientDoc, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B2CFDD8D223B2649E3B51BDBBB0E38E3"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ClientDoc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.ClientDoc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_B7B25FB4D37F4C96BA11E79DFDF9A985"
            {
            "SourcePath" = "8:..\\ENChartReports\\bin\\Release\\Reports\\Disposition.rpt"
            "TargetName" = "8:Disposition.rpt"
            "Tag" = "8:"
            "Folder" = "8:_D8EAA257E16B42CFB3BBC500DB8D1CB5"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C9DF3B20906D41A78ACE8C305F9264D9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Utils.v7.2, Version=*******, Culture=neutral, PublicKeyToken=9b171c9fd64da1d1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C9DF3B20906D41A78ACE8C305F9264D9"
                    {
                    "Name" = "8:DevExpress.Utils.v7.2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Utils.v7.2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CCED8303BAD73F81CA780FCF9BB6A75A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:ShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_CCED8303BAD73F81CA780FCF9BB6A75A"
                    {
                    "Name" = "8:ShockwaveFlashObjects.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ShockwaveFlashObjects.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_D011CB64E26C4587B340053FFECD0B93"
            {
            "SourcePath" = "8:..\\ENChartReports\\bin\\Release\\Reports\\EMDistribution.rpt"
            "TargetName" = "8:EMDistribution.rpt"
            "Tag" = "8:"
            "Folder" = "8:_D8EAA257E16B42CFB3BBC500DB8D1CB5"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DAACF9B761F6858D3C9955987223A9F0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:iAnywhere.Data.AsaClient, Version=9.0.2.3412, Culture=neutral, PublicKeyToken=f222fc4333e0d400"
                "ScatterAssemblies"
                {
                    "_DAACF9B761F6858D3C9955987223A9F0"
                    {
                    "Name" = "8:iAnywhere.Data.AsaClient.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:iAnywhere.Data.AsaClient.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_E43EA453C0E84A47B8256A3D268A1E33"
            {
            "SourcePath" = "8:..\\ENChartReports\\bin\\Release\\Reports\\VisitsReport.rpt"
            "TargetName" = "8:VisitsReport.rpt"
            "Tag" = "8:"
            "Folder" = "8:_D8EAA257E16B42CFB3BBC500DB8D1CB5"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_F5EBFC041B0B40909D73EF56BC63481F"
            {
            "SourcePath" = "8:..\\ENChartReports\\bin\\Release\\Reports\\ChartsByCoder.rpt"
            "TargetName" = "8:ChartsByCoder.rpt"
            "Tag" = "8:"
            "Folder" = "8:_D8EAA257E16B42CFB3BBC500DB8D1CB5"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_67894242A83343E59D656A704BFD1893"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_A50210C9EE3341DEB89E85A8ACDDE166"
            {
            "DefaultLocation" = "8:[SourceDir]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_D8EAA257E16B42CFB3BBC500DB8D1CB5"
                    {
                    "Name" = "8:Reports"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_E02F57712D3E458089F49E31684E65A8"
                        "Folders"
                        {
                        }
                    }
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_E239E46963EB4C4C80BE4EA51C73CF40"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_E6C37A56DF8A43DAB0F53F08005E89F6"
                    {
                    "Name" = "8:EN-Chart"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_19DC16643D9F45B5AEFBD1C0F9E5911E"
                        "Folders"
                        {
                        }
                    }
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:ENChartReportsInstaller"
        "ProductCode" = "8:{509B832D-0040-48FA-A592-F06452807408}"
        "PackageCode" = "8:{3CD70602-F91F-4CA8-BDF4-C0D3A6EF84B2}"
        "UpgradeCode" = "8:{064E46F6-F7FE-4CC8-9A28-AD4827DA19CD}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:Default Company Name"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:ENChartReportsInstaller"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Default Company Name"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_43369A107EB34B11AF99FFA8B4DC0274"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_DEDA4B44B0AA49209FB725CE08BAB039"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_FCA3E7BF7CC041A0A8696518491A5450"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_57AC6182F5594EF3860FBE4C730C54E9"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_8AB554438F974035A3B261ABF3BE7A87"
            {
            "Name" = "8:EN-Chart Reports"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_6549F2BDBA1848039990B85D03F9EE8F"
            "Folder" = "8:_E6C37A56DF8A43DAB0F53F08005E89F6"
            "WorkingFolder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Icon" = "8:_16937E5C22C44C6B93754430ED014F0B"
            "Feature" = "8:"
            }
        }
        "UserInterface"
        {
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_0BF422F173484C429E9EFC635CCEE4E9"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_F695B55B14A34DA3ABCBE3151213E3DE"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_11930F875BFF47509F9A24F7E6077DDC"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_008CDB7383E14B84B89120B09E8A6A7B"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_3DA15B9D64F24AB7976E600A483B3CE6"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_C7429DA0DB97414F8D498C76C5490176"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_57BD91D12F694B87BA583228894FA814"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_6442846B70C74C70ACB496A1E395FD3C"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_6DC512DED90744848B9A1747420478E9"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_00B8C459EDBE4B6AB8FA97B2068C2722"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_74CF3C4CE96B48D28D08E62E33A0BA3F"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_ED58CD6ACB024779B2C55AAF2FA39E7E"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_DB32202213694E1DA4184304C5ECDDC2"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_0BFAA51A028A4B1A828CAAB25A771A8F"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_53CE12B56C00484A9465B1F04EE251CF"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_7C58A8958A5C46EB8DAE34608F80AC92"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_E2F1264CE4AD4C68B83D5309C7BBAA46"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_DD9A5620DB3C4068AEE4D910EAD8D7B4"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_6549F2BDBA1848039990B85D03F9EE8F"
            {
            "SourcePath" = "8:..\\ENChartReports\\obj\\Debug\\ENChartReports.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_A50210C9EE3341DEB89E85A8ACDDE166"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{31A9CC51-723A-4A75-ACF6-AEBA6BC3F950}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}

Imports DevExpress
Imports DevExpress.XtraTreeList
Imports EnchartDOLib
Imports DevExpress.Xpo
Imports ENChartCAC

Public Class ComboBoxListEditorFormv2
    Dim cboList As DOConfigComboBoxList
    Dim ConfigInstance As DOConfigInstance

    Private ListToEdit As String 'donconfigcomboboxlist name
    Private ListName As String 'Generic List Name Displayed to user.

    Private _IsLoading As Boolean
    Public Property IsLoading() As Boolean
        Get
            Return _IsLoading
        End Get
        Set(ByVal value As Boolean)
            _IsLoading = value
        End Set
    End Property


    Public Sub New(ByVal pcboList As DOConfigComboBoxList, ByVal pListName As String)
        cboList = pcboList
        ListToEdit = Nothing 'pListToEdit
        ListName = pListName

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Public Sub New(ByVal pListToEdit As String, ByVal pListName As String)
        cboList = Nothing
        ListToEdit = pListToEdit
        ListName = pListName

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Private Sub ComboBoxListEditorFormv2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        'If DialogResult.OK = MessageBox.Show("Are you sure you want exit without saving changes?", "Really close?", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
        '    Session.DefaultSession.RollbackTransaction()
        '    cboList.Reload()

        '    Dim tlist As New List(Of DOConfigComboBoxListsItem)(cboList.ListItems)

        '    For Each li As DOConfigComboBoxListsItem In tlist
        '        li.Reload()
        '    Next
        '    'ECGlobals.CurrentFacility.Reload()
        '    'Me.Close()
        'Else
        '    e.Cancel = True
        'End If
    End Sub

    Private Sub ComboBoxListEditorForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        IsLoading = True

        Try
            Me.lblListName.Text = ListName

            If cboList Is Nothing Then
                'cboList = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxList(ListToEdit)
                cboList = ECGlobals.CurrentConfigInstance.GetComboBoxList(ListToEdit)
            End If

            'Note we need to reload in case someone else has modified the configinstance
            'since we last loaded it. Otherwise, our attempts to save it to update the updatever
            'will fail.
            Me.ConfigInstance = cboList.ConfigInstance

            'guess how friggen long it took to figure out to call session.reload() and pass true to force load
            'aggregates... ? for gd ever....
            Me.ConfigInstance.ECReload()
            'Session.DefaultSession.Reload(cboList, True)
            'Me.ConfigInstance.Reload()


            'cboList.Reload() 'this is aggregated and should reload listitems, but doesn't appear to.
            ''note this appears to pick up any additiosn or deletes, but not changes to individual items...
            'cboList.ListItems.Reload() 'this really should not be necessary

            'Dim tlist As New List(Of DOConfigComboBoxListsItem)(cboList.ListItems)
            'For Each li As DOConfigComboBoxListsItem In tlist
            '    li.Reload()
            'Next

            'If ECGlobals.CurrentUser.IsInRole(ECRoles.Developer) Then
            '    Me.btnEspEdit.Visible = True
            'Else
            '    Me.btnEspEdit.Visible = False
            'End If

            'If ECGlobals.CurrentUser.IsAdmin OrElse ECGlobals.CurrentUser.IsInRole(ECRoles.Developer) Then
            Me.lblRealListName.Text = "Config List Name : " & cboList.ListName
            Me.btnSaveToDisk.Visible = True
            'Else
            'Me.lblRealListName.Text = ""
            'Me.btnSaveToDisk.Visible = False
            'End If
            Me.RadioGroup1.EditValue = cboList.SortOrder
            Try
                Session.DefaultSession.BeginTransaction()
            Catch ex As Exception 'something went wrong...
                Session.DefaultSession.RollbackTransaction() 'undo... there was a problem
                Session.DefaultSession.BeginTransaction()
            End Try

            If cboList.SortOrder = "USE SORT ORDER" Then
                Me.ReNumberList(cboList)
            End If

            Me.TreeList1.DataSource = cboList.ListItems 'ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxList("NurseList").ListItems
        Finally
            IsLoading = False
        End Try

    End Sub

    Private Sub btnAddNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddNew.Click
        If String.IsNullOrEmpty(Me.teNewItem.EditValue) Then
            Beep()
            Me.teNewItem.EditValue = "Please enter a value"
            Return
        End If

        If Me.FindNodeByDisplayValue(Me.teNewItem.EditValue) IsNot Nothing Then
            MessageBox.Show("Value already exists in list", "Duplicate list item error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        Dim nli As New DOConfigComboBoxListsItem
        nli.ItemDisplayOrder = -1

        'testing
        Dim ci As DOConfigComboBoxListsItem = Me.GetCurrentDataRow

        'Could not find a current focused row... so...
        If ci Is Nothing Then
            'dim
            nli.ItemDisplayOrder = Me.GetHighestSordOrderValue - 10
        Else
            nli.ItemDisplayOrder = Me.GetCurrentDataRow.ItemDisplayOrder - 10
        End If

        nli.ComboBoxList = Me.cboList
        nli.Enabled = True
        nli.ItemDisplayName = Me.teNewItem.EditValue
        'nli.ItemDisplayOrder = 999999999

        nli.Save() 'with transactions turned on, the oid is not updated (until, commit is done?)
        cboList.ListItems.Add(nli)
        Me.ReNumberList(cboList)

        Me.TreeList1.SetFocusedNode(Me.FindNodeByDisplayValue(nli.ItemDisplayName))
        'Me.TreeList1.SetFocusedNode(Me.TreeList1.Nodes(cboList.ListItems.Count - 1))

        Me.teNewItem.EditValue = ""
        Me.teNewItem.Focus()

    End Sub

    Private Sub SaveButton_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDone.Click
        Me.Cursor = Cursors.WaitCursor
        Try
            'ConfigInstance.IncrementUpdateVer() 'moved to Doconfiginstance.Save method.
            ConfigInstance.Save()
            Session.DefaultSession.CommitTransaction()
        Catch ex As Exception
            MessageBox.Show("An error occured trying to save changes to the database. The most likely causse of this would be that someone else is also trying to modify the database.", "Error Saving Changes", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ECLog.WriteEntry(Me.ToString & " -> The following error occured : " & ex.Message, TraceEventType.Error)
            Me.Close()
            Return
        End Try

        'Try
        '    Dim cfg As DOConfigInstance = Me.ConfigInstance 'cboList.ConfigInstance
        '    cfg.IncrementUpdateVer()
        '    cfg.Save()
        'Catch ex As Exception
        '    MessageBox.Show("Error saving Config, updatever = " & Me.ConfigInstance.UpdateVer.ToString)
        'End Try

        Me.DialogResult = DialogResult.OK
        Me.Close()
    End Sub

    Private Sub teNewItem_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles teNewItem.Click
        Me.teNewItem.EditValue = ""
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        If DialogResult.OK = MessageBox.Show("Are you sure you want exit without saving changes?", "Really close?", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
            Session.DefaultSession.RollbackTransaction()
            cboList.Reload()

            Dim tlist As New List(Of DOConfigComboBoxListsItem)(cboList.ListItems)

            For Each li As DOConfigComboBoxListsItem In tlist
                li.Reload()
            Next
            Me.Close()
        End If
        'If DialogResult.OK = MessageBox.Show("Are you sure you want exit without saving changes?", "Delete List Item", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
        '    Session.DefaultSession.RollbackTransaction()
        '    cboList.Reload()

        '    Dim tlist As New List(Of DOConfigComboBoxListsItem)(cboList.ListItems)

        '    For Each li As DOConfigComboBoxListsItem In tlist
        '        li.Reload()
        '    Next
        '    'ECGlobals.CurrentFacility.Reload()
        '    Me.Close()
        'End If

    End Sub

    Sub EnableSortButtons(Optional ByVal status As Boolean = True)
        Me.btnMoveToBottom.Enabled = status
        Me.btnMoveToTop.Enabled = status
        Me.btnUp.Enabled = status
        Me.btnDown.Enabled = status
    End Sub

    Private Sub RadioGroup1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioGroup1.SelectedIndexChanged
        If Me.RadioGroup1.EditValue = "USE SORT ORDER" Then
            EnableSortButtons()
        Else
            EnableSortButtons(False)
        End If

        If Me.IsLoading Then
            Return
        End If
        cboList.SortOrder = Me.RadioGroup1.EditValue
        cboList.Save()
        Me.TreeList1.DataSource = Nothing
        Me.TreeList1.DataSource = cboList.ListItems


    End Sub

    Private Sub TreeList1_FocusedNodeChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraTreeList.FocusedNodeChangedEventArgs) Handles TreeList1.FocusedNodeChanged
        ' Debug.WriteLine(e.Node.Item(0))
        'me.TreeList1.
    End Sub

    Function GetCurrentDataRow() As DOConfigComboBoxListsItem
        Dim tn As DevExpress.XtraTreeList.Nodes.TreeListNode = Me.TreeList1.FocusedNode

        Return Me.TreeList1.GetDataRecordByNode(tn)
    End Function

    Function GetHighestSordOrderValue() As Integer
        Dim hso As Integer

        For Each i As DOConfigComboBoxListsItem In cboList.ListItems

            If i.ItemDisplayOrder > hso Then
                hso = i.ItemDisplayOrder
            End If
        Next

        Return hso
    End Function

    Sub ReNumberList(ByVal pList As DOConfigComboBoxList)
        Dim lList As New List(Of DOConfigComboBoxListsItem)(pList.ListItems)

        Dim ido As Integer = 100
        For Each li As DOConfigComboBoxListsItem In lList
            li.ItemDisplayOrder = ido
            ido += 100
            li.Save()
        Next

        TreeList1.RefreshDataSource()
    End Sub

    Function FindNodeByOID(ByVal oid As Integer) As DevExpress.XtraTreeList.Nodes.TreeListNode
        'For Each li As DOConfigComboBoxListsItem In cboList.ListItems
        For Each tln As DevExpress.XtraTreeList.Nodes.TreeListNode In Me.TreeList1.Nodes
            If DirectCast(Me.TreeList1.GetDataRecordByNode(tln), DOConfigComboBoxListsItem).Oid = oid Then
                Return tln
            End If
        Next
        Return Nothing
    End Function

    Function FindNodeByDisplayValue(ByVal value As String) As DevExpress.XtraTreeList.Nodes.TreeListNode
        'For Each li As DOConfigComboBoxListsItem In cboList.ListItems
        For Each tln As DevExpress.XtraTreeList.Nodes.TreeListNode In Me.TreeList1.Nodes
            If DirectCast(Me.TreeList1.GetDataRecordByNode(tln), DOConfigComboBoxListsItem).ItemDisplayName.CompareTo(value) = 0 Then
                Return tln
            End If
        Next
        Return Nothing
    End Function

    Sub SetFocesedRowByDisplayValue(ByVal value As String)
        Dim tln As DevExpress.XtraTreeList.Nodes.TreeListNode = Me.FindNodeByDisplayValue(value)
        If tln Is Nothing Then
            Beep()
            Return
        End If

        Me.TreeList1.SetFocusedNode(tln)
    End Sub

    Private Sub btnUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUp.Click

        Dim cdo As DOConfigComboBoxListsItem = Me.GetCurrentDataRow

        If cdo Is Nothing OrElse cdo.ItemDisplayOrder = 100 Then
            Beep()
            Return
        End If

        cdo.ItemDisplayOrder -= 101
        Me.ReNumberList(cboList)

        SetFocesedRowByDisplayValue(cdo.ItemDisplayName)

    End Sub


    Private Sub btnDown_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDown.Click

        Dim cdo As DOConfigComboBoxListsItem = Me.GetCurrentDataRow

        If cdo Is Nothing OrElse cdo.ItemDisplayOrder = Me.GetHighestSordOrderValue Then
            Beep()
            Return
        End If

        cdo.ItemDisplayOrder += 101
        Me.ReNumberList(cboList)

        SetFocesedRowByDisplayValue(cdo.ItemDisplayName)

    End Sub

    Private Sub btnMoveToTop_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveToTop.Click
        Dim cdo As DOConfigComboBoxListsItem = Me.GetCurrentDataRow

        If cdo Is Nothing OrElse cdo.ItemDisplayOrder = 100 Then
            Beep()
            Return
        End If

        cdo.ItemDisplayOrder = 0
        Me.ReNumberList(cboList)

        SetFocesedRowByDisplayValue(cdo.ItemDisplayName)
    End Sub

    Private Sub btnMoveToBottom_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveToBottom.Click
        Dim cdo As DOConfigComboBoxListsItem = Me.GetCurrentDataRow

        If cdo Is Nothing OrElse cdo.ItemDisplayOrder = Me.GetHighestSordOrderValue Then
            Beep()
            Return
        End If

        cdo.ItemDisplayOrder = Me.GetHighestSordOrderValue + 100
        Me.ReNumberList(cboList)

        SetFocesedRowByDisplayValue(cdo.ItemDisplayName)
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        Try
            Dim lrow As DOConfigComboBoxListsItem = Nothing
            lrow = Me.GetCurrentDataRow
            If lrow Is Nothing Then
                Beep()
                Return
            End If

            If DialogResult.OK = MessageBox.Show(String.Format("Are you sure you want to permanently delete list item '{0}'?", lrow.ItemDisplayName), "Delete List Item", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
                Me.GetCurrentDataRow.Delete()
                Me.TreeList1.RefreshDataSource()
            End If

        Catch
        End Try
    End Sub

    Private Sub btnDeleteDisabled_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteDisabled.Click
        Dim dList As New List(Of DOConfigComboBoxListsItem)(cboList.ListItems)

        If DialogResult.OK = MessageBox.Show("Are you sure you want to permanently delete ALL disabled list items?", "Delete disabled list items", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
            For Each li As DOConfigComboBoxListsItem In dList
                If li.Enabled = False Then
                    li.Delete()
                End If
            Next
            Me.TreeList1.RefreshDataSource()
        End If
    End Sub

    Private Sub TreeList1_MouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles TreeList1.MouseDoubleClick
        'Dim c As Columns.TreeListColumn = TreeList1.Columns.Item("ItemDisplayName")
        'c.OptionsColumn.
        Me.ceAllowEdit.Checked = Not Me.ceAllowEdit.Checked
    End Sub

    Private Sub ceAllowEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ceAllowEdit.CheckedChanged
        If Me.ceAllowEdit.Checked Then
            TreeList1.Columns.Item("ItemDisplayName").OptionsColumn.AllowEdit = True
        Else
            TreeList1.Columns.Item("ItemDisplayName").OptionsColumn.AllowEdit = False
        End If
    End Sub

    Private Sub btnSaveToDisk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveToDisk.Click
        'SaveSingleComboList(cboList)
    End Sub


    ' ''jjc 10.22.13 As far as i can tell, the btnEspEdit never gets enabled so we don't need this logic anywho...
    ''Private Sub btnEspEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEspEdit.Click
    ''    Dim pi As New ProcessStartInfo
    ''    pi.FileName = "espcodeeditv2.exe"
    ''    pi.Arguments = ""
    ''    pi.UseShellExecute = True
    ''    Dim bFirstTime As Boolean = True

    ''    Dim controlList As New List(Of String)
    ''    For Each c As DOConfigComboBoxListControl In Me.cboList.ComboBoxes
    ''        If bFirstTime Then
    ''            pi.Arguments = c.ComboBoxName
    ''            bFirstTime = False
    ''        Else
    ''            pi.Arguments += " " & c.ComboBoxName
    ''        End If
    ''    Next

    ''    Dim p As New Process
    ''    p.StartInfo = pi
    ''    p.Start()

    ''End Sub

End Class
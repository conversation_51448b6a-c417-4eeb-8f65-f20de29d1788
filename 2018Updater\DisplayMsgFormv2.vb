Imports DevExpress.XtraEditors

Public Class DisplayMsgFormv2

#Region "Fields"

    Private _msg As String

#End Region 'Fields

#Region "Constructors"

    Sub New()
        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Sub New(ByVal pmsg As String)
        MyBase.New()



        Me.InitializeComponent()
        Msg = pmsg
    End Sub

    Public Sub DoubleDisplayWidth()
        Size = New Size(Size.Width * 2, Size.Height) ' Size.Width * 2
    End Sub

    Public Sub AllignTextLeft()
        lblMsg.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
    End Sub

#End Region 'Constructors

#Region "Properties"

    Public Property Msg() As String
        Get
            Return _msg
        End Get
        Set(ByVal value As String)
            _msg = value
            Me.lblMsg.Text = _msg
            Refresh()
        End Set
    End Property

#End Region 'Properties

#Region "Methods"

    Private Sub DisplayMsgForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
    End Sub

#End Region 'Methods

End Class
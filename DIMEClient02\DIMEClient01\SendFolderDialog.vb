Imports System.Windows.Forms

Public Class SendFolderDialog

    Dim _send_folder As String

    Public Property SendFolder() As String
        Get
            Return _send_folder
        End Get
        Set(ByVal value As String)
            _send_folder = value
        End Set
    End Property

    Private Sub SelectFolder(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSendFolder.Click, btnBrowse.Click
        If fbdSendFolder.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
            txtSendFolder.Text = fbdSendFolder.SelectedPath
        End If
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.OK

        _send_folder = txtSendFolder.Text

        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub
End Class

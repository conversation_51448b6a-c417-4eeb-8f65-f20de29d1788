﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ChargeAllocationAndModifiersMainForm
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        components = New ComponentModel.Container()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridLevelNode2 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        colUserDefinedBillingdate = New DevExpress.XtraGrid.Columns.GridColumn()
        colTreatmentArea1 = New DevExpress.XtraGrid.Columns.GridColumn()
        colUnits = New DevExpress.XtraGrid.Columns.GridColumn()
        colCDM = New DevExpress.XtraGrid.Columns.GridColumn()
        colTreatmentAreaType = New DevExpress.XtraGrid.Columns.GridColumn()
        colPhysician = New DevExpress.XtraGrid.Columns.GridColumn()
        GridControl1 = New DevExpress.XtraGrid.GridControl()
        ChargeBindingSource = New BindingSource(components)
        ChargesGridView = New DevExpress.XtraGrid.Views.Grid.GridView()
        colHcpcs = New DevExpress.XtraGrid.Columns.GridColumn()
        colChargeMaster = New DevExpress.XtraGrid.Columns.GridColumn()
        colTotalUnits = New DevExpress.XtraGrid.Columns.GridColumn()
        colOriginalBillingdate = New DevExpress.XtraGrid.Columns.GridColumn()
        colTreatmentArea = New DevExpress.XtraGrid.Columns.GridColumn()
        colCodeType = New DevExpress.XtraGrid.Columns.GridColumn()
        colESPValue = New DevExpress.XtraGrid.Columns.GridColumn()
        colDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Button2 = New Button()
        EditBtn = New Button()
        CType(GridView1, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).BeginInit()
        CType(ChargeBindingSource, ComponentModel.ISupportInitialize).BeginInit()
        CType(ChargesGridView, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' GridView1
        ' 
        GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colUserDefinedBillingdate, colTreatmentArea1, colUnits, colCDM, colTreatmentAreaType, colPhysician})
        GridView1.GridControl = GridControl1
        GridView1.Name = "GridView1"
        GridView1.OptionsBehavior.Editable = False
        GridView1.OptionsView.ShowGroupPanel = False
        ' 
        ' colUserDefinedBillingdate
        ' 
        colUserDefinedBillingdate.FieldName = "UserDefinedBillingdate"
        colUserDefinedBillingdate.Name = "colUserDefinedBillingdate"
        colUserDefinedBillingdate.Visible = True
        colUserDefinedBillingdate.VisibleIndex = 0
        colUserDefinedBillingdate.Width = 190
        ' 
        ' colTreatmentArea1
        ' 
        colTreatmentArea1.FieldName = "TreatmentArea"
        colTreatmentArea1.Name = "colTreatmentArea1"
        colTreatmentArea1.Visible = True
        colTreatmentArea1.VisibleIndex = 1
        colTreatmentArea1.Width = 248
        ' 
        ' colUnits
        ' 
        colUnits.FieldName = "Units"
        colUnits.Name = "colUnits"
        colUnits.Visible = True
        colUnits.VisibleIndex = 2
        colUnits.Width = 93
        ' 
        ' colCDM
        ' 
        colCDM.FieldName = "CDM"
        colCDM.Name = "colCDM"
        colCDM.Visible = True
        colCDM.VisibleIndex = 3
        colCDM.Width = 208
        ' 
        ' colTreatmentAreaType
        ' 
        colTreatmentAreaType.FieldName = "TreatmentAreaType"
        colTreatmentAreaType.Name = "colTreatmentAreaType"
        colTreatmentAreaType.Visible = True
        colTreatmentAreaType.VisibleIndex = 4
        colTreatmentAreaType.Width = 176
        ' 
        ' colPhysician
        ' 
        colPhysician.Caption = "Physician"
        colPhysician.FieldName = "Physician"
        colPhysician.Name = "colPhysician"
        colPhysician.Visible = True
        colPhysician.VisibleIndex = 5
        colPhysician.Width = 386
        ' 
        ' GridControl1
        ' 
        GridControl1.Anchor = AnchorStyles.Top Or AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        GridControl1.DataSource = ChargeBindingSource
        GridLevelNode1.LevelTemplate = GridView1
        GridLevelNode1.RelationName = "ChargeAllocations"
        GridLevelNode2.RelationName = "Modifiers"
        GridControl1.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1, GridLevelNode2})
        GridControl1.Location = New Point(26, 12)
        GridControl1.MainView = ChargesGridView
        GridControl1.Name = "GridControl1"
        GridControl1.Size = New Size(930, 412)
        GridControl1.TabIndex = 0
        GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {ChargesGridView, GridView1})
        ' 
        ' ChargeBindingSource
        ' 
        ChargeBindingSource.DataSource = GetType(AIC.SharedData.CAM2.Charge)
        ' 
        ' ChargesGridView
        ' 
        ChargesGridView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colHcpcs, colChargeMaster, colTotalUnits, colOriginalBillingdate, colTreatmentArea, colCodeType, colESPValue, colDescription})
        ChargesGridView.GridControl = GridControl1
        ChargesGridView.Name = "ChargesGridView"
        ChargesGridView.OptionsBehavior.Editable = False
        ChargesGridView.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(colOriginalBillingdate, DevExpress.Data.ColumnSortOrder.Ascending), New DevExpress.XtraGrid.Columns.GridColumnSortInfo(colTreatmentArea, DevExpress.Data.ColumnSortOrder.Ascending), New DevExpress.XtraGrid.Columns.GridColumnSortInfo(colHcpcs, DevExpress.Data.ColumnSortOrder.Ascending)})
        ' 
        ' colHcpcs
        ' 
        colHcpcs.FieldName = "Hcpcs"
        colHcpcs.Name = "colHcpcs"
        colHcpcs.Visible = True
        colHcpcs.VisibleIndex = 2
        colHcpcs.Width = 137
        ' 
        ' colChargeMaster
        ' 
        colChargeMaster.Caption = "CDM"
        colChargeMaster.FieldName = "ChargeMaster"
        colChargeMaster.Name = "colChargeMaster"
        colChargeMaster.Visible = True
        colChargeMaster.VisibleIndex = 4
        colChargeMaster.Width = 100
        ' 
        ' colTotalUnits
        ' 
        colTotalUnits.Caption = "Units"
        colTotalUnits.FieldName = "TotalUnits"
        colTotalUnits.Name = "colTotalUnits"
        colTotalUnits.Visible = True
        colTotalUnits.VisibleIndex = 3
        colTotalUnits.Width = 55
        ' 
        ' colOriginalBillingdate
        ' 
        colOriginalBillingdate.Caption = "Date"
        colOriginalBillingdate.FieldName = "OriginalBillingdate"
        colOriginalBillingdate.Name = "colOriginalBillingdate"
        colOriginalBillingdate.Visible = True
        colOriginalBillingdate.VisibleIndex = 0
        colOriginalBillingdate.Width = 113
        ' 
        ' colTreatmentArea
        ' 
        colTreatmentArea.Caption = "Treatment Area/Nursing Unit"
        colTreatmentArea.FieldName = "TreatmentArea"
        colTreatmentArea.Name = "colTreatmentArea"
        colTreatmentArea.Visible = True
        colTreatmentArea.VisibleIndex = 1
        colTreatmentArea.Width = 209
        ' 
        ' colCodeType
        ' 
        colCodeType.FieldName = "CodeType"
        colCodeType.Name = "colCodeType"
        colCodeType.Visible = True
        colCodeType.VisibleIndex = 5
        colCodeType.Width = 55
        ' 
        ' colESPValue
        ' 
        colESPValue.FieldName = "ESPValue"
        colESPValue.Name = "colESPValue"
        colESPValue.Width = 53
        ' 
        ' colDescription
        ' 
        colDescription.FieldName = "Description"
        colDescription.Name = "colDescription"
        colDescription.Visible = True
        colDescription.VisibleIndex = 6
        colDescription.Width = 263
        ' 
        ' Button2
        ' 
        Button2.Anchor = AnchorStyles.Bottom Or AnchorStyles.Right
        Button2.Location = New Point(881, 430)
        Button2.Name = "Button2"
        Button2.Size = New Size(75, 23)
        Button2.TabIndex = 2
        Button2.Text = "OK"
        Button2.UseVisualStyleBackColor = True
        ' 
        ' EditBtn
        ' 
        EditBtn.Anchor = AnchorStyles.Bottom Or AnchorStyles.Right
        EditBtn.Location = New Point(787, 430)
        EditBtn.Name = "EditBtn"
        EditBtn.Size = New Size(75, 23)
        EditBtn.TabIndex = 1
        EditBtn.Text = "Edit"
        EditBtn.UseVisualStyleBackColor = True
        ' 
        ' ChargeAllocationAndModifiersMainForm
        ' 
        AutoScaleDimensions = New SizeF(6.0F, 13.0F)
        AutoScaleMode = AutoScaleMode.Font
        ClientSize = New Size(984, 465)
        Controls.Add(Button2)
        Controls.Add(EditBtn)
        Controls.Add(GridControl1)
        Name = "ChargeAllocationAndModifiersMainForm"
        StartPosition = FormStartPosition.CenterParent
        Text = "Original Calculated Charges  (Prior to Allocation)"
        CType(GridView1, ComponentModel.ISupportInitialize).EndInit()
        CType(GridControl1, ComponentModel.ISupportInitialize).EndInit()
        CType(ChargeBindingSource, ComponentModel.ISupportInitialize).EndInit()
        CType(ChargesGridView, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
    End Sub
    Friend WithEvents ChargeBindingSource As BindingSource
    Friend WithEvents Button2 As Button
    Friend WithEvents EditBtn As Button
    Friend WithEvents colDescription As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colESPValue As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCodeType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colOriginalBillingdate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTotalUnits As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChargeMaster As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHcpcs As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ChargesGridView As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colUserDefinedBillingdate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUnits As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCDM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentAreaType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPhysician As DevExpress.XtraGrid.Columns.GridColumn
End Class

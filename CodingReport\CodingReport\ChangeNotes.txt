**************  (v. 3.0.0.0) 2016-03-31 jjc
	* US36560: SAP 17 Upgrade - Upgrade Coding Report Solutions
	* Upgraded solution to .NET 4.0
	* Upgraded DevExpress to v15.2
	* Changes to eliminate the need for separate Sybase and SQL Server versions of this solution

**************  (v. 2.1.1.0) 2015-11-18 dao
	* Update version to correspond with MIC release version number

**************  (v. 1.7.0.8) 2015-09-14 crc
	* removed dynamic user and facility information retrieval for audit logging for print, export, e-mail output ...
		the UI will already have this information.  the coding report db connect and updated information causes problems 
		and overrides the UI data and the logging is incorrect

**************  (v. 1.7.0.4) 2015-08-24 crc
	* updated dynamic user and facility information retrieval for audit logging for print, export, e-mail output US28640

**************  (v. 1.7.0.3) 2015-08-24 crc
	* added audit logging for print, export, e-mail output US28640

**************  (v. 1.7.0.1) 2015-08-04 crc
	* removed ICD9 column and label references US27277

**************  (v. 1.7.0.0) 2014-12-03
	* Update version number to correspond with the MIC release version number
	* Defect - Summary E/M Level will be hidden if a non-ED E/M Level detail item exists
		XtraCodingReport.vb
			- Add extra condition to make sure the detail level E/M item's CodeType is Facility before hiding the summary level
			  E/M item (~lines 152-156)

**************  (v. 1.5.0.3) 2014-10-30
	* US13712: Defect - WI 2837 - MIC: Fix coding report with the removal of the E/M summary at the bottom to work with previously saved charts
	* Cannot rely on the UsesPoints column. Instead we can check the detail items for an E/M summary detail. If there's an E/M summary detail
	  the chart uses the grid model and the footer should not be displayed
		XtraCodingReport.vb
			- Removed code that checked the UsesPoints column
			- Created new method SetFooterVisibility that takes a report and a boolean as paramaters and sets the visibility of the passed
			  report's footer row to the passed boolean value (~lines 334-339)
			- Loop through the detail records and if one contains the text "E/M Level" hide the report footer (~lines 140-163) 
		
**************  (v. 1.5.0.2) 2014-10-29
	* US13712: Defect - WI 2837 - MIC: Fix coding report with the removal of the E/M summary at the bottom to work with previously saved charts
	* If a chart's DOChartInfo.UsesPoints field is true or null, display the Facility E/M Summary row. By setting the default visibility for that
	  row's elements to true, we can ensure that the only scenario in which it will be hidden is when the displayed chart is a grid model chart
		XtraCodingReport.vb [Design]
			- Set the Visible property for all elements in ReportFooter1 to 'True'
		XtraCodingReport.vb
			- Improved commenting in Sub Area51CodingReport
			- Set the default value of the uses_points variable to True (~line 121)
			
**************  (v. 1.5.0.1) 2014-06-20
	* US10542: Coding Report - Display Facility E/M Summary Row for Points Model Charts
	* If a chart's DOChartInfo.UsesPoints field is true, display the Facility E/M Summary row
		CodingReportDataSet1.xsd
			- Modified the DOChartInfoTableAdapter query to also select the UsesPoints field from the DOChartInfo table
			- Added a UsesPoints field to the DOChartInfo DataTable that will hold the UsesPoints value from the DOChartInfo database table
		XtraCodingReport.vb [Design]
			- Renamed the E&M Level column footer/summary label to lblEMLevelFooter
			- Renamed the HCPCS column footer/summary label to lblHCPCSFooter
			- Renamed the CDM column footer/summary label to lblCDMFooter
			- Renamed the coding summary version label to lblCodingReportVersion
		XtraCodingReport.vb
			- Check the UsesPoints value of the chart, if the charts does use points, display the footer/summary labels (~lines 116-127)

**************  (v. 1.5.0.0) 2014-06-01
	* Copyright message should use the year of the display chart as the year the report was generated
		XtraCodingReport.vb [Design]
			- Renamed the copyright string label to lblCopyright
			- Changed the default year of lblCopyright's text to 2014
		XtraCodingReport.vb
			- Use the current date's year value as the year in lblCopyright (~line 97)
	* For Clinic Module charts, hide the Points column and the ICD9 column
		CodingReportDataSet1.xsd
			- Modified the DOChartInfoTableAdapter query to also select the ChartType field from the DOChartInfo table
			- Added a ChartType field to the DOChartInfo DataTable that will hold the ChartType value from the DOChartInfo database table
		XtraCodingReport.vb [Design]
			- Renamed the Points column header label to lblPointsHeader
			- Renamed the ICD9 column header label to lblICD9Header
			- Renamed the Points column detail label to lblPointsDetail
			- Renamed the ICD9 column detail label to lblICD9Detail
			- Renamed the Points column footer label to lblPointsFooter
		XtraCodingReport.vb
			- Check the ChartType value of the chart, if it's a Clinic Module chart hide the Points column and the ICD9 column (~lines 99-114)
	* For all charts, change the visit ID label's text at the top of the report from "Chart" to "Visit ID"
		XtraCodingReport.vb [Design]
			- Changed the text of XrLabel5 to "Visit ID"
	* For all charts, do not show the Facility E/M summary level item
		XtraCodingReport.vb [Design]
			- Set the Visible property for all elements in ReportFooter1 to 'False'

version ******** 4/6/2011
	-added DOS to Dataset and use the year from it as the year in the copyright string

version *******, 1/1/2009
	- 2009 release
	
version *******, 1/28/2009
	- modified .exe.config file path information to fix multiple .pdf export issue

version *******, 3/25/2009
	- modified footer AMA copyright to 2009 since we are using new codes for 2009
	- modified DOChartInfoTableAdapter SELECT command to fix historical chart version 
		report viewing/printing
	
version *******, ???

version *******, 3/10/10
	- modified footer AMA copyright to 2010
	- modified DOChartInfoTableAdapter SELECT command to include chart version creation date (DOChart table)
		to display when the chart version was saved

version *******, 5/x/10
	- modified DOChartInfoTableAdapter from SELECT...a.Chart,... to SELECT...d.OID as Chart... to allow view/print 
		of non-current (previous) chart versions -- this should allow image export of a specific version of a chart
		
version ******* 5/x/10
	- modified XtraCodingReport.Area51CodingReport to accept new optional parms
		* ImageExportPathFilename As String (fully qualified path and filename for export image/file)
		* ImageExportType as String (export image/file type, i.e. "tiff","txt","jpeg","gif","png")
	- modified XtraCodingReport.Area51CodingReport to skip any forced delays and report retrieval retries if 
		running the cold feed export (CFE) process
		
version ******* 12/8/10
	- VS 2008 conversion
	- modified AMA copyright from 2010 to 2011

==> TODO: add success/failure logging
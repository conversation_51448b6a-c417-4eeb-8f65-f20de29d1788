﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CrystalReportsUpdateDialog
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.lblMultipurpose = New DevExpress.XtraEditors.LabelControl()
        Me.tmrUpdating = New System.Windows.Forms.Timer(Me.components)
        Me.progbarUpdating = New DevExpress.XtraEditors.MarqueeProgressBarControl()
        Me.btnMultipurpose = New DevExpress.XtraEditors.SimpleButton()
        Me.btnExit = New DevExpress.XtraEditors.SimpleButton()
        Me.lblMultipurpose2 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.progbarUpdating.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblMultipurpose
        '
        Me.lblMultipurpose.Location = New System.Drawing.Point(13, 13)
        Me.lblMultipurpose.Name = "lblMultipurpose"
        Me.lblMultipurpose.Size = New System.Drawing.Size(335, 13)
        Me.lblMultipurpose.TabIndex = 2
        Me.lblMultipurpose.Text = "An update is REQUIRED to run reports and may take several minutes."
        '
        'tmrUpdating
        '
        Me.tmrUpdating.Interval = 1000
        '
        'progbarUpdating
        '
        Me.progbarUpdating.EditValue = 0
        Me.progbarUpdating.Location = New System.Drawing.Point(12, 55)
        Me.progbarUpdating.Name = "progbarUpdating"
        Me.progbarUpdating.Size = New System.Drawing.Size(422, 18)
        Me.progbarUpdating.TabIndex = 3
        Me.progbarUpdating.Visible = False
        '
        'btnMultipurpose
        '
        Me.btnMultipurpose.Location = New System.Drawing.Point(13, 56)
        Me.btnMultipurpose.Name = "btnMultipurpose"
        Me.btnMultipurpose.Size = New System.Drawing.Size(75, 23)
        Me.btnMultipurpose.TabIndex = 4
        Me.btnMultipurpose.Text = "Update Now"
        '
        'btnExit
        '
        Me.btnExit.Location = New System.Drawing.Point(94, 56)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(125, 23)
        Me.btnExit.TabIndex = 5
        Me.btnExit.Text = "Exit and Update Later"
        '
        'lblMultipurpose2
        '
        Me.lblMultipurpose2.Location = New System.Drawing.Point(13, 33)
        Me.lblMultipurpose2.Name = "lblMultipurpose2"
        Me.lblMultipurpose2.Size = New System.Drawing.Size(301, 13)
        Me.lblMultipurpose2.TabIndex = 6
        Me.lblMultipurpose2.Text = "You will not be able to run reports until the update is complete."
        '
        'CrystalReportsUpdateDialog
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(447, 85)
        Me.ControlBox = False
        Me.Controls.Add(Me.lblMultipurpose2)
        Me.Controls.Add(Me.btnExit)
        Me.Controls.Add(Me.btnMultipurpose)
        Me.Controls.Add(Me.progbarUpdating)
        Me.Controls.Add(Me.lblMultipurpose)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "CrystalReportsUpdateDialog"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "AIC Standard Reports - Updating"
        CType(Me.progbarUpdating.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents lblMultipurpose As DevExpress.XtraEditors.LabelControl
    Friend WithEvents tmrUpdating As System.Windows.Forms.Timer
    Friend WithEvents progbarUpdating As DevExpress.XtraEditors.MarqueeProgressBarControl
    Friend WithEvents btnMultipurpose As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnExit As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblMultipurpose2 As DevExpress.XtraEditors.LabelControl

End Class

﻿Imports System.Linq
Imports DevExpress.Xpo

Public Class EditTreatmentAreaForm

    Public Sub New(treatmentArea As DOTreatmentArea)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        nameTextBox.DataBindings.Add("Text", treatmentArea, "Name")
        chargeMasterTextBox.DataBindings.Add("Text", treatmentArea, "ChargeMaster")
        treatmentAreaTypeComboBoxEdit.DataBindings.Add("EditValue", treatmentArea, "TreatmentAreaType")
        enabledCheckEdit.DataBindings.Add("Checked", treatmentArea, "Enabled")
    End Sub

    Private Sub saveButton_Click(sender As Object, e As EventArgs) Handles saveButton.Click
        Me.DialogResult = DialogResult.OK
        Me.Close()
    End Sub

    Private Sub cancelButton_Click(sender As Object, e As EventArgs) Handles cancelButton.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

End Class
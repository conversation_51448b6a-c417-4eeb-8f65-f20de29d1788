Imports EnchartDOLib
Imports System.IO
Imports System.Xml

Public Class FormXMLImport

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()
        txtFacilityIDs.Text = String.Join(",", DOFacility.GetAllFacilitesAsList().Select(Function(f) f.Oid.ToString).ToArray())
    End Sub

    Private Async Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        Me.UseWaitCursor = True
        ' Run the import process

        Try
            Await Task.Run(Sub()
                               LoadDataToForm()
                           End Sub)

            DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            Me.UseWaitCursor = False
            MessageBox.Show("Something Went Wrong! " & ex.ToString)
        End Try
        Me.UseWaitCursor = False
        Me.Close()
    End Sub

    Private Sub LoadDataToForm()
        Dim adminReportsDTO As DTOAdminReports

        Using fs As New FileStream(txtReportFile.Text, FileMode.Open)
            Dim xmlSettings As New XmlReaderSettings
            xmlSettings.DtdProcessing = DtdProcessing.Prohibit

            Using reader = XmlReader.Create(fs, xmlSettings)
                Dim x As New Xml.Serialization.XmlSerializer(GetType(DTOAdminReports))
                adminReportsDTO = x.Deserialize(reader)
            End Using
        End Using

        For Each facility_id As String In txtFacilityIDs.Text.Split(",")
            Dim facility As DOFacility = DOFacility.GetFacilityByOid(facility_id)
            If ceDeleteConfig.Checked Then
                DeleteExistingCategoriesByFacility(facility)
            End If

            If facility IsNot Nothing Then
                Dim reportsUnpacker As New AdminReportsUnpacker(facility, adminReportsDTO)
                reportsUnpacker.Unpack()
            End If
        Next
    End Sub

    Private Sub DeleteExistingCategoriesByFacility(facility As DOFacility)
        For Each category As DOFacilityReportCategory In DOFacilityReportCategory.GetRootCategoriesByFacility(facility)
            category.Delete()
        Next
    End Sub

    Private Sub txtReportFile_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtReportFile.TextChanged
        btnImport.Enabled = (txtReportFile.Text <> "") And (txtFacilityIDs.Text <> "")
    End Sub

    Private Sub btnBrowse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBrowse.Click
        fileSelectFile.InitialDirectory = Application.StartupPath
        fileSelectFile.ShowDialog()
    End Sub

    Private Sub FormNewReport_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        Me.Dispose()
    End Sub

    Private Sub fileSelectFile_FileOk(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles fileSelectFile.FileOk
        txtReportFile.Text = fileSelectFile.FileName
    End Sub
End Class
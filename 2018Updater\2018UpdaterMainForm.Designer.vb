﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.ceUse2018ChargeAllocationLogic = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018CodingReport = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018ESPCodeLogic = New DevExpress.XtraEditors.CheckEdit()
        Me.lblMsg = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018EspcodeLogiclbl = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018CodingReportLogiclbl = New DevExpress.XtraEditors.LabelControl()
        Me.Use2018ObsChargeAlloclbl = New DevExpress.XtraEditors.LabelControl()
        Me.EspcodeMigrationlbl = New DevExpress.XtraEditors.LabelControl()
        Me.progressBar = New DevExpress.XtraEditors.ProgressBarControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ceCodeMigration = New DevExpress.XtraEditors.CheckEdit()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.ceConvertProviderListslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceConvertProviderLists = New DevExpress.XtraEditors.CheckEdit()
        Me.CreateRealTimeSettinglbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceCreateRealtimeChargesSetting = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.TurnOnMaintenanceModelbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceEnableMainenanceMode = New DevExpress.XtraEditors.CheckEdit()
        Me.FetalMonlbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceFetalMon = New DevExpress.XtraEditors.CheckEdit()
        Me.UpdateOvernightScriptslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceUpdateDbScripts = New DevExpress.XtraEditors.CheckEdit()
        Me.ceNcciEdits = New DevExpress.XtraEditors.CheckEdit()
        Me.progressBarNcciEdits = New DevExpress.XtraEditors.ProgressBarControl()
        Me.ncciEditslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceAddReports = New DevExpress.XtraEditors.CheckEdit()
        Me.AddReportslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceAddBlankList = New DevExpress.XtraEditors.CheckEdit()
        Me.addBlankListlbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceObsCarveOutTimes = New DevExpress.XtraEditors.CheckEdit()
        Me.ObsCarveOutTimeslbl = New DevExpress.XtraEditors.LabelControl()
        Me.ceGTubeCodes = New DevExpress.XtraEditors.CheckEdit()
        Me.GTubeCodeslbl = New DevExpress.XtraEditors.LabelControl()
        Me.btnJustDoIt = New DevExpress.XtraEditors.SimpleButton()
        Me.ceAddMissingEspcodes = New DevExpress.XtraEditors.CheckEdit()
        Me.addMissingEspcodeslbl = New DevExpress.XtraEditors.LabelControl()
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.progressBar.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceCodeMigration.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceConvertProviderLists.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceCreateRealtimeChargesSetting.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceEnableMainenanceMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFetalMon.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUpdateDbScripts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceNcciEdits.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.progressBarNcciEdits.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAddReports.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAddBlankList.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceObsCarveOutTimes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceGTubeCodes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAddMissingEspcodes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Location = New System.Drawing.Point(765, 376)
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(2)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(112, 19)
        Me.SimpleButton1.TabIndex = 2
        Me.SimpleButton1.Text = "Exit"
        '
        'ceUse2018ChargeAllocationLogic
        '
        Me.ceUse2018ChargeAllocationLogic.EditValue = True
        Me.ceUse2018ChargeAllocationLogic.Location = New System.Drawing.Point(9, 99)
        Me.ceUse2018ChargeAllocationLogic.Name = "ceUse2018ChargeAllocationLogic"
        Me.ceUse2018ChargeAllocationLogic.Properties.AllowFocused = False
        Me.ceUse2018ChargeAllocationLogic.Properties.Caption = "Use 2018 Obs. Charge Allocation Logic"
        Me.ceUse2018ChargeAllocationLogic.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018ChargeAllocationLogic.Size = New System.Drawing.Size(271, 22)
        Me.ceUse2018ChargeAllocationLogic.TabIndex = 16
        '
        'ceUse2018CodingReport
        '
        Me.ceUse2018CodingReport.EditValue = True
        Me.ceUse2018CodingReport.Location = New System.Drawing.Point(9, 77)
        Me.ceUse2018CodingReport.Name = "ceUse2018CodingReport"
        Me.ceUse2018CodingReport.Properties.AllowFocused = False
        Me.ceUse2018CodingReport.Properties.Caption = "Use 2018 Coding Report Logic"
        Me.ceUse2018CodingReport.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018CodingReport.Size = New System.Drawing.Size(271, 22)
        Me.ceUse2018CodingReport.TabIndex = 15
        '
        'ceUse2018ESPCodeLogic
        '
        Me.ceUse2018ESPCodeLogic.EditValue = True
        Me.ceUse2018ESPCodeLogic.Location = New System.Drawing.Point(9, 55)
        Me.ceUse2018ESPCodeLogic.Name = "ceUse2018ESPCodeLogic"
        Me.ceUse2018ESPCodeLogic.Properties.AllowFocused = False
        Me.ceUse2018ESPCodeLogic.Properties.Caption = "Use 2018 ESPCode Logic"
        Me.ceUse2018ESPCodeLogic.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUse2018ESPCodeLogic.Size = New System.Drawing.Size(271, 22)
        Me.ceUse2018ESPCodeLogic.TabIndex = 14
        '
        'lblMsg
        '
        Me.lblMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblMsg.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.2!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblMsg.Appearance.Options.UseFont = True
        Me.lblMsg.Appearance.Options.UseTextOptions = True
        Me.lblMsg.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.lblMsg.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.lblMsg.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.lblMsg.AutoEllipsis = True
        Me.lblMsg.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.lblMsg.CausesValidation = False
        Me.lblMsg.Cursor = System.Windows.Forms.Cursors.WaitCursor
        Me.lblMsg.Location = New System.Drawing.Point(9, 400)
        Me.lblMsg.Margin = New System.Windows.Forms.Padding(2, 3, 2, 3)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(772, 21)
        Me.lblMsg.TabIndex = 17
        Me.lblMsg.Text = "Click Process To Begin"
        Me.lblMsg.UseMnemonic = False
        Me.lblMsg.UseWaitCursor = True
        '
        'Use2018EspcodeLogiclbl
        '
        Me.Use2018EspcodeLogiclbl.Location = New System.Drawing.Point(285, 57)
        Me.Use2018EspcodeLogiclbl.Margin = New System.Windows.Forms.Padding(2)
        Me.Use2018EspcodeLogiclbl.Name = "Use2018EspcodeLogiclbl"
        Me.Use2018EspcodeLogiclbl.Size = New System.Drawing.Size(24, 13)
        Me.Use2018EspcodeLogiclbl.TabIndex = 18
        Me.Use2018EspcodeLogiclbl.Text = "......"
        '
        'Use2018CodingReportLogiclbl
        '
        Me.Use2018CodingReportLogiclbl.Location = New System.Drawing.Point(285, 79)
        Me.Use2018CodingReportLogiclbl.Margin = New System.Windows.Forms.Padding(2)
        Me.Use2018CodingReportLogiclbl.Name = "Use2018CodingReportLogiclbl"
        Me.Use2018CodingReportLogiclbl.Size = New System.Drawing.Size(24, 13)
        Me.Use2018CodingReportLogiclbl.TabIndex = 19
        Me.Use2018CodingReportLogiclbl.Text = "......"
        '
        'Use2018ObsChargeAlloclbl
        '
        Me.Use2018ObsChargeAlloclbl.Location = New System.Drawing.Point(285, 101)
        Me.Use2018ObsChargeAlloclbl.Margin = New System.Windows.Forms.Padding(2)
        Me.Use2018ObsChargeAlloclbl.Name = "Use2018ObsChargeAlloclbl"
        Me.Use2018ObsChargeAlloclbl.Size = New System.Drawing.Size(24, 13)
        Me.Use2018ObsChargeAlloclbl.TabIndex = 20
        Me.Use2018ObsChargeAlloclbl.Text = "......"
        '
        'EspcodeMigrationlbl
        '
        Me.EspcodeMigrationlbl.Location = New System.Drawing.Point(285, 299)
        Me.EspcodeMigrationlbl.Margin = New System.Windows.Forms.Padding(2)
        Me.EspcodeMigrationlbl.Name = "EspcodeMigrationlbl"
        Me.EspcodeMigrationlbl.Size = New System.Drawing.Size(24, 13)
        Me.EspcodeMigrationlbl.TabIndex = 22
        Me.EspcodeMigrationlbl.Text = "......"
        '
        'progressBar
        '
        Me.progressBar.Location = New System.Drawing.Point(428, 301)
        Me.progressBar.Margin = New System.Windows.Forms.Padding(2)
        Me.progressBar.Name = "progressBar"
        Me.progressBar.Properties.EndColor = System.Drawing.Color.Green
        Me.progressBar.Properties.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Style3D
        Me.progressBar.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.progressBar.Properties.StartColor = System.Drawing.Color.Red
        Me.progressBar.Size = New System.Drawing.Size(441, 21)
        Me.progressBar.TabIndex = 23
        Me.progressBar.Visible = False
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(285, 10)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(2)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(99, 13)
        Me.LabelControl1.TabIndex = 24
        Me.LabelControl1.Text = "... Trying to connect"
        '
        'ceCodeMigration
        '
        Me.ceCodeMigration.EditValue = True
        Me.ceCodeMigration.Location = New System.Drawing.Point(9, 297)
        Me.ceCodeMigration.Name = "ceCodeMigration"
        Me.ceCodeMigration.Properties.AllowFocused = False
        Me.ceCodeMigration.Properties.Caption = "2018 Espcode Migration"
        Me.ceCodeMigration.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceCodeMigration.Size = New System.Drawing.Size(271, 22)
        Me.ceCodeMigration.TabIndex = 25
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.BarAnimationElementThickness = 2
        Me.ProgressPanel1.Location = New System.Drawing.Point(686, 47)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(2)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.ShowDescription = False
        Me.ProgressPanel1.Size = New System.Drawing.Size(184, 31)
        Me.ProgressPanel1.TabIndex = 26
        Me.ProgressPanel1.Text = "ProgressPanel1"
        Me.ProgressPanel1.Visible = False
        '
        'ceConvertProviderListslbl
        '
        Me.ceConvertProviderListslbl.Location = New System.Drawing.Point(285, 189)
        Me.ceConvertProviderListslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.ceConvertProviderListslbl.Name = "ceConvertProviderListslbl"
        Me.ceConvertProviderListslbl.Size = New System.Drawing.Size(24, 13)
        Me.ceConvertProviderListslbl.TabIndex = 28
        Me.ceConvertProviderListslbl.Text = "......"
        '
        'ceConvertProviderLists
        '
        Me.ceConvertProviderLists.EditValue = True
        Me.ceConvertProviderLists.Location = New System.Drawing.Point(9, 187)
        Me.ceConvertProviderLists.Name = "ceConvertProviderLists"
        Me.ceConvertProviderLists.Properties.AllowFocused = False
        Me.ceConvertProviderLists.Properties.Caption = "Convert Provider Lists"
        Me.ceConvertProviderLists.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceConvertProviderLists.Size = New System.Drawing.Size(271, 22)
        Me.ceConvertProviderLists.TabIndex = 27
        '
        'CreateRealTimeSettinglbl
        '
        Me.CreateRealTimeSettinglbl.Location = New System.Drawing.Point(285, 167)
        Me.CreateRealTimeSettinglbl.Margin = New System.Windows.Forms.Padding(2)
        Me.CreateRealTimeSettinglbl.Name = "CreateRealTimeSettinglbl"
        Me.CreateRealTimeSettinglbl.Size = New System.Drawing.Size(24, 13)
        Me.CreateRealTimeSettinglbl.TabIndex = 30
        Me.CreateRealTimeSettinglbl.Text = "......"
        '
        'ceCreateRealtimeChargesSetting
        '
        Me.ceCreateRealtimeChargesSetting.EditValue = True
        Me.ceCreateRealtimeChargesSetting.Location = New System.Drawing.Point(9, 165)
        Me.ceCreateRealtimeChargesSetting.Name = "ceCreateRealtimeChargesSetting"
        Me.ceCreateRealtimeChargesSetting.Properties.AllowFocused = False
        Me.ceCreateRealtimeChargesSetting.Properties.Caption = "Create RealTime Charges Setting"
        Me.ceCreateRealtimeChargesSetting.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceCreateRealtimeChargesSetting.Size = New System.Drawing.Size(271, 22)
        Me.ceCreateRealtimeChargesSetting.TabIndex = 29
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(10, 10)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(2)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(92, 13)
        Me.LabelControl2.TabIndex = 32
        Me.LabelControl2.Text = "Connection String: "
        '
        'TurnOnMaintenanceModelbl
        '
        Me.TurnOnMaintenanceModelbl.Location = New System.Drawing.Point(285, 35)
        Me.TurnOnMaintenanceModelbl.Margin = New System.Windows.Forms.Padding(2)
        Me.TurnOnMaintenanceModelbl.Name = "TurnOnMaintenanceModelbl"
        Me.TurnOnMaintenanceModelbl.Size = New System.Drawing.Size(24, 13)
        Me.TurnOnMaintenanceModelbl.TabIndex = 34
        Me.TurnOnMaintenanceModelbl.Text = "......"
        '
        'ceEnableMainenanceMode
        '
        Me.ceEnableMainenanceMode.EditValue = True
        Me.ceEnableMainenanceMode.Location = New System.Drawing.Point(9, 33)
        Me.ceEnableMainenanceMode.Name = "ceEnableMainenanceMode"
        Me.ceEnableMainenanceMode.Properties.AllowFocused = False
        Me.ceEnableMainenanceMode.Properties.Caption = "Turn On Maintenance Mode"
        Me.ceEnableMainenanceMode.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceEnableMainenanceMode.Size = New System.Drawing.Size(271, 22)
        Me.ceEnableMainenanceMode.TabIndex = 33
        '
        'FetalMonlbl
        '
        Me.FetalMonlbl.Location = New System.Drawing.Point(285, 123)
        Me.FetalMonlbl.Margin = New System.Windows.Forms.Padding(2)
        Me.FetalMonlbl.Name = "FetalMonlbl"
        Me.FetalMonlbl.Size = New System.Drawing.Size(24, 13)
        Me.FetalMonlbl.TabIndex = 36
        Me.FetalMonlbl.Text = "......"
        '
        'ceFetalMon
        '
        Me.ceFetalMon.EditValue = True
        Me.ceFetalMon.Location = New System.Drawing.Point(9, 121)
        Me.ceFetalMon.Name = "ceFetalMon"
        Me.ceFetalMon.Properties.AllowFocused = False
        Me.ceFetalMon.Properties.Caption = "Update Description for Mon_Fetal Espocodes"
        Me.ceFetalMon.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceFetalMon.Size = New System.Drawing.Size(271, 22)
        Me.ceFetalMon.TabIndex = 35
        '
        'UpdateOvernightScriptslbl
        '
        Me.UpdateOvernightScriptslbl.Location = New System.Drawing.Point(285, 145)
        Me.UpdateOvernightScriptslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.UpdateOvernightScriptslbl.Name = "UpdateOvernightScriptslbl"
        Me.UpdateOvernightScriptslbl.Size = New System.Drawing.Size(24, 13)
        Me.UpdateOvernightScriptslbl.TabIndex = 38
        Me.UpdateOvernightScriptslbl.Text = "......"
        '
        'ceUpdateDbScripts
        '
        Me.ceUpdateDbScripts.EditValue = True
        Me.ceUpdateDbScripts.Location = New System.Drawing.Point(9, 143)
        Me.ceUpdateDbScripts.Name = "ceUpdateDbScripts"
        Me.ceUpdateDbScripts.Properties.AllowFocused = False
        Me.ceUpdateDbScripts.Properties.Caption = "Update Overnight Reports DB Scripts"
        Me.ceUpdateDbScripts.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceUpdateDbScripts.Size = New System.Drawing.Size(271, 22)
        Me.ceUpdateDbScripts.TabIndex = 37
        '
        'ceNcciEdits
        '
        Me.ceNcciEdits.EditValue = True
        Me.ceNcciEdits.Location = New System.Drawing.Point(9, 275)
        Me.ceNcciEdits.Name = "ceNcciEdits"
        Me.ceNcciEdits.Properties.AllowFocused = False
        Me.ceNcciEdits.Properties.Caption = "Load NCCI Edits"
        Me.ceNcciEdits.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceNcciEdits.Size = New System.Drawing.Size(271, 22)
        Me.ceNcciEdits.TabIndex = 41
        '
        'progressBarNcciEdits
        '
        Me.progressBarNcciEdits.Location = New System.Drawing.Point(428, 279)
        Me.progressBarNcciEdits.Margin = New System.Windows.Forms.Padding(2)
        Me.progressBarNcciEdits.Name = "progressBarNcciEdits"
        Me.progressBarNcciEdits.Properties.EndColor = System.Drawing.Color.Green
        Me.progressBarNcciEdits.Properties.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Style3D
        Me.progressBarNcciEdits.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.progressBarNcciEdits.Properties.StartColor = System.Drawing.Color.Red
        Me.progressBarNcciEdits.Size = New System.Drawing.Size(441, 21)
        Me.progressBarNcciEdits.TabIndex = 40
        Me.progressBarNcciEdits.Visible = False
        '
        'ncciEditslbl
        '
        Me.ncciEditslbl.Location = New System.Drawing.Point(285, 277)
        Me.ncciEditslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.ncciEditslbl.Name = "ncciEditslbl"
        Me.ncciEditslbl.Size = New System.Drawing.Size(24, 13)
        Me.ncciEditslbl.TabIndex = 39
        Me.ncciEditslbl.Text = "......"
        '
        'ceAddReports
        '
        Me.ceAddReports.EditValue = True
        Me.ceAddReports.Location = New System.Drawing.Point(9, 209)
        Me.ceAddReports.Name = "ceAddReports"
        Me.ceAddReports.Properties.AllowFocused = False
        Me.ceAddReports.Properties.Caption = "Add Reports"
        Me.ceAddReports.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceAddReports.Size = New System.Drawing.Size(271, 22)
        Me.ceAddReports.TabIndex = 43
        '
        'AddReportslbl
        '
        Me.AddReportslbl.Location = New System.Drawing.Point(285, 211)
        Me.AddReportslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.AddReportslbl.Name = "AddReportslbl"
        Me.AddReportslbl.Size = New System.Drawing.Size(24, 13)
        Me.AddReportslbl.TabIndex = 42
        Me.AddReportslbl.Text = "......"
        '
        'ceAddBlankList
        '
        Me.ceAddBlankList.EditValue = True
        Me.ceAddBlankList.Location = New System.Drawing.Point(9, 231)
        Me.ceAddBlankList.Name = "ceAddBlankList"
        Me.ceAddBlankList.Properties.AllowFocused = False
        Me.ceAddBlankList.Properties.Caption = "Add ExceptionOverrideRequired List"
        Me.ceAddBlankList.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceAddBlankList.Size = New System.Drawing.Size(271, 22)
        Me.ceAddBlankList.TabIndex = 45
        '
        'addBlankListlbl
        '
        Me.addBlankListlbl.Location = New System.Drawing.Point(285, 233)
        Me.addBlankListlbl.Margin = New System.Windows.Forms.Padding(2)
        Me.addBlankListlbl.Name = "addBlankListlbl"
        Me.addBlankListlbl.Size = New System.Drawing.Size(24, 13)
        Me.addBlankListlbl.TabIndex = 44
        Me.addBlankListlbl.Text = "......"
        '
        'ceObsCarveOutTimes
        '
        Me.ceObsCarveOutTimes.EditValue = True
        Me.ceObsCarveOutTimes.Location = New System.Drawing.Point(9, 253)
        Me.ceObsCarveOutTimes.Name = "ceObsCarveOutTimes"
        Me.ceObsCarveOutTimes.Properties.AllowFocused = False
        Me.ceObsCarveOutTimes.Properties.Caption = "Migrate Obs Carve Out Times Lists"
        Me.ceObsCarveOutTimes.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceObsCarveOutTimes.Size = New System.Drawing.Size(271, 22)
        Me.ceObsCarveOutTimes.TabIndex = 47
        '
        'ObsCarveOutTimeslbl
        '
        Me.ObsCarveOutTimeslbl.Location = New System.Drawing.Point(285, 255)
        Me.ObsCarveOutTimeslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.ObsCarveOutTimeslbl.Name = "ObsCarveOutTimeslbl"
        Me.ObsCarveOutTimeslbl.Size = New System.Drawing.Size(24, 13)
        Me.ObsCarveOutTimeslbl.TabIndex = 46
        Me.ObsCarveOutTimeslbl.Text = "......"
        '
        'ceGTubeCodes
        '
        Me.ceGTubeCodes.EditValue = True
        Me.ceGTubeCodes.Location = New System.Drawing.Point(10, 321)
        Me.ceGTubeCodes.Name = "ceGTubeCodes"
        Me.ceGTubeCodes.Properties.AllowFocused = False
        Me.ceGTubeCodes.Properties.Caption = "GTube - Add new db records"
        Me.ceGTubeCodes.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceGTubeCodes.Size = New System.Drawing.Size(271, 22)
        Me.ceGTubeCodes.TabIndex = 49
        '
        'GTubeCodeslbl
        '
        Me.GTubeCodeslbl.Location = New System.Drawing.Point(286, 323)
        Me.GTubeCodeslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.GTubeCodeslbl.Name = "GTubeCodeslbl"
        Me.GTubeCodeslbl.Size = New System.Drawing.Size(24, 13)
        Me.GTubeCodeslbl.TabIndex = 48
        Me.GTubeCodeslbl.Text = "......"
        '
        'btnJustDoIt
        '
        Me.btnJustDoIt.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnJustDoIt.Location = New System.Drawing.Point(402, 376)
        Me.btnJustDoIt.Margin = New System.Windows.Forms.Padding(2)
        Me.btnJustDoIt.Name = "btnJustDoIt"
        Me.btnJustDoIt.Size = New System.Drawing.Size(112, 19)
        Me.btnJustDoIt.TabIndex = 1
        Me.btnJustDoIt.Text = "Process"
        '
        'ceAddMissingEspcodes
        '
        Me.ceAddMissingEspcodes.EditValue = True
        Me.ceAddMissingEspcodes.Location = New System.Drawing.Point(9, 344)
        Me.ceAddMissingEspcodes.Name = "ceAddMissingEspcodes"
        Me.ceAddMissingEspcodes.Properties.AllowFocused = False
        Me.ceAddMissingEspcodes.Properties.Caption = "Add Missing Espcodes"
        Me.ceAddMissingEspcodes.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style1
        Me.ceAddMissingEspcodes.Size = New System.Drawing.Size(271, 22)
        Me.ceAddMissingEspcodes.TabIndex = 51
        '
        'addMissingEspcodeslbl
        '
        Me.addMissingEspcodeslbl.Location = New System.Drawing.Point(285, 346)
        Me.addMissingEspcodeslbl.Margin = New System.Windows.Forms.Padding(2)
        Me.addMissingEspcodeslbl.Name = "addMissingEspcodeslbl"
        Me.addMissingEspcodeslbl.Size = New System.Drawing.Size(24, 13)
        Me.addMissingEspcodeslbl.TabIndex = 50
        Me.addMissingEspcodeslbl.Text = "......"
        '
        'Form1
        '
        Me.AcceptButton = Me.btnJustDoIt
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(888, 425)
        Me.Controls.Add(Me.ceAddMissingEspcodes)
        Me.Controls.Add(Me.addMissingEspcodeslbl)
        Me.Controls.Add(Me.btnJustDoIt)
        Me.Controls.Add(Me.ceGTubeCodes)
        Me.Controls.Add(Me.GTubeCodeslbl)
        Me.Controls.Add(Me.ceObsCarveOutTimes)
        Me.Controls.Add(Me.ObsCarveOutTimeslbl)
        Me.Controls.Add(Me.ceAddBlankList)
        Me.Controls.Add(Me.addBlankListlbl)
        Me.Controls.Add(Me.ceAddReports)
        Me.Controls.Add(Me.AddReportslbl)
        Me.Controls.Add(Me.ceNcciEdits)
        Me.Controls.Add(Me.progressBarNcciEdits)
        Me.Controls.Add(Me.ncciEditslbl)
        Me.Controls.Add(Me.UpdateOvernightScriptslbl)
        Me.Controls.Add(Me.ceUpdateDbScripts)
        Me.Controls.Add(Me.FetalMonlbl)
        Me.Controls.Add(Me.ceFetalMon)
        Me.Controls.Add(Me.TurnOnMaintenanceModelbl)
        Me.Controls.Add(Me.ceEnableMainenanceMode)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.CreateRealTimeSettinglbl)
        Me.Controls.Add(Me.ceCreateRealtimeChargesSetting)
        Me.Controls.Add(Me.ceConvertProviderListslbl)
        Me.Controls.Add(Me.ceConvertProviderLists)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.Controls.Add(Me.ceCodeMigration)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.progressBar)
        Me.Controls.Add(Me.EspcodeMigrationlbl)
        Me.Controls.Add(Me.Use2018ObsChargeAlloclbl)
        Me.Controls.Add(Me.Use2018CodingReportLogiclbl)
        Me.Controls.Add(Me.Use2018EspcodeLogiclbl)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.ceUse2018ChargeAllocationLogic)
        Me.Controls.Add(Me.ceUse2018CodingReport)
        Me.Controls.Add(Me.ceUse2018ESPCodeLogic)
        Me.Controls.Add(Me.SimpleButton1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Margin = New System.Windows.Forms.Padding(2)
        Me.Name = "Form1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "2018 Config Update Utility 8.0"
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.progressBar.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceCodeMigration.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceConvertProviderLists.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceCreateRealtimeChargesSetting.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceEnableMainenanceMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFetalMon.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUpdateDbScripts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceNcciEdits.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.progressBarNcciEdits.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAddReports.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAddBlankList.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceObsCarveOutTimes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceGTubeCodes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAddMissingEspcodes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents ButtonEdit1 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ceUse2018ChargeAllocationLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018CodingReport As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ESPCodeLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents lblMsg As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018EspcodeLogiclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018CodingReportLogiclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Use2018ObsChargeAlloclbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents EspcodeMigrationlbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents progressBar As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceCodeMigration As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents ceConvertProviderListslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceConvertProviderLists As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CreateRealTimeSettinglbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceCreateRealtimeChargesSetting As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TurnOnMaintenanceModelbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceEnableMainenanceMode As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FetalMonlbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceFetalMon As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents UpdateOvernightScriptslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceUpdateDbScripts As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceNcciEdits As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents progressBarNcciEdits As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents ncciEditslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceAddReports As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents AddReportslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceAddBlankList As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents addBlankListlbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceObsCarveOutTimes As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ObsCarveOutTimeslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceGTubeCodes As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GTubeCodeslbl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnJustDoIt As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ceAddMissingEspcodes As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents addMissingEspcodeslbl As DevExpress.XtraEditors.LabelControl
End Class

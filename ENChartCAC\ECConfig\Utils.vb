﻿'Imports EnchartDOLib
Imports DevExpress.Xpo
Imports System.Collections.Specialized
Imports ECConfig.MainForm
Imports System.Threading.Tasks
Imports System.Linq
Imports ENChartCAC
Imports System.Data

Module Utils
    Private deleteEspcodesProgressAmount As Integer = 2
    Private addEspcodesProgressAmount As Integer = 2
    Private addCDMProgressAmount As Integer = 70
    Private addComboBoxesProgressAmount As Integer = 15

    Public Delegate Sub Callback(ByVal percentDone As Integer)

    Public Sub UpdateProgress(progress As Integer)
        ProgressIndicator.Report(progress)
    End Sub

    Private ProgressIndicator As IProgress(Of Integer)

    ''' <summary>
    '''
    ''' </summary>
    ''' <remarks> Note, if onlyLoadTAs is true, that means we are just trying to copy a treatment area using
    ''' the existing facility copy logic (with new tweaks)
    ''' </remarks>
    Public Sub LoadConfigFromProxy(ByVal facility As DOFacility,
                                   ByVal CI As DOConfigInstance,
                                   ByVal fp As FacilityProxy,
                                   pProgress As IProgress(Of Integer),
                                   Optional ByVal bLoadCDMTables As Boolean = True,
                                   Optional ByVal bLoadReports As Boolean = False,
                                   Optional onlyLoadTAs As Boolean = False,
                                   Optional sourceTAName As String = Nothing)

        ProgressIndicator = pProgress
        Dim tw As New Stopwatch
        tw.Start()
        Dim progress As Double = 0
        Dim defaultProgressIncrementAmount As Short = 2

        If onlyLoadTAs Then
            progress = 10
        End If

        UpdateProgress(progress)
        If Not onlyLoadTAs Then 'no need to do this if only copying a treatment area
            CI.ActiveFormClassName = fp.FormVersion
            CI.Save()
        End If

        progress = fcl_ProcessEspcodes(facility, fp, onlyLoadTAs, sourceTAName, progress, tw)

        Debug.WriteLine($"TW 5a: {tw.Elapsed.TotalSeconds}")

        fcl_DeleteCreateFacilitySettingsRecords(facility, fp, tw)

        Debug.WriteLine($"TW 5b: {tw.Elapsed.TotalSeconds}")

        fcl_CreateUpdateTreatmentAreas(facility, fp)

        If bLoadCDMTables Then
            progress = fcl_CreateChargeMasterRecords(facility, fp, progress)
        End If

        Debug.WriteLine($"TW 6: {tw.Elapsed.TotalSeconds}")

        If onlyLoadTAs Then
            Return
        End If

        fcl_CreateUserFacilitySpecificSettings(facility, fp)
        Debug.WriteLine($"TW 8: {tw.Elapsed.TotalSeconds}")

        If bLoadReports Then
            Dim reportsUnpacker As New AdminReportsUnpacker(facility, fp.DtoAdminReports)
            reportsUnpacker.Unpack()
        End If
        Debug.WriteLine($"TW 9: {tw.Elapsed.TotalSeconds}")

        '-------------Control overrides -------------
        progress = fcl_CreateControlOverridesForCI(CI, fp, progress, defaultProgressIncrementAmount, tw)

        progress = fcl_CreateConfigSettings(CI, fp, progress, defaultProgressIncrementAmount)
        Debug.WriteLine($"TW 11: {tw.Elapsed.TotalSeconds}")

        '---------- Load ComboBoxes -----------------
        progress = fcl_CreateComboBoxLists(facility, CI, fp, progress, tw)
        Debug.WriteLine($"TW 12: {tw.Elapsed.TotalSeconds}")

        '------------Load CatMap --------------------
        progress = fcl_CreateSupplyCategoryMappings(CI, fp, progress, defaultProgressIncrementAmount)
        Debug.WriteLine($"TW 13: {tw.Elapsed.TotalSeconds}")

        '--------Load CodingReportTexts---------------
        progress = fcl_DeleteCreateCodingReportTextRecords(CI, fp, progress, defaultProgressIncrementAmount)
        UpdateProgress(progress)

        '--------Load PhysicianCodesFilter---------------
        progress = fcl_DeleteCreatePhysicianCodesFilterRecords(CI, fp, progress, defaultProgressIncrementAmount, tw)
        UpdateProgress(progress)

        ''DeleteList(Of DOModifierGroups)(DOModifierGroups.GetModifierGroups)
        'Dim dl As List(Of DOModifierGroups) = DOModifierGroups.GetModifierGroups
        'If dl IsNot Nothing Then
        '    For i As Integer = 0 To dl.Count - 1
        '        dl(i).Delete()
        '    Next

        'End If
        'Debug.WriteLine($"TW 15: {tw.Elapsed.TotalSeconds}")
        'If fp.ModifierGroups IsNot Nothing Then
        '    For Each mg In fp.ModifierGroups
        '        Dim doMG As New DOModifierGroups(mg)
        '        doMG.Save()
        '    Next
        'End If
        Debug.WriteLine($"TW 16: {tw.Elapsed.TotalSeconds}")

        fcl_DeleteCreateCISpecificMedications(CI, fp)

        Debug.WriteLine($"TW 17: {tw.Elapsed.TotalSeconds}")

        fcl_DeleteCreateDeductedTimes(CI, fp)
        Debug.WriteLine($"TW 18: {tw.Elapsed.TotalSeconds}")

        fcl_DeleteCreateFinancialClassMappings(CI, fp)

        Debug.WriteLine($"TW 19: {tw.Elapsed.TotalSeconds}")

        progress += defaultProgressIncrementAmount
        UpdateProgress(100)
        Debug.WriteLine($"TW Done: {tw.Elapsed.TotalSeconds}")
    End Sub

    Private Sub fcl_CreateUpdateTreatmentAreas(facility As DOFacility, fp As FacilityProxy)
        '-------------TreatmentAreas and Nursing Units -------------
        Dim dotaList = GetTreatmentAreasByFacility(facility)
        Dim dotaDic As New Dictionary(Of String, DOTreatmentArea)
        For Each dotaItem In dotaList
            dotaDic(dotaItem.Name) = dotaItem
        Next
        For Each ta In fp.TreatmentAreas
            Dim dota As DOTreatmentArea
            If dotaDic.Keys.Contains(ta.TreatmentArea) Then
                dota = dotaDic(ta.TreatmentArea)
            Else
                dota = New DOTreatmentArea() With {
                    .Name = ta.TreatmentArea,
                    .Facility = facility,
                    .ChargeMaster = ta.ChargeMaster,
                    .Enabled = ta.Enabled}
            End If
            dota.Save()
        Next
    End Sub

    Private Sub fcl_DeleteCreateFacilitySettingsRecords(facility As DOFacility, fp As FacilityProxy, tw As Stopwatch)
        fcl_DeleteFacilitySettingsForEachTA(facility, fp)
        fcl_CreateFacilitySettingsForEachTA(facility, fp)
        fcl_CreateFacilitySettingsRecordsForNullTA(facility, fp, tw)
    End Sub

    Private Function fcl_DeleteEspCodes(
            facility As DOFacility,
            fp As FacilityProxy,
            onlyLoadTAs As Boolean,
            progress As Double,
            tw As Stopwatch) As Double

        Dim recordCountToDeleteFromTargetDb As Integer
        If ECGlobals.Use2018EspcodeLogic Then
            recordCountToDeleteFromTargetDb = fp.EspCodes2018?.Count
        Else
            For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                Dim esplist As List(Of DOESPCode) = DOESPCode.GetESPCodesList(facility.FacilityID, TA.TreatmentArea)
                If esplist Is Nothing Then Continue For
                recordCountToDeleteFromTargetDb += esplist.Count
            Next
        End If
        Debug.WriteLine($"TW 2: {tw.Elapsed.TotalSeconds}")

        'Delete existing records as slowly as possible...
        If recordCountToDeleteFromTargetDb > 0 Then
            Dim step1 As Double = deleteEspcodesProgressAmount / recordCountToDeleteFromTargetDb
            If ECGlobals.Use2018EspcodeLogic Then
                If Not onlyLoadTAs Then
                    Delete2018EspcodesByFacility(facility.Oid)
                    progress += deleteEspcodesProgressAmount
                    UpdateProgress(progress)
                End If
            Else

                For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                    Dim esplist As List(Of DOESPCode) = DOESPCode.GetESPCodesList(facility.FacilityID, TA.TreatmentArea)
                    If esplist Is Nothing Then Continue For
                    For r As Integer = 0 To esplist.Count - 1
                        esplist(r).Delete()
                        progress += step1
                        UpdateProgress(progress)
                    Next

                Next
            End If
        End If

        Debug.WriteLine($"TW 3: {tw.Elapsed.TotalSeconds}")
        'There were no espcodes to delete in target facility, but increment counter anywho...
        If recordCountToDeleteFromTargetDb <= 1 Then
            progress += deleteEspcodesProgressAmount
            UpdateProgress(progress)
        End If

        Return progress
    End Function

    Private Sub fcl_DeleteFacilitySettingsForEachTA(facility As DOFacility, fp As FacilityProxy)
        If fp.TreatmentAreas IsNot Nothing Then
            For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                Dim facSettings = DOFacilitySettings.GetFacilitySettings(facility, TA.TreatmentArea)
                If facSettings Is Nothing Then Continue For
                For r2 As Integer = 0 To facSettings.Count - 1
                    facSettings(r2).Delete()
                Next
            Next
        End If
    End Sub

    Private Function fcl_ProcessEspcodes(
            facility As DOFacility,
            fp As FacilityProxy,
            onlyLoadTAs As Boolean,
            sourceTAName As String,
            progress As Double, tw As Stopwatch) As Double

        Debug.WriteLine($"TW 1: {tw.Elapsed.TotalSeconds}")
        progress = fcl_DeleteEspCodes(facility, fp, onlyLoadTAs, progress, tw)

        Debug.WriteLine($"TW 4: {tw.Elapsed.TotalSeconds}")

        Dim espcodesCount As Integer
        For Each TA As DTOTreatmentArea In fp.TreatmentAreas
            espcodesCount += TA.ESPCodes.Count
        Next


        If ECGlobals.Use2018EspcodeLogic And Not onlyLoadTAs Then
            For Each EspcodeItem In fp.EspCodes2018
                Dim newEspCode = New DOESPCode2018(EspcodeItem) With {.Facility = facility.Oid}
                newEspCode.Save()
            Next
            progress += addEspcodesProgressAmount
        Else
            If espcodesCount > 0 Then
                Dim step2 As Double = addEspcodesProgressAmount / espcodesCount
                For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                    For Each esp In TA.ESPCodes

                        Dim doESP As New DOESPCode(esp)
                        doESP.Facility = facility.Oid
                        doESP.Facility_ID = facility.FacilityID
                        If onlyLoadTAs Then 'jjc 8.20.18 -- this seems odd
                            If Not String.IsNullOrEmpty(doESP.LongName) AndAlso Not String.IsNullOrEmpty(sourceTAName) Then
                                If doESP.LongName.StartsWith("Nursing Unit") AndAlso doESP.LongName.Contains(sourceTAName) Then
                                    doESP.LongName = doESP.LongName.Replace(sourceTAName, TA.TreatmentArea)
                                End If
                            End If
                        End If

                        doESP.TreatmentArea = TA.TreatmentArea
                        doESP.Save()

                        progress += step2
                        UpdateProgress(progress)
                    Next
                Next
            Else
                progress += addEspcodesProgressAmount
                UpdateProgress(progress)
            End If
        End If

        Return progress
    End Function

    Private Sub fcl_DeleteCreateFinancialClassMappings(CI As DOConfigInstance, fp As FacilityProxy)
        DeleteList(Of DOFinancialClassMapping)(CI.FinancialClassMappings) ''JJC 05.2014
        If fp.FinancialClassMappings IsNot Nothing Then
            For Each mapping In fp.FinancialClassMappings
                Dim doFCMapping As New DOFinancialClassMapping(mapping)
                doFCMapping.ConfigInstance = CI
                doFCMapping.Save()
            Next
        End If
    End Sub

    Private Sub fcl_DeleteCreateDeductedTimes(CI As DOConfigInstance, fp As FacilityProxy)
        DeleteList(Of DODeductedTimes)(CI.DeductedTimes) ''JJC 05.2014
        If fp.DeductedTimes IsNot Nothing Then
            For Each deductedTime In fp.DeductedTimes
                Dim doDeductedTme As New DODeductedTimes(deductedTime)
                doDeductedTme.ConfigInstance = CI
                doDeductedTme.Save()
            Next
        End If
    End Sub

    Private Sub fcl_DeleteCreateCISpecificMedications(CI As DOConfigInstance, fp As FacilityProxy)
        DeleteList(Of DOMedication)(CI.Medications) 'JJC 05.2014
        If fp.Medications IsNot Nothing Then
            For Each med In fp.Medications
                Dim doMed As New DOMedication(med)
                doMed.ConfigInstance = CI
                doMed.Save()
            Next
        End If
    End Sub

    Private Function fcl_DeleteCreatePhysicianCodesFilterRecords(CI As DOConfigInstance, fp As FacilityProxy, progress As Double, defaultProgressIncrementAmount As Short, tw As Stopwatch) As Double
        DeleteList(Of DOPhysicianCodesFilter)(CI.PhysicianCodesFilter)
        For Each pcf In fp.PhysicianCodesFilter
            Dim doPCF As New DOPhysicianCodesFilter(pcf)
            doPCF.ConfigInstance = CI
            doPCF.Save()
            CI.PhysicianCodesFilter.Add(doPCF)
        Next
        Debug.WriteLine($"TW 14: {tw.Elapsed.TotalSeconds}")
        progress += defaultProgressIncrementAmount
        Return progress
    End Function

    Private Function fcl_DeleteCreateCodingReportTextRecords(CI As DOConfigInstance, fp As FacilityProxy, progress As Double, defaultProgressIncrementAmount As Short) As Double
        DeleteList(Of DOCodingReportText)(CI.CodingReportTexts)
        For Each crt In fp.CodingReportTexts
            Dim doCodeRepText As New DOCodingReportText(crt) With {.ConfigInstance = CI}
            doCodeRepText.Save()
            CI.CodingReportTexts.Add(doCodeRepText)
        Next

        progress += defaultProgressIncrementAmount
        Return progress
    End Function

    Private Function fcl_CreateSupplyCategoryMappings(CI As DOConfigInstance, fp As FacilityProxy, progress As Double, defaultProgressIncrementAmount As Short) As Double
        DeleteList(Of DOSupplyCatMap)(CI.SupplyCatMap)

        For Each cm As DTOSupplyCatMap In fp.SupplyCatMappings
            Dim doCatMap As New DOSupplyCatMap(cm)
            doCatMap.ConfigInstance = CI
            doCatMap.Save()
            CI.SupplyCatMap.Add(doCatMap)
        Next

        progress += defaultProgressIncrementAmount
        UpdateProgress(progress)
        Return progress
    End Function

    Private Function fcl_CreateComboBoxLists(facility As DOFacility, CI As DOConfigInstance, fp As FacilityProxy, progress As Double, tw As Stopwatch) As Double
        'First Delete the old Lists...
        DeleteList(Of DOConfigComboBoxList)(CI.ComboBoxLists)

        Dim step4 = 0.0
        If fp.ComboBoxLists IsNot Nothing AndAlso fp.ComboBoxLists.Count > 0 Then
            step4 = addComboBoxesProgressAmount / fp.ComboBoxLists.Count

            For Each cbolist In fp.ComboBoxLists
                Dim doCBOList As New DOConfigComboBoxList(cbolist)
                doCBOList.ConfigInstance = CI
                doCBOList.Save()
                CI.ComboBoxLists.Add(doCBOList)

                progress += step4
                UpdateProgress(progress)
            Next
        Else
            progress += step4
            UpdateProgress(progress)
        End If

        Dim sharedCi = facility.SharedConfigInstanceVersion
        'First Delete the old Lists...
        DeleteList(Of DOConfigComboBoxList)(sharedCi.ComboBoxLists)
        Debug.WriteLine($"TW 12: {tw.Elapsed.TotalSeconds}")
        If fp.SharedComboBoxLists IsNot Nothing AndAlso fp.SharedComboBoxLists.Count > 0 Then
            '   step4 = addComboBoxesProgressAmount / fp.ComboBoxLists.Count

            For Each cbolist In fp.SharedComboBoxLists
                Dim doCBOList As New DOConfigComboBoxList(cbolist)

                doCBOList.ConfigInstance = sharedCi
                doCBOList.Save()
                sharedCi.ComboBoxLists.Add(doCBOList)

                '    progress += step4
                UpdateProgress(progress)
            Next
        Else
            '  progress += step4
            UpdateProgress(progress)
        End If

        Return progress
    End Function

    Private Function fcl_CreateConfigSettings(CI As DOConfigInstance, fp As FacilityProxy, progress As Double, defaultProgressIncrementAmount As Short) As Double
        'Config settings ....
        Dim appConfigGroup As DOConfigGroup = CI.GetConfigSettingGroup("Application")
        DeleteList(Of DOConfigSetting)(appConfigGroup.Settings)
        For Each cs As DTOConfigSetting In fp.ConfigSettings
            Dim doCS As New DOConfigSetting(cs)
            doCS.Group = appConfigGroup
            doCS.Save()
            appConfigGroup.Settings.Add(doCS)
        Next

        progress += defaultProgressIncrementAmount
        UpdateProgress(progress)
        Return progress
    End Function

    Private Function fcl_CreateControlOverridesForCI(CI As DOConfigInstance, fp As FacilityProxy, progress As Double, defaultProgressIncrementAmount As Short, tw As Stopwatch) As Double

        DeleteList(Of DOControlConfigGroup)(CI.ControlOverrides) 'LOL it's so clear!
        For Each dtoCG As DTOControlConfigGroup In fp.ControlOverrides
            Dim doCG As New DOControlConfigGroup(dtoCG) With {.ConfigInstance = CI}
            doCG.Save()
            CI.ControlOverrides.Add(doCG)
        Next
        Debug.WriteLine($"TW 10: {tw.Elapsed.TotalSeconds}")
        progress += defaultProgressIncrementAmount
        UpdateProgress(progress)
        Return progress
    End Function

    Private Sub fcl_CreateUserFacilitySpecificSettings(facility As DOFacility, fp As FacilityProxy)
        If fp.Users IsNot Nothing Then
            For Each _dtoUser In fp.Users
                Dim _doUser = DOUser.GetUserByIdAndDomain(_dtoUser.UserID, _dtoUser.DomainName)
                If _doUser IsNot Nothing Then
                    For Each role In _dtoUser.Permissions
                        _doUser.AddRole(role, facility)
                    Next

                    For Each setting In _dtoUser.FacilitySpecificSetting
                        _doUser.AddFacilitySpecificSetting(setting, facility)
                    Next
                End If
            Next
        End If
    End Sub

    Private Sub fcl_CreateFacilitySettingsRecordsForNullTA(facility As DOFacility, fp As FacilityProxy, tw As Stopwatch)
        Dim facSettings2 = DOFacilitySettings.GetFacilitySettingsWithEmptyTA(facility)
        If facSettings2 IsNot Nothing Then 'Continue For
            For r2 As Integer = 0 To facSettings2.Count - 1
                facSettings2(r2).Delete()
            Next
        End If
        Debug.WriteLine($"TW 7: {tw.Elapsed.TotalSeconds}")
        'Create these facilitySettings Records that didnt' have a treatmentArea
        If fp.FacilityAllTa.Count > 0 Then
            For Each fSet In fp.FacilityAllTa
                Dim doFacSet As New DOFacilitySettings(fSet)
                doFacSet.Facility = facility.Oid
                doFacSet.FacilityID = facility.FacilityID
                doFacSet.Save()
            Next
        End If
    End Sub

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <param name="facility"></param>
    ''' <param name="fp"></param>
    ''' <param name="progress"></param>
    ''' <returns></returns>
    ''' <TODO> ??? Add logic to update or delete records prior ???</TODO>
    Private Function fcl_CreateChargeMasterRecords(facility As DOFacility, fp As FacilityProxy, progress As Double) As Double
        Dim chargeMasterList As New List(Of DOChargeMaster)(2000)
        Dim cdmRecordCount As Integer = 0

        If fp.TreatmentAreas IsNot Nothing Then
            For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                cdmRecordCount += TA.CDMs.Count
            Next
        End If

        If cdmRecordCount > 0 Then
            Dim step3 As Double = addCDMProgressAmount / cdmRecordCount
            For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                DeleteChargeMasterByFacilityAndTa(facility.Oid, TA.TreatmentArea)
                For Each cdm In TA.CDMs
                    Dim newChargeMast As New DOChargeMaster(cdm) With {
                        .Facility = facility,
                       .TreatmentArea = TA.TreatmentArea
                       }

                    'newChargeMast.Save()
                    chargeMasterList.Add(newChargeMast)
                    If chargeMasterList.Count > 2000 Then
                        XpoDefault.Session.Save(chargeMasterList)
                        chargeMasterList.Clear()
                    End If
                    progress += step3
                    UpdateProgress(progress)
                Next
            Next
        Else
            progress += addCDMProgressAmount
            UpdateProgress(progress)
        End If

        Return progress
    End Function

    Private Sub fcl_CreateFacilitySettingsForEachTA(facility As DOFacility, fp As FacilityProxy)
        If fp.TreatmentAreas IsNot Nothing Then
            For Each TA As DTOTreatmentArea In fp.TreatmentAreas
                For Each fset In TA.FacilitySettings
                    Dim doFacSet As New DOFacilitySettings(fset)
                    doFacSet.Facility = facility.Oid
                    doFacSet.FacilityID = facility.FacilityID
                    doFacSet.TreatmentArea = TA.TreatmentArea
                    doFacSet.Save()
                Next
            Next
        End If

    End Sub

    Private Sub DeleteChargeMasterByFacilityAndTa(ByVal facilityID As Integer, treatmentArea As String)
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            ' Parameterized query to prevent SQL injection
            cmd.CommandText = "DELETE FROM DOChargeMaster WHERE Facility = @facilityID AND TreatmentArea = @treatmentArea"

            ' Create parameters
            Dim facilityParam As IDbDataParameter = cmd.CreateParameter()
            facilityParam.ParameterName = "@facilityID"
            facilityParam.Value = facilityID
            cmd.Parameters.Add(facilityParam)

            Dim treatmentAreaParam As IDbDataParameter = cmd.CreateParameter()
            treatmentAreaParam.ParameterName = "@treatmentArea"
            treatmentAreaParam.Value = treatmentArea
            cmd.Parameters.Add(treatmentAreaParam)

            ' Open the connection, execute the command, and close the connection
            cmd.Connection = XpoDefault.Session.Connection
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    Private Sub Delete2018EspcodesByFacility(ByVal facilityID As Integer)
        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
            ' Use a parameterized query
            cmd.CommandText = "DELETE FROM DOESPcode2018 WHERE Facility = @facilityID"

            ' Create and add the parameter
            Dim facilityParam As IDbDataParameter = cmd.CreateParameter()
            facilityParam.ParameterName = "@facilityID"
            facilityParam.Value = facilityID
            cmd.Parameters.Add(facilityParam)

            ' Open the connection, execute the command, and close the connection
            cmd.Connection = XpoDefault.Session.Connection
            cmd.ExecuteNonQuery()
        End Using
    End Sub


    'Private Sub DeleteFacilitySettingsByFacility(ByVal facilityID As String)
    '    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
    '        cmd.CommandText = String.Format("Delete From facilitysettings WHERE FacilityID='{0}' ", facilityID)
    '        cmd.Connection = XpoDefault.Session.Connection
    '        cmd.ExecuteNonQuery()
    '    End Using
    'End Sub


    'Private Sub DeleteEspcodesByFacility(ByVal facilityID As String, treatmentArea As String)

    '    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
    '        cmd.CommandText = String.Format("Delete From espcodes WHERE Facility_ID='{0}' and TreatmentArea = '{1}'", facilityID, treatmentArea)
    '        cmd.Connection = XpoDefault.Session.Connection
    '        cmd.ExecuteNonQuery()
    '    End Using
    'End Sub

    'Private Sub DeleteFacilitySettingsByFacility(ByVal facilityID As String, treatmentArea As String)

    '    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
    '        cmd.CommandText = String.Format("Delete From facilitysettings WHERE FacilityID='{0}' and TreatmentArea = '{1}'", facilityID, treatmentArea)
    '        cmd.Connection = XpoDefault.Session.Connection
    '        cmd.ExecuteNonQuery()
    '    End Using
    'End Sub
    Public Function StripValue(ByVal value As String) As String
        'combo box list - replace special chars
        value = Replace(value, " ", "")
        value = Replace(value, "-", "")
        value = Replace(value, "/", "")
        value = Replace(value, ",", "")
        value = Replace(value, "&", "")
        value = Replace(value, "`", "")
        value = Replace(value, "@", "")
        value = Replace(value, "#", "")
        value = Replace(value, "%", "")
        value = Replace(value, "*", "")
        value = Replace(value, "\", "")
        value = Replace(value, "|", "")
        value = Replace(value, ".", "")
        value = Replace(value, ">=", "GTE")
        value = Replace(value, "<=", "LTE")
        value = Replace(value, ">", "GT")
        value = Replace(value, "<", "LT")
        value = Replace(value, "=", "")
        value = Replace(value, "(", "")
        value = Replace(value, ")", "")
        Return value
    End Function

    'Friend Function GetTreatmentAreasAsListByFacility(ByVal facility As DOFacility) As IEnumerable 'StringCollection

    '    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
    '        cmd.CommandText = String.Format("Select Distinct(TreatmentArea) from DOChargeMaster WHERE Facility='{0}'", facility.Oid)
    '        cmd.Connection = XpoDefault.Session.Connection

    '        Using reader As IDataReader = cmd.ExecuteReader()
    '            Dim sc As New StringCollection
    '            Do While reader.Read
    '                sc.Add(reader("TreatmentArea"))
    '            Loop

    '            Return sc
    '        End Using
    '    End Using

    'End Function

End Module
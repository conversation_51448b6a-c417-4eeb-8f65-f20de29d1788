﻿Imports System.Collections.Specialized
Imports System.Data.Sql
Imports System.Data.SqlClient
Imports iAnywhere.Data.AsaClient
Imports System.Data.Odbc

Imports DevExpress.Xpo
Imports System.IO
Imports System.IO.FileStream

Imports System.Reflection

Public Class ModWorkStation

    Private blDirty As Boolean


    Private _modType As Integer = 0
    Public Property modType()
        Get
            Return _modType
        End Get
        Set(ByVal value)
            _modType = value
        End Set
    End Property

    Private _OID As Integer = 0
    Public Property OID()
        Get
            Return _OID
        End Get
        Set(ByVal value)
            _OID = value
        End Set
    End Property


    Private Sub ModWorkStation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'HandleConfigs(frmType)
        'blNumLic = False
        If txtComment.Text = "" Or txtWorkstationName.Text = "" Then
            btnSave.Visible = False
            chkEnabled.Checked = True
        Else
            btnSave.Visible = True
        End If

        Dim rect As Rectangle = Screen.PrimaryScreen.WorkingArea
        'Divide the screen in half, and find the center of the form to center it
        Me.Top = 50 '(rect.Height / 2) - (Me.Height / 2)
        Me.Left = (rect.Width / 2) - (Me.Width / 2)

    End Sub


    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click

        If CheckSQL("Exists", "DOWLV") Then
            UpdateDB("Add")
        Else
            CheckSQL("Insert", "DOWLV")
        End If


        Me.Close()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        UpdateDB("Save")
        Me.Close()
    End Sub


    Private Function CheckSQL(ByVal sType As String, ByVal Table As String) As Boolean


        Dim strCmd As String = String.Empty

        Try

            Select Case sType
                Case "Insert"

                    strCmd = "INSERT INTO " + Table + "([WorkstationName],[Comment])VALUES("
                    strCmd = strCmd + "'" + txtWorkstationName.Text.ToString() + "',"
                    strCmd = strCmd + "'" + txtComment.Text.ToString() + "')"

                    Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                        cmd.Connection = XpoDefault.Session.Connection
                        cmd.CommandText = String.Format(strCmd)
                        cmd.ExecuteReader()
                        Return True
                    End Using
                Case "Delete"
                Case "Update"
                    ''strCmd = "Update " + Table
                    ''strCmd = strCmd + " Set Enabled = '" + CStr(CInt(Int(CheckBox1.Checked))) + "'"
                    ''strCmd = strCmd + ", MaxLic = '" + NumericUpDown1.Value.ToString() + "'"
                Case "reset_identity"
                    ''strCmd = String.Format("sa_reset_identity DOWLV,dba,0")
                Case "Exists"

                    Dim blTrue As Boolean = False

                    Try

                        Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                            cmd.CommandText = String.Format("SELECT 1 FROM sysobjects WHERE name = '" + Table + "' AND type = 'U'")
                            cmd.Connection = XpoDefault.Session.Connection
                            Using reader As IDataReader = cmd.ExecuteReader()
                                Do While reader.Read
                                    blTrue = True
                                Loop
                            End Using

                            If Not blTrue Then

                                cmd.CommandText = String.Format("CREATE TABLE DOWLV ([GCRecord] integer NULL," _
                                                    + " [OID] integer NOT NULL DEFAULT autoincrement, " _
                                                    + " [WorkstationName] varchar(256) NULL," _
                                                    + " [Comment] varchar(156) NULL," _
                                                    + " [OptimisticLockField] integer NULL," _
                                                    + " PRIMARY KEY ( OID ));")
                                cmd.ExecuteNonQuery()

                                Return False

                            Else
                                blTrue = False
                                strCmd = "SELECT count(*) FROM " + Table

                                cmd.CommandText = String.Format(strCmd)
                                cmd.Connection = XpoDefault.Session.Connection

                                Using reader As IDataReader = cmd.ExecuteReader()
                                    Do While reader.Read
                                        If reader.GetValue(0) > 0 Then
                                            Return True
                                        Else
                                            Return False
                                        End If
                                    Loop
                                End Using

                            End If

                        End Using

                    Catch ex As Exception
                        If ex.Message.ToLower().Contains("not found") Then
                            'MessageBox.Show("hey")
                        End If
                    End Try


            End Select

        Catch ex As Exception
            Return False
        End Try

        Return True

    End Function


    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click

        Select Case modType
            Case FormType.Add
                If txtComment.Text = "" Or txtWorkstationName.Text = "" Then
                    If MessageBox.Show("Cancel Adding a New Workstation?", "Add WorkStation", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                        Me.Close()
                    End If
                Else
                    If MessageBox.Show("Do You Want to Add the Workstation?", "Add WorkStation", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                        UpdateDB("Save")
                        Me.Close()
                    Else
                        Me.Close()
                    End If
                End If
            Case FormType.Edit
                If MessageBox.Show("Do You Want to Update the Workstation?", "Update WorkStation", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                    UpdateDB("Save")
                    Me.Close()
                Else
                    Me.Close()
                End If
            Case FormType.Delete
                If MessageBox.Show("Do You Want to Delete the Workstation?", "Delete WorkStation", MessageBoxButtons.YesNoCancel) = DialogResult.Yes Then
                    btnDelete_Click(Me, Nothing)
                    Me.Close()
                Else
                    Me.Close()
                End If
        End Select

    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        UpdateDB("Delete")
        UpdateDB("DelTmp")
        UpdateDB("CopyTmp")
        UpdateDB("reset_identity")
        UpdateDB("Fill")
        UpdateDB("DelTmp")
        Me.Close()
    End Sub

    Private Sub txtWorkstationName_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWorkstationName.TextChanged
        If txtComment.Text = "" Or txtWorkstationName.Text = "" Then
            btnSave.Enabled = False
            btnDelete.Enabled = False
            btnAdd.Enabled = False
        Else
            btnSave.Enabled = True
            btnAdd.Enabled = True
            btnDelete.Enabled = True
        End If
    End Sub

    Private Sub txtComment_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtComment.TextChanged
        If txtComment.Text = "" Or txtWorkstationName.Text = "" Then
            btnSave.Enabled = False
            btnDelete.Enabled = False
            btnAdd.Enabled = False
        Else
            btnSave.Enabled = True
            btnAdd.Enabled = True
            btnDelete.Enabled = True
        End If
    End Sub


    Public Sub UpdateDB(ByVal sType As String, Optional ByVal Table As String = "DOWLV")

        Dim strCmd As String = String.Empty
        Dim strEnabled As String = CStr(CInt(Int(chkEnabled.Checked)))


        If strEnabled.ToString() = "1" Then
            strEnabled = Nothing
        Else
            strEnabled = "99"
        End If


        Try

            Select Case sType
                Case "Save"
                    strCmd = "Update " + Table

                    If strEnabled = Nothing Then

                        strCmd = strCmd + " Set  [WorkstationName] = '" + txtWorkstationName.Text _
                                + "', [GCRECORD] = NULL " _
                                + ", [Comment] = '" + txtComment.Text + "'" _
                                + " WHERE [OID] = '" + OID.ToString() + "';"
                    Else
                        strCmd = strCmd + " Set  [WorkstationName] = '" + txtWorkstationName.Text _
                                + "', [GCRECORD] = '" + strEnabled _
                                + "', [Comment] = '" + txtComment.Text + "'" _
                                + " WHERE [OID] = '" + OID.ToString() + "';"
                    End If

                Case "Delete"
                    strCmd = "DELETE " + Table _
                            + " WHERE [OID] = '" + OID.ToString() + "';"
                    'strCmd = strCmd + " WHERE [WorkstationName] = '" _
                    '+ txtWorkstationName.Text + "' AND [Comment] = '" + txtComment.Text + "';"
                Case "Add"
                    strCmd = "INSERT INTO " + Table

                    If strEnabled = Nothing Then

                        ''strCmd = strCmd + " Set  [WorkstationName] = '" + txtWorkstationName.Text _
                        ''        + "', [GCRECORD] = NULL " _
                        ''        + ", [Comment] = '" + txtComment.Text + "'" _
                        ''        + " WHERE [OID] = '" + OID.ToString() + "';"

                        strCmd = strCmd + " ([WorkstationName], [Comment]) VALUES('" _
                        + txtWorkstationName.Text + "','" + txtComment.Text + "')"
                    Else
                        strCmd = strCmd + " ([GCRECORD], [WorkstationName], [Comment]) VALUES('" _
                        + strEnabled + "','" + txtWorkstationName.Text + "','" + txtComment.Text + "')"
                    End If

                Case "reset_identity"
                    strCmd = String.Format("sa_reset_identity DOWLV,dba,0")
                Case "DelTmp"
                    strCmd = "IF EXISTS (SELECT 1 FROM sysobjects WHERE name = 'tmp342' AND type = 'U')" _
                    + " drop table tmp342 "
                    strCmd = strCmd + " commit "
                Case "CopyTmp"
                    strCmd = strCmd + vbCrLf + "select [GCRECORD], [WorkstationName], [Comment] into tmp342 from " + Table
                    strCmd = strCmd + vbCrLf + "IF EXISTS (SELECT 1 FROM sysobjects WHERE name = 'dowlv' AND type = 'U')delete dowlv"
                    strCmd = strCmd + " commit "
                Case "Fill"
                    strCmd = "insert into dowlv([GCRECORD], [WorkstationName], [Comment]) select [GCRECORD], [WorkstationName], [Comment] from tmp342"

            End Select

            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = String.Format(strCmd)
                cmd.Connection = XpoDefault.Session.Connection
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            strCmd = String.Empty
        End Try

    End Sub


    Public Enum FormType
        Add = 0
        Edit = 1
        Delete = 2
    End Enum

End Class
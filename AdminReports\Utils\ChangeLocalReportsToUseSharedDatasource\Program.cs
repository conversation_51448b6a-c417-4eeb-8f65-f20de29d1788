﻿using ChangeLocalReportsToUseSharedDatasource.ReportService2010WebRef;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using System.Xml.Linq;
using System.Xml;
using System.IO;

namespace ConsoleApp3
{
    //using Newtonsoft.Json.Linq;

    namespace SSRSConsoleApp
    {
        class Program
        {
            //const string ReportExecution2005EndPointUrl = "https://localhost/ReportServer/ReportingService2010.asmx";
            //const string SsrsServiceAccountActiveDirectoryUserName = "J794116";
            //const string SsrsServiceAccountActiveDirectoryPassword = "nope nope nope";
            //const string SsrsServiceAccountActiveDirectoryDomain = "corporate";

            //static async System.Threading.Tasks.Task Main(string[] args)
            static void Main(string[] args)
            {
                string reportDirectory = Directory.GetCurrentDirectory();
                string embeddedDataSourceName = "VIC";
                string sharedDataSourceName = "VICShared";

                string reportNamespace = "http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition";

                // Create a namespace manager to handle the default namespace
                XmlNamespaceManager namespaceManager = new XmlNamespaceManager(new NameTable());
                namespaceManager.AddNamespace("ns", reportNamespace);

                // Get all RDL files in the directory
                string[] reportFiles = Directory.GetFiles(reportDirectory, "*.rdl");

                foreach (string reportFile in reportFiles)
                {
                    try
                    {
                        // Load the RDL file into an XmlDocument
                        XmlDocument doc = new XmlDocument();
                        doc.Load(reportFile);

                        // Find the DataSources element using the namespace manager
                        XmlNode dataSourcesNode = doc.SelectSingleNode("//ns:DataSources", namespaceManager);

                        if (dataSourcesNode != null)
                        {

                            // Find the DataSource element Named "VIC" to replace
                            XmlNode dataSourceNode = dataSourcesNode.SelectSingleNode($"//ns:DataSource[@Name='{embeddedDataSourceName}']", namespaceManager);

                            if (dataSourceNode != null)
                            {
                                // Create a new DataSource element that references the shared data source
                                XmlNode newDataSourceNode = doc.CreateElement("DataSource", reportNamespace);
                                XmlAttribute nameAttribute = doc.CreateAttribute("Name");
                                nameAttribute.Value = embeddedDataSourceName;
                                newDataSourceNode.Attributes.Append(nameAttribute);


                                XmlNode dataSourceReferenceNode = doc.CreateElement("DataSourceReference", reportNamespace);
                                dataSourceReferenceNode.InnerText = sharedDataSourceName;
                                newDataSourceNode.AppendChild(dataSourceReferenceNode);

                                //JJC 06.27.2023 - Add the type attribute to the new datasource node; though i'm not sure it's needed
                                //i would think it would be defined in the datasource itself.
                                //An attribute to set the type of connection to the data source. The default value is "SQL".
                                XmlAttribute typeAttribute = doc.CreateAttribute("Type");
                                typeAttribute.Value = "SQL";
                                newDataSourceNode.Attributes.Append(typeAttribute);


                                // Replace the existing DataSource element with the new one
                                dataSourcesNode.ReplaceChild(newDataSourceNode, dataSourceNode);

                                // Save the modified XML document back to disk
                                doc.Save(reportFile);


                                //// Find the DataSource element to replace
                                //XmlNode dataSourceNode = dataSourcesNode.SelectSingleNode($"//ns:DataSource[@Name='{embeddedDataSourceName}']", namespaceManager);
                                //if (dataSourceNode != null)
                                //{
                                //    // Create a new DataSource node with the updated credentials


                                //    // Replace the old DataSource node with the new one

                                //    // Save the updated RDL file
                                //    doc.Save(reportFile);
                                //}
                                //else
                                //{
                                //    Console.WriteLine($"Shared DataSource '{sharedDataSourceName}' not found in report '{reportFile}'.");
                                //}

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error updating report file '{reportFile}': {ex.Message}");
                    }
                }

                Console.WriteLine("All report files have been updated.");
                Console.ReadLine();
            }
        }
    }
}



{"version": 3, "targets": {"net7.0-windows7.0": {"DevExpress.Charts/23.1.5": {"type": "package", "dependencies": {"DevExpress.Charts.Core": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.DataVisualization.Core": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.XtraCharts.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.XtraCharts.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Charts.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard2.0/DevExpress.Charts.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Charts.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.CodeParser/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.CodeDom": "4.4.0"}, "compile": {"lib/netstandard2.0/DevExpress.CodeParser.v23.1.dll": {}}, "runtime": {"lib/netstandard2.0/DevExpress.CodeParser.v23.1.dll": {}}}, "DevExpress.Data/23.1.5": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NETStandard.Library": "2.0.3", "System.ComponentModel.Annotations": "4.5.0", "System.Drawing.Common": "4.7.2", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Encoding.CodePages": "4.5.1"}, "compile": {"lib/netstandard2.0/DevExpress.Data.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Data.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Data.Desktop/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll": {"related": ".xml"}}, "build": {"build/netcoreapp3.0/DevExpress.Data.Desktop.props": {}}}, "DevExpress.DataAccess/23.1.5": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Xpo": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.5", "System.Drawing.Common": "4.7.2", "System.Reflection.Emit": "4.7.0"}, "compile": {"lib/netstandard2.0/DevExpress.DataAccess.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.DataAccess.v23.1.dll": {"related": ".xml"}}}, "DevExpress.DataAccess.UI/23.1.5": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.Diagram.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Utils.UI": "[23.1.5]", "DevExpress.Win.Diagram": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.RichEdit": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]", "DevExpress.Xpo": "[23.1.5]", "System.Data.SqlClient": "4.8.5"}, "compile": {"lib/net6.0-windows/DevExpress.DataAccess.v23.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.DataAccess.v23.1.UI.dll": {"related": ".xml"}}}, "DevExpress.DataVisualization.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard2.0/DevExpress.DataVisualization.v23.1.Core.dll": {}}, "runtime": {"lib/netstandard2.0/DevExpress.DataVisualization.v23.1.Core.dll": {}}}, "DevExpress.Diagram.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.Diagram.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Diagram.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Drawing/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2", "System.Reflection.Emit": "4.7.0"}, "compile": {"lib/netstandard2.0/DevExpress.Drawing.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Drawing.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Gauges.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.XtraGauges.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.XtraGauges.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Images/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.Images.v23.1.dll": {}}, "runtime": {"lib/netstandard2.0/DevExpress.Images.v23.1.dll": {}}}, "DevExpress.Map.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.Map.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Map.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Mvvm/23.1.5": {"type": "package", "compile": {"lib/net6.0-windows/DevExpress.Mvvm.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Mvvm.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Office.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/DevExpress.Office.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Office.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "6.0.3"}, "compile": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Drawing/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Drawing.dll": {"related": ".xml"}}}, "DevExpress.PivotGrid.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/netstandard2.0/DevExpress.PivotGrid.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.PivotGrid.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Printing.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Pdf.Drawing": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.ComponentModel.Annotations": "4.5.0", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "6.0.3", "System.ServiceModel.Http": "4.10.2", "System.Text.Encoding.CodePages": "4.5.1"}, "compile": {"lib/netstandard2.0/DevExpress.Printing.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Printing.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Reporting.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Charts": "[23.1.5]", "DevExpress.Charts.Core": "[23.1.5]", "DevExpress.CodeParser": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Gauges.Core": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Pdf.Drawing": "[23.1.5]", "DevExpress.PivotGrid.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.RichEdit.Export": "[23.1.5]", "DevExpress.Sparkline.Core": "[23.1.5]", "DevExpress.Xpo": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.CodeDom": "4.4.0", "System.Collections.Immutable": "1.5.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Drawing.Common": "4.7.2", "System.Runtime.Loader": "4.3.0", "System.Security.Permissions": "4.7.0"}, "compile": {"lib/netstandard2.0/DevExpress.XtraReports.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.XtraReports.v23.1.dll": {"related": ".xml"}}, "build": {"buildTransitive/DevExpress.Reporting.Core.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Reporting.Core.targets": {}}}, "DevExpress.RichEdit.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.RichEdit.Export/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Export.dll": {}}, "runtime": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Export.dll": {}}}, "DevExpress.Scheduler.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.XtraScheduler.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.XtraScheduler.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Scheduler.CoreDesktop/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Images": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Scheduler.Core": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Core.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Core.Desktop.dll": {"related": ".xml"}}}, "DevExpress.Sparkline.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.Sparkline.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Sparkline.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.SpellChecker.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard2.0/DevExpress.SpellChecker.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.SpellChecker.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Spreadsheet.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.DataVisualization.Core": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Sparkline.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2", "System.Security.AccessControl": "6.0.0"}, "compile": {"lib/netstandard2.0/DevExpress.Spreadsheet.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Spreadsheet.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.TreeMap/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.DataVisualization.Core": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.TreeMap.Core": "[23.1.5]", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/DevExpress.XtraTreeMap.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.XtraTreeMap.v23.1.dll": {"related": ".xml"}}}, "DevExpress.TreeMap.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard2.0/DevExpress.TreeMap.v23.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.TreeMap.v23.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Utils/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.Utils.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Utils.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Utils.UI/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.RichEdit": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]", "DevExpress.Win.VerticalGrid": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.Utils.v23.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Utils.v23.1.UI.dll": {"related": ".xml"}}}, "DevExpress.Win/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.RichEdit.Export": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.PivotGrid": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]", "DevExpress.Win.VerticalGrid": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraNavBar.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraWizard.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraNavBar.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraWizard.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Charts/23.1.5": {"type": "package", "dependencies": {"DevExpress.Charts": "[23.1.5]", "DevExpress.Charts.Core": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.DataAccess.UI": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Utils.UI": "[23.1.5]", "DevExpress.Win": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]", "DevExpress.Win.VerticalGrid": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Extensions.dll": {}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.UI.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Wizard.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Extensions.dll": {}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.UI.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Wizard.dll": {"related": ".xml"}}}, "DevExpress.Win.Design/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Mvvm": "[23.1.5]", "DevExpress.Win": "[23.1.5]", "DevExpress.Win.Charts": "[23.1.5]", "DevExpress.Win.Diagram": "[23.1.5]", "DevExpress.Win.Dialogs": "[23.1.5]", "DevExpress.Win.Gantt": "[23.1.5]", "DevExpress.Win.Gauges": "[23.1.5]", "DevExpress.Win.Map": "[23.1.5]", "DevExpress.Win.PdfViewer": "[23.1.5]", "DevExpress.Win.Reporting": "[23.1.5]", "DevExpress.Win.RichEdit": "[23.1.5]", "DevExpress.Win.SchedulerExtensions": "[23.1.5]", "DevExpress.Win.SpellChecker": "[23.1.5]", "DevExpress.Win.Spreadsheet": "[23.1.5]", "DevExpress.Win.TreeMap": "[23.1.5]", "DevExpress.Xpo": "[23.1.5]", "Microsoft.Win32.Registry": "4.7.0", "System.ComponentModel.Annotations": "4.5.0", "System.ComponentModel.Composition": "5.0.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.Odbc": "4.5.0", "System.Data.OleDb": "4.6.0", "System.Data.SqlClient": "4.8.5", "System.Drawing.Common": "4.7.2", "System.IO.Ports": "4.5.0", "System.ServiceModel.Http": "4.10.2", "System.ServiceModel.NetTcp": "4.4.4", "System.ServiceModel.Security": "4.4.4", "System.ServiceModel.Syndication": "4.5.0", "System.ServiceProcess.ServiceController": "4.5.0", "System.Text.Encoding.CodePages": "4.5.1"}, "compile": {"lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Diagram/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Diagram.Core": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.VerticalGrid": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraDiagram.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraDiagram.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Dialogs/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Dialogs.Core": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraDialogs.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraDialogs.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Dialogs.Core/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.Dialogs.v23.1.Core.dll": {}}, "runtime": {"lib/net6.0-windows/DevExpress.Dialogs.v23.1.Core.dll": {}}}, "DevExpress.Win.Gantt/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraGantt.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGantt.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Gauges/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Gauges.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Presets.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Win.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Presets.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Win.dll": {"related": ".xml"}}}, "DevExpress.Win.Grid/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraGrid.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGrid.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Map/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Map.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "System.Data.SqlClient": "4.8.5"}, "compile": {"lib/net6.0-windows/DevExpress.XtraMap.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraMap.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Navigation/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Sparkline.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraBars.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraEditors.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraLayout.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraBars.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraEditors.v23.1.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraLayout.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.PdfViewer/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Pdf.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraPdfViewer.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPdfViewer.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.PivotGrid/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.PivotGrid.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraPivotGrid.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPivotGrid.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Printing/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraPrinting.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPrinting.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Reporting/23.1.5": {"type": "package", "dependencies": {"DevExpress.Charts": "[23.1.5]", "DevExpress.Charts.Core": "[23.1.5]", "DevExpress.CodeParser": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.DataAccess.UI": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Gauges.Core": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.PivotGrid.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Reporting.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Sparkline.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Utils.UI": "[23.1.5]", "DevExpress.Win": "[23.1.5]", "DevExpress.Win.Charts": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.PivotGrid": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.RichEdit": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]", "DevExpress.Win.VerticalGrid": "[23.1.5]", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/net6.0-windows/DevExpress.XtraReports.v23.1.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraReports.v23.1.Extensions.dll": {"related": ".xml"}}}, "DevExpress.Win.RichEdit/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Images": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Pdf.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraRichEdit.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraRichEdit.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Scheduler/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Scheduler.Core": "[23.1.5]", "DevExpress.Scheduler.CoreDesktop": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.SchedulerExtensions/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Reporting.Core": "[23.1.5]", "DevExpress.Scheduler.Core": "[23.1.5]", "DevExpress.Scheduler.CoreDesktop": "[23.1.5]", "DevExpress.SpellChecker.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.Reporting": "[23.1.5]", "DevExpress.Win.Scheduler": "[23.1.5]", "DevExpress.Win.SchedulerReporting": "[23.1.5]", "DevExpress.Win.SpellChecker": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Extensions.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Extensions.dll": {"related": ".xml"}, "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.dll": {"related": ".xml"}}}, "DevExpress.Win.SchedulerReporting/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Reporting.Core": "[23.1.5]", "DevExpress.Scheduler.Core": "[23.1.5]", "DevExpress.Scheduler.CoreDesktop": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.Scheduler": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.dll": {"related": ".xml"}}}, "DevExpress.Win.SpellChecker/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.SpellChecker.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraSpellChecker.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraSpellChecker.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Spreadsheet/23.1.5": {"type": "package", "dependencies": {"DevExpress.Charts": "[23.1.5]", "DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.DataAccess": "[23.1.5]", "DevExpress.DataAccess.UI": "[23.1.5]", "DevExpress.DataVisualization.Core": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Office.Core": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.RichEdit.Core": "[23.1.5]", "DevExpress.Spreadsheet.Core": "[23.1.5]", "DevExpress.TreeMap": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Utils.UI": "[23.1.5]", "DevExpress.Win": "[23.1.5]", "DevExpress.Win.Grid": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]", "DevExpress.Win.RichEdit": "[23.1.5]", "DevExpress.Win.TreeList": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraSpreadsheet.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraSpreadsheet.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.TreeList/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraTreeList.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraTreeList.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Win.TreeMap/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.TreeMap": "[23.1.5]", "DevExpress.TreeMap.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraTreeMap.v23.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraTreeMap.v23.1.UI.dll": {"related": ".xml"}}}, "DevExpress.Win.VerticalGrid/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "DevExpress.Data.Desktop": "[23.1.5]", "DevExpress.Drawing": "[23.1.5]", "DevExpress.Printing.Core": "[23.1.5]", "DevExpress.Utils": "[23.1.5]", "DevExpress.Win.Navigation": "[23.1.5]", "DevExpress.Win.Printing": "[23.1.5]"}, "compile": {"lib/net6.0-windows/DevExpress.XtraVerticalGrid.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows/DevExpress.XtraVerticalGrid.v23.1.dll": {"related": ".xml"}}}, "DevExpress.Xpo/23.1.5": {"type": "package", "dependencies": {"DevExpress.Data": "[23.1.5]", "Microsoft.Extensions.DependencyInjection": "2.0.0", "Microsoft.Win32.Registry": "4.7.0", "NETStandard.Library": "2.0.3", "System.Data.SqlClient": "4.8.5", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "6.0.3", "System.ServiceModel.Http": "4.10.2", "System.ServiceModel.NetTcp": "4.4.4", "System.ServiceModel.Security": "4.4.4", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/DevExpress.Xpo.v23.1.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DevExpress.Xpo.v23.1.dll": {"related": ".xml"}}}, "Mapster/7.3.0": {"type": "package", "dependencies": {"Mapster.Core": "1.2.0", "Microsoft.CSharp": "4.3.0", "System.Reflection.Emit": "4.3.0"}, "compile": {"lib/netstandard2.0/Mapster.dll": {}}, "runtime": {"lib/netstandard2.0/Mapster.dll": {}}}, "Mapster.Core/1.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Mapster.Core.dll": {}}, "runtime": {"lib/netstandard2.0/Mapster.Core.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {"type": "package", "build": {"build/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.props": {}}}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "dependencies": {"System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "compile": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Options": "2.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.VisualBasic/10.4.0-preview.18571.3": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Windows.Compatibility/7.0.3": {"type": "package", "dependencies": {"Microsoft.Win32.Registry.AccessControl": "7.0.0", "Microsoft.Win32.SystemEvents": "7.0.0", "System.CodeDom": "7.0.0", "System.ComponentModel.Composition": "7.0.0", "System.ComponentModel.Composition.Registration": "7.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.Odbc": "7.0.0", "System.Data.OleDb": "7.0.0", "System.Data.SqlClient": "4.8.5", "System.Diagnostics.EventLog": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0", "System.DirectoryServices": "7.0.1", "System.DirectoryServices.AccountManagement": "7.0.0", "System.DirectoryServices.Protocols": "7.0.1", "System.Drawing.Common": "7.0.0", "System.IO.Packaging": "7.0.0", "System.IO.Ports": "7.0.0", "System.Management": "7.0.2", "System.Reflection.Context": "7.0.0", "System.Runtime.Caching": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.ProtectedData": "7.0.1", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Permissions": "7.0.0", "System.ServiceModel.Duplex": "4.9.0", "System.ServiceModel.Http": "4.9.0", "System.ServiceModel.NetTcp": "4.9.0", "System.ServiceModel.Primitives": "4.9.0", "System.ServiceModel.Security": "4.9.0", "System.ServiceModel.Syndication": "7.0.0", "System.ServiceProcess.ServiceController": "7.0.1", "System.Speech": "7.0.0", "System.Text.Encoding.CodePages": "7.0.0", "System.Threading.AccessControl": "7.0.1", "System.Web.Services.Description": "4.9.0"}, "build": {"buildTransitive/net6.0/_._": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Serilog/2.10.0": {"type": "package", "compile": {"lib/netstandard2.1/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/6.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "compile": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "2.0.0", "Serilog": "2.9.0"}, "compile": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "dependencies": {"Serilog": "2.8.0"}, "compile": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.10.0"}, "compile": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Collections.Immutable/1.5.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Composition/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ComponentModel.Composition.Registration/7.0.0": {"type": "package", "dependencies": {"System.ComponentModel.Composition": "7.0.0", "System.Reflection.Context": "7.0.0"}, "compile": {"lib/net7.0/System.ComponentModel.Composition.Registration.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ComponentModel.Composition.Registration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Data.DataSetExtensions.dll": {}}, "runtime": {"lib/netstandard2.0/System.Data.DataSetExtensions.dll": {}}}, "System.Data.Odbc/7.0.0": {"type": "package", "dependencies": {"System.Text.Encoding.CodePages": "7.0.0"}, "compile": {"lib/net7.0/System.Data.Odbc.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Data.Odbc.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "freebsd"}, "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "illumos"}, "runtimes/ios/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "ios"}, "runtimes/linux/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "solaris"}, "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "tvos"}, "runtimes/win/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.OleDb/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0"}, "compile": {"lib/net7.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "compile": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices/7.0.1": {"type": "package", "dependencies": {"System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.AccountManagement/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.DirectoryServices": "7.0.0", "System.DirectoryServices.Protocols": "7.0.0"}, "compile": {"lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.Protocols/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/7.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "compile": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/8.0.1": {"type": "package", "compile": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Packaging/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "compile": {"lib/net7.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net7.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net7.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management/7.0.2": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Private.ServiceModel/4.10.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Context/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Reflection.Context.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Reflection.Context.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "compile": {"ref/netstandard2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "compile": {"lib/net7.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/7.0.2": {"type": "package", "dependencies": {"System.Formats.Asn1": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/7.0.1": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Permissions/7.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Duplex/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Duplex.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"related": ".pdb"}}}, "System.ServiceModel.Http/4.10.2": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.10.2", "System.ServiceModel.Primitives": "4.10.2"}, "compile": {"ref/net6.0/System.ServiceModel.Http.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}}, "System.ServiceModel.NetTcp/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.NetTcp.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}}, "System.ServiceModel.Primitives/4.10.2": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.10.2"}, "compile": {"ref/net6.0/System.ServiceModel.Primitives.dll": {}, "ref/net6.0/System.ServiceModel.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}, "lib/net6.0/System.ServiceModel.dll": {"related": ".Primitives.pdb"}}}, "System.ServiceModel.Security/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Security.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"related": ".pdb"}}}, "System.ServiceModel.Syndication/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ServiceProcess.ServiceController/7.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0"}, "compile": {"lib/net7.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Speech/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Speech.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Speech.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Speech.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "compile": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.AccessControl/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Threading.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Web.Services.Description/4.9.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/7.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "7.0.0"}, "compile": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "AIC.SharedData/2023.1.2.57": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "compile": {"bin/placeholder/AIC.SharedData.dll": {}}, "runtime": {"bin/placeholder/AIC.SharedData.dll": {}}}, "EnchartDOLib/2023.1.0.2": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"AIC.SharedData": "2023.1.2.57", "DevExpress.Xpo": "23.1.5", "EnchartServer.Data": "2023.1.2.57", "MICCustomConnectionProviders": "2023.1.3.2", "Mapster": "7.3.0", "McKessonIntelligentCoding.Data": "2023.1.2.57", "Microsoft.VisualBasic": "10.4.0-preview.18571.3", "Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "compile": {"bin/placeholder/EnchartDOLib.dll": {}}, "runtime": {"bin/placeholder/EnchartDOLib.dll": {}}}, "EnchartServer.Data/2023.1.2.57": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"AIC.SharedData": "2023.1.2.57", "DevExpress.Xpo": "23.1.5", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Newtonsoft.Json": "13.0.3", "Serilog.AspNetCore": "6.1.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1"}, "compile": {"bin/placeholder/EnchartServer.Data.dll": {}}, "runtime": {"bin/placeholder/EnchartServer.Data.dll": {}}}, "ICCryptoHelper/2023.1.2.57": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DevExpress.Xpo": "23.1.5", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Windows.Compatibility": "7.0.3", "Newtonsoft.Json": "13.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1", "System.IdentityModel.Tokens.Jwt": "6.35.0", "System.ValueTuple": "4.5.0"}, "compile": {"bin/placeholder/ICCryptoHelper.dll": {}}, "runtime": {"bin/placeholder/ICCryptoHelper.dll": {}}}, "McKessonIntelligentCoding.Data/2023.1.2.57": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1", "System.ValueTuple": "4.5.0"}, "compile": {"bin/placeholder/McKessonIntelligentCoding.Data.dll": {}}, "runtime": {"bin/placeholder/McKessonIntelligentCoding.Data.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "MICCustomConnectionProviders/2023.1.3.2": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DevExpress.Xpo": "23.1.5", "ICCryptoHelper": "2023.1.2.57", "Newtonsoft.Json": "13.0.3", "System.Data.SqlClient": "4.8.6", "System.Formats.Asn1": "8.0.1"}, "compile": {"bin/placeholder/MICCustomConnectionProviders.dll": {}}, "runtime": {"bin/placeholder/MICCustomConnectionProviders.dll": {}}}, "Timeout/2023.1.2.57": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "compile": {"bin/placeholder/Timeout.dll": {}}, "runtime": {"bin/placeholder/Timeout.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}}}, "libraries": {"DevExpress.Charts/23.1.5": {"sha512": "7J+gtEuT2LDkRf4BaXxP6E6Ue4SaTzXx+jnppXFwE8TNdFFiRUS4H7bzA3MYT0vM/1QiJ6Kkt1ux3AH4mCblnQ==", "type": "package", "path": "devexpress.charts/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.23.1.5.nupkg.sha512", "devexpress.charts.nuspec", "lib/net452/DevExpress.XtraCharts.v23.1.dll", "lib/net452/DevExpress.XtraCharts.v23.1.xml", "lib/netstandard2.0/DevExpress.XtraCharts.v23.1.dll", "lib/netstandard2.0/DevExpress.XtraCharts.v23.1.xml"]}, "DevExpress.Charts.Core/23.1.5": {"sha512": "c2zXEMaStd+dWHDxDNzRT6SOPdx1p3k16n9XiokqpTW19DRXvcl0LaRetFKMQD/t2N+mETTsiGIniiDIkkbMsA==", "type": "package", "path": "devexpress.charts.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.core.23.1.5.nupkg.sha512", "devexpress.charts.core.nuspec", "lib/net452/DevExpress.Charts.v23.1.Core.dll", "lib/net452/DevExpress.Charts.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Charts.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Charts.v23.1.Core.xml"]}, "DevExpress.CodeParser/23.1.5": {"sha512": "EXiWeuyzF35CAAW4H+kOfnTsvX+fmwf4UkXEZlx10NDbUOGNcwKx5QVL3txkP7oXjB43ExXZLPQ4lxbFS27AaQ==", "type": "package", "path": "devexpress.codeparser/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.codeparser.23.1.5.nupkg.sha512", "devexpress.codeparser.nuspec", "lib/net452/DevExpress.CodeParser.v23.1.dll", "lib/netstandard2.0/DevExpress.CodeParser.v23.1.dll"]}, "DevExpress.Data/23.1.5": {"sha512": "G/RZYe1OCBQIdVSw1ZODe7irbtYi3RHmKgUMM0JojEn/Tj6c//hfQZpImB5MgCKqNmfkQzuoID8PXVDyh6O7Cw==", "type": "package", "path": "devexpress.data/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.data.23.1.5.nupkg.sha512", "devexpress.data.nuspec", "lib/net452/DevExpress.Data.v23.1.dll", "lib/net452/DevExpress.Data.v23.1.xml", "lib/netstandard2.0/DevExpress.Data.v23.1.dll", "lib/netstandard2.0/DevExpress.Data.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Data.Desktop/23.1.5": {"sha512": "JVvdPXVWkcfhw/Ni8/zmGZrK7rlBffVQ0N+WlXhfHLWJ6pewyJaI/UzHyBFCrDBZLRu4fX6jpzNoHQOR/ZnzXg==", "type": "package", "path": "devexpress.data.desktop/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "build/netcoreapp3.0/DevExpress.Data.Desktop.props", "devexpress.data.desktop.23.1.5.nupkg.sha512", "devexpress.data.desktop.nuspec", "lib/net452/DevExpress.Data.Desktop.v23.1.dll", "lib/net452/DevExpress.Data.Desktop.v23.1.xml", "lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll", "lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.xml"]}, "DevExpress.DataAccess/23.1.5": {"sha512": "5WXeLDxyXDX81pLtuPzgCsBJUg376bIjjqAyuaeabMznadlhUb/xJ0+wXwp4aAKcc6RF8iZknMT3gJKZqo1ERA==", "type": "package", "path": "devexpress.dataaccess/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.23.1.5.nupkg.sha512", "devexpress.dataaccess.nuspec", "lib/net452/DevExpress.DataAccess.v23.1.dll", "lib/net452/DevExpress.DataAccess.v23.1.xml", "lib/netstandard2.0/DevExpress.DataAccess.v23.1.dll", "lib/netstandard2.0/DevExpress.DataAccess.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataAccess.UI/23.1.5": {"sha512": "/+BfJ5kQOFEWeIK0HtjqYMJgYIg5BQ/dnK77rnDjvt1HXuqY6QqhKMfFgJxwOMEHIspwpJI8fra3Lr8MZWhV1A==", "type": "package", "path": "devexpress.dataaccess.ui/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.ui.23.1.5.nupkg.sha512", "devexpress.dataaccess.ui.nuspec", "lib/net452/DevExpress.DataAccess.v23.1.UI.dll", "lib/net452/DevExpress.DataAccess.v23.1.UI.xml", "lib/net6.0-windows/DevExpress.DataAccess.v23.1.UI.dll", "lib/net6.0-windows/DevExpress.DataAccess.v23.1.UI.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataVisualization.Core/23.1.5": {"sha512": "PUhVBPPy8AEfB+IXD2urxhgroXK17AknFoYwY4z85QLlUgg9R7lCZYOI4UBs3RX2ddUQyJ7aU3nJ2fHZQ8rHkw==", "type": "package", "path": "devexpress.datavisualization.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.datavisualization.core.23.1.5.nupkg.sha512", "devexpress.datavisualization.core.nuspec", "lib/net452/DevExpress.DataVisualization.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.DataVisualization.v23.1.Core.dll"]}, "DevExpress.Diagram.Core/23.1.5": {"sha512": "SQ0pCtN+SM6hCgKUrJon/ovoURLtljvXd3clEOg+P2IxweiajQxEyRxZ1Y/+itXOiVWMp+kpL+KafF9fSbN2FA==", "type": "package", "path": "devexpress.diagram.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.diagram.core.23.1.5.nupkg.sha512", "devexpress.diagram.core.nuspec", "lib/net452/DevExpress.Diagram.v23.1.Core.dll", "lib/net452/DevExpress.Diagram.v23.1.Core.xml", "lib/net6.0-windows/DevExpress.Diagram.v23.1.Core.dll", "lib/net6.0-windows/DevExpress.Diagram.v23.1.Core.xml"]}, "DevExpress.Drawing/23.1.5": {"sha512": "AcAVX8uAkqlgjGwBe49c5KeYgst1D/xf1lDdM3kizhUgi9kF/ahABHbUI5FsPKhwMEHI5UvBoLsskn+WcO0irA==", "type": "package", "path": "devexpress.drawing/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.drawing.23.1.5.nupkg.sha512", "devexpress.drawing.nuspec", "lib/net452/DevExpress.Drawing.v23.1.dll", "lib/net452/DevExpress.Drawing.v23.1.xml", "lib/netstandard2.0/DevExpress.Drawing.v23.1.dll", "lib/netstandard2.0/DevExpress.Drawing.v23.1.xml"]}, "DevExpress.Gauges.Core/23.1.5": {"sha512": "6v0r4UwQ1QNecV7HvW9V+Ke7PTZaGG2dPCXS8/NoQtjA3UDwcmGp7fgFb8/geC9Pne645zhPkJIvrKHvYRLBPQ==", "type": "package", "path": "devexpress.gauges.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.gauges.core.23.1.5.nupkg.sha512", "devexpress.gauges.core.nuspec", "lib/net452/DevExpress.XtraGauges.v23.1.Core.dll", "lib/net452/DevExpress.XtraGauges.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.XtraGauges.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.XtraGauges.v23.1.Core.xml"]}, "DevExpress.Images/23.1.5": {"sha512": "0o6dVPA42thJUmmtcxBNfKK68B+utbx2KCW0UfQA/68HWsn0T2vcnd2/tVnRtQ3siFi4Q6v9SurIHqME164fhg==", "type": "package", "path": "devexpress.images/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.images.23.1.5.nupkg.sha512", "devexpress.images.nuspec", "lib/net452/DevExpress.Images.v23.1.dll", "lib/netstandard2.0/DevExpress.Images.v23.1.dll"]}, "DevExpress.Map.Core/23.1.5": {"sha512": "foBuO4PhsllHKRMbZlQKNfiapbOV7wTsLxGvTLoK7CXPkGnjT0mTl8lDfl8bM3JP8lNLu2VGZJuFomZEK9m9+w==", "type": "package", "path": "devexpress.map.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.map.core.23.1.5.nupkg.sha512", "devexpress.map.core.nuspec", "lib/net452/DevExpress.Map.v23.1.Core.dll", "lib/net452/DevExpress.Map.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Map.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Map.v23.1.Core.xml"]}, "DevExpress.Mvvm/23.1.5": {"sha512": "4nq3bkRkVRVj1Ym9Ft5UcUtCMRlgHcS0JDQ52UOiJ+JXvvudGN6mXJUtDlrwV0xZPoctJ5iC2HjOMDpVyy+1+Q==", "type": "package", "path": "devexpress.mvvm/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.mvvm.23.1.5.nupkg.sha512", "devexpress.mvvm.nuspec", "lib/net452/DevExpress.Mvvm.v23.1.dll", "lib/net452/DevExpress.Mvvm.v23.1.xml", "lib/net6.0-windows/DevExpress.Mvvm.v23.1.dll", "lib/net6.0-windows/DevExpress.Mvvm.v23.1.xml"]}, "DevExpress.Office.Core/23.1.5": {"sha512": "Km4+e9LVesj03yXEbEwDY3mTdPO+N17di9+J/f2kDMlh5V65ZCMtU1MplvULe6s0pdJwYJJvCsth6fSnyjFKLw==", "type": "package", "path": "devexpress.office.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.office.core.23.1.5.nupkg.sha512", "devexpress.office.core.nuspec", "lib/net452/DevExpress.Office.v23.1.Core.dll", "lib/net452/DevExpress.Office.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Office.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Office.v23.1.Core.xml"]}, "DevExpress.Pdf.Core/23.1.5": {"sha512": "aoBsFJhLy+T8Yg9bhv+aDFEUnU7FIDSrZO/HkgrhkZao35bhHxFCNvqM502cCPyu5vHp3Hbw1ZR1kN3TUWzbFw==", "type": "package", "path": "devexpress.pdf.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.core.23.1.5.nupkg.sha512", "devexpress.pdf.core.nuspec", "lib/net452/DevExpress.Pdf.v23.1.Core.dll", "lib/net452/DevExpress.Pdf.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Pdf.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Pdf.v23.1.Core.xml"]}, "DevExpress.Pdf.Drawing/23.1.5": {"sha512": "gjHvRDUSk+Rm3Rhn74XeGyU3EezdCdxfP7ckjT1UiXIj60obqevHX2oAEXpGryO9PGGKpfPnuL1ULnUKgCQfiQ==", "type": "package", "path": "devexpress.pdf.drawing/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.drawing.23.1.5.nupkg.sha512", "devexpress.pdf.drawing.nuspec", "lib/net452/DevExpress.Pdf.v23.1.Drawing.dll", "lib/net452/DevExpress.Pdf.v23.1.Drawing.xml", "lib/netstandard2.0/DevExpress.Pdf.v23.1.Drawing.dll", "lib/netstandard2.0/DevExpress.Pdf.v23.1.Drawing.xml"]}, "DevExpress.PivotGrid.Core/23.1.5": {"sha512": "3vdPAkJi/GL1/UtYgnwaxBPgAkS1NZtVIlaVPVIcEjcVHdTt+0zc3ccCBP4TTisg/PIZxzx7Yu/Q4aCizNfGVA==", "type": "package", "path": "devexpress.pivotgrid.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pivotgrid.core.23.1.5.nupkg.sha512", "devexpress.pivotgrid.core.nuspec", "lib/net452/DevExpress.PivotGrid.v23.1.Core.dll", "lib/net452/DevExpress.PivotGrid.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.PivotGrid.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.PivotGrid.v23.1.Core.xml"]}, "DevExpress.Printing.Core/23.1.5": {"sha512": "pytJr70/1QqfelRkGOJri0Xq3ye7F1H3SK39ypgAlaqfdFt96JAGA1uFFHlRThtnrbeqCGA9FE8tmaxlRbpW+Q==", "type": "package", "path": "devexpress.printing.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.printing.core.23.1.5.nupkg.sha512", "devexpress.printing.core.nuspec", "lib/net452/DevExpress.Printing.v23.1.Core.dll", "lib/net452/DevExpress.Printing.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Printing.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Printing.v23.1.Core.xml"]}, "DevExpress.Reporting.Core/23.1.5": {"sha512": "rnRJT435bgsYLCO7CbRs+yqrN/EjvCl+rxwOS9GLA/zxpgbFs6YRAGif1wP4zAHjudcnTrC0EL85zaYGmiPj+A==", "type": "package", "path": "devexpress.reporting.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Reporting.Core.targets", "buildMultiTargeting/DevExpress.Reporting.Core.targets", "buildTransitive/DevExpress.Reporting.Core.targets", "devexpress.reporting.core.23.1.5.nupkg.sha512", "devexpress.reporting.core.nuspec", "lib/net452/DevExpress.XtraReports.v23.1.dll", "lib/net452/DevExpress.XtraReports.v23.1.xml", "lib/netstandard2.0/DevExpress.XtraReports.v23.1.dll", "lib/netstandard2.0/DevExpress.XtraReports.v23.1.xml"]}, "DevExpress.RichEdit.Core/23.1.5": {"sha512": "uBnvT0wIRwiyQJVdE3OEe3pWJSllqvwQk1d6Yj35ciLxT+VJke9nJo6pbs2JLzyQhNMYcfvmD6q1T7SU3LcKAw==", "type": "package", "path": "devexpress.richedit.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.core.23.1.5.nupkg.sha512", "devexpress.richedit.core.nuspec", "lib/net452/DevExpress.RichEdit.v23.1.Core.dll", "lib/net452/DevExpress.RichEdit.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.RichEdit.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.RichEdit.v23.1.Core.xml"]}, "DevExpress.RichEdit.Export/23.1.5": {"sha512": "P/jv1TE/mBenScc1u89ukKH3wp1z0M+5m8A4D7YGxGKJajhZseV6iNd/RJFb1lpYG33E27VXLpNrP0W7PssIZA==", "type": "package", "path": "devexpress.richedit.export/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.export.23.1.5.nupkg.sha512", "devexpress.richedit.export.nuspec", "lib/net452/DevExpress.RichEdit.v23.1.Export.dll", "lib/netstandard2.0/DevExpress.RichEdit.v23.1.Export.dll"]}, "DevExpress.Scheduler.Core/23.1.5": {"sha512": "jUrcDzR/T4RrIUa/3cff9srP77IJbhfM+a3EsgUT5Ok+UjUhDUOJF3krlF0m62MJ6BW+EzlqEjSk9rIi2FOIrg==", "type": "package", "path": "devexpress.scheduler.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.core.23.1.5.nupkg.sha512", "devexpress.scheduler.core.nuspec", "lib/net452/DevExpress.XtraScheduler.v23.1.Core.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.XtraScheduler.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.XtraScheduler.v23.1.Core.xml"]}, "DevExpress.Scheduler.CoreDesktop/23.1.5": {"sha512": "joGzLWvAsaTjaO/zFm7bnqLZaP7qfpg5Xo0Czu7UKummUiytFPc6M4iaZ5c9zqsF25p1eCUvrl60pYlSbyEF2g==", "type": "package", "path": "devexpress.scheduler.coredesktop/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.coredesktop.23.1.5.nupkg.sha512", "devexpress.scheduler.coredesktop.nuspec", "lib/net452/DevExpress.XtraScheduler.v23.1.Core.Desktop.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.Core.Desktop.xml", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Core.Desktop.dll", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Core.Desktop.xml"]}, "DevExpress.Sparkline.Core/23.1.5": {"sha512": "i45VUeRNNfrMUGsQsh8wP51tUpvk/HELQbXKC0E8bSGtOwhUk58a1ViA7zdMeX2gvdQrkEa95c6vUK2fNOnBPQ==", "type": "package", "path": "devexpress.sparkline.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.sparkline.core.23.1.5.nupkg.sha512", "devexpress.sparkline.core.nuspec", "lib/net452/DevExpress.Sparkline.v23.1.Core.dll", "lib/net452/DevExpress.Sparkline.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Sparkline.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Sparkline.v23.1.Core.xml"]}, "DevExpress.SpellChecker.Core/23.1.5": {"sha512": "biGiyZRjrptvYxIYeC4O8tP23GID9RaodONiWQTab+Z5OioMJzQWOJfQDof5nrCP/ykeSczbMv0KRAA8VXWOEA==", "type": "package", "path": "devexpress.spellchecker.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.spellchecker.core.23.1.5.nupkg.sha512", "devexpress.spellchecker.core.nuspec", "lib/net452/DevExpress.SpellChecker.v23.1.Core.dll", "lib/net452/DevExpress.SpellChecker.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.SpellChecker.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.SpellChecker.v23.1.Core.xml"]}, "DevExpress.Spreadsheet.Core/23.1.5": {"sha512": "2thNpI02m1oHnafubdkKJ1qScL+8jhK4uIbUHeTtckcbowyPXYZkwKIV2JqiSIrt9przpKhU3hNusdaQZDefJg==", "type": "package", "path": "devexpress.spreadsheet.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.spreadsheet.core.23.1.5.nupkg.sha512", "devexpress.spreadsheet.core.nuspec", "lib/net452/DevExpress.Spreadsheet.v23.1.Core.dll", "lib/net452/DevExpress.Spreadsheet.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.Spreadsheet.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.Spreadsheet.v23.1.Core.xml"]}, "DevExpress.TreeMap/23.1.5": {"sha512": "PZO+kTccTymVk7W2ARxdqwhrH41tL32eN5VqI9w+IH0aLbBxBZOQlBJfBaElJ+uxKRw8QKajwzxc9OH3V72FyA==", "type": "package", "path": "devexpress.treemap/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.treemap.23.1.5.nupkg.sha512", "devexpress.treemap.nuspec", "lib/net452/DevExpress.XtraTreeMap.v23.1.dll", "lib/net452/DevExpress.XtraTreeMap.v23.1.xml", "lib/netstandard2.0/DevExpress.XtraTreeMap.v23.1.dll", "lib/netstandard2.0/DevExpress.XtraTreeMap.v23.1.xml"]}, "DevExpress.TreeMap.Core/23.1.5": {"sha512": "0fBBiOaKwXSAtJLanEl2T86AKa9r0FqQXsmJCxaEpl0Ua+J3j7GEwnecmjAjUSBtd7iv5O0BttcnYSDTBVC4OQ==", "type": "package", "path": "devexpress.treemap.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.treemap.core.23.1.5.nupkg.sha512", "devexpress.treemap.core.nuspec", "lib/net452/DevExpress.TreeMap.v23.1.Core.dll", "lib/net452/DevExpress.TreeMap.v23.1.Core.xml", "lib/netstandard2.0/DevExpress.TreeMap.v23.1.Core.dll", "lib/netstandard2.0/DevExpress.TreeMap.v23.1.Core.xml"]}, "DevExpress.Utils/23.1.5": {"sha512": "/KfCEAeYZOYyUTcJV/qoIQcmmslb4ztmP4M16B1NBrZk8cIz039F+VallWAGeJ/LApHHaaz3mLLcZFdZsAY/pg==", "type": "package", "path": "devexpress.utils/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.utils.23.1.5.nupkg.sha512", "devexpress.utils.nuspec", "lib/net452/DevExpress.Utils.v23.1.dll", "lib/net452/DevExpress.Utils.v23.1.xml", "lib/net6.0-windows/DevExpress.Utils.v23.1.dll", "lib/net6.0-windows/DevExpress.Utils.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Utils.UI/23.1.5": {"sha512": "xDX7KnKUgE7AwRyL07cp+zKWQGyGoqHqNhpfE5L1P1n6/juj4xZRKsPo1B1kXYuhxw7BzWrdczbX/uhS0B0jQg==", "type": "package", "path": "devexpress.utils.ui/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.utils.ui.23.1.5.nupkg.sha512", "devexpress.utils.ui.nuspec", "lib/net452/DevExpress.Utils.v23.1.UI.dll", "lib/net452/DevExpress.Utils.v23.1.UI.xml", "lib/net6.0-windows/DevExpress.Utils.v23.1.UI.dll", "lib/net6.0-windows/DevExpress.Utils.v23.1.UI.xml"]}, "DevExpress.Win/23.1.5": {"sha512": "T7uSoGQk7v+kvIV6EGzwoMOH/WYV0benLgqu7dS7M1uboRpdukjVtot03dig/1gSOyEnYajTs2cnoZvPZVQNzA==", "type": "package", "path": "devexpress.win/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.23.1.5.nupkg.sha512", "devexpress.win.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraNavBar.v23.1.Design.dll", "lib/net452/Design/WinForms/Server/DevExpress.XtraWizard.v23.1.Design.dll", "lib/net452/DevExpress.XtraNavBar.v23.1.dll", "lib/net452/DevExpress.XtraNavBar.v23.1.xml", "lib/net452/DevExpress.XtraWizard.v23.1.dll", "lib/net452/DevExpress.XtraWizard.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraNavBar.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraNavBar.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraWizard.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraWizard.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Charts/23.1.5": {"sha512": "iOh86dnuMtkoiMn6LivvcQebNY4J9KMEVMUkucFrtBLwWpRnWvg+wuQclrhN50GHFV0I6wqkuJeR3cotJwGf4g==", "type": "package", "path": "devexpress.win.charts/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.charts.23.1.5.nupkg.sha512", "devexpress.win.charts.nuspec", "lib/net452/DevExpress.XtraCharts.v23.1.Extensions.dll", "lib/net452/DevExpress.XtraCharts.v23.1.UI.dll", "lib/net452/DevExpress.XtraCharts.v23.1.UI.xml", "lib/net452/DevExpress.XtraCharts.v23.1.Wizard.dll", "lib/net452/DevExpress.XtraCharts.v23.1.Wizard.xml", "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Extensions.dll", "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.UI.dll", "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.UI.xml", "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Wizard.dll", "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Wizard.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Design/23.1.5": {"sha512": "YDB70TG/+eSrPuWTbUMgTOoCz4vyjXro/yrv2+HOl47V5DH21/qWGvEQH7A6SVuJPoSRWvNsz6eXLnhrDfiHSw==", "type": "package", "path": "devexpress.win.design/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.design.23.1.5.nupkg.sha512", "devexpress.win.design.nuspec", "lib/net6.0-windows/Design/WinForms/DevExpress.Design.Protocol.RemoteClient.v23.1.dll", "lib/net6.0-windows/Design/WinForms/DevExpress.Design.RemoteClient.v23.1.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.DataAccess.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.Design.Protocol.v23.1.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.Design.Surface.Core.v23.1.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.Design.v23.1.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.Xpo.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraBars.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraCharts.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraDiagram.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraDialogs.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraEditors.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraGantt.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraGauges.v23.1.Design.Win.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraGrid.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraLayout.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraMap.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraNavBar.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraPdfViewer.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraPivotGrid.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraPrinting.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraRichEdit.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraScheduler.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraSpellChecker.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraSpreadsheet.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraTreeList.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraTreeMap.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraVerticalGrid.v23.1.Design.dll", "lib/net6.0-windows/Design/WinForms/Server/DevExpress.XtraWizard.v23.1.Design.dll", "lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll", "lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Diagram/23.1.5": {"sha512": "UhdDnxkuxFCi+tXCe/govW9aQYXiAEMiaRJ01Iqos0ClnZ4XVkaP5Yy78ekmJQMDy39h3E4/pLi3ZFHa65Juog==", "type": "package", "path": "devexpress.win.diagram/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.diagram.23.1.5.nupkg.sha512", "devexpress.win.diagram.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraDiagram.v23.1.Design.dll", "lib/net452/DevExpress.XtraDiagram.v23.1.dll", "lib/net452/DevExpress.XtraDiagram.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraDiagram.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraDiagram.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Dialogs/23.1.5": {"sha512": "Ib/eMFC4vshgowy/IayISXIZjGUtQwiENoWIu+mtu+U1ABo8+3x+i2H35Qe127AJC4OVguKdEjZvOpdEFQgdIA==", "type": "package", "path": "devexpress.win.dialogs/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.dialogs.23.1.5.nupkg.sha512", "devexpress.win.dialogs.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraDialogs.v23.1.Design.dll", "lib/net452/DevExpress.XtraDialogs.v23.1.dll", "lib/net452/DevExpress.XtraDialogs.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraDialogs.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraDialogs.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Dialogs.Core/23.1.5": {"sha512": "BL4tHqTJ65g4+A15Qcc+6SSPKxRKI7pZw24uoPxlJEmhGW86J2MOkKdLs0Yol+XbJ/jW+47LwY2v9UAGbOsi6Q==", "type": "package", "path": "devexpress.win.dialogs.core/23.1.5", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.dialogs.core.23.1.5.nupkg.sha512", "devexpress.win.dialogs.core.nuspec", "lib/net452/DevExpress.Dialogs.v23.1.Core.dll", "lib/net6.0-windows/DevExpress.Dialogs.v23.1.Core.dll"]}, "DevExpress.Win.Gantt/23.1.5": {"sha512": "8glB+nFiky/oOUzRk9UTfdlLvsNXaoCCQnHJyC8D/5/vtj6NWE1uOBPxQFfQdWOXnGs0YXvKBKZMVuAZ1u3QmQ==", "type": "package", "path": "devexpress.win.gantt/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.gantt.23.1.5.nupkg.sha512", "devexpress.win.gantt.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraGantt.v23.1.Design.dll", "lib/net452/DevExpress.XtraGantt.v23.1.dll", "lib/net452/DevExpress.XtraGantt.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraGantt.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraGantt.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Gauges/23.1.5": {"sha512": "RPdsd/7XWiC7P69PfCIYJyQEyZf7SDbBocWJpfza0i/8sJn2ypvQ4urXao+Tl4mW+d4sPdrmKNdgc62+cD1V6w==", "type": "package", "path": "devexpress.win.gauges/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.gauges.23.1.5.nupkg.sha512", "devexpress.win.gauges.nuspec", "lib/net452/DevExpress.XtraGauges.v23.1.Presets.dll", "lib/net452/DevExpress.XtraGauges.v23.1.Presets.xml", "lib/net452/DevExpress.XtraGauges.v23.1.Win.dll", "lib/net452/DevExpress.XtraGauges.v23.1.Win.xml", "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Presets.dll", "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Presets.xml", "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Win.dll", "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Win.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Grid/23.1.5": {"sha512": "zpGObAf5BPq6WIHsDddDQ/KNej7bbWIy+dySAMpt/Hej6md+1aFNmXHpcnUSHc7Yd8T3OCsW90tvodUmBv83XQ==", "type": "package", "path": "devexpress.win.grid/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.grid.23.1.5.nupkg.sha512", "devexpress.win.grid.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraGrid.v23.1.Design.dll", "lib/net452/DevExpress.XtraGrid.v23.1.dll", "lib/net452/DevExpress.XtraGrid.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraGrid.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraGrid.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Map/23.1.5": {"sha512": "H8WjxUWQgUuNGOy7EE6a2KhAKbKkhUKfEM3aEnCoy2C2++NgeAFhd+RR7xmtQeN8pU4Dutk9p+KUjeXLBrWGGg==", "type": "package", "path": "devexpress.win.map/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.map.23.1.5.nupkg.sha512", "devexpress.win.map.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraMap.v23.1.Design.dll", "lib/net452/DevExpress.XtraMap.v23.1.dll", "lib/net452/DevExpress.XtraMap.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraMap.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraMap.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Navigation/23.1.5": {"sha512": "Rq6M+Jpw5kojFs1IpebB+ru3jBGkTsjOCv7MFwTodFxkbgKlI1HS41hq0yGbAzr3ZhrtVbg3vhSFEBQTVkja/A==", "type": "package", "path": "devexpress.win.navigation/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.navigation.23.1.5.nupkg.sha512", "devexpress.win.navigation.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraBars.v23.1.Design.dll", "lib/net452/Design/WinForms/Server/DevExpress.XtraEditors.v23.1.Design.dll", "lib/net452/Design/WinForms/Server/DevExpress.XtraLayout.v23.1.Design.dll", "lib/net452/DevExpress.XtraBars.v23.1.dll", "lib/net452/DevExpress.XtraBars.v23.1.xml", "lib/net452/DevExpress.XtraEditors.v23.1.dll", "lib/net452/DevExpress.XtraEditors.v23.1.xml", "lib/net452/DevExpress.XtraLayout.v23.1.dll", "lib/net452/DevExpress.XtraLayout.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraBars.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraBars.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraEditors.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraEditors.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraLayout.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraLayout.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.PdfViewer/23.1.5": {"sha512": "FOUvDROV1zsQBh+PesPMbUZOSZOGis3IDYBpZix32dbhFMjDuYPn1FtDe6n97LPX6e2WyzYCdbsIuxjtczeXeg==", "type": "package", "path": "devexpress.win.pdfviewer/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.pdfviewer.23.1.5.nupkg.sha512", "devexpress.win.pdfviewer.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraPdfViewer.v23.1.Design.dll", "lib/net452/DevExpress.XtraPdfViewer.v23.1.dll", "lib/net452/DevExpress.XtraPdfViewer.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraPdfViewer.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraPdfViewer.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.PivotGrid/23.1.5": {"sha512": "Jxfjt55mBP2p3eChme1+aolCDvI2K59PwGXwpOn1Y+68WP2PJKZxaAyqZYeJNho1NmtJ9qTKJxLk3n+DjmGnCA==", "type": "package", "path": "devexpress.win.pivotgrid/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.pivotgrid.23.1.5.nupkg.sha512", "devexpress.win.pivotgrid.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraPivotGrid.v23.1.Design.dll", "lib/net452/DevExpress.XtraPivotGrid.v23.1.dll", "lib/net452/DevExpress.XtraPivotGrid.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraPivotGrid.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraPivotGrid.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Printing/23.1.5": {"sha512": "t1Y6ER7dIdSBpzdC8RIltLz3hVNYbYwswYwIVvtAB423JHgMQZAKQMgJOTg5I9nT+G+UQ+Zh87MMyjiCgYUXbg==", "type": "package", "path": "devexpress.win.printing/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.printing.23.1.5.nupkg.sha512", "devexpress.win.printing.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraPrinting.v23.1.Design.dll", "lib/net452/DevExpress.XtraPrinting.v23.1.dll", "lib/net452/DevExpress.XtraPrinting.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraPrinting.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraPrinting.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Reporting/23.1.5": {"sha512": "ow7eDN4uZ4YjdXX8nzqvMC+g1J/Nty3IhHfWdB7DygETpSHfEVf+5WD8hoQEaPtEt3e4/A9ghm1jJrjiksKdIA==", "type": "package", "path": "devexpress.win.reporting/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.reporting.23.1.5.nupkg.sha512", "devexpress.win.reporting.nuspec", "lib/net452/DevExpress.XtraReports.v23.1.Extensions.dll", "lib/net452/DevExpress.XtraReports.v23.1.Extensions.xml", "lib/net6.0-windows/DevExpress.XtraReports.v23.1.Extensions.dll", "lib/net6.0-windows/DevExpress.XtraReports.v23.1.Extensions.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.RichEdit/23.1.5": {"sha512": "TGbujl4/dA7e96B2UYOIxq7VOLnToNYoPQZJz/93kGrdH5VMX/81QxjL3/7gFtLrPJCL0M3dRI4LcBDFK5EBTA==", "type": "package", "path": "devexpress.win.richedit/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.richedit.23.1.5.nupkg.sha512", "devexpress.win.richedit.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraRichEdit.v23.1.Design.dll", "lib/net452/DevExpress.XtraRichEdit.v23.1.dll", "lib/net452/DevExpress.XtraRichEdit.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraRichEdit.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraRichEdit.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Scheduler/23.1.5": {"sha512": "yerByDkBjIMobWfjITRm7DRI1okpXzhncwbZs7MQRTFy/wTiBsZZkB2BL+K0W4HgoD1X90Rg2eIs1m0GVRV4tQ==", "type": "package", "path": "devexpress.win.scheduler/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.scheduler.23.1.5.nupkg.sha512", "devexpress.win.scheduler.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraScheduler.v23.1.Design.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SchedulerExtensions/23.1.5": {"sha512": "+M5Aunh8EzCXepifV4EZzLGwkzhD0GP9qC7OL0z2DLEh5F6arVC1eFsNGCXE/YzwpEOz8VmmYIFiJJLkd4Nd/Q==", "type": "package", "path": "devexpress.win.schedulerextensions/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.schedulerextensions.23.1.5.nupkg.sha512", "devexpress.win.schedulerextensions.nuspec", "lib/net452/DevExpress.XtraScheduler.v23.1.Extensions.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.Extensions.xml", "lib/net452/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.xml", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Extensions.dll", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Extensions.xml", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.dll", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SchedulerReporting/23.1.5": {"sha512": "dTQ+riBBmnl9pTXMZR8IE1NduESzbBmNCWNFyw49J8tyeXdMyg8/bbI2gsjby+c0BMmE5ZXpyXEQbT9OsKXp+w==", "type": "package", "path": "devexpress.win.schedulerreporting/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.schedulerreporting.23.1.5.nupkg.sha512", "devexpress.win.schedulerreporting.nuspec", "lib/net452/DevExpress.XtraScheduler.v23.1.Reporting.dll", "lib/net452/DevExpress.XtraScheduler.v23.1.Reporting.xml", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.dll", "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SpellChecker/23.1.5": {"sha512": "/Ns96bEciO7q5rCPKt+tAz3kviFT1x6rFX8278RH5trIt8zkg+Zv0b+uA7LHSlGY+wwsmPNOOBaEmEQXY8qa+Q==", "type": "package", "path": "devexpress.win.spellchecker/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.spellchecker.23.1.5.nupkg.sha512", "devexpress.win.spellchecker.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraSpellChecker.v23.1.Design.dll", "lib/net452/DevExpress.XtraSpellChecker.v23.1.dll", "lib/net452/DevExpress.XtraSpellChecker.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraSpellChecker.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraSpellChecker.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Spreadsheet/23.1.5": {"sha512": "y4C4TYEh5QuCn+eBIZD7OOnCd38c/nHvdbconWozd+gm2vzMHtgM+8sxrXIza3Q0mmQU/dZK++0DV0s9mTxaVQ==", "type": "package", "path": "devexpress.win.spreadsheet/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.spreadsheet.23.1.5.nupkg.sha512", "devexpress.win.spreadsheet.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraSpreadsheet.v23.1.Design.dll", "lib/net452/DevExpress.XtraSpreadsheet.v23.1.dll", "lib/net452/DevExpress.XtraSpreadsheet.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraSpreadsheet.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraSpreadsheet.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.TreeList/23.1.5": {"sha512": "QJ6XaSoAmvpmkqFEMxV+kFH7WxCKodFkv8hp8kRzY6RMyxCZ1HfHNml4sK5cTQDyh7dIlR+1VphxF9tDNy2UTQ==", "type": "package", "path": "devexpress.win.treelist/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.treelist.23.1.5.nupkg.sha512", "devexpress.win.treelist.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraTreeList.v23.1.Design.dll", "lib/net452/DevExpress.XtraTreeList.v23.1.dll", "lib/net452/DevExpress.XtraTreeList.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraTreeList.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraTreeList.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.TreeMap/23.1.5": {"sha512": "d3hJQd8gY2gYzdoqRl5pQS7tNxnU1VOnSm6qnVMtymrxbzOUtzwyrtNkK9nvvswAY7pU6YorV62Ghh7C4Nh72A==", "type": "package", "path": "devexpress.win.treemap/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.treemap.23.1.5.nupkg.sha512", "devexpress.win.treemap.nuspec", "lib/net452/DevExpress.XtraTreeMap.v23.1.UI.dll", "lib/net452/DevExpress.XtraTreeMap.v23.1.UI.xml", "lib/net6.0-windows/DevExpress.XtraTreeMap.v23.1.UI.dll", "lib/net6.0-windows/DevExpress.XtraTreeMap.v23.1.UI.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.VerticalGrid/23.1.5": {"sha512": "zvznW4JKfRicHq40MEQNxBJI5odoZD/M7JKnRs/Itt1qSbI6jbNT+dk8g6PZcUbb9S5HrKkFmSWVWMQyjkN4OQ==", "type": "package", "path": "devexpress.win.verticalgrid/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.verticalgrid.23.1.5.nupkg.sha512", "devexpress.win.verticalgrid.nuspec", "lib/net452/Design/WinForms/Server/DevExpress.XtraVerticalGrid.v23.1.Design.dll", "lib/net452/DevExpress.XtraVerticalGrid.v23.1.dll", "lib/net452/DevExpress.XtraVerticalGrid.v23.1.xml", "lib/net6.0-windows/DevExpress.XtraVerticalGrid.v23.1.dll", "lib/net6.0-windows/DevExpress.XtraVerticalGrid.v23.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Xpo/23.1.5": {"sha512": "HIPM90H3u7EM4RyynytYUtNqhJ1CGmw2Cfm0Z1ISMZkwReCc/KUuvbexqJ7jVtcVPkRaZnhXbVgaL/TW2CyP1g==", "type": "package", "path": "devexpress.xpo/23.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.xpo.23.1.5.nupkg.sha512", "devexpress.xpo.nuspec", "lib/net452/DevExpress.Xpo.v23.1.dll", "lib/net452/DevExpress.Xpo.v23.1.xml", "lib/netstandard2.0/DevExpress.Xpo.v23.1.dll", "lib/netstandard2.0/DevExpress.Xpo.v23.1.xml", "readme.txt", "tools/VisualStudioToolsManifest.xml"]}, "Mapster/7.3.0": {"sha512": "NrCUX/rJa5PTyo6iW4AL5dZLU9PDNlYnrJOVjgdpo5OQM9EtWH2CMHnC5sSuJWC0d0b0SnmeRrIviEem6WxtuQ==", "type": "package", "path": "mapster/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Mapster.dll", "lib/netstandard2.0/Mapster.dll", "mapster.7.3.0.nupkg.sha512", "mapster.nuspec"]}, "Mapster.Core/1.2.0": {"sha512": "TNdqZk2zAuBYfJF88D/3clQTOyOdqr1crU81yZQtlGa+e7FYWhJdK/buBWT+TpM3qQko9UzmzfOT4iq3JCs/ZA==", "type": "package", "path": "mapster.core/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net40/Mapster.Core.dll", "lib/net45/Mapster.Core.dll", "lib/netstandard1.3/Mapster.Core.dll", "lib/netstandard2.0/Mapster.Core.dll", "mapster.core.1.2.0.nupkg.sha512", "mapster.core.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"sha512": "W8DPQjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {"sha512": "ZFc3Pqiox/AcJECaYHXZtdB8XF12pJKvlrIxavL01z7VpW8Oy1d4tAgcnZfgburH5eVtAH4Bi2QKTHnN8va0ug==", "type": "package", "path": "microsoft.dotnet.upgradeassistant.extensions.default.analyzers/0.4.410601", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.Common.dll", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.CodeFixes.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.Common.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.CodeFixes.dll", "build/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.props", "build/TypeMaps.props", "build/WebTypeReplacements.typemap", "icon.png", "microsoft.dotnet.upgradeassistant.extensions.default.analyzers.0.4.410601.nupkg.sha512", "microsoft.dotnet.upgradeassistant.extensions.default.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.Extensions.Configuration/2.0.0": {"sha512": "SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "type": "package", "path": "microsoft.extensions.configuration/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.2.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec"]}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"sha512": "0qbNyxGpuNP/fuQ3FLHesm1Vn/83qYcAgVsi1UQCQN1peY4YH1uiizOh4xbYkQyxiVMD/c/zhiYYv94G0DXSSA==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/3.1.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"sha512": "IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec"]}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"sha512": "elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "type": "package", "path": "microsoft.extensions.dependencyinjection/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"sha512": "h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/3.0.0": {"sha512": "Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "type": "package", "path": "microsoft.extensions.dependencymodel/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/net451/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"sha512": "U7ffyzrPfRDH5K3h/mBpqJVoHbppw1kc1KyHZcZeDR7b1A0FRaqMSiizGpN9IGwWs9BuN7oXIKFyviuSGBjHtQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/3.1.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec"]}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"sha512": "7ZJUKwPipkDvuv2KJPZ3r01wp2AWNMiYH+61i0dL89F7QICknjKpWgLKLpTSUYFgl77S3b4264I6i4HzDdrb2A==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/3.1.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/7.0.0": {"sha512": "Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "type": "package", "path": "microsoft.extensions.logging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.7.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"sha512": "kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/5.0.10": {"sha512": "pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "type": "package", "path": "microsoft.extensions.objectpool/5.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net5.0/Microsoft.Extensions.ObjectPool.dll", "lib/net5.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.5.0.10.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/7.0.0": {"sha512": "lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "type": "package", "path": "microsoft.extensions.options/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.7.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"sha512": "Y/lGICwO27fCkQRK3tZseVzFjZaxfGmui990E67sB4MuiPzdJHnJDS/BeYWrHShSSBgCl4KyKRx4ux686fftPg==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"sha512": "xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"sha512": "9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.35.0": {"sha512": "jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "type": "package", "path": "microsoft.identitymodel.logging/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.35.0": {"sha512": "RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "type": "package", "path": "microsoft.identitymodel.tokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.VisualBasic/10.4.0-preview.18571.3": {"sha512": "9o+ngDFh/65nEtLaMJ4I7nsMFqTYStbMzdp0gX0ytOFs77WrCNMoEEt72fRHvDbcPXmFD/+UF6LOPqEdcU//0Q==", "type": "package", "path": "microsoft.visualbasic/10.4.0-preview.18571.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netcore50/Microsoft.VisualBasic.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.VisualBasic.dll", "lib/netstandard2.0/Microsoft.VisualBasic.dll", "lib/portable-net45+win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "microsoft.visualbasic.10.4.0-preview.18571.3.nupkg.sha512", "microsoft.visualbasic.nuspec", "ref/net45/_._", "ref/netcore50/Microsoft.VisualBasic.dll", "ref/netcore50/Microsoft.VisualBasic.xml", "ref/netcore50/de/Microsoft.VisualBasic.xml", "ref/netcore50/es/Microsoft.VisualBasic.xml", "ref/netcore50/fr/Microsoft.VisualBasic.xml", "ref/netcore50/it/Microsoft.VisualBasic.xml", "ref/netcore50/ja/Microsoft.VisualBasic.xml", "ref/netcore50/ko/Microsoft.VisualBasic.xml", "ref/netcore50/ru/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hans/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hant/Microsoft.VisualBasic.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/Microsoft.VisualBasic.dll", "ref/netstandard1.1/Microsoft.VisualBasic.xml", "ref/netstandard1.1/de/Microsoft.VisualBasic.xml", "ref/netstandard1.1/es/Microsoft.VisualBasic.xml", "ref/netstandard1.1/fr/Microsoft.VisualBasic.xml", "ref/netstandard1.1/it/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ja/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ko/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ru/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hans/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hant/Microsoft.VisualBasic.xml", "ref/netstandard2.0/Microsoft.VisualBasic.dll", "ref/netstandard2.0/Microsoft.VisualBasic.xml", "ref/portable-net45+win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {"sha512": "JwM65WXVca58WzqY/Rpz7FGyHbN/SMdyr/3EI2CwPIYkB55EIRJUdPQJwO64x3ntOwPQoqCATKuDYA9K7Np5Ww==", "type": "package", "path": "microsoft.win32.registry.accesscontrol/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.Registry.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.Registry.AccessControl.targets", "lib/net462/Microsoft.Win32.Registry.AccessControl.dll", "lib/net462/Microsoft.Win32.Registry.AccessControl.xml", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.xml", "microsoft.win32.registry.accesscontrol.7.0.0.nupkg.sha512", "microsoft.win32.registry.accesscontrol.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Win32.SystemEvents/7.0.0": {"sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "type": "package", "path": "microsoft.win32.systemevents/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.7.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Windows.Compatibility/7.0.3": {"sha512": "vVTf/gmV6tLpi8IvmIwhD/86xWcCPazbrGRoxjXe5LQ0HqcmLgKOTYzvE058kRtVTZgYuQFFrErrXMFG4weCeQ==", "type": "package", "path": "microsoft.windows.compatibility/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Windows.Compatibility.targets", "microsoft.windows.compatibility.7.0.3.nupkg.sha512", "microsoft.windows.compatibility.nuspec", "useSharedDesignerContext.txt"]}, "NETStandard.Library/2.0.3": {"sha512": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "type": "package", "path": "netstandard.library/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/NETStandard.Library.targets", "build/netstandard2.0/ref/Microsoft.Win32.Primitives.dll", "build/netstandard2.0/ref/System.AppContext.dll", "build/netstandard2.0/ref/System.Collections.Concurrent.dll", "build/netstandard2.0/ref/System.Collections.NonGeneric.dll", "build/netstandard2.0/ref/System.Collections.Specialized.dll", "build/netstandard2.0/ref/System.Collections.dll", "build/netstandard2.0/ref/System.ComponentModel.Composition.dll", "build/netstandard2.0/ref/System.ComponentModel.EventBasedAsync.dll", "build/netstandard2.0/ref/System.ComponentModel.Primitives.dll", "build/netstandard2.0/ref/System.ComponentModel.TypeConverter.dll", "build/netstandard2.0/ref/System.ComponentModel.dll", "build/netstandard2.0/ref/System.Console.dll", "build/netstandard2.0/ref/System.Core.dll", "build/netstandard2.0/ref/System.Data.Common.dll", "build/netstandard2.0/ref/System.Data.dll", "build/netstandard2.0/ref/System.Diagnostics.Contracts.dll", "build/netstandard2.0/ref/System.Diagnostics.Debug.dll", "build/netstandard2.0/ref/System.Diagnostics.FileVersionInfo.dll", "build/netstandard2.0/ref/System.Diagnostics.Process.dll", "build/netstandard2.0/ref/System.Diagnostics.StackTrace.dll", "build/netstandard2.0/ref/System.Diagnostics.TextWriterTraceListener.dll", "build/netstandard2.0/ref/System.Diagnostics.Tools.dll", "build/netstandard2.0/ref/System.Diagnostics.TraceSource.dll", "build/netstandard2.0/ref/System.Diagnostics.Tracing.dll", "build/netstandard2.0/ref/System.Drawing.Primitives.dll", "build/netstandard2.0/ref/System.Drawing.dll", "build/netstandard2.0/ref/System.Dynamic.Runtime.dll", "build/netstandard2.0/ref/System.Globalization.Calendars.dll", "build/netstandard2.0/ref/System.Globalization.Extensions.dll", "build/netstandard2.0/ref/System.Globalization.dll", "build/netstandard2.0/ref/System.IO.Compression.FileSystem.dll", "build/netstandard2.0/ref/System.IO.Compression.ZipFile.dll", "build/netstandard2.0/ref/System.IO.Compression.dll", "build/netstandard2.0/ref/System.IO.FileSystem.DriveInfo.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Watcher.dll", "build/netstandard2.0/ref/System.IO.FileSystem.dll", "build/netstandard2.0/ref/System.IO.IsolatedStorage.dll", "build/netstandard2.0/ref/System.IO.MemoryMappedFiles.dll", "build/netstandard2.0/ref/System.IO.Pipes.dll", "build/netstandard2.0/ref/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/ref/System.IO.dll", "build/netstandard2.0/ref/System.Linq.Expressions.dll", "build/netstandard2.0/ref/System.Linq.Parallel.dll", "build/netstandard2.0/ref/System.Linq.Queryable.dll", "build/netstandard2.0/ref/System.Linq.dll", "build/netstandard2.0/ref/System.Net.Http.dll", "build/netstandard2.0/ref/System.Net.NameResolution.dll", "build/netstandard2.0/ref/System.Net.NetworkInformation.dll", "build/netstandard2.0/ref/System.Net.Ping.dll", "build/netstandard2.0/ref/System.Net.Primitives.dll", "build/netstandard2.0/ref/System.Net.Requests.dll", "build/netstandard2.0/ref/System.Net.Security.dll", "build/netstandard2.0/ref/System.Net.Sockets.dll", "build/netstandard2.0/ref/System.Net.WebHeaderCollection.dll", "build/netstandard2.0/ref/System.Net.WebSockets.Client.dll", "build/netstandard2.0/ref/System.Net.WebSockets.dll", "build/netstandard2.0/ref/System.Net.dll", "build/netstandard2.0/ref/System.Numerics.dll", "build/netstandard2.0/ref/System.ObjectModel.dll", "build/netstandard2.0/ref/System.Reflection.Extensions.dll", "build/netstandard2.0/ref/System.Reflection.Primitives.dll", "build/netstandard2.0/ref/System.Reflection.dll", "build/netstandard2.0/ref/System.Resources.Reader.dll", "build/netstandard2.0/ref/System.Resources.ResourceManager.dll", "build/netstandard2.0/ref/System.Resources.Writer.dll", "build/netstandard2.0/ref/System.Runtime.CompilerServices.VisualC.dll", "build/netstandard2.0/ref/System.Runtime.Extensions.dll", "build/netstandard2.0/ref/System.Runtime.Handles.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.RuntimeInformation.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.dll", "build/netstandard2.0/ref/System.Runtime.Numerics.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Formatters.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Json.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Primitives.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Xml.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.dll", "build/netstandard2.0/ref/System.Runtime.dll", "build/netstandard2.0/ref/System.Security.Claims.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Algorithms.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Csp.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Encoding.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Primitives.dll", "build/netstandard2.0/ref/System.Security.Cryptography.X509Certificates.dll", "build/netstandard2.0/ref/System.Security.Principal.dll", "build/netstandard2.0/ref/System.Security.SecureString.dll", "build/netstandard2.0/ref/System.ServiceModel.Web.dll", "build/netstandard2.0/ref/System.Text.Encoding.Extensions.dll", "build/netstandard2.0/ref/System.Text.Encoding.dll", "build/netstandard2.0/ref/System.Text.RegularExpressions.dll", "build/netstandard2.0/ref/System.Threading.Overlapped.dll", "build/netstandard2.0/ref/System.Threading.Tasks.Parallel.dll", "build/netstandard2.0/ref/System.Threading.Tasks.dll", "build/netstandard2.0/ref/System.Threading.Thread.dll", "build/netstandard2.0/ref/System.Threading.ThreadPool.dll", "build/netstandard2.0/ref/System.Threading.Timer.dll", "build/netstandard2.0/ref/System.Threading.dll", "build/netstandard2.0/ref/System.Transactions.dll", "build/netstandard2.0/ref/System.ValueTuple.dll", "build/netstandard2.0/ref/System.Web.dll", "build/netstandard2.0/ref/System.Windows.dll", "build/netstandard2.0/ref/System.Xml.Linq.dll", "build/netstandard2.0/ref/System.Xml.ReaderWriter.dll", "build/netstandard2.0/ref/System.Xml.Serialization.dll", "build/netstandard2.0/ref/System.Xml.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.dll", "build/netstandard2.0/ref/System.Xml.XmlDocument.dll", "build/netstandard2.0/ref/System.Xml.XmlSerializer.dll", "build/netstandard2.0/ref/System.Xml.dll", "build/netstandard2.0/ref/System.dll", "build/netstandard2.0/ref/mscorlib.dll", "build/netstandard2.0/ref/netstandard.dll", "build/netstandard2.0/ref/netstandard.xml", "lib/netstandard1.0/_._", "netstandard.library.2.0.3.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"sha512": "CBvgRaF+M0xGLDv2Geb/0v0LEADheH8aK72GRAUJdnqnJVsQO60ki1XO8M3keEhnjm+T5NvLm41pNXAVYAPiSg==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "5VCyRCtCIYU8FR/W8oo7ouFuJ8tmAg9ddsuXhfCKZfZrbaVZSKxkmNBa6fxkfYPueD0jQfOvwFBmE5c6zalCSw==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "DV9dWDUs23OoZqMWl5IhLr3D+b9koDiSHQxFKdYgWnQbnthv8c/yDjrlrI8nMrDc71RAKCO8jlUojzuPMX04gg==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.native.System.IO.Ports/7.0.0": {"sha512": "L4Ivegqc3B0Fee7VifFy2JST9nndm+uvJ0viLIZUaImDfnr+JmRin9Tbqd56KuMtm0eVxHpNOWZBPtKrA/1h5Q==", "type": "package", "path": "runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "jFwh4sKSXZ7al5XrItEO4GdGWa6XNxvNx+LhEHjrSzOwawO1znwJ+Dy+VjnrkySX9Qi4bnHNLoiqOXbqMuka4g==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "X4LrHEfke/z9+z+iuVr35NlkhdZldY8JGNMYUN+sfPK/U/6TcE+vP44I0Yv0ir1v0bqIzq3v6Qdv1c1vmp8s4g==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Serilog/2.10.0": {"sha512": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "type": "package", "path": "serilog/2.10.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.dll", "lib/net45/Serilog.xml", "lib/net46/Serilog.dll", "lib/net46/Serilog.xml", "lib/netstandard1.0/Serilog.dll", "lib/netstandard1.0/Serilog.xml", "lib/netstandard1.3/Serilog.dll", "lib/netstandard1.3/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.2.10.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/6.1.0": {"sha512": "iMwFUJDN+/yWIPz4TKCliagJ1Yn//SceCYCzgdPwe/ECYUwb5/WUL8cTzRKV+tFwxGjLEV/xpm0GupS5RwbhSQ==", "type": "package", "path": "serilog.aspnetcore/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net5.0/Serilog.AspNetCore.dll", "lib/net5.0/Serilog.AspNetCore.xml", "lib/netcoreapp3.1/Serilog.AspNetCore.dll", "lib/netcoreapp3.1/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.6.1.0.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Extensions.Hosting/5.0.1": {"sha512": "o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "type": "package", "path": "serilog.extensions.hosting/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.5.0.1.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/3.1.0": {"sha512": "IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "type": "package", "path": "serilog.extensions.logging/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.3.1.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/1.1.0": {"sha512": "pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "type": "package", "path": "serilog.formatting.compact/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/Serilog.Formatting.Compact.dll", "lib/net452/Serilog.Formatting.Compact.xml", "lib/netstandard1.1/Serilog.Formatting.Compact.dll", "lib/netstandard1.1/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog.formatting.compact.1.1.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/3.3.0": {"sha512": "7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "type": "package", "path": "serilog.settings.configuration/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net451/Serilog.Settings.Configuration.dll", "lib/net451/Serilog.Settings.Configuration.xml", "lib/net461/Serilog.Settings.Configuration.dll", "lib/net461/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.3.3.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Console/4.0.1": {"sha512": "apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "type": "package", "path": "serilog.sinks.console/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Console.dll", "lib/net45/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/netstandard1.3/Serilog.Sinks.Console.dll", "lib/netstandard1.3/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.4.0.1.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/2.0.0": {"sha512": "Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "type": "package", "path": "serilog.sinks.debug/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Debug.dll", "lib/net45/Serilog.Sinks.Debug.xml", "lib/net46/Serilog.Sinks.Debug.dll", "lib/net46/Serilog.Sinks.Debug.xml", "lib/netstandard1.0/Serilog.Sinks.Debug.dll", "lib/netstandard1.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.1/Serilog.Sinks.Debug.dll", "lib/netstandard2.1/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.2.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Collections.Immutable/1.5.0": {"sha512": "EXKiDFsChZW0RjrZ4FYHu9aW6+P4MCgEDCklsVseRfhoO0F+dXeMSsMRAlVXIo06kGJ/zv+2w1a2uc2+kxxSaQ==", "type": "package", "path": "system.collections.immutable/1.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.1.5.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Composition/7.0.0": {"sha512": "orv0h38ZVPCPo/FW0LGv8/TigXwX8cIwXeQcaNYhikkqELDm8sUFLMcof/Sjcq5EvYCm5NA7MV3hG4u75H44UQ==", "type": "package", "path": "system.componentmodel.composition/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.targets", "lib/net462/_._", "lib/net6.0/System.ComponentModel.Composition.dll", "lib/net6.0/System.ComponentModel.Composition.xml", "lib/net7.0/System.ComponentModel.Composition.dll", "lib/net7.0/System.ComponentModel.Composition.xml", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.xml", "system.componentmodel.composition.7.0.0.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Composition.Registration/7.0.0": {"sha512": "yy/xYOznnc7Hfg2/LeVqAMlJGv1v7b1ILxFShzx5PWUv53PwU0MaKPG8Dh9DC3gxayzw44UVuQJImhw7LtMKlw==", "type": "package", "path": "system.componentmodel.composition.registration/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.Registration.targets", "lib/net462/_._", "lib/net6.0/System.ComponentModel.Composition.Registration.dll", "lib/net6.0/System.ComponentModel.Composition.Registration.xml", "lib/net7.0/System.ComponentModel.Composition.Registration.dll", "lib/net7.0/System.ComponentModel.Composition.Registration.xml", "lib/netstandard2.1/System.ComponentModel.Composition.Registration.dll", "lib/netstandard2.1/System.ComponentModel.Composition.Registration.xml", "system.componentmodel.composition.registration.7.0.0.nupkg.sha512", "system.componentmodel.composition.registration.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/7.0.0": {"sha512": "WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "type": "package", "path": "system.configuration.configurationmanager/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.7.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.DataSetExtensions/4.5.0": {"sha512": "221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "type": "package", "path": "system.data.datasetextensions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netstandard2.0/System.Data.DataSetExtensions.dll", "ref/net45/_._", "ref/netstandard2.0/System.Data.DataSetExtensions.dll", "system.data.datasetextensions.4.5.0.nupkg.sha512", "system.data.datasetextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.Odbc/7.0.0": {"sha512": "siwu7NoCsfHa9bfw2a2wSeTt2c/rhk3X8I28nJln1dlxdW3KqhRp0aW87yH1XkCo9h8zO1qcIfdTHO7YvvWLEA==", "type": "package", "path": "system.data.odbc/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.Odbc.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.Odbc.targets", "lib/net462/System.Data.Odbc.dll", "lib/net462/System.Data.Odbc.xml", "lib/net6.0/System.Data.Odbc.dll", "lib/net6.0/System.Data.Odbc.xml", "lib/net7.0/System.Data.Odbc.dll", "lib/net7.0/System.Data.Odbc.xml", "lib/netstandard2.0/System.Data.Odbc.dll", "lib/netstandard2.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.xml", "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll", "runtimes/illumos/lib/net7.0/System.Data.Odbc.xml", "runtimes/ios/lib/net7.0/System.Data.Odbc.dll", "runtimes/ios/lib/net7.0/System.Data.Odbc.xml", "runtimes/linux/lib/net6.0/System.Data.Odbc.dll", "runtimes/linux/lib/net6.0/System.Data.Odbc.xml", "runtimes/linux/lib/net7.0/System.Data.Odbc.dll", "runtimes/linux/lib/net7.0/System.Data.Odbc.xml", "runtimes/osx/lib/net6.0/System.Data.Odbc.dll", "runtimes/osx/lib/net6.0/System.Data.Odbc.xml", "runtimes/osx/lib/net7.0/System.Data.Odbc.dll", "runtimes/osx/lib/net7.0/System.Data.Odbc.xml", "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll", "runtimes/solaris/lib/net7.0/System.Data.Odbc.xml", "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll", "runtimes/tvos/lib/net7.0/System.Data.Odbc.xml", "runtimes/win/lib/net6.0/System.Data.Odbc.dll", "runtimes/win/lib/net6.0/System.Data.Odbc.xml", "runtimes/win/lib/net7.0/System.Data.Odbc.dll", "runtimes/win/lib/net7.0/System.Data.Odbc.xml", "system.data.odbc.7.0.0.nupkg.sha512", "system.data.odbc.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/7.0.0": {"sha512": "bhAs+5X5acgg3zQ6N4HqxqfwwmqWJzgt54BC8iwygcqa2jktxDFzxwN83GNvqgoTcTs2tenDS/jmhC+AQsmcyg==", "type": "package", "path": "system.data.oledb/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "system.data.oledb.7.0.0.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.EventLog/7.0.0": {"sha512": "eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "type": "package", "path": "system.diagnostics.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.7.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/7.0.0": {"sha512": "L+zIMEaXp1vA4wZk1KLMpk6tvU0xy94R0IfmhkmTWeC4KwShsmAfbg5I19LgjsCTYp6GVdXZ2aHluVWL0QqBdA==", "type": "package", "path": "system.diagnostics.performancecounter/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.7.0.0.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/7.0.1": {"sha512": "Z4FVdUJEVXbf7/f/hU6cFZDtxN5ozUVKJMzXoHmC+GCeTcqzlxqmWtxurejxG3K+kZ6H0UKwNshoK1CYnmJ1sg==", "type": "package", "path": "system.directoryservices/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.dll", "lib/net6.0/System.DirectoryServices.xml", "lib/net7.0/System.DirectoryServices.dll", "lib/net7.0/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.xml", "system.directoryservices.7.0.1.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.AccountManagement/7.0.0": {"sha512": "qMpVgR5+XactuWzpqsiif++lnTzfDESbQv4UYFZpgdRvFCFIi4JgufOITCDlu+x2vEmwYOVbwrR1N365dDJRLg==", "type": "package", "path": "system.directoryservices.accountmanagement/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.AccountManagement.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.AccountManagement.dll", "lib/net6.0/System.DirectoryServices.AccountManagement.xml", "lib/net7.0/System.DirectoryServices.AccountManagement.dll", "lib/net7.0/System.DirectoryServices.AccountManagement.xml", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.xml", "system.directoryservices.accountmanagement.7.0.0.nupkg.sha512", "system.directoryservices.accountmanagement.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/7.0.1": {"sha512": "t9hsL+UYRzNs30pnT2Tdx6ngX8McFUjru0a0ekNgu/YXfkXN+dx5OvSEv0/p7H2q3pdJLH7TJPWX7e55J8QB9A==", "type": "package", "path": "system.directoryservices.protocols/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.Protocols.dll", "lib/net6.0/System.DirectoryServices.Protocols.xml", "lib/net7.0/System.DirectoryServices.Protocols.dll", "lib/net7.0/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.7.0.1.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/7.0.0": {"sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "type": "package", "path": "system.drawing.common/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/net7.0/System.Drawing.Common.dll", "runtimes/win/lib/net7.0/System.Drawing.Common.xml", "system.drawing.common.7.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/8.0.1": {"sha512": "XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "type": "package", "path": "system.formats.asn1/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.8.0.1.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"sha512": "yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Packaging/7.0.0": {"sha512": "+j5ezLP7785/pd4taKQhXAWsymsIW2nTnE/U3/jpGZzcJx5lip6qkj6UrxSE7ZYZfL0GaLuymwGLqwJV/c7O7Q==", "type": "package", "path": "system.io.packaging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net6.0/System.IO.Packaging.dll", "lib/net6.0/System.IO.Packaging.xml", "lib/net7.0/System.IO.Packaging.dll", "lib/net7.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.7.0.0.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/7.0.0": {"sha512": "0nWQjM5IofaIGpvkifN+LLuYwBG6BHlpmphLhhOJepcW12G8qToGuNDRgBzeTVBZzp33wVsESSZ8hUOCfq+8QA==", "type": "package", "path": "system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net6.0/System.IO.Ports.dll", "lib/net6.0/System.IO.Ports.xml", "lib/net7.0/System.IO.Ports.dll", "lib/net7.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net6.0/System.IO.Ports.dll", "runtimes/unix/lib/net6.0/System.IO.Ports.xml", "runtimes/unix/lib/net7.0/System.IO.Ports.dll", "runtimes/unix/lib/net7.0/System.IO.Ports.xml", "runtimes/win/lib/net6.0/System.IO.Ports.dll", "runtimes/win/lib/net6.0/System.IO.Ports.xml", "runtimes/win/lib/net7.0/System.IO.Ports.dll", "runtimes/win/lib/net7.0/System.IO.Ports.xml", "system.io.ports.7.0.0.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.2": {"sha512": "/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "type": "package", "path": "system.management/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Private.ServiceModel/4.10.2": {"sha512": "bi2/w2EDXqxno8zfbt6vHcrpGw0Pav8tEMzmJraHwJvWYJd45wcqr7gNa2IUs91j4z+BNGMooStaWS6pm2Lq0A==", "type": "package", "path": "system.private.servicemodel/4.10.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/System.Private.ServiceModel.dll", "lib/netstandard2.0/System.Private.ServiceModel.pdb", "lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll", "ref/netstandard2.0/_._", "system.private.servicemodel.4.10.2.nupkg.sha512", "system.private.servicemodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Context/7.0.0": {"sha512": "rVf4vEyGQphXTITF39uXlgTcp8Ekcu2aNwxyVLU7fDyNOk0W+/PPpj9PoC2cFL4wgJZJltiss5eQptE2C4f1Sw==", "type": "package", "path": "system.reflection.context/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Context.targets", "lib/net462/_._", "lib/net6.0/System.Reflection.Context.dll", "lib/net6.0/System.Reflection.Context.xml", "lib/net7.0/System.Reflection.Context.dll", "lib/net7.0/System.Reflection.Context.xml", "lib/netstandard2.0/System.Reflection.Context.dll", "lib/netstandard2.0/System.Reflection.Context.xml", "lib/netstandard2.1/System.Reflection.Context.dll", "lib/netstandard2.1/System.Reflection.Context.xml", "system.reflection.context.7.0.0.nupkg.sha512", "system.reflection.context.nuspec", "useSharedDesignerContext.txt"]}, "System.Reflection.DispatchProxy/4.7.1": {"sha512": "C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "type": "package", "path": "system.reflection.dispatchproxy/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Reflection.DispatchProxy.dll", "lib/net461/System.Reflection.DispatchProxy.xml", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.xml", "lib/netstandard1.3/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Reflection.DispatchProxy.dll", "ref/net461/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/System.Reflection.DispatchProxy.dll", "ref/netstandard1.3/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/de/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/es/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/fr/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/it/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ja/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ko/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ru/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hans/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hant/System.Reflection.DispatchProxy.xml", "ref/netstandard2.0/System.Reflection.DispatchProxy.dll", "ref/netstandard2.0/System.Reflection.DispatchProxy.xml", "ref/uap10.0.16299/System.Reflection.DispatchProxy.dll", "ref/uap10.0.16299/System.Reflection.DispatchProxy.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.DispatchProxy.dll", "runtimes/win-aot/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "runtimes/win/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "system.reflection.dispatchproxy.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.7.0": {"sha512": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "type": "package", "path": "system.reflection.emit.lightweight/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/7.0.0": {"sha512": "M0riW7Zgxca3Elp1iZVhzH7PWWT5bPSrdMFGCAGoH1n9YLuXOYE78ryui051Icf3swWWa8feBRoSxOCYwgMy8w==", "type": "package", "path": "system.runtime.caching/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/net7.0/System.Runtime.Caching.dll", "lib/net7.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/net7.0/System.Runtime.Caching.dll", "runtimes/win/lib/net7.0/System.Runtime.Caching.xml", "system.runtime.caching.7.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/7.0.2": {"sha512": "xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==", "type": "package", "path": "system.security.cryptography.pkcs/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.7.0.2.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/7.0.1": {"sha512": "3evI3sBfKqwYSwuBcYgShbmEgtXcg8N5Qu+jExLdkBXPty2yGDXq5m1/4sx9Exb8dqdeMPUs/d9DQ0wy/9Adwg==", "type": "package", "path": "system.security.cryptography.protecteddata/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.7.0.1.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/7.0.1": {"sha512": "MCxBCtH0GrDuvU63ZODwQHQZPchb24pUAX3MfZ6b13qg246ZD10PRdOvay8C9HBPfCXkymUNwFPEegud7ax2zg==", "type": "package", "path": "system.security.cryptography.xml/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.7.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/7.0.0": {"sha512": "Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "type": "package", "path": "system.security.permissions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.7.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Duplex/4.9.0": {"sha512": "Yb8MFiJxBBtm2JnfS/5SxYzm2HqkEmHu5xeaVIHXy83sNpty9wc30JifH2xgda821D6nr1UctbwbdZqN4LBUKQ==", "type": "package", "path": "system.servicemodel.duplex/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Duplex.dll", "lib/net461/System.ServiceModel.Duplex.pdb", "lib/net6.0/System.ServiceModel.Duplex.dll", "lib/net6.0/System.ServiceModel.Duplex.pdb", "lib/netcore50/System.ServiceModel.Duplex.dll", "lib/netstandard1.3/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Duplex.dll", "ref/net6.0/System.ServiceModel.Duplex.dll", "ref/netcore50/System.ServiceModel.Duplex.dll", "ref/netstandard1.1/System.ServiceModel.Duplex.dll", "ref/netstandard2.0/System.ServiceModel.Duplex.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.duplex.4.9.0.nupkg.sha512", "system.servicemodel.duplex.nuspec"]}, "System.ServiceModel.Http/4.10.2": {"sha512": "1AhiJwPc+90GjBd/sDkT93RVuRj688ZQInXzWfd56AEjDzWieUcAUh9ppXhRuEVpT+x48D5GBYJc1VxDP4IT+Q==", "type": "package", "path": "system.servicemodel.http/4.10.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.pdb", "lib/net6.0/System.ServiceModel.Http.dll", "lib/net6.0/System.ServiceModel.Http.pdb", "lib/netcore50/System.ServiceModel.Http.dll", "lib/netstandard1.3/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Http.dll", "ref/net461/System.ServiceModel.Http.dll", "ref/net6.0/System.ServiceModel.Http.dll", "ref/netcore50/System.ServiceModel.Http.dll", "ref/netstandard1.0/System.ServiceModel.Http.dll", "ref/netstandard1.1/System.ServiceModel.Http.dll", "ref/netstandard1.3/System.ServiceModel.Http.dll", "ref/netstandard2.0/System.ServiceModel.Http.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.http.4.10.2.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetTcp/4.9.0": {"sha512": "nXgnnkrZERUF/KwmoLwZPkc7fqgiq94DXkmUZBvDNh/LdZquDvjy2NbhJLElpApOa5x8zEoQoBZyJ2PqNC39qg==", "type": "package", "path": "system.servicemodel.nettcp/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.pdb", "lib/net6.0/System.ServiceModel.NetTcp.dll", "lib/net6.0/System.ServiceModel.NetTcp.pdb", "lib/netcore50/System.ServiceModel.NetTcp.dll", "lib/netstandard1.3/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.NetTcp.dll", "ref/net461/System.ServiceModel.NetTcp.dll", "ref/net6.0/System.ServiceModel.NetTcp.dll", "ref/netcore50/System.ServiceModel.NetTcp.dll", "ref/netstandard1.1/System.ServiceModel.NetTcp.dll", "ref/netstandard1.3/System.ServiceModel.NetTcp.dll", "ref/netstandard2.0/System.ServiceModel.NetTcp.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.nettcp.4.9.0.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/4.10.2": {"sha512": "8QOguIqHtWYywBt7SubPhdICE2LClHzqOMDy0LQIui4T3QJOae7g6UR+alCW61nEufYNtO8Uss41EbXqD8hdww==", "type": "package", "path": "system.servicemodel.primitives/4.10.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.Primitives.dll", "lib/net6.0/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.dll", "lib/netcore50/System.ServiceModel.Primitives.dll", "lib/netstandard1.3/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.pdb", "lib/netstandard2.0/System.ServiceModel.dll", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Primitives.dll", "ref/net461/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.dll", "ref/netcore50/System.ServiceModel.Primitives.dll", "ref/netstandard1.0/System.ServiceModel.Primitives.dll", "ref/netstandard1.1/System.ServiceModel.Primitives.dll", "ref/netstandard1.3/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.primitives.4.10.2.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.ServiceModel.Security/4.9.0": {"sha512": "iurpbSmPgotHps94VQ6acvL6hU2gjiuBmQI7PwLLN76jsbSpUcahT0PglccKIAwoMujATk/LWtAapBHpwCFn2g==", "type": "package", "path": "system.servicemodel.security/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Security.dll", "lib/net461/System.ServiceModel.Security.pdb", "lib/net6.0/System.ServiceModel.Security.dll", "lib/net6.0/System.ServiceModel.Security.pdb", "lib/netcore50/System.ServiceModel.Security.dll", "lib/netstandard1.3/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Security.dll", "ref/net6.0/System.ServiceModel.Security.dll", "ref/netcore50/System.ServiceModel.Security.dll", "ref/netstandard1.0/System.ServiceModel.Security.dll", "ref/netstandard1.1/System.ServiceModel.Security.dll", "ref/netstandard2.0/System.ServiceModel.Security.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.security.4.9.0.nupkg.sha512", "system.servicemodel.security.nuspec"]}, "System.ServiceModel.Syndication/7.0.0": {"sha512": "V3q1Jr3KWo+i201/vUUPfg83rjJLhL5+ROh16PtPhaUJRHwoEBoGWtg0r6pFBRPaDqNY6hXvNgHktDj0gvMEpA==", "type": "package", "path": "system.servicemodel.syndication/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceModel.Syndication.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceModel.Syndication.targets", "lib/net462/System.ServiceModel.Syndication.dll", "lib/net462/System.ServiceModel.Syndication.xml", "lib/net6.0/System.ServiceModel.Syndication.dll", "lib/net6.0/System.ServiceModel.Syndication.xml", "lib/net7.0/System.ServiceModel.Syndication.dll", "lib/net7.0/System.ServiceModel.Syndication.xml", "lib/netstandard2.0/System.ServiceModel.Syndication.dll", "lib/netstandard2.0/System.ServiceModel.Syndication.xml", "system.servicemodel.syndication.7.0.0.nupkg.sha512", "system.servicemodel.syndication.nuspec", "useSharedDesignerContext.txt"]}, "System.ServiceProcess.ServiceController/7.0.1": {"sha512": "rPfXTJzYU46AmWYXRATQzQQ01hICrkl3GuUHgpAr9mnUwAVSsga5x3mBxanFPlJBV9ilzqMXbQyDLJQAbyTnSw==", "type": "package", "path": "system.serviceprocess.servicecontroller/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceProcess.ServiceController.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceProcess.ServiceController.targets", "lib/net462/System.ServiceProcess.ServiceController.dll", "lib/net462/System.ServiceProcess.ServiceController.xml", "lib/net6.0/System.ServiceProcess.ServiceController.dll", "lib/net6.0/System.ServiceProcess.ServiceController.xml", "lib/net7.0/System.ServiceProcess.ServiceController.dll", "lib/net7.0/System.ServiceProcess.ServiceController.xml", "lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.xml", "system.serviceprocess.servicecontroller.7.0.1.nupkg.sha512", "system.serviceprocess.servicecontroller.nuspec", "useSharedDesignerContext.txt"]}, "System.Speech/7.0.0": {"sha512": "7E0uB92Cx2sXR67HW9rMKJqDACdLuz9t3I3OwZUFDzAgwKXWuY6CYeRT/NiypHcyZO2be9+0H0w0M6fn7HQtgQ==", "type": "package", "path": "system.speech/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Speech.targets", "lib/net462/_._", "lib/net6.0/System.Speech.dll", "lib/net6.0/System.Speech.xml", "lib/net7.0/System.Speech.dll", "lib/net7.0/System.Speech.xml", "lib/netstandard2.0/System.Speech.dll", "lib/netstandard2.0/System.Speech.xml", "runtimes/win/lib/net6.0/System.Speech.dll", "runtimes/win/lib/net6.0/System.Speech.xml", "runtimes/win/lib/net7.0/System.Speech.dll", "runtimes/win/lib/net7.0/System.Speech.xml", "system.speech.7.0.0.nupkg.sha512", "system.speech.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/7.0.0": {"sha512": "LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "type": "package", "path": "system.text.encoding.codepages/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/net7.0/System.Text.Encoding.CodePages.dll", "lib/net7.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.7.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/4.7.2": {"sha512": "iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "type": "package", "path": "system.text.encodings.web/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.7.2.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.AccessControl/7.0.1": {"sha512": "uh6LWSk8Dlp1cavk4XQYtDHOMZpSa5KiqM0VBiflhXWGT63RGV+NhNsVxiEykL4S/0LVcgy+/AxC5ITQ9QLo8w==", "type": "package", "path": "system.threading.accesscontrol/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.AccessControl.targets", "lib/net462/System.Threading.AccessControl.dll", "lib/net462/System.Threading.AccessControl.xml", "lib/net6.0/System.Threading.AccessControl.dll", "lib/net6.0/System.Threading.AccessControl.xml", "lib/net7.0/System.Threading.AccessControl.dll", "lib/net7.0/System.Threading.AccessControl.xml", "lib/netstandard2.0/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net6.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net6.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net7.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net7.0/System.Threading.AccessControl.xml", "system.threading.accesscontrol.7.0.1.nupkg.sha512", "system.threading.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Web.Services.Description/4.9.0": {"sha512": "d20B3upsWddwSG5xF3eQLs0cAV3tXDsBNqP4kh02ylfgZwqfpf4f/9KiZVIGIoxULt2cKqxWs+U4AdNAJ7L8cQ==", "type": "package", "path": "system.web.services.description/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Web.Services.Description.dll", "lib/net461/System.Web.Services.Description.pdb", "lib/netstandard2.0/System.Web.Services.Description.dll", "lib/netstandard2.0/System.Web.Services.Description.pdb", "lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll", "system.web.services.description.4.9.0.nupkg.sha512", "system.web.services.description.nuspec"]}, "System.Windows.Extensions/7.0.0": {"sha512": "bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "type": "package", "path": "system.windows.extensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "system.windows.extensions.7.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "AIC.SharedData/2023.1.2.57": {"type": "project", "path": "../AIC.SharedData/AIC.SharedData.vbproj", "msbuildProject": "../AIC.SharedData/AIC.SharedData.vbproj"}, "EnchartDOLib/2023.1.0.2": {"type": "project", "path": "../EnchartDOLib/EnchartDOLib.vbproj", "msbuildProject": "../EnchartDOLib/EnchartDOLib.vbproj"}, "EnchartServer.Data/2023.1.2.57": {"type": "project", "path": "../../Server/EnchartServer/src/EnchartServer.Data/EnchartServer.Data.vbproj", "msbuildProject": "../../Server/EnchartServer/src/EnchartServer.Data/EnchartServer.Data.vbproj"}, "ICCryptoHelper/2023.1.2.57": {"type": "project", "path": "../../Server/EnchartServer/ICCryptoHelper/AICCryptoHelper.vbproj", "msbuildProject": "../../Server/EnchartServer/ICCryptoHelper/AICCryptoHelper.vbproj"}, "McKessonIntelligentCoding.Data/2023.1.2.57": {"type": "project", "path": "../../McKessonIntelligentCoding/McKessonIntelligentCoding.Data/McKessonIntelligentCoding.Data.vbproj", "msbuildProject": "../../McKessonIntelligentCoding/McKessonIntelligentCoding.Data/McKessonIntelligentCoding.Data.vbproj"}, "MICCustomConnectionProviders/2023.1.3.2": {"type": "project", "path": "../MICCustomDataProviders/MICCustomConnectionProviders.csproj", "msbuildProject": "../MICCustomDataProviders/MICCustomConnectionProviders.csproj"}, "Timeout/2023.1.2.57": {"type": "project", "path": "../../Timeout/Timeout/Timeout.vbproj", "msbuildProject": "../../Timeout/Timeout/Timeout.vbproj"}}, "projectFileDependencyGroups": {"net7.0-windows7.0": ["DevExpress.Win.Design >= 23.1.5", "DevExpress.Xpo >= 23.1.5", "EnchartDOLib >= 2023.1.0.2", "MICCustomConnectionProviders >= 2023.1.3.2", "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers >= 0.4.410601", "System.Formats.Asn1 >= 8.0.1", "Timeout >= 2023.1.2.57"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages": {}, "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\": {}}, "project": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\CdmReportsViewer2\\CDMReportViewer2.vbproj", "projectName": "CDMViewer", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\CdmReportsViewer2\\CDMReportViewer2.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\CdmReportsViewer2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj"}, "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj"}, "C:\\AIC\\Main_Next\\SourceCode\\Timeout\\Timeout\\Timeout.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\Timeout\\Timeout\\Timeout.vbproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Win.Design": {"target": "Package", "version": "[23.1.5, )"}, "DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}
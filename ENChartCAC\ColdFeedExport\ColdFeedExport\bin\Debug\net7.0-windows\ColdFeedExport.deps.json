{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"ColdFeedExport/2023.1.2.57": {"dependencies": {"DevExpress.Win.Design": "23.1.5", "DevExpress.Xpo": "23.1.5", "EnchartDOLib": "2023.1.0.2", "McKesson.HIC.ChargeSummaryColdFeedHelper": "2023.1.2.57", "McKesson.HIC.HPFColdFeed": "2023.1.2.57", "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": "0.4.410601", "Microsoft.VisualBasic": "10.4.0-preview.18571.3", "System.Formats.Asn1": "8.0.1", "ICCryptoHelper.Reference": "2023.1.2.57"}, "runtime": {"ColdFeedExport.dll": {}}}, "DevExpress.Charts/23.1.5": {"dependencies": {"DevExpress.Charts.Core": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.DataVisualization.Core": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.XtraCharts.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Charts.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard2.0/DevExpress.Charts.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.CodeParser/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3", "System.CodeDom": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.CodeParser.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Data/23.1.5": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NETStandard.Library": "2.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Drawing.Common": "7.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Data.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Data.Desktop/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.Data.Desktop.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.DataAccess/23.1.5": {"dependencies": {"DevExpress.CodeParser": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Xpo": "23.1.5", "NETStandard.Library": "2.0.3", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.SqlClient": "4.8.6", "System.Drawing.Common": "7.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/DevExpress.DataAccess.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.DataAccess.UI/23.1.5": {"dependencies": {"DevExpress.CodeParser": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.Diagram.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Utils.UI": "23.1.5", "DevExpress.Win.Diagram": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.RichEdit": "23.1.5", "DevExpress.Win.TreeList": "23.1.5", "DevExpress.Xpo": "23.1.5", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net6.0-windows/DevExpress.DataAccess.v23.1.UI.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.DataVisualization.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard2.0/DevExpress.DataVisualization.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Diagram.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.Diagram.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Drawing/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Drawing.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Gauges.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.XtraGauges.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Images/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Images.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Map.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Map.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Mvvm/23.1.5": {"runtime": {"lib/net6.0-windows/DevExpress.Mvvm.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Office.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0", "System.Security.Cryptography.Xml": "7.0.1"}, "runtime": {"lib/netstandard2.0/DevExpress.Office.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Pdf.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2"}, "runtime": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Pdf.Drawing/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Pdf.v23.1.Drawing.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.PivotGrid.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/DevExpress.PivotGrid.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Printing.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Pdf.Drawing": "23.1.5", "NETStandard.Library": "2.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Drawing.Common": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.ServiceModel.Http": "4.10.2", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Printing.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Reporting.Core/23.1.5": {"dependencies": {"DevExpress.Charts": "23.1.5", "DevExpress.Charts.Core": "23.1.5", "DevExpress.CodeParser": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Gauges.Core": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Pdf.Drawing": "23.1.5", "DevExpress.PivotGrid.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.RichEdit.Export": "23.1.5", "DevExpress.Sparkline.Core": "23.1.5", "DevExpress.Xpo": "23.1.5", "NETStandard.Library": "2.0.3", "System.CodeDom": "7.0.0", "System.Collections.Immutable": "6.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Drawing.Common": "7.0.0", "System.Runtime.Loader": "4.3.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.XtraReports.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.RichEdit.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.RichEdit.Export/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.RichEdit.v23.1.Export.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Scheduler.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.XtraScheduler.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Scheduler.CoreDesktop/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Images": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Scheduler.Core": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Core.Desktop.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Sparkline.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Sparkline.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.SpellChecker.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard2.0/DevExpress.SpellChecker.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Spreadsheet.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.DataVisualization.Core": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Sparkline.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0", "System.Security.AccessControl": "6.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.Spreadsheet.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.TreeMap/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.DataVisualization.Core": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.TreeMap.Core": "23.1.5", "NETStandard.Library": "2.0.3", "System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/DevExpress.XtraTreeMap.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.TreeMap.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard2.0/DevExpress.TreeMap.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Utils/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.Utils.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Utils.UI/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.RichEdit": "23.1.5", "DevExpress.Win.TreeList": "23.1.5", "DevExpress.Win.VerticalGrid": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.Utils.v23.1.UI.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.RichEdit.Export": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.PivotGrid": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.TreeList": "23.1.5", "DevExpress.Win.VerticalGrid": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraNavBar.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraWizard.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Charts/23.1.5": {"dependencies": {"DevExpress.Charts": "23.1.5", "DevExpress.Charts.Core": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.DataAccess.UI": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Utils.UI": "23.1.5", "DevExpress.Win": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.TreeList": "23.1.5", "DevExpress.Win.VerticalGrid": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Extensions.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.UI.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraCharts.v23.1.Wizard.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Design/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Mvvm": "23.1.5", "DevExpress.Win": "23.1.5", "DevExpress.Win.Charts": "23.1.5", "DevExpress.Win.Diagram": "23.1.5", "DevExpress.Win.Dialogs": "23.1.5", "DevExpress.Win.Gantt": "23.1.5", "DevExpress.Win.Gauges": "23.1.5", "DevExpress.Win.Map": "23.1.5", "DevExpress.Win.PdfViewer": "23.1.5", "DevExpress.Win.Reporting": "23.1.5", "DevExpress.Win.RichEdit": "23.1.5", "DevExpress.Win.SchedulerExtensions": "23.1.5", "DevExpress.Win.SpellChecker": "23.1.5", "DevExpress.Win.Spreadsheet": "23.1.5", "DevExpress.Win.TreeMap": "23.1.5", "DevExpress.Xpo": "23.1.5", "Microsoft.Win32.Registry": "4.7.0", "System.ComponentModel.Annotations": "5.0.0", "System.ComponentModel.Composition": "7.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.Odbc": "7.0.0", "System.Data.OleDb": "7.0.0", "System.Data.SqlClient": "4.8.6", "System.Drawing.Common": "7.0.0", "System.IO.Ports": "7.0.0", "System.ServiceModel.Http": "4.10.2", "System.ServiceModel.NetTcp": "4.9.0", "System.ServiceModel.Security": "4.9.0", "System.ServiceModel.Syndication": "7.0.0", "System.ServiceProcess.ServiceController": "7.0.1", "System.Text.Encoding.CodePages": "7.0.0"}}, "DevExpress.Win.Diagram/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Diagram.Core": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.VerticalGrid": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraDiagram.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Dialogs/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Dialogs.Core": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraDialogs.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Dialogs.Core/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.Dialogs.v23.1.Core.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Gantt/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGantt.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Gauges/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Gauges.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Presets.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraGauges.v23.1.Win.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Grid/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraGrid.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Map/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Map.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraMap.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Navigation/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Sparkline.Core": "23.1.5", "DevExpress.Utils": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraBars.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraEditors.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraLayout.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.PdfViewer/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Pdf.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPdfViewer.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.PivotGrid/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.PivotGrid.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPivotGrid.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Printing/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraPrinting.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Reporting/23.1.5": {"dependencies": {"DevExpress.Charts": "23.1.5", "DevExpress.Charts.Core": "23.1.5", "DevExpress.CodeParser": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.DataAccess.UI": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Gauges.Core": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.PivotGrid.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Reporting.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Sparkline.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Utils.UI": "23.1.5", "DevExpress.Win": "23.1.5", "DevExpress.Win.Charts": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.PivotGrid": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.RichEdit": "23.1.5", "DevExpress.Win.TreeList": "23.1.5", "DevExpress.Win.VerticalGrid": "23.1.5", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraReports.v23.1.Extensions.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.RichEdit/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Images": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Pdf.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraRichEdit.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Scheduler/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Scheduler.Core": "23.1.5", "DevExpress.Scheduler.CoreDesktop": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.SchedulerExtensions/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Reporting.Core": "23.1.5", "DevExpress.Scheduler.Core": "23.1.5", "DevExpress.Scheduler.CoreDesktop": "23.1.5", "DevExpress.SpellChecker.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.Reporting": "23.1.5", "DevExpress.Win.Scheduler": "23.1.5", "DevExpress.Win.SchedulerReporting": "23.1.5", "DevExpress.Win.SpellChecker": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Extensions.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}, "lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.Extensions.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.SchedulerReporting/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Reporting.Core": "23.1.5", "DevExpress.Scheduler.Core": "23.1.5", "DevExpress.Scheduler.CoreDesktop": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.Scheduler": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraScheduler.v23.1.Reporting.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.SpellChecker/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.SpellChecker.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraSpellChecker.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.Spreadsheet/23.1.5": {"dependencies": {"DevExpress.Charts": "23.1.5", "DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.DataAccess": "23.1.5", "DevExpress.DataAccess.UI": "23.1.5", "DevExpress.DataVisualization.Core": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Office.Core": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.RichEdit.Core": "23.1.5", "DevExpress.Spreadsheet.Core": "23.1.5", "DevExpress.TreeMap": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Utils.UI": "23.1.5", "DevExpress.Win": "23.1.5", "DevExpress.Win.Grid": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5", "DevExpress.Win.RichEdit": "23.1.5", "DevExpress.Win.TreeList": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraSpreadsheet.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.TreeList/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraTreeList.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.TreeMap/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.TreeMap": "23.1.5", "DevExpress.TreeMap.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraTreeMap.v23.1.UI.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Win.VerticalGrid/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "DevExpress.Data.Desktop": "23.1.5", "DevExpress.Drawing": "23.1.5", "DevExpress.Printing.Core": "23.1.5", "DevExpress.Utils": "23.1.5", "DevExpress.Win.Navigation": "23.1.5", "DevExpress.Win.Printing": "23.1.5"}, "runtime": {"lib/net6.0-windows/DevExpress.XtraVerticalGrid.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "DevExpress.Xpo/23.1.5": {"dependencies": {"DevExpress.Data": "23.1.5", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Win32.Registry": "4.7.0", "NETStandard.Library": "2.0.3", "System.Data.SqlClient": "4.8.6", "System.Drawing.Common": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.ServiceModel.Http": "4.10.2", "System.ServiceModel.NetTcp": "4.9.0", "System.ServiceModel.Security": "4.9.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/DevExpress.Xpo.v23.1.dll": {"assemblyVersion": "23.1.5.0", "fileVersion": "23.1.5.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Mapster/7.3.0": {"dependencies": {"Mapster.Core": "1.2.0", "Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/Mapster.dll": {"assemblyVersion": "7.3.0.0", "fileVersion": "7.3.0.0"}}}, "Mapster.Core/1.2.0": {"runtime": {"lib/netstandard2.0/Mapster.Core.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis/4.4.0": {"dependencies": {"Microsoft.CodeAnalysis.CSharp.Workspaces": "4.4.0", "Microsoft.CodeAnalysis.VisualBasic.Workspaces": "4.4.0"}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.4.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Memory": "4.5.5", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.4.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.4.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.4.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.4.0", "Microsoft.CodeAnalysis.Common": "4.4.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.4.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.VisualBasic/4.4.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.4.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.VisualBasic.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.VisualBasic.Workspaces/4.4.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.4.0", "Microsoft.CodeAnalysis.VisualBasic": "4.4.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.4.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.4.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.4.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.400.22.56111"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {}, "Microsoft.Extensions.Configuration/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.DependencyModel/3.0.0": {"dependencies": {"System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {}, "Microsoft.Extensions.ObjectPool/5.0.10": {}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.VisualBasic/10.4.0-preview.18571.3": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {}, "Microsoft.Win32.SystemEvents/7.0.0": {}, "Microsoft.Windows.Compatibility/7.0.3": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "7.0.0", "Microsoft.Win32.SystemEvents": "7.0.0", "System.CodeDom": "7.0.0", "System.ComponentModel.Composition": "7.0.0", "System.ComponentModel.Composition.Registration": "7.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.Odbc": "7.0.0", "System.Data.OleDb": "7.0.0", "System.Data.SqlClient": "4.8.6", "System.Diagnostics.EventLog": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0", "System.DirectoryServices": "7.0.1", "System.DirectoryServices.AccountManagement": "7.0.0", "System.DirectoryServices.Protocols": "7.0.1", "System.Drawing.Common": "7.0.0", "System.IO.Packaging": "7.0.0", "System.IO.Ports": "7.0.0", "System.Management": "7.0.2", "System.Reflection.Context": "7.0.0", "System.Runtime.Caching": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.ProtectedData": "7.0.1", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Permissions": "7.0.0", "System.ServiceModel.Duplex": "4.9.0", "System.ServiceModel.Http": "4.10.2", "System.ServiceModel.NetTcp": "4.9.0", "System.ServiceModel.Primitives": "4.10.2", "System.ServiceModel.Security": "4.9.0", "System.ServiceModel.Syndication": "7.0.0", "System.ServiceProcess.ServiceController": "7.0.1", "System.Speech": "7.0.0", "System.Text.Encoding.CodePages": "7.0.0", "System.Threading.AccessControl": "7.0.1", "System.Web.Services.Description": "4.9.0"}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/7.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "System.CodeDom/7.0.0": {}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Composition/7.0.0": {"runtime": {"lib/net7.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.ComponentModel.Composition.Registration/7.0.0": {"dependencies": {"System.ComponentModel.Composition": "7.0.0", "System.Reflection.Context": "7.0.0"}, "runtime": {"lib/net7.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.1", "System.Security.Permissions": "7.0.0"}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Data.Odbc/7.0.0": {"dependencies": {"System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net7.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll": {"rid": "illumos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/ios/lib/net7.0/System.Data.Odbc.dll": {"rid": "ios", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/linux/lib/net7.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/osx/lib/net7.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll": {"rid": "solaris", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll": {"rid": "tvos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/win/lib/net7.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Data.OleDb/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0"}, "runtime": {"lib/net7.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.EventLog/7.0.0": {}, "System.Diagnostics.PerformanceCounter/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}}, "System.DirectoryServices/7.0.1": {"dependencies": {"System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "System.DirectoryServices.AccountManagement/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.DirectoryServices": "7.0.1", "System.DirectoryServices.Protocols": "7.0.1"}, "runtime": {"lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.DirectoryServices.Protocols/7.0.1": {"runtime": {"lib/net7.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}, "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}, "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}}, "System.Formats.Asn1/8.0.1": {"runtime": {"lib/net7.0/System.Formats.Asn1.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.724.31311"}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Packaging/7.0.0": {}, "System.IO.Pipelines/6.0.3": {}, "System.IO.Ports/7.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "runtime": {"lib/net7.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/unix/lib/net7.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}, "runtimes/win/lib/net7.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net7.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Memory/4.5.5": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.223.17202"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Context/7.0.0": {"runtime": {"lib/net7.0/System.Reflection.Context.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "7.0.22.51805"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net7.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Pkcs/7.0.2": {"dependencies": {"System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Security.Cryptography.ProtectedData/7.0.1": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "System.Security.Cryptography.Xml/7.0.1": {"dependencies": {"System.Security.Cryptography.Pkcs": "7.0.2"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.222.60605"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.9.0": {"dependencies": {"System.Private.ServiceModel": "4.10.2", "System.ServiceModel.Primitives": "4.10.2"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.21.52002"}}}, "System.ServiceModel.Http/4.10.2": {"dependencies": {"System.Private.ServiceModel": "4.10.2", "System.ServiceModel.Primitives": "4.10.2"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.223.17202"}}}, "System.ServiceModel.NetTcp/4.9.0": {"dependencies": {"System.Private.ServiceModel": "4.10.2", "System.ServiceModel.Primitives": "4.10.2"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.21.52002"}}}, "System.ServiceModel.Primitives/4.10.2": {"dependencies": {"System.Private.ServiceModel": "4.10.2"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.223.17202"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.1000.223.17202"}}}, "System.ServiceModel.Security/4.9.0": {"dependencies": {"System.Private.ServiceModel": "4.10.2", "System.ServiceModel.Primitives": "4.10.2"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.21.52002"}}}, "System.ServiceModel.Syndication/7.0.0": {"runtime": {"lib/net7.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.ServiceProcess.ServiceController/7.0.1": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0"}, "runtime": {"lib/net7.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.1", "fileVersion": "7.0.723.27404"}}}, "System.Speech/7.0.0": {"runtime": {"lib/net7.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/7.0.0": {}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading.AccessControl/7.0.1": {"runtime": {"lib/net7.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.222.60605"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.222.60605"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.5.0": {}, "System.Web.Services.Description/4.9.0": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.21.52002"}}, "resources": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}}, "AIC.SharedData/2023.1.2.57": {"dependencies": {"Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "runtime": {"AIC.SharedData.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "CodingReport/2023.1.2.103": {"dependencies": {"DevExpress.Reporting.Core": "23.1.5", "DevExpress.Win.Reporting": "23.1.5", "EnchartDOLib": "2023.1.0.2", "Microsoft.CodeAnalysis": "4.4.0", "System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1"}, "runtime": {"CodingReport.dll": {"assemblyVersion": "2023.1.2.103", "fileVersion": "2023.1.2.103"}}}, "EnchartDOLib/2023.1.0.2": {"dependencies": {"AIC.SharedData": "2023.1.2.57", "DevExpress.Xpo": "23.1.5", "EnchartServer.Data": "2023.1.2.57", "MICCustomConnectionProviders": "2023.1.3.2", "Mapster": "7.3.0", "McKessonIntelligentCoding.Data": "2023.1.2.57", "Microsoft.VisualBasic": "10.4.0-preview.18571.3", "Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "runtime": {"EnchartDOLib.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "EnchartServer.Data/2023.1.2.57": {"dependencies": {"AIC.SharedData": "2023.1.2.57", "DevExpress.Xpo": "23.1.5", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Newtonsoft.Json": "13.0.3", "Serilog.AspNetCore": "6.1.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1"}, "runtime": {"EnchartServer.Data.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "ICCryptoHelper/2023.1.2.57": {"dependencies": {"DevExpress.Xpo": "23.1.5", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Windows.Compatibility": "7.0.3", "Newtonsoft.Json": "13.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1", "System.IdentityModel.Tokens.Jwt": "6.35.0", "System.ValueTuple": "4.5.0"}, "runtime": {"ICCryptoHelper.dll": {"fileVersion": "0.0.0.0"}}}, "McKesson.HIC.ChargeSummaryColdFeedHelper/2023.1.2.57": {"dependencies": {"EnchartDOLib": "2023.1.0.2", "Microsoft.Windows.Compatibility": "7.0.3", "System.Formats.Asn1": "8.0.1"}, "runtime": {"McKesson.HIC.ChargeSummaryColdFeedHelper.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "McKesson.HIC.HPFColdFeed/2023.1.2.57": {"dependencies": {"CodingReport": "2023.1.2.103", "McKesson.HIC.ChargeSummaryColdFeedHelper": "2023.1.2.57", "System.Configuration.ConfigurationManager": "7.0.0", "System.Formats.Asn1": "8.0.1"}, "runtime": {"McKesson.HIC.HPFColdFeed.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "McKessonIntelligentCoding.Data/2023.1.2.57": {"dependencies": {"System.Data.DataSetExtensions": "4.5.0", "System.Formats.Asn1": "8.0.1", "System.ValueTuple": "4.5.0"}, "runtime": {"McKessonIntelligentCoding.Data.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "MICCustomConnectionProviders/2023.1.3.2": {"dependencies": {"DevExpress.Xpo": "23.1.5", "ICCryptoHelper": "2023.1.2.57", "Newtonsoft.Json": "13.0.3", "System.Data.SqlClient": "4.8.6", "System.Formats.Asn1": "8.0.1"}, "runtime": {"MICCustomConnectionProviders.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}, "ICCryptoHelper.Reference/2023.1.2.57": {"runtime": {"ICCryptoHelper.dll": {"assemblyVersion": "2023.1.2.57", "fileVersion": "2023.1.2.57"}}}}}, "libraries": {"ColdFeedExport/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "DevExpress.Charts/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-7J+gtEuT2LDkRf4BaXxP6E6Ue4SaTzXx+jnppXFwE8TNdFFiRUS4H7bzA3MYT0vM/1QiJ6Kkt1ux3AH4mCblnQ==", "path": "devexpress.charts/23.1.5", "hashPath": "devexpress.charts.23.1.5.nupkg.sha512"}, "DevExpress.Charts.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-c2zXEMaStd+dWHDxDNzRT6SOPdx1p3k16n9XiokqpTW19DRXvcl0LaRetFKMQD/t2N+mETTsiGIniiDIkkbMsA==", "path": "devexpress.charts.core/23.1.5", "hashPath": "devexpress.charts.core.23.1.5.nupkg.sha512"}, "DevExpress.CodeParser/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-EXiWeuyzF35CAAW4H+kOfnTsvX+fmwf4UkXEZlx10NDbUOGNcwKx5QVL3txkP7oXjB43ExXZLPQ4lxbFS27AaQ==", "path": "devexpress.codeparser/23.1.5", "hashPath": "devexpress.codeparser.23.1.5.nupkg.sha512"}, "DevExpress.Data/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-G/RZYe1OCBQIdVSw1ZODe7irbtYi3RHmKgUMM0JojEn/Tj6c//hfQZpImB5MgCKqNmfkQzuoID8PXVDyh6O7Cw==", "path": "devexpress.data/23.1.5", "hashPath": "devexpress.data.23.1.5.nupkg.sha512"}, "DevExpress.Data.Desktop/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-JVvdPXVWkcfhw/Ni8/zmGZrK7rlBffVQ0N+WlXhfHLWJ6pewyJaI/UzHyBFCrDBZLRu4fX6jpzNoHQOR/ZnzXg==", "path": "devexpress.data.desktop/23.1.5", "hashPath": "devexpress.data.desktop.23.1.5.nupkg.sha512"}, "DevExpress.DataAccess/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXeLDxyXDX81pLtuPzgCsBJUg376bIjjqAyuaeabMznadlhUb/xJ0+wXwp4aAKcc6RF8iZknMT3gJKZqo1ERA==", "path": "devexpress.dataaccess/23.1.5", "hashPath": "devexpress.dataaccess.23.1.5.nupkg.sha512"}, "DevExpress.DataAccess.UI/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-/+BfJ5kQOFEWeIK0HtjqYMJgYIg5BQ/dnK77rnDjvt1HXuqY6QqhKMfFgJxwOMEHIspwpJI8fra3Lr8MZWhV1A==", "path": "devexpress.dataaccess.ui/23.1.5", "hashPath": "devexpress.dataaccess.ui.23.1.5.nupkg.sha512"}, "DevExpress.DataVisualization.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-PUhVBPPy8AEfB+IXD2urxhgroXK17AknFoYwY4z85QLlUgg9R7lCZYOI4UBs3RX2ddUQyJ7aU3nJ2fHZQ8rHkw==", "path": "devexpress.datavisualization.core/23.1.5", "hashPath": "devexpress.datavisualization.core.23.1.5.nupkg.sha512"}, "DevExpress.Diagram.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-SQ0pCtN+SM6hCgKUrJon/ovoURLtljvXd3clEOg+P2IxweiajQxEyRxZ1Y/+itXOiVWMp+kpL+KafF9fSbN2FA==", "path": "devexpress.diagram.core/23.1.5", "hashPath": "devexpress.diagram.core.23.1.5.nupkg.sha512"}, "DevExpress.Drawing/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-AcAVX8uAkqlgjGwBe49c5KeYgst1D/xf1lDdM3kizhUgi9kF/ahABHbUI5FsPKhwMEHI5UvBoLsskn+WcO0irA==", "path": "devexpress.drawing/23.1.5", "hashPath": "devexpress.drawing.23.1.5.nupkg.sha512"}, "DevExpress.Gauges.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-6v0r4UwQ1QNecV7HvW9V+Ke7PTZaGG2dPCXS8/NoQtjA3UDwcmGp7fgFb8/geC9Pne645zhPkJIvrKHvYRLBPQ==", "path": "devexpress.gauges.core/23.1.5", "hashPath": "devexpress.gauges.core.23.1.5.nupkg.sha512"}, "DevExpress.Images/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-0o6dVPA42thJUmmtcxBNfKK68B+utbx2KCW0UfQA/68HWsn0T2vcnd2/tVnRtQ3siFi4Q6v9SurIHqME164fhg==", "path": "devexpress.images/23.1.5", "hashPath": "devexpress.images.23.1.5.nupkg.sha512"}, "DevExpress.Map.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-foBuO4PhsllHKRMbZlQKNfiapbOV7wTsLxGvTLoK7CXPkGnjT0mTl8lDfl8bM3JP8lNLu2VGZJuFomZEK9m9+w==", "path": "devexpress.map.core/23.1.5", "hashPath": "devexpress.map.core.23.1.5.nupkg.sha512"}, "DevExpress.Mvvm/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-4nq3bkRkVRVj1Ym9Ft5UcUtCMRlgHcS0JDQ52UOiJ+JXvvudGN6mXJUtDlrwV0xZPoctJ5iC2HjOMDpVyy+1+Q==", "path": "devexpress.mvvm/23.1.5", "hashPath": "devexpress.mvvm.23.1.5.nupkg.sha512"}, "DevExpress.Office.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-Km4+e9LVesj03yXEbEwDY3mTdPO+N17di9+J/f2kDMlh5V65ZCMtU1MplvULe6s0pdJwYJJvCsth6fSnyjFKLw==", "path": "devexpress.office.core/23.1.5", "hashPath": "devexpress.office.core.23.1.5.nupkg.sha512"}, "DevExpress.Pdf.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-aoBsFJhLy+T8Yg9bhv+aDFEUnU7FIDSrZO/HkgrhkZao35bhHxFCNvqM502cCPyu5vHp3Hbw1ZR1kN3TUWzbFw==", "path": "devexpress.pdf.core/23.1.5", "hashPath": "devexpress.pdf.core.23.1.5.nupkg.sha512"}, "DevExpress.Pdf.Drawing/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-gjHvRDUSk+Rm3Rhn74XeGyU3EezdCdxfP7ckjT1UiXIj60obqevHX2oAEXpGryO9PGGKpfPnuL1ULnUKgCQfiQ==", "path": "devexpress.pdf.drawing/23.1.5", "hashPath": "devexpress.pdf.drawing.23.1.5.nupkg.sha512"}, "DevExpress.PivotGrid.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-3vdPAkJi/GL1/UtYgnwaxBPgAkS1NZtVIlaVPVIcEjcVHdTt+0zc3ccCBP4TTisg/PIZxzx7Yu/Q4aCizNfGVA==", "path": "devexpress.pivotgrid.core/23.1.5", "hashPath": "devexpress.pivotgrid.core.23.1.5.nupkg.sha512"}, "DevExpress.Printing.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-pytJr70/1QqfelRkGOJri0Xq3ye7F1H3SK39ypgAlaqfdFt96JAGA1uFFHlRThtnrbeqCGA9FE8tmaxlRbpW+Q==", "path": "devexpress.printing.core/23.1.5", "hashPath": "devexpress.printing.core.23.1.5.nupkg.sha512"}, "DevExpress.Reporting.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnRJT435bgsYLCO7CbRs+yqrN/EjvCl+rxwOS9GLA/zxpgbFs6YRAGif1wP4zAHjudcnTrC0EL85zaYGmiPj+A==", "path": "devexpress.reporting.core/23.1.5", "hashPath": "devexpress.reporting.core.23.1.5.nupkg.sha512"}, "DevExpress.RichEdit.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-uBnvT0wIRwiyQJVdE3OEe3pWJSllqvwQk1d6Yj35ciLxT+VJke9nJo6pbs2JLzyQhNMYcfvmD6q1T7SU3LcKAw==", "path": "devexpress.richedit.core/23.1.5", "hashPath": "devexpress.richedit.core.23.1.5.nupkg.sha512"}, "DevExpress.RichEdit.Export/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-P/jv1TE/mBenScc1u89ukKH3wp1z0M+5m8A4D7YGxGKJajhZseV6iNd/RJFb1lpYG33E27VXLpNrP0W7PssIZA==", "path": "devexpress.richedit.export/23.1.5", "hashPath": "devexpress.richedit.export.23.1.5.nupkg.sha512"}, "DevExpress.Scheduler.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-jUrcDzR/T4RrIUa/3cff9srP77IJbhfM+a3EsgUT5Ok+UjUhDUOJF3krlF0m62MJ6BW+EzlqEjSk9rIi2FOIrg==", "path": "devexpress.scheduler.core/23.1.5", "hashPath": "devexpress.scheduler.core.23.1.5.nupkg.sha512"}, "DevExpress.Scheduler.CoreDesktop/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-joGzLWvAsaTjaO/zFm7bnqLZaP7qfpg5Xo0Czu7UKummUiytFPc6M4iaZ5c9zqsF25p1eCUvrl60pYlSbyEF2g==", "path": "devexpress.scheduler.coredesktop/23.1.5", "hashPath": "devexpress.scheduler.coredesktop.23.1.5.nupkg.sha512"}, "DevExpress.Sparkline.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-i45VUeRNNfrMUGsQsh8wP51tUpvk/HELQbXKC0E8bSGtOwhUk58a1ViA7zdMeX2gvdQrkEa95c6vUK2fNOnBPQ==", "path": "devexpress.sparkline.core/23.1.5", "hashPath": "devexpress.sparkline.core.23.1.5.nupkg.sha512"}, "DevExpress.SpellChecker.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-biGiyZRjrptvYxIYeC4O8tP23GID9RaodONiWQTab+Z5OioMJzQWOJfQDof5nrCP/ykeSczbMv0KRAA8VXWOEA==", "path": "devexpress.spellchecker.core/23.1.5", "hashPath": "devexpress.spellchecker.core.23.1.5.nupkg.sha512"}, "DevExpress.Spreadsheet.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-2thNpI02m1oHnafubdkKJ1qScL+8jhK4uIbUHeTtckcbowyPXYZkwKIV2JqiSIrt9przpKhU3hNusdaQZDefJg==", "path": "devexpress.spreadsheet.core/23.1.5", "hashPath": "devexpress.spreadsheet.core.23.1.5.nupkg.sha512"}, "DevExpress.TreeMap/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-PZ<PERSON>+kTccTymVk7W2ARxdqwhrH41tL32eN5VqI9w+IH0aLbBxBZOQlBJfBaElJ+uxKRw8QKajwzxc9OH3V72FyA==", "path": "devexpress.treemap/23.1.5", "hashPath": "devexpress.treemap.23.1.5.nupkg.sha512"}, "DevExpress.TreeMap.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-0fBBiOaKwXSAtJLanEl2T86AKa9r0FqQXsmJCxaEpl0Ua+J3j7GEwnecmjAjUSBtd7iv5O0BttcnYSDTBVC4OQ==", "path": "devexpress.treemap.core/23.1.5", "hashPath": "devexpress.treemap.core.23.1.5.nupkg.sha512"}, "DevExpress.Utils/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-/KfCEAeYZOYyUTcJV/qoIQcmmslb4ztmP4M16B1NBrZk8cIz039F+VallWAGeJ/LApHHaaz3mLLcZFdZsAY/pg==", "path": "devexpress.utils/23.1.5", "hashPath": "devexpress.utils.23.1.5.nupkg.sha512"}, "DevExpress.Utils.UI/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-xDX7KnKUgE7AwRyL07cp+zKWQGyGoqHqNhpfE5L1P1n6/juj4xZRKsPo1B1kXYuhxw7BzWrdczbX/uhS0B0jQg==", "path": "devexpress.utils.ui/23.1.5", "hashPath": "devexpress.utils.ui.23.1.5.nupkg.sha512"}, "DevExpress.Win/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-T7uSoGQk7v+kvIV6EGzwoMOH/WYV0benLgqu7dS7M1uboRpdukjVtot03dig/1gSOyEnYajTs2cnoZvPZVQNzA==", "path": "devexpress.win/23.1.5", "hashPath": "devexpress.win.23.1.5.nupkg.sha512"}, "DevExpress.Win.Charts/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-iOh86dnuMtkoiMn6LivvcQebNY4J9KMEVMUkucFrtBLwWpRnWvg+wuQclrhN50GHFV0I6wqkuJeR3cotJwGf4g==", "path": "devexpress.win.charts/23.1.5", "hashPath": "devexpress.win.charts.23.1.5.nupkg.sha512"}, "DevExpress.Win.Design/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-YDB70TG/+eSrPuWTbUMgTOoCz4vyjXro/yrv2+HOl47V5DH21/qWGvEQH7A6SVuJPoSRWvNsz6eXLnhrDfiHSw==", "path": "devexpress.win.design/23.1.5", "hashPath": "devexpress.win.design.23.1.5.nupkg.sha512"}, "DevExpress.Win.Diagram/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-UhdDnxkuxFCi+tXCe/govW9aQYXiAEMiaRJ01Iqos0ClnZ4XVkaP5Yy78ekmJQMDy39h3E4/pLi3ZFHa65Juog==", "path": "devexpress.win.diagram/23.1.5", "hashPath": "devexpress.win.diagram.23.1.5.nupkg.sha512"}, "DevExpress.Win.Dialogs/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ib/eMFC4vshgowy/IayISXIZjGUtQwiENoWIu+mtu+U1ABo8+3x+i2H35Qe127AJC4OVguKdEjZvOpdEFQgdIA==", "path": "devexpress.win.dialogs/23.1.5", "hashPath": "devexpress.win.dialogs.23.1.5.nupkg.sha512"}, "DevExpress.Win.Dialogs.Core/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-BL4tHqTJ65g4+A15Qcc+6SSPKxRKI7pZw24uoPxlJEmhGW86J2MOkKdLs0Yol+XbJ/jW+47LwY2v9UAGbOsi6Q==", "path": "devexpress.win.dialogs.core/23.1.5", "hashPath": "devexpress.win.dialogs.core.23.1.5.nupkg.sha512"}, "DevExpress.Win.Gantt/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-8glB+nFiky/oOUzRk9UTfdlLvsNXaoCCQnHJyC8D/5/vtj6NWE1uOBPxQFfQdWOXnGs0YXvKBKZMVuAZ1u3QmQ==", "path": "devexpress.win.gantt/23.1.5", "hashPath": "devexpress.win.gantt.23.1.5.nupkg.sha512"}, "DevExpress.Win.Gauges/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-RPdsd/7XWiC7P69PfCIYJyQEyZf7SDbBocWJpfza0i/8sJn2ypvQ4urXao+Tl4mW+d4sPdrmKNdgc62+cD1V6w==", "path": "devexpress.win.gauges/23.1.5", "hashPath": "devexpress.win.gauges.23.1.5.nupkg.sha512"}, "DevExpress.Win.Grid/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-zpGObAf5BPq6WIHsDddDQ/KNej7bbWIy+dySAMpt/Hej6md+1aFNmXHpcnUSHc7Yd8T3OCsW90tvodUmBv83XQ==", "path": "devexpress.win.grid/23.1.5", "hashPath": "devexpress.win.grid.23.1.5.nupkg.sha512"}, "DevExpress.Win.Map/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-H8WjxUWQgUuNGOy7EE6a2KhAKbKkhUKfEM3aEnCoy2C2++NgeAFhd+RR7xmtQeN8pU4Dutk9p+KUjeXLBrWGGg==", "path": "devexpress.win.map/23.1.5", "hashPath": "devexpress.win.map.23.1.5.nupkg.sha512"}, "DevExpress.Win.Navigation/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-Rq6M+Jpw5kojFs1IpebB+ru3jBGkTsjOCv7MFwTodFxkbgKlI1HS41hq0yGbAzr3ZhrtVbg3vhSFEBQTVkja/A==", "path": "devexpress.win.navigation/23.1.5", "hashPath": "devexpress.win.navigation.23.1.5.nupkg.sha512"}, "DevExpress.Win.PdfViewer/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-FOUvDROV1zsQBh+PesPMbUZOSZOGis3IDYBpZix32dbhFMjDuYPn1FtDe6n97LPX6e2WyzYCdbsIuxjtczeXeg==", "path": "devexpress.win.pdfviewer/23.1.5", "hashPath": "devexpress.win.pdfviewer.23.1.5.nupkg.sha512"}, "DevExpress.Win.PivotGrid/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-Jxfjt55mBP2p3eChme1+aolCDvI2K59PwGXwpOn1Y+68WP2PJKZxaAyqZYeJNho1NmtJ9qTKJxLk3n+DjmGnCA==", "path": "devexpress.win.pivotgrid/23.1.5", "hashPath": "devexpress.win.pivotgrid.23.1.5.nupkg.sha512"}, "DevExpress.Win.Printing/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-t1Y6ER7dIdSBpzdC8RIltLz3hVNYbYwswYwIVvtAB423JHgMQZAKQMgJOTg5I9nT+G+UQ+Zh87MMyjiCgYUXbg==", "path": "devexpress.win.printing/23.1.5", "hashPath": "devexpress.win.printing.23.1.5.nupkg.sha512"}, "DevExpress.Win.Reporting/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-ow7eDN4uZ4YjdXX8nzqvMC+g1J/Nty3IhHfWdB7DygETpSHfEVf+5WD8hoQEaPtEt3e4/A9ghm1jJrjiksKdIA==", "path": "devexpress.win.reporting/23.1.5", "hashPath": "devexpress.win.reporting.23.1.5.nupkg.sha512"}, "DevExpress.Win.RichEdit/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-TGbujl4/dA7e96B2UYOIxq7VOLnToNYoPQZJz/93kGrdH5VMX/81QxjL3/7gFtLrPJCL0M3dRI4LcBDFK5EBTA==", "path": "devexpress.win.richedit/23.1.5", "hashPath": "devexpress.win.richedit.23.1.5.nupkg.sha512"}, "DevExpress.Win.Scheduler/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-yerByDkBjIMobWfjITRm7DRI1okpXzhncwbZs7MQRTFy/wTiBsZZkB2BL+K0W4HgoD1X90Rg2eIs1m0GVRV4tQ==", "path": "devexpress.win.scheduler/23.1.5", "hashPath": "devexpress.win.scheduler.23.1.5.nupkg.sha512"}, "DevExpress.Win.SchedulerExtensions/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-+M5Aunh8EzCXepifV4EZzLGwkzhD0GP9qC7OL0z2DLEh5F6arVC1eFsNGCXE/YzwpEOz8VmmYIFiJJLkd4Nd/Q==", "path": "devexpress.win.schedulerextensions/23.1.5", "hashPath": "devexpress.win.schedulerextensions.23.1.5.nupkg.sha512"}, "DevExpress.Win.SchedulerReporting/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-dTQ+riBBmnl9pTXMZR8IE1NduESzbBmNCWNFyw49J8tyeXdMyg8/bbI2gsjby+c0BMmE5ZXpyXEQbT9OsKXp+w==", "path": "devexpress.win.schedulerreporting/23.1.5", "hashPath": "devexpress.win.schedulerreporting.23.1.5.nupkg.sha512"}, "DevExpress.Win.SpellChecker/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-/Ns96bEciO7q5rCPKt+tAz3kviFT1x6rFX8278RH5trIt8zkg+Zv0b+uA7LHSlGY+wwsmPNOOBaEmEQXY8qa+Q==", "path": "devexpress.win.spellchecker/23.1.5", "hashPath": "devexpress.win.spellchecker.23.1.5.nupkg.sha512"}, "DevExpress.Win.Spreadsheet/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-y4C4TYEh5QuCn+eBIZD7OOnCd38c/nHvdbconWozd+gm2vzMHtgM+8sxrXIza3Q0mmQU/dZK++0DV0s9mTxaVQ==", "path": "devexpress.win.spreadsheet/23.1.5", "hashPath": "devexpress.win.spreadsheet.23.1.5.nupkg.sha512"}, "DevExpress.Win.TreeList/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-QJ6XaSoAmvpmkqFEMxV+kFH7WxCKodFkv8hp8kRzY6RMyxCZ1HfHNml4sK5cTQDyh7dIlR+1VphxF9tDNy2UTQ==", "path": "devexpress.win.treelist/23.1.5", "hashPath": "devexpress.win.treelist.23.1.5.nupkg.sha512"}, "DevExpress.Win.TreeMap/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-d3hJQd8gY2gYzdoqRl5pQS7tNxnU1VOnSm6qnVMtymrxbzOUtzwyrtNkK9nvvswAY7pU6YorV62Ghh7C4Nh72A==", "path": "devexpress.win.treemap/23.1.5", "hashPath": "devexpress.win.treemap.23.1.5.nupkg.sha512"}, "DevExpress.Win.VerticalGrid/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-zvznW4JKfRicHq40MEQNxBJI5odoZD/M7JKnRs/Itt1qSbI6jbNT+dk8g6PZcUbb9S5HrKkFmSWVWMQyjkN4OQ==", "path": "devexpress.win.verticalgrid/23.1.5", "hashPath": "devexpress.win.verticalgrid.23.1.5.nupkg.sha512"}, "DevExpress.Xpo/23.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-HIPM90H3u7EM4RyynytYUtNqhJ1CGmw2Cfm0Z1ISMZkwReCc/KUuvbexqJ7jVtcVPkRaZnhXbVgaL/TW2CyP1g==", "path": "devexpress.xpo/23.1.5", "hashPath": "devexpress.xpo.23.1.5.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Mapster/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NrCUX/rJa5PTyo6iW4AL5dZLU9PDNlYnrJOVjgdpo5OQM9EtWH2CMHnC5sSuJWC0d0b0SnmeRrIviEem6WxtuQ==", "path": "mapster/7.3.0", "hashPath": "mapster.7.3.0.nupkg.sha512"}, "Mapster.Core/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TNdqZk2zAuBYfJF88D/3clQTOyOdqr1crU81yZQtlGa+e7FYWhJdK/buBWT+TpM3qQko9UzmzfOT4iq3JCs/ZA==", "path": "mapster.core/1.2.0", "hashPath": "mapster.core.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-mXJYgxqyjIy6Zd+1pFMkl0CtIY4coLFAzidr4RGeWW0tjB8nEWpye7h6PtoavRJa6j5KeGXZsqYGvMwqI8dx6Q==", "path": "microsoft.codeanalysis/4.4.0", "hashPath": "microsoft.codeanalysis.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-JfHupS/B7Jb5MZoYkFFABn3mux0wQgxi2D8F/rJYZeRBK2ZOyk7TjQ2Kq9rh6W/DCh0KNbbSbn5qoFar+ueHqw==", "path": "microsoft.codeanalysis.common/4.4.0", "hashPath": "microsoft.codeanalysis.common.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eD2w0xHRoaqK07hjlOKGR9eLNy3nimiGNeCClNax1NDgS/+DBtBqCjXelOa+TNy99kIB3nHhUqDmr46nDXy/RQ==", "path": "microsoft.codeanalysis.csharp/4.4.0", "hashPath": "microsoft.codeanalysis.csharp.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ADmI2jcwJP9GgBsVx2l0Bo0v3Hn4hHBg1uJ5zHd230mkO8rUJBLZu2h3tCbpwJMkpAIRtrxuZDD5uNfiyz0Q5Q==", "path": "microsoft.codeanalysis.csharp.workspaces/4.4.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.VisualBasic/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-+GsWdmbKcjJ9ggduM6+ZI4pMI/2doX+BLbQNEQcF2oytpwzeBgyJpA3FgUWtRGBhPidzaKOj4/BMezlzn0VqxA==", "path": "microsoft.codeanalysis.visualbasic/4.4.0", "hashPath": "microsoft.codeanalysis.visualbasic.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.VisualBasic.Workspaces/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-B+r34TW1GhlbuntW5072Prm2BOk3dvdzg9yEzYmuR7jgMRZqFyypCLjizNTzWb7omEX4OfCWckDFCHH01XK10g==", "path": "microsoft.codeanalysis.visualbasic.workspaces/4.4.0", "hashPath": "microsoft.codeanalysis.visualbasic.workspaces.4.4.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-6KzmmTIUU7qInQldcSPaW0nkrO71zlFPhoiabFBhkokEit49rLx4Kr/G3agBchYzirScrXibqgTRQkvx9WcJTw==", "path": "microsoft.codeanalysis.workspaces.common/4.4.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.4.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {"type": "package", "serviceable": true, "sha512": "sha512-ZFc3Pqiox/AcJECaYHXZtdB8XF12pJKvlrIxavL01z7VpW8Oy1d4tAgcnZfgburH5eVtAH4Bi2QKTHnN8va0ug==", "path": "microsoft.dotnet.upgradeassistant.extensions.default.analyzers/0.4.410601", "hashPath": "microsoft.dotnet.upgradeassistant.extensions.default.analyzers.0.4.410601.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "path": "microsoft.extensions.configuration/2.0.0", "hashPath": "microsoft.extensions.configuration.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-0qbNyxGpuNP/fuQ3FLHesm1Vn/83qYcAgVsi1UQCQN1peY4YH1uiizOh4xbYkQyxiVMD/c/zhiYYv94G0DXSSA==", "path": "microsoft.extensions.configuration.abstractions/3.1.8", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "path": "microsoft.extensions.configuration.binder/2.0.0", "hashPath": "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "path": "microsoft.extensions.dependencymodel/3.0.0", "hashPath": "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-U7ffyzrPfRDH5K3h/mBpqJVoHbppw1kc1KyHZcZeDR7b1A0FRaqMSiizGpN9IGwWs9BuN7oXIKFyviuSGBjHtQ==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-7ZJUKwPipkDvuv2KJPZ3r01wp2AWNMiYH+61i0dL89F7QICknjKpWgLKLpTSUYFgl77S3b4264I6i4HzDdrb2A==", "path": "microsoft.extensions.hosting.abstractions/3.1.8", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "path": "microsoft.extensions.objectpool/5.0.10", "hashPath": "microsoft.extensions.objectpool.5.0.10.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/lGICwO27fCkQRK3tZseVzFjZaxfGmui990E67sB4MuiPzdJHnJDS/BeYWrHShSSBgCl4KyKRx4ux686fftPg==", "path": "microsoft.extensions.options.configurationextensions/2.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.VisualBasic/10.4.0-preview.18571.3": {"type": "package", "serviceable": true, "sha512": "sha512-9o+ngDFh/65nEtLaMJ4I7nsMFqTYStbMzdp0gX0ytOFs77WrCNMoEEt72fRHvDbcPXmFD/+UF6LOPqEdcU//0Q==", "path": "microsoft.visualbasic/10.4.0-preview.18571.3", "hashPath": "microsoft.visualbasic.10.4.0-preview.18571.3.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JwM65WXVca58WzqY/Rpz7FGyHbN/SMdyr/3EI2CwPIYkB55EIRJUdPQJwO64x3ntOwPQoqCATKuDYA9K7Np5Ww==", "path": "microsoft.win32.registry.accesscontrol/7.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.7.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "Microsoft.Windows.Compatibility/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vVTf/gmV6tLpi8IvmIwhD/86xWcCPazbrGRoxjXe5LQ0HqcmLgKOTYzvE058kRtVTZgYuQFFrErrXMFG4weCeQ==", "path": "microsoft.windows.compatibility/7.0.3", "hashPath": "microsoft.windows.compatibility.7.0.3.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvgRaF+M0xGLDv2Geb/0v0LEADheH8aK72GRAUJdnqnJVsQO60ki1XO8M3keEhnjm+T5NvLm41pNXAVYAPiSg==", "path": "runtime.linux-arm.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5VCyRCtCIYU8FR/W8oo7ouFuJ8tmAg9ddsuXhfCKZfZrbaVZSKxkmNBa6fxkfYPueD0jQfOvwFBmE5c6zalCSw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DV9dWDUs23OoZqMWl5IhLr3D+b9koDiSHQxFKdYgWnQbnthv8c/yDjrlrI8nMrDc71RAKCO8jlUojzuPMX04gg==", "path": "runtime.linux-x64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-L4Ivegqc3B0Fee7VifFy2JST9nndm+uvJ0viLIZUaImDfnr+JmRin9Tbqd56KuMtm0eVxHpNOWZBPtKrA/1h5Q==", "path": "runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jFwh4sKSXZ7al5XrItEO4GdGWa6XNxvNx+LhEHjrSzOwawO1znwJ+Dy+VjnrkySX9Qi4bnHNLoiqOXbqMuka4g==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4LrHEfke/z9+z+iuVr35NlkhdZldY8JGNMYUN+sfPK/U/6TcE+vP44I0Yv0ir1v0bqIzq3v6Qdv1c1vmp8s4g==", "path": "runtime.osx-x64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.AspNetCore/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iMwFUJDN+/yWIPz4TKCliagJ1Yn//SceCYCzgdPwe/ECYUwb5/WUL8cTzRKV+tFwxGjLEV/xpm0GupS5RwbhSQ==", "path": "serilog.aspnetcore/6.1.0", "hashPath": "serilog.aspnetcore.6.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-orv0h38ZVPCPo/FW0LGv8/TigXwX8cIwXeQcaNYhikkqELDm8sUFLMcof/Sjcq5EvYCm5NA7MV3hG4u75H44UQ==", "path": "system.componentmodel.composition/7.0.0", "hashPath": "system.componentmodel.composition.7.0.0.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yy/xYOznnc7Hfg2/LeVqAMlJGv1v7b1ILxFShzx5PWUv53PwU0MaKPG8Dh9DC3gxayzw44UVuQJImhw7LtMKlw==", "path": "system.componentmodel.composition.registration/7.0.0", "hashPath": "system.componentmodel.composition.registration.7.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "path": "system.data.datasetextensions/4.5.0", "hashPath": "system.data.datasetextensions.4.5.0.nupkg.sha512"}, "System.Data.Odbc/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-siwu7NoCsfHa9bfw2a2wSeTt2c/rhk3X8I28nJln1dlxdW3KqhRp0aW87yH1XkCo9h8zO1qcIfdTHO7YvvWLEA==", "path": "system.data.odbc/7.0.0", "hashPath": "system.data.odbc.7.0.0.nupkg.sha512"}, "System.Data.OleDb/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bhAs+5X5acgg3zQ6N4HqxqfwwmqWJzgt54BC8iwygcqa2jktxDFzxwN83GNvqgoTcTs2tenDS/jmhC+AQsmcyg==", "path": "system.data.oledb/7.0.0", "hashPath": "system.data.oledb.7.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-L+zIMEaXp1vA4wZk1KLMpk6tvU0xy94R0IfmhkmTWeC4KwShsmAfbg5I19LgjsCTYp6GVdXZ2aHluVWL0QqBdA==", "path": "system.diagnostics.performancecounter/7.0.0", "hashPath": "system.diagnostics.performancecounter.7.0.0.nupkg.sha512"}, "System.DirectoryServices/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z4FVdUJEVXbf7/f/hU6cFZDtxN5ozUVKJMzXoHmC+GCeTcqzlxqmWtxurejxG3K+kZ6H0UKwNshoK1CYnmJ1sg==", "path": "system.directoryservices/7.0.1", "hashPath": "system.directoryservices.7.0.1.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qMpVgR5+XactuWzpqsiif++lnTzfDESbQv4UYFZpgdRvFCFIi4JgufOITCDlu+x2vEmwYOVbwrR1N365dDJRLg==", "path": "system.directoryservices.accountmanagement/7.0.0", "hashPath": "system.directoryservices.accountmanagement.7.0.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-t9hsL+UYRzNs30pnT2Tdx6ngX8McFUjru0a0ekNgu/YXfkXN+dx5OvSEv0/p7H2q3pdJLH7TJPWX7e55J8QB9A==", "path": "system.directoryservices.protocols/7.0.1", "hashPath": "system.directoryservices.protocols.7.0.1.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Packaging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+j5ezLP7785/pd4taKQhXAWsymsIW2nTnE/U3/jpGZzcJx5lip6qkj6UrxSE7ZYZfL0GaLuymwGLqwJV/c7O7Q==", "path": "system.io.packaging/7.0.0", "hashPath": "system.io.packaging.7.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0nWQjM5IofaIGpvkifN+LLuYwBG6BHlpmphLhhOJepcW12G8qToGuNDRgBzeTVBZzp33wVsESSZ8hUOCfq+8QA==", "path": "system.io.ports/7.0.0", "hashPath": "system.io.ports.7.0.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-bi2/w2EDXqxno8zfbt6vHcrpGw0Pav8tEMzmJraHwJvWYJd45wcqr7gNa2IUs91j4z+BNGMooStaWS6pm2Lq0A==", "path": "system.private.servicemodel/4.10.2", "hashPath": "system.private.servicemodel.4.10.2.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rVf4vEyGQphXTITF39uXlgTcp8Ekcu2aNwxyVLU7fDyNOk0W+/PPpj9PoC2cFL4wgJZJltiss5eQptE2C4f1Sw==", "path": "system.reflection.context/7.0.0", "hashPath": "system.reflection.context.7.0.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M0riW7Zgxca3Elp1iZVhzH7PWWT5bPSrdMFGCAGoH1n9YLuXOYE78ryui051Icf3swWWa8feBRoSxOCYwgMy8w==", "path": "system.runtime.caching/7.0.0", "hashPath": "system.runtime.caching.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==", "path": "system.security.cryptography.pkcs/7.0.2", "hashPath": "system.security.cryptography.pkcs.7.0.2.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3evI3sBfKqwYSwuBcYgShbmEgtXcg8N5Qu+jExLdkBXPty2yGDXq5m1/4sx9Exb8dqdeMPUs/d9DQ0wy/9Adwg==", "path": "system.security.cryptography.protecteddata/7.0.1", "hashPath": "system.security.cryptography.protecteddata.7.0.1.nupkg.sha512"}, "System.Security.Cryptography.Xml/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MCxBCtH0GrDuvU63ZODwQHQZPchb24pUAX3MfZ6b13qg246ZD10PRdOvay8C9HBPfCXkymUNwFPEegud7ax2zg==", "path": "system.security.cryptography.xml/7.0.1", "hashPath": "system.security.cryptography.xml.7.0.1.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Yb8MFiJxBBtm2JnfS/5SxYzm2HqkEmHu5xeaVIHXy83sNpty9wc30JifH2xgda821D6nr1UctbwbdZqN4LBUKQ==", "path": "system.servicemodel.duplex/4.9.0", "hashPath": "system.servicemodel.duplex.4.9.0.nupkg.sha512"}, "System.ServiceModel.Http/4.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-1AhiJwPc+90GjBd/sDkT93RVuRj688ZQInXzWfd56AEjDzWieUcAUh9ppXhRuEVpT+x48D5GBYJc1VxDP4IT+Q==", "path": "system.servicemodel.http/4.10.2", "hashPath": "system.servicemodel.http.4.10.2.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-nXgnnkrZERUF/KwmoLwZPkc7fqgiq94DXkmUZBvDNh/LdZquDvjy2NbhJLElpApOa5x8zEoQoBZyJ2PqNC39qg==", "path": "system.servicemodel.nettcp/4.9.0", "hashPath": "system.servicemodel.nettcp.4.9.0.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-8QOguIqHtWYywBt7SubPhdICE2LClHzqOMDy0LQIui4T3QJOae7g6UR+alCW61nEufYNtO8Uss41EbXqD8hdww==", "path": "system.servicemodel.primitives/4.10.2", "hashPath": "system.servicemodel.primitives.4.10.2.nupkg.sha512"}, "System.ServiceModel.Security/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-iurpbSmPgotHps94VQ6acvL6hU2gjiuBmQI7PwLLN76jsbSpUcahT0PglccKIAwoMujATk/LWtAapBHpwCFn2g==", "path": "system.servicemodel.security/4.9.0", "hashPath": "system.servicemodel.security.4.9.0.nupkg.sha512"}, "System.ServiceModel.Syndication/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V3q1Jr3KWo+i201/vUUPfg83rjJLhL5+ROh16PtPhaUJRHwoEBoGWtg0r6pFBRPaDqNY6hXvNgHktDj0gvMEpA==", "path": "system.servicemodel.syndication/7.0.0", "hashPath": "system.servicemodel.syndication.7.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rPfXTJzYU46AmWYXRATQzQQ01hICrkl3GuUHgpAr9mnUwAVSsga5x3mBxanFPlJBV9ilzqMXbQyDLJQAbyTnSw==", "path": "system.serviceprocess.servicecontroller/7.0.1", "hashPath": "system.serviceprocess.servicecontroller.7.0.1.nupkg.sha512"}, "System.Speech/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7E0uB92Cx2sXR67HW9rMKJqDACdLuz9t3I3OwZUFDzAgwKXWuY6CYeRT/NiypHcyZO2be9+0H0w0M6fn7HQtgQ==", "path": "system.speech/7.0.0", "hashPath": "system.speech.7.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading.AccessControl/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uh6LWSk8Dlp1cavk4XQYtDHOMZpSa5KiqM0VBiflhXWGT63RGV+NhNsVxiEykL4S/0LVcgy+/AxC5ITQ9QLo8w==", "path": "system.threading.accesscontrol/7.0.1", "hashPath": "system.threading.accesscontrol.7.0.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Web.Services.Description/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-d20B3upsWddwSG5xF3eQLs0cAV3tXDsBNqP4kh02ylfgZwqfpf4f/9KiZVIGIoxULt2cKqxWs+U4AdNAJ7L8cQ==", "path": "system.web.services.description/4.9.0", "hashPath": "system.web.services.description.4.9.0.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "AIC.SharedData/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "CodingReport/2023.1.2.103": {"type": "project", "serviceable": false, "sha512": ""}, "EnchartDOLib/2023.1.0.2": {"type": "project", "serviceable": false, "sha512": ""}, "EnchartServer.Data/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "ICCryptoHelper/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "McKesson.HIC.ChargeSummaryColdFeedHelper/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "McKesson.HIC.HPFColdFeed/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "McKessonIntelligentCoding.Data/2023.1.2.57": {"type": "project", "serviceable": false, "sha512": ""}, "MICCustomConnectionProviders/2023.1.3.2": {"type": "project", "serviceable": false, "sha512": ""}, "ICCryptoHelper.Reference/2023.1.2.57": {"type": "reference", "serviceable": false, "sha512": ""}}}
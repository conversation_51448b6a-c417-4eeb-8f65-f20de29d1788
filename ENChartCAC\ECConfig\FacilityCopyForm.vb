Option Infer On

Imports ECConfig.MainForm
Imports DevExpress.Xpo
Imports System.ComponentModel
Imports System.Collections.Specialized
Imports Timeout
Imports System.Threading.Tasks

Public Class FacilityCopyForm

    Private Sub FacilityCopyForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Dim fcol As New XPCollection(GetType(DOFacility))
        fcol.Reload()
        If fcol IsNot Nothing Then
            For Each facility In fcol
                cboFrom.Properties.Items.Add(facility)
                cboTo.Properties.Items.Add(facility)
            Next
        End If

        Dim userCollection As New XPCollection(Of DOUser)
        For Each user In userCollection
            'Dim _dtoUser As New DTOUser(user, f.Oid)
            'Me.Users.Add(_dtoUser)

            Dim row As String() = {user.Oid, user.UserName, True}
            gvUsers.Rows.Add(row)
        Next
        gvUsers.Sort(gvUsers.Columns(1), ListSortDirection.Ascending)
        gvTreatmentArea.Sort(gvTreatmentArea.Columns(0), ListSortDirection.Ascending)
    End Sub

    Private Function GetSelectedUsersToCopyAsListOfOid() As List(Of Integer)
        Dim userListToReturn As New List(Of Integer)

        For Each row As DataGridViewRow In gvUsers.Rows
            If row.Cells("colCopy").Value = True Then
                Dim oid As Integer = row.Cells("colOID").Value
                userListToReturn.Add(oid)
            End If
        Next

        Return userListToReturn
    End Function

    Private Function GetTreatmentAreasToCopyAsListOfStrings() As StringCollection
        Dim taList As New StringCollection

        For Each row As DataGridViewRow In gvTreatmentArea.Rows
            If row.Cells("colCopyTA").Value = True Then
                taList.Add(row.Cells("colTA").Value)
            End If
        Next

        Return taList
    End Function

    Private Async Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        'Dim sw As New Stopwatch
        'sw.Start()

        Try
            BasicTimeoutWatcher.Suspend()

            Dim fromFac As DOFacility = cboFrom.EditValue
            Dim toFac As DOFacility = cboTo.EditValue

            Me.btnOK.Enabled = False
            Me.btnCancel.Enabled = False
            Me.cboFrom.Enabled = False
            Me.cboTo.Enabled = False

            UpDateProgress(-1)
            UpDateProgress(1)
            Me.Refresh()
            Application.DoEvents()

            Dim taList = GetTreatmentAreasToCopyAsListOfStrings()
            Dim userList = GetSelectedUsersToCopyAsListOfOid()

            Dim fp As New FacilityProxy(fromFac, taList, userList, ceImportCDM.Checked)
            Dim pi As New Progress(Of Integer)(AddressOf UpDateProgress)
            Dim tt = Task.Run(Sub() Utils.LoadConfigFromProxy(toFac, toFac.ConfigInstanceVersion, fp, pi, ceImportCDM.Checked, ceImportReports.Checked))
            Await tt

            'Utils.LoadConfigFromProxy(toFac, toFac.ConfigInstanceVersion, fp, AddressOf UpDateProgress, ceImportCDM.Checked, ceImportReports.Checked)
            'sw.Stop()
            'MessageBox.Show(sw.Elapsed.ToString())

            BasicTimeoutWatcher.Resume()
            MessageBox.Show("Configuration successfully copied!", "Be sure to test!", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Finally
            BasicTimeoutWatcher.Resume()
        End Try

        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private _nextRefreshePos As Integer = 0

    Public Sub UpDateProgress(ByVal pDone As Integer)
        Me.progressControl.Position = pDone

        If pDone = -1 Then
            _nextRefreshePos = 1
        End If

        If pDone >= _nextRefreshePos Then
            _nextRefreshePos += 1
            Me.Refresh()
            Application.DoEvents()
        End If
    End Sub

    Private Sub cboFrom_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboFrom.SelectedIndexChanged
        gvTreatmentArea.Rows.Clear()

        Dim fac As DOFacility = cboFrom.EditValue
        Dim taList = GetTreatmentAreasAsListByFacility(fac)
        For Each ta As String In taList
            Dim row As String() = {ta, True}
            gvTreatmentArea.Rows.Add(row)
        Next

        gvTreatmentArea.Sort(gvTreatmentArea.Columns(0), ListSortDirection.Ascending)
    End Sub

    Private Sub btnClearAllUsers_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClearAllUsers.Click
        For Each row As DataGridViewRow In gvUsers.Rows
            row.Cells("colCopy").Value = False
        Next
    End Sub

    Private Sub btnSellectAllusers_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSellectAllusers.Click
        For Each row As DataGridViewRow In gvUsers.Rows
            row.Cells("colCopy").Value = True
        Next
    End Sub
End Class
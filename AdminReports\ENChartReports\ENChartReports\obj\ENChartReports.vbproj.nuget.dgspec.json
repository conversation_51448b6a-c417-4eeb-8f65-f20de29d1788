{"format": 1, "restore": {"C:\\AIC\\Main_Next\\SourceCode\\AdminReports\\ENChartReports\\ENChartReports\\ENChartReports.vbproj": {}}, "projects": {"C:\\AIC\\Main_Next\\SourceCode\\AdminReports\\ENChartReports\\ENChartReports\\ENChartReports.vbproj": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\AdminReports\\ENChartReports\\ENChartReports\\ENChartReports.vbproj", "projectName": "ENChartReports", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\AdminReports\\ENChartReports\\ENChartReports\\ENChartReports.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\AdminReports\\ENChartReports\\ENChartReports\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Document.Processor": {"target": "Package", "version": "[23.1.5, )"}, "DevExpress.Win.Design": {"target": "Package", "version": "[23.1.5, )"}, "DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj", "projectName": "AIC.SharedData", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[7.0.3, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj": {"version": "2023.1.0.2", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj", "projectName": "EnchartDOLib", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\EnchartDOLib.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\EnchartDOLib\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj"}, "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj"}, "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj"}, "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "Mapster": {"target": "Package", "version": "[7.3.0, )"}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "Microsoft.VisualBasic": {"target": "Package", "version": "[10.4.0-preview.18571.3, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[7.0.3, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj": {"version": "2023.1.3.2", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj", "projectName": "MICCustomConnectionProviders", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\MICCustomConnectionProviders.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\MICCustomDataProviders\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj", "projectName": "McKessonIntelligentCoding.Data", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\McKessonIntelligentCoding.Data.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\McKessonIntelligentCoding\\McKessonIntelligentCoding.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}, "System.ValueTuple": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj", "projectName": "ICCryptoHelper", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\AICCryptoHelper.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\ICCryptoHelper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[7.0.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.35.0, )"}, "System.ValueTuple": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj", "projectName": "EnchartServer.Data", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\EnchartServer.Data.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\Server\\EnchartServer\\src\\EnchartServer.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj": {"projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\AIC.SharedData\\AIC.SharedData.vbproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"DevExpress.Xpo": {"target": "Package", "version": "[23.1.5, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[7.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[6.1.0, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[7.0.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}
﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
ENChartReports
</name>
</assembly>
<members>
<member name="T:ENChartReports.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:ENChartReports.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:ENChartReports.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:ENChartReports.My.Resources.Resources.apply_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:ENChartReports.My.Resources.Resources.cancel_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:ENChartReports.My.Resources.Resources.edit_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="M:ENChartReports.ReportRolesEditor.BuildReportList(EnchartDOLib.DOFacility)">
 <summary>
 This builds a master list of reports for that particular facility and should be rebuilt each time the facility changes.
 </summary>
 <param name="facility"></param>
 <returns></returns>
</member>
<member name="M:ENChartReports.ReportRolesEditor.LoadChanges">
 <summary>
 Load the changes based on the current UserRole. If the change exists it will be added/removed from the listbox based upon if it is enabled or disabled.
 </summary>
</member>
<member name="M:ENChartReports.ReportRolesEditor.AddReportRoleToAddList(EnchartDOLib.DOFacilityReportCategoryRole)">
 <summary>
 Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
 </summary>
 <param name="item"></param>
</member>
<member name="M:ENChartReports.ReportRolesEditor.AddReportRoleToAddList(EnchartDOLib.DOFacilityReportRole)">
 <summary>
 Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
 </summary>
 <param name="item"></param>
</member>
<member name="M:ENChartReports.ReportRolesEditor.AddReportRoleToRemoveList(EnchartDOLib.DOFacilityReportCategoryRole)">
 <summary>
 Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
 </summary>
 <param name="item"></param>
</member>
<member name="M:ENChartReports.ReportRolesEditor.AddReportRoleToRemoveList(EnchartDOLib.DOFacilityReportRole)">
 <summary>
 Cycle through the ChangeList and add the change if it is a new change and update the change if already exist
 </summary>
 <param name="item"></param>
</member>
<member name="P:ENChartReports.ReportStartup.ActiveFacility">
 <summary>
 The ActiveFacility is the DOFacility corresponding to the facility OID that is passed into this application via command line, 
 if no command line parameter is passed, ActiveFacility will be Nothing
 </summary>
 <returns></returns>
</member>
<member name="P:ENChartReports.ReportStartup.CurrentFacility">
 <summary>
 The CurrentFacility is the facility of the node that is currently selected in treeReports
 </summary>
 <returns></returns>
</member>
<member name="M:ENChartReports.ReportStartup.BulkLoadReportConfig(EnchartDOLib.DOFacility)">
 <summary>
 This method essentially just loads a big portion of the reports configuration so that it is stored in the
 devexpress cache on the client machine.
 </summary>
 <remarks>
 note, this may also help refresh some lists after editing categories and reports, but i'm not sure.</remarks>
 <param name="facility"></param>
</member>
</members>
</doc>

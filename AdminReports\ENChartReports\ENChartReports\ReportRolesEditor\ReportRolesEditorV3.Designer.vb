﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ReportRolesEditorV3
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.TreeList1 = New DevExpress.XtraTreeList.TreeList()
        Me.colID = New DevExpress.XtraTreeList.Columns.TreeListColumn()
        Me.colName = New DevExpress.XtraTreeList.Columns.TreeListColumn()
        Me.colParentID = New DevExpress.XtraTreeList.Columns.TreeListColumn()
        Me.colAllowed = New DevExpress.XtraTreeList.Columns.TreeListColumn()
        Me.ReportNodeBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.lblReportRole = New DevExpress.XtraEditors.LabelControl()
        Me.lblFacility = New DevExpress.XtraEditors.LabelControl()
        Me.cboFacility = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnNewRole = New DevExpress.XtraEditors.SimpleButton()
        Me.cboRoles = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Button1 = New System.Windows.Forms.Button()
        CType(Me.TreeList1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ReportNodeBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboFacility.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboRoles.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'TreeList1
        '
        Me.TreeList1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TreeList1.Columns.AddRange(New DevExpress.XtraTreeList.Columns.TreeListColumn() {Me.colID, Me.colName, Me.colParentID, Me.colAllowed})
        Me.TreeList1.DataSource = Me.ReportNodeBindingSource
        Me.TreeList1.Location = New System.Drawing.Point(19, 96)
        Me.TreeList1.Name = "TreeList1"
        Me.TreeList1.OptionsBehavior.PopulateServiceColumns = True
        Me.TreeList1.Size = New System.Drawing.Size(580, 413)
        Me.TreeList1.TabIndex = 0
        '
        'colID
        '
        Me.colID.FieldName = "ID"
        Me.colID.Name = "colID"
        Me.colID.Width = 141
        '
        'colName
        '
        Me.colName.FieldName = "Name"
        Me.colName.Name = "colName"
        Me.colName.Visible = True
        Me.colName.VisibleIndex = 0
        Me.colName.Width = 141
        '
        'colParentID
        '
        Me.colParentID.FieldName = "ParentID"
        Me.colParentID.Name = "colParentID"
        Me.colParentID.OptionsColumn.ReadOnly = True
        Me.colParentID.Width = 140
        '
        'colAllowed
        '
        Me.colAllowed.FieldName = "Allowed"
        Me.colAllowed.Name = "colAllowed"
        Me.colAllowed.Width = 140
        '
        'ReportNodeBindingSource
        '
        Me.ReportNodeBindingSource.DataSource = GetType(ENChartReports.ReportRolesEditorV3.ReportNode)
        '
        'lblReportRole
        '
        Me.lblReportRole.Location = New System.Drawing.Point(19, 43)
        Me.lblReportRole.Name = "lblReportRole"
        Me.lblReportRole.Size = New System.Drawing.Size(61, 13)
        Me.lblReportRole.TabIndex = 19
        Me.lblReportRole.Text = "Report Role:"
        '
        'lblFacility
        '
        Me.lblFacility.Location = New System.Drawing.Point(19, 20)
        Me.lblFacility.Name = "lblFacility"
        Me.lblFacility.Size = New System.Drawing.Size(37, 13)
        Me.lblFacility.TabIndex = 18
        Me.lblFacility.Text = "Facility:"
        '
        'cboFacility
        '
        Me.cboFacility.Location = New System.Drawing.Point(62, 17)
        Me.cboFacility.Name = "cboFacility"
        Me.cboFacility.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboFacility.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboFacility.Size = New System.Drawing.Size(275, 20)
        Me.cboFacility.TabIndex = 17
        '
        'btnNewRole
        '
        Me.btnNewRole.Location = New System.Drawing.Point(361, 46)
        Me.btnNewRole.Name = "btnNewRole"
        Me.btnNewRole.Size = New System.Drawing.Size(43, 23)
        Me.btnNewRole.TabIndex = 16
        Me.btnNewRole.Text = "New"
        '
        'cboRoles
        '
        Me.cboRoles.Location = New System.Drawing.Point(112, 43)
        Me.cboRoles.Name = "cboRoles"
        Me.cboRoles.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboRoles.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboRoles.Size = New System.Drawing.Size(225, 20)
        Me.cboRoles.TabIndex = 15
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(344, 538)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(75, 23)
        Me.Button1.TabIndex = 20
        Me.Button1.Text = "Button1"
        Me.Button1.UseVisualStyleBackColor = True
        '
        'ReportRolesEditorV3
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(624, 596)
        Me.Controls.Add(Me.Button1)
        Me.Controls.Add(Me.lblReportRole)
        Me.Controls.Add(Me.lblFacility)
        Me.Controls.Add(Me.cboFacility)
        Me.Controls.Add(Me.btnNewRole)
        Me.Controls.Add(Me.cboRoles)
        Me.Controls.Add(Me.TreeList1)
        Me.Name = "ReportRolesEditorV3"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "ReportRolesEditorV3"
        CType(Me.TreeList1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ReportNodeBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboFacility.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboRoles.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents TreeList1 As DevExpress.XtraTreeList.TreeList
    Friend WithEvents lblReportRole As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblFacility As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cboFacility As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnNewRole As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents cboRoles As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ReportNodeBindingSource As BindingSource
    Friend WithEvents colID As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents colName As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents colParentID As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents colAllowed As DevExpress.XtraTreeList.Columns.TreeListColumn
    Friend WithEvents Button1 As Button
End Class

SELECT DISTINCT "DOChartInfo"."Visit<PERSON>", 
	"DOChart"."DateOfService", "DOChartSummary"."EMLevel", "DOChartSummary"."EMLevelHCPCS", 
	"DOChartSummary"."EMLevelCDM",
"DOCodingReportRecord"."Detail", "DOCodingReportRecord"."CDM", "DOCodingReportRecord"."HCPCS", 
	"DOCodingReportRecord"."InsertDate", "DOCodingReportRecord"."Points"

FROM   ((("DBA"."DOChartInfo" "DOChartInfo" INNER JOIN "DBA"."DOChart" "DOChart" ON "DOChartInfo"."OID" = "DOChart"."ChartInfo")
        left outer join "DBA"."DOChartSummary" "DOChartSummary" ON "DOChartInfo"."Chart" = "DOChartSummary"."Chart")
        left outer join "DBA"."DOCodingReportRecord" "DOCodingReportRecord" ON "DOChartInfo"."Chart" = "DOCodingReportRecord"."Chart")
where "DOChart"."CreationDate" between '2007-11-26' and '2007-11-27'
AND "DOCodingReportRecord"."CDM"<>''

﻿Imports AIC.SharedData

Public Class BOMedTherapy
    Public Property Chart As Integer
    Public Property TherapyType As MedTherapyType
    Private _startDate As Date?
    Private _endDate As Date?

    Public Property StartDate As Date?
        Get
            If StartTime.HasValue AndAlso _startDate.HasValue Then
                Return New Date(_startDate.Value.Year, _startDate.Value.Month, _startDate.Value.Day, StartTime.Value.Hour, StartTime.Value.Minute, StartTime.Value.Second)
            Else
                Return _startDate
            End If
        End Get
        Set(value As Date?)
            _startDate = value
        End Set
    End Property
    Public Property EndDate As Date?
        Get
            If EndTime.HasValue AndAlso _endDate.HasValue Then
                Return New Date(_endDate.Value.Year, _endDate.Value.Month, _endDate.Value.Day, EndTime.Value.Hour, EndTime.Value.Minute, EndTime.Value.Second)
            Else
                Return _endDate
            End If
        End Get
        Set(value As Date?)
            _endDate = value
        End Set
    End Property
    Public Property Duration As Integer

    Public Property Medication As String
    Public Property Route As String
    Public Property Site As String
    Public Property Rate As String
    Public Property IsObs As Boolean
    Public Property StartTime As Date?
    Public Property EndTime As Date?

    Public Sub New()
    End Sub

    Public Function ToLegacyString() As String
        'createswitch statement to handle the different types of therapyTypes
        Select Case TherapyType
            'Case MedTherapyType.Infusion
            '    Return "Infusion" & "|" & Medication & "|" & Route & "|" & Site & "|" & Rate & "|" & StartDate & "|" & EndDate
            'Case MedTherapyType.Hydration
            '    Return "Hydration" & "|" & Medication & "|" & Route & "|" & Site & "|" & Rate & "|" & StartDate & "|" & EndDate
            Case MedTherapyType.Injection
                If _startDate.HasValue Then
                    Dim ldate = _startDate.Value.Date
                    'Get the date in the format of MM/DD/YYYY
                    Dim ldateString = ldate.Month & "/" & ldate.Day & "/" & ldate.Year
                    'Get the time in the format of HH:MM:SS
                    Dim ltimeString = _startDate.Value.TimeOfDay.Hours & ":" & _startDate.Value.TimeOfDay.Minutes & ":" & _startDate.Value.TimeOfDay.Seconds
                    Dim returnString = Route & "|" & "|" & "|" & "|" & ldateString & "|" & ltimeString & "|" & Medication
                    Return returnString
                End If
            Case MedTherapyType.Immunization
                Return Medication & "|" & "|" & _startDate
            Case Else
                Return "nothing"

        End Select

        Return Nothing
        'MedicationImmunizationsImmunizations01 & "|" & MedicationImmunizationsResponse01 & "|" & MedicationImmunizationsDate01
        'MedicationInjectionsInjections01 & "|" & MedicationInjectionsRepeat01 & "|" & MedicationInjectionsResponse01 & "|" & MedicationInjectionsGR3001 & "|" & MedicationInjectionsStartDate01 & "|" & MedicationInjectionsStartTime01 & "|" & MedicationInjectionsMedication01
        Return ""
    End Function
End Class
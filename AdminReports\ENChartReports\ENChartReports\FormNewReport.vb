Imports EnchartDOLib

Public Class FormNewReport

    Dim parent_form As ReportStartup
    Dim is_edit As Boolean = False

    Public Property Report As DOFacilityReport

    Public Event ReportApplyChanges(Byval report As DOFacilityReport)

    Public Sub New(Byval report As DOFacilityReport)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        Me.Report = report
        txtReportName.Text = Me.Report.ReportDescription
        txtReportFile.Text = Me.Report.ReportFilename
        chkIsExport.Checked = Me.Report?.ReportType.IsExportReport
        chkIsPhysician.Checked = Me.Report?.ReportType.IsPhysicianReport
        chkIsObservation.Checked = Me.Report?.ReportType.IsObservationReport
        chkIsInfusion.Checked = Me.Report?.ReportType.IsInfusionReport

        If String.IsNullOrEmpty(Me.Report.ReportDescription) Then
            btnAddReport.Text = "Add Report"
            Me.Text = "Add a Report"
        Else
            btnAddReport.Text = "Edit Report"
            Me.Text = "Edit a Report"
        End If
        
        txtReportName.Focus()
    End Sub

    Private Sub OnReportApplyChanges(Byval report as DOFacilityReport) 
        RaiseEvent ReportApplyChanges(report)
    End Sub

    'Public Overloads Function ShowDialog(ByVal pf As ReportStartup) As DialogResult
    '    parent_form = pf
    '    txtReportName.Focus()

    '    is_edit = False
    '    btnAddReport.Text = "Add Report"
    '    Me.Text = "Add a Report"

    '    Return MyBase.ShowDialog()
    'End Function

    'Public Overloads Function ShowDialog(ByVal pf As ReportStartup, ByVal report_name As String, ByVal report_file As String, ByVal is_export As Boolean, ByVal is_physician As Boolean, ByVal is_observation As Boolean, ByVal is_infusion As Boolean) As DialogResult
    '    parent_form = pf

    '    txtReportName.Text = report_name
    '    txtReportName.Focus()
    '    txtReportFile.Text = report_file
    '    chkIsExport.Checked = is_export
    '    chkIsPhysician.Checked = is_physician
    '    chkIsObservation.Checked = is_observation
    '    chkIsInfusion.Checked = is_infusion

    '    is_edit = True
    '    btnAddReport.Text = "Edit Report"
    '    Me.Text = "Edit a Report"

    '    Return MyBase.ShowDialog()
    'End Function

    Private Sub btnAddReport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddReport.Click
        ''fileSelectFile.InitialDirectory = Application.StartupPath

        'If is_edit Then
        '    parent_form.EditItem(txtReportName.Text, False, txtReportFile.Text, chkIsExport.Checked, chkIsPhysician.Checked, chkIsObservation.Checked, chkIsInfusion.Checked)
        'Else
        '    parent_form.AddItem(txtReportName.Text, False, txtReportFile.Text, chkIsExport.Checked, chkIsPhysician.Checked, chkIsObservation.Checked, chkIsInfusion.Checked)
        'End If

        'txtReportName.Text = ""
        'txtReportFile.Text = ""
        'chkIsExport.Checked = False
        'chkIsPhysician.Checked = False

        'DialogResult = System.Windows.Forms.DialogResult.OK

        Me.Report.ReportDescription = txtReportName.Text
        Me.Report.ReportFilename = txtReportFile.Text
        Me.Report.ReportType.IsExportReport = chkIsExport.Checked 
        Me.Report.ReportType.IsPhysicianReport = chkIsPhysician.Checked 
        Me.Report.ReportType.IsObservationReport = chkIsObservation.Checked 
        Me.Report.ReportType.IsInfusionReport = chkIsInfusion.Checked
        OnReportApplyChanges(Me.Report)
        DialogResult = System.Windows.Forms.DialogResult.OK
        Me.Close
    End Sub

    Private Sub txtReportName_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtReportName.TextChanged
        btnAddReport.Enabled = ((txtReportName.Text <> "") And (txtReportFile.Text <> ""))
    End Sub

    Private Sub txtReportFile_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtReportFile.TextChanged
        btnAddReport.Enabled = ((txtReportName.Text <> "") And (txtReportFile.Text <> ""))
    End Sub

    Private Sub btnBrowse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBrowse.Click
        fileSelectFile.InitialDirectory = Application.StartupPath & "\reports\"
        fileSelectFile.ShowDialog()

        'If fileSelectFile.ShowDialog() = Windows.Forms.DialogResult.OK Then
        '    Dim file_name As String = fileSelectFile.FileName
        '    txtReportFile.Text = file_name.Substring(file_name.LastIndexOf("\") + 1)
        'End If
    End Sub

    Private Sub txtReportFile_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtReportFile.Click
        fileSelectFile.InitialDirectory = Application.StartupPath & "\reports\"
        fileSelectFile.ShowDialog()

        'If fileSelectFile.ShowDialog() = Windows.Forms.DialogResult.OK Then
        '    Dim file_name As String = fileSelectFile.FileName
        '    txtReportFile.Text = file_name.Substring(file_name.LastIndexOf("\") + 1)
        'End If

        btnBrowse.Focus()
    End Sub

    Private Sub FormNewReport_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Me.Dispose()
    End Sub

    Private Sub fileSelectFile_FileOk(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles fileSelectFile.FileOk
        Dim file_name As String = fileSelectFile.FileName
        txtReportFile.Text = file_name.Substring(file_name.LastIndexOf("\") + 1)
    End Sub
End Class
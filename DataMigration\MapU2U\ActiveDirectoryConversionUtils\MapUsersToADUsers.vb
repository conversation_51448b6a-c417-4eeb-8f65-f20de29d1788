﻿Imports System.IO
Imports EnchartDOLib
Imports DevExpress.Xpo
Imports System.Security.Cryptography
Imports System.Text

Module MapUsersToADUsers
    Enum FieldsEnum As Integer
        OldID = 0
        NewID
        Domain
    End Enum

    Sub Main(ByVal args As String())
        Try

            If args.Length <> 2 Then
                Console.WriteLine("Utility to update or restore DOUser UserID and Passwords ")
                Console.WriteLine("")
                Console.WriteLine("MapU2U password [-u | -r]")
                Console.WriteLine("")
                Console.WriteLine("Usage: -u    update users from MapU2U.txt file")
                Console.WriteLine("Usage: -r    restore old userIDs and passwords")
                Console.WriteLine("")
                Console.WriteLine("Format of MapU2U.txt file must be a comma delimited file in the format of :  ")
                Console.WriteLine("OldUserID, NewUserID, Domain")
                Console.WriteLine("")
                Console.WriteLine("NOTE the -u option will first back up all DOUser records that don't already")
                Console.WriteLine("exist in the backup table (DOUserBackUpTemp).")
                Console.WriteLine("")
                Exit Sub
            End If

            'check password =  'espnc51'
            If Hash(args(0), "ADMIN") <> "PH+oC5SsDOazHTs2FxbxPQ==" Then '"ubFPVgcv5QLY/AB3HBgx7g==" Then
                Console.WriteLine("Invalid password")
                Console.WriteLine("")
                Exit Sub
            End If

            If args(1) <> "-u" And args(1) <> "-r" Then
                Console.WriteLine("Invalid option")
                Console.WriteLine("")
                Exit Sub
            End If

            Utils.ConnectToECDataBase()
            Session.DefaultSession.UpdateSchema()

            If args(1) = "-r" Then
                Console.WriteLine("Are you sure you want to restore UserIDs and Passwords? (y/n)")
                Dim answer = Console.ReadKey()
                If answer.KeyChar.ToString <> "y" Then
                    Exit Sub
                End If
                Restore()
                Exit Sub
                Console.WriteLine("Nope.. not gonna happen")
            End If

            '---- OK We got here... so we're gonna backup and then update DOUser records from a file.

            If BackUpAllUsers() = False Then
                Exit Sub
            End If

            Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)

            Dim ffile As New FileInfo("MapU2U.txt")
            If Not ffile.Exists Then
                Console.WriteLine("Can't find MapAdUsers.txt file")
                Console.WriteLine("Press any key to continue ...")
                Console.ReadKey()
                Exit Sub
            End If

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)
            Dim User As DOUser = Nothing

            For Each Line As String In Lines
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                Dim lUserID As String = fields(FieldsEnum.OldID)
                User = DOUser.GetUserByID(lUserID)
                If User.Oid = -1 Then
                    Console.WriteLine("{0,-30 } Error - doesn't exist", User.UserID)
                    Continue For
                End If

                User.UserID = Trim(fields(FieldsEnum.NewID))
                User.DomainName = Trim(fields(FieldsEnum.Domain))
                User.Password = String.Empty
                User.Save()
                Console.WriteLine("{0, -30} Updated", lUserID)
            Next

            Console.WriteLine("")
            Console.WriteLine("Update complete ----------------------------------------")
        Catch ex As Exception
            Console.WriteLine(ex.Message)

        Finally
            Console.WriteLine("Press any key to continue ...")
            Console.ReadKey()
        End Try

    End Sub

    Function BackUpAllUsers() As Boolean

        For Each user As DOUser In DOUser.GetAllUsersAsList
            Dim crypt As New Simple3Des(user.UserID)

            Dim userMapping As DOUserBackUpTemp = DOUserBackUpTemp.GetUserMappingByUser(user)
            If userMapping.Oid > 0 Then
                Continue For
            End If
            Try
                userMapping.OldUserID = user.UserID
                userMapping.HashedData1 = crypt.EncryptData(user.Password)
                userMapping.HashedData2 = user.HashedPassword
                userMapping.Save()

                user.Password = String.Empty
                user.Save()
                Console.WriteLine("{0, -30} backed up", user.UserID)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
 Next
        Console.WriteLine("")
        Console.WriteLine("Backup complete ----------------------------------------")
        Console.WriteLine("")
        Return True
    End Function

    Function Restore() As Boolean
        Console.WriteLine("")
        Console.WriteLine("")
        For Each user As DOUser In DOUser.GetAllUsersAsList
            Try
                Dim userMapping As DOUserBackUpTemp = DOUserBackUpTemp.GetUserMappingByUser(user)
                If userMapping.Oid < 0 Then
                    Continue For
                End If

                Dim crypt As New Simple3Des(userMapping.OldUserID)

                user.UserID = userMapping.OldUserID
                user.Password = crypt.DecryptData(userMapping.HashedData1)
                user.HashedPassword = userMapping.HashedData2
                user.Save()
            Catch ex As Exception
                Console.Write(user.UserID & ": ")
                Console.WriteLine(ex.Message)
            End Try
        Next
        'Console.WriteLine("DOUser UserIDs and Passwords have been restored.")
        Console.WriteLine("")
        Console.WriteLine("Restore complete ---------------------------------------")
        Console.WriteLine("")
        Return False
    End Function

    Public Function Hash(ByVal to_hash As String, ByVal salt As String) As String
        Dim md5Hasher As New MD5CryptoServiceProvider()

        Dim hashedBytes As Byte()
        Dim encoder As New UTF8Encoding()

        hashedBytes = md5Hasher.ComputeHash(encoder.GetBytes(to_hash & salt))

        Return Convert.ToBase64String(hashedBytes)
    End Function
End Module

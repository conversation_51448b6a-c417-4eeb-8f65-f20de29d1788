﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class GlobalSettingsForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.XpCollection1 = New DevExpress.Xpo.XPCollection()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colOid = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSettingName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSettingValue = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.ceUseEnhancedPassword = New DevExpress.XtraEditors.CheckEdit()
        Me.teDefaultDomain = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ceUseActiveDirectory = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.ceUse2018CodingReport = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018ESPCodeLogic = New DevExpress.XtraEditors.CheckEdit()
        Me.seAutoLogOffTimeOutMins = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.teMMMsg = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.ceEnableMaintenanceMode = New DevExpress.XtraEditors.CheckEdit()
        Me.ceUse2018ChargeAllocationLogic = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.ceUseEnhancedPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teDefaultDomain.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUseActiveDirectory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.seAutoLogOffTimeOutMins.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teMMMsg.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceEnableMaintenanceMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'XpCollection1
        '
        Me.XpCollection1.ObjectType = GetType(EnchartDOLib.DOGlobalSetting)
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.XpCollection1
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4)
        Me.GridControl1.Location = New System.Drawing.Point(16, 15)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(793, 544)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.UseEmbeddedNavigator = True
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colOid, Me.colSettingName, Me.colSettingValue})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.NewItemRowText = "Add new row"
        Me.GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colSettingName, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'colOid
        '
        Me.colOid.Caption = "Oid"
        Me.colOid.FieldName = "Oid"
        Me.colOid.Name = "colOid"
        Me.colOid.Width = 78
        '
        'colSettingName
        '
        Me.colSettingName.Caption = "SettingName"
        Me.colSettingName.FieldName = "SettingName"
        Me.colSettingName.Name = "colSettingName"
        Me.colSettingName.Visible = True
        Me.colSettingName.VisibleIndex = 0
        Me.colSettingName.Width = 290
        '
        'colSettingValue
        '
        Me.colSettingValue.Caption = "SettingValue"
        Me.colSettingValue.FieldName = "SettingValue"
        Me.colSettingValue.Name = "colSettingValue"
        Me.colSettingValue.Visible = True
        Me.colSettingValue.VisibleIndex = 1
        Me.colSettingValue.Width = 297
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Location = New System.Drawing.Point(355, 724)
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(512, 28)
        Me.SimpleButton1.TabIndex = 1
        Me.SimpleButton1.Text = "OK"
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.ceUseEnhancedPassword)
        Me.GroupControl1.Controls.Add(Me.teDefaultDomain)
        Me.GroupControl1.Controls.Add(Me.LabelControl1)
        Me.GroupControl1.Controls.Add(Me.ceUseActiveDirectory)
        Me.GroupControl1.Location = New System.Drawing.Point(852, 15)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(353, 181)
        Me.GroupControl1.TabIndex = 2
        Me.GroupControl1.Text = "Log In / Authentication"
        '
        'ceUseEnhancedPassword
        '
        Me.ceUseEnhancedPassword.Enabled = False
        Me.ceUseEnhancedPassword.Location = New System.Drawing.Point(21, 143)
        Me.ceUseEnhancedPassword.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUseEnhancedPassword.Name = "ceUseEnhancedPassword"
        Me.ceUseEnhancedPassword.Properties.Caption = "Use Enhanced Passwords"
        Me.ceUseEnhancedPassword.Size = New System.Drawing.Size(311, 20)
        Me.ceUseEnhancedPassword.TabIndex = 3
        '
        'teDefaultDomain
        '
        Me.teDefaultDomain.Location = New System.Drawing.Point(24, 94)
        Me.teDefaultDomain.Margin = New System.Windows.Forms.Padding(4)
        Me.teDefaultDomain.Name = "teDefaultDomain"
        Me.teDefaultDomain.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.teDefaultDomain.Properties.MaxLength = 64
        Me.teDefaultDomain.Size = New System.Drawing.Size(308, 22)
        Me.teDefaultDomain.TabIndex = 2
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(24, 70)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(182, 16)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "Default Domain Name For LogIn"
        '
        'ceUseActiveDirectory
        '
        Me.ceUseActiveDirectory.Location = New System.Drawing.Point(21, 38)
        Me.ceUseActiveDirectory.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUseActiveDirectory.Name = "ceUseActiveDirectory"
        Me.ceUseActiveDirectory.Properties.Caption = "Use Active Directory For Authentication"
        Me.ceUseActiveDirectory.Size = New System.Drawing.Size(311, 20)
        Me.ceUseActiveDirectory.TabIndex = 0
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.ceUse2018ChargeAllocationLogic)
        Me.GroupControl2.Controls.Add(Me.ceUse2018CodingReport)
        Me.GroupControl2.Controls.Add(Me.ceUse2018ESPCodeLogic)
        Me.GroupControl2.Controls.Add(Me.seAutoLogOffTimeOutMins)
        Me.GroupControl2.Controls.Add(Me.LabelControl4)
        Me.GroupControl2.Controls.Add(Me.LabelControl3)
        Me.GroupControl2.Location = New System.Drawing.Point(852, 203)
        Me.GroupControl2.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(357, 356)
        Me.GroupControl2.TabIndex = 4
        Me.GroupControl2.Text = "Misc."
        '
        'ceUse2018CodingReport
        '
        Me.ceUse2018CodingReport.Location = New System.Drawing.Point(7, 109)
        Me.ceUse2018CodingReport.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018CodingReport.Name = "ceUse2018CodingReport"
        Me.ceUse2018CodingReport.Properties.Caption = "Use 2018 Coding Report Logic"
        Me.ceUse2018CodingReport.Size = New System.Drawing.Size(325, 20)
        Me.ceUse2018CodingReport.TabIndex = 12
        '
        'ceUse2018ESPCodeLogic
        '
        Me.ceUse2018ESPCodeLogic.Location = New System.Drawing.Point(7, 81)
        Me.ceUse2018ESPCodeLogic.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018ESPCodeLogic.Name = "ceUse2018ESPCodeLogic"
        Me.ceUse2018ESPCodeLogic.Properties.Caption = "Use 2018 ESPCode Logic"
        Me.ceUse2018ESPCodeLogic.Size = New System.Drawing.Size(325, 20)
        Me.ceUse2018ESPCodeLogic.TabIndex = 11
        '
        'seAutoLogOffTimeOutMins
        '
        Me.seAutoLogOffTimeOutMins.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.seAutoLogOffTimeOutMins.Location = New System.Drawing.Point(197, 37)
        Me.seAutoLogOffTimeOutMins.Margin = New System.Windows.Forms.Padding(4)
        Me.seAutoLogOffTimeOutMins.Name = "seAutoLogOffTimeOutMins"
        Me.seAutoLogOffTimeOutMins.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.seAutoLogOffTimeOutMins.Properties.IsFloatValue = False
        Me.seAutoLogOffTimeOutMins.Properties.Mask.EditMask = "N00"
        Me.seAutoLogOffTimeOutMins.Properties.MaxValue = New Decimal(New Integer() {1440, 0, 0, 0})
        Me.seAutoLogOffTimeOutMins.Size = New System.Drawing.Size(89, 22)
        Me.seAutoLogOffTimeOutMins.TabIndex = 9
        Me.seAutoLogOffTimeOutMins.ToolTip = "Set to 0 to disable"
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(291, 41)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(26, 16)
        Me.LabelControl4.TabIndex = 3
        Me.LabelControl4.Text = "Mins"
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(7, 42)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(124, 16)
        Me.LabelControl3.TabIndex = 1
        Me.LabelControl3.Text = "Auto Log off Timeout "
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(Me.CheckEdit1)
        Me.GroupControl3.Controls.Add(Me.teMMMsg)
        Me.GroupControl3.Controls.Add(Me.LabelControl5)
        Me.GroupControl3.Controls.Add(Me.ceEnableMaintenanceMode)
        Me.GroupControl3.Location = New System.Drawing.Point(16, 566)
        Me.GroupControl3.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(1189, 138)
        Me.GroupControl3.TabIndex = 5
        Me.GroupControl3.Text = "Maintenance Mode"
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Location = New System.Drawing.Point(21, 143)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Caption = "Use Enhanced Passwords"
        Me.CheckEdit1.Size = New System.Drawing.Size(311, 20)
        Me.CheckEdit1.TabIndex = 3
        '
        'teMMMsg
        '
        Me.teMMMsg.Location = New System.Drawing.Point(21, 94)
        Me.teMMMsg.Margin = New System.Windows.Forms.Padding(4)
        Me.teMMMsg.Name = "teMMMsg"
        Me.teMMMsg.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.teMMMsg.Properties.EditValueChangedDelay = 1000
        Me.teMMMsg.Properties.MaxLength = 120
        Me.teMMMsg.Size = New System.Drawing.Size(1147, 22)
        Me.teMMMsg.TabIndex = 2
        '
        'LabelControl5
        '
        Me.LabelControl5.Location = New System.Drawing.Point(24, 70)
        Me.LabelControl5.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(337, 16)
        Me.LabelControl5.TabIndex = 1
        Me.LabelControl5.Text = "Message - (Should include some estimation of down time)."
        '
        'ceEnableMaintenanceMode
        '
        Me.ceEnableMaintenanceMode.Location = New System.Drawing.Point(21, 38)
        Me.ceEnableMaintenanceMode.Margin = New System.Windows.Forms.Padding(4)
        Me.ceEnableMaintenanceMode.Name = "ceEnableMaintenanceMode"
        Me.ceEnableMaintenanceMode.Properties.Caption = "Maintenance Mode"
        Me.ceEnableMaintenanceMode.Size = New System.Drawing.Size(311, 20)
        Me.ceEnableMaintenanceMode.TabIndex = 0
        '
        'ceUse2018ChargeAllocationLogic
        '
        Me.ceUse2018ChargeAllocationLogic.Location = New System.Drawing.Point(6, 137)
        Me.ceUse2018ChargeAllocationLogic.Margin = New System.Windows.Forms.Padding(4)
        Me.ceUse2018ChargeAllocationLogic.Name = "ceUse2018ChargeAllocationLogic"
        Me.ceUse2018ChargeAllocationLogic.Properties.Caption = "Use 2018 Obs. Charge Allocation Logic"
        Me.ceUse2018ChargeAllocationLogic.Size = New System.Drawing.Size(325, 20)
        Me.ceUse2018ChargeAllocationLogic.TabIndex = 13
        '
        'GlobalSettingsForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1221, 767)
        Me.Controls.Add(Me.GroupControl3)
        Me.Controls.Add(Me.GroupControl2)
        Me.Controls.Add(Me.GroupControl1)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.GridControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "GlobalSettingsForm"
        Me.ShowIcon = False
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Global Settings"
        Me.TopMost = True
        CType(Me.XpCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.ceUseEnhancedPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teDefaultDomain.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUseActiveDirectory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.ceUse2018CodingReport.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018ESPCodeLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.seAutoLogOffTimeOutMins.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teMMMsg.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceEnableMaintenanceMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceUse2018ChargeAllocationLogic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents XpCollection1 As DevExpress.Xpo.XPCollection
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colOid As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSettingName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSettingValue As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents teDefaultDomain As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceUseActiveDirectory As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUseEnhancedPassword As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents seAutoLogOffTimeOutMins As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents teMMMsg As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceEnableMaintenanceMode As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ESPCodeLogic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018CodingReport As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceUse2018ChargeAllocationLogic As DevExpress.XtraEditors.CheckEdit
End Class

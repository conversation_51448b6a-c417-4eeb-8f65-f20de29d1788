<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
          <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="EnchartDOLib.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
                                       
      </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="EnchartDOLib.My.MySettings.DefualtConnectionString" connectionString="XpoProvider=Asa;eng=enchartplus;Links=tcpip;persist security info=true"/>
    </connectionStrings>

  <applicationSettings>
    <EnchartDOLib.My.MySettings>
      <setting name="JJCTest" serializeAs="String">
        <value>it failed</value>
      </setting>
      <setting name="umm" serializeAs="String">
        <value>ttt</value>
      </setting>
    </EnchartDOLib.My.MySettings>
  </applicationSettings>
  
  <system.diagnostics>
    <sources>
      <!-- This section defines the logging configuration for My.Application.Log -->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog"/>
          <!-- Uncomment the below section to write to the Application Event Log -->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information"/>
    </switches>
    <sharedListeners>
      <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
  
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup></configuration>

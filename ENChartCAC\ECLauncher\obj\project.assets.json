{"version": 3, "targets": {"net7.0-windows7.0": {"Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {"type": "package", "build": {"build/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.props": {}}}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "compile": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Windows.Compatibility/7.0.3": {"type": "package", "dependencies": {"Microsoft.Win32.Registry.AccessControl": "7.0.0", "Microsoft.Win32.SystemEvents": "7.0.0", "System.CodeDom": "7.0.0", "System.ComponentModel.Composition": "7.0.0", "System.ComponentModel.Composition.Registration": "7.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.Odbc": "7.0.0", "System.Data.OleDb": "7.0.0", "System.Data.SqlClient": "4.8.5", "System.Diagnostics.EventLog": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0", "System.DirectoryServices": "7.0.1", "System.DirectoryServices.AccountManagement": "7.0.0", "System.DirectoryServices.Protocols": "7.0.1", "System.Drawing.Common": "7.0.0", "System.IO.Packaging": "7.0.0", "System.IO.Ports": "7.0.0", "System.Management": "7.0.2", "System.Reflection.Context": "7.0.0", "System.Runtime.Caching": "7.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.ProtectedData": "7.0.1", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Permissions": "7.0.0", "System.ServiceModel.Duplex": "4.9.0", "System.ServiceModel.Http": "4.9.0", "System.ServiceModel.NetTcp": "4.9.0", "System.ServiceModel.Primitives": "4.9.0", "System.ServiceModel.Security": "4.9.0", "System.ServiceModel.Syndication": "7.0.0", "System.ServiceProcess.ServiceController": "7.0.1", "System.Speech": "7.0.0", "System.Text.Encoding.CodePages": "7.0.0", "System.Threading.AccessControl": "7.0.1", "System.Web.Services.Description": "4.9.0"}, "build": {"buildTransitive/net6.0/_._": {}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ComponentModel.Composition/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ComponentModel.Composition.Registration/7.0.0": {"type": "package", "dependencies": {"System.ComponentModel.Composition": "7.0.0", "System.Reflection.Context": "7.0.0"}, "compile": {"lib/net7.0/System.ComponentModel.Composition.Registration.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ComponentModel.Composition.Registration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.Odbc/7.0.0": {"type": "package", "dependencies": {"System.Text.Encoding.CodePages": "7.0.0"}, "compile": {"lib/net7.0/System.Data.Odbc.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Data.Odbc.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "freebsd"}, "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "illumos"}, "runtimes/ios/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "ios"}, "runtimes/linux/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "solaris"}, "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "tvos"}, "runtimes/win/lib/net7.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.OleDb/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.PerformanceCounter": "7.0.0"}, "compile": {"lib/net7.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SqlClient/4.8.5": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "compile": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices/7.0.1": {"type": "package", "dependencies": {"System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.AccountManagement/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.DirectoryServices": "7.0.0", "System.DirectoryServices.Protocols": "7.0.0"}, "compile": {"lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.Protocols/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/7.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "compile": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/8.0.1": {"type": "package", "compile": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Packaging/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "compile": {"lib/net7.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net7.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net7.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management/7.0.2": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Private.ServiceModel/4.9.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection.Context/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Reflection.Context.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Reflection.Context.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "compile": {"ref/netstandard2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll": {"related": ".xml"}}}, "System.Runtime.Caching/7.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "compile": {"lib/net7.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/7.0.2": {"type": "package", "dependencies": {"System.Formats.Asn1": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/7.0.1": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Permissions/7.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Duplex/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Duplex.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"related": ".pdb"}}}, "System.ServiceModel.Http/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Http.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}}, "System.ServiceModel.NetTcp/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.NetTcp.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}}, "System.ServiceModel.Primitives/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Primitives.dll": {}, "ref/net6.0/System.ServiceModel.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}, "lib/net6.0/System.ServiceModel.dll": {"related": ".Primitives.pdb"}}}, "System.ServiceModel.Security/4.9.0": {"type": "package", "dependencies": {"System.Private.ServiceModel": "4.9.0", "System.ServiceModel.Primitives": "4.9.0"}, "compile": {"ref/net6.0/System.ServiceModel.Security.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"related": ".pdb"}}}, "System.ServiceModel.Syndication/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ServiceProcess.ServiceController/7.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0"}, "compile": {"lib/net7.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Speech/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Speech.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Speech.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Speech.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.AccessControl/7.0.1": {"type": "package", "compile": {"lib/net7.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Threading.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Web.Services.Description/4.9.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/7.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "7.0.0"}, "compile": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Microsoft.Bcl.AsyncInterfaces/5.0.0": {"sha512": "W8DPQjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers/0.4.410601": {"sha512": "ZFc3Pqiox/AcJECaYHXZtdB8XF12pJKvlrIxavL01z7VpW8Oy1d4tAgcnZfgburH5eVtAH4Bi2QKTHnN8va0ug==", "type": "package", "path": "microsoft.dotnet.upgradeassistant.extensions.default.analyzers/0.4.410601", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.Common.dll", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.CodeFixes.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.Common.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.CodeFixes.dll", "build/Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers.props", "build/TypeMaps.props", "build/WebTypeReplacements.typemap", "icon.png", "microsoft.dotnet.upgradeassistant.extensions.default.analyzers.0.4.410601.nupkg.sha512", "microsoft.dotnet.upgradeassistant.extensions.default.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.Extensions.ObjectPool/5.0.10": {"sha512": "pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "type": "package", "path": "microsoft.extensions.objectpool/5.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net5.0/Microsoft.Extensions.ObjectPool.dll", "lib/net5.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.5.0.10.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry.AccessControl/7.0.0": {"sha512": "JwM65WXVca58WzqY/Rpz7FGyHbN/SMdyr/3EI2CwPIYkB55EIRJUdPQJwO64x3ntOwPQoqCATKuDYA9K7Np5Ww==", "type": "package", "path": "microsoft.win32.registry.accesscontrol/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.Registry.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.Registry.AccessControl.targets", "lib/net462/Microsoft.Win32.Registry.AccessControl.dll", "lib/net462/Microsoft.Win32.Registry.AccessControl.xml", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.xml", "microsoft.win32.registry.accesscontrol.7.0.0.nupkg.sha512", "microsoft.win32.registry.accesscontrol.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Win32.SystemEvents/7.0.0": {"sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "type": "package", "path": "microsoft.win32.systemevents/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.7.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Windows.Compatibility/7.0.3": {"sha512": "vVTf/gmV6tLpi8IvmIwhD/86xWcCPazbrGRoxjXe5LQ0HqcmLgKOTYzvE058kRtVTZgYuQFFrErrXMFG4weCeQ==", "type": "package", "path": "microsoft.windows.compatibility/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Windows.Compatibility.targets", "microsoft.windows.compatibility.7.0.3.nupkg.sha512", "microsoft.windows.compatibility.nuspec", "useSharedDesignerContext.txt"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"sha512": "CBvgRaF+M0xGLDv2Geb/0v0LEADheH8aK72GRAUJdnqnJVsQO60ki1XO8M3keEhnjm+T5NvLm41pNXAVYAPiSg==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "5VCyRCtCIYU8FR/W8oo7ouFuJ8tmAg9ddsuXhfCKZfZrbaVZSKxkmNBa6fxkfYPueD0jQfOvwFBmE5c6zalCSw==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "DV9dWDUs23OoZqMWl5IhLr3D+b9koDiSHQxFKdYgWnQbnthv8c/yDjrlrI8nMrDc71RAKCO8jlUojzuPMX04gg==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.native.System.IO.Ports/7.0.0": {"sha512": "L4Ivegqc3B0Fee7VifFy2JST9nndm+uvJ0viLIZUaImDfnr+JmRin9Tbqd56KuMtm0eVxHpNOWZBPtKrA/1h5Q==", "type": "package", "path": "runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "jFwh4sKSXZ7al5XrItEO4GdGWa6XNxvNx+LhEHjrSzOwawO1znwJ+Dy+VjnrkySX9Qi4bnHNLoiqOXbqMuka4g==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "X4LrHEfke/z9+z+iuVr35NlkhdZldY8JGNMYUN+sfPK/U/6TcE+vP44I0Yv0ir1v0bqIzq3v6Qdv1c1vmp8s4g==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Composition/7.0.0": {"sha512": "orv0h38ZVPCPo/FW0LGv8/TigXwX8cIwXeQcaNYhikkqELDm8sUFLMcof/Sjcq5EvYCm5NA7MV3hG4u75H44UQ==", "type": "package", "path": "system.componentmodel.composition/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.targets", "lib/net462/_._", "lib/net6.0/System.ComponentModel.Composition.dll", "lib/net6.0/System.ComponentModel.Composition.xml", "lib/net7.0/System.ComponentModel.Composition.dll", "lib/net7.0/System.ComponentModel.Composition.xml", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.xml", "system.componentmodel.composition.7.0.0.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Composition.Registration/7.0.0": {"sha512": "yy/xYOznnc7Hfg2/LeVqAMlJGv1v7b1ILxFShzx5PWUv53PwU0MaKPG8Dh9DC3gxayzw44UVuQJImhw7LtMKlw==", "type": "package", "path": "system.componentmodel.composition.registration/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.Registration.targets", "lib/net462/_._", "lib/net6.0/System.ComponentModel.Composition.Registration.dll", "lib/net6.0/System.ComponentModel.Composition.Registration.xml", "lib/net7.0/System.ComponentModel.Composition.Registration.dll", "lib/net7.0/System.ComponentModel.Composition.Registration.xml", "lib/netstandard2.1/System.ComponentModel.Composition.Registration.dll", "lib/netstandard2.1/System.ComponentModel.Composition.Registration.xml", "system.componentmodel.composition.registration.7.0.0.nupkg.sha512", "system.componentmodel.composition.registration.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/7.0.0": {"sha512": "WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "type": "package", "path": "system.configuration.configurationmanager/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.7.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.Odbc/7.0.0": {"sha512": "siwu7NoCsfHa9bfw2a2wSeTt2c/rhk3X8I28nJln1dlxdW3KqhRp0aW87yH1XkCo9h8zO1qcIfdTHO7YvvWLEA==", "type": "package", "path": "system.data.odbc/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.Odbc.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.Odbc.targets", "lib/net462/System.Data.Odbc.dll", "lib/net462/System.Data.Odbc.xml", "lib/net6.0/System.Data.Odbc.dll", "lib/net6.0/System.Data.Odbc.xml", "lib/net7.0/System.Data.Odbc.dll", "lib/net7.0/System.Data.Odbc.xml", "lib/netstandard2.0/System.Data.Odbc.dll", "lib/netstandard2.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.xml", "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll", "runtimes/illumos/lib/net7.0/System.Data.Odbc.xml", "runtimes/ios/lib/net7.0/System.Data.Odbc.dll", "runtimes/ios/lib/net7.0/System.Data.Odbc.xml", "runtimes/linux/lib/net6.0/System.Data.Odbc.dll", "runtimes/linux/lib/net6.0/System.Data.Odbc.xml", "runtimes/linux/lib/net7.0/System.Data.Odbc.dll", "runtimes/linux/lib/net7.0/System.Data.Odbc.xml", "runtimes/osx/lib/net6.0/System.Data.Odbc.dll", "runtimes/osx/lib/net6.0/System.Data.Odbc.xml", "runtimes/osx/lib/net7.0/System.Data.Odbc.dll", "runtimes/osx/lib/net7.0/System.Data.Odbc.xml", "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll", "runtimes/solaris/lib/net7.0/System.Data.Odbc.xml", "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll", "runtimes/tvos/lib/net7.0/System.Data.Odbc.xml", "runtimes/win/lib/net6.0/System.Data.Odbc.dll", "runtimes/win/lib/net6.0/System.Data.Odbc.xml", "runtimes/win/lib/net7.0/System.Data.Odbc.dll", "runtimes/win/lib/net7.0/System.Data.Odbc.xml", "system.data.odbc.7.0.0.nupkg.sha512", "system.data.odbc.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/7.0.0": {"sha512": "bhAs+5X5acgg3zQ6N4HqxqfwwmqWJzgt54BC8iwygcqa2jktxDFzxwN83GNvqgoTcTs2tenDS/jmhC+AQsmcyg==", "type": "package", "path": "system.data.oledb/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "system.data.oledb.7.0.0.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.5": {"sha512": "fRqxut4lrndPHrXD+ht1XRmCL3obuKldm4XjCRYS9p5f7FSR7shBxAwTkDrpFMsHC9BhNgjjmUtiIjvehn5zkg==", "type": "package", "path": "system.data.sqlclient/4.8.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.5.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.EventLog/7.0.0": {"sha512": "eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "type": "package", "path": "system.diagnostics.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.7.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/7.0.0": {"sha512": "L+zIMEaXp1vA4wZk1KLMpk6tvU0xy94R0IfmhkmTWeC4KwShsmAfbg5I19LgjsCTYp6GVdXZ2aHluVWL0QqBdA==", "type": "package", "path": "system.diagnostics.performancecounter/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.7.0.0.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/7.0.1": {"sha512": "Z4FVdUJEVXbf7/f/hU6cFZDtxN5ozUVKJMzXoHmC+GCeTcqzlxqmWtxurejxG3K+kZ6H0UKwNshoK1CYnmJ1sg==", "type": "package", "path": "system.directoryservices/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.dll", "lib/net6.0/System.DirectoryServices.xml", "lib/net7.0/System.DirectoryServices.dll", "lib/net7.0/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.xml", "system.directoryservices.7.0.1.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.AccountManagement/7.0.0": {"sha512": "qMpVgR5+XactuWzpqsiif++lnTzfDESbQv4UYFZpgdRvFCFIi4JgufOITCDlu+x2vEmwYOVbwrR1N365dDJRLg==", "type": "package", "path": "system.directoryservices.accountmanagement/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.AccountManagement.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.AccountManagement.dll", "lib/net6.0/System.DirectoryServices.AccountManagement.xml", "lib/net7.0/System.DirectoryServices.AccountManagement.dll", "lib/net7.0/System.DirectoryServices.AccountManagement.xml", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.AccountManagement.xml", "system.directoryservices.accountmanagement.7.0.0.nupkg.sha512", "system.directoryservices.accountmanagement.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/7.0.1": {"sha512": "t9hsL+UYRzNs30pnT2Tdx6ngX8McFUjru0a0ekNgu/YXfkXN+dx5OvSEv0/p7H2q3pdJLH7TJPWX7e55J8QB9A==", "type": "package", "path": "system.directoryservices.protocols/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.Protocols.dll", "lib/net6.0/System.DirectoryServices.Protocols.xml", "lib/net7.0/System.DirectoryServices.Protocols.dll", "lib/net7.0/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.7.0.1.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/7.0.0": {"sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "type": "package", "path": "system.drawing.common/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/net7.0/System.Drawing.Common.dll", "runtimes/win/lib/net7.0/System.Drawing.Common.xml", "system.drawing.common.7.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/8.0.1": {"sha512": "XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "type": "package", "path": "system.formats.asn1/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.8.0.1.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Packaging/7.0.0": {"sha512": "+j5ezLP7785/pd4taKQhXAWsymsIW2nTnE/U3/jpGZzcJx5lip6qkj6UrxSE7ZYZfL0GaLuymwGLqwJV/c7O7Q==", "type": "package", "path": "system.io.packaging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net6.0/System.IO.Packaging.dll", "lib/net6.0/System.IO.Packaging.xml", "lib/net7.0/System.IO.Packaging.dll", "lib/net7.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.7.0.0.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/7.0.0": {"sha512": "0nWQjM5IofaIGpvkifN+LLuYwBG6BHlpmphLhhOJepcW12G8qToGuNDRgBzeTVBZzp33wVsESSZ8hUOCfq+8QA==", "type": "package", "path": "system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net6.0/System.IO.Ports.dll", "lib/net6.0/System.IO.Ports.xml", "lib/net7.0/System.IO.Ports.dll", "lib/net7.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net6.0/System.IO.Ports.dll", "runtimes/unix/lib/net6.0/System.IO.Ports.xml", "runtimes/unix/lib/net7.0/System.IO.Ports.dll", "runtimes/unix/lib/net7.0/System.IO.Ports.xml", "runtimes/win/lib/net6.0/System.IO.Ports.dll", "runtimes/win/lib/net6.0/System.IO.Ports.xml", "runtimes/win/lib/net7.0/System.IO.Ports.dll", "runtimes/win/lib/net7.0/System.IO.Ports.xml", "system.io.ports.7.0.0.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.2": {"sha512": "/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "type": "package", "path": "system.management/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Private.ServiceModel/4.9.0": {"sha512": "d3RjkrtpjUQ63PzFmm/SZ4aOXeJNP+8YW5QeP0lCJy8iX4xlHdlNLWTF9sRn9SmrFTK757kQXT9Op/R4l858uw==", "type": "package", "path": "system.private.servicemodel/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/System.Private.ServiceModel.dll", "lib/netstandard2.0/System.Private.ServiceModel.pdb", "lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll", "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll", "ref/netstandard2.0/_._", "system.private.servicemodel.4.9.0.nupkg.sha512", "system.private.servicemodel.nuspec"]}, "System.Reflection.Context/7.0.0": {"sha512": "rVf4vEyGQphXTITF39uXlgTcp8Ekcu2aNwxyVLU7fDyNOk0W+/PPpj9PoC2cFL4wgJZJltiss5eQptE2C4f1Sw==", "type": "package", "path": "system.reflection.context/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Context.targets", "lib/net462/_._", "lib/net6.0/System.Reflection.Context.dll", "lib/net6.0/System.Reflection.Context.xml", "lib/net7.0/System.Reflection.Context.dll", "lib/net7.0/System.Reflection.Context.xml", "lib/netstandard2.0/System.Reflection.Context.dll", "lib/netstandard2.0/System.Reflection.Context.xml", "lib/netstandard2.1/System.Reflection.Context.dll", "lib/netstandard2.1/System.Reflection.Context.xml", "system.reflection.context.7.0.0.nupkg.sha512", "system.reflection.context.nuspec", "useSharedDesignerContext.txt"]}, "System.Reflection.DispatchProxy/4.7.1": {"sha512": "C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "type": "package", "path": "system.reflection.dispatchproxy/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Reflection.DispatchProxy.dll", "lib/net461/System.Reflection.DispatchProxy.xml", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.dll", "lib/netcoreapp2.0/System.Reflection.DispatchProxy.xml", "lib/netstandard1.3/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.dll", "lib/netstandard2.0/System.Reflection.DispatchProxy.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Reflection.DispatchProxy.dll", "ref/net461/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/System.Reflection.DispatchProxy.dll", "ref/netstandard1.3/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/de/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/es/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/fr/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/it/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ja/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ko/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ru/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hans/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hant/System.Reflection.DispatchProxy.xml", "ref/netstandard2.0/System.Reflection.DispatchProxy.dll", "ref/netstandard2.0/System.Reflection.DispatchProxy.xml", "ref/uap10.0.16299/System.Reflection.DispatchProxy.dll", "ref/uap10.0.16299/System.Reflection.DispatchProxy.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.DispatchProxy.dll", "runtimes/win-aot/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "runtimes/win/lib/uap10.0.16299/System.Reflection.DispatchProxy.dll", "system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "system.reflection.dispatchproxy.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Caching/7.0.0": {"sha512": "M0riW7Zgxca3Elp1iZVhzH7PWWT5bPSrdMFGCAGoH1n9YLuXOYE78ryui051Icf3swWWa8feBRoSxOCYwgMy8w==", "type": "package", "path": "system.runtime.caching/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/net7.0/System.Runtime.Caching.dll", "lib/net7.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/net7.0/System.Runtime.Caching.dll", "runtimes/win/lib/net7.0/System.Runtime.Caching.xml", "system.runtime.caching.7.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/7.0.2": {"sha512": "xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==", "type": "package", "path": "system.security.cryptography.pkcs/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.7.0.2.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/7.0.1": {"sha512": "3evI3sBfKqwYSwuBcYgShbmEgtXcg8N5Qu+jExLdkBXPty2yGDXq5m1/4sx9Exb8dqdeMPUs/d9DQ0wy/9Adwg==", "type": "package", "path": "system.security.cryptography.protecteddata/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.7.0.1.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/7.0.1": {"sha512": "MCxBCtH0GrDuvU63ZODwQHQZPchb24pUAX3MfZ6b13qg246ZD10PRdOvay8C9HBPfCXkymUNwFPEegud7ax2zg==", "type": "package", "path": "system.security.cryptography.xml/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.7.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/7.0.0": {"sha512": "Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "type": "package", "path": "system.security.permissions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.7.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Duplex/4.9.0": {"sha512": "Yb8MFiJxBBtm2JnfS/5SxYzm2HqkEmHu5xeaVIHXy83sNpty9wc30JifH2xgda821D6nr1UctbwbdZqN4LBUKQ==", "type": "package", "path": "system.servicemodel.duplex/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Duplex.dll", "lib/net461/System.ServiceModel.Duplex.pdb", "lib/net6.0/System.ServiceModel.Duplex.dll", "lib/net6.0/System.ServiceModel.Duplex.pdb", "lib/netcore50/System.ServiceModel.Duplex.dll", "lib/netstandard1.3/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Duplex.dll", "ref/net6.0/System.ServiceModel.Duplex.dll", "ref/netcore50/System.ServiceModel.Duplex.dll", "ref/netstandard1.1/System.ServiceModel.Duplex.dll", "ref/netstandard2.0/System.ServiceModel.Duplex.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.duplex.4.9.0.nupkg.sha512", "system.servicemodel.duplex.nuspec"]}, "System.ServiceModel.Http/4.9.0": {"sha512": "Z+s3RkLNzJ31fDXAjqXdXp67FqsNG4V3Md3r7FOrzMkHmg61gY8faEfTFPBLxU9tax1HPWt6IHVAquXBKySJaw==", "type": "package", "path": "system.servicemodel.http/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.pdb", "lib/net6.0/System.ServiceModel.Http.dll", "lib/net6.0/System.ServiceModel.Http.pdb", "lib/netcore50/System.ServiceModel.Http.dll", "lib/netstandard1.3/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Http.dll", "ref/net461/System.ServiceModel.Http.dll", "ref/net6.0/System.ServiceModel.Http.dll", "ref/netcore50/System.ServiceModel.Http.dll", "ref/netstandard1.0/System.ServiceModel.Http.dll", "ref/netstandard1.1/System.ServiceModel.Http.dll", "ref/netstandard1.3/System.ServiceModel.Http.dll", "ref/netstandard2.0/System.ServiceModel.Http.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.http.4.9.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetTcp/4.9.0": {"sha512": "nXgnnkrZERUF/KwmoLwZPkc7fqgiq94DXkmUZBvDNh/LdZquDvjy2NbhJLElpApOa5x8zEoQoBZyJ2PqNC39qg==", "type": "package", "path": "system.servicemodel.nettcp/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.pdb", "lib/net6.0/System.ServiceModel.NetTcp.dll", "lib/net6.0/System.ServiceModel.NetTcp.pdb", "lib/netcore50/System.ServiceModel.NetTcp.dll", "lib/netstandard1.3/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.NetTcp.dll", "ref/net461/System.ServiceModel.NetTcp.dll", "ref/net6.0/System.ServiceModel.NetTcp.dll", "ref/netcore50/System.ServiceModel.NetTcp.dll", "ref/netstandard1.1/System.ServiceModel.NetTcp.dll", "ref/netstandard1.3/System.ServiceModel.NetTcp.dll", "ref/netstandard2.0/System.ServiceModel.NetTcp.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.nettcp.4.9.0.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/4.9.0": {"sha512": "LTFPVdS8Nf76xg/wRZkDa+2Q+GnjTOmwkTlwuoetwX37mAfYnGkf7p8ydhpDwVmomNljpUOhUUGxfjQyd5YcOg==", "type": "package", "path": "system.servicemodel.primitives/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.Primitives.dll", "lib/net6.0/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.dll", "lib/netcore50/System.ServiceModel.Primitives.dll", "lib/netcoreapp2.1/System.ServiceModel.Primitives.dll", "lib/netcoreapp2.1/System.ServiceModel.Primitives.pdb", "lib/netcoreapp2.1/System.ServiceModel.dll", "lib/netstandard1.3/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.pdb", "lib/netstandard2.0/System.ServiceModel.dll", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Primitives.dll", "ref/net461/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.dll", "ref/netcore50/System.ServiceModel.Primitives.dll", "ref/netcoreapp2.1/System.ServiceModel.Primitives.dll", "ref/netcoreapp2.1/System.ServiceModel.dll", "ref/netstandard1.0/System.ServiceModel.Primitives.dll", "ref/netstandard1.1/System.ServiceModel.Primitives.dll", "ref/netstandard1.3/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.primitives.4.9.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.ServiceModel.Security/4.9.0": {"sha512": "iurpbSmPgotHps94VQ6acvL6hU2gjiuBmQI7PwLLN76jsbSpUcahT0PglccKIAwoMujATk/LWtAapBHpwCFn2g==", "type": "package", "path": "system.servicemodel.security/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Security.dll", "lib/net461/System.ServiceModel.Security.pdb", "lib/net6.0/System.ServiceModel.Security.dll", "lib/net6.0/System.ServiceModel.Security.pdb", "lib/netcore50/System.ServiceModel.Security.dll", "lib/netstandard1.3/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Security.dll", "ref/net6.0/System.ServiceModel.Security.dll", "ref/netcore50/System.ServiceModel.Security.dll", "ref/netstandard1.0/System.ServiceModel.Security.dll", "ref/netstandard1.1/System.ServiceModel.Security.dll", "ref/netstandard2.0/System.ServiceModel.Security.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.security.4.9.0.nupkg.sha512", "system.servicemodel.security.nuspec"]}, "System.ServiceModel.Syndication/7.0.0": {"sha512": "V3q1Jr3KWo+i201/vUUPfg83rjJLhL5+ROh16PtPhaUJRHwoEBoGWtg0r6pFBRPaDqNY6hXvNgHktDj0gvMEpA==", "type": "package", "path": "system.servicemodel.syndication/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceModel.Syndication.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceModel.Syndication.targets", "lib/net462/System.ServiceModel.Syndication.dll", "lib/net462/System.ServiceModel.Syndication.xml", "lib/net6.0/System.ServiceModel.Syndication.dll", "lib/net6.0/System.ServiceModel.Syndication.xml", "lib/net7.0/System.ServiceModel.Syndication.dll", "lib/net7.0/System.ServiceModel.Syndication.xml", "lib/netstandard2.0/System.ServiceModel.Syndication.dll", "lib/netstandard2.0/System.ServiceModel.Syndication.xml", "system.servicemodel.syndication.7.0.0.nupkg.sha512", "system.servicemodel.syndication.nuspec", "useSharedDesignerContext.txt"]}, "System.ServiceProcess.ServiceController/7.0.1": {"sha512": "rPfXTJzYU46AmWYXRATQzQQ01hICrkl3GuUHgpAr9mnUwAVSsga5x3mBxanFPlJBV9ilzqMXbQyDLJQAbyTnSw==", "type": "package", "path": "system.serviceprocess.servicecontroller/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceProcess.ServiceController.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceProcess.ServiceController.targets", "lib/net462/System.ServiceProcess.ServiceController.dll", "lib/net462/System.ServiceProcess.ServiceController.xml", "lib/net6.0/System.ServiceProcess.ServiceController.dll", "lib/net6.0/System.ServiceProcess.ServiceController.xml", "lib/net7.0/System.ServiceProcess.ServiceController.dll", "lib/net7.0/System.ServiceProcess.ServiceController.xml", "lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.xml", "system.serviceprocess.servicecontroller.7.0.1.nupkg.sha512", "system.serviceprocess.servicecontroller.nuspec", "useSharedDesignerContext.txt"]}, "System.Speech/7.0.0": {"sha512": "7E0uB92Cx2sXR67HW9rMKJqDACdLuz9t3I3OwZUFDzAgwKXWuY6CYeRT/NiypHcyZO2be9+0H0w0M6fn7HQtgQ==", "type": "package", "path": "system.speech/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Speech.targets", "lib/net462/_._", "lib/net6.0/System.Speech.dll", "lib/net6.0/System.Speech.xml", "lib/net7.0/System.Speech.dll", "lib/net7.0/System.Speech.xml", "lib/netstandard2.0/System.Speech.dll", "lib/netstandard2.0/System.Speech.xml", "runtimes/win/lib/net6.0/System.Speech.dll", "runtimes/win/lib/net6.0/System.Speech.xml", "runtimes/win/lib/net7.0/System.Speech.dll", "runtimes/win/lib/net7.0/System.Speech.xml", "system.speech.7.0.0.nupkg.sha512", "system.speech.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/7.0.0": {"sha512": "LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "type": "package", "path": "system.text.encoding.codepages/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/net7.0/System.Text.Encoding.CodePages.dll", "lib/net7.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.7.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.AccessControl/7.0.1": {"sha512": "uh6LWSk8Dlp1cavk4XQYtDHOMZpSa5KiqM0VBiflhXWGT63RGV+NhNsVxiEykL4S/0LVcgy+/AxC5ITQ9QLo8w==", "type": "package", "path": "system.threading.accesscontrol/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.AccessControl.targets", "lib/net462/System.Threading.AccessControl.dll", "lib/net462/System.Threading.AccessControl.xml", "lib/net6.0/System.Threading.AccessControl.dll", "lib/net6.0/System.Threading.AccessControl.xml", "lib/net7.0/System.Threading.AccessControl.dll", "lib/net7.0/System.Threading.AccessControl.xml", "lib/netstandard2.0/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net6.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net6.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net7.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net7.0/System.Threading.AccessControl.xml", "system.threading.accesscontrol.7.0.1.nupkg.sha512", "system.threading.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Web.Services.Description/4.9.0": {"sha512": "d20B3upsWddwSG5xF3eQLs0cAV3tXDsBNqP4kh02ylfgZwqfpf4f/9KiZVIGIoxULt2cKqxWs+U4AdNAJ7L8cQ==", "type": "package", "path": "system.web.services.description/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Web.Services.Description.dll", "lib/net461/System.Web.Services.Description.pdb", "lib/netstandard2.0/System.Web.Services.Description.dll", "lib/netstandard2.0/System.Web.Services.Description.pdb", "lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll", "system.web.services.description.4.9.0.nupkg.sha512", "system.web.services.description.nuspec"]}, "System.Windows.Extensions/7.0.0": {"sha512": "bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "type": "package", "path": "system.windows.extensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "system.windows.extensions.7.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net7.0-windows7.0": ["Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers >= 0.4.410601", "Microsoft.Windows.Compatibility >= 7.0.3", "System.Formats.Asn1 >= 8.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages": {}, "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\": {}}, "project": {"version": "2023.1.2.57", "restore": {"projectUniqueName": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\ECLauncher\\ECLauncher.vbproj", "projectName": "ECLauncher", "projectPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\ECLauncher\\ECLauncher.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AIC\\Main_Next\\SourceCode\\ENChartCAC\\ECLauncher\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 20.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\AIC\\Main_Next\\SourceCode\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[0.4.410601, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[7.0.3, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}
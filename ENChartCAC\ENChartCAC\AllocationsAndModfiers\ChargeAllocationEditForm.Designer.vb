﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class ChargeAllocationEditForm
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm
    ''' <summary>
    ''' Required designer variable.
    ''' </summary>
    Private components As System.ComponentModel.IContainer = Nothing

    ''' <summary>
    ''' Clean up any resources being used.
    ''' </summary>
    ''' <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso (components IsNot Nothing) Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

#Region "Windows Form Designer generated code"

    ''' <summary>
    ''' Required method for Designer support - do not modify
    ''' the contents of this method with the code editor.
    ''' </summary>
    Private Sub InitializeComponent()
        components = New ComponentModel.Container()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim ToolTipSeparatorItem1 As DevExpress.Utils.ToolTipSeparatorItem = New DevExpress.Utils.ToolTipSeparatorItem()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim EditorButtonImageOptions1 As Controls.EditorButtonImageOptions = New Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions2 As Controls.EditorButtonImageOptions = New Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject5 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject6 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject7 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject8 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        gridControl = New DevExpress.XtraGrid.GridControl()
        ChargeAllocationBindingSource = New BindingSource(components)
        PersistentRepository1 = New Repository.PersistentRepository(components)
        taNurseUnitLookupEdit = New Repository.RepositoryItemGridLookUpEdit()
        RepositoryItemGridLookUpEdit2View = New DevExpress.XtraGrid.Views.Grid.GridView()
        gridView = New DevExpress.XtraGrid.Views.Grid.GridView()
        colHcpcsJJC = New DevExpress.XtraGrid.Columns.GridColumn()
        colUnits = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemTextEdit1 = New Repository.RepositoryItemTextEdit()
        colUserDefinedBillingdate = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemDateEdit1 = New Repository.RepositoryItemDateEdit()
        colTreatmentArea = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemGridLookUpEdit1 = New Repository.RepositoryItemGridLookUpEdit()
        TreatementAreaNurseUnitBindingSource = New BindingSource(components)
        RepositoryItemGridLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        colTreatmentAreaType = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemTextEdit2 = New Repository.RepositoryItemTextEdit()
        colPhys2 = New DevExpress.XtraGrid.Columns.GridColumn()
        RepositoryItemComboBox1 = New Repository.RepositoryItemComboBox()
        ribbonControl = New DevExpress.XtraBars.Ribbon.RibbonControl()
        bbiPrintPreview = New DevExpress.XtraBars.BarButtonItem()
        bsiRecordsCount = New DevExpress.XtraBars.BarStaticItem()
        bbiEdit = New DevExpress.XtraBars.BarButtonItem()
        bbiDelete = New DevExpress.XtraBars.BarButtonItem()
        bbiExit = New DevExpress.XtraBars.BarButtonItem()
        BarButtonItem2 = New DevExpress.XtraBars.BarButtonItem()
        bbiSave = New DevExpress.XtraBars.BarButtonItem()
        bbiModifiersUpdate = New DevExpress.XtraBars.BarButtonItem()
        bbiModifiersClearAll = New DevExpress.XtraBars.BarButtonItem()
        ribbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        ribbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Modifiers = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        ribbonStatusBar = New DevExpress.XtraBars.Ribbon.RibbonStatusBar()
        RepositoryItemSpinEdit1 = New Repository.RepositoryItemSpinEdit()
        RepositoryItemLookUpEdit1 = New Repository.RepositoryItemLookUpEdit()
        ChargeBindingSource = New BindingSource(components)
        ObsMods_Mod5_cbo_01 = New ComboBoxEdit()
        XtraForm1LayoutControl1ConvertedLayout = New DevExpress.XtraLayout.LayoutControl()
        DOSDateEdit = New DateEdit()
        ObsMods_Mod4_cbo_01 = New ComboBoxEdit()
        ObsMods_Mod1_cbo_01 = New ComboBoxEdit()
        ObsMods_Mod3_cbo_01 = New ComboBoxEdit()
        ObsMods_Mod2_cbo_01 = New ComboBoxEdit()
        AddNewRowbtn = New SimpleButton()
        SearchLookUpEdit1 = New GridLookUpEdit()
        GridLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        colTA = New DevExpress.XtraGrid.Columns.GridColumn()
        colTAType = New DevExpress.XtraGrid.Columns.GridColumn()
        colCDM = New DevExpress.XtraGrid.Columns.GridColumn()
        UnitsSpinEdit = New SpinEdit()
        HcpcsTextBox = New TextEdit()
        OriginalBillingdateTextBox = New TextEdit()
        TotalUnitsTextBox = New TextEdit()
        TreatmentAreaTextBox = New TextEdit()
        DescriptionTextBox = New TextEdit()
        PhysicianCbo = New ComboBoxEdit()
        LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        EditGroupControl = New DevExpress.XtraLayout.LayoutControlGroup()
        LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        ModifiersLayoutControlGroup = New DevExpress.XtraLayout.LayoutControlGroup()
        LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        TextEdit1 = New TextEdit()
        LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(gridControl, ComponentModel.ISupportInitialize).BeginInit()
        CType(ChargeAllocationBindingSource, ComponentModel.ISupportInitialize).BeginInit()
        CType(taNurseUnitLookupEdit, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemGridLookUpEdit2View, ComponentModel.ISupportInitialize).BeginInit()
        CType(gridView, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemTextEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemDateEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemDateEdit1.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemGridLookUpEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(TreatementAreaNurseUnitBindingSource, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemGridLookUpEdit1View, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemTextEdit2, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemComboBox1, ComponentModel.ISupportInitialize).BeginInit()
        CType(ribbonControl, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemSpinEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(RepositoryItemLookUpEdit1, ComponentModel.ISupportInitialize).BeginInit()
        CType(ChargeBindingSource, ComponentModel.ISupportInitialize).BeginInit()
        CType(ObsMods_Mod5_cbo_01.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(XtraForm1LayoutControl1ConvertedLayout, ComponentModel.ISupportInitialize).BeginInit()
        XtraForm1LayoutControl1ConvertedLayout.SuspendLayout()
        CType(DOSDateEdit.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DOSDateEdit.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ObsMods_Mod4_cbo_01.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ObsMods_Mod1_cbo_01.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ObsMods_Mod3_cbo_01.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(ObsMods_Mod2_cbo_01.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(SearchLookUpEdit1.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(GridLookUpEdit1View, ComponentModel.ISupportInitialize).BeginInit()
        CType(UnitsSpinEdit.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(HcpcsTextBox.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(OriginalBillingdateTextBox.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TotalUnitsTextBox.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(TreatmentAreaTextBox.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(DescriptionTextBox.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(PhysicianCbo.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlGroup1, ComponentModel.ISupportInitialize).BeginInit()
        CType(EditGroupControl, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem2, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem3, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem4, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem15, ComponentModel.ISupportInitialize).BeginInit()
        CType(EmptySpaceItem3, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem5, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlGroup3, ComponentModel.ISupportInitialize).BeginInit()
        CType(ModifiersLayoutControlGroup, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem10, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem11, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem13, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem14, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem12, ComponentModel.ISupportInitialize).BeginInit()
        CType(EmptySpaceItem1, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem17, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem8, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem7, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem6, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem9, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem1, ComponentModel.ISupportInitialize).BeginInit()
        CType(EmptySpaceItem2, ComponentModel.ISupportInitialize).BeginInit()
        CType(TextEdit1.Properties, ComponentModel.ISupportInitialize).BeginInit()
        CType(LayoutControlItem16, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' gridControl
        ' 
        gridControl.DataSource = ChargeAllocationBindingSource
        gridControl.ExternalRepository = PersistentRepository1
        gridControl.Location = New Point(12, 294)
        gridControl.MainView = gridView
        gridControl.MenuManager = ribbonControl
        gridControl.Name = "gridControl"
        gridControl.RepositoryItems.AddRange(New Repository.RepositoryItem() {RepositoryItemGridLookUpEdit1, RepositoryItemDateEdit1, RepositoryItemSpinEdit1, RepositoryItemTextEdit1, RepositoryItemLookUpEdit1, RepositoryItemComboBox1, RepositoryItemTextEdit2})
        gridControl.Size = New Size(1150, 272)
        gridControl.TabIndex = 16
        gridControl.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {gridView})
        ' 
        ' ChargeAllocationBindingSource
        ' 
        ChargeAllocationBindingSource.DataSource = GetType(AIC.SharedData.CAM2.ChargeAllocation)
        ' 
        ' PersistentRepository1
        ' 
        PersistentRepository1.Items.AddRange(New Repository.RepositoryItem() {taNurseUnitLookupEdit})
        ' 
        ' taNurseUnitLookupEdit
        ' 
        taNurseUnitLookupEdit.AutoHeight = False
        taNurseUnitLookupEdit.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        taNurseUnitLookupEdit.Name = "taNurseUnitLookupEdit"
        taNurseUnitLookupEdit.PopupView = RepositoryItemGridLookUpEdit2View
        ' 
        ' RepositoryItemGridLookUpEdit2View
        ' 
        RepositoryItemGridLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        RepositoryItemGridLookUpEdit2View.Name = "RepositoryItemGridLookUpEdit2View"
        RepositoryItemGridLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = False
        RepositoryItemGridLookUpEdit2View.OptionsView.ShowGroupPanel = False
        ' 
        ' gridView
        ' 
        gridView.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        gridView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colHcpcsJJC, colUnits, colUserDefinedBillingdate, colTreatmentArea, colTreatmentAreaType, colPhys2})
        gridView.CustomizationFormBounds = New Rectangle(958, 613, 264, 272)
        gridView.DetailHeight = 377
        gridView.GridControl = gridControl
        gridView.Name = "gridView"
        gridView.NewItemRowText = "Add new row"
        gridView.OptionsNavigation.AutoFocusNewRow = True
        gridView.OptionsNavigation.EnterMoveNextColumn = True
        gridView.OptionsView.ShowFooter = True
        ' 
        ' colHcpcsJJC
        ' 
        colHcpcsJJC.FieldName = "Hcpcs"
        colHcpcsJJC.MinWidth = 23
        colHcpcsJJC.Name = "colHcpcsJJC"
        colHcpcsJJC.UnboundType = DevExpress.Data.UnboundColumnType.String
        colHcpcsJJC.Width = 87
        ' 
        ' colUnits
        ' 
        colUnits.ColumnEdit = RepositoryItemTextEdit1
        colUnits.FieldName = "Units"
        colUnits.MinWidth = 23
        colUnits.Name = "colUnits"
        colUnits.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Units", "SUM={0:0.##}")})
        colUnits.Visible = True
        colUnits.VisibleIndex = 1
        colUnits.Width = 63
        ' 
        ' RepositoryItemTextEdit1
        ' 
        RepositoryItemTextEdit1.AutoHeight = False
        RepositoryItemTextEdit1.Mask.EditMask = "N00"
        RepositoryItemTextEdit1.Mask.MaskType = Mask.MaskType.Numeric
        RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        ' 
        ' colUserDefinedBillingdate
        ' 
        colUserDefinedBillingdate.Caption = "Date"
        colUserDefinedBillingdate.ColumnEdit = RepositoryItemDateEdit1
        colUserDefinedBillingdate.FieldName = "UserDefinedBillingdate"
        colUserDefinedBillingdate.MinWidth = 23
        colUserDefinedBillingdate.Name = "colUserDefinedBillingdate"
        colUserDefinedBillingdate.Visible = True
        colUserDefinedBillingdate.VisibleIndex = 0
        colUserDefinedBillingdate.Width = 175
        ' 
        ' RepositoryItemDateEdit1
        ' 
        RepositoryItemDateEdit1.AutoHeight = False
        RepositoryItemDateEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemDateEdit1.Name = "RepositoryItemDateEdit1"
        ' 
        ' colTreatmentArea
        ' 
        colTreatmentArea.ColumnEdit = RepositoryItemGridLookUpEdit1
        colTreatmentArea.FieldName = "TreatmentArea"
        colTreatmentArea.MinWidth = 23
        colTreatmentArea.Name = "colTreatmentArea"
        colTreatmentArea.Visible = True
        colTreatmentArea.VisibleIndex = 2
        colTreatmentArea.Width = 326
        ' 
        ' RepositoryItemGridLookUpEdit1
        ' 
        RepositoryItemGridLookUpEdit1.AutoHeight = False
        RepositoryItemGridLookUpEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemGridLookUpEdit1.DataSource = TreatementAreaNurseUnitBindingSource
        RepositoryItemGridLookUpEdit1.DisplayMember = "TA"
        RepositoryItemGridLookUpEdit1.EditValueChangedDelay = 100
        RepositoryItemGridLookUpEdit1.HideSelection = False
        RepositoryItemGridLookUpEdit1.KeyMember = "TA"
        RepositoryItemGridLookUpEdit1.Name = "RepositoryItemGridLookUpEdit1"
        RepositoryItemGridLookUpEdit1.PopupView = RepositoryItemGridLookUpEdit1View
        ' 
        ' TreatementAreaNurseUnitBindingSource
        ' 
        TreatementAreaNurseUnitBindingSource.DataSource = GetType(TreatementAreaNurseUnit)
        ' 
        ' RepositoryItemGridLookUpEdit1View
        ' 
        RepositoryItemGridLookUpEdit1View.DetailHeight = 377
        RepositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        RepositoryItemGridLookUpEdit1View.Name = "RepositoryItemGridLookUpEdit1View"
        RepositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        RepositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = False
        ' 
        ' colTreatmentAreaType
        ' 
        colTreatmentAreaType.ColumnEdit = RepositoryItemTextEdit2
        colTreatmentAreaType.FieldName = "TreatmentAreaType"
        colTreatmentAreaType.MinWidth = 23
        colTreatmentAreaType.Name = "colTreatmentAreaType"
        colTreatmentAreaType.OptionsColumn.AllowEdit = False
        colTreatmentAreaType.OptionsColumn.ReadOnly = True
        colTreatmentAreaType.Visible = True
        colTreatmentAreaType.VisibleIndex = 3
        colTreatmentAreaType.Width = 564
        ' 
        ' RepositoryItemTextEdit2
        ' 
        RepositoryItemTextEdit2.AutoHeight = False
        RepositoryItemTextEdit2.Name = "RepositoryItemTextEdit2"
        ' 
        ' colPhys2
        ' 
        colPhys2.Caption = "Physician"
        colPhys2.ColumnEdit = RepositoryItemComboBox1
        colPhys2.FieldName = "Physician"
        colPhys2.Name = "colPhys2"
        colPhys2.Visible = True
        colPhys2.VisibleIndex = 4
        colPhys2.Width = 175
        ' 
        ' RepositoryItemComboBox1
        ' 
        RepositoryItemComboBox1.AutoHeight = False
        RepositoryItemComboBox1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemComboBox1.Name = "RepositoryItemComboBox1"
        RepositoryItemComboBox1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ' 
        ' ribbonControl
        ' 
        ribbonControl.AllowMinimizeRibbon = False
        ribbonControl.ExpandCollapseItem.Id = 0
        ribbonControl.Items.AddRange(New DevExpress.XtraBars.BarItem() {ribbonControl.ExpandCollapseItem, ribbonControl.SearchEditItem, bbiPrintPreview, bsiRecordsCount, bbiEdit, bbiDelete, bbiExit, BarButtonItem2, bbiSave, bbiModifiersUpdate, bbiModifiersClearAll})
        ribbonControl.Location = New Point(0, 0)
        ribbonControl.MaxItemId = 26
        ribbonControl.Name = "ribbonControl"
        ribbonControl.OptionsMenuMinWidth = 385
        ribbonControl.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {ribbonPage1})
        ribbonControl.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2013
        ribbonControl.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False
        ribbonControl.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False
        ribbonControl.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False
        ribbonControl.Size = New Size(1174, 158)
        ribbonControl.StatusBar = ribbonStatusBar
        ribbonControl.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden
        ' 
        ' bbiPrintPreview
        ' 
        bbiPrintPreview.Caption = "Print Preview"
        bbiPrintPreview.Id = 14
        bbiPrintPreview.ImageOptions.ImageUri.Uri = "Preview"
        bbiPrintPreview.Name = "bbiPrintPreview"
        ' 
        ' bsiRecordsCount
        ' 
        bsiRecordsCount.Caption = "RECORDS : 0"
        bsiRecordsCount.Id = 15
        bsiRecordsCount.Name = "bsiRecordsCount"
        bsiRecordsCount.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        ' 
        ' bbiEdit
        ' 
        bbiEdit.Caption = "Edit"
        bbiEdit.Enabled = False
        bbiEdit.Id = 17
        bbiEdit.ImageOptions.ImageUri.Uri = "Edit"
        bbiEdit.Name = "bbiEdit"
        bbiEdit.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        ' 
        ' bbiDelete
        ' 
        bbiDelete.Caption = "Delete"
        bbiDelete.Enabled = False
        bbiDelete.Id = 18
        bbiDelete.ImageOptions.ImageUri.Uri = "Delete"
        bbiDelete.Name = "bbiDelete"
        ' 
        ' bbiExit
        ' 
        bbiExit.Caption = "Exit"
        bbiExit.Id = 20
        bbiExit.ImageOptions.ImageUri.Uri = "Apply"
        bbiExit.Name = "bbiExit"
        ' 
        ' BarButtonItem2
        ' 
        BarButtonItem2.Caption = "BarButtonItem2"
        BarButtonItem2.Id = 21
        BarButtonItem2.ImageOptions.ImageUri.Uri = "AddItem"
        BarButtonItem2.Name = "BarButtonItem2"
        ' 
        ' bbiSave
        ' 
        bbiSave.Caption = "Save"
        bbiSave.Enabled = False
        bbiSave.Id = 22
        bbiSave.ImageOptions.ImageUri.Uri = "Save"
        bbiSave.Name = "bbiSave"
        ' 
        ' bbiModifiersUpdate
        ' 
        bbiModifiersUpdate.Caption = "Update"
        bbiModifiersUpdate.Enabled = False
        bbiModifiersUpdate.Id = 24
        bbiModifiersUpdate.ImageOptions.ImageUri.Uri = "Edit"
        bbiModifiersUpdate.Name = "bbiModifiersUpdate"
        ToolTipTitleItem1.Text = "Save Modifiers"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "After changing (add/edit/delete) modifers, use this to save the modifiers to the charge."
        ToolTipTitleItem2.LeftIndent = 6
        ToolTipTitleItem2.Text = "Application of modifiers is not implented yet! (Comming Soon)"
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        SuperToolTip1.Items.Add(ToolTipSeparatorItem1)
        SuperToolTip1.Items.Add(ToolTipTitleItem2)
        bbiModifiersUpdate.SuperTip = SuperToolTip1
        bbiModifiersUpdate.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        ' 
        ' bbiModifiersClearAll
        ' 
        bbiModifiersClearAll.Caption = "Clear All"
        bbiModifiersClearAll.Id = 25
        bbiModifiersClearAll.ImageOptions.ImageUri.Uri = "Clear"
        bbiModifiersClearAll.Name = "bbiModifiersClearAll"
        ' 
        ' ribbonPage1
        ' 
        ribbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {ribbonPageGroup1, Modifiers})
        ribbonPage1.MergeOrder = 0
        ribbonPage1.Name = "ribbonPage1"
        ribbonPage1.Text = "Home"
        ' 
        ' ribbonPageGroup1
        ' 
        ribbonPageGroup1.AllowTextClipping = False
        ribbonPageGroup1.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False
        ribbonPageGroup1.ItemLinks.Add(bbiSave)
        ribbonPageGroup1.ItemLinks.Add(bbiEdit)
        ribbonPageGroup1.ItemLinks.Add(bbiDelete)
        ribbonPageGroup1.ItemLinks.Add(bbiExit)
        ribbonPageGroup1.Name = "ribbonPageGroup1"
        ribbonPageGroup1.Text = "Allocations"
        ' 
        ' Modifiers
        ' 
        Modifiers.ItemLinks.Add(bbiModifiersUpdate)
        Modifiers.ItemLinks.Add(bbiModifiersClearAll)
        Modifiers.Name = "Modifiers"
        Modifiers.Text = "Modifiers"
        ' 
        ' ribbonStatusBar
        ' 
        ribbonStatusBar.ItemLinks.Add(bsiRecordsCount)
        ribbonStatusBar.Location = New Point(0, 752)
        ribbonStatusBar.Name = "ribbonStatusBar"
        ribbonStatusBar.Ribbon = ribbonControl
        ribbonStatusBar.Size = New Size(1174, 24)
        ' 
        ' RepositoryItemSpinEdit1
        ' 
        RepositoryItemSpinEdit1.AutoHeight = False
        RepositoryItemSpinEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemSpinEdit1.Mask.EditMask = "N00"
        RepositoryItemSpinEdit1.MaxValue = New Decimal(New Integer() {24, 0, 0, 0})
        RepositoryItemSpinEdit1.Name = "RepositoryItemSpinEdit1"
        ' 
        ' RepositoryItemLookUpEdit1
        ' 
        RepositoryItemLookUpEdit1.AutoHeight = False
        RepositoryItemLookUpEdit1.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        RepositoryItemLookUpEdit1.Name = "RepositoryItemLookUpEdit1"
        ' 
        ' ChargeBindingSource
        ' 
        ChargeBindingSource.DataSource = GetType(AIC.SharedData.CAM2.Charge)
        ' 
        ' ObsMods_Mod5_cbo_01
        ' 
        ObsMods_Mod5_cbo_01.EditValue = ""
        ObsMods_Mod5_cbo_01.Location = New Point(485, 158)
        ObsMods_Mod5_cbo_01.Name = "ObsMods_Mod5_cbo_01"
        ObsMods_Mod5_cbo_01.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ObsMods_Mod5_cbo_01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ObsMods_Mod5_cbo_01.Size = New Size(108, 20)
        ObsMods_Mod5_cbo_01.StyleController = XtraForm1LayoutControl1ConvertedLayout
        ObsMods_Mod5_cbo_01.TabIndex = 10
        ' 
        ' XtraForm1LayoutControl1ConvertedLayout
        ' 
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(DOSDateEdit)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(ObsMods_Mod5_cbo_01)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(ObsMods_Mod4_cbo_01)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(ObsMods_Mod1_cbo_01)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(ObsMods_Mod3_cbo_01)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(ObsMods_Mod2_cbo_01)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(gridControl)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(AddNewRowbtn)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(SearchLookUpEdit1)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(UnitsSpinEdit)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(HcpcsTextBox)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(OriginalBillingdateTextBox)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(TotalUnitsTextBox)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(TreatmentAreaTextBox)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(DescriptionTextBox)
        XtraForm1LayoutControl1ConvertedLayout.Controls.Add(PhysicianCbo)
        XtraForm1LayoutControl1ConvertedLayout.Dock = DockStyle.Fill
        XtraForm1LayoutControl1ConvertedLayout.Location = New Point(0, 158)
        XtraForm1LayoutControl1ConvertedLayout.Name = "XtraForm1LayoutControl1ConvertedLayout"
        XtraForm1LayoutControl1ConvertedLayout.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New Rectangle(805, 313, 615, 636)
        XtraForm1LayoutControl1ConvertedLayout.Root = LayoutControlGroup1
        XtraForm1LayoutControl1ConvertedLayout.Size = New Size(1174, 618)
        XtraForm1LayoutControl1ConvertedLayout.TabIndex = 541
        ' 
        ' DOSDateEdit
        ' 
        DOSDateEdit.EditValue = Nothing
        DOSDateEdit.EnterMoveNextControl = True
        DOSDateEdit.Location = New Point(24, 258)
        DOSDateEdit.Name = "DOSDateEdit"
        DOSDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True
        DOSDateEdit.Properties.AppearanceDisabled.ForeColor = Color.Black
        DOSDateEdit.Properties.AppearanceDisabled.Options.UseForeColor = True
        DOSDateEdit.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo), New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Minus, "Minus", -1, True, True, False, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut(Keys.None), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "-1 Day", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.Default), New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Plus, "Plus", -1, True, True, False, EditorButtonImageOptions2, New DevExpress.Utils.KeyShortcut(Keys.None), SerializableAppearanceObject5, SerializableAppearanceObject6, SerializableAppearanceObject7, SerializableAppearanceObject8, "+1 Day", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.Default)})
        DOSDateEdit.Properties.CalendarTimeEditing = DevExpress.Utils.DefaultBoolean.True
        DOSDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton()})
        DOSDateEdit.Properties.Mask.ShowPlaceHolders = False
        DOSDateEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        DOSDateEdit.Properties.MaxValue = New Date(9999, 12, 31, 23, 59, 0, 0)
        DOSDateEdit.Size = New Size(215, 20)
        DOSDateEdit.StyleController = XtraForm1LayoutControl1ConvertedLayout
        DOSDateEdit.TabIndex = 11
        DOSDateEdit.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Error
        ' 
        ' ObsMods_Mod4_cbo_01
        ' 
        ObsMods_Mod4_cbo_01.EditValue = ""
        ObsMods_Mod4_cbo_01.Location = New Point(373, 158)
        ObsMods_Mod4_cbo_01.Name = "ObsMods_Mod4_cbo_01"
        ObsMods_Mod4_cbo_01.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ObsMods_Mod4_cbo_01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ObsMods_Mod4_cbo_01.Size = New Size(108, 20)
        ObsMods_Mod4_cbo_01.StyleController = XtraForm1LayoutControl1ConvertedLayout
        ObsMods_Mod4_cbo_01.TabIndex = 9
        ' 
        ' ObsMods_Mod1_cbo_01
        ' 
        ObsMods_Mod1_cbo_01.EditValue = ""
        ObsMods_Mod1_cbo_01.Location = New Point(36, 158)
        ObsMods_Mod1_cbo_01.Name = "ObsMods_Mod1_cbo_01"
        ObsMods_Mod1_cbo_01.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ObsMods_Mod1_cbo_01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ObsMods_Mod1_cbo_01.Size = New Size(108, 20)
        ObsMods_Mod1_cbo_01.StyleController = XtraForm1LayoutControl1ConvertedLayout
        ObsMods_Mod1_cbo_01.TabIndex = 6
        ' 
        ' ObsMods_Mod3_cbo_01
        ' 
        ObsMods_Mod3_cbo_01.EditValue = ""
        ObsMods_Mod3_cbo_01.Location = New Point(261, 158)
        ObsMods_Mod3_cbo_01.Name = "ObsMods_Mod3_cbo_01"
        ObsMods_Mod3_cbo_01.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ObsMods_Mod3_cbo_01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ObsMods_Mod3_cbo_01.Size = New Size(108, 20)
        ObsMods_Mod3_cbo_01.StyleController = XtraForm1LayoutControl1ConvertedLayout
        ObsMods_Mod3_cbo_01.TabIndex = 8
        ' 
        ' ObsMods_Mod2_cbo_01
        ' 
        ObsMods_Mod2_cbo_01.EditValue = ""
        ObsMods_Mod2_cbo_01.Location = New Point(148, 158)
        ObsMods_Mod2_cbo_01.Name = "ObsMods_Mod2_cbo_01"
        ObsMods_Mod2_cbo_01.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        ObsMods_Mod2_cbo_01.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        ObsMods_Mod2_cbo_01.Size = New Size(109, 20)
        ObsMods_Mod2_cbo_01.StyleController = XtraForm1LayoutControl1ConvertedLayout
        ObsMods_Mod2_cbo_01.TabIndex = 7
        ' 
        ' AddNewRowbtn
        ' 
        AddNewRowbtn.Enabled = False
        AddNewRowbtn.Location = New Point(1087, 256)
        AddNewRowbtn.Name = "AddNewRowbtn"
        AddNewRowbtn.Size = New Size(63, 22)
        AddNewRowbtn.StyleController = XtraForm1LayoutControl1ConvertedLayout
        AddNewRowbtn.TabIndex = 15
        AddNewRowbtn.Text = "Add"
        ' 
        ' SearchLookUpEdit1
        ' 
        SearchLookUpEdit1.Location = New Point(383, 258)
        SearchLookUpEdit1.MenuManager = ribbonControl
        SearchLookUpEdit1.Name = "SearchLookUpEdit1"
        SearchLookUpEdit1.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        SearchLookUpEdit1.Properties.DataSource = TreatementAreaNurseUnitBindingSource
        SearchLookUpEdit1.Properties.DisplayMember = "TA"
        SearchLookUpEdit1.Properties.PopupView = GridLookUpEdit1View
        SearchLookUpEdit1.Properties.ValueMember = "TA"
        SearchLookUpEdit1.Size = New Size(538, 20)
        SearchLookUpEdit1.StyleController = XtraForm1LayoutControl1ConvertedLayout
        SearchLookUpEdit1.TabIndex = 13
        ' 
        ' GridLookUpEdit1View
        ' 
        GridLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colTA, colTAType, colCDM})
        GridLookUpEdit1View.DetailHeight = 377
        GridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        GridLookUpEdit1View.Name = "GridLookUpEdit1View"
        GridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        GridLookUpEdit1View.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never
        GridLookUpEdit1View.OptionsView.ShowGroupPanel = False
        ' 
        ' colTA
        ' 
        colTA.FieldName = "TA"
        colTA.MinWidth = 23
        colTA.Name = "colTA"
        colTA.Visible = True
        colTA.VisibleIndex = 0
        colTA.Width = 608
        ' 
        ' colTAType
        ' 
        colTAType.FieldName = "TAType"
        colTAType.MinWidth = 23
        colTAType.Name = "colTAType"
        colTAType.Visible = True
        colTAType.VisibleIndex = 1
        colTAType.Width = 269
        ' 
        ' colCDM
        ' 
        colCDM.FieldName = "CDM"
        colCDM.MinWidth = 23
        colCDM.Name = "colCDM"
        colCDM.Visible = True
        colCDM.VisibleIndex = 2
        colCDM.Width = 272
        ' 
        ' UnitsSpinEdit
        ' 
        UnitsSpinEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        UnitsSpinEdit.Location = New Point(243, 258)
        UnitsSpinEdit.MenuManager = ribbonControl
        UnitsSpinEdit.Name = "UnitsSpinEdit"
        UnitsSpinEdit.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        UnitsSpinEdit.Properties.EditFormat.FormatString = "N00"
        UnitsSpinEdit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        UnitsSpinEdit.Properties.IsFloatValue = False
        UnitsSpinEdit.Properties.Mask.EditMask = "N00"
        UnitsSpinEdit.Properties.ValidateOnEnterKey = True
        UnitsSpinEdit.Size = New Size(136, 20)
        UnitsSpinEdit.StyleController = XtraForm1LayoutControl1ConvertedLayout
        UnitsSpinEdit.TabIndex = 12
        ' 
        ' HcpcsTextBox
        ' 
        HcpcsTextBox.DataBindings.Add(New Binding("Text", ChargeBindingSource, "Hcpcs", True))
        HcpcsTextBox.EditValue = ""
        HcpcsTextBox.Location = New Point(24, 61)
        HcpcsTextBox.MenuManager = ribbonControl
        HcpcsTextBox.Name = "HcpcsTextBox"
        HcpcsTextBox.Properties.ReadOnly = True
        HcpcsTextBox.Size = New Size(277, 20)
        HcpcsTextBox.StyleController = XtraForm1LayoutControl1ConvertedLayout
        HcpcsTextBox.TabIndex = 0
        ' 
        ' OriginalBillingdateTextBox
        ' 
        OriginalBillingdateTextBox.DataBindings.Add(New Binding("Text", ChargeBindingSource, "OriginalBillingdate", True))
        OriginalBillingdateTextBox.Location = New Point(305, 61)
        OriginalBillingdateTextBox.MenuManager = ribbonControl
        OriginalBillingdateTextBox.Name = "OriginalBillingdateTextBox"
        OriginalBillingdateTextBox.Properties.ReadOnly = True
        OriginalBillingdateTextBox.Size = New Size(280, 20)
        OriginalBillingdateTextBox.StyleController = XtraForm1LayoutControl1ConvertedLayout
        OriginalBillingdateTextBox.TabIndex = 2
        ' 
        ' TotalUnitsTextBox
        ' 
        TotalUnitsTextBox.DataBindings.Add(New Binding("Text", ChargeBindingSource, "TotalUnits", True))
        TotalUnitsTextBox.Location = New Point(589, 61)
        TotalUnitsTextBox.MenuManager = ribbonControl
        TotalUnitsTextBox.Name = "TotalUnitsTextBox"
        TotalUnitsTextBox.Properties.ReadOnly = True
        TotalUnitsTextBox.Size = New Size(277, 20)
        TotalUnitsTextBox.StyleController = XtraForm1LayoutControl1ConvertedLayout
        TotalUnitsTextBox.TabIndex = 3
        ' 
        ' TreatmentAreaTextBox
        ' 
        TreatmentAreaTextBox.DataBindings.Add(New Binding("Text", ChargeBindingSource, "TreatmentArea", True))
        TreatmentAreaTextBox.Location = New Point(870, 61)
        TreatmentAreaTextBox.MenuManager = ribbonControl
        TreatmentAreaTextBox.Name = "TreatmentAreaTextBox"
        TreatmentAreaTextBox.Properties.ReadOnly = True
        TreatmentAreaTextBox.Size = New Size(280, 20)
        TreatmentAreaTextBox.StyleController = XtraForm1LayoutControl1ConvertedLayout
        TreatmentAreaTextBox.TabIndex = 4
        ' 
        ' DescriptionTextBox
        ' 
        DescriptionTextBox.DataBindings.Add(New Binding("Text", ChargeBindingSource, "Description", True))
        DescriptionTextBox.Location = New Point(127, 85)
        DescriptionTextBox.MenuManager = ribbonControl
        DescriptionTextBox.Name = "DescriptionTextBox"
        DescriptionTextBox.Properties.ReadOnly = True
        DescriptionTextBox.Size = New Size(1023, 20)
        DescriptionTextBox.StyleController = XtraForm1LayoutControl1ConvertedLayout
        DescriptionTextBox.TabIndex = 5
        ' 
        ' PhysicianCbo
        ' 
        PhysicianCbo.Location = New Point(925, 258)
        PhysicianCbo.MenuManager = ribbonControl
        PhysicianCbo.Name = "PhysicianCbo"
        PhysicianCbo.Properties.Buttons.AddRange(New Controls.EditorButton() {New Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        PhysicianCbo.Properties.ImmediatePopup = True
        PhysicianCbo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        PhysicianCbo.Properties.UseReadOnlyAppearance = False
        PhysicianCbo.Size = New Size(158, 20)
        PhysicianCbo.StyleController = XtraForm1LayoutControl1ConvertedLayout
        PhysicianCbo.TabIndex = 14
        ' 
        ' LayoutControlGroup1
        ' 
        LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True
        LayoutControlGroup1.GroupBordersVisible = False
        LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {EditGroupControl, LayoutControlGroup3, LayoutControlItem1, EmptySpaceItem2})
        LayoutControlGroup1.Name = "Root"
        LayoutControlGroup1.Size = New Size(1174, 618)
        LayoutControlGroup1.TextVisible = False
        ' 
        ' EditGroupControl
        ' 
        EditGroupControl.Enabled = False
        EditGroupControl.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {LayoutControlItem2, LayoutControlItem3, LayoutControlItem4, LayoutControlItem15, EmptySpaceItem3, LayoutControlItem5})
        EditGroupControl.Location = New Point(0, 197)
        EditGroupControl.Name = "EditGroupControl"
        EditGroupControl.Size = New Size(1154, 85)
        EditGroupControl.Text = "Add/Edit"
        ' 
        ' LayoutControlItem2
        ' 
        LayoutControlItem2.Control = SearchLookUpEdit1
        LayoutControlItem2.Location = New Point(359, 0)
        LayoutControlItem2.Name = "SearchLookUpEdit1item"
        LayoutControlItem2.Size = New Size(542, 40)
        LayoutControlItem2.Text = "TA/Nursing Unit"
        LayoutControlItem2.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem2.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem3
        ' 
        LayoutControlItem3.Control = UnitsSpinEdit
        LayoutControlItem3.Location = New Point(219, 0)
        LayoutControlItem3.Name = "UnitsSpinEdititem"
        LayoutControlItem3.Size = New Size(140, 40)
        LayoutControlItem3.Text = "Units"
        LayoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem3.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem4
        ' 
        LayoutControlItem4.Control = DOSDateEdit
        LayoutControlItem4.Location = New Point(0, 0)
        LayoutControlItem4.Name = "DOSDateEdititem"
        LayoutControlItem4.Size = New Size(219, 40)
        LayoutControlItem4.Text = "Date"
        LayoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem4.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem15
        ' 
        LayoutControlItem15.Control = AddNewRowbtn
        LayoutControlItem15.Location = New Point(1063, 14)
        LayoutControlItem15.Name = "LayoutControlItem15"
        LayoutControlItem15.Size = New Size(67, 26)
        LayoutControlItem15.TextSize = New Size(0, 0)
        LayoutControlItem15.TextVisible = False
        ' 
        ' EmptySpaceItem3
        ' 
        EmptySpaceItem3.AllowHotTrack = False
        EmptySpaceItem3.Location = New Point(1063, 0)
        EmptySpaceItem3.Name = "EmptySpaceItem3"
        EmptySpaceItem3.Size = New Size(67, 14)
        EmptySpaceItem3.TextSize = New Size(0, 0)
        ' 
        ' LayoutControlItem5
        ' 
        LayoutControlItem5.Control = PhysicianCbo
        LayoutControlItem5.Location = New Point(901, 0)
        LayoutControlItem5.Name = "LayoutControlItem5"
        LayoutControlItem5.Size = New Size(162, 40)
        LayoutControlItem5.Text = "Physician"
        LayoutControlItem5.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem5.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlGroup3
        ' 
        LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {ModifiersLayoutControlGroup, LayoutControlItem17, LayoutControlItem8, LayoutControlItem7, LayoutControlItem6, LayoutControlItem9})
        LayoutControlGroup3.Location = New Point(0, 0)
        LayoutControlGroup3.Name = "GroupControl2item"
        LayoutControlGroup3.Size = New Size(1154, 197)
        LayoutControlGroup3.Text = "Master"
        ' 
        ' ModifiersLayoutControlGroup
        ' 
        ModifiersLayoutControlGroup.CustomizationFormText = "Modfiers"
        ModifiersLayoutControlGroup.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {LayoutControlItem10, LayoutControlItem11, LayoutControlItem13, LayoutControlItem14, LayoutControlItem12, EmptySpaceItem1})
        ModifiersLayoutControlGroup.Location = New Point(0, 64)
        ModifiersLayoutControlGroup.Name = "ModifiersLayoutControlGroup"
        ModifiersLayoutControlGroup.Size = New Size(1130, 88)
        ModifiersLayoutControlGroup.Text = "Modifiers"
        ' 
        ' LayoutControlItem10
        ' 
        LayoutControlItem10.BestFitWeight = 50
        LayoutControlItem10.Control = ObsMods_Mod5_cbo_01
        LayoutControlItem10.Location = New Point(449, 0)
        LayoutControlItem10.Name = "ComboBoxEdit1item"
        LayoutControlItem10.OptionsTableLayoutItem.ColumnIndex = 1
        LayoutControlItem10.OptionsTableLayoutItem.RowIndex = 2
        LayoutControlItem10.Size = New Size(112, 43)
        LayoutControlItem10.Text = "Modifier 5"
        LayoutControlItem10.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem10.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem11
        ' 
        LayoutControlItem11.BestFitWeight = 50
        LayoutControlItem11.Control = ObsMods_Mod4_cbo_01
        LayoutControlItem11.Location = New Point(337, 0)
        LayoutControlItem11.Name = "ObsMods_Mod4_cbo_01item"
        LayoutControlItem11.OptionsTableLayoutItem.RowIndex = 3
        LayoutControlItem11.Size = New Size(112, 43)
        LayoutControlItem11.Text = "Modifier 4"
        LayoutControlItem11.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem11.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem13
        ' 
        LayoutControlItem13.Control = ObsMods_Mod3_cbo_01
        LayoutControlItem13.Location = New Point(225, 0)
        LayoutControlItem13.Name = "ObsMods_Mod3_cbo_01item"
        LayoutControlItem13.OptionsTableLayoutItem.RowIndex = 4
        LayoutControlItem13.Size = New Size(112, 43)
        LayoutControlItem13.Text = "Modifier 3"
        LayoutControlItem13.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem13.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem14
        ' 
        LayoutControlItem14.Control = ObsMods_Mod2_cbo_01
        LayoutControlItem14.Location = New Point(112, 0)
        LayoutControlItem14.MaxSize = New Size(0, 43)
        LayoutControlItem14.MinSize = New Size(111, 43)
        LayoutControlItem14.Name = "ObsMods_Mod2_cbo_01item"
        LayoutControlItem14.OptionsTableLayoutItem.ColumnIndex = 1
        LayoutControlItem14.OptionsTableLayoutItem.RowIndex = 4
        LayoutControlItem14.Size = New Size(113, 43)
        LayoutControlItem14.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        LayoutControlItem14.Text = "Modifier 2"
        LayoutControlItem14.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem14.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem12
        ' 
        LayoutControlItem12.Control = ObsMods_Mod1_cbo_01
        LayoutControlItem12.Location = New Point(0, 0)
        LayoutControlItem12.Name = "ObsMods_Mod1_cbo_01item"
        LayoutControlItem12.OptionsTableLayoutItem.ColumnIndex = 1
        LayoutControlItem12.OptionsTableLayoutItem.RowIndex = 3
        LayoutControlItem12.Size = New Size(112, 43)
        LayoutControlItem12.Text = "Modifier 1"
        LayoutControlItem12.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem12.TextSize = New Size(91, 13)
        ' 
        ' EmptySpaceItem1
        ' 
        EmptySpaceItem1.AllowHotTrack = False
        EmptySpaceItem1.Location = New Point(561, 0)
        EmptySpaceItem1.Name = "EmptySpaceItem1"
        EmptySpaceItem1.Size = New Size(545, 43)
        EmptySpaceItem1.TextSize = New Size(0, 0)
        ' 
        ' LayoutControlItem17
        ' 
        LayoutControlItem17.Control = HcpcsTextBox
        LayoutControlItem17.CustomizationFormText = "HCPCS"
        LayoutControlItem17.Location = New Point(0, 0)
        LayoutControlItem17.Name = "LayoutControlItem17"
        LayoutControlItem17.Size = New Size(281, 40)
        LayoutControlItem17.Text = "HCPCS"
        LayoutControlItem17.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem17.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem8
        ' 
        LayoutControlItem8.Control = OriginalBillingdateTextBox
        LayoutControlItem8.Location = New Point(281, 0)
        LayoutControlItem8.Name = "LayoutControlItem8"
        LayoutControlItem8.Size = New Size(284, 40)
        LayoutControlItem8.Text = "Original Billingdate:"
        LayoutControlItem8.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem8.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem7
        ' 
        LayoutControlItem7.Control = TotalUnitsTextBox
        LayoutControlItem7.Location = New Point(565, 0)
        LayoutControlItem7.Name = "LayoutControlItem7"
        LayoutControlItem7.Size = New Size(281, 40)
        LayoutControlItem7.Text = " Units:"
        LayoutControlItem7.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem7.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem6
        ' 
        LayoutControlItem6.Control = TreatmentAreaTextBox
        LayoutControlItem6.Location = New Point(846, 0)
        LayoutControlItem6.Name = "LayoutControlItem6"
        LayoutControlItem6.Size = New Size(284, 40)
        LayoutControlItem6.Text = "TA/Nursing Unit"
        LayoutControlItem6.TextLocation = DevExpress.Utils.Locations.Top
        LayoutControlItem6.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem9
        ' 
        LayoutControlItem9.Control = DescriptionTextBox
        LayoutControlItem9.Location = New Point(0, 40)
        LayoutControlItem9.Name = "LayoutControlItem9"
        LayoutControlItem9.Size = New Size(1130, 24)
        LayoutControlItem9.Text = "Description"
        LayoutControlItem9.TextSize = New Size(91, 13)
        ' 
        ' LayoutControlItem1
        ' 
        LayoutControlItem1.Control = gridControl
        LayoutControlItem1.Location = New Point(0, 282)
        LayoutControlItem1.Name = "gridControlitem"
        LayoutControlItem1.Size = New Size(1154, 276)
        LayoutControlItem1.TextSize = New Size(0, 0)
        LayoutControlItem1.TextVisible = False
        ' 
        ' EmptySpaceItem2
        ' 
        EmptySpaceItem2.AllowHotTrack = False
        EmptySpaceItem2.Location = New Point(0, 558)
        EmptySpaceItem2.Name = "EmptySpaceItem2"
        EmptySpaceItem2.Size = New Size(1154, 40)
        EmptySpaceItem2.TextSize = New Size(0, 0)
        ' 
        ' TextEdit1
        ' 
        TextEdit1.Location = New Point(138, 85)
        TextEdit1.MenuManager = ribbonControl
        TextEdit1.Name = "TextEdit1"
        TextEdit1.Size = New Size(1012, 20)
        TextEdit1.TabIndex = 544
        ' 
        ' LayoutControlItem16
        ' 
        LayoutControlItem16.Control = TextEdit1
        LayoutControlItem16.Location = New Point(0, 40)
        LayoutControlItem16.Name = "LayoutControlItem16"
        LayoutControlItem16.Size = New Size(1130, 24)
        LayoutControlItem16.TextSize = New Size(91, 13)
        ' 
        ' ChargeAllocationEditForm
        ' 
        Appearance.Options.UseFont = True
        AutoScaleDimensions = New SizeF(7F, 14F)
        AutoScaleMode = AutoScaleMode.Font
        ClientSize = New Size(1174, 776)
        ControlBox = False
        Controls.Add(ribbonStatusBar)
        Controls.Add(XtraForm1LayoutControl1ConvertedLayout)
        Controls.Add(ribbonControl)
        Font = New Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point)
        IconOptions.ShowIcon = False
        MaximizeBox = False
        MinimizeBox = False
        Name = "ChargeAllocationEditForm"
        Ribbon = ribbonControl
        RibbonVisibility = DevExpress.XtraBars.Ribbon.RibbonVisibility.Visible
        StartPosition = FormStartPosition.CenterParent
        StatusBar = ribbonStatusBar
        Text = "Allocate Charges"
        CType(gridControl, ComponentModel.ISupportInitialize).EndInit()
        CType(ChargeAllocationBindingSource, ComponentModel.ISupportInitialize).EndInit()
        CType(taNurseUnitLookupEdit, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemGridLookUpEdit2View, ComponentModel.ISupportInitialize).EndInit()
        CType(gridView, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemTextEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemDateEdit1.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemDateEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemGridLookUpEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(TreatementAreaNurseUnitBindingSource, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemGridLookUpEdit1View, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemTextEdit2, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemComboBox1, ComponentModel.ISupportInitialize).EndInit()
        CType(ribbonControl, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemSpinEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(RepositoryItemLookUpEdit1, ComponentModel.ISupportInitialize).EndInit()
        CType(ChargeBindingSource, ComponentModel.ISupportInitialize).EndInit()
        CType(ObsMods_Mod5_cbo_01.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(XtraForm1LayoutControl1ConvertedLayout, ComponentModel.ISupportInitialize).EndInit()
        XtraForm1LayoutControl1ConvertedLayout.ResumeLayout(False)
        CType(DOSDateEdit.Properties.CalendarTimeProperties, ComponentModel.ISupportInitialize).EndInit()
        CType(DOSDateEdit.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ObsMods_Mod4_cbo_01.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ObsMods_Mod1_cbo_01.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ObsMods_Mod3_cbo_01.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(ObsMods_Mod2_cbo_01.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(SearchLookUpEdit1.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(GridLookUpEdit1View, ComponentModel.ISupportInitialize).EndInit()
        CType(UnitsSpinEdit.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(HcpcsTextBox.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(OriginalBillingdateTextBox.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TotalUnitsTextBox.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(TreatmentAreaTextBox.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(DescriptionTextBox.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(PhysicianCbo.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlGroup1, ComponentModel.ISupportInitialize).EndInit()
        CType(EditGroupControl, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem2, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem3, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem4, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem15, ComponentModel.ISupportInitialize).EndInit()
        CType(EmptySpaceItem3, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem5, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlGroup3, ComponentModel.ISupportInitialize).EndInit()
        CType(ModifiersLayoutControlGroup, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem10, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem11, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem13, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem14, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem12, ComponentModel.ISupportInitialize).EndInit()
        CType(EmptySpaceItem1, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem17, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem8, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem7, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem6, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem9, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem1, ComponentModel.ISupportInitialize).EndInit()
        CType(EmptySpaceItem2, ComponentModel.ISupportInitialize).EndInit()
        CType(TextEdit1.Properties, ComponentModel.ISupportInitialize).EndInit()
        CType(LayoutControlItem16, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
        PerformLayout()
    End Sub

#End Region
    Private WithEvents gridControl As DevExpress.XtraGrid.GridControl
    Private WithEvents gridView As DevExpress.XtraGrid.Views.Grid.GridView
    Private WithEvents ribbonControl As DevExpress.XtraBars.Ribbon.RibbonControl
    Private WithEvents ribbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Private WithEvents ribbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Private WithEvents bbiPrintPreview As DevExpress.XtraBars.BarButtonItem
    Private WithEvents ribbonStatusBar As DevExpress.XtraBars.Ribbon.RibbonStatusBar
    Private WithEvents bsiRecordsCount As DevExpress.XtraBars.BarStaticItem
    Private WithEvents bbiEdit As DevExpress.XtraBars.BarButtonItem
    Private WithEvents bbiDelete As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents ChargeAllocationBindingSource As BindingSource
    Friend WithEvents colPhysician As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHcpcsJJC As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUnits As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUserDefinedBillingdate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTreatmentArea As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents bbiExit As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents ChargeBindingSource As BindingSource
    Public WithEvents ObsMods_Mod4_cbo_01 As ComboBoxEdit
    Public WithEvents ObsMods_Mod1_cbo_01 As ComboBoxEdit
    Public WithEvents ObsMods_Mod3_cbo_01 As ComboBoxEdit
    Public WithEvents ObsMods_Mod2_cbo_01 As ComboBoxEdit
    Friend WithEvents BarButtonItem2 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiSave As DevExpress.XtraBars.BarButtonItem
    Public WithEvents ObsMods_Mod5_cbo_01 As ComboBoxEdit
    Public WithEvents DOSDateEdit As DateEdit
    Friend WithEvents colTreatmentAreaType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents XtraForm1LayoutControl1ConvertedLayout As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EditGroupControl As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ModifiersLayoutControlGroup As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents bbiModifiersUpdate As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Modifiers As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bbiModifiersClearAll As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents AddNewRowbtn As SimpleButton
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents SearchLookUpEdit1 As GridLookUpEdit
    Friend WithEvents GridLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents TreatementAreaNurseUnitBindingSource As BindingSource
    Friend WithEvents colTA As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTAType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemGridLookUpEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit
    Friend WithEvents RepositoryItemGridLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents PersistentRepository1 As DevExpress.XtraEditors.Repository.PersistentRepository
    Friend WithEvents taNurseUnitLookupEdit As DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit
    Friend WithEvents RepositoryItemGridLookUpEdit2View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colCDM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemDateEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
    Friend WithEvents RepositoryItemSpinEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents UnitsSpinEdit As SpinEdit
    Friend WithEvents TextEdit1 As TextEdit
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents HcpcsTextBox As TextEdit
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents OriginalBillingdateTextBox As TextEdit
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TotalUnitsTextBox As TextEdit
    Friend WithEvents TreatmentAreaTextBox As TextEdit
    Friend WithEvents DescriptionTextBox As TextEdit
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colPhys2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemLookUpEdit1 As Repository.RepositoryItemLookUpEdit
    Friend WithEvents RepositoryItemComboBox1 As Repository.RepositoryItemComboBox
    Friend WithEvents ComboBoxEdit1 As ComboBoxEdit
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents PhysicianCbo As ComboBoxEdit
    Friend WithEvents RepositoryItemTextEdit2 As Repository.RepositoryItemTextEdit
End Class

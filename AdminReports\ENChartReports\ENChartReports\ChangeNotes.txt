**************  (v. *******) 2017-12-07
	* Allscripts Rebranding
		ReportStartup.vb [Design]
			- Changed Form.Text to "AIC Reporting"
		ReportStartup.vb
			- Changed warning header from "MIC Standard Reports" to "AIC Reporting" (~line 689)
		WebServiceReportViewer.vb
			- Changed "Mc<PERSON>esson" to "Allscripts" in error messaging (~lines 50, 59, 72, 80)
			- Changed "MIC" to "AIC" in error codes (~lines 51, 60, 73, 81)
		CrystalReportsUpdateDialog.vb
			- Changed "MIC" to "AIC" in update messages (~line 34, 53)
		Removed crimage.jpg from ENChartReports project, this image is instead loaded dynamically at runtime


**************  (v. *******) 2016-03-30
	* US36671: SAP 17 Upgrade - Upgrade ENChartReports Solution
	* Removed the Installer project
	* Upgraded ENChartReports to .NET 4.0
	* Removed Unused References
	* Upgraded DevExpress References to v15.2

**************  (v. *******) 2015-12-31
	* US34120 - Infusion Center Module - New reports category
	* A new top-level reports category called "Infusion/Chemo Reports" is needed
		ReportStartup.vb
			- Updated signatures of the AddItem and EditItem functions to include a new boolean parameter, is_infusion (~lines 1011, 1072)
			- Cleaned up code that sets the ReportType when AddItem or EditItem are called (~lines 1029-1032, 1076-1079)
			- Add code to set ReportType for infusion reports when AddItem or EditItem are called (~lines 1033-1081)
			- Updated call to FormNewReport's ShowDialog function to include a parameter for is_infusion (~lines 993)
		FormNewReport.vb [Design]
			- Added a new checkbox, chkIsInfusion
		FormNewReport.vb
			- Updated calls to ReportStartup's AddItem and EditItem to include a parameter for is_infusion using the value of the chkIsInfusion
			  checkbox (~lines 38-40)
			- Updated signature of the ShowDialog function to include a new boolean parameter, is_infusion (~line 17)
		FormNewCategory.vb
			- Updated calls to ReportStartup's AddItem and EditItem to include a parameter for is_infusion (~lines 31, 33)

**************  (v. 2.1.1.0) 2015-11-18
	* Updated to match the MIC release version number. Very sorry about this revision, but it allows us to get all application versions in line.
	* Removed several unused files from the project
		CrystalReport1.rpt
		CrystalReport1.vb
		Form1.Designer.vb
		Form1.vb
		Settings.vb
		/Resources and contents
	* Report Source Setting
		Settings
			- New setting ReportViewerClass, used to store the name of the class that implements IReportViewer to be used
		/ReportViewer created
		/ReportViewer/IReportViewer.vb
			- New interface to allow for multiple methods of retrieving reports
		/ReportViewer/BaseReportViewer.vb
			- New base class for ReportViewer to derive from so that common functionality does not have to be duplicated
		ReportViewer.vb renamed /ReportViewer/WebServiceReportViewer.vb
			- Implementation if IReportViewer for retrieving reports from the web service
			- Inherit BaseReportViewer
			- Implement IReportViewer
			- Code cleanup - removal of unused references, unreachable code, etc.
			- ReportViewer class renamed WebServiceReportViewer
		/ReportViewer/LocalReportViewer.vb
			- New implementation of IReportViewer and derivative of BaseReportViewer for retrieving reports from local files
		ReportStartup.vb
			- Code cleanup - removal of unused references, unreachable code, etc.
			- Import System.Reflection
			- In sub IsCrystalReportsUpToDate, updated reference to ReportViewer to LocalReportViewer when doing the Crystal Reports version check. This does
			  not need to depend on the configuration since no report will actually be generated (~line 173)
			- In sub RunReport_btn_Click, create an instance of IReportViewer using the ReportViewerClass setting, then display the report (~lines 236-237)
		* Note: I attempted to create a BaseReportViewer class for the ReportViewers, but something was causing the report to not appear when the 
		  ReportViewer was loaded.

**************  (v. 2.4.3.3) 2015-09-03
	* US28072 - Audit Log - Log when a user runs an admin report
	* Create an Audit Log entry when a user runs an admin report
		ReportViewer.vb
			- In the Show_Report method, write audit log entry with the report's selection criteria (~line 42)

**************  (v. 2.4.3.2) 2015-08-27
	* Inactivity Timeout Update
	* Update to use functionality of Timeout.dll v.1.0.0.1
		ReportStartup.vb
			- ReportStartup class no longer implements the ITimeoutable interface
			- Remove the methods that are no longer required
			- Create sub HandleIdleTimeoutShutdown with instructions for what to do when a timeout occurs (~lines 1423-1426)
			- Call BasicTimeoutWatcher.Init to begin monitoring for inactivity (~line 109)
			- Suspend any timeouts when a report is being fetched (~line 217)
			- Resume timeouts when the report is loaded (~line 256)

**************  (v. 2.4.3.1) 2015-08-24
	* US29058 - Audit Log - Log Admin Reports Events
	* Minor tweaks to the logging method names that are called
		ReportViewer.vb
			- Cleaned up calls to audit logger methods (~lines 139, 143)

**************  (v. 2.4.3.1) 2015-08-21
	* US29058 - Audit Log - Log Admin Reports Events
	* Create a subclass of CrystalReportViewer to be used to override the ExportReport and SaveReport events so that we can create an audit log entry when
	* those actions occur
		References
			- Update reference to EnchartDOLib.dll v.2.1.0.3
		ReportViewer.vb
			- Import System.Text for StringBuilder (~line 9)
			- Added sub RegisterButtonHandlers that will add EventHandlers to the "Export Report" and "Print Report" buttons' click events (~lines 79-106)
			- Added sub SetupAuditLogger that will prepare the audit logger to be used (~lines 108-123)
			- Added sub BuildSelectionCriteriaString that takes a dictionary of query parameters and uses them to build a String to be logged (~lines 125-136)
			- Added subs ExportClick and PrintClick that handle the click events and create an audit log entry (~lines 138-144)
			- When a report is created, make calls to RegisterButtonHandlers, SetupAuditLogger, and BuildSelectionCriteriaString (~lines 39-41)
	* Inactivity Timeout Update
	* Use Timeout.dll to manage the inactivity timeout funcionality created in US28802
		References
			- Added reference to Timeout.dll
		ReportStartup.vb [Design]
			- Removed timer, 'timerIdleTimeout'
		NewReportRole.vb, ReportRolesEditor.vb, FormNewCategory.vb, FormNewReport.vb, ReportViewer.vb
			- Removed handlers
		ReportStartup.vb
			- Removed handlers
			- Import the Timeout library (~line 8)
			- Implement the ITimeoutable interface (~line 11)
			- Instantiate a TimeoutWatcher object (~line 112)
			- Methods to implement the ITimeoutable interface (~lines 1426-1439)

**************  (v. 2.4.3.0) 2015-07-16
	* US27350 - Automatic Logoff - When the determined amount of inactive time has been reached, close Admin Reports Module
	* Distinguish existing edit timeout timer from new idle timeout timer
		ReportStartup.vb [Design]
			- Renamed 'timerTimeout' to 'timerEditTimeout'
		ReportStartup.vb
			- Updated all references of 'timerTimeout' to 'timerEditTimeout'
			- Renamed timerTimeout_Tick to timerEditTimeout_Tick accordingly (~line 1347)
			- Renamed variable timeout_seconds to edit_timeout_seconds (~line 36)
			- Updated all references of timeout_seconds to edit_timeout_seconds accordingly
	* Add a timer to monitor the application's idle status
		ReportStartup.vb [Design]
			- Added a new timer, 'timerIdleTimeout'
		ReportStartup.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 1428-1517)
		NewReportRole.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 36-48)
		ReportRolesEditor.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 364-376)
		FormNewCategory.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 54-66)
		FormNewReport.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 96-108)
		ReportViewer.vb
			- Added event handlers and other logic for handling inactivity in the application (~lines 135-160)
	* Update TabIndex properties of the ReportStartup form
		ReportStartup.vb [Design]
			- Reordered the TabIndex properties of most controls on the form, treeReports's is set to 0 to be selected initially
	* Note: A new DOGlobablSetting is needed for this update, "AR_AutoLogoffTimeout"


**************  (v. 2.4.2.0) 2015-05-14
	* Davis, Jason - Updated to use Reports Web Service for SQL Server Migration
	
**************  (v. 2.4.1.3) 2015-02-27
	* US19471 - Database Migration - Stored Procedures in HICLegacyDatabase.dll
	* The HICLegacyDatabase.ReportsDatabase.AddReportCrossRecord method now uses a nullable date instead of a plain date
		References
			- Update reference to HICLegacyDatabase.dll 1.5.0.3
		ReportStartup.vb
			- In the RecursiveRebuild sub, changed the type of report_expiration to Nullable(Of Date) (~line 1143)

**************  (v. 2.4.1.2) 2014-01-09
	* Removed configuration settings for db connection strings (have been and will be managed by HICLegacyDatabase)
	* Update HICLegacyDatabase reference to v.*******

**************  (v. *******) 2013-07-10
	* Update HICLegacyDatabase library reference to point to the SharedLibs folder
		References
			- Changed reference location for HICLegacyDatabase to the SharedLibs folder

**************  (v. *******) 2012-12-14
	* Changed version number for end-of-year release

**************  (v. *******) 2012-12-11
	* Update to the language in the update dialog box
		CrystalReportsUpdateDialog.vb [Design]
			- Added new label lblMultipurpose2
			- Changed text value of lblMultipurpose
		CrystalReportsUpdateDialog.vb
			- Hide lblMultipurpse2 after the user clicks the "Update Now" button (~line 35)

**************  (v. *******) 2012-12-03
	* If the runtime file is missing, the application will now exit after displaying an error message
		CrystalReportsUpdateDialog.vb
			- In sub UpdateCrystalReports, wrap the runtime process exec in a Try...Catch block in case the file is missing or there are any other problems so that the application can
			  exit gracefully (~lines 39-45)

**************  (v. *******) 2012-11-26
	* Check for Crystal Reports v. 13.0.2000.0 installation on startup, install the Crystal Reports runtime if it's not up-to-date
		ReportStartup.vb [Design]
			- Changed the Text value for the form (ReportStartup) to "MIC Standard Reports"
		ReportStartup.vb
			- Added new function IsCrystalReportsUpToDate that checks to see if a Report object can be created via a Try...Catch block and returns True if the object is created (~lines 150-158)
			- Added new sub UpdateCrystalReports that begins the update process, then closes the application when complete (~lines 160-167)
			- On startup, use IsCrystalReportsUpToDate to check if the runtime has been installed, if not, begin the update process (~lines 62-64)
		CrystalReportsUpdateDialog.vb
			- New dialog form that runs the runtime installation process


**************  (v. 2.4.0.1) 2012-08-10
	* Upgrade project to VS 2012
	* Ensure compatibility with Crystal Reports 13
		References
			- New references required
				o CrystalDecisions.CrystalReports.Engine (v. 13.0.2000.0)
				o CrystalDecisions.ReportSource (v. 13.0.2000.0)
				o CrystalDecisions.Shared (v. 13.0.2000.0)
				o CrystalDecisions.Windows.Forms (v. 13.0.2000.0)

**************  (v. 2.4.0.0) 2012-08-10
	* Changed version number for GA
	* When creating selection criteria for an Observation report, send ObsTreatmentArea instead of TreatmentArea
		ReportViewer.vb
			- Check to see what type of report is being opened and use ObsTreatmentArea or TreatmentArea accordingly (~lines 64-68)
	
**************  (v. 2.3.1.5) 2012-08-08
	* Utilize new functionality for Observation reports
		ENChartReports
			- Updated HICLegacyDatabase reference to 1.4.1.20
		ReportStartup.vb
			- Modified sub EditItem to accept new parameter is_observation (~line 1035)
			- Use the new EditItem parameter to set the report's type as observation if necessary (~line 1043)
			- Modified sub AddItem to accept new parameter is_observation (~line 975)
			- Use the new EditItem parameter to set the report's type as observation if necessary (~line 996)
		FormNewReport.vb [Design]
			- Added check box to mark if the new report is an observation report or not
		FormNewReport.vb
			- Updated calls to subs AddItem and EditItem to pass the value from the new checkbox as is_observation (~lines 36, 38)
		FormNewCategory.vb
			- Updated calls to subs AddItem and EditItem to pass False for is_observation (~lines 31, 33)
		ReportViewer.vb
			- Build the selection criteria with the appropriate date if using an observation report (~lines 26-31, 38-39)

**************  (v. 2.3.1.4) 2011-05-17
	* Fixed reference errors. When the project was upgraded to VS2008, Crystal Report references were undesirably updated also.
		ENChartReports
			- Reverted Crystal Report references to v. 10.2.3600.0

**************  (v. 2.3.1.3) 2011-05-13
	* Bug when saving a facility's user-created categories would delete user-created categories in other facilities
		ReportStartup.vb
			- Changed call to ClearNonFixedFacilityReportCategoryCrossByRole to also pass the facility_id (~line 411)

**************  (v. 2.3.1.2)

	* - Added better error handling when report settings cannot be loaded for a facility.
		ReportsSettings.vb
			- Added checks for loading of report settings in LoadReportSettings sub. Show error message and set default settings if they could not. (~ lines 18-53)

**************  (v. 2.3.1.0)

	* - Fixed bug when saving as admin/admin, if multi-facility site, would clear cross records table for all facilities and re-build for only one
		ReportStartup.vb
			- Made change in SaveChanges sub so that if only one facility is showing (admin/admin) then ReportsDatabase.ClearFacilityReportCategoryCross is called with that facility's OID as a parameter (~lines 416-420)	

	* - Fixed bug when closing the ReportRolesEditor when changes have been made but no saved. If 'No' was clicked, dialog would close anyway.
		ReportRolesEditor.vb
			- Changed ReportRolesEditor_FormClosing Sub, added else statement to check for dialog result and moved clean up inside (~lines 229-231)

	* - Fixed bug that would clear listRoleReports as the Facility DDL was changed
		ReportRolesEditor.vb
			- Added is_facility_changing boolean variable (~line 14)
			- When the facility is changed (cboFacility_SelectedIndexChanged), made call to save the active role reports (~lines 317-319)
			- Added additional condition to cboRoles_SelectedIndexChanged to check if the facility change has already saved the active role reports (~lines 330-332)

**************  (v. 2.3.0.1)

	* - Made ENChartReports a single instance application
	

**************  (v. 2.3.0.x)

	* - Fixed bug when dragging report onto a closed category

	* - Disabled dragging of any categories
		ReportStarup.vb
			- Added ElseIf to see if IsCategory (~lines 765-771)

	* - Disabled ctrl+ dragging
		ReportStartup.vb
			- Removed login for KeyState = 9 (ctrl key) (~lines 451-455)

	* - Fixed bug in report query building with treatment area
		ReportViewer.vb
			- Added (+) to concatenate instead of overwriting (~line 59)

	* - Changed behavior of "Move Up", "Move Down" for subcategories
		ReportStartup.vb
			- Changed if logic to exclude subcategories (~line 678)

	* - Fixed bug when dragging a node to an untargetable location, would move the node to the bottom of the original list
		ReportStartup.vb
			- Moved MoveNode and SetNodeIndex outside of the if statement (~line 481-487)

	* - Fixed bug when dragging item over empty category, sometimes would add item (if the category was "expanded")
		ReportStartup.vb
			- Set context_node to the tree reports selected node in delete logic (~line 737)
			- Added logic to set Expanded property of a deleted item's parent to false if it was the last item removed (~lines 746-748, 757-759, 919-921, 925-927)

**************  (v. 2.2.0.1x)

	* - Added dependency to HICLegacyDatabase.dll

ReportRolesEditor.vb
	- Added overloaded ShowDialog Function that accepts an array of FacilityList and replaces the Form Load Sub (~lines 16-24)
	- Changed LoadFacilitiesList Sub to accept an array of FacilityList and then load the Facility drop down from that instead of retrieving its own list (~lines 26-36)
	- Changed call to LoadFacilitiesList in ShowDialog function to pass along the array of FacilityList (~line 21)

ReportStartup.vb
	- Changed call to ReportRolesEditor.ShowDialog to pass along array of FacilityList (~line 1050)
	- Updated Function CanDropIntoNode to not allow drop onto a category node to prevent the node from opening automatically (~line 720)
	- Added clean-up code in treeReports_MouseDown Sub to fix strange drag behavior bug (~lines 587-589)
	- Added code to support "Move Up" and "Move Down" context items (~lines 591-680, 885-898)

ReportStartup.vb [Design]
	- Added context menu items "Move Up" and "Move Down"

ReportsSettings.vb
	- Added this class to load and hold the reports settings values

**************  (v. 2.2.0.0x)

* Many more changes made due to user roles

**************  (v. 2.1.1.0)

ReportsDatabase.vb
	- Removed Unused Subs and Functions

**************  (v. 2.1.1.0)

* Update to bring in line with v. 2.1.0.1
* Major overhaul to correct Report List issues with User Roles

ReportsDatabase.vb
	- Added ClearNonFixedFacilityReportCategoryCross

ReportStartup.vb
	- Calling ClearNonFixedFacilityReportCategoryCross instead of ClearFacilityReportCategoryCross (~line 338)
	- If the current user is admin/admin, show all reports (~lines 94-96)
	- If no role assigned, disable edit button (~line 99)

**************  (v. 2.0.1.0)

ENChartReports

ReportStartup_Dustin [Design]
	- Added Treatment Area label and Combo Box (ddlTreatmentArea)
	- Added Admin Menu (menuAdmin)
ReportStartup_Dustin.vb
	- Added ClearTreatmentAreas Sub and LoadTreatmentAreas Sub (~lines 554-567)
	- Passing selected treatment area to the Report Viewer's Show_Report Sub (~line 95)
	- In the PopulateReportTree Sub, added call to LoadTreatmentAreas (~line 165)
	- Added logic to show Admin Menu is user is Admin (~lines 85-88)
	- Changed tree loading logic, if no user/facility is passed, call PopulateAllReports (~line 82). Otherwise, call PopulateRoleReports (~line 78)
	- Added PopulateRoleReports Sub (~lines 215-264)
	- Added PopulateReportCategoryForRole Sub (~lines 285-302)
ReportViewer.vb
	- Added treatment_area as a parameter for the Show_Report Sub (~line 3)
	- Added TreatmentArea as parameter to Report SQL (~line 56)
ReportStartup.vb
	- Calling UpdateSchema to update the changes in the database to match with the ENChartDOLib.dll (~line 51)

version 0.0.0.13, ??/??/2008
	- added export button, modified facilityreportlist table to include reporttype column
	
version 0.0.0.14 4/21/2009
	- modified picturebox1 on ReportStartup form to load image from image file
	- modified ReportStartup form text, replaced EN-Chart with HIC
	
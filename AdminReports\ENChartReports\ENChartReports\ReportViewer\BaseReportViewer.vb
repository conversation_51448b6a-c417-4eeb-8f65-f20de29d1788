Imports EnchartDOLib
Imports ENChartReports

Public Class NonAbstractReportViewer
    Inherits BaseReportViewer

    Public Overrides Sub Show_Report(facilityReport As DOFacilityReport, treatment_area As String, mindate As Date, maxdate As Date, datetype As String, pf As ReportStartup)
        Throw New NotImplementedException()
    End Sub
End Class


Public Class BaseReportViewer
    Implements IReportViewer

    Protected Friend Property ParentReportStartup As ReportStartup
    Protected Friend Property SelectionCriteria As String

    'Public MustOverride Sub Show_Report(facilityOID As Integer, treatment_area As String, reportFilename As String, mindate As Date, maxdate As Date, datetype As String, reportinfo As ReportList, pf As ReportStartup) Implements IReportViewer.Show_Report
    Public Overridable Sub Show_Report(facilityReport As DOFacilityReport, treatment_area As String, mindate As Date, maxdate As Date, datetype As String, pf As ReportStartup) Implements IReportViewer.Show_Report

    End Sub

    Protected Friend Sub RegisterButtonHandlers()

        ' Look for the ToolStrip within the CrystalReportViewer's controls
        For Each c As Control In CrystalReportViewer1.Controls
            Select Case c.GetType.ToString

                ' Found it!
                Case "System.Windows.Forms.ToolStrip"
                    Dim strip As ToolStrip = CType(c, ToolStrip)

                    ' Now let's look through each of the the ToolStrip's buttons for the Export and Print buttons
                    For Each o As Object In strip.Items
                        If o.GetType.ToString = "System.Windows.Forms.ToolStripButton" Then
                            Dim ts_button As ToolStripButton = CType(o, ToolStripButton)

                            Select Case ts_button.ToolTipText

                                ' Found them! Let's add the appropriate handlers to their Click events
                                Case "Export Report"
                                    AddHandler ts_button.Click, AddressOf ExportClick
                                Case "Print Report"
                                    AddHandler ts_button.Click, AddressOf PrintClick
                            End Select
                        End If
                    Next
            End Select
        Next
    End Sub

    Protected Friend Sub SetupAuditLogger()

        Dim current_user As DOUser = ParentReportStartup.CurrentUser
        Dim current_facility As DOFacility = ParentReportStartup.CurrentFacility

        If current_facility IsNot Nothing Then
            Dim log_helper As New BasicAuditLogHelper(current_user, current_facility)
            Dim log_writer As New DefaultAuditLogWriter()

            Try
                AuditLogger.Init(log_helper, log_writer)
                AuditLogger.SetIsRemoteSession(SystemInformation.TerminalServerSession)
            Catch ex As Exception

            End Try
        Else
            Throw New Exception("ReportViewer.vb, SetupAuditLogger - Error creating Audit Logger, facility could not be looked up")
        End If

    End Sub

    Protected Friend Sub ExportClick(ByVal sender As Object, ByVal e As EventArgs)
        AuditLogger.Reports.AdminReportsLogExport(SelectionCriteria)
    End Sub

    Protected Friend Sub PrintClick(ByVal sender As Object, ByVal e As EventArgs)
        'If MessageBox.Show($"This report contains sensative information.{vbCrLf}{vbCrLf} Do you wish to proceed?", "Sensative Data Warning", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> DialogResult.OK Then
        '    Exit Sub
        'End If
        AuditLogger.Reports.AdminReportsLogPrint(SelectionCriteria)
    End Sub

    Protected Friend Sub ReportViewer_Resize(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Resize
        CrystalReportViewer1.Height = Me.Height - 30
        CrystalReportViewer1.Width = Me.Width - 10
    End Sub

    Protected Friend Sub LoadReport(ByVal filepath As String)
        CrystalReportViewer1.ReportSource = filepath
        Me.Show()
    End Sub

    Protected Friend Sub LoadReport(ByVal filepath As String, ByVal sql_selection As String)
        CrystalReportViewer1.ReportSource = filepath
        CrystalReportViewer1.SelectionFormula = sql_selection
        CrystalReportViewer1.RefreshReport()
        Me.Show()
    End Sub
End Class

#Region "Imports"
Imports System.ComponentModel
Imports System.Data
Imports System.IO
Imports System.Reflection
Imports System.Runtime
Imports System.Runtime.Intrinsics
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Imports DevExpress.Data.Filtering
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB
Imports DevExpress.XtraBars
Imports EnchartDOLib
Imports EnchartDOLib.SimpleResult
Imports ENChartReports
Imports Microsoft.Extensions.Configuration
'Imports McKessonIntelligentCoding.Common.Contracts
Imports SSP = System.Security.Principal


#End Region

Public Class MDIFrameForm
    Inherits DevExpress.XtraEditors.XtraForm

    Private secMan As SecurityAuthManager

    Class APLFormFactory
        Public Sub New()

        End Sub

        Public Shared Function GetAPLForm(parentForm As MDIFrameForm) As APLBaseForm
            If ECGlobals.CurrentUser Is Nothing Then
                Return Nothing
            End If

            Dim apl As APLBaseForm = Nothing
            apl = New APLForm(parentForm)
            'If ECGlobals.CurrentUser.IsAdmin Then
            '    apl = New APLForm(parentForm)
            'Else
            '    apl = New APLForm
            'End If

            Return apl
        End Function
    End Class

#Region "Fields"

    Dim bIgnoreEvents As Boolean = False

    Public LastUserInput As New DetectActivity
    Private pTempFormPtr As Form = Nothing
    Private skinMask As String = "Skin: "
    Private _firstTime As Boolean
    Private _spalshScreen As SplashScreen

#End Region 'Fields

#Region "Constructors"

    ' Dim AT As Timer
    Public Sub New()
        MyBase.New()

        Try
            'JJC 07.30.19 Take advantage of multicore jit
            ProfileOptimization.SetProfileRoot(Application.StartupPath)
            ProfileOptimization.StartProfile("Startup.Profile")

        Catch ex As Exception
            'Is not critical, so we'll just ignore it.
        End Try

        WindowsFormsSettings.LoadApplicationSettings()
        Me.SplashScreen.Show()
        Application.DoEvents()
        SpinUpNewFormOnBackGroundThread()

        ECGlobals.MainForm = Me

        'This call is required by the Windows Form Designer.
        InitializeComponent()

        'DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle("The Asphalt World")
#If DEBUG Then
        FormBorderStyle = FormBorderStyle.Sizable
        ControlBox = True
        MaximizeBox = True
#Else
        MaximizeBox = True
        FormBorderStyle = FormBorderStyle.Sizable
#End If

        secMan = Auth_MoreSecure.Init()
        AddHandler secMan.InvalidCertDetected, AddressOf InvalidCertDetected

        InitializeNetCoreConfiguration()
    End Sub

    Private Shared Sub InitializeNetCoreConfiguration()
        Dim builder As New ConfigurationBuilder()
        builder.SetBasePath(Directory.GetCurrentDirectory()) _
        .AddJsonFile("appsettings.json", optional:=True, reloadOnChange:=True) _
        .AddUserSecrets(Of MDIFrameForm)()  ' Replace 'YourStartupClass' with the appropriate class

        Dim Configuration As IConfiguration = builder.Build()
        ECGlobals.Configuration = Configuration
    End Sub

    Private Sub MDIFrameForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            secMan.GetStartupGlobalSettings()
        Catch ex As Exception
            MessageBox.Show(Me, $"{ex.Message}", "Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Me.Size = New Size(1440, 850)

        Try

            Dim cs = AICUserSpecificSettingsHelper.RetrieveAICUserSpecificSettings
            If cs.MainWindowState = FormWindowState.Maximized Then
                Me.WindowState = FormWindowState.Maximized
            Else
                If cs?.MainAppWindowSize.HasValue Then
                    Me.Size = cs.MainAppWindowSize
                    Me.CenterToScreen()
                End If
            End If

            ' AICUserSpecificSettingsHelper.StoreAICUserSpecificSettings(cs)
        Catch ex As Exception

        End Try

        Dim versionString As String = String.Empty
        Try
            ' Get the current assembly
            Dim assembly As Assembly = Assembly.GetExecutingAssembly()
            ' Get the assembly version
            versionString = assembly.GetName().Version?.ToString

            ''System.AppContext.BaseDirectory'.
            Dim loc1 = System.Reflection.Assembly.GetExecutingAssembly().Location
            Dim loc2 = AppContext.BaseDirectory
            Dim loc3 = Environment.Version
            'Dim fileVersion As String = FileVersionInfo.GetVersionInfo(System.Reflection.Assembly.GetExecutingAssembly().Location).FileVersion
            Dim fileVersion As String = FileVersionInfo.GetVersionInfo(loc2).FileVersion

        Catch ex As Exception

        End Try
        Me.Text = String.Format("{0} V.{1}", ECGlobals.ProductNameString, versionString)
        Me.bsiVersion.Caption = "V." & versionString

        '--- The next few lines somehow force the application to not magically switch to returning a WindowsPrincipal Object...
        'but still changes when the coding report is run...
        Dim ii As New SSP.GenericIdentity("NewUser")
        Dim ip As New SSP.GenericPrincipal(ii, Nothing)
        Threading.Thread.CurrentPrincipal = ip
        AppDomain.CurrentDomain.SetThreadPrincipal(ip)

        'Me.bsiVersion.Caption = "V." & fileVersion

        Try

            'ConnectToESS(SecurityAuthManager.GetManager(MicSettingsBuilder.Settings.ESSUrl))
            'If TryCast(XpoDefault.DataLayer, SimpleDataLayer) IsNot Nothing Then
            '    If Not DirectCast(XpoDefault.DataLayer, SimpleDataLayer).ConnectionProvider.GetType.ToString.Contains("ESSDataStoreProvider") Then
            '        MessageBox.Show(Me, "A direct connection to the database is being used and may not be secure.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '    End If
            'ElseIf TryCast(XpoDefault.DataLayer, ThreadSafeDataLayer) IsNot Nothing Then
            '    If Not DirectCast(XpoDefault.DataLayer, ThreadSafeDataLayer).ConnectionProvider.GetType.ToString.Contains("ESSDataStoreProvider") Then
            '        MessageBox.Show(Me, "A direct connection to the database is being used and may not be secure.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '    End If
            'Else
            '    MessageBox.Show(Me, "A direct connection to the database is being used and may not be secure.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            'End If


            Auth_MoreSecure.Init()
        Catch ex As Exception
            DbPollingTimer.Stop()

            Me.SplashScreen.Close() 'for some strange reason, if we don't close this first, we automically
            'return from the below messagebox before the user gets to see it or click any damn butotn.
            MessageBox.Show(Me, "(MDIFrameForm_Load) Error:" & vbCrLf & vbCrLf & ex.Message,
                            "Datalayer Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Error)

            Dim uhoh As New SimpleResult(Of Boolean)

            Throw New Exception($"{uhoh.GetExceptionChainMessage(ex)}")

            Environment.Exit(0)
            Return 'seems odd to me... but this is necessary.
        End Try

        ECGlobals.WorkingDay = Today

        'ask XPO to make sure there is table for each of our Data Objects...
        'UpdateDBSchema()

        bbiChangeSecurityQuestions.Visibility = BarItemVisibility.Never

        If ECGlobals.AutoLogOffTimeOut > 0 Then
            Me.IdleUserTimer.Interval = 60 * 1000
            Me.IdleUserTimer.Start()
        End If

        DbPollingTimer.Enabled = True
        DbPollingTimer.Interval = 15 * 60 * 1000 '5 * 60 * 1000
        Me.DbPollingTimer.Start()

        'ECLog.WriteEntry("(MDIFrameForm_Load) Starting Up ----------------------------------------", TraceEventType.Information)

        'DevExpress.XtraEditors.WindowsFormsSettings.AllowHoverAnimation = DevExpress.Utils.DefaultBoolean.True
        'DevExpress.XtraEditors.WindowsFormsSettings.PopupAnimation = DevExpress.XtraEditors.PopupAnimation.System
        'DevExpress.XtraEditors.WindowsFormsSettings.AnimationMode = AnimationMode.DisableAll
    End Sub

    Public Sub StopDbPollingTimer()
        DbPollingTimer.Stop()
    End Sub
    Public Sub StartDbPollingTimer()
        DbPollingTimer.Start()
    End Sub

    Private Sub MDIFrameForm_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Activated
        Auth_MoreSecure.ForceLogInWindowToFront()
        Try
            If pTempFormPtr IsNot Nothing Then
                pTempFormPtr.Select()
                pTempFormPtr.Refresh()
            End If
        Catch ex As Exception
            'this is never a good idea jonathan!
        End Try
    End Sub

    Private Sub MDIFrameForm_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing

        Try
            If Auth_MoreSecure.logInExternalProcess IsNot Nothing Then
                Auth_MoreSecure.logInExternalProcess.Kill()
            End If
        Catch ex As Exception

        End Try

        Try
            'Application.Log.DefaultFileLogWriter
            Dim cs = AICUserSpecificSettingsHelper.RetrieveAICUserSpecificSettings
            cs.MainWindowState = Me.WindowState
            cs.MainAppWindowSize = Me.Size
            AICUserSpecificSettingsHelper.StoreAICUserSpecificSettings(cs)

        Catch ex As Exception

        End Try

        Session.DefaultSession?.DataLayer?.Connection?.Close()

    End Sub

    Sub InitMenus()

        'ECGlobals.Reset() 'JJC 04.02.20 Removed
        Dim fs = ECGlobals.FacilityTipVersionCISpecific

        If ECGlobals.CurrentUser.IsInRole(ECRoles.Confg) Then 'OrElse ECGlobals.CurrentUser.IsInRole(ECRoles.Admin) Then
            Me.bsiSettings.Visibility = BarItemVisibility.Always
        Else
            Me.bsiSettings.Visibility = BarItemVisibility.Never
        End If

        If ECGlobals.CurrentUser.IsInRole(ECRoles.Developer) Then
            Me.bsiUtils.Visibility = BarItemVisibility.Always
            btnAlias.Visibility = BarItemVisibility.Always
            bbiDataRequester.Visibility = BarItemVisibility.Always
            SkinDropDownButtonItem1.Visibility = BarItemVisibility.Always
            SkinPaletteDropDownButtonItem2.Visibility = BarItemVisibility.Always
        Else
            Me.bsiUtils.Visibility = BarItemVisibility.Never
            btnAlias.Visibility = BarItemVisibility.Never
            bbiDataRequester.Visibility = BarItemVisibility.Never
            SkinDropDownButtonItem1.Visibility = BarItemVisibility.Never
            SkinPaletteDropDownButtonItem2.Visibility = BarItemVisibility.Never
        End If

        If ECGlobals.CurrentUser.IsInRole(ECRoles.Admin) OrElse ECGlobals.CurrentUser.IsInRole(ECRoles.DevAdmin) Then
            bbiUsersAndPermissions.Enabled = True
            '  bsiCDMViewer.Enabled = True 'still exists but is no longer used. bbiCdmEditor has taken it's place
            bbAuditLogViewer.Enabled = True
            bbiCdmEditor.Enabled = True
            bbMedicationsViewer.Enabled = True
            bbiDeductedTimes.Enabled = fs.EnableObservationTab 'True
            bsiManualEntry.Visibility = BarItemVisibility.Always
            bbiEmailConfig.Enabled = True
        Else
            bsiManualEntry.Visibility = BarItemVisibility.Never
            bbiUsersAndPermissions.Enabled = False
            '   bsiCDMViewer.Enabled = False
            bbAuditLogViewer.Enabled = False
            bbMedicationsViewer.Enabled = False
            bbiCdmEditor.Enabled = False
            bbiDeductedTimes.Enabled = False
            bbiEmailConfig.Enabled = False
        End If

        'if in debug mode, show the "test" menu
        '#If CONFIG = "JJCDebug" Then

        'Testing ----------------------------------------------------------------------------------------
        'ECGlobals.UseUserPaintStyles = True
        'If ECGlobals.UseUserPaintStyles Then 'checks dev permission
        '    If Not String.IsNullOrEmpty(ECGlobals.CurrentUser.PaintStyle) Then
        '        DevExpress.Skins.SkinManager.EnableFormSkins()
        '        Me.iPaintStyle.Visibility = BarItemVisibility.Always
        '        Me.InitPaintStyle("The Asphalt World")
        '        Me.InitPaintStyle(ECGlobals.CurrentUser.PaintStyle)
        '    Else
        '        DevExpress.Skins.SkinManager.DisableFormSkins()
        '    End If
        'Else
        '    DevExpress.Skins.SkinManager.DisableFormSkins()
        '    Me.iPaintStyle.Visibility = BarItemVisibility.Never
        '    Me.InitPaintStyle("The Asphalt World")
        'End If
        'Testing ----------------------------------------------------------------------------------------

        If ECGlobals.CurrentUser.IsInRole(ECRoles.Admin) Then
            bsiMaintEditLists.Enabled = True
            bsiMaintEditLists.Visibility = BarItemVisibility.Always

        Else
            bsiMaintEditLists.Enabled = False
            bsiMaintEditLists.Visibility = BarItemVisibility.Never

        End If

        If ECGlobals.CurrentUser.IsInRole(ECRoles.DevAdmin) Then
            bbiBaseLine.Visibility = BarItemVisibility.Always
        Else
            bbiBaseLine.Visibility = BarItemVisibility.Never
        End If

        If ECGlobals.UseActiveDirectoryLogIn Then
            bbiChangePassword.Visibility = BarItemVisibility.Never
        End If

#If CONFIG = "JJCDebug" Then
        bbiConvertEspcodesTo2018.Visibility = BarItemVisibility.Always
        bbiBetaCodeDeleteStuff.Visibility = BarItemVisibility.Always 'gen espocdes
        BarButtonItem50.Visibility = BarItemVisibility.Always 'delete stuff
#End If
        UpdateNurseLists()
        UpdateManualEntryMenuOptions()
        InitPrevChartVerMenu()
    End Sub

    Private Sub InitPaintStyle(ByVal UserPaintStyleName As String)
        If DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSkinName = UserPaintStyleName Then
            Return
        End If

        DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(UserPaintStyleName)
        BarManager1.GetController().PaintStyleName = "Skin"
        ' solutionExplorer1.barManager1.GetController().PaintStyleName = "Skin"
        iPaintStyle.Caption = skinMask & UserPaintStyleName 'e.Item.Caption
        iPaintStyle.Hint = iPaintStyle.Caption
        iPaintStyle.ImageIndex = -1

        BarManager1.GetController().ResetStyleDefaults()

        'If PaintStyleName = "Default" Then
        '    PaintStyleName = "The Asphalt World"
        'End If

        'iPaintStyle.ImageIndex = -1 'item.ImageIndex
        'iPaintStyle.Caption = PaintStyleName ' item.Caption
        'iPaintStyle.Hint = PaintStyleName ' item.Description
        'BarManager1.GetController().ResetStyleDefaults()
    End Sub

    Private MaintenanceModeSettings As MaintenanceSettingsHelper

    Private Function IsMaintenanceModeEanbled() As Boolean
        If MaintenanceModeSettings IsNot Nothing AndAlso MaintenanceModeSettings.IsOn Then
            Return True
        Else
            MaintenanceModeSettings = MaintenanceSettingsHelper.GetSettings()
            If MaintenanceModeSettings.IsOn Then Return True
        End If
        Return False
    End Function

    Private Async Sub DbPollingTimerTick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DbPollingTimer.Tick
        'Dim bt = New Thread(New ThreadStart(Sub()
        '                                        DbPollingTimerTickHandler()
        '                                    End Sub))
        'bt.Start()
        Await DbPollingTimerTickHandler()
        'Debug.WriteLine("boo!")
    End Sub

    Private Async Function DbPollingTimerTickHandler() As Task
        Debug.WriteLine($"DbPollingTimerTickHandler: {Now.ToLongTimeString}")
        'Await Task.Delay(30000)

        If ECGlobals.CurrentUser Is Nothing Then Return
        If ECGlobals.CurrentUser?.IsTheAICAdmin Then
            ECGlobals.AdminUserWasLoggegIn = True
        End If
        If Not ECGlobals.AdminUserWasLoggegIn Then
            If ECGlobals.CurrentUser Is Nothing OrElse Not ECGlobals.CurrentUser.IsTheAICAdmin Then
                If IsMaintenanceModeEanbled() Then
                    StatusBarMessage.Visibility = BarItemVisibility.Always
                    If ShutDownTimer.Enabled = False Then
                        ShutDownTimer.Enabled = True

                        Dim minsLeft = MaintenanceModeSettings.ForcefullyQuitTime.Value.Subtract(Now()).Minutes
                        If minsLeft = 0 Then minsLeft = 1

                        Dim dlg = New ShutDownMsgForm(MaintenanceModeSettings.Message, $"Shutting down in {minsLeft} min(s).")
                        dlg.Show()

                        Await Task.Delay(30000)
                        dlg.Close()
                        dlg.Dispose()
                    End If
                End If
            End If
        End If

        If ECGlobals.UpdatePending <> ECGlobals.UpdatePendingEnum.None Then
            Return
        End If

        ECGlobals.CheckForPublishUpdate()
        If ECGlobals.IsShutDownNeeded(True) Then

        End If
    End Function

    Private Sub InvalidCertDetected(Optional msg As String = "N/A")
        StatusBarMessage.Enabled = True
        StatusBarMessage.Visibility = BarItemVisibility.Always
        StatusBarMessage.Caption = "****************  Invalid SSL Server Certificate Detected   ****************"

        'JJC We should probably show a Messege before forcefully exiting, but doing so (i would think) makes it easier to find and bypass for hacking
        'So for now ... we wont' show it.

        MessageBox.Show($"Invalid SSL Server Certificate Detected", "MITM Suspected", MessageBoxButtons.OK, MessageBoxIcon.Error)
        ' Debug.Assert(False, $"Invalid SSL Server Certificate Detected")
        Environment.Exit(0)
        Return

    End Sub

    Private Sub SpinUpNewFormOnBackGroundThread()

        Dim t = New Thread(Sub()
                               preCreatedChartForm = New ChartFormV18()
                           End Sub)
        t.SetApartmentState(ApartmentState.STA)
        t.Priority = ThreadPriority.Highest
        t.IsBackground = True
        t.Start()
    End Sub

#End Region 'Constructors

#Region "Properties"

    Private _chartSelectScreen As APLBaseForm
    Public Property ChartSelectScreen() As APLBaseForm 'ChartSelectForm
        Get
            If _chartSelectScreen Is Nothing Then
                '_chartSelectScreen = New ChartSelectForm
                _chartSelectScreen = APLFormFactory.GetAPLForm(Me)
            End If
            Return _chartSelectScreen
        End Get

        Set(ByVal value As APLBaseForm)
            _chartSelectScreen = value
        End Set
    End Property

    Public Property FirstTime() As Boolean
        Get
            Return _firstTime
        End Get
        Set(ByVal value As Boolean)
            _firstTime = value
        End Set
    End Property

    Public ReadOnly Property SplashScreen() As SplashScreen
        Get
            If _spalshScreen Is Nothing Then
                _spalshScreen = New SplashScreen
            End If
            Return _spalshScreen
        End Get
        'Set(ByVal value As SplashScreen)
        '    _spalshScreen = value
        'End Set
    End Property

#End Region 'Properties

#Region "Methods"

    Public Sub OpenNewChart(ByVal DOS As Date)
        ECGlobals.CheckDBConnection()
        If Me.ShouldForceLogOut Then Return
        '  Me.Cursor = Cursors.WaitCursor

        Me.CreateChartForm(DOS).Show()
    End Sub

    'This is called when the program first starts to open a chart....
    Public Sub OpenNewChart()
        Try
            ProfileOptimization.StartProfile("OpenNewChart.Profile")
        Catch ex As Exception

        End Try

        'ECGlobals.CheckDBConnection()
        'JJC 07.24.19 Removed for performance
        'If Me.ShouldForceLogOut Then Return
        'Me.Cursor = Cursors.WaitCursor
        Dim sw As New Stopwatch
        sw.Start()
        Me.CreateChartForm.Show()
        sw.Stop()
        Debug.WriteLine("MDIForm OpenNewChart took:" & sw.ElapsedMilliseconds)
        ' MessageBox.Show($"MDIForm OpenNewChart took {sw.ElapsedMilliseconds}")
        ECLog.WriteEntry("MDIForm OpenNewChart took:" & sw.ElapsedMilliseconds)
    End Sub

    Private preCreatedChartForm As ChartBaseForm

    Private Sub InitializeChartForm(frm As ChartBaseForm)
        If frm IsNot Nothing Then
            RemoveHandler frm.ForceLoggOut, AddressOf ForceLogOutEventHandler
            AddHandler frm.ForceLoggOut, AddressOf ForceLogOutEventHandler

            'now do the same for childform closed event
            RemoveHandler frm.FormClosed, AddressOf ChildFormClosed
            AddHandler frm.FormClosed, AddressOf ChildFormClosed

            'now do same for child shown event
            RemoveHandler frm.Shown, AddressOf ChildFormShown
            AddHandler frm.Shown, AddressOf ChildFormShown


            Dim validDateRangeProvider = TryCast(frm, IValidDateRangeProvider)
            If validDateRangeProvider IsNot Nothing Then
                ' validDateRangeProvider.ValidDateRange = ECGlobals.CurrentFacility.ConfigInstanceVersion.ValidDateRange
                AddHandler validDateRangeProvider.ValidDateRangeChanged, AddressOf ValidDateRangeChanged

            End If

            frm.MdiParent = Me
            frm.CustomMDIParent = Me
        End If
    End Sub

    Private Sub ChildFormShown(sender As Object, e As EventArgs)
        UpdateFooterDates()
    End Sub

    Private Sub ValidDateRangeChanged(sender As Object, e As EventArgs)
        Dim dateProvider = TryCast(Me.ActiveMdiChild, IValidDateRangeProvider)
        If dateProvider Is Nothing Then Return
        UpdateFooterDates()
    End Sub

    Private Sub ChildFormClosed(sender As Object, e As FormClosedEventArgs)
        Dim frm As ChartBaseForm = TryCast(sender, ChartBaseForm)
        RemoveHandler frm.ForceLoggOut, AddressOf ForceLogOutEventHandler
        RemoveHandler frm.FormClosed, AddressOf ChildFormClosed

        Dim validDateRangeProvider = TryCast(frm, IValidDateRangeProvider)
        If validDateRangeProvider IsNot Nothing Then
            RemoveHandler validDateRangeProvider.ValidDateRangeChanged, AddressOf ValidDateRangeChanged
        End If
        If MdiChildren.Length <= 1 Then
            UpdateFooterDates(True)
        End If
        'UpdateFooterDates()
    End Sub

    Private Function CreateChartForm(ByVal FormClassName As String) As ChartBaseForm
        Dim sw As New Stopwatch
        sw.Start()
        Application.UseWaitCursor = True
        Try
            Dim ts2 As String = "ENChartCAC." & FormClassName
            Dim t As Type = Type.GetType(ts2)
            If t Is Nothing Then
                Dim ts As String = $"The specified form ({FormClassName}) is no longer supported."
                ECLog.WriteEntry(ts, TraceEventType.Critical)
                Throw New Exception(ts)
            End If

            If preCreatedChartForm IsNot Nothing AndAlso preCreatedChartForm.GetType() = t Then
                ECLog.WriteEntry($"(CreateChartForm) - using preCreatedForm")
                Dim returnForm = preCreatedChartForm
                preCreatedChartForm = Nothing
                SpinUpNewFormOnBackGroundThread()
                InitializeChartForm(returnForm)
                Return returnForm
            Else
                Dim o As ChartBaseForm = Activator.CreateInstance(t)
                InitializeChartForm(o)
                Return o
            End If

        Finally
            sw.Stop()
            'Debug.WriteLine($"MDIFrameForm.CreateChartForm({FormClassName}) took {sw.Elapsed}")
            Application.UseWaitCursor = False
        End Try
    End Function

    Public Function CreateChartForm(ByVal pci As DOConfigInstance) As ChartBaseForm
        If pci Is Nothing Then
            Throw New ArgumentNullException(NameOf(pci))
        End If

        Dim frm As ChartBaseForm = Nothing
        Try
            frm = CreateChartForm(pci.ActiveFormClassName) 'USE Form that was defined at CI creation
        Catch ex As Exception
            Dim msg = $"The ActiveFormClassName ({pci.ActiveFormClassName}) for CI({pci.Oid}) is no longer supported"
            Throw New Exception($"{msg}", ex)
        End Try
        frm.ConfigInstance = pci
        frm.SkipConfigInstanceInit = True

        InitializeChartForm(frm)

        Return frm
    End Function

    Public Function CreateChartForm(Optional ByVal pchart As DOChart = Nothing, Optional ByVal ShowOriginal As Boolean = False) As ChartBaseForm
        Try
            Dim frm As ChartBaseForm = Nothing

            If pchart Is Nothing Then
                'Ok... we need open the form/configinstance that is active for the current configinstance...
                frm = CreateChartForm(ECGlobals.CurrentFacility.ConfigInstanceVersion.ActiveFormClassName)
            Else
                If ShowOriginal Then
                    frm = CreateChartForm(pchart.ConfigInstanceVersion.ActiveFormClassName)
                Else
                    frm = CreateChartForm(pchart.ConfigInstanceVersion.MajorChangeDate) 'jjc 08.13.15
                End If
            End If

            InitializeChartForm(frm)

            Return frm
        Finally
            Application.UseWaitCursor = False
        End Try
    End Function

    Public Function CreateChartForm(ByVal DOS As Date) As ChartBaseForm
        Dim frm As ChartBaseForm
        Dim ci As DOConfigInstance = Nothing

        'When ECGlobals.ConfigInstanceDict is created, ci's with "blank" activatedDate are skipped...
        Dim rd As New SortedList(Of Date, DOConfigInstance)(ECGlobals.ConfigInstanceDict, New DateReverseComparer)

        Dim d As Date
        'Iterate through, from most recent to oldest, based of MajorChangeDate
        'oldest being where 1967 is older the 2009 ... =/
        For Each d In rd.Keys
            If d <= DOS Then
                ci = ECGlobals.ConfigInstanceDict(d)
                Exit For
            End If
        Next

        'if the user entered a date prior to the earliest configInstance, just use the oldest (first) one...
        If ci Is Nothing Then
            ci = ECGlobals.ConfigInstanceDict(d)
        End If

        If ci Is Nothing Then
            ECLog.WriteEntry($"CreateChartForm({DOS}) Error, a valid ConfigInstance could not be found for date specified.", TraceEventType.Error, True)
        End If
        Debug.Assert(ci IsNot Nothing)

        Try
            frm = CreateChartForm(ci)
        Catch ex As Exception
            Dim msg = $"The CI for {DOS} uses a Chartform that appears to no longer be supported."
            ECLog.WriteExceptionError(msg, ex, False)
            Throw New Exception(msg, ex)
        End Try

        InitializeChartForm(frm)
        Return frm
    End Function


    Public Sub ActivePatientLists_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiActivePatientLists.ItemClick
        ECLog.WriteEntry("(ActivePatientLists_ItemClick)", TraceEventType.Verbose)
        ' If Me.ShouldForceLogOut Then Return
        If ECGlobals.IsShutDownNeeded(True) Then Return

        ' ''ECGlobals.CheckForUpdate()
        ' ''DenyAccessIfUpdatePending will call CheckForUpdate
        ''If ECGlobals.ShouldDenyAccessIfUpdatePending Then Return
        Me.APLClick(Me)

        'If Me.ShouldForceLogOut Then Return
    End Sub

    Private Sub DeleleteMouseClicks()
        Try
            LastUserInput.IgnoreMouseClicks = True
            Application.DoEvents()
        Catch ex As Exception
        Finally
            LastUserInput.IgnoreMouseClicks = False
        End Try
    End Sub

    Public Sub APLClick(ByVal sender As Object)
        Dim sw As New Stopwatch
        Try
            ECLog.WriteEntry("(APLClick) Entering...)", TraceEventType.Verbose)

            Dim f As APLBaseForm
            Dim dr As DialogResult
            f = Me.ChartSelectScreen

            Me.Cursor = Cursors.WaitCursor
            If f.Enabled = False Then
                f.Enabled = True
            End If
            Try
                dr = f.ShowDialog(Me)

            Catch ex As Exception
                ECLog.WriteExceptionError("APLClick", ex, True, "APL1")
                Exit Sub
                ' Throw
            End Try

            sw.Start()
            Application.UseWaitCursor = True

            'jjc 7.24.19 commented out for performance
            If ECGlobals.IsShutDownNeeded(pDisplayMsg:=True) Then
                Return
            End If

            If dr = DialogResult.Cancel Then Exit Sub

            Dim selectedChart As DOChart = f.GetSelectedChartInfo.Chart 'f.LastRow.ChartInfo.Chart

            Dim InitialChartCI As DOConfigInstance = Nothing

            If Me.XtraTabbedMdiManager1.Pages.Count > 0 Then
                Dim page As ChartBaseForm
                page = Me.ActiveMdiChild
                'page should never be nothing.. but occasionally is, so for now, we'll just test it
                'but it stinks of a bigger problem....
                If page IsNot Nothing Then
                    If page.IsReadyForInput Then

                        ' If f.ceAuditMode.Checked Then
                        If f.IsAuditModeChecked Then

                            page.AuditMode = True
                            page.RefreshTabLabel() 'May not be necessary, but appends the AuditMode flag to the tab label (window.text)
                        Else
                            page.AuditMode = False
                        End If

                        InitialChartCI = selectedChart.ConfigInstanceVersion
                        'This will only modify the CI if the chart is an ADT or HEC (DataLink) chart
                        ECGlobals.CurrentFacility.UpdateConfigInstanceIfNeeded(selectedChart)
                        'If InitialChartCI = selectedChart.ConfigInstanceVersion Then

                        If page.ConfigInstance = selectedChart.ConfigInstanceVersion Then
                            ECLog.WriteEntry("MDIFrame opening chart in existing chartform", TraceEventType.Verbose)
                            page.LoadChartFromActivePatientListFeed(selectedChart)
                            Return
                        Else 'config instances dont' match
                            If page.AllowImport(selectedChart) Then
                                'page.OverrideConfigurationInstance = True

                                ECLog.WriteEntry("MDIFrame opening (Importing) chart in existing chartform", TraceEventType.Verbose)
                                page.LoadChartFromActivePatientListFeed(selectedChart)

                                Return
                            End If
                        End If ' If page.ConfigInstance = f.LastRow.ChartInfo.Chart.ConfigInstanceVersion Then
                        'End If 'CI has changed
                    End If
                Else
                    'Debugger.Break()
                    'XtraTabbedMdiManager1.Pages(0).GetType()
                End If
            End If

            'JJC 12.04.11 Added the below line to open new ADT charts in the correct form/CI if it was created
            'while a pending CI was waiting to be activated.
            ECGlobals.CurrentFacility.UpdateConfigInstanceIfNeeded(selectedChart)

            'We may be opening either the current form or a previous chartform/configInstance saved with the chart being loaded....
            'I don't feel an urge to prompt them... we shall see....
            Dim cForm As ChartBaseForm = Me.CreateChartForm(selectedChart)

            cForm.LoadedFromAPL = True
            cForm.AuditMode = f.IsAuditModeChecked
            cForm.Show(selectedChart)

        Finally
            sw.Stop()
            Debug.WriteLine($"APLClick elapsed time: {sw.Elapsed}")
            ECLog.WriteEntry("(APLClick) Leaving...)", TraceEventType.Verbose)  'we don't ever seem to get here ... oh dear...
            Me.Cursor = DefaultCursor
            Application.UseWaitCursor = False
            DeleleteMouseClicks()
        End Try

        'cForm.RefreshTabLabel()
    End Sub

    Public Sub ForceLogOut(Optional ByVal o As Object = Nothing)
        Try
            ECGlobals.IsForcedLoggOutInProcess = True
            CloseAllOpenForms()

            ChangeUser()
        Finally
            ECGlobals.IsForcedLoggOutInProcess = False
        End Try
    End Sub

    Private Sub CloseAllOpenForms()

        'I use an ArrayList here because when iterating through other collections that
        'you are actively changing (removing elements) it throws an error.
        For Each f As Form In New ArrayList(My.Application.OpenForms)
            If Not f.Equals(Me) Then
                If TypeOf f Is ChartBaseForm Then
                    Dim cb As ChartBaseForm = TryCast(f, ChartBaseForm)
                    If cb IsNot Nothing Then cb._isDirty = False 'programming gods are crying
                    cb.ECClose()
                Else
                    f.Close()
                    If f.Equals(Me.ChartSelectScreen) Then
                        Me.ChartSelectScreen.Dispose()
                        Me.ChartSelectScreen = Nothing
                    End If
                End If
            Else
                Debug.WriteLine(f.Name)
            End If
        Next
    End Sub

    Public Sub ForceLogOutEventHandler(ByVal s As Object, ByVal e As EventArgs)
        ForceLogOut()
    End Sub

    Public Sub FormCloseCheck()
        If XtraTabbedMdiManager1.Pages.Count > 1 Then
            Me.beiCompanyClient.Enabled = False
            Me.beiFacility.Enabled = False
            ' bbiLogOff.Enabled = False
        Else
            Me.beiCompanyClient.Enabled = True
            Me.beiFacility.Enabled = True
            ' bbiLogOff.Enabled = True
        End If
    End Sub

    Public Sub FormOpenCheck()
        If XtraTabbedMdiManager1.Pages.Count > 0 Then
            Me.beiCompanyClient.Enabled = False
            Me.beiFacility.Enabled = False
            ' bbiLogOff.Enabled = False
        Else
            Me.beiCompanyClient.Enabled = True
            Me.beiFacility.Enabled = True
            ' bbiLogOff.Enabled = True
        End If
    End Sub

    Public Function GetOpenCharts() As Dictionary(Of String, DOChart)
        Dim dic As New Dictionary(Of String, DOChart)
        For Each f As Form In My.Application.OpenForms
            If TypeOf f Is ChartBaseForm Then
                Dim cf As ChartBaseForm = CType(f, ChartBaseForm)
                If cf.Chart IsNot Nothing Then
                    dic(cf.Chart.VisitID) = cf.Chart
                End If
            End If
        Next

        Return dic
    End Function

    Public Sub LoadMedicationsFromFile(Optional ByVal bLoadIntoPendingCI As Boolean = False)
        Dim lFac As DOFacility = ECGlobals.CurrentFacility
        Dim lCI As DOConfigInstance = Nothing

        If bLoadIntoPendingCI Then
            lCI = lFac.PendingConfigInstanceVersion
        Else
            lCI = lFac.ConfigInstanceVersion
        End If

        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Dim ConfigInstance As DOConfigInstance = lCI 'ECGlobals.CurrentFacility.ConfigInstanceVersion

        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = "Medications.txt"
            ofd.Filter = String.Format("{0} Medications definition file (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim Medication As DOMedication = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            'Using session As New Session
            XpoDefault.Session.BeginTransaction()

            For Each Line As String In Lines
                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                Dim lMedName As String = fields(0)
                Medication = ConfigInstance.GetMedicationByName(lMedName)
                Medication.ConfigInstance = ConfigInstance 'ECGlobals.CurrentFacility.ConfigInstanceVersion

                Try
                    If fields.Count >= 8 Then

                        If fields(DOMedication.FieldsEnum.Titrated) <> "" Then
                            Medication.Titrated = True
                        End If

                        If fields(DOMedication.FieldsEnum.CC) <> "" Then
                            Medication.CC = True
                        End If

                        If fields(DOMedication.FieldsEnum.DedicatedLine) <> "" Then
                            Medication.DedicatedLine = True
                        End If

                        If fields(DOMedication.FieldsEnum.Injection) <> "" Then
                            Medication.Injected = True
                        End If

                        If fields(DOMedication.FieldsEnum.NotInfused) <> "" Then
                            Medication.NotInfused = True
                        End If

                        If fields(DOMedication.FieldsEnum.Obs) <> "" Then
                            Medication.Obs = True
                        End If

                        If fields(DOMedication.FieldsEnum.Chemo) <> "" Then
                            Medication.Chemo = True
                        End If
                        If fields.Count > 8 Then
                            If fields(DOMedication.FieldsEnum.Hormonal) <> "" Then
                                Medication.Hormonal = True
                            End If
                        End If
                    Else
                        If fields(DOMedication.FieldsEnum2019.CC) <> "" Then
                            Medication.CC = True
                        End If

                        If fields(DOMedication.FieldsEnum2019.DedicatedLine) <> "" Then
                            Medication.DedicatedLine = True
                        End If

                        If fields(DOMedication.FieldsEnum2019.Chemo) <> "" Then
                            Medication.Chemo = True
                        End If

                        If fields(DOMedication.FieldsEnum2019.Hormonal) <> "" Then
                            Medication.Hormonal = True
                        End If
                    End If
                Catch
                    If Medication.Oid < 0 Then
                        ConfigInstance.Medications.Add(Medication)
                    End If
                    Medication.Save()
                    Continue For
                End Try
                If Medication.Oid < 0 Then
                    ConfigInstance.Medications.Add(Medication)
                End If
                Medication.Save()
            Next
            ' End Using

            '==========================================================
            '08.03.09 - Adding this call to confingInstanceSave ...
            'Resaving the current CI will update the CI.UpdateVer
            'this is then checked in the ReloadAllTabComboBoxes method.
            'Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
            'which is called during a Done/next, or close...
            'I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
            'ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()

            ConfigInstance.Save()
            XpoDefault.Session.CommitTransaction()
            '---------------------------------------------------------
            If Not bLoadIntoPendingCI Then
                ReloadAllTabComboBoxes()
                Me.ReloadAllTabMedications()
            End If

            MessageBox.Show("Ask, and Ye Shall Receive ... if you ask nice...", "Tada...", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)

            'MessageBox.Show(String.Format("'{0}' loaded successfully", ListObj.ListName), "Load List", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ' my.Application.
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
                'my.Application.
            End Try

            ' ofd.Dispose() '???
        End Try
    End Sub

    Public Sub LoadSupplyCatListMapFromFile(ByVal bLoadIntoPendingCI As Boolean)
        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        Dim lCI As DOConfigInstance = Nothing
        Dim lfac As DOFacility = ECGlobals.CurrentFacility

        If bLoadIntoPendingCI Then
            lCI = lfac.PendingConfigInstanceVersion
        Else
            lCI = lfac.ConfigInstanceVersion
        End If

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Dim ConfigInstance As DOConfigInstance = lCI

        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = "SupplyCatMap.txt"
            ofd.Filter = String.Format("{0} Supply Categegory Mapping definition file (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim SupCatMap As DOSupplyCatMap = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            For Each Line As String In Lines
                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                Dim lCategory As String = fields(0)
                SupCatMap = ConfigInstance.GetSupCatMapByCategory(lCategory)
                SupCatMap.ConfigInstance = ConfigInstance
                Try
                    If fields(DOSupplyCatMap.FieldsEnum.Category) <> "" Then
                        Dim lname As String = fields(DOSupplyCatMap.FieldsEnum.SupplyListName)
                        If lname Is Nothing Then
                            'complain
                        Else
                            SupCatMap.SupplyListName = lname
                        End If

                    End If

                    If fields(DOSupplyCatMap.FieldsEnum.UnitsListName) <> "" Then
                        Dim lname As String = fields(DOSupplyCatMap.FieldsEnum.UnitsListName)
                        If lname Is Nothing Then
                            'complain
                        Else
                            SupCatMap.UnitsListName = lname
                        End If
                    End If

                Catch ex As Exception
                    'If SupCatMap.Oid < 0 Then
                    '    ConfigInstance.SupplyCatMap.Add(SupCatMap)
                    'End If
                    'SupCatMap.Save()
                    Continue For
                End Try

                If SupCatMap.Oid < 0 Then
                    ConfigInstance.SupplyCatMap.Add(SupCatMap)
                End If
                SupCatMap.Save()
            Next

            '==========================================================
            '08.03.09 - Adding this call to confingInstanceSave ...
            'Resaving the current CI will update the CI.UpdateVer
            'this is then checked in the ReloadAllTabComboBoxes method.
            'Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
            'which is called during a Done/next, or close...
            'I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
            ConfigInstance.Save()
            '---------------------------------------------------------
            'ReloadAllTabComboBoxes()

            MessageBox.Show("Ask, and Ye Shall Receive ... if you ask nice...", "Tada...", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)

        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ' my.Application.
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
                'my.Application.
            End Try

            ' ofd.Dispose() '???
        End Try
    End Sub

    Public Sub OpenChartInNewForm(ByVal pVisitID As String)
        ECLog.WriteEntry("MDIFrame creating new ChartForm and loading chart", TraceEventType.Verbose)

        'We may be opening either the current form or a previous chartform/configInstance saved with the chart being loaded....
        'I don't feel an urge to prompt them... we shall see....

        Dim ChartToShow As DOChart = ECGlobals.CurrentFacility.GetChart(pVisitID)

        Dim cForm As ChartBaseForm = Me.CreateChartForm(ChartToShow)
        cForm.Show(ChartToShow)
    End Sub


    Public Sub ReloadAllTabComboBoxes()
        Try

            For Each p As DevExpress.XtraTabbedMdi.XtraMdiTabPage In Me.XtraTabbedMdiManager1.Pages
                Dim bp As ChartBaseForm = DirectCast(p.MdiChild, ChartBaseForm)
                bp.ReLoadCboListsIfChanged()
            Next
        Catch ex As Exception
            ECLog.WriteEntry("ReloadAllTabComboBoxes -> " & ex.Message, TraceEventType.Error)
        End Try
    End Sub

    Public Sub ReloadAllTabMedications()
        Try

            For Each p As DevExpress.XtraTabbedMdi.XtraMdiTabPage In Me.XtraTabbedMdiManager1.Pages
                Dim bp As ChartBaseForm = DirectCast(p.MdiChild, ChartBaseForm)
                ' bp.ReLoadCboListsIfChanged()
                bp.ReLoadMedicationsCboListsIfChanged() '08.28.09
            Next
        Catch ex As Exception
            ECLog.WriteEntry("ReloadAllTabMedications -> " & ex.Message, TraceEventType.Error)
        End Try
    End Sub

    Public Sub SetTabPageImage(ByVal pChartForm As ChartBaseForm, ByVal index As Integer)
        If Me.XtraTabbedMdiManager1 Is Nothing Then
            Return
        End If

        'there must be a better way.... at the point where the PageAdded event is thrown we dont' know if we're loading an old chart...
        'and selected page points at nothing (for the first chart) or the previous seleted page/chart while the new page is being loaded...
        For Each tabpage As DevExpress.XtraTabbedMdi.XtraMdiTabPage In Me.XtraTabbedMdiManager1.Pages
            If tabpage.MdiChild.Equals(pChartForm) Then
                tabpage.ImageIndex = index
            End If
        Next
    End Sub

    Protected Sub AddCriticalCareControlsList(ByVal pListName As String, ByVal pbInclusionList As Boolean)
        Dim ccs As DOConfigSetting = ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup("CriticalCareControlsByList")

        'If disabled or doesn't exist, create a new one
        If ccs.Enabled = False Then
            ccs = New DOConfigSetting(True)
            ccs.SettingName = "CriticalCareControlsByList"
            ccs.Group = ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup
            ccs.Save()
            ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup.Settings.Add(ccs)
        End If

        Dim ccsp As DOConfigPropertySetting = ccs(pListName)
        'If ccsp.Enabled = False Then ' assume it doesn't exist...
        If ccsp.Oid < 0 Then
            ccsp = New DOConfigPropertySetting
        End If
        ccsp.Enabled = True
        ccsp.ConfigSetting = ccs
        ccsp.PropertyName = pListName
        ccsp.PropertyValue = IIf(pbInclusionList, "InclusionList", "ExclusionList")
        ccsp.Save()
        ccs.PropertySettings.Add(ccsp)
        'End If 'CriticalCareControlsByList is enabled
    End Sub

    Protected Sub HideAllForms(ByVal fDict As Dictionary(Of Control, Boolean))
        For Each f As Form In New ArrayList(My.Application.OpenForms)
            Dim attr As ECFormTypeAttribute = DirectCast(Attribute.GetCustomAttribute(f.GetType, GetType(ECFormTypeAttribute)), ECFormTypeAttribute)
            If attr Is Nothing Then Continue For

            'If TypeOf f Is DevExpress.XtraEditors.XtraForm Then
            Dim cb As Form = TryCast(f, Form)
            fDict.Add(cb, cb.Visible)
            cb.Visible = False
        Next
    End Sub


    'Reshow previously hidden forms...
    Protected Sub HideAllForms_NOT(ByVal fDict As Dictionary(Of Control, Boolean))
        For Each c As Control In fDict.Keys
            c.Visible = fDict(c)
        Next
    End Sub

    Protected Function IsValidWorkstation() As Boolean
        Dim wksname As String = My.Computer.Name
        Dim xpcol As New XPCollection(Of DOWLV)(CriteriaOperator.Parse(String.Format("WorkstationName = '{0}'", wksname)))

        If xpcol Is Nothing Then
            ECLog.WriteEntry("Could not find workstation name in databae", TraceEventType.Error)
        End If

        If xpcol.Count = 1 Then Return True
        Return False
    End Function

    Protected Function ProcessCommandLine() As Boolean
#If CONFIG = "JJCDebug" Then
        Dim Args() As String
        Args = Split(Command$, " ")

        'UserName
        'PassWord
        'FacilityID
        'Visit ID

        If Args.Length = 2 Then
            ECGlobals.StartUpArgs.UserName = Args(0)
            ECGlobals.StartUpArgs.Password = Args(1)
            ECGlobals.StartUpArgs.UseLogin = True
            Return False
        End If

        If Args.Length < 4 Then
            'we have a problem
            ECGlobals.StartUpArgs.Use = False
            Return False
        End If

        ECGlobals.StartUpArgs.UserName = Args(0)
        ECGlobals.StartUpArgs.Password = Args(1)
        ECGlobals.StartUpArgs.Facility = Args(2)
        ECGlobals.StartUpArgs.visitID = Args(3)
        ECGlobals.StartUpArgs.Use = True

        Return True
#Else
        Return False
#End If
    End Function

    Protected Function ShouldForceLogOut() As Boolean
        CheckDBConnection()
        'ECGlobals.CheckDBConnection() 'and why shouldn't we do this here?
        'Note : IsModifiedByOutsideSource calls ENChartDOLib.Utils.CheckDBConnection ...
        If ECGlobals.CurrentUser IsNot Nothing AndAlso ECGlobals.CurrentUser.IsModifiedByOutsideSource Then
            ForceLogOut()
            Return True
        End If

        Return False
    End Function

    Protected Function WorkStationValidationEnabled() As Boolean
        Dim xpcol As New XPCollection(Of DODBLicValidation)
        If xpcol Is Nothing Then Return False
        If xpcol.Count <> 1 Then Return False

        Dim wkve As DODBLicValidation = xpcol(0)
        If wkve.Enabled Then Return True
        Return False
    End Function

    ''' <summary>
    ''' Set's the current thread to the AIC
    ''' </summary>
    ''' <notes>
    '''  I really dont' think this method is needed, but am reluctant to delete it
    ''' </notes>
    Private Shared Sub CalcuateNewPermissionsAndUpdateCurrentThreadWith()
        'Calcuate New permissions and update the current thread with the new principal object to store
        'the  user and roles.
        Dim al As New List(Of String)
        For Each PermissionObj As DOUserPermission In ECGlobals.CurrentUser.Permissions
            Try
                'If PermissionObj.Facility.Oid = ECGlobals.CurrentFacility.Oid Then
                If PermissionObj.Facility = ECGlobals.CurrentFacility Then
                    al.Add(PermissionObj.Role)
                End If
            Catch ex As Exception
                'don't know why i'm catching an exception here and not displaying a message
                'so i've added this assert to alert if it happens.

                'ah... it appears this catch is to handle the case where blank records get added.
                'Debug.Assert(False)
            End Try
        Next

        ECGlobals.CurrentUser.ActiveFacility = ECGlobals.CurrentFacility

        'Convert List object to string array
        Dim roleArray() As String = al.ToArray

        Dim i As New SSP.GenericIdentity(ECGlobals.CurrentUser.UserName)
        Dim p As New SSP.GenericPrincipal(i, roleArray)

        System.Threading.Thread.CurrentPrincipal = p   ' attach to current thread
        'below line throws an error... so i'll comment it out for now ...
        Try
            'this will always fail except for the very first time
            '       System.AppDomain.CurrentDomain.SetThreadPrincipal(p) ' all future threads as well
        Catch ex As Exception
            'this will always fail except for the very first time
        End Try
    End Sub

    ''' <summary>
    ''' This is to make sure that any newly created ConfigInstances don't have a
    ''' MajorChangeDate earlier than any previously activated CI's MajorChangeDates.
    ''' </summary>
    ''' <param name="CIDict"></param>
    ''' <remarks> Each new CI should use a majorchangeDate greater than the last one used. Not following this
    ''' rule could cause older charts created in this range to have a DOS that doesn't match with the
    ''' CI it they were saved with.
    ''' </remarks>
    Private Shared Sub ConfigInstanceConfigurationIntegrityCheck(ByVal CIDict As SortedDictionary(Of Date, DOConfigInstance))
        Dim tLastActivatedDate As New Nullable(Of Date)
        Dim tLastCI As DOConfigInstance = Nothing

        'We are going from the latest to the oldest, sorted by majorChangeDate
        'for ex.: 2010, 2009,2008
        For Each tCI In CIDict
            'Debug.WriteLine(tCI.Value.MajorChangeDate)
            If tLastActivatedDate.HasValue Then
                If tLastActivatedDate < tCI.Value.ActivatedDate Then
                    Dim msg As String = String.Format("A configuration error has been detected. The MajorChangeDate of ConfigInstace OID({0}) should be Greater than the MajorChangeDate of ConfigInstance OID({1}).", tCI.Value.Oid.ToString, tLastCI.Oid.ToString)
                    MessageBox.Show(msg, "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    ECLog.WriteEntry(msg, TraceEventType.Critical)
                    Application.Exit()
                End If
            End If
            tLastCI = tCI.Value
            tLastActivatedDate = tCI.Value.ActivatedDate
        Next
    End Sub

    ''' <summary>
    '''
    ''' </summary>
    ''' <remarks>
    ''' There are other FacilitySpecificSettings that autoLoad themselves the first time they are called ...
    ''' </remarks>
    Private Shared Sub LoadFacilitySpecificCISettings()
        'Load facility specific, non-configinstance, specific settings....
        Dim lAppConfigGroup As DOConfigGroup = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetConfigSettingGroup("Application")
        ECGlobals.AdvancedTimesEnabled = lAppConfigGroup("Advanced Times Module").Enabled

        ECGlobals.DateTimeSettings.TriageNurseDateTimeRequired = Not lAppConfigGroup("DateTimeTriageNurseNotRequired").Enabled
        ECGlobals.DateTimeSettings.DispositionDateTimeRequired = Not lAppConfigGroup("DateTimeDispositionNotRequired").Enabled

        If ECGlobals.AdvancedTimesEnabled Then
            ECGlobals.DateTimeSettings.TriageDocDateTimeRequired = Not lAppConfigGroup("DateTimeTriageDocNotRequired").Enabled
            ECGlobals.DateTimeSettings.ProtocolDateTimeRequired = Not lAppConfigGroup("DateTimeProtocolNotRequired").Enabled
            ECGlobals.DateTimeSettings.MSEDateTimeRequired = Not lAppConfigGroup("DateTimeMSENotRequired").Enabled
            ECGlobals.DateTimeSettings.TreatmentLocDateTimeRequired = Not lAppConfigGroup("DateTimeTreatmentLocNotRequired").Enabled
            ECGlobals.DateTimeSettings.DecisionDateTimeRequired = Not lAppConfigGroup("DateTimeDecisionNotRequired").Enabled
        End If

        ECGlobals.DontUseLocalInfusionWorkingdays = lAppConfigGroup("IgnoreInfusionLocalWD").Enabled
        ECGlobals.PhysicianRequireEmOverrideReason = lAppConfigGroup("PhysicianRequireEmOverrideReason").Enabled
        ECGlobals.AllowFutureDates = lAppConfigGroup("AllowFutureDates").Enabled
        ECGlobals.ColdFeedEnabled = lAppConfigGroup("EnableColdFeed").Enabled
        ECGlobals.EnableSeriesVisits = lAppConfigGroup("EnableSeriesVisits").Enabled
        ECGlobals.PhysModFirst = lAppConfigGroup("PhysModFirst").Enabled

        ECGlobals.UseGlobalMedicationsTable = lAppConfigGroup("UseGlobalMedicationsTable").Enabled

        'Try
        '    Dim UseGlobalMedicationsTableSetting = lAppConfigGroup("UseGlobalMedicationsTable")
        '    If UseGlobalMedicationsTableSetting.Enabled = False Then
        '        ECGlobals.UseGlobalMedicationsTable = True
        '    Else
        '        If Not String.IsNullOrWhiteSpace(UseGlobalMedicationsTableSetting?.SettingValue) Then
        '            ECGlobals.UseGlobalMedicationsTable = CBool(UseGlobalMedicationsTableSetting?.SettingValue.Trim)
        '        End If
        '    End If
        'Catch ex As Exception
        '    ECGlobals.UseGlobalMedicationsTable = True
        'End Try


        ECGlobals.FacilityTipVersionCISpecific = ConfigInstanceSpecificSetings.LoadNewConfigInstanceSpecificConfigSettings(ECGlobals.CurrentFacility)

    End Sub


    Private Sub BarButtonItem11_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem11.ItemClick
        Dim f As New UserConfigUserForm
        f.Show()
    End Sub

    Private Sub BarButtonItem12_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem12.ItemClick
        Me.LoadUserListFromDisk()
    End Sub

    Private Sub BarButtonItem13_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem13.ItemClick
        Dim frm As New PhysicianCodesFilterForm
        frm.Show()
    End Sub

    Private Sub BarButtonItem14_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem14.ItemClick
        Dim frm As New ChartHistoryDiagForm
        frm.Show()
    End Sub

    'Private Sub BarButtonItem15_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem15.ItemClick
    '    LoadComboListsFromDiskV2()
    '    '==========================================================
    '    '08.03.09 - Adding this call to confingInstanceSave ...
    '    'Resaving the current CI will update the CI.UpdateVer
    '    'this is then checked in the ReloadAllTabComboBoxes method.
    '    'Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
    '    'which is called during a Done/next, or close...
    '    'I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
    '    ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
    '    '---------------------------------------------------------
    '    ReloadAllTabComboBoxes() 'jjc 08.03.09
    'End Sub

    Private Sub BarButtonItem16_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem16.ItemClick
        Me.LookForErrors()
    End Sub

    Private Sub LoadMedicationsFromFile_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem17.ItemClick
        'If ECGlobals.UseGlobalMedicationsTable Then 'DOMedicationMaster.HasData() Then
        '    Dim f As New LoadMedicationMastersForm

        '    f.ShowDialog()
        'Else
        Dim f As New LoadMedicationsForm
        f.ShowDialog()
        'do a dance ...
        'End If

    End Sub

    Private Sub BarButtonItem18_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem18.ItemClick
        Dim f As New ConfigMedicationsFormV2
        f.ShowDialog()
        ECGlobals.CurrentFacility.ConfigInstanceVersion.ResetEnabledMedicationsList()
        '==========================================================
        '08.03.09 - Adding this call to confingInstanceSave ...
        'Resaving the current CI will update the CI.UpdateVer
        'this is then checked in the ReloadAllTabComboBoxes method.
        'Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
        'which is called during a Done/next, or close...
        'I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
        ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
        '---------------------------------------------------------

        Me.ReloadAllTabMedications()
    End Sub

    Private Sub BarButtonItem19_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem19.ItemClick
        Dim f As New ConfigSupplyCatMap
        f.ShowDialog()

        ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
    End Sub

    Private Sub BarButtonItem20_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem20.ItemClick
        Dim f As New LoadSupplyCatMapForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem3_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        'If Not System.Threading.Thread.CurrentPrincipal.IsInRole("Config") AndAlso Not CurrentPrincipal.IsInRole("Admin") Then
        '    MessageBox.Show("You don't have permissions to edit the configuration")
        '    Exit Sub
        'End If

        Dim frm As New ConfigUserForm
        'frm.MdiParent = Me
        frm.Show()
    End Sub

    Private Sub BarButtonItem4_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        'If Not System.Threading.Thread.CurrentPrincipal.IsInRole("Config") AndAlso Not CurrentPrincipal.IsInRole("Admin") Then
        '    MessageBox.Show("You don't have permissions to edit the configuration")
        '    Exit Sub
        'End If

        Dim frm As New ConfigSettingsFormV3
        'frm.MdiParent = Me
        frm.Show()
    End Sub

    Private Sub BarButtonItem5_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem5.ItemClick
        'If Not System.Threading.Thread.CurrentPrincipal.IsInRole("Config") AndAlso Not CurrentPrincipal.IsInRole("Admin") Then
        '    MessageBox.Show("You don't have permissions to edit the configuration")
        '    Exit Sub
        'End If
        Dim frm As New ControlConfigForm3
        'frm.MdiParent = Me
        frm.Show()
    End Sub


    Private Sub BarButtonItem7_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiLoadComboLists.ItemClick
        Dim f As New LoadComboBoxListsV2Form
        f.ShowDialog()

        'LoadComboListsFromDisk()
        ''==========================================================
        ''08.03.09 - Adding this call to confingInstanceSave ...
        ''Resaving the current CI will update the CI.UpdateVer
        ''this is then checked in the ReloadAllTabComboBoxes method.
        ''Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
        ''which is called during a Done/next, or close...
        ''I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
        'ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
        ''---------------------------------------------------------
        'ReloadAllTabComboBoxes() 'jjc 08.03.09
    End Sub


    Private Sub BarButtonItem8_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem8.ItemClick
        Me.Cursor = Cursors.WaitCursor
        Try
            'Not working :()
            'right now it only purges records deleted since the app started....
            XpoDefault.Session.PurgeDeletedObjects()
        Finally
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    Private Sub BarButtonItem9_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem9.ItemClick
        If DialogResult.OK = MessageBox.Show("Are you sure you want to exit?", "Exit App?", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) Then
            Application.Exit()
        End If
    End Sub

    Private Sub bbiAdminReports_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAdminReports.ItemClick
        'LaunchAdminReportsApp()
        If MessageBox.Show(Me, $"The reports application may contain potentially protected information. All attempts to Print or Export a report will be tracked.{vbCrLf}{vbCrLf}Do you wish to Continue?", "HIPAA Warning", MsgBoxStyle.OkCancel, MessageBoxIcon.Warning) <> MsgBoxResult.Ok Then
            Exit Sub
        End If

        Dim reportDirPath = ECGlobals.GlobalSettings.GetString("ReportsDirectory")
        Dim f As New ReportStartup(ECGlobals.CurrentUser, ECGlobals.CurrentFacility, IdleUserTimer, SecurityAuthManager.DefaultInstance, reportDirPath)
        f.StartPosition = FormStartPosition.CenterParent 'this is not be honored .... it starts wherever it wants ...
        f.ShowDialog(Me)

    End Sub

    Private Shared Sub LaunchAdminReportsApp()
        Try
            ECLog.WriteEntry("(bbiAdminReports_ItemClick)", TraceEventType.Information)
            If Not ECGlobals.CurrentUser.IsInRole(ECRoles.AdminReports) Then
                MessageBox.Show("You don't have the necessary permissions to access this feature.", "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Return
            End If

            'End If'Return '--------------------------------------- do da.... do da....-----------------------------

            Dim info As New ProcessStartInfo

            Dim ExeToRun As String = "ENChartReports.exe"
            'Dim ExeToRun As String = "ecconfig.exe"

            '#If DEBUG Then
            '           info.FileName = Path.Combine(Environment.CurrentDirectory, ExeToRun)
            '#Else
            info.FileName = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, ExeToRun)
            '#End If
            info.Arguments = ECGlobals.CurrentFacility.Oid & " " & ECGlobals.CurrentUser.Oid

            Dim sp As New Process()
            sp.StartInfo = info
            sp.Start()
            'sp.WaitForExit()
        Catch ex As Exception
            ECLog.WriteEntry("The following error occred trying to run Admin Rerports : " & ex.Message.ToString, TraceEventType.Error)
        End Try
    End Sub

    'Private Sub bbiBaseLine_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiBaseLine.ItemClick
    '    Dim frm As New BaseLineTestForm
    '    frm.Show()
    'End Sub

    Private Sub bbiBig4_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiBig4.ItemClick
        'If Not System.Threading.Thread.CurrentPrincipal.IsInRole("Config") AndAlso Not CurrentPrincipal.IsInRole("Admin") Then
        '    MessageBox.Show("You don't have permissions to edit the configuration")
        '    Exit Sub
        'End If

        Dim tmpForm As New ConfigBig4Form
        tmpForm.Show()
    End Sub

    Private Sub bbiChangePassword_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiChangePassword.ItemClick
        Throw New NotImplementedException()
        'Dim f As New ChangePasswordForm
        'f.InitAndShowDialog(False)
    End Sub
    Private Sub bbiChangeSecurityQuestions_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiChangeSecurityQuestions.ItemClick
        'Dim f As New CreateSecurityQuestionsForm(ECGlobals.CurrentUser)
        'f.ShowDialog()
    End Sub

    Private Sub bbiChartHistory_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiChartHistory.ItemClick
        Dim tform As New ChartHistory
        tform.ShowDialog()

        If ShouldForceLogOut() Then
            tform.Dispose()
            Return
        End If

        If tform.ChartToEdit Is Nothing Then
            tform.Dispose()
            Return
        End If

        'Dim cForm As New ChartForm
        'cForm.MdiParent = Me
        Dim cForm As ChartBaseForm = Me.CreateChartForm(tform.ChartToEdit, True)

        cForm.ReadOnlyMode = True
        cForm.CloseOnDone = True

        cForm.Show(tform.ChartToEdit)
    End Sub

    Private Sub bbiEditComboBoxLists2_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditComboBoxLists2.ItemClick
        'If Not System.Threading.Thread.CurrentPrincipal.IsInRole("Config") AndAlso Not CurrentPrincipal.IsInRole("Admin") Then
        '    MessageBox.Show("You don't have permissions to edit the configuration")
        '    Exit Sub
        'End If
        Dim tmpForm As New ConfigComboBoxListsForm
        tmpForm.Show()
    End Sub

    Private Sub bbiEditListNPPA_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditListNPPA.ItemClick
        'Dim f As New ComboBoxListEditorForm("NPPAList", "NP/PA List")
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("Triage_NPPA_cbo")

        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList
            cboList.ListName = "NPPAList"
            cboList.Enabled = True
            Dim cboControl As New DOConfigComboBoxListControl
            cboControl.ComboBoxList = cboList
            cboControl.ComboBoxName = "Triage_NPPA_cbo"
            cboControl.Enabled = True
            cboControl.Save()
            cboList.ComboBoxes.Add(cboControl)
            cboList.ConfigInstance = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
            cboList.Save()
            ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists.Add(cboList)
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "NP/PA List")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
        End If

        f.Dispose()
        Return
    End Sub

    Private Sub bbiEditListNurse_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditListNurse.ItemClick
        'Dim listName As String = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxListByControlName("Triage_Nurse_cbo").ListName
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("Triage_Nurse_cbo")
        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList
            cboList.ListName = "NurseList"
            cboList.Enabled = True
            Dim cboControl As New DOConfigComboBoxListControl
            cboControl.ComboBoxList = cboList
            cboControl.ComboBoxName = "Triage_Nurse_cbo"
            cboControl.Enabled = True
            cboControl.Save()
            cboList.ComboBoxes.Add(cboControl)
            cboList.ConfigInstance = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
            cboList.Save()
            ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists.Add(cboList)
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Nurse List")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
            '   UpdatePreviousConfigInstance(cboList)
        End If

        f.Dispose()
        Return
    End Sub

    Private Sub GetPreviousConfigInstance()
        Dim ci As DOConfigInstance = Nothing

        'When ECGlobals.ConfigInstanceDict is created, ci's with "blank" activatedDate are skipped...
        Dim rd As New SortedList(Of Date, DOConfigInstance)(ECGlobals.ConfigInstanceDict, New DateReverseComparer)

        'Dim d As Date

        'Iterate through, from most recent to oldest, based of MajorChangeDate
        'oldest being where 1967 is older the 2009 ... =/
        'For Each d In rd.Keys
        '    If d <= DOS Then
        '        ci = ECGlobals.ConfigInstanceDict(d)
        '        Exit For
        '    End If
        'Next
        '   Dim pci = ECGlobals.ConfigInstanceDict()
    End Sub

    Private Sub bbiEditListPhysician_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditListPhysician.ItemClick
        'Dim f As New ComboBoxListEditorForm("PhysicianList", "Physician List")
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("Triage_Physician_cbo")

        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList
            cboList.ListName = "PhysicianList"
            cboList.Enabled = True
            Dim cboControl As New DOConfigComboBoxListControl
            cboControl.ComboBoxList = cboList
            cboControl.ComboBoxName = "Triage_Physician_cbo"
            cboControl.Enabled = True
            cboControl.Save()
            cboList.ComboBoxes.Add(cboControl)
            cboList.ConfigInstance = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
            cboList.Save()
            ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists.Add(cboList)
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Physician List")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
        End If
        f.Dispose()
        Return
    End Sub

    Private Sub bbiLogOff_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiLogOff.ItemClick
        '   Throw New Exception("oh oh")
        ECLog.WriteEntry("(bbiLogOff_ItemClick)", TraceEventType.Verbose)
        LogOff()
    End Sub

    Private Sub LogOff()
        LogOutParagonUser()

        Try
            'JJC 12.2021 - As of right now, we are seeing an exception thrown when there is a pending CI
            'and the user is logging out after being prompted to. I believe the error has something to do
            'with the app.exit call below trying to interate through open forms to close them, but something
            'else is also causing that forms collection to change during that iteration.
            If ECGlobals.UpdatePending Then
                Application.Exit()
                Exit Sub
            End If
        Catch
            'we don't care ...
        End Try
        ECGlobals.CheckDBConnection() '07.22.09 -  added

        Me.CloseAllForms()
        AuditLogger.Authentication.UserLogOut()

        ChangeUser()
    End Sub

    Private Shared Sub LogOutParagonUser()
        Try
            If ECGlobals.CurrentUser IsNot Nothing Then
                Dim bresult = SecurityAuthManager.DefaultInstance.LogoutParagonUser(ECGlobals.CurrentUser.UserID)

            End If
        Catch ex As Exception
            ECLog.WriteExceptionError("(LogOutParagonUser)", ex)
            '  Throw
        End Try
    End Sub

    Private Sub bbiOpenNewChart_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiOpenNewChart.ItemClick
        ProfileOptimization.StartProfile("OpenNewChart.Profile")
        Application.UseWaitCursor = True
        Application.DoEvents()
        bbiOpenNewChart.Enabled = False
        bbiOpenNewChart.Refresh()

        Dim sw As New Stopwatch
        sw.Start()
        Try
            ECLog.WriteEntry("(bbiOpenNewChart_ItemClick)", TraceEventType.Verbose)

            If ECGlobals.IsShutDownNeeded(True) Then Exit Sub

            Dim tDate As Date = Me.bbiOpenChartDate.EditValue

            If tDate = New Date Then
                OpenNewChart()
            Else
                OpenNewChart(tDate)
            End If
        Finally
            sw.Stop()
            ' MessageBox.Show($"Time to Init form: {sw.Elapsed}")
            bbiOpenNewChart.Enabled = True
        End Try

    End Sub

    'Private Sub bbiOpenNewChart_ToolBar_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiOpenNewChart_ToolBar.ItemClick
    '    If String.IsNullOrEmpty(Me.beiCompanyClient.EditValue) OrElse
    '    (TypeOf Me.beiFacility.EditValue Is String AndAlso String.IsNullOrEmpty(Me.beiFacility.EditValue)) Then
    '        MessageBox.Show("Please select a Facility", "Facility Needed", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '        Return
    '    End If
    '    '        If System.Threading.Thread.CurrentPrincipal.IsInRole("NONE") Then
    '    '            MessageBox.Show("You have no permissions")
    '    '        End If
    '    '        '        Else
    '    '        '				MessageBox.Show("You are not authorized to perform this action, sorry.");
    '    OpenNewChart()
    'End Sub

    Private Sub bbiSaveComboLists_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSaveComboLists.ItemClick
        SaveComboListsToDisk()
    End Sub

    Private Sub beiCompanyClient_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles beiCompanyClient.EditValueChanged
        If bIgnoreEvents Then
            Return
        End If

        'If beiCompanyClient.EditValue = "" Then Return

        'beiFacility.EditValue = ""
        'RepositoryItemComboBox3.Items.Clear()

        ''02.14.11 - This sort is really not necessary anymore because the ComboBoxEdit
        ''control will now correctly sort it now that I've overridden the inherited
        ''(non generic) IComparable interface. But we'll go ahead and sort it anywho.

        'Dim faclist As New List(Of DOFacility)
        'For Each facility As DOFacility In CompanyClientDict(beiCompanyClient.EditValue)
        '    faclist.Add(facility)
        'Next

        'faclist.Sort()

        'Dim bfirstTime As Boolean = True
        'For Each facility As DOFacility In CompanyClientDict(beiCompanyClient.EditValue)
        '    'Note DOFacility can in fact be directly inserted, there is no need to wrap it in
        '    'a ComboBoxItem Object. However, i'm not willing to change the code just now.
        '    RepositoryItemComboBox3.Items.Add(New ComboBoxItem(facility))
        '    If bfirstTime AndAlso bDontSetFacility = False Then
        '        bfirstTime = False
        '        beiFacility.EditValue = facility
        '    End If
        'Next

    End Sub

    Private Sub beiFacility_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles beiFacility.EditValueChanged
        ECLog.WriteEntry("(beiFacility_EditValueChanged) - Facility Changed", TraceEventType.Verbose)
        If ECGlobals.CurrentUser Is Nothing Then
            Exit Sub 'JJC 07.29.19
        End If
        ECGlobals.Reset() 'JJC 04.02.20 added
        If bIgnoreEvents Then
            Return
        End If

        Try
            'On startup, this method gets called before we've had a chance to setup the facility list...
            'editvalue type changes to a dofacility object once it's been set up.
            If TypeOf beiFacility.EditValue Is String Then
                Exit Sub
            End If

            'ECGlobals.CurrentFacility = beiFacility.EditValue
            ECGlobals.CurrentFacility = beiFacility.EditValue
            Try
                ECGlobals.CurrentFacility?.ConfigInstanceVersion?.PreLoadLists()
                ECGlobals.CurrentFacility?.SharedConfigInstanceVersion?.PreLoadLists()
            Catch ex As Exception
                ECLog.WriteExceptionError($"(beiFacility_EditValueChanged) PreLoadLists Error: {ex.Message}", ex)
            End Try

            '07.14.10 We need CurrentUser.ActiveFacility to be set prior to any calls to DOUser.IsInRole.
            'this also gets set in CalcuateNewPermissionsAndUpdateCurrentThreadWith, but that routine
            'is skipped if it's the admin admin user, which was causing  a problem checking for the DevAdmin Permission.
            ECGlobals.CurrentUser.ActiveFacility = ECGlobals.CurrentFacility

            ECGlobals.DefaultTreatmentArea = Nothing
            Try
                If ECGlobals.CurrentUser.DefaultFacility Is Nothing OrElse
                ECGlobals.CurrentUser.DefaultFacility <> ECGlobals.CurrentFacility Then
                    ECGlobals.CurrentUser.DefaultFacility = ECGlobals.CurrentFacility
                    ECGlobals.CurrentUser.Save()
                End If
            Catch ex As Exception
                ECLog.WriteExceptionError($"(beiFacility_EditValueChanged) an error occurred trying to update the default facility for user {ECGlobals.CurrentUser.UserName}", ex)
            End Try

            Try 'TODO - 'Figure out why Null Config Setting blows up!
                If ECGlobals.IsDatalinkEnabled Then
                    btnAlias.Enabled = True
                    bbiDataRequester.Enabled = True
                Else
                    btnAlias.Enabled = False
                    bbiDataRequester.Enabled = False
                End If
                LoadFacilitySpecificCISettings()


            Catch ex As Exception
                Dim exChain = New SimpleExceptionResult(Of Boolean)(ex)
                ECLog.WriteExceptionError($"{exChain.GetExceptionChainMessage()}", ex, True)
                Environment.Exit(0)
            End Try

            'If logged in user is admin we don't need to recaculate permissions based of the newly
            'selected facility, so just return. The admin user has the "Admin" role by default
            'which is hardcoded...
            If Not ECGlobals.CurrentUser.IsTheAICAdmin Then
                CalcuateNewPermissionsAndUpdateCurrentThreadWith()
            End If

            LoadUserFacilitySpecificSettings()
            InitMenus()

        Catch ex As Exception
            'OLD TODO This should be handled different, but this handler is being called as soon as a Companyclient
            'is selected, but the facility combo is blank... so we have an invalid object until one is selected
            'from the list. So for now, just catch it and ignore it....

            'OLD TODO: Should probably disable a bunch of menu options....
        Finally
            ECGlobals.TempTracePrincipal("leaving...")
        End Try
    End Sub

    Private Sub bsiLoadComboList_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bsiLoadComboList.ItemClick
        LoadSingleComboListsFromDisk()

        '==========================================================
        '08.03.09 - Adding this call to confingInstanceSave ...
        'Resaving the current CI will update the CI.UpdateVer
        'this is then checked in the ReloadAllTabComboBoxes method.
        'Note, this should also force all open forms (for the current CI) on other workstations to also update their cbo lists in the chartbaseform.reset method
        'which is called during a Done/next, or close...
        'I'm a little nervous about the overhead in calling this save method, and what the odds are of screwing up other things....
        ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
        '---------------------------------------------------------
        ReloadAllTabComboBoxes() 'jjc 08.03.09
    End Sub

    Private Sub bsiMaintUser_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiUsersAndPermissions.ItemClick
        Me.Cursor = Cursors.WaitCursor
        'Cursor.Current = Cursors.WaitCursor
        Application.DoEvents()

        Try
            If ECGlobals.CurrentUser.IsInRole(ECRoles.Admin) OrElse ECGlobals.CurrentUser.IsInRole(ECRoles.DevAdmin) Then
                Dim f As New UserConfigUserForm
                f.ShowDialog()
            Else
                MessageBox.Show("You don't have the necessary permissions to access this feature.", "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Stop)
            End If
        Catch ex As Exception
            Throw
        Finally
            Me.Cursor = DefaultCursor
        End Try
    End Sub

    Private Sub ChangeUser()
        ECLog.WriteEntry("(ChangeUser) - Entering...", TraceEventType.Verbose)

        beiCompanyClient.EditValue = ""
        beiFacility.EditValue = ""

        'We need to reset the ChartSelectionScreen or it wont' reload new user settings when you
        'change users
        If ChartSelectScreen IsNot Nothing Then
            Me.ChartSelectScreen.Dispose()
            Me.ChartSelectScreen = Nothing
        End If

        Dim lastUser As DOUser = ECGlobals.CurrentUser
        If ECGlobals.CurrentUser IsNot Nothing Then
            ECGlobals.CurrentUser.LogOut()
            AuditLogger.Authentication.UserLogOut()
            ECGlobals.CurrentUser = Nothing
        End If

        Me.bsiLoggedInUser.Caption = "User: "
        If ECGlobals.IsForcedLoggOutInProcess Then
            MessageBox.Show(String.Format("A forced log out has occurred. The current User ({0}) has logged in on a different machine.", lastUser.UserName), "Forced Log Out Has Occurred", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
        ClearFacility()

        'The login dialog is called in the call tree to AuthenticatUser
        If ECGlobals.DontUseSecureLogin Then
            If Auth_MoreSecure.LogInUser(Me) = False Then
                Application.Exit()
                Return
            End If
        Else
            If Auth_MoreSecure.LogInUserExternal(Me) = False Then
                Application.Exit()
                Return
            End If
        End If


        Me.bsiLoggedInUser.Caption = "User: " & ECGlobals.CurrentUser.UserName
        'JJC 07-10-07 The below line needs to be moved to after the place where the CurrentFacility is set.

        CheckConfigIntegrity()

        SetupCClientAndFacilityComboBoxes()
        LoadUserFacilitySpecificSettings()

        'and just what is this all about? hmmm....
        ECGlobals.CurrentUser.ActiveFacility = ECGlobals.CurrentFacility

        'InitMenus() 'JJC 04.02.2020 removed

        If ECGlobals.IsShutDownNeeded(True) Then
            Application.Exit()
            Close()
            Exit Sub
        End If

        If ECGlobals.StartUpArgs.Use Then
            OpenChartInNewForm(ECGlobals.StartUpArgs.visitID)
        End If

        ECLog.WriteEntry("(ChangeUser) Leaving...", TraceEventType.Verbose)
    End Sub

    ''' <summary>
    ''' Set the Facility select comboBox to nothing
    '''     ''' <remarks>
    ''' </remarks>
    ''' Added 08.26.15 
    ''' We now clear this when a user logs Out so it's not then logged as
    ''' the active facility for the next user to log in.
    ''' </summary>
    Private Sub ClearFacility()
        beiFacility.EditValue = Nothing
    End Sub
    Sub DeleteLists()
        Dim nlist As New List(Of DOConfigComboBoxList)
        For Each list As DOConfigComboBoxList In ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists
            nlist.Add(list)
        Next

        For i As Integer = 0 To nlist.Count - 1
            nlist(i).Delete()
        Next i

        nlist.Clear()
        For Each list As DOConfigComboBoxList In ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists
            nlist.Add(list)
        Next

        For i As Integer = 0 To nlist.Count - 1
            'ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists.
            nlist(i).Delete()
        Next i

        'JJC 08-16-07 (Version 1.0.2.33)
        'The below line is causing locking issues on trying to save the configinstance, on the configInstanceBase dervied
        'forms like comboBox Lists screen.... after doing a loadcomboBoxes.... so... for now....
        ' Session.DefaultSession.PurgeDeletedObjects()
    End Sub

    Private Sub DeleteSharedCboLists()
        If MessageBox.Show("Are you sure you want to delete all shared lists", "Double Checking", MessageBoxButtons.OKCancel) = DialogResult.OK Then
            Dim nlist As New List(Of DOConfigComboBoxList)
            For Each list As DOConfigComboBoxList In ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists
                nlist.Add(list)
            Next
            Dim count = nlist.Count

            For i As Integer = 0 To nlist.Count - 1
                'ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists.
                nlist(i).Delete()
            Next i

            MessageBox.Show($"{count} Lists Deleted!")
        End If
    End Sub

    Sub HandleIdleUserTimerTick()

        If ECGlobals.AutoLogOffTimeOut < 1 Then
            'MessageBox.Show("ECGlobals.AutoLogOffTimeOut < 1")
            Return
        End If

        Dim ts As System.TimeSpan = Now.Subtract(LastUserInput.LastActivity)
        If ts.TotalMinutes >= ECGlobals.AutoLogOffTimeOut - 1 Then
            'If ts.TotalSeconds > 12 Then
            If ECGlobals.CurrentUser Is Nothing Then Return
            'If Not ECGlobals.CurrentUser.IsAdmin Then Return

            'Me.Visible = True
            AutoLogOFf()

            'Me.Timer1.Start()
            'Me.Visible = True

        End If
    End Sub

    Private Sub AutoLogOFf()
        Try

            Me.IdleUserTimer.Stop()
            Dim keepWaiting As Boolean = True

            'If AtLeastOneChartHasUnsavedData() Then
            '    Me.Activate() 'Shawna didn't like this idea ...
            'End If

            CustomBringToFront()

            Dim f As New AutoLogOffWarningForm
            f.Owner = Me

            If Me.WindowState = FormWindowState.Minimized Then
                f.StartPosition = FormStartPosition.CenterScreen
            Else
                f.StartPosition = FormStartPosition.CenterParent
            End If

            Dim dresult As DialogResult = f.ShowDialog()
            If dresult = DialogResult.Cancel Then
                LastUserInput.Reset()
            End If

            If f.AbortAutoLogOff Then
                Return
            End If

            ECGlobals.AutoLoggingOut = True
            CleanUpTransactionsOnAutoLogoff()
            If ECGlobals.CurrentUser IsNot Nothing Then
                'BeginInvoke(Sub() ECGlobals.CurrentUser.LogOut(), Nothing)
                AuditLogger.Authentication.UserAutoLogOut()

                ECGlobals.CurrentUser.LogOut()
                'ECGlobals.CurrentUser = Nothing
            End If

            CloseAllForms()
            ECGlobals.CurrentUser = Nothing
            Environment.Exit(0)

        Finally
            ECGlobals.AutoLoggingOut = False
            Me.IdleUserTimer.Start()
        End Try
    End Sub

    Private Sub CustomBringToFront()
        Me.Activate()
        Me.Visible = True
        BringToFront()
        'WindowState = FormWindowState.Minimized
        'Show()
        WindowState = FormWindowState.Normal
    End Sub

    ''' <summary>
    ''' If there is a transaction in progress cancel it and Rollback any changes.
    ''' </summary>
    ''' <remarks>
    ''' The edit NPPA,Nurse, ect lists use a transaction which prevents us from logging out.
    ''' </remarks>
    Private Sub CleanUpTransactionsOnAutoLogoff()
        If Session.DefaultSession.InTransaction Then
            Session.DefaultSession.RollbackTransaction()
        End If
    End Sub

    Private Function AtLeastOneChartHasUnsavedData() As Boolean
        Debug.WriteLine("============================= Close all forms========================================================")
        For Each f As Form In New ArrayList(My.Application.OpenForms)
            '11.25.09 if a form is not marked with the custom attribute ECFormType then don'd close it...
            'this is try and avoid accidentally closing some "behind the scenes" forms/controls that devexpress
            'seems to autocreate and expect...

            Dim attr As ECFormTypeAttribute = DirectCast(Attribute.GetCustomAttribute(f.GetType, GetType(ECFormTypeAttribute)), ECFormTypeAttribute)

            If attr Is Nothing Then
                Continue For
            End If

            If Not f.Equals(Me) Then
                If TypeOf f Is ChartBaseForm Then
                    Dim cb As ChartBaseForm = TryCast(f, ChartBaseForm)
                    If cb._isDirty Then
                        Return True
                    End If
                End If
            End If
        Next
        Return False
    End Function

    Public Sub CloseAllForms()
        Try
            For Each f As Form In New ArrayList(My.Application.OpenForms)
                '11.25.09 if a form is not marked with the custom attribute ECFormType then don'd close it...
                'this is try and avoid accidentally closing some "behind the scenes" forms/controls that devexpress
                'seems to autocreate and expect...

                Dim attr As ECFormTypeAttribute = DirectCast(Attribute.GetCustomAttribute(f.GetType, GetType(ECFormTypeAttribute)), ECFormTypeAttribute)

                If TypeOf f Is DevExpress.XtraPrinting.Preview.PrintPreviewFormEx Then
                    f.Close()
                    f.Dispose()
                    Continue For
                End If

                If attr Is Nothing Then
                    Continue For
                End If

                If Not f.Equals(Me) Then
                    If TypeOf f Is ChartBaseForm Then
                        Dim cb As ChartBaseForm = TryCast(f, ChartBaseForm)

                        cb.ExportChargesForAutoLogoff()
                        cb.ECClose(False)
                    Else
                        f.Dispose()

                        If f.Equals(ChartSelectScreen) Then
                            Me.ChartSelectScreen = Nothing
                        End If
                    End If
                Else
                    Debug.WriteLine(f.Name)
                End If
            Next
            Application.DoEvents()

        Catch ex As Exception
            ECLog.WriteEntry("(CloseAllForms) " & ex.Message, TraceEventType.Error)
        End Try
    End Sub

    Private Sub UpdateNurseLists()
        Dim fs = ECGlobals.FacilityTipVersionCISpecific

        bbiEditListNurse.Enabled = fs.FacilityMode
        bbiEditListNPPA.Enabled = fs.FacilityMode
        bbiEditListPhysician.Enabled = fs.FacilityMode

        bbiEditObsListPhysician.Enabled = fs.EnableObservationTab
        bbiEditObsListNurse.Enabled = fs.EnableObservationTab

        If fs.EnableObservationTab AndAlso fs.ObservationStandAloneMode Then
            bbiEditListNurse.Enabled = True
        End If
    End Sub

    Private Sub UpdateManualEntryMenuOptions()
        Dim fs = ECGlobals.FacilityTipVersionCISpecific

        bbiManualEntryObservation.Enabled = fs.EnableObservationTab 'GetBoolConfigSetting("EnableObservationTab")
        bbiManualEntryFacility.Enabled = fs.FacilityMode 'Not fs.ObservationStandAloneMode 'GetBoolConfigSetting("ObservationStandAloneMode")
        bbiManualEntryProfeeV2.Enabled = fs.UseAlternatePhysicianTab
    End Sub

    Private Function GetBoolConfigSetting(setting As String) As Boolean
        Dim returnSetting As Boolean = False

        Dim configSetting As DOConfigSetting =
                ECGlobals.CurrentFacility.ConfigInstanceVersion.AppConfigGroup(setting)
        If configSetting Is Nothing Then Return False
        Return configSetting.Enabled
    End Function

    'Private Sub InitPaintStyle(ByVal UserPaintStyleName As String)
    '    If DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSkinName = UserPaintStyleName Then
    '        Return
    '    End If

    '    DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(UserPaintStyleName)
    '    BarManager1.GetController().PaintStyleName = "Skin"
    '    ' solutionExplorer1.barManager1.GetController().PaintStyleName = "Skin"
    '    iPaintStyle.Caption = skinMask & UserPaintStyleName 'e.Item.Caption
    '    iPaintStyle.Hint = iPaintStyle.Caption
    '    iPaintStyle.ImageIndex = -1

    '    BarManager1.GetController().ResetStyleDefaults()

    '    'If PaintStyleName = "Default" Then
    '    '    PaintStyleName = "The Asphalt World"
    '    'End If

    '    'iPaintStyle.ImageIndex = -1 'item.ImageIndex
    '    'iPaintStyle.Caption = PaintStyleName ' item.Caption
    '    'iPaintStyle.Hint = PaintStyleName ' item.Description
    '    'BarManager1.GetController().ResetStyleDefaults()
    'End Sub

    'Private Sub InitPaintStyle(ByVal item As BarItem)
    '    If item Is Nothing Then
    '        Return
    '    End If
    '    iPaintStyle.ImageIndex = item.ImageIndex
    '    iPaintStyle.Caption = item.Caption
    '    iPaintStyle.Hint = item.Description

    '    'BarManager1.GetController().PaintStyleName = BarManager1.GetController().PaintStyleName
    '    BarManager1.GetController().ResetStyleDefaults()
    '    'solutionExplorer1.barManager1.GetController().PaintStyleName = barManager1.GetController().PaintStyleName
    '    'solutionExplorer1.barManager1.GetController().ResetStyleDefaults()
    'End Sub

    ''' <summary>
    ''' Add menu items for each old config Instance
    ''' Intialize ECGlobals.ConfigInstanceDict
    ''' </summary>
    ''' <remarks></remarks>
    Private Sub InitPrevChartVerMenu()
        If ECGlobals.CurrentFacility Is Nothing Then
            Return
        End If

        bsiPrevChartFormVer.ClearLinks()

        Dim CIDict As SortedDictionary(Of Date, DOConfigInstance) = ECGlobals.CurrentFacility.GetConfigInstanceDictionary(bIncludePending:=False)

        'integrity check (hmm.... perhaps we should do something like shutdown if the below Integrity check fails.
        ConfigInstanceConfigurationIntegrityCheck(CIDict)

        'GetMaxValidDate needs the list to be oldest to newest, thus why its stored global that way (in ECGlobals.ConfigInstanceDict), instead of with the
        'DateReverseComparer.
        ECGlobals.ConfigInstanceDict = New SortedList(Of Date, DOConfigInstance)(CIDict)

        Dim bft As Boolean = True
        Dim ci As DOConfigInstance = Nothing
        For Each ci In CIDict.Values
            Dim item As BarButtonItem
            If bft Then
                bft = False
                item = New BarButtonItem(BarManager1, "(Current) " & ci.MajorChangeDate.ToString & " : " & ExtractFormVersion(ci.ActiveFormClassName))
            Else
                item = New BarButtonItem(BarManager1, ci.MajorChangeDate & " : " & ExtractFormVersion(ci.ActiveFormClassName))
            End If
            item.Name = "biPrevChartVer" & ci.Oid
            item.Id = BarManager1.GetNewItemId()
            item.Tag = ci

            Me.bsiPrevChartFormVer.AddItem(item)
            AddHandler item.ItemClick, AddressOf OnPrevChartFormVerClick
        Next

        'ci should now point at the oldest one...
        'but more specifically, the last one in the list is the most recent minor change for "that"
        'majorchange
        If ci IsNot Nothing Then
            ci.IsOldest = True
        End If
    End Sub

    Private Function ExtractFormVersion(ByVal formName As String) As String
        Dim v As String = String.Empty
        Try
            Dim pos As Short = formName.LastIndexOf("V")
            If pos > 1 Then
                v = formName.Substring(pos)
            Else
                v = "V1"
            End If
        Catch
            v = "N/A"
        End Try

        Return v
    End Function

    Private Sub InitSkins()
        BarManager1.ForceInitialize()
        If BarManager1.GetController().PaintStyleName = "Skin" Then
            iPaintStyle.Caption = skinMask & DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSkinName
            iPaintStyle.Hint = iPaintStyle.Caption
        End If
        For Each cnt As DevExpress.Skins.SkinContainer In DevExpress.Skins.SkinManager.Default.Skins
            Dim item As BarButtonItem = New BarButtonItem(BarManager1, skinMask & cnt.SkinName)
            item.Name = "bi" & cnt.SkinName
            item.Id = BarManager1.GetNewItemId()
            iPaintStyle.AddItem(item)
            AddHandler item.ItemClick, AddressOf OnSkinClick
        Next cnt
    End Sub

    Sub LoadComboListsFromDisk(Optional ByVal bLoadIntoPendingCI As Boolean = False)
        Dim fbd As New FolderBrowserDialog
        fbd.SelectedPath = My.Computer.FileSystem.CurrentDirectory
        Dim result As DialogResult = fbd.ShowDialog()
        If result = DialogResult.Cancel Then Exit Sub

        Dim dir As New DirectoryInfo(fbd.SelectedPath)
        fbd.Dispose()

        DeleteLists()
        Me.Cursor = Cursors.WaitCursor
        Try
            For Each ffile As FileInfo In dir.GetFiles("*.txt")
                Debug.Print(ffile.Name)
                LoadCboListFromFile(bLoadIntoPendingCI, ffile)
            Next

            ' If Not bLoadIntoPendingCI Then ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
            '      MessageBox.Show("Completed Successfully", "Done", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        Catch
        Finally
            Cursor = Cursors.Default
        End Try
    End Sub

    'this is the same as the regular version (the other function) except it doesn't first delete all the lists, and  instead
    'it deletes a single list if it is found before adding new list...
    '
    'I didnt' want to modify original incase there are some issues with this new approach...
    '  Sub LoadComboListsFromDiskV2(progress As Progress(Of Integer), Optional ByVal bLoadIntoPendingCI As Boolean = False)
    Public Async Function LoadComboListsFromDiskV2(files() As FileInfo,
                                                   progressCallback As IProgress(Of Integer), deleteOldListsFirst As Boolean,
                                                   Optional ByVal bLoadIntoPendingCI As Boolean = False) As Task(Of Integer)

        Debug.WriteLine($"---------------------------LoadComboListsFromDiskV2--------------------------------------")

        If deleteOldListsFirst Then DeleteLists()

        Dim totalFiles = files.Count()
        Dim count = 1

        Try
            Dim result = Await Task.Run(Function()

                                            For Each ffile As FileInfo In files
                                                Debug.WriteLine($"---------------------------  For Each ffile As--------------------------------------")
                                                count += 1
                                                Dim percent = (count / totalFiles) * 100
                                                progressCallback.Report(percent)
                                                Debug.WriteLine(ffile.Name)
                                                LoadCboListFromFile(bLoadIntoPendingCI, ffile)
                                            Next
                                            Return count
                                        End Function).ConfigureAwait(False)

            Return result

        Catch ex As Exception
            ECLog.WriteExceptionError($"(LoadComboListsFromDiskV2) Error:{ex.Message}", ex)
        Finally
            Cursor = Cursors.Default
        End Try

        Return 0 'jjc this is mostly to suppress a warning msg until i can rethink/refactor this
    End Function


    Sub LoadSingleComboListsFromDisk(Optional ByVal bLoadIntoPendingCI As Boolean = False)

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = ".txt"
            ofd.Filter = String.Format("{0} ComboBox Config File (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            ECGlobals.ComboBoxListDir = ofd.FileName
            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim ListObj As DOConfigComboBoxList = LoadCboListFromFile(bLoadIntoPendingCI, ffile)
            If Not bLoadIntoPendingCI Then ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()

            MessageBox.Show(String.Format("'{0}' loaded successfully", ListObj.ListName), "Load List", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
            End Try
        End Try
    End Sub
    Public Shared Function isUTF8(ByVal pText As Byte()) As Boolean
        Dim expectedLength As Integer = 0

        For i As Integer = 0 To pText.Length - 1

            If (pText(i) And &*********) = &B0 Then
                expectedLength = 1
            ElseIf (pText(i) And &*********) = &********* Then
                expectedLength = 2
            ElseIf (pText(i) And &*********) = &********* Then
                expectedLength = 3
            ElseIf (pText(i) And &*********) = &********* Then
                expectedLength = 4
            ElseIf (pText(i) And &*********) = &********* Then
                expectedLength = 5
            ElseIf (pText(i) And &*********) = &********* Then
                expectedLength = 6
            Else
                Return False
            End If

            While System.Threading.Interlocked.Decrement(expectedLength) > 0

                If System.Threading.Interlocked.Increment(i) >= pText.Length Then
                    Return False
                End If

                If (pText(i) And &*********) <> &********* Then
                    Return False
                End If
            End While
        Next

        Return True
    End Function

    Private Function LoadCboListFromFile(bLoadIntoPendingCI As Boolean, ffileInfo As FileInfo) As DOConfigComboBoxList
        Dim listName = Path.GetFileNameWithoutExtension(ffileInfo.Name)
        Dim lfac As DOFacility = ECGlobals.CurrentFacility
        Dim lCI As DOConfigInstance = Nothing
        If bLoadIntoPendingCI Then
            lCI = lfac.PendingConfigInstanceVersion
        Else
            lCI = lfac.ConfigInstanceVersion
        End If

        Dim ListObj As New DOConfigComboBoxList
        ListObj.ListName = listName
        ListObj.ConfigInstance = lCI

        ListObj.Enabled = True
        ListObj.SortOrder = "USE SORT ORDER"
        ListObj.Save()

        Dim byteArray = File.ReadAllBytes(ffileInfo.FullName)

        Dim bIsValidUtf8 = isUTF8(byteArray)

        If bIsValidUtf8 = False Then
            byteArray = Encoding.Convert(Encoding.GetEncoding(1252), Encoding.UTF8, byteArray)
        End If

        Dim utf8String = Encoding.UTF8.GetString(byteArray)

        Dim delims As Char() = {vbCr, vbLf}
        Dim ListItems As String() = utf8String.Split(delims, StringSplitOptions.RemoveEmptyEntries)


        'Dim ListItems = utf8String.Split(Environment.NewLine) ', StringSplitOptions.RemoveEmptyEntries)

        'Dim ListItems2 As String = File.ReadAllText(ffileInfo.FullName)
        'Dim ListItems As String = File.ReadAllText(ffileInfo.FullName, New UTF8Encoding)
        'Dim ListItems3 As String = File.ReadAllText(ffileInfo.FullName, Encoding.GetEncoding(1252))


        'Dim look4 = ""
        'For Each tline In ListItems

        '    Dim ser As XmlSerializer = New XmlSerializer(tline.GetType)

        '    'Dim ser As XmlSerializer = New XmlSerializer(GetType(tline))
        '    Dim sourceText As String
        '    Using sr As New StringWriter
        '        ser.Serialize(sr, tline)
        '        Dim result = ser.Deserialize(New StringReader(sr.ToString))
        '    End Using



        '    'Dim byteArray() = Encoding.UTF8.GetBytes(tline)

        '    'For Each b In byteArray
        '    '    Debug.WriteLine(b)
        '    'Next

        '    'For Each tchar In tline

        '    '    If tchar = Chr(26) Then
        '    '        Debugger.Break()

        '    '    End If
        '    'Next
        'Next
        'Return Nothing

        Dim notdone = False
        Do
            Try
                notdone = False
                XpoDefault.Session.BeginTransaction()

                Dim o As Integer = 1000
                Dim linecount As Integer = 0
                For Each LI As String In ListItems
                    linecount += 1
                    Debug.WriteLine(LI)
                    If LI.Length = 0 Then Continue For 'ignore blank lines

                    'Check First Line
                    If linecount = 1 Then
                        If Regex.IsMatch(LI, "CriticalCare[(Exclusion)|(Inclusion)]? List", RegexOptions.IgnoreCase) Then
                            DeleteAllListsOfSameName(lCI, ListObj)
                            ListObj.Enabled = False
                            ListObj.Comments = "CriticalCare List - DON'T ENABLE THIS LIST"
                            ListObj.Save()
                            AddCriticalCareControlsList(ListObj.ListName, LI.ToUpper.Contains("INCLUSION"))
                            Continue For 'ignore this line
                        End If
                    End If

                    If Regex.IsMatch(LI, "^CONTROLNAME\s?:\s?\w+$", RegexOptions.IgnoreCase) Then 'optionial
                        Dim c As New DOConfigComboBoxListControl
                        c.ComboBoxList = ListObj
                        Dim m As Match = Regex.Match(LI, "^CONTROLNAME\s?:\s?(\w+$)", RegexOptions.IgnoreCase)
                        c.ComboBoxName = m.Groups(1).ToString
                        c.Enabled = True
                        c.Save()
                        ListObj.ComboBoxes.Add(c)
                    Else
                        Dim ListItem As New DOConfigComboBoxListsItem
                        ListItem.ItemDisplayName = LI
                        ListItem.ComboBoxList = ListObj
                        ListItem.Enabled = True
                        ListItem.ItemDisplayOrder = o
                        ListItem.Save()
                        ListObj.ListItems.Add(ListItem)
                        o += 10
                    End If
                Next

                '2018 NON CI Specific Lists -----------------------------------------
                '1. Check if is provider list
                '2. if so, change to shared CI
                If EnchartDOLib.Utils.IsSharedCboList(ListObj) Then
                    lCI = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
                End If

                DeleteAllListsOfSameName(lCI, ListObj)

                ListObj.ConfigInstance = lCI
                ListObj.Save()
                lCI.ComboBoxLists.Add(ListObj)
                '2018 NON CI Specific Lists -----------------------------------------

                If XpoDefault.Session.InTransaction Then
                    XpoDefault.Session.CommitTransaction()
                End If
            Catch ex As Exception
                Static retry = 2
                retry -= 1
                If retry > 0 Then
                    notdone = True
                End If
            End Try
        Loop While notdone

        Return ListObj
    End Function

    Private Shared Sub DeleteAllListsOfSameName(lCI As DOConfigInstance, ListObj As DOConfigComboBoxList)
        Dim OldListObj As DOConfigComboBoxList = lCI.GetFirstComboBoxList(ListObj.ListName)
        Do
            If OldListObj IsNot Nothing Then
                lCI.ComboBoxLists.Remove(OldListObj)
                OldListObj.Delete()
            End If
            OldListObj = lCI.GetFirstComboBoxList(ListObj.ListName)
        Loop While OldListObj IsNot Nothing
    End Sub

    'Currently LoadUserFacilitySpecificSettings needs to be called whenver a user and or facility changes
    Sub LoadUserFacilitySpecificSettings()
        ECGlobals.UserDefaultChartStatus = ECGlobals.CurrentUser.GetUserSetting("DefaultChartStatus", ECGlobals.CurrentFacility)
    End Sub

    'Enum UserListFieldsEnum As Integer
    '    ID = 0
    '    Name
    '    DefaultPermission
    '    AdminPermssion
    '    AuditPermission
    '    CompleteChartPermission
    '    DeleteChartPermission
    'End Enum
    Sub LoadUserListFromDisk()
        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        'Dim fileLayoutMsgForm As New LoadUsersFromFileForm
        'fileLayoutMsgForm.ShowDialog()
        'fileLayoutMsgForm.Close()

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = ".txt"
            ofd.Filter = String.Format("{0} Users (Coders) Permissions File (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim User As DOUser = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            For Each Line As String In Lines
                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")

                If fields.Length <> 16 Then
                    MessageBox.Show("The input file must have 16 fields per line." & vbCrLf & "Did you forget the Domain field?", "Incorrect number of fields")
                    Exit Sub
                End If

                Dim lUserID As String = fields(0)
                Dim domainName As String = fields(DOUser.UserListFieldsEnum.Domain)

                User = DOUser.GetUserByIdAndDomain(lUserID, domainName)
                User.UserName = fields(DOUser.UserListFieldsEnum.Name)
                User.DomainName = domainName

                If ECGlobals.UseActiveDirectoryLogIn Then
                    'User.Secret = ""
                    User.HashedPassword = ""
                    'ElseIf ECGlobals.UseEnhancedPasswords Then
                    '    Throw New NotImplementedException()
                    '    'User.Secret = ""
                    '    'User.PasswordExpirationDate = Now
                    '    'User.HashedPassword = Auth_MoreSecure.Hash(fields(DOUser.UserListFieldsEnum.Password)) ', User.UserID)
                    'Else
                    '    User.Secret = fields(DOUser.UserListFieldsEnum.Password)
                End If

                User.DefaultFacility = ECGlobals.CurrentFacility

                If fields(DOUser.UserListFieldsEnum.DefaultPermission) <> "" Then
                    User.AddRole(ECRoles.Default, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.AdminPermssion) <> "" Then
                    User.AddRole(ECRoles.Admin, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.AuditPermission) <> "" Then
                    User.AddRole(ECRoles.Auditor, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.CompleteChartPermission) <> "" Then
                    User.AddRole(ECRoles.ChartStatusComplete, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.DeleteChartPermission) <> "" Then
                    User.AddRole(ECRoles.ChartDelete, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.ChartUnlockPermission) <> "" Then
                    User.AddRole(ECRoles.UnLockChart, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.AdminReportsPermission) <> "" Then
                    User.AddRole(ECRoles.AdminReports, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.PhysCompleteChartPermission) <> "" Then
                    User.AddRole(ECRoles.PhysChartStatusComplete, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.ObsEditChartPermission) <> "" Then
                    User.AddRole(ECRoles.ObsEditChart, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.ObsCompleteChartPermission) <> "" Then
                    User.AddRole(ECRoles.ObsChartStatusComplete, ECGlobals.CurrentFacility)
                End If

                If fields(DOUser.UserListFieldsEnum.ViewChartHistory) <> "" Then
                    User.AddRole(ECRoles.ViewChartHistory, ECGlobals.CurrentFacility)
                End If

                Try
                    If fields(DOUser.UserListFieldsEnum.ReportsRole) <> "" Then
                        ' User.AddRole(ECRoles.PhysChartStatusComplete, ECGlobals.CurrentFacility)
                        Dim rr As DOUserFacilitySettings = User.GetUserSettingAsDOObject("ReportRole", ECGlobals.CurrentFacility)
                        rr.SettingValue = fields(DOUser.UserListFieldsEnum.ReportsRole)
                        rr.Save()
                        User.UserFacilitySettings.Add(rr)
                    End If
                Catch
                    'ignore it...
                End Try

                User.Save()
            Next

            MessageBox.Show("User records successfully imported.", "File Import Complete", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)

        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ' my.Application.
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
                'my.Application.
            End Try

            ' ofd.Dispose() '???
        End Try
    End Sub

    'Sub SaveUsersToDisk()
    '    Me.Cursor = Cursors.WaitCursor
    '    Try
    '        Dim fsa As New SaveFileDialog
    '        fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
    '        fsa.Filter = "Users and Permissions Config File|*.txt"
    '        fsa.DefaultExt = "*.txt"
    '        fsa.FileName = "UsersPermission.txt"

    '        Dim result As DialogResult = fsa.ShowDialog()
    '        If result = DialogResult.Cancel Then Exit Sub

    '        Dim fi As New FileInfo(fsa.FileName)
    '        Dim fs As FileStream

    '        If fi.Exists Then
    '            fs = fi.Open(FileMode.Truncate, FileAccess.Write)
    '        Else
    '            fs = fi.OpenWrite
    '        End If

    '        Dim sw As New StreamWriter(fs)

    '        Dim lUserCollection As New XPCollection(GetType(DOUser))
    '        If lUserCollection Is Nothing Then
    '            Beep()
    '            Return
    '        End If

    '        Dim sb As New StringBuilder
    '        For Each user As DOUser In lUserCollection
    '            sb.Length = 0

    '            sb.Append(user.UserID + ",")
    '            sb.Append(user.UserName + ",")
    '            sb.Append(IIf(user.Titrated, "X,", ","))

    '            'sb.Append(med.MedName + ",")
    '            'sb.Append(IIf(med.Titrated, "X,", ","))
    '            'sb.Append(IIf(med.CC, "X,", ","))
    '            'sb.Append(IIf(med.Titrated, "X,", ","))
    '            'sb.Append(IIf(med.DedicatedLine, "X,", ","))
    '            'sb.Append(IIf(med.Injected, "X,", ","))
    '            'sb.Append(IIf(med.NotInfused, "X", ""))
    '            sw.WriteLine(sb.ToString)
    '        Next

    '        sw.Close()

    '        MessageBox.Show("Medications successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

    '    Finally
    '        Me.Cursor = Cursors.Default
    '    End Try
    'End Sub

    Sub LookForErrors()
        Me.TestPending()

        '' AS IS STATNDS NOW, THIS FUNCTION TAKES AN HOUR OR TWO AND THEN RUNS OUT OF MEMORY ....
        'Dim rFound As Boolean = False
        'Dim cicoll = ECGlobals.CurrentFacility.ChartInfo

        'For Each ci In cicoll
        '    'Debug.WriteLine(String.Format("{0}, v.{1}", ci.VisitID, ci.CreationDate))
        '    Debug.WriteLine(String.Format("{0}", ci.CreationDate))
        '    rFound = False
        '    For Each c As DOChart In ci.Charts
        '        'Debug.WriteLine(String.Format("{0}, v.{1}", c.VisitID, c.Version))
        '        If rFound Then
        '            If c.ChartRedisplay.Count = 0 Then
        '                Debug.WriteLine("VisitID =" & c.VisitID.ToString)
        '                MessageBox.Show(String.Format("VisitID =" & c.VisitID.ToString))
        '            End If
        '        End If
        '        If c.ChartRedisplay.Count > 0 Then
        '            rFound = True
        '        End If
        '    Next
        'Next
    End Sub


    Private Sub CheckRELicenseExpiratioin()
        LicenseExpirationStatusBarControl.Visibility = BarItemVisibility.Never

        'Dim result = GetLicenseExpirationInDays()
        'If result.Succeeded Then
        '    LicenseExpirationStatusBarControl.Visibility = BarItemVisibility.Always
        '    LicenseExpirationStatusBarControl.Caption = $"License Expiration: {result.Value} day(s)"
        'Else
        '    LicenseExpirationStatusBarControl.Visibility = BarItemVisibility.Never
        'End If
    End Sub

    ''' <summary>
    ''' This method is triggered by devexpress when it's changing an object, either loading or saving.
    ''' Can be used to help debug issues....
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    <System.Diagnostics.CodeAnalysis.SuppressMessage("CodeRush", "Member is not implemented")>
    Private Sub SessionObjectChangedEventHandler(sender As Object, e As ObjectChangeEventArgs)
        'Dim changedObject =  e.Object

        'If TypeOf changedObject Is DOChart then
        '    Debugger.Break
        '    debug.WriteLine (e.NewValue)

        'End If
    End Sub

    Private Function GetServerVersion() As String
        Try
            'SELECT @@VERSION AS 'SQL Server Version'
            If XpoDefault.DataLayer.Connection.ConnectionString.ToUpper.Contains("INITIAL") Then
                Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                    cmd.CommandText = "SELECT @@VERSION AS 'SQL Server Version'"
                    Using reader = cmd.ExecuteReader()
                        reader.Read()
                        If reader(0) IsNot Nothing Then
                            Return reader(0)
                        End If

                    End Using
                End Using
            Else
                Return "Sybase"
            End If
        Catch ex As Exception
            Return "Error finding version info"
        End Try

        Return Nothing
    End Function

    Private Function GetSQLServerName() As String
        Try
            Using uow As New UnitOfWork()
                Dim rdata As SelectedData = uow.ExecuteQuery("SELECT @@SERVERNAME AS 'Server Name'")
                Return rdata?.ResultSet?(0).Rows?(0).Values?(0)
            End Using
        Catch
            Return "Error finding version info"
        End Try
    End Function
    Private Function GetSQLServerNameOld() As String
        Try
            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = "SELECT @@SERVERNAME AS 'Server Name'"
                Using reader = cmd.ExecuteReader()
                    reader.Read()
                    If reader(0) IsNot Nothing Then
                        Return reader(0)
                    End If
                End Using
            End Using
        Catch ex As Exception
            Return "Error finding version info"
        End Try
        Return Nothing
    End Function


    Private Function GetSQLDatabaseName() As String
        Try
            'SELECT @@VERSION AS 'SQL Server Version'
            If XpoDefault.DataLayer.Connection.ConnectionString.ToUpper.Contains("INITIAL") Then
                Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                    cmd.CommandText = "SELECT db_name() AS 'dbName'"
                    Using reader = cmd.ExecuteReader()
                        reader.Read()
                        If reader(0) IsNot Nothing Then
                            Return reader(0)
                        End If

                    End Using
                End Using
            Else
                Return "Sybase"
            End If
        Catch ex As Exception
            Return "Error finding version info"
        End Try

        Return Nothing
    End Function
    Private Async Sub MDIFrameForm_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyBase.Shown
        ' DOUser.IsTestAdmin = True
        Await Task.Yield() 'jjc 11.13.08 - this is to allow the form to load before we start doing stuff...

        If Not FirstTime Then 'nice variable name huh?
            FirstTime = True

            'show called during load
            Me.SplashScreen.Close()

            'The PublishUpdate application populates teh CurAppVer with this assemblies FileVersion
            'Dim fileVersion As String = FileVersionInfo.GetVersionInfo(System.Reflection.Assembly.GetExecutingAssembly().Location).FileVersion

            'Dim updateObj = ECGlobals.GetUpdaterObj()

            'If Not String.IsNullOrEmpty(updateObj.CurAppVer) AndAlso Not updateObj.CurAppVer.Equals(fileVersion) Then
            '    MessageBox.Show("Application out of date. Please contact customer support.", "Application out of date", MessageBoxButtons.OK, MessageBoxIcon.Error)
            'End If

            'If ECGlobals.UseActiveDirectoryLogIn = False AndAlso ECGlobals.UseEnhancedPasswords = False Then
            '    MessageBox.Show("Configuration error. Active Directory or Enhanced Password authentication must be enabled. Defaulting to ActiveDirectory.", "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '    'ECGlobals.UseActiveDirectoryLogIn = True
            '    'Application.Exit()
            '    'Return
            'End If

            If ProcessCommandLine() Then
                Me.ChangeUser()
                ECGlobals.StartUpArgs.Use = False
            Else
                Me.ChangeUser()
            End If

            If WorkStationValidationEnabled() Then
                If Not Me.IsValidWorkstation Then
                    MessageBox.Show("This workstation is not authorized to run! If you feel you got this message in error, please contact technical support.", "Machine name not found", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Application.Exit()
                    Return
                End If
            End If

            ECGlobals.StartUpArgs.UseLogin = False '06.16.09

            '06-01-07 - changed to this from above 2 lines

            ECGlobals.WorkingDay = Today

            'ask XPO to make sure there is table for each of our Data Objects...
            'jjc 11.13.08 we already do this in the load event... i dont' think we need this...
            'UpdateDBSchema()
        End If
    End Sub


    'Private Sub MultiUserLoginEventHandler(ByVal sender As Object, ByVal e As EventArgs)
    '    If ECGlobals.CurrentUser IsNot Nothing Then
    '        If ECGlobals.CurrentUser.UserLoggedInSomeWhereElse = False Then
    '            ECLog.WriteEntry("MDIFrameForm.MultiUserLoginEventHandler : This is an errant call/bug that needs to be looked at. Exiting without calling ForceLogOut", TraceEventType.Warning)
    '            Exit Sub
    '        End If
    '    Else
    '        Debug.Assert(True, "Why is user = nothing?")
    '    End If

    '    ECLog.WriteEntry("MDIFrameForm.MultiUserLoginEventHandler : Sender= " & sender.GetHashCode.ToString & "calling ForceLogOut", TraceEventType.Verbose)
    '    ForceLogOut()
    'End Sub

    Private Sub OnPrevChartFormVerClick(ByVal sender As Object, ByVal e As ItemClickEventArgs)
        Dim ci As DOConfigInstance = e.Item.Tag
        Try
            CreateChartForm(ci)?.Show()
        Catch ex As Exception
            Dim msg = $"The selected ChartForm could not be created."
            MessageBox.Show(Me, msg, "Error")
            ECLog.WriteExceptionError($"OnPrevChartFormVerClick", ex)
        End Try


    End Sub

    Private Sub OnSkinClick(ByVal sender As Object, ByVal e As ItemClickEventArgs)
        Dim skinName As String = e.Item.Caption.Replace(skinMask, "")
        DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(skinName)
        BarManager1.GetController().PaintStyleName = "Skin"
        ' solutionExplorer1.barManager1.GetController().PaintStyleName = "Skin"
        iPaintStyle.Caption = e.Item.Caption
        iPaintStyle.Hint = iPaintStyle.Caption
        iPaintStyle.ImageIndex = -1

        SaveUserPaintStyle()
    End Sub

    Sub SaveComboListsToDisk()
        Me.Cursor = Cursors.WaitCursor
        Try
            Dim fbd As New FolderBrowserDialog
            fbd.SelectedPath = My.Computer.FileSystem.CurrentDirectory
            Dim result As DialogResult = fbd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim dir As New DirectoryInfo(fbd.SelectedPath)
            fbd.Dispose() '???

            For Each cbolist As DOConfigComboBoxList In ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists
                SaveSingleComboList(cbolist, dir)
            Next

            For Each cbolist As DOConfigComboBoxList In ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists
                SaveSingleComboList(cbolist, dir)
            Next
            MessageBox.Show("Lists successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    Sub SaveMedicationsToDisk()
        Me.Cursor = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "Medications Config File|*.txt"
            fsa.DefaultExt = "*.txt"
            fsa.FileName = "Medications.txt"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)

            Dim sb As New StringBuilder
            For Each med In ECGlobals.CurrentFacility.ConfigInstanceVersion.Medications
                sb.Length = 0
                sb.Append(med.MedName + ",")
                ' sb.Append(IIf(med.Titrated, "X,", ","))
                sb.Append(IIf(med.CC, "X,", ","))
                sb.Append(IIf(med.DedicatedLine, "X,", ","))
                ' sb.Append(IIf(med.Injected, "X,", ","))
                ' sb.Append(IIf(med.NotInfused, "X,", ","))
                ' sb.Append(IIf(med.Obs, "X,", ",")) '08.06.12 ---  '11.27.12 - Modded
                sb.Append(IIf(med.Chemo, "X,", ",")) '11.27.2012
                sb.Append(IIf(med.Hormonal, "X", "")) '05.2014
                sw.WriteLine(sb.ToString)
            Next

            sw.Close()

            MessageBox.Show("Medications successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    Sub SaveDeductedTimesToDisk()
        Me.Cursor = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "Deducted Times Config File|*.txt"
            fsa.DefaultExt = "*.txt"
            fsa.FileName = "DeductedTimes.txt"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)

            Dim sb As New StringBuilder
            For Each ddt In ECGlobals.CurrentFacility.ConfigInstanceVersion.DeductedTimes
                sb.Length = 0
                sb.Append(ddt.Procedure + ",")
                sb.Append(ddt.DeductedHours.ToString + ",")
                sb.Append(ddt.DeductedMinutes.ToString + ",")
                sb.Append(IIf(ddt.TimeOutsideOfObservation, "X,", ","))
                sb.Append(IIf(ddt.SeparateBillableProcedures, "X,", ","))
                sw.WriteLine(sb.ToString)
            Next

            sw.Close()

            MessageBox.Show("Deducted Times successfully saved to disk", "Save Complete", MessageBoxButtons.OK)

        Finally
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    Public Sub LoadDeductedTimesFromFile(Optional ByVal bLoadIntoPendingCI As Boolean = False)
        Dim lFac As DOFacility = ECGlobals.CurrentFacility
        Dim lCI As DOConfigInstance = Nothing

        If bLoadIntoPendingCI Then
            lCI = lFac.PendingConfigInstanceVersion
        Else
            lCI = lFac.ConfigInstanceVersion
        End If

        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Dim ConfigInstance As DOConfigInstance = lCI 'ECGlobals.CurrentFacility.ConfigInstanceVersion

        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = "DeductedTimes.txt"
            ofd.Filter = String.Format("{0} Deducted Times definition file (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim deductedTimes As DODeductedTimes = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            For Each Line As String In Lines
                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                Dim procedure As String = fields(0)
                deductedTimes = ConfigInstance.GetDeductedTimeByProcedure(procedure)
                If deductedTimes Is Nothing Then
                    deductedTimes = New DODeductedTimes With {.Procedure = procedure, .Enabled = True}
                End If
                deductedTimes.ConfigInstance = ConfigInstance 'ECGlobals.CurrentFacility.ConfigInstanceVersion

                Try
                    If fields(DODeductedTimes.FieldsEnum.Procedure) <> "" Then
                        deductedTimes.Procedure = fields(DODeductedTimes.FieldsEnum.Procedure)
                    End If

                    If fields(DODeductedTimes.FieldsEnum.DeductedHours) <> "" Then
                        deductedTimes.DeductedHours = fields(DODeductedTimes.FieldsEnum.DeductedHours)
                    End If

                    If fields(DODeductedTimes.FieldsEnum.DecutedMinutes) <> "" Then
                        deductedTimes.DeductedMinutes = fields(DODeductedTimes.FieldsEnum.DecutedMinutes)
                    End If

                    If fields(DODeductedTimes.FieldsEnum.TimeOutsideOfObservation) <> "" Then
                        deductedTimes.TimeOutsideOfObservation = True
                    End If

                    If fields(DODeductedTimes.FieldsEnum.SeparateBillableProcedure) <> "" Then
                        deductedTimes.SeparateBillableProcedures = True
                    End If

                Catch
                    If deductedTimes.Oid < 0 Then
                        ConfigInstance.DeductedTimes.Add(deductedTimes)
                    End If
                    deductedTimes.Save()
                    Continue For
                End Try
                If deductedTimes.Oid < 0 Then
                    ConfigInstance.DeductedTimes.Add(deductedTimes)
                End If
                deductedTimes.Save()
            Next

            ConfigInstance.Save()

            If Not bLoadIntoPendingCI Then
                ReloadAllTabComboBoxes()
            End If

            MessageBox.Show(String.Format("Deducted Times loaded successfully"), "Load List", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ' my.Application.
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
                'my.Application.
            End Try

            ' ofd.Dispose() '???
        End Try
    End Sub

    Sub SaveFinancialClassMappingsToDisk()
        Me.Cursor = Cursors.WaitCursor
        Try
            Dim fsa As New SaveFileDialog
            fsa.InitialDirectory = My.Computer.FileSystem.CurrentDirectory
            fsa.Filter = "Financial Class Mappings Config File|*.txt"
            fsa.DefaultExt = "*.txt"
            fsa.FileName = "FinancialClassMappings.txt"

            Dim result As DialogResult = fsa.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim fi As New FileInfo(fsa.FileName)
            Dim fs As FileStream

            If fi.Exists Then
                fs = fi.Open(FileMode.Truncate, FileAccess.Write)
            Else
                fs = fi.OpenWrite
            End If

            Dim sw As New StreamWriter(fs)

            Dim sb As New StringBuilder
            For Each fcm In ECGlobals.CurrentFacility.ConfigInstanceVersion.FinancialClassMappings
                sb.Length = 0
                sb.Append(fcm.FinancialClass + ",")
                sb.Append(fcm.PayorRule.ToString)

                sw.WriteLine(sb.ToString)
            Next

            sw.Close()

            MessageBox.Show("Financial Class Mappings successfully saved to disk", "Save Complete", MessageBoxButtons.OK)
        Catch ex As Exception
            ECLog.WriteExceptionError("SaveFinancialClassMappingsToDisk()", ex, True)
        Finally
            Me.Cursor = Cursors.Default
        End Try
    End Sub
    Public Sub LoadFinancialClassMappingsFromFile(Optional ByVal bLoadIntoPendingCI As Boolean = False)
        Dim lFac As DOFacility = ECGlobals.CurrentFacility
        Dim lCI As DOConfigInstance = Nothing

        If bLoadIntoPendingCI Then
            lCI = lFac.PendingConfigInstanceVersion
        Else
            lCI = lFac.ConfigInstanceVersion
        End If

        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Dim ConfigInstance As DOConfigInstance = lCI 'ECGlobals.CurrentFacility.ConfigInstanceVersion

        Try
            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = "FinancialClassMappings.txt"
            ofd.Filter = String.Format("{0} FinancialClassMappings definition file (*.txt) | *.txt", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim financialClassMapping As DOFinancialClassMapping = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            For Each Line As String In Lines
                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                Dim mapping As String = fields(0)
                financialClassMapping = ConfigInstance.GetFinancialClassMappingByFinancialClass(mapping)
                If financialClassMapping Is Nothing Then
                    financialClassMapping = New DOFinancialClassMapping With {.FinancialClass = mapping, .Enabled = True}
                End If
                financialClassMapping.ConfigInstance = ConfigInstance 'ECGlobals.CurrentFacility.ConfigInstanceVersion

                Try
                    'If fields(DOFinancialClassMapping.FieldsEnum.FinancialClass) <> "" Then
                    '    financialClassMapping.FinancialClass = fields(DOFinancialClassMapping.FieldsEnum.FinancialClass)
                    'End If

                    If fields(DOFinancialClassMapping.FieldsEnum.PayorRule) <> "" Then
                        financialClassMapping.PayorRule = fields(DOFinancialClassMapping.FieldsEnum.PayorRule)
                    End If

                Catch ex As Exception
                    If financialClassMapping.Oid < 0 Then
                        ConfigInstance.FinancialClassMappings.Add(financialClassMapping)
                    End If
                    financialClassMapping.Save()
                    Continue For
                End Try
                If financialClassMapping.Oid < 0 Then
                    ConfigInstance.FinancialClassMappings.Add(financialClassMapping)
                End If
                financialClassMapping.Save()
            Next

            ConfigInstance.Save()

            'If Not bLoadIntoPendingCI Then
            '    ReloadAllTabComboBoxes()
            'End If

            MessageBox.Show(String.Format("FinancialClassMappings loaded successfully"), "Load List", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Finally
            Try
                Directory.SetCurrentDirectory(curdir.FullName)
            Catch ex2 As Exception
                ' my.Application.
                ECLog.WriteEntry("Error Resetting Current Directory", TraceEventType.Error)
                'my.Application.
            End Try

            ' ofd.Dispose() '???
        End Try
    End Sub

    Private Sub SaveUserPaintStyle()
        'ByVal PaintStyleName As String)
        If ECGlobals.UseUserPaintStyles Then
            ECGlobals.CurrentUser.PaintStyle = DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSkinName
            ECGlobals.CurrentUser.Save()
        End If
    End Sub

    Private Sub SetupCClientAndFacilityComboBoxes()

        Dim UserFacList As New List(Of DOFacility)
        Dim AllFacilitiesList As New XPCollection(GetType(DOFacility)) ', CriteriaOperator.Parse(String.Format("UserName = '{0}'", ECGlobals.CurrentUser.UserName.ToUpper)))
        For Each fc As DOFacility In AllFacilitiesList
            If ECGlobals.CurrentUser.IsTheAICAdmin Then
                UserFacList.Add(fc)
            Else
                For Each PermissionObj As DOUserPermission In ECGlobals.CurrentUser.Permissions
                    If PermissionObj.Enabled AndAlso PermissionObj.Facility = fc Then
                        UserFacList.Add(fc)
                        Exit For
                    End If
                Next
            End If
        Next

        UserFacList.Sort()
        RepositoryItemComboBox3.Items.Clear()
        For Each facility In UserFacList
            RepositoryItemComboBox3.Items.Add(facility)
        Next

        Try
            If Not UserFacList.Contains(ECGlobals.CurrentUser.DefaultFacility) Then
                MessageBox.Show("The current user doesn't have any permissions for the configured default facility for this user", "Config Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            beiFacility.EditValue = ECGlobals.CurrentUser.DefaultFacility

        Finally
        End Try
    End Sub



    Sub TestPending()
        Dim frm = CreateChartForm(ECGlobals.CurrentFacility.PendingConfigInstanceVersion)
        frm.Show()
    End Sub


    ''' <summary>
    ''' Once we know we want to shutdown, this 1 second timer gets enabled.
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Async Sub ShutDownTimer_Tick(sender As Object, e As EventArgs) Handles ShutDownTimer.Tick
        Dim shutdown As Boolean = False
        Dim msg As String = ""

        If ECGlobals.CurrentUser Is Nothing Then
            shutdown = True
        Else
            If MaintenanceModeSettings?.ForcefullyQuitTime.HasValue Then

                Dim duration = MaintenanceModeSettings.ForcefullyQuitTime.Value.Subtract(Now())

                If duration.TotalSeconds >= 60 Then
                    Dim minsLeft = duration.Minutes
                    If minsLeft = 1 Then minsLeft = 2
                    msg = $"Forced Shutdown in {minsLeft} minute(s)"
                ElseIf duration.TotalSeconds >= 0 Then
                    StatusBarMessage.Appearance.BackColor = Color.Red
                    StatusBarMessage.Appearance.Options.UseBackColor = True
                    msg = $"Forced Shutdown in {duration.Seconds} second(s)"
                Else
                    shutdown = True
                End If
            End If
        End If

        If shutdown Then
            msg = $"Shutting down..."
            StatusBarMessage.Caption = msg
            ShutDownTimer.Stop()
            Await Task.Delay(5000)

            CloseAllOpenForms()
            ECGlobals.CurrentUser?.LogOut()
            Application.Exit()
        End If

        StatusBarMessage.Caption = msg
    End Sub
    Private Sub Timer1Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles IdleUserTimer.Tick
        Me.HandleIdleUserTimerTick()
    End Sub


    Private Sub CheckConfigIntegrity()
        If EnchartDOLib.Utils.IsDbSchemaUpdateNeeded() Then
            If DialogResult.OK = MessageBox.Show($"The database schema is out of date and needs to be updated.", "Database update needed", MessageBoxButtons.OK, MessageBoxIcon.Warning) Then
                'UpdateSchema()
            End If
            ECGlobals.CurrentUser?.LogOut()
            Application.Exit()
        End If
    End Sub

    Sub UpdateDBSchema()
        Try
            ECLog.WriteEntry("UpdateDBSchema)", TraceEventType.Verbose)
            'After trying a number of different things... the below call appears to be all that is
            'needed to ask XPO to make sure there is table for each of our Data Objects...
            Session.DefaultSession.UpdateSchema()
        Catch ex As Exception
            Dim msg As String = String.Format("An error occurred while trying to update the database schema.")
            ECLog.WriteEntry(msg, TraceEventType.Error)

            'MessageBox.Show(msg, "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ECLog.WriteEntry("UpdateDBSchema) - Leaving", TraceEventType.Verbose)
        End Try
    End Sub

    Private Sub XtraTabbedMdiManager1_SelectedPageChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles XtraTabbedMdiManager1.SelectedPageChanged
        Try
            If Me.XtraTabbedMdiManager1.Pages.Count > 0 Then
                Dim page As ChartBaseForm
                page = Me.ActiveMdiChild

                If page IsNot Nothing Then

                    Me.Text = ECGlobals.ProductNameString
                    Me.Text += " - " & page.Text
                Else
                    Me.Text = ECGlobals.ProductNameString
                End If
            Else
                Me.Text = ECGlobals.ProductNameString
            End If
        Catch ex As Exception
            ECLog.WriteEntry("The following error occurred in XtraTabbedMdiManager1_SelectedPageChanged : " & ex.Message, TraceEventType.Error)
        End Try
    End Sub

#End Region 'Methods

#Region "Nested Types"

    Friend Class DateReverseComparer
        Implements IComparer(Of Date)

#Region "Methods"

        Public Function Compare(ByVal x As Date, ByVal y As Date) As Integer Implements System.Collections.Generic.IComparer(Of Date).Compare
            Return Date.Compare(y, x)
        End Function

#End Region 'Methods

    End Class

#End Region 'Nested Types

    Private Sub bbiChangeVisitID_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiChangeVisitID.ItemClick

        If Me.XtraTabbedMdiManager1.Pages.Count > 0 Then
            Dim page As ChartBaseForm
            page = Me.ActiveMdiChild
            'page should never be nothing.. but occasionly is, so for now, we'll just test it
            'but it stinks of a bigger problem....
            If page IsNot Nothing AndAlso page.Chart IsNot Nothing Then
                'If Not ECGlobals.CurrentUser.IsInRole(ECRoles.Admin) Then
                '    MessageBox.Show("You do not have the necessary permssions to change a Visit ID.", "Action Forbidden", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                '    Return
                'End If

                If Not String.IsNullOrEmpty(page.Chart.VisitID) Then
                    Dim nf As New ChangeVisitIDForm
                    nf.VisitIdOriginal = page.Chart.VisitID
                    Dim VistIDControl As DevExpress.XtraEditors.TextEdit = page.FindControl("VisitID")
                    nf.EditMask = VistIDControl.Properties.Mask.EditMask

                    If nf.ShowDialog() = DialogResult.OK Then
                        If VistIDControl IsNot Nothing Then
                            VistIDControl.EditValue = nf.VisitIdnew
                            page.MarkAsDirty()
                            Return
                        End If
                    Else 'User clicked Cancel Button
                        Return
                    End If
                End If
            End If
        End If
        MessageBox.Show("You must open an existing chart before you can change the Visit ID.", "No Chart Open", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    End Sub

    Private Sub RunEnhancedPassworUtil_ClickHandler(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem21.ItemClick
        Try
            Dim info As New ProcessStartInfo
            Dim ExeToRun As String = "EnhancedPasswordUtility.exe"

            info.FileName = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, ExeToRun)

            Dim sp As New Process()
            sp.StartInfo = info
            sp.Start()

        Catch ex As Exception
            ECLog.WriteEntry("The following error occred trying to run Enhanced Password Utility : " & ex.Message.ToString, TraceEventType.Error)
        End Try
    End Sub

    Private Sub BarButtonItem22_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem22.ItemClick
        SaveMedicationsToDisk()
    End Sub
    Private Sub bbiEditObsListPhysician_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditObsListPhysician.ItemClick ' Handles BarButtonItem24.ItemClick
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("ObsTimesFinalBillHoursPhys_cbo")

        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList
            cboList.ListName = "ObsPhysicianList"
            cboList.Enabled = True
            Dim cboControl As New DOConfigComboBoxListControl
            cboControl.ComboBoxList = cboList
            cboControl.ComboBoxName = "ObsTimesFinalBillHoursPhys_cbo"
            cboControl.Enabled = True
            cboControl.Save()
            cboList.ComboBoxes.Add(cboControl)
            cboList.ConfigInstance = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
            cboList.Save()
            ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists.Add(cboList)
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Obs Physician List")
        If f.ShowDialog() = DialogResult.OK Then
            '       ReloadAllTabComboBoxes()
        End If
        f.Dispose()
        Return
    End Sub

    Private Sub bbiEditObsListNurse_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditObsListNurse.ItemClick
        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.SharedConfigInstanceVersion.GetComboBoxListByControlName("Obs_Medication_ObsNurse_cbo")

        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList
            cboList.ListName = "ObsNurseList"
            cboList.Enabled = True
            Dim cboControl As New DOConfigComboBoxListControl
            cboControl.ComboBoxList = cboList
            cboControl.ComboBoxName = "Obs_Medication_ObsNurse_cbo"
            cboControl.Enabled = True
            cboControl.Save()
            cboList.ComboBoxes.Add(cboControl)
            cboList.ConfigInstance = ECGlobals.CurrentFacility.SharedConfigInstanceVersion
            cboList.Save()
            ECGlobals.CurrentFacility.SharedConfigInstanceVersion.ComboBoxLists.Add(cboList)
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Obs Nurse List")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
        End If
        f.Dispose()
        Return
    End Sub

    Private Sub BarButtonItem24_ItemClick_1(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCdmEditor.ItemClick

        Dim cdm = New CDMReports.CDMReports() 'ECGlobals.CurrentFacility.Oid & " " & ECGlobals.CurrentUser.Oid)
        cdm.InitFromUI(ECGlobals.CurrentUser, ECGlobals.CurrentFacility)
        cdm.ShowDialog()

        'LaunchCdmEditor()
    End Sub

    Private Shared Sub LaunchCdmEditor()
        Try
            Dim info As New ProcessStartInfo
            Dim ExeToRun As String = "CDMViewer.exe"

            '#If DEBUG Then
            '            info.FileName = Path.Combine(Environment.CurrentDirectory, ExeToRun)
            '#Else
            info.FileName = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, ExeToRun)
            '#End If
            info.Arguments = ECGlobals.CurrentFacility.Oid & " " & ECGlobals.CurrentUser.Oid

            Dim sp As New Process()
            sp.StartInfo = info
            sp.Start()

        Catch ex As Exception
            ECLog.WriteEntry("The following error occred trying to run CDMViewer : " & ex.Message.ToString, TraceEventType.Error)
        End Try
    End Sub

    Private Sub BarButtonItem25_ItemClick(sender As System.Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem25.ItemClick
        Dim f As New ConfigDeductedTimesForm
        f.ShowDialog()
        ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
    End Sub

    Private Sub BarButtonItem27_ItemClick(sender As System.Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem27.ItemClick
        SaveDeductedTimesToDisk()
    End Sub

    Private Sub BarButtonItem28_ItemClick(sender As System.Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem28.ItemClick
        Dim f As New LoadDeductedTimesForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem29_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem29.ItemClick
        Dim f As New ConfigFinancialClassMappingForm
        f.ShowDialog()
        ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
    End Sub

    Private Sub BarButtonItem31_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem31.ItemClick
        Dim f As New LoadFinancialClassMappingsForm
        f.ShowDialog()
    End Sub

    Private Sub BarButtonItem30_ItemClick(ByVal sender As System.Object, ByVal e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem30.ItemClick
        SaveFinancialClassMappingsToDisk()
    End Sub

    Private Sub BarButtonItem32_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem32.ItemClick
        ShowDBServerInfo()

    End Sub


    Private Sub ShowDBServerInfo()
        Dim msg As New StringBuilder
        'Dim dbname As String = GetSQLDatabaseName()
        msg.AppendLine($"{My.Application.Info.ProductName} Assembly Version: {My.Application.Info.Version.ToString}")
        msg.Append(vbCrLf)

        If XpoDefault.DataLayer.Connection?.ConnectionString Is Nothing Then
            msg.Append(vbCrLf)
            ' msg.Append(vbCrLf)
            msg.AppendLine("---------- ESS Service ---------- ")
            msg.Append(vbCrLf)
            'msg.Append(vbCrLf)
            msg.Append($"ESS Server: {SecurityAuthManager.DefaultInstance.ESSUrl}")
            msg.Append(vbCrLf)
            msg.Append($"Current AccessToken Expiration: {SecurityAuthManager.DefaultInstance.AccessTokenExpiration}")

        ElseIf XpoDefault.DataLayer.Connection.ConnectionString.ToUpper.Contains("INITIAL") Then
            msg.Append(String.Format("Server Name = '{0}' ({1})", GetSQLServerName(), GetSQLDatabaseName))
            msg.Append(vbCrLf)
            msg.Append(vbCrLf)
            msg.AppendLine("---------- Server Version Info ---------- ")
            msg.Append(vbCrLf)
            msg.Append(GetServerVersion().Replace(vbTab, ""))

        Else
            Dim sVer = GetSybaseDatabaseVersion()
            msg.Append("SQL Anywhere ")
            If sVer IsNot Nothing Then
                msg.Append(sVer)
                msg.Append(vbCrLf)
                msg.Append(vbCrLf)
                msg.AppendLine("---------- Server Version Info ---------- ")
                If GetSapDatabaseEdition() IsNot Nothing Then
                    msg.Append(GetSapDatabaseEdition())
                End If
            Else
                msg.Append("9 Something or other")
            End If
        End If

        MessageBox.Show(msg.ToString, "Server Info")
    End Sub

    Private Function GetSybaseDatabaseVersion() As String
        Try
            'SELECT @@VERSION AS 'SQL Server Version'
            Dim ver As String = Nothing
            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = "SELECT PROPERTY ( 'ProductVersion') as 'version'"
                Using reader = cmd.ExecuteReader()
                    reader.Read()
                    If reader(0) IsNot Nothing Then
                        ver = reader(0)
                        Return ver
                    End If

                End Using
            End Using
            Return "N/A"

        Catch ex As Exception
            Return "Error finding version info"
        End Try

        Return Nothing
    End Function

    Private Function GetSapDatabaseEdition() As String
        Try
            'SELECT @@VERSION AS 'SQL Server Version'
            Dim ver As String = Nothing
            Using cmd As IDbCommand = XpoDefault.DataLayer.CreateCommand
                cmd.CommandText = "SELECT PROPERTY ( 'ServerEdition') as 'edition'"
                Using reader = cmd.ExecuteReader()
                    reader.Read()
                    If reader(0) IsNot Nothing Then
                        ver = reader(0)
                        Return ver
                    End If

                End Using
            End Using
            Return Nothing

        Catch ex As Exception
            Return Nothing
        End Try

        Return Nothing
    End Function
    Private Sub bsiVersion_ItemDoubleClick(sender As Object, e As ItemClickEventArgs) Handles bsiVersion.ItemDoubleClick
        ShowDBServerInfo()
    End Sub

    Private Sub BarButtonItem33_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem33.ItemClick
        AutoLogOFf()
    End Sub

    Private Sub bbAuditLogViewer_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbAuditLogViewer.ItemClick
        ShowAuditView()
    End Sub

    Private Sub ShowAuditView()
        AuditLogger.Reports.AuditLogView()
        Dim f As New AuditLogViewer
        f.ShowDialog()
        f.Close()
    End Sub

    Private Sub BarButtonItem23_ItemClick_2(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem23.ItemClick

        Dim f As New UtilCreateChartsForm
        f.ShowDialog()
    End Sub

    Private Sub bbMedicationsViewer_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbMedicationsViewer.ItemClick
        Dim f As New ConfigMedicationsFormV2

        'f.ConfigEnbeddedNavigator(ECGlobals.CurrentUser.IsInRole(ECRoles.Admin),
        '                          ECGlobals.CurrentUser.IsAdmin)
        f.ShowDialog()

        ECGlobals.CurrentFacility.ConfigInstanceVersion.ResetEnabledMedicationsList()
    End Sub

    Private Sub BarButtonItem24_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem24.ItemClick
        Using f As New ShowAdminUsersForm
            f.ShowDialog()
        End Using
    End Sub

    Private Sub BarButtonItem36_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiDeductedTimes.ItemClick
        Dim f As New ConfigDeductedTimesForm
        f.ShowDialog()
        ' ECGlobals.CurrentFacility.ConfigInstanceVersion.Save()
    End Sub

    Private Sub ConvertEspcodesTo2018ButtonClicked(sender As Object, e As ItemClickEventArgs) Handles bbiConvertEspcodesTo2018.ItemClick
        ConvertEspcodesTo2018()
    End Sub

    Private Async Sub ConvertEspcodesTo2018()
        Dim userResponse = MessageBox.Show("Are you sure you want to migrate espcodes to new DOEspCodes2018 Table?", "EsPCode Migration", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning)
        If userResponse <> DialogResult.OK Then Exit Sub

        DOESPCode2018.DeleteAllEspcodes()

        Dim dlg As New DisplayMsgFormv2() With {.Text = "Processing Espcodes"}

        dlg.Show()

        Dim progressIndicator As New Progress(Of String)(Sub(msg) dlg.Msg = msg)

        Try
            Dim result = Await Task.Run(Function() MigrateEspocodes(progressIndicator))
        Catch ex As Exception
            ECLog.WriteExceptionError("ConvertEspcodesTo2018", ex, True)
            Exit Sub
        Finally
            dlg.Close()
        End Try

        MessageBox.Show("Probably saved to db successfully... go check!", "Important Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function MigrateEspocodes(progressIndicator As IProgress(Of String)) As List(Of DOESPCode2018)
        Dim codeMigrationEngine = New ESPCode2018Migrator()
        Dim results = codeMigrationEngine.MigrateEspocodesTo2018VersionsV1(progressIndicator)

        Dim updatedEspCodesList = codeMigrationEngine.TokenizeEspcodes(results)
        progressIndicator.Report("Saving Results To DB ... Please wait") ' .Msg = "Saving Results To DB ... Please wait"
        Try
            DbPollingTimer.Stop()
            XpoDefault.Session.Save(updatedEspCodesList)

        Finally
            DbPollingTimer.Start()
        End Try

        Return updatedEspCodesList
    End Function

    Private Sub BarButtonItem36_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem36.ItemClick
        Dim f As New Espcodes2018Form
        f.Show()
    End Sub

    Private Sub BarButtonItem38_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem38.ItemClick
        Dim tmpForm As New ConfigComboBoxListsForm(ECGlobals.CurrentFacility.SharedConfigInstanceVersion)
        tmpForm.Show()
    End Sub

    Private Sub BarButtonItem39_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem39.ItemClick
        DeleteSharedCboLists()
    End Sub

    Private Sub BarButtonItem40_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem40.ItemClick
        Dim f As New GlobalSettingsForm()
        '     f.Owner = Me
        f.StartPosition = FormStartPosition.CenterParent
        f.ShowDialog(Me)
    End Sub


    Private Sub BarButtonItem42_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem42.ItemClick

    End Sub

    Private Sub BarButtonItem43_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem43.ItemClick
        System.Diagnostics.Process.Start("https://central.allscripts.com")
    End Sub

    Private Sub BarButtonItem44_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiManualEntryFacility.ItemClick

        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxListByControlName("Supplies_ManualEntry_SupplyNum01_cbo")
        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList

            cboList.ListName = "Supplies_ManualEntry_SupplyNum_cbo"
            cboList.Enabled = True
            cboList.ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
            ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists.Add(cboList)
            cboList.Save()
        End If

        Const NumberOfManualEntryControls As Integer = 20  ' Define a constant for clarity
        If cboList.ComboBoxes?.Count < NumberOfManualEntryControls Then
            For i = 1 To NumberOfManualEntryControls
                Dim controlName = $"Supplies_ManualEntry_SupplyNum{i:D2}_cbo"

                If cboList.ComboBoxes.Any(Function(cbo) cbo.ComboBoxName = controlName) Then
                    Continue For
                End If

                Dim cboControl As New DOConfigComboBoxListControl
                cboControl.ComboBoxList = cboList
                cboControl.ComboBoxName = controlName
                cboControl.Enabled = True
                cboControl.Save()
                cboList.ComboBoxes.Add(cboControl)
            Next
        End If

        If ECGlobals.FacilityTipVersionCISpecific.EnableClinicMode Then
            Const MaxClinicManualEntryControls As Integer = 6  ' Define a constant for clarity

            For i = 1 To MaxClinicManualEntryControls
                Dim controlName = $"Clinic_ManualEntry_Cdm{i:D2}cbo"

                ' Check if the control already exists
                If cboList.ComboBoxes.Any(Function(cbo) cbo.ComboBoxName = controlName) Then
                    Continue For
                End If

                Dim cboControl As New DOConfigComboBoxListControl With {
                .ComboBoxList = cboList,
                .ComboBoxName = controlName,
                .Enabled = True
                }
                cboControl.Save()
                cboList.ComboBoxes.Add(cboControl)
            Next
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Manual Entry")
        If f.ShowDialog() = DialogResult.OK Then
            'ReloadAllTabComboBoxes()
            'UpdatePreviousConfigInstance(cboList)
        End If

        f.Dispose()
        Return

    End Sub

    Private Sub BarButtonItem45_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiManualEntryObservation.ItemClick

        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxListByControlName("ObsTimesManEntryCode_txt_01")
        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList

            cboList.ListName = "ObsManualEntry"
            cboList.Enabled = True
            cboList.ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
            ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists.Add(cboList)
            cboList.Save()
        End If

        If cboList.ComboBoxes?.Count < 20 Then

            For i = 1 To 20
                Dim controlName = $"ObsTimesManEntryCode_txt_{i:D2}"
                For Each cbo In cboList.ComboBoxes
                    If cbo.ComboBoxName = controlName Then
                        Continue For
                    End If
                Next

                Dim cboControl As New DOConfigComboBoxListControl
                cboControl.ComboBoxList = cboList
                cboControl.ComboBoxName = controlName
                cboControl.Enabled = True
                cboControl.Save()
                cboList.ComboBoxes.Add(cboControl)
            Next
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Obs Manual Entry")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
            '   UpdatePreviousConfigInstance(cboList)
        End If

        f.Dispose()
        Return
    End Sub

    Private Sub ManualEntryProfeeV2_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiManualEntryProfeeV2.ItemClick

        Dim cboList As DOConfigComboBoxList = ECGlobals.CurrentFacility.ConfigInstanceVersion.GetComboBoxListByControlName("ProfeeV2_ManualEntry_Cdm01cbo")
        If cboList Is Nothing Then
            cboList = New DOConfigComboBoxList

            cboList.ListName = "ProfeeV2ManualEntry"
            cboList.Enabled = True
            cboList.ConfigInstance = ECGlobals.CurrentFacility.ConfigInstanceVersion
            ECGlobals.CurrentFacility.ConfigInstanceVersion.ComboBoxLists.Add(cboList)
            cboList.Save()
        End If

        If cboList.ComboBoxes?.Count < 6 Then
            'ProfeeV2_ManualEntry_Cdm01cbo
            For i = 1 To 6
                Dim controlName = $"ProfeeV2_ManualEntry_Cdm{i:D2}cbo"
                For Each cbo In cboList.ComboBoxes
                    If cbo.ComboBoxName = controlName Then
                        Continue For
                    End If
                Next

                Dim cboControl As New DOConfigComboBoxListControl
                cboControl.ComboBoxList = cboList
                cboControl.ComboBoxName = controlName
                cboControl.Enabled = True
                cboControl.Save()
                cboList.ComboBoxes.Add(cboControl)
            Next
        End If

        Dim f As New ComboBoxListEditorFormv2(cboList, "Profee Manual Entry")
        If f.ShowDialog() = DialogResult.OK Then
            ReloadAllTabComboBoxes()
            '   UpdatePreviousConfigInstance(cboList)
        End If

        f.Dispose()
        Return
    End Sub

    Private Sub BarButtonItem45_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem45.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\EDFacilityModule.pdf")

        OpenPdfDoc(docPath)
        ' Process.Start(docPath)
    End Sub

    Private Sub BarButtonItem46_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem46.ItemClick
        'ED Physician Module.pdf
        Dim docPath = Path.Combine(Application.StartupPath, "docs\EDPhysicianModule.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Public Function OpenPdfDoc(docpath) As Boolean
        Try
            Dim viewer As New PdfViewerForm
            viewer.StartPosition = FormStartPosition.CenterScreen
            viewer.LoadDoc(docpath)
            viewer.Show()
            Return True
        Catch ex As Exception
            Dim msg = $"An error occured trying to open '{docpath}'. {vbCr}{vbCrLf}Error: {ex.Message}"
            MessageBox.Show(msg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try

    End Function

    Private Sub BarButtonItem44_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem44.ItemClick
        'System.Diagnostics.Process.Start("https://central.allscripts.com")
        OpenUrl()
    End Sub

    Sub OpenUrl()
        Dim url As String = "https://central.allscripts.com"
        Process.Start(New ProcessStartInfo("cmd", $"/c start {url}") With {
        .CreateNoWindow = True
    })
    End Sub


    Private Sub BarButtonItem47_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem47.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\InfusionandChemotherapyModule.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Private Sub BarButtonItem48_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem48.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\ObservationModule.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Private Sub BarButtonItem53_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem53.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\DataLink.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Private Sub BarButtonItem54_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem54.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\UrgentCareClinicCoding.pdf")
        OpenPdfDoc(docPath)
    End Sub


    Private Sub BarButtonItem49_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem49.ItemClick
        ShowReportUsage()
    End Sub

    Private reportsDict As Dictionary(Of Integer, String)

    Private Function GetReportDescriptionFromOid(repid As Integer) As String
        Dim description As String = Nothing
        If reportsDict.TryGetValue(repid, description) Then
            Return description
        Else
            Return "N/A"
        End If
        Return ""
    End Function
    Private Sub ShowReportUsage()
        Dim reportsXPO As New XPCollection(Of DOFacilityReport)()
        reportsXPO.SelectDeleted = True
        reportsDict = New Dictionary(Of Integer, String)
        For Each rep In reportsXPO
            reportsDict.Add(rep.Oid, rep.ReportDescription)
        Next

        Dim auditRecords As New XPCollection(Of DOAuditLog)(CriteriaOperator.Parse($"ActionDescription='Reports'"))
        Dim RunReportsDict As New Dictionary(Of String, ReportUsageDTO)(StringComparer.OrdinalIgnoreCase)
        For Each ar In auditRecords
            Dim reportFilename As String
            Dim ri = ParseReportInfoFromLogv2(ar)
            If ri Is Nothing Then Continue For

            reportFilename = ri.Value.ReportFileName
            If reportFilename = "" Then Continue For
            Dim count As Integer
            Dim ru As ReportUsageDTO = Nothing
            If RunReportsDict.TryGetValue(reportFilename, ru) Then
                count = ru.TimesRun
            Else
                count = 0
                ru = New ReportUsageDTO()
                'RunReportsDict(reportFilename) = count
            End If
            ru.TimesRun = count + 1

            If ru.LastRun Is Nothing OrElse ru.LastRun < ri.Value.DateRun Then
                ru.LastRun = ri.Value.DateRun
            End If
            ru.LastRunBy = ri.Value.LastRunBy
            ru.FacilityName = ri.Value.FacilityName
            ru.ReportDescription = ri.Value.ReportDescription
            RunReportsDict(reportFilename) = ru 'RunReportsDict(reportFilename) + 1
        Next

        'Get Unused Reports
        Dim pathFi = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "reports")
        Dim di As New IO.DirectoryInfo(pathFi)
        Dim aryFi As IO.FileInfo() = di.GetFiles("*.rpt")
        Dim fi As IO.FileInfo

        For Each fi In aryFi
            If RunReportsDict.ContainsKey(fi.Name) Then Continue For

            RunReportsDict(fi.Name) = New ReportUsageDTO(fi.Name, 0, Nothing, Nothing, Nothing, Nothing)
        Next

        Dim ReportUsageDTOList As New List(Of ReportUsageDTO)
        For Each keypair In RunReportsDict
            ReportUsageDTOList.Add(New ReportUsageDTO(keypair.Key, keypair.Value.TimesRun, keypair.Value.LastRun, keypair.Value.FacilityName, keypair.Value.LastRunBy, keypair.Value.ReportDescription))
        Next

        Dim f As New ReportUsageForm(ReportUsageDTOList)
        ' f.Data = ReportUsage()
        f.ShowDialog(Me)
    End Sub

    Public Structure ReportEntryInfo
        Public Sub New(name As String)
            ReportFileName = name
        End Sub

        Public Property ReportFileName As String
        Public Property DateRun As Date
        Public Property LastRunBy As String
        '  Public Property UserName As String
        Public Property FacilityName As String
        Public Property ReportDescription As String
    End Structure

    Private Function ParseReportInfoFromLogv2(al As DOAuditLog) As Nullable(Of ReportEntryInfo)
        If al Is Nothing Then Return Nothing

        Dim details As String = al.Details
        Dim reportFilename As String = ""
        If Not details.Contains("reportFilename") Then
            Return Nothing
        End If

        Dim ri As ReportEntryInfo = Nothing
        'ri.DateRun = al.TransactionDate

        Dim splits = Split(details, ",")
        If splits.Count < 2 Then Return Nothing

        For Each pairItem In splits
            Dim pair = Split(pairItem, ":")
            If pair(0).Contains("reportFilename") Then
                reportFilename = pair(1)
                ri.ReportFileName = reportFilename
            End If

            If pair(0).Contains("reportID") Then
                reportFilename = pair(1)
                ri.ReportDescription = GetReportDescriptionFromOid(pair(1))
            End If
        Next
        If ri.ReportFileName Is Nothing Then Return Nothing
        ri.DateRun = al.TransactionDate
        ri.LastRunBy = al.UserName
        ri.FacilityName = al.FacilityName

        Return ri
    End Function

    Public Class ReportUsageDTO
        Public Sub New()
            TimesRun = 0
        End Sub
        Public Sub New(reportFileName As String, timesRun As Integer, lastRun As Date?, facilityName As String, LastRunBy As String, description As String)
            Me.ReportFileName = reportFileName
            Me.TimesRun = timesRun
            Me.LastRun = lastRun
            Me.FacilityName = facilityName
            Me.LastRunBy = LastRunBy
            Me.ReportDescription = description
        End Sub

        Public Property ReportFileName As String
        Public Property TimesRun As Integer
        Public Property LastRun As Nullable(Of Date)
        Public Property LastRunBy As String
        Public Property FacilityName As String
        Public Property ReportDescription As String
    End Class

    Private Function ParseReportInfoFromLog(details As String) As String
        Dim reportFilename As String = ""
        If Not details.Contains("reportFilename") Then
            Return ""
        End If

        Dim splits = Split(details, ",")
        If splits.Count < 2 Then Return ""

        For Each pairItem In splits
            Dim pair = Split(pairItem, ":")
            If pair(0).Contains("reportFilename") Then
                reportFilename = pair(1)
            End If
        Next

        Return reportFilename
    End Function

    Private Sub BarToggleSwitchItem1_CheckedChanged(sender As Object, e As ItemClickEventArgs) Handles BarToggleSwitchItem1.CheckedChanged

        ECGlobals.UseEnhancedSec = BarToggleSwitchItem1.Checked


    End Sub

    Private Function GetHcpcs(newOrEstablished As String, level As Int16, mod25 As String) As String
        Dim newDic As New Dictionary(Of Int16, String)
        Dim estDic As New Dictionary(Of Int16, String)

        newDic(0) = "NOCHARGE"
        newDic(1) = "99211"
        newDic(2) = "99202"
        newDic(3) = "99203"
        newDic(4) = "99204"
        newDic(5) = "99205"
        newDic(6) = "99291"

        estDic(0) = "NOCHARGE"
        estDic(1) = "99211"
        estDic(2) = "99212"
        estDic(3) = "99213"
        estDic(4) = "99214"
        estDic(5) = "99215"
        estDic(6) = "99291"

        Dim returnValue = ""
        If newOrEstablished = "New" Then
            'If level = 1 Then
            '    level += 1
            'End If
            returnValue = newDic(level)
        Else
            returnValue = estDic(level)
        End If

        If mod25 = "25" Then
            returnValue = returnValue + "-25"
        Else
            returnValue = returnValue + "-00"
        End If

        Return returnValue

    End Function


    Public Structure NewOrEstablishedLevel
        Public [New] As String
        Public Established As String

        Public Sub New([new] As String, established As String)
            If [new] Is Nothing Then
                Throw New ArgumentNullException(NameOf([new]))
            End If

            If established Is Nothing Then
                Throw New ArgumentNullException(NameOf(established))
            End If

            Me.[New] = [new]
            Me.Established = established
        End Sub
    End Structure
    Private Sub BarButtonItem50_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem50.ItemClick
        'CreateCdmRecords("Emergency Department")

        CreateNewEspCodeRecs()

        'WriteToDisk()
    End Sub

    Private Sub CreateNewEspCodeRecs()
        Dim emLevelCodes = {"99202", "99203", "99204", "99205", "99211", "99212", "99213", "99214", "99215"}
        Dim recList As New List(Of DOESPCode)
        Const Preface = "ClinicEmLevel"
        Dim ne = {"New", "Established"}
        Dim wp = {"00", "25"}

        Dim levelDict As New Dictionary(Of Integer, NewOrEstablishedLevel)
        levelDict(0) = New NewOrEstablishedLevel("", "")
        levelDict(1) = New NewOrEstablishedLevel("99211", "99211")
        levelDict(2) = New NewOrEstablishedLevel("99202", "99212")
        levelDict(3) = New NewOrEstablishedLevel("99203", "99213")
        levelDict(4) = New NewOrEstablishedLevel("99204", "99214")
        levelDict(5) = New NewOrEstablishedLevel("99205", "99215")
        levelDict(6) = New NewOrEstablishedLevel("99291", "99291")

        DeleteEspCodesForUrgentCareEMLevels()

        For ilevel As Int16 = 0 To 6
            For Each neitem In ne
                For Each wpItem In wp

                    Dim levelString As String = ilevel.ToString
                    Dim llstring As String = ""
                    If ilevel = 6 Then
                        levelString = "Critical Care"
                        llstring = "CC"
                    ElseIf ilevel = 0 Then
                        levelString = "No Charge"
                        llstring = ilevel.ToString
                    Else
                        llstring = ilevel.ToString
                    End If

                    Dim espCodeValue As String = $"{Preface}{llstring}{neitem}{wpItem}"
                    'Dim patientTypeDescription As String = IIf(neitem = "new", "New")
                    'Dim espCodeDescription As String = $"E/M Level {levelString} - {neitem} Patient"
                    Dim espCodeDescription As String = $"E/M Level {levelString} - {neitem} {IIf(wpItem = "00", "without procedures", "with procedures")}"

                    Dim esp As New DOESPCode2018 With {.ESPVALUE = espCodeValue,
                        .LongName = espCodeDescription,
                        .ESP_Policy = "ClinicEMLevel",
                        .Flag = "Urgent Care",
                        .Points = 0,
                        .HCPCS = GetHcpcs(neitem, ilevel, wpItem)
                        }
                    esp.Save()

                    Dim cpt As String = ""
                    If neitem = "New" Then
                        cpt = levelDict(ilevel).[New]
                    Else
                        cpt = levelDict(ilevel).Established
                    End If

                    If cpt = "" Then
                        cpt = "Nocharge"
                    End If

                    'If ilevel = 6 Then
                    '    cpt = ""
                    'End If

                    If ilevel < 6 Then
                        espCodeValue = $"PhysChargeEMLevel_{cpt}{wpItem}"
                        If cpt = "99211" Then
                            '    Debug.WriteLine("break")
                            neitem = ""
                        End If
                        espCodeDescription = $"E/M Level {levelString} - {neitem} {IIf(wpItem = "00", "without procedures", "with procedures")}"
                        '$"E/M Level {levelString} - {neitem} Patient"


                        Dim physEsp = New DOESPCode2018 With {.ESPVALUE = espCodeValue,
                                .LongName = espCodeDescription,
                                .ESP_Policy = "ClinicEMLevel",
                                .Flag = "Urgent Care",
                                .Points = 0,
                                .HCPCS = GetHcpcs(neitem, ilevel, wpItem)
                                }
                        physEsp.Save()

                        espCodeValue = $"PhysChargeEMLevelOverride_{cpt}{wpItem}"
                        'espCodeDescription = $"E/M Level {levelString} - {neitem} Patient"
                        Dim physEsp2 = New DOESPCode2018 With {.ESPVALUE = espCodeValue,
                            .LongName = espCodeDescription,
                            .ESP_Policy = "ClinicEMLevel",
                            .Flag = "Urgent Care",
                            .Points = 0,
                            .HCPCS = GetHcpcs(neitem, ilevel, wpItem)
                            }
                        physEsp2.Save()
                    End If
                Next
            Next
        Next

        'Dim esp20 As New DOESPCode2018 With {
        '            .ESPVALUE = $"{Preface}Medicare",
        '           .LongName = $"E/M Level Medicare",
        '           .ESP_Policy = "ClinicEMLevel",
        '           .Flag = "Urgent Care",
        '           .Points = 0,
        '           .HCPCS = "G0463" 'GetHcpcs(neitem, ilevel)
        '           }
        'esp20.Save()

        Dim esp20 = New DOESPCode2018 With {
                    .ESPVALUE = $"{Preface}0Medicare00",
                   .LongName = $"E/M Level No Charge",
                   .ESP_Policy = "ClinicEMLevel",
                   .Flag = "Urgent Care",
                   .Points = 0,
                   .HCPCS = "NOCHARGE-00" 'GetHcpcs(neitem, ilevel)
                   }
        esp20.Save()

        esp20 = New DOESPCode2018 With {
                    .ESPVALUE = $"{Preface}0Medicare25",
                   .LongName = $"E/M Level No Charge",
                   .ESP_Policy = "ClinicEMLevel",
                   .Flag = "Urgent Care",
                   .Points = 0,
                   .HCPCS = "NOCHARGE-25" 'GetHcpcs(neitem, ilevel)
                   }
        esp20.Save()

        Dim esp2 As New DOESPCode2018 With {
                    .ESPVALUE = $"{Preface}Medicare00",
                   .LongName = $"E/M Level Medicare",
                   .ESP_Policy = "ClinicEMLevel",
                   .Flag = "Urgent Care",
                   .Points = 0,
                   .HCPCS = "G0463-00" 'GetHcpcs(neitem, ilevel)
                   }
        esp2.Save()

        Dim esp3 As New DOESPCode2018 With {
                 .ESPVALUE = $"{Preface}Medicare25",
                .LongName = $"E/M Level Medicare",
                .ESP_Policy = "ClinicEMLevel",
                .Flag = "Urgent Care",
                .Points = 0,
                .HCPCS = "G0463-25" 'GetHcpcs(neitem, ilevel)
                }
        esp3.Save()

        Dim esp5 As New DOESPCode2018 With {
                 .ESPVALUE = $"{Preface}CCMedicare00",
                .LongName = $"E/M Level Medicare CC",
                .ESP_Policy = "ClinicEMLevel",
                .Flag = "Urgent Care",
                .Points = 0,
                .HCPCS = "G0463-00" 'GetHcpcs(neitem, ilevel)
                }
        esp5.Save()

        Dim esp4 As New DOESPCode2018 With {
                 .ESPVALUE = $"{Preface}CCMedicare25",
                .LongName = $"E/M Level Medicare CC",
                .ESP_Policy = "ClinicEMLevel",
                .Flag = "Urgent Care",
                .Points = 0,
                .HCPCS = "G0463-25" 'GetHcpcs(neitem, ilevel)
                }
        esp4.Save()

        Dim esp6 As New DOESPCode2018 With {
             .ESPVALUE = $"ProlongedService_99XXXx01",
            .LongName = $"Prolonged Service",
            .ESP_Policy = "ClinicEMLevel",
            .Flag = "Urgent Care",
            .Points = 0,
            .HCPCS = "99XXXx{{QTY}}" 'GetHcpcs(neitem, ilevel)
            }
        esp6.Save()

        esp6 = New DOESPCode2018 With {
             .ESPVALUE = $"PhysChargeEMLevelOverride_9929100",
            .LongName = $"E/M Level Critical Care - without procedures",
            .ESP_Policy = "ClinicEMLevel",
            .Flag = "Urgent Care",
            .Points = 0,
            .HCPCS = "99291-00"
            }
        esp6.Save()

        esp6 = New DOESPCode2018 With {
             .ESPVALUE = $"PhysChargeEMLevelOverride_9929125",
            .LongName = $"E/M Level Critical Care - with procedures",
            .ESP_Policy = "ClinicEMLevel",
            .Flag = "Urgent Care",
            .Points = 0,
            .HCPCS = "99291-25"
            }
        esp6.Save()
    End Sub

    Private Sub WriteToDisk()
        Dim localSession = New Session
        Dim sb As New StringBuilder

        Try

            Dim blah = From code As DOESPCode2018 In New XPQuery(Of DOESPCode2018)(localSession)
                       Where code.ESP_Policy.StartsWith("ClinicEMLevel")
                       Order By code.ESPVALUE
                       Select code

            For Each code In blah '                                                                                                                                                                                                                    'ClinicEmLevelCCMedicare25', 'ClinicEMLevel', ?, 'Urgent Care', 'G0463-25', 'E/M Level Medicare CC', ?, '0', 0, 0, False, 0, ?)
                Debug.WriteLine($"DeleteEspCodesForUrgentCareEMLevels -  Deleting{code.ESPVALUE}")
                Dim dbStatement As String = $"INSERT INTO ""DBA"".""DOESPCode2018"" (""ESPVALUE"", ""ESP_Policy"", ""Flag"", ""HCPCS"", ""LongName"", ""OrcaTab"", ""Points"", ""ReportDisplayOrder"", ""Special"", ""MigrationError"") values ('{code.ESPVALUE}','{code.ESP_Policy}','{code.Flag}','{code.HCPCS}','{code.LongName}','{code.OrcaTab}',{code.Points},{code.ReportDisplayOrder},{code.Special}, 0)"
                sb.AppendLine(dbStatement)

                Debug.WriteLine(dbStatement)
            Next

            File.WriteAllText($"c:\apps\eccoder\EspcodeStatements.txt", sb.ToString())

        Catch ex As Exception
        Finally

        End Try
    End Sub

    Private Sub CreateCdmRecords(ta As String)
        Dim localSession = New Session
        Dim sb As New StringBuilder
        Dim dic As New Dictionary(Of String, DOChargeMaster)
        Dim facility As DOFacility

        facility = localSession.GetObjectByKey(Of DOFacility)(ECGlobals.CurrentFacility.Oid)
        Try

            Dim blah = From cdmRec As DOChargeMaster In New XPQuery(Of DOChargeMaster)(localSession)
                       Where cdmRec.Facility = facility And cdmRec.TreatmentArea = ta
                       Select cdmRec

            'For Each item In blah
            '    If dic.ContainsKey(item.HCPCS) = False Then
            '        dic(item.HCPCS) = item
            '    End If
            'Next

            Dim espcodes = From code As DOESPCode2018 In New XPQuery(Of DOESPCode2018)(localSession)
                           Where code.ESP_Policy.StartsWith("ClinicEMLevel")
                           Order By code.ESPVALUE
                           Select code

            For Each espItem In espcodes
                If dic.ContainsKey(espItem.HCPCS) = False Then
                    dic(espItem.HCPCS) = New DOChargeMaster() With
                        {
                        .Facility = facility,
                        .TreatmentArea = ta,'espItem.TreatmentArea,
                         .HCPCS = espItem.HCPCS,
                         .CDM = $"cdm{espItem.HCPCS}",
                         .PhysicianCDM = $"physcdm{espItem.HCPCS}",
                         .LongName = "auto created placeholder",
                         .Quantity = 1
                        }
                End If
            Next

            For Each code In dic.Values                                                                                                                                                                                                                    'ClinicEmLevelCCMedicare25', 'ClinicEMLevel', ?, 'Urgent Care', 'G0463-25', 'E/M Level Medicare CC', ?, '0', 0, 0, False, 0, ?)

                Dim dbStatement As String = $"INSERT INTO ""DBA"".""DOChargeMaster"" (""Facility"", ""TreatmentArea"",""HCPCS"", ""CDM"",""LongName"", ""Quantity"", ""PhysicianCDM"") values ({code.Facility.Oid},'{code.TreatmentArea}','{code.HCPCS}','{code.CDM}','{code.LongName}',{code.Quantity},'{code.PhysicianCDM}')"
                sb.AppendLine(dbStatement)

                Debug.WriteLine(dbStatement)
            Next

            File.WriteAllText($"c:\apps\eccoder\ChargeMasterStatements.txt", sb.ToString())

        Catch ex As Exception
        Finally

        End Try
    End Sub
    Private Sub DeleteEspCodesForUrgentCareEMLevels()
        Dim localSession = New Session
        Try

            localSession.BeginTransaction()
            Dim blah = From code As DOESPCode2018 In New XPQuery(Of DOESPCode2018)(localSession)
                       Where code.ESP_Policy.StartsWith("ClinicEMLevel")
                       Select code

            For Each code In blah
                Debug.WriteLine($"DeleteEspCodesForUrgentCareEMLevels -  Deleting{code.ESPVALUE}")
                code.Delete()
            Next


        Catch ex As Exception
        Finally
            localSession.CommitTransaction()
            localSession.PurgeDeletedObjects()
        End Try

    End Sub


    Private Sub BarButtonItem51_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem51.ItemClick
        Try

            Dim reportsToKeep As New List(Of String)
            reportsToKeep.Add("AMA Report")
            ' Dim adminReportsDTO As DTOAdminReports

            Dim blah = File.ReadAllText("c:\apps\UrgentCareReportsToKeep.txt")
            '' Dim tt As New TextReader(blah)
            Dim delims As Char() = {vbCr, vbLf}
            Dim lines As String() = blah.Split(delims, StringSplitOptions.RemoveEmptyEntries)
            For Each line In lines
                reportsToKeep.Add(line)
            Next

            'Using fs As New FileStream("c:\apps\jjc\Reports-MasterList.xml", FileMode.Open)

            '    Dim xmlSettings As New XmlReaderSettings
            '    xmlSettings.DtdProcessing = DtdProcessing.Prohibit

            '    Using reader = XmlReader.Create(fs, xmlSettings)
            '        Dim x As New Xml.Serialization.XmlSerializer(GetType(DTOAdminReports))
            '        adminReportsDTO = x.Deserialize(reader)
            '    End Using
            'End Using

            Dim adminReportsDTO = New DTOAdminReports(ECGlobals.CurrentFacility)

            For Each category In adminReportsDTO.FacilityCategories.ToList
                If category.Reports Is Nothing Then Continue For
                For Each rpt In category.Reports.ToList
                    If Not reportsToKeep.Contains(rpt.Description) Then
                        category.Reports.Remove(rpt)
                    Else
                        Debug.Print("keepit")
                    End If
                Next
            Next

            Dim writer As New System.Xml.Serialization.XmlSerializer(GetType(DTOAdminReports))
            Dim fw As New System.IO.StreamWriter(
            "c:\apps\UrgentCareDefaultReportsv2.xml")
            writer.Serialize(fw, adminReportsDTO)
            fw.Close()


            Debug.Print("help")
            'For Each facility_id As String In txtFacilityIDs.Text.Split(",")
            '    Dim facility As DOFacility = DOFacility.GetFacilityByOid(facility_id)
            '    If facility IsNot Nothing Then
            '        Dim reportsUnpacker As New AdminReportsUnpacker(facility, adminReportsDTO)
            '        reportsUnpacker.Unpack()
            '    End If
            'Next

            DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            MessageBox.Show("Something Went Wrong! " & ex.ToString)
        End Try

    End Sub

    Private Sub BarButtonItem52_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiBetaCodeDeleteStuff.ItemClick
        If MessageBox.Show($"Are you sure you want to continue? This could freeze the UI for a very long time!", "Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> DialogResult.OK Then
            Exit Sub
        End If

        Dim session = XpoDefault.Session
        ' Using uof As UnitOfWork = New UnitOfWork()
        Dim col As ICollection = session.GetObjects(session.GetClassInfo(Of DOChartInfo)(), New OperandProperty("GCRecord").IsNotNull(),
                Nothing, 0, True, True)
        For Each ci As DOChartInfo In col
            DeleteChartInfoData(ci)
        Next
        ' End Using


        XpoDefault.Session.PurgeDeletedObjects()
    End Sub

    Private Shared Sub DeleteChartInfoData(chartInfo As DOChartInfo)
        Debug.WriteLine($"*** VisitID:{chartInfo.VisitID}, ChartInfo.Oid:{chartInfo.Oid} ***")
        Dim count = 0
        XpoDefault.Session.BeginTransaction()
        For Each Chart In chartInfo.Charts.ToList
            count += 1
            Debug.WriteLine(count)
            For Each redisplay In Chart.ChartRedisplay.ToList
                redisplay.Delete()
            Next

            For Each item In Chart.ChartHCPCSInfo.ToList
                item.Delete()
            Next

            For Each item In Chart.ChartSummary.ToList
                item.Delete()
            Next

            For Each item In Chart.CodingReportRecords.ToList
                item.Delete()
            Next

            'For Each item In chart..ToList
            '    item.Delete()
            'Next

            'For Each item In chart.Charges.ToList
            '    item.Delete()
            'Next

            'For Each item In chart.ChargesHistory.ToList
            '    item.Delete()
            'Next

            Chart.Delete()
        Next

        For Each prop In chartInfo.HL7Properties.ToList
            prop?.Delete()
        Next
        'chartInfo.Delete()
        chartInfo.Chart?.Delete()
        chartInfo?.ExportedChart?.Delete()
        chartInfo?.ObsExportChart?.Delete()
        chartInfo?.PhysExportChart?.Delete()

        chartInfo.CFExportFacilityChartVersion?.Delete()
        chartInfo.CFExportObservationChartVersion?.Delete()
        chartInfo.CFExportPhysicianChartVersion?.Delete()

        'chartInfo.Delete(
        chartInfo.Chart = Nothing
        chartInfo.ExportedChart = Nothing
        chartInfo.ObsExportChart = Nothing
        chartInfo.PhysExportChart = Nothing

        chartInfo.CFExportFacilityChartVersion = Nothing
        chartInfo.CFExportObservationChartVersion = Nothing
        chartInfo.CFExportPhysicianChartVersion = Nothing
        chartInfo.Save()
        chartInfo.Delete()
        XpoDefault.Session.CommitTransaction()
        'If chartInfo.Chart Is Nothing Then
        '    Debug.WriteLine("break")
        'End If
    End Sub

    'Private Sub MDIFrameForm_ResizBegin(sender As Object, e As EventArgs) Handles Me.ResizeBegin
    '    Debug.WriteLine("MDIFrameForm_ResizBegin")
    '    '      Me.SuspendLayout()

    '    'For Each baseform As ChartBaseForm In MdiChildren
    '    '    baseform.ResizeBeginForForm()
    '    'Next
    'End Sub
    Private _lastWindowState As FormWindowState
    Private Sub MDIFrameForm_Resize(sender As Object, e As EventArgs) Handles MyBase.Resize
        Debug.WriteLine($"MDIFrameForm_Resize {Me.WindowState}")
        If Me.WindowState <> _lastWindowState Then
            _lastWindowState = Me.WindowState
            If Me.WindowState = FormWindowState.Minimized Then
                Exit Sub
            End If
            For Each baseform As ChartBaseForm In MdiChildren
                baseform.ResizeEndForForm()
            Next
        End If
    End Sub

    Private Sub MDIFrameForm_ResizeEnd(sender As Object, e As EventArgs) Handles MyBase.ResizeEnd
        Debug.WriteLine("MDIFrameForm_ResizeEnd")

        For Each baseform As ChartBaseForm In MdiChildren
            baseform.ResizeEndForForm()
        Next
    End Sub

    Private Sub MDIFrameForm_SizeChanged(sender As Object, e As EventArgs) Handles MyBase.SizeChanged
        BarStaticItem1.Caption = Me.Size.ToString
    End Sub

    'Private Sub BarButtonItem55_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem55.ItemClick
    '    Dim f As New XtraForm2
    '    f.Show()
    'End Sub

    Private Sub BarButtonItem56_ItemClick(bLoadIntoPendingCI As Boolean)
        Dim lFac As DOFacility = ECGlobals.CurrentFacility
        Dim lCI As DOConfigInstance = Nothing

        If bLoadIntoPendingCI Then
            lCI = lFac.PendingConfigInstanceVersion
        Else
            lCI = lFac.ConfigInstanceVersion
        End If

        If ECGlobals.CurrentFacility Is Nothing Then
            MessageBox.Show("Please select a facility on the main form (Chuck!) so I know what facility to import to, eh!", "Doh", MessageBoxButtons.OK, MessageBoxIcon.Hand)
            Return
        End If

        Dim curdir As New DirectoryInfo(My.Computer.FileSystem.CurrentDirectory)
        Dim ConfigInstance As DOConfigInstance = lCI 'ECGlobals.CurrentFacility.ConfigInstanceVersion

        Try

            Dim ofd As New OpenFileDialog
            ofd.DefaultExt = "MedIDs2.csv"
            ofd.Filter = String.Format("{0} Medications definition file (*.csb) | *.csv", ECGlobals.CompanyNameString)
            ofd.InitialDirectory = ECGlobals.ComboBoxListDir
            Dim result As DialogResult = ofd.ShowDialog()
            If result = DialogResult.Cancel Then Exit Sub

            Dim ffile As New FileInfo(ofd.FileName)
            ofd.Dispose() '???

            Dim Lines() As String = File.ReadAllLines(ffile.FullName)

            Dim Medication As DOMedication = Nothing
            Dim o As Integer = 1000
            Dim linecount As Integer = 0
            'Using session As New Session
            'XpoDefault.Session.BeginTransaction()

            For Each Line As String In Lines

                linecount += 1
                Debug.WriteLine(Line)
                If Line.Length = 0 Then Continue For 'ignore blank lines
                Dim fields() As String = Split(Line, ",")
                ' Dim lMedName As String = fields(0)

                Dim dmed As New DODataLinkLookup()
                dmed.Facility = ECGlobals.CurrentFacility
                dmed.[Type] = fields(2)
                dmed.[Key] = fields(3)
                dmed.Value = fields(4)
                dmed.Save()

                'Medication = ConfigInstance.GetMedicationByName(lMedName)
                'Medication.ConfigInstance = ConfigInstance 'ECGlobals.CurrentFacility.ConfigInstanceVersion

                'Try
                '    If fields.Count > 2 Then

                '        If fields(DOMedication.FieldsEnum.Titrated) <> "" Then
                '            Medication.Titrated = True
                '        End If

                '        If fields(DOMedication.FieldsEnum.CC) <> "" Then
                '            Medication.CC = True
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try

            Next

        Catch ex As Exception

        End Try
    End Sub

    Private Sub BarButtonItem56_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem56.ItemClick
        BarButtonItem56_ItemClick(False)
    End Sub

    'Private Sub BarButtonItem57_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem57.ItemClick
    '    Dim phelper As New ShawnasParagonMedIdMappingsHelper
    '    phelper.LoadFile("C:\Users\<USER>\Downloads\AIC Paragon Med List Mapped to MEDIDs_FINAL.csv")
    'End Sub

    Private Sub BarButtonItem58_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem58.ItemClick
        Dim frm As New ConfigSettingsFormV3(GetCoOidForActiveForm())
        'frm.MdiParent = Me
        frm.Show()
    End Sub

    Private Function GetCoOidForActiveForm() As DOConfigInstance
        Dim InitialChartCI As DOConfigInstance = Nothing

        If Me.XtraTabbedMdiManager1.Pages.Count > 0 Then
            Dim page As ChartBaseForm
            page = Me.ActiveMdiChild

            If page IsNot Nothing Then
                Return page.ConfigInstance
            End If
        End If
        Return Nothing
    End Function

    Private Sub BarButtonItem59_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem59.ItemClick
        Auth_MoreSecure.ShutDownESS()
    End Sub

    Private Sub BarButtonItem52_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem52.ItemClick
        AICUserSpecificSettingsHelper.Reset()
    End Sub

    Private Sub BarButtonItem60_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem60.ItemClick
        Dim f As New ParagonDatalinkLoginForm
        f.ShowDialog()
        f.Close()
    End Sub

    Private Sub ParagonDataRequestorBtn_ItemClick(sender As Object, e As ItemClickEventArgs) Handles ParagonDataRequestorBtn.ItemClick
        Dim f As New DataViewer
        f.Show(Me)
    End Sub

    Private Sub BarButtonItem61_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiDataRequester.ItemClick
        Dim f As New DataViewer
        f.Show()
    End Sub

    Private Sub BarButtonItem62_ItemClick(sender As Object, e As ItemClickEventArgs) Handles btnAlias.ItemClick
        Dim f As New ParagonDatalinkLoginForm
        f.ShowDialog()
        f.Close()
    End Sub

    Private Sub MDIFrameForm_Closing(sender As Object, e As CancelEventArgs) Handles MyBase.Closing
        LogOutParagonUser()
    End Sub

    Private Sub BarButtonItem61_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem61.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\ReportsGuide.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Private Sub BarButtonItem63_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem63.ItemClick
        Dim docPath = Path.Combine(Application.StartupPath, "docs\SystemRequirements.pdf")
        OpenPdfDoc(docPath)
    End Sub

    Private Sub scratchpad_ItemClick(sender As Object, e As ItemClickEventArgs) Handles scratchpad.ItemClick
        Dim f As New ScratchForm3
        f.Show()
    End Sub

    Private Sub beiFacility_ItemClick(sender As Object, e As ItemClickEventArgs) Handles beiFacility.ItemClick

    End Sub

    Private Sub MDIFrameForm_MdiChildActivate(sender As Object, e As EventArgs) Handles MyBase.MdiChildActivate

        If Me.ActiveMdiChild Is Nothing Then Exit Sub
        Dim childform = TryCast(Me.ActiveMdiChild, ChartBaseForm)
        'If childForm IsNot Nothing Then
        '    childForm.BeginInvoke(New Action(Sub()
        '                                         UpdateFooterDates()
        '                                     End Sub))
        'End If
        Dim dateProvider = TryCast(Me.ActiveMdiChild, IValidDateRangeProvider)
        If dateProvider Is Nothing Then
            UpdateFooterDates(forceClear:=True)
        Else
            UpdateFooterDates()
        End If
        'chartForm.FindControl("DispositionDate")

    End Sub

    Private Sub UpdateFooterDates(Optional forceClear As Boolean = False)
        forceClear = True
        Dim dateFormat = "MM/dd/yyyy HH:mm"
        If Me.ActiveMdiChild Is Nothing OrElse forceClear Then
            BarStaticItem2.Caption = ""
            BarStaticItem2.Visibility = BarItemVisibility.Never

            BarStaticItem3.Caption = ""
            BarStaticItem3.Visibility = BarItemVisibility.Never

            BarStaticItem4.Caption = ""
            BarStaticItem4.Visibility = BarItemVisibility.Never
            Return
        End If

        Dim chartform = TryCast(Me.ActiveMdiChild, ChartBaseForm)
        If chartform Is Nothing OrElse chartform.IsLoading Then Return

        Dim dateProvider = TryCast(Me.ActiveMdiChild, IValidDateRangeProvider)
        If dateProvider Is Nothing Then Return

        Dim edDisposition As Date? = dateProvider.GetValidFacilityEndDate
        If edDisposition IsNot Nothing Then
            Dim footerCaption = $"ED Disposition:{edDisposition.Value.ToString(dateFormat)}"
            BarStaticItem2.Caption = footerCaption
            BarStaticItem2.Visibility = BarItemVisibility.Always
        Else
            BarStaticItem2.Visibility = BarItemVisibility.Never

        End If

        Dim obsDate = dateProvider.GetValidObsStartDate()
        If obsDate IsNot Nothing AndAlso obsDate <> Date.MinValue Then
            BarStaticItem3.Caption = $"Obs Start:{obsDate.Value.ToString(dateFormat)}"
            BarStaticItem3.Visibility = BarItemVisibility.Always

        Else
            BarStaticItem3.Visibility = BarItemVisibility.Never
        End If

        BarStaticItem4.Visibility = BarStaticItem3.Visibility
        Dim obsEndDate = dateProvider.GetValidObsEndDate()
        If obsEndDate IsNot Nothing Then
            BarStaticItem4.Caption = $"Obs End:{obsEndDate.Value.ToString(dateFormat)}"
        End If
    End Sub

    Private Sub BarButtonItem62_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem62.ItemClick
        'Dim col = New XPCollection(Of DOChart)
        'Dim count = col.Count()

        Dim f As New ChatBotForm

        f.Show(Me)
    End Sub

    Private Sub bbiEmailConfig_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiEmailConfig.ItemClick
        Dim f As New EmailConfigForm
        f.ShowDialog(Me)
    End Sub
End Class


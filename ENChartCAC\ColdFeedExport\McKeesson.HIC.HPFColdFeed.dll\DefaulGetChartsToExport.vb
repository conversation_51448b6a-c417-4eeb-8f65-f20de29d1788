﻿Imports System.CodeDom.Compiler
Imports System.IO
Imports System.Reflection
Imports DevExpress.Xpo
Imports DevExpress.Xpo.DB
Imports EnchartDOLib

Imports McKesson.HIC.ColdFeed
Imports McKesson.HIC.ColdFeed.ChartObjWrapper.ReasonEnum

Namespace ColdFeed

    ''' <summary>
    ''' Return List of DOChart.Oids
    ''' </summary>
    ''' <remarks>
    ''' By using ado.net to get our list of DOChart Oids that need to ColdFeed export, we are primarily making
    ''' it easier to accommodate all possible queries that come up, without having to be devexpress guru...
    ''' 
    ''' </remarks>
    Public Class DefaulGetChartsToExport
        Implements IGetChartsToExport

#Region "Fields"

        Dim SourceCodeEnd As String = String.Format("End function{0}" & "End Class{0}", vbCrLf)
        Dim SourceCodeStart As String = String.Format("Imports System{0}" & "Imports EnchartDOLib{0}" & "Imports DevExpress.Xpo{0}" & "Public Class JJCRules{0}" & "Public Function JJCCompare(ByVal chart As DOChart) As Boolean{0}", vbCrLf)
        Private _CFHelper As ColdFeedHelper
        Private _TriggerMethod As String

#End Region 'Fields

#Region "Properties"

        Public Property CFHelper() As ColdFeedHelper Implements IGetChartsToExport.CFHelper
            Get
                Return _CFHelper
            End Get
            Set(ByVal value As ColdFeedHelper)
                _CFHelper = value
            End Set
        End Property

        Public Property TriggerMethod() As String Implements IGetChartsToExport.TriggerMethod
            Get
                Return _TriggerMethod
            End Get
            Set(ByVal value As String)
                _TriggerMethod = value
            End Set
        End Property

#End Region 'Properties

#Region "Methods"

        'Public Function CustomShouldExport(ByVal chart As DOChart) As Boolean
        '    Static Dim method As MethodInfo = Nothing
        '    Dim asm As Assembly = Nothing
        '    Static evaluator As Object = Nothing
        '    Static firstTime As Boolean = True

        '    If firstTime AndAlso method Is Nothing Then
        '        firstTime = False
        '        Dim sourceMiddle As String = Nothing
        '        Using fs As New FileStream("customfunc.txt", FileMode.Open)
        '            If fs Is Nothing OrElse Not fs.CanRead Then
        '                Return True
        '            End If
        '            Using sr As New StreamReader(fs)
        '                sourceMiddle = sr.ReadToEnd
        '            End Using
        '        End Using

        '        Dim source = String.Format("{1}{0} {2}{0} {3}{0}", vbCrLf, SourceCodeStart, sourceMiddle, SourceCodeEnd)
        '        ' Console.WriteLine(source)

        '        Dim p As New CompilerParameters
        '        p.GenerateExecutable = False
        '        p.GenerateInMemory = True
        '        p.ReferencedAssemblies.Add("system.dll")
        '        p.ReferencedAssemblies.Add("EnchartDOLib.dll")
        '        p.ReferencedAssemblies.Add("DevExpress.Xpo.v7.2.dll")

        '        Using provider As New VBCodeProvider()
        '            Dim comPres As CompilerResults = provider.CompileAssemblyFromSource(p, source)
        '            Dim msg As String = ""
        '            If comPres.Errors.Count > 0 Then
        '                For Each cerror In comPres.Errors
        '                    msg &= cerror.ToString & vbCrLf
        '                Next
        '                Console.WriteLine("Error Compiling CustomFunc.txt: " & msg)
        '                Return True
        '            Else
        '                asm = comPres.CompiledAssembly
        '                evaluator = asm.CreateInstance("JJCRules")
        '                method = evaluator.GetType.GetMethod("JJCCompare")
        '            End If
        '        End Using

        '    End If

        '    If method IsNot Nothing Then
        '        Dim args() As Object = {chart}
        '        Return method.Invoke(evaluator, args)
        '    End If

        '    Return True
        'End Function

        Public Function GetCharts(ByVal facility As DOFacility, ByVal TriggerMethod As String) As ChartWrapperDictionary Implements IGetChartsToExport.GetCharts
            Dim chartsList As New ChartWrapperDictionary

            Me.TriggerMethod = TriggerMethod

            'Note... it's vital that adoGetCharts returns the list in chart.version order...
            'because we're using a dictionary with a key of the DOChartInfo.Oid to make sure we only
            'have one DOchart per DOChartInfo, and that it's the latest version numbered one
            'that matched our selection criteria...
            Dim PossibleDOChartsOid As List(Of Integer) = adoGetCharts(facility.Oid, IsPhysicianTabEnabled(facility), IsObservationTabEnabled(facility))

            Dim count = 0
            For Each chartOid In PossibleDOChartsOid
                Dim chart = DOChart.GetChartByOid(chartOid)
                Dim chartWrapper As New ChartObjWrapper(chart)
                If ShouldExport(chartWrapper, IsPhysicianTabEnabled(facility)) Then
                    'If CustomShouldExport(chart) Then
                    chart.ChartInfo.Lock("CFExport")
                    chartsList(chart.ChartInfo.Oid) = chartWrapper
                    'End If
                Else
                    'ShowMessage(String.Format("chart({0} for VisitID({1}) did not meet the selection criteria", _
                    '                          chart.Oid, chart.ChartInfo.VisitID))
                    'ShowMessage("Thinking...")
                End If

            Next

            ShowMessage(String.Format("GetCharts selected {0} charts from {1} returned from query", chartsList.Count, PossibleDOChartsOid.Count))
            Return chartsList
        End Function

        ''' <summary>
        ''' Extract a database connection (in the current case, an ASADbConnection object) from the DevExpress DataLayer
        ''' </summary>
        ''' <returns>IDbConection</returns>
        ''' <remarks>This is so we can use ado.net without needing another connection string</remarks>
        Public Function GetdbConnection() As System.Data.IDbConnection Implements IGetChartsToExport.GetdbConnection
            Return DevExpress.Xpo.Session.DefaultSession.Connection
        End Function

        Private Function GetChartOid(ByVal c As DOChart) As Integer
            If c Is Nothing Then Return 0
            Return c.Oid
        End Function

        'Private Function GetLastExportedChartOid(ByVal chartwrapper As ChartObjWrapper) As Integer
        '    Dim ci As DOChartInfo = chartwrapper.Chart.ChartInfo

        '    Dim maxChart As Integer = GetChartOid(ci.ExportedChart)

        '    If GetChartOid(ci.PhysExportChart) > maxChart Then maxChart = GetChartOid(ci.PhysExportChart)

        '    If GetChartOid(ci.ObsExportChart) > maxChart Then maxChart = GetChartOid(ci.ObsExportChart)

        '    Return maxChart

        'End Function

        'Private Function IfAtLeastOneChartStatusComplete(ByVal chartwrapper As ChartObjWrapper) As Boolean
        '    Dim chart As DOChart = chartwrapper.Chart

        '    If chart.ChartStatus IsNot Nothing AndAlso chart.ChartStatus = STR_Complete Then
        '        Return True
        '    End If

        '    If chart.PhysChartStatus IsNot Nothing AndAlso chart.PhysChartStatus = STR_Complete Then
        '        Return True
        '    End If
        '    If chart.ObsChartStatus IsNot Nothing AndAlso chart.ObsChartStatus = STR_Complete Then
        '        Return True
        '    End If

        '    Return False
        'End Function

        Protected Function IsValidUserRequestedExport(ByVal chartWrapper As ChartObjWrapper) As Boolean
            Dim chart As DOChart = chartWrapper.Chart
            Dim ci As DOChartInfo = chart.ChartInfo

            If Not ci.CFExportUserRequestPending Then Return False

            If chart.ChartStatus = STR_Complete Then Return True
            If chart.PhysChartStatus = STR_Complete Then Return True
            If chart.ObsChartStatus = STR_Complete Then Return True

            Return False
        End Function

        Private Function GetLastExportedChart(ByVal chartInfo As DOChartInfo) As DOChart
            ' Throw New System.NotImplementedException()

            Dim ci = chartInfo ' As DOChartInfo = chartwrapper.Chart.ChartInfo

            Dim maxChartOid As Integer = GetChartOid(ci.ExportedChart)
            Dim maxChart As DOChart = ci.ExportedChart

            If GetChartOid(ci.PhysExportChart) > maxChartOid Then
                maxChartOid = GetChartOid(ci.PhysExportChart)
                maxChart = ci.PhysExportChart
            End If


            If GetChartOid(ci.ObsExportChart) > maxChartOid Then
                maxChartOid = GetChartOid(ci.ObsExportChart)
                maxChart = ci.ObsExportChart
            End If

            Return maxChart
        End Function

        ''' <summary>
        ''' Determine if this Visit should be exported
        ''' </summary>
        ''' <param name="chartWrapper"></param>
        ''' <param name="physicianModuleEnabled"></param>
        ''' <returns></returns>
        ''' <remarks>
        ''' 
        ''' Regarding useRequrested exports, we will only do an export if at least 1 chartstatus is "Complete"
        ''' 
        ''' Note - The CFEExportXXXXXChartVersion fields may or maynot be the actual version that was billing exported.
        ''' These fields are used to manage when we are in a new condition that requires an export, and of course to document
        ''' the version of the chart when said condition is met.
        '''  </remarks>
        Public Function ShouldExport(ByVal chartWrapper As ChartObjWrapper, _
            ByVal physicianModuleEnabled As Boolean) As Boolean Implements IGetChartsToExport.ShouldExport
            Dim pThisChart = chartWrapper.Chart
            Dim result As Boolean = False

            'UpdateColdFeedExportStatusAndUnLock will need this value
            chartWrapper.PhysicianEnabled = physicianModuleEnabled

            Dim chartInfo As DOChartInfo = pThisChart.ChartInfo

            'Dim multiExportEnabled As Boolean
            'If CFHelper.OptionsDict.ContainsKey("MultiExport") Then
            '    multiExportEnabled = CFHelper.OptionsDict("MultiExport")
            'Else
            '    multiExportEnabled = False
            'End If

            Dim lExportChargeExportedChart As Boolean = False

            Select Case TriggerMethod.ToUpper
                Case "EXPORTSTATUS"

                    'Do a check to see if a billing export (for facility, phyxician, or observation) has been done,
                    ' but has not been cold feed exported.
                    If chartInfo.ExportedChart IsNot Nothing And chartInfo.CFExportFacilityStatus Is Nothing Then
                        chartWrapper.bSetFacToExported = True
                        lExportChargeExportedChart = True
                    End If

                    If chartInfo.PhysExportChart IsNot Nothing And chartInfo.CFExportPhysicianStatus Is Nothing Then
                        chartWrapper.bSetPhysToExported = True
                        lExportChargeExportedChart = True
                    End If

                    If chartInfo.ObsExportChart IsNot Nothing And chartInfo.CFExportObservationStatus Is Nothing Then
                        chartWrapper.bSetObsToExported = True
                        lExportChargeExportedChart = True
                    End If

                    If lExportChargeExportedChart Then
                        Dim xchart As DOChart = GetLastExportedChart(chartWrapper.Chart.ChartInfo)
                        If xchart IsNot Nothing Then
                            chartWrapper.Chart = xchart
                            Return True
                        End If

                        'If chartWrapper.Chart.Oid = GetLastExportedChartOid(chartWrapper) Then
                        '    Return True
                        '    'Else
                        '    '    Return False
                        'End If
                    End If


                    If IsValidUserRequestedExport(chartWrapper) Then
                        Return True
                    End If

                    Return False

                Case Else
                    'Debug.Assert("Oh oh")
                    ShowMessage(String.Format("Trigger method {0} is not valid", Me.TriggerMethod))
            End Select

            Return False
        End Function


        '''' <summary>
        '''' 
        '''' </summary>
        '''' <param name="chartWrapper"></param>
        '''' <param name="physicianModuleEnabled"></param>
        '''' <returns></returns>
        '''' <remarks> There must be an easier way to write this... </remarks>
        'Public Function ShouldExport(ByVal chartWrapper As ChartObjWrapper, _
        '    ByVal physicianModuleEnabled As Boolean) As Boolean Implements IGetChartsToExport.ShouldExport
        '    Dim pThisChart = chartWrapper.Chart
        '    Dim result As Boolean = False

        '    'UpdateColdFeedExportStatusAndUnLock will need this value
        '    chartWrapper.PhysicianEnabled = physicianModuleEnabled

        '    Dim chartInfo As DOChartInfo = pThisChart.ChartInfo

        '    Dim multiExportEnabled As Boolean = False
        '    If CFHelper.OptionsDict.ContainsKey("MultiExport") Then
        '        multiExportEnabled = CFHelper.OptionsDict("MultiExport")
        '    End If

        '    Select Case TriggerMethod.ToUpper
        '        Case "EXPORTSTATUS"
        '            If Not physicianModuleEnabled Then

        '                If chartInfo.ExportedChart Is Nothing Then Return False
        '                If chartInfo.ExportedChart = pThisChart Then
        '                    'Look for initial reason
        '                    If chartInfo.CFExportFacilityStatus = Nothing Then
        '                        chartWrapper.ReasonForExport = FacilityBillingExportStatus
        '                        Return True
        '                    End If
        '                End If

        '                If chartInfo.CFExportUserRequestPending Then
        '                    If chartInfo.ExportStatus Is Nothing Then Return False
        '                    If chartInfo.Chart.ChartStatus Is Nothing Then Return False

        '                    If chartInfo.ExportStatus = STR_Exported Then
        '                        If chartInfo.Chart.ChartStatus = STR_Complete Then
        '                            chartWrapper.ReasonForExport = UserRequested
        '                            Return True
        '                        End If
        '                    Else
        '                        Return False
        '                    End If
        '                End If
        '                Return False
        '            Else 'if phy enabled
        '                'ExportStatus trigger & PhysTab enabled ....
        '                If BothBillIngExportsDone(chartInfo) Then
        '                    If BothCFExportsStillPending(chartInfo) Then
        '                        'OK... Both Billing Exports have been done
        '                        If MostRecentBillingExportedChart(chartInfo) = pThisChart Then
        '                            chartWrapper.ReasonForExport = BothBillingExportStatus
        '                            Return True
        '                        End If
        '                    Else
        '                        'if only 1 still to send
        '                        If OnlyOneCFExported(chartInfo) Then
        '                            If pThisChart = MostRecentBillingExportedChart(chartInfo) Then
        '                                If chartInfo.ExportStatus Is Nothing Then
        '                                    chartWrapper.ReasonForExport = PhysicianBillingExportStatus
        '                                Else
        '                                    chartWrapper.ReasonForExport = FacilityBillingExportStatus
        '                                End If
        '                                Return True
        '                            Else
        '                                Return False
        '                            End If 'If pThisChart = MostRecentBillingExportedChart(chartInfo) Then
        '                        End If 'If OnlyOneCFExported(chartInfo) Then
        '                    End If 'else .... If BothCFExportsStillPending(chartInfo) Then
        '                End If 'If BothBillIngExportsDone(chartInfo) Then
        '            End If 'else phy enabled

        '            '09.14.11 - At this point, we know Both Fac and Phys Tabs are enable, and only 1 has been been
        '            'billing exported ...

        '            If chartInfo.ExportedChart IsNot Nothing AndAlso chartInfo.ExportedChart = pThisChart Then
        '                If chartInfo.CFExportFacilityStatus = Nothing Then
        '                    chartWrapper.ReasonForExport = FacilityBillingExportStatus
        '                    Return True
        '                End If
        '            End If

        '            If chartInfo.PhysExportChart IsNot Nothing AndAlso chartInfo.PhysExportChart = pThisChart Then
        '                If chartInfo.CFExportPhysicianStatus = Nothing Then
        '                    chartWrapper.ReasonForExport = PhysicianBillingExportStatus
        '                    Return True
        '                End If
        '            End If

        '            If chartInfo.CFExportUserRequestPending Then
        '                If (chartInfo.ExportStatus = STR_Exported) And (chartInfo.PhysExportStatus = STR_Exported) Then
        '                    If (chartInfo.Chart.ChartStatus = "Complete") And chartInfo.Chart.PhysChartStatus = "Complete" Then
        '                        chartWrapper.ReasonForExport = UserRequested
        '                        Return True
        '                    End If
        '                End If
        '            End If

        '        Case "CHARTSTATUS" 'trigger method
        '            If Not chartInfo.Chart = pThisChart Then
        '                Return False
        '            End If

        '            If Not physicianModuleEnabled Then
        '                If chartInfo.CFExportFacilityStatus Is Nothing OrElse multiExportEnabled Then
        '                    If chartInfo.Chart.ChartStatus = STR_Complete Then
        '                        ' If chartInfo.Chart = pThisChart Then
        '                        chartWrapper.ReasonForExport = FacilityChartStatus
        '                        Return True
        '                        'End If
        '                    End If
        '                End If

        '                If chartInfo.CFExportUserRequestPending Then
        '                    If FacilityChartStatusComplete(chartInfo) Then
        '                        chartWrapper.ReasonForExport = UserRequested
        '                        Return True
        '                    End If
        '                End If

        '            Else 'If physicianModuleEnabled Then
        '                If BothChartStatusComplete(chartInfo) Then
        '                    If BothCFExportsStillPending(chartInfo) Then
        '                        chartWrapper.ReasonForExport = BothChartStatus
        '                        Return True
        '                    End If
        '                End If

        '                If FacilityChartStatusComplete(chartInfo) And Not BothCFExportsDone(chartInfo) Then
        '                    If CFExportFacilityStatusNotExported(chartInfo) Then
        '                        chartWrapper.ReasonForExport = FacilityChartStatus
        '                        Return True
        '                    End If
        '                End If

        '                If PhysChartStatusComplete(chartInfo) Then
        '                    If chartInfo.CFExportPhysicianStatus Is Nothing OrElse Not chartInfo.CFExportPhysicianStatus = STR_Exported Then
        '                        chartWrapper.ReasonForExport = PhysicianChartStatus
        '                        Return True
        '                    End If
        '                End If

        '                If chartInfo.CFExportUserRequestPending Then
        '                    If BothChartStatusComplete(chartInfo) Then
        '                        chartWrapper.ReasonForExport = UserRequested
        '                        Return True
        '                    End If
        '                End If

        '                If multiExportEnabled Then
        '                    If BothChartStatusComplete(chartInfo) Then
        '                        chartWrapper.ReasonForExport = BothChartStatus
        '                        Return True
        '                    End If
        '                End If
        '            End If

        '        Case Else
        '            'Debug.Assert("Oh oh")
        '            ShowMessage(String.Format("Trigger method {0} is not valid", Me.TriggerMethod))
        '    End Select

        '    Return False
        'End Function

        'Private Shared Function BothBillIngExportsDone(ByVal chartInfo As DOChartInfo) As Boolean
        '    If chartInfo.ExportedChart Is Nothing Then Return False
        '    If chartInfo.PhysExportChart Is Nothing Then Return False

        '    If (chartInfo.ExportStatus = STR_Exported) And (chartInfo.PhysExportStatus = STR_Exported) Then
        '        Return True
        '    End If
        '    Return False
        'End Function

        'Private Shared Function BothCFExportsDone(ByVal chartinfo As DOChartInfo)
        '    If (chartinfo.CFExportFacilityStatus IsNot Nothing AndAlso chartinfo.CFExportPhysicianStatus IsNot Nothing) AndAlso _
        '        (chartinfo.CFExportFacilityStatus = STR_Exported AndAlso chartinfo.CFExportPhysicianStatus = STR_Exported) Then
        '        Return True
        '    End If
        '    Return False
        'End Function

        'Private Shared Function BothCFExportsStillPending(ByVal chartinfo As DOChartInfo)
        '    If chartinfo.CFExportFacilityStatus = Nothing AndAlso chartinfo.CFExportPhysicianStatus = Nothing Then
        '        Return True
        '    End If
        '    Return False
        'End Function

        'Private Shared Function BothChartStatusComplete(ByVal chartInfo As DOChartInfo) As Boolean
        '    Return (chartInfo.Chart.ChartStatus IsNot Nothing AndAlso chartInfo.Chart.ChartStatus = STR_Complete) And _
        '                                (chartInfo.Chart.PhysChartStatus IsNot Nothing AndAlso chartInfo.Chart.PhysChartStatus = STR_Complete)
        'End Function



        'Private Shared Function CFExportFacilityStatusNotExported(ByVal chartInfo As DOChartInfo) As Boolean
        '    Return chartInfo.CFExportFacilityStatus Is Nothing OrElse Not chartInfo.CFExportFacilityStatus = STR_Exported
        'End Function

        'Private Shared Function FacilityChartStatusComplete(ByVal chartInfo As DOChartInfo) As Boolean
        '    Return chartInfo.Chart.ChartStatus IsNot Nothing AndAlso chartInfo.Chart.ChartStatus = STR_Complete
        'End Function

        Private Shared Function IsPhysicianTabEnabled(ByVal pFacilitydo As DOFacility) As Boolean
            Dim enabled As Boolean = pFacilitydo.ConfigInstanceVersion.AppConfigGroup("EnablePhysicianTab").Enabled 'ConfigSettingsGroups("Application")("PhysTabEnabled").Enabled

            Return enabled
        End Function

        Private Shared Function IsObservationTabEnabled(ByVal pFacilitydo As DOFacility) As Boolean
            Dim enabled As Boolean = pFacilitydo.ConfigInstanceVersion.AppConfigGroup("EnableObservationTab").Enabled

            Return enabled
        End Function

        'Private Shared Function MaxChart(ByVal chart1 As DOChart, ByVal chart2 As DOChart) As DOChart
        '    If chart1.Oid > chart2.Oid Then
        '        Return chart1
        '    End If
        '    Return chart2
        'End Function

        'Private Shared Function MostRecentBillingExportedChart(ByVal chartInfo As DOChartInfo) As DOChart
        '    Return MaxChart(chartInfo.ExportedChart, chartInfo.PhysExportChart)
        'End Function

        'Private Shared Function OnlyOneCFExported(ByVal chartinfo As DOChartInfo) As Boolean
        '    Return (chartinfo.CFExportFacilityStatus Is Nothing) Xor (chartinfo.CFExportPhysicianStatus Is Nothing)
        'End Function

        'Private Shared Function PhysChartStatusComplete(ByVal chartInfo As DOChartInfo) As Boolean
        '    Return chartInfo.Chart.PhysChartStatus IsNot Nothing AndAlso chartInfo.Chart.PhysChartStatus = STR_Complete
        'End Function

        'Dim d As New DOChartInfo
        'd.CFExportFacilityChartVersion = Nothing
        'd.CFExportFacilityExportedDate = Nothing
        'd.CFExportFacilityStatus = Nothing
        'd.CFExportPhysicianChartVersion = Nothing
        'd.CFExportPhysicianExportedDate = Nothing
        'd.CFExportPhysicianStatus = Nothing
        'd.CFExportInclude = True
        'd.CFExportUserRequestPending = False
        'd.ExportStatus = Nothing
        'd.PhysExportStatus = Nothing
        Private Function adoGetCharts(ByVal facilityOID As Integer, ByVal IsPhysTabEnabled As Boolean, Optional ByVal IsObsTabEnabled As Boolean = False) As List(Of Integer)

            Dim chartsList As New List(Of Integer)
            Dim query As String

            query = BuildQueryAdo(facilityOID, IsPhysTabEnabled) 
            ShowMessage(String.Format("Executing Query: {0}", query))

            Using uow As New UnitOfWork()
                Dim rdata As SelectedData = uow.ExecuteQuery(query)
                For each row In rdata?.ResultSet?(0).Rows
                    Dim chartOID As Integer = row.Values(0)
                    chartsList.Add(chartOID)
                Next
            End Using

            ShowMessage(String.Format("Query: returned {0} charts", chartsList.Count))
            Return chartsList
        End Function

        Private Function adoGetChartsOld(ByVal facilityOID As Integer, ByVal IsPhysTabEnabled As Boolean, Optional ByVal IsObsTabEnabled As Boolean = False) As List(Of Integer)

            Dim chartsList As New List(Of Integer)
            Dim query As String

            Dim conn = GetdbConnection() 'don't use a 'USING' statement as it will close the devexpress connection
            Using comm = conn.CreateCommand
                query = BuildQueryAdo(facilityOID, IsPhysTabEnabled) ', pFacilityOID)
                ShowMessage(String.Format("Executing Query: {0}", query))
                comm.CommandText = query

                Using reader = comm.ExecuteReader
                    While reader.Read
                        chartsList.Add(reader("Oid"))
                        'iShowMessage.Show(reader("Oid"))
                    End While
                End Using
            End Using

            ShowMessage(String.Format("Query: returned {0} charts", chartsList.Count))
            Return chartsList
        End Function

        Private Function BuildQueryAdo(ByVal facilityOid As Integer, ByVal IsPhysTabEnabled As Boolean, Optional ByVal IsObsTabenabled As Boolean = False) As String
            Dim sb As New Text.StringBuilder
            Try
                Dim MaxAge As DateTime = Now.AddMonths(-3)

                sb.Append("Select chart.oid as Oid from ")
                sb.Append("DOChartInfo as ci left outer join DOChart as chart on ci.chart = chart.oid ")
                sb.Append(String.Format("where ci.Facility = {0} and ci.GCRecord is Null ", facilityOid))
                sb.Append(String.Format(" and chart.CreationDate > '{0}' ", MaxAge.ToString("yyyy/MM/dd")))
                sb.Append(String.Format(" and chart.DateOfService > '{0}' ", CFHelper.OptionsDict("StartDate")))
                sb.Append("and ((ci.locked = 0) or (ci.LockStatus = 'CFExport' ))")

                'JJC 02-08-2012 Fixed a bug that was a result of the last changes i made in
                'preperation for the Observation tab. This has changed a lot from what we
                'initially deployed. Check Starteam history for old usage if more issues 
                'surface regarding this query.

                'Note, normally it's only possible for the CFExportUserRequestPending flag to be set and the
                'CFExportInclude to be false, if the coldfeed has already processed the chart, thus clearing
                'that flag, and then the use clicks the "Image Export" button in the UI, but then doesn't save
                'the chart. There's some bad magic happening that allows the CFExportUserRequestPending to be 
                'set without updating the CFExportInclude flag in the above scenario where the opt to not save
                'the chart. If everything was working correctly, we would not need to append the ..
                '" or (ci.CFExportUserRequestPending = 1)", but because of the exiting bug in the UI... we 
                'should still check it.

                sb.Append(" and ((ci.CFExportInclude = 1) or (ci.CFExportUserRequestPending = 1)) ")
                sb.Append(" order by chart.Version")
                'JJC 02-08-2012 End of change

            Catch ex As Exception
                'My.Application.Log.WriteEntry(ex.Message)
                CFHelper.ShowMessageAndLog(ex.Message)
            End Try

            Dim query = sb.ToString

            Return query
        End Function

        Sub ShowMessage(ByVal msg As String)
            CFHelper.ShowMessageAndLog(msg)
        End Sub

#End Region 'Methods

    End Class

End Namespace

